#!/bin/bash

# Docker Compose Quick Setup for 1-Minute Follow-ups
echo "🐳 GrowthHive Docker - Quick 1-Minute Follow-up Setup"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running!"
    echo "   Please start Docker Desktop and try again"
    exit 1
fi

echo "✅ Docker is running"

# Check if containers are running
echo ""
echo "📋 Checking container status..."
docker-compose ps

# Start containers if not running
if ! docker-compose ps | grep -q "Up"; then
    echo ""
    echo "🚀 Starting Docker containers..."
    docker-compose up -d
    
    echo "⏳ Waiting for containers to be ready..."
    sleep 10
fi

# Check container health
echo ""
echo "🏥 Container health check..."
docker-compose ps

# Check if backend is responding
echo ""
echo "🔍 Testing backend connectivity..."
if curl -s http://localhost:8000/docs > /dev/null; then
    echo "✅ Backend is responding"
else
    echo "❌ Backend not responding - waiting 10 more seconds..."
    sleep 10
    if curl -s http://localhost:8000/docs > /dev/null; then
        echo "✅ Backend is now responding"
    else
        echo "❌ Backend still not responding - check logs:"
        echo "   docker logs growthhive-backend"
        exit 1
    fi
fi

# Run the Python setup script
echo ""
echo "🔧 Running 1-minute follow-up setup..."
python3 docker_setup_1minute_followups.py

echo ""
echo "🎉 Setup complete!"
echo ""
echo "💡 Useful Docker commands:"
echo "   View backend logs:  docker logs -f growthhive-backend"
echo "   View celery logs:   docker logs -f growthhive-celery"
echo "   Stop containers:    docker-compose down"
echo "   Restart containers: docker-compose restart"
