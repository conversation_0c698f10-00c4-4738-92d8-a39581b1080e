# Andy AI Workflow Changes Summary

## Overview
Updated the existing Andy AI system to follow the new workflow specification provided. The changes focus on dialogue improvements, dynamic data fetching, and enhanced objection handling while maintaining all existing functionality.

## Key Changes Made

### 1. Introduction Stage Updates
**File**: `app/agents/sms_assistant.py`

- **Updated Introduction Template**: Changed from generic greeting to specific format:
  - **New**: `"Hi {lead_name}, My name is <PERSON>, I'm reaching out in regards to the {franchisor_name} franchise you enquired about. Do you have a minute for a quick SMS chat? Thanks!"`
  - **Old**: `"G'day {name}! Thanks for your interest in {franchisor_name}. Do you have a minute to chat about this opportunity?"`

- **Dynamic Name Fetching**: Enhanced all introduction and hello response methods to:
  - Fetch lead name dynamically from database using `_get_lead_name_by_phone()`
  - Use lead name in introduction if available
  - Fallback to generic greeting if name not found
  - Applied to: `send_introduction`, `send_hello_response`, and fallback methods

### 2. Workflow Role Prompt Updates
**File**: `app/agents/sms_assistant.py` - `_get_andy_role_prompt()` method

- **Updated Introduction Format**: Changed to match new specification
- **Enhanced Qualification Flow**: Updated qualification questions to match exact workflow:
  - Qualification 1: Work background identification (corporate vs trades)
  - Qualification 2: Motivation categorization (8 specific categories)
  - Qualification 3: Budget confirmation with dynamic data
- **Improved Scheduling Logic**: Updated to use dynamic person and time slot information
- **Enhanced Objection Handling**: Added support for dynamic convincing messages

### 3. Objection Handling Enhancements
**File**: `app/agents/sms_assistant.py`

- **New Objection Templates**: Added templates with dynamic message placeholders:
  ```python
  OBJECTION_NO_VALUE = "I understand however we are yet to walk you through the business potential. {convencing_message}"
  OBJECTION_NOT_HEARD = "Fair enough. {convencing_message_2}"
  OBJECTION_MARKETING = "We will provide you lead guarantee..."
  OBJECTION_NO_EXPERIENCE = "We will provide you comprehensive training..."
  OBJECTION_INCOME = "A totally fair question. Some of our franchise partners..."
  OBJECTION_INCOME_GUARANTEE = "In the first year, we guarantee $60K net income..."
  OBJECTION_ROYALTY_FEE = "There is {royalty_fee} and {marketing_fund}..."
  ```

- **New Method**: `_get_convincing_messages()` - Fetches dynamic convincing messages using RAG system
- **New Method**: `_handle_objection_response()` - Handles objection detection and response generation
- **Integration**: Added objection handling to response generation pipeline

### 4. Dynamic Data Integration
**File**: `app/agents/sms_assistant.py`

- **Convincing Messages**: Added RAG-based fetching of convincing messages for objection handling
- **Person Available for Meeting**: Added `_get_person_available_for_meeting()` method
- **Available Time Slots**: Added `_get_available_time_slots()` method for dynamic scheduling
- **Franchisor ID Extraction**: Added `_get_franchisor_id_from_state()` for RAG queries

### 5. Template Updates
**File**: `app/agents/sms_assistant.py` - `AndyTemplates` class

- **Introduction Templates**: Updated to use `{lead_name}` instead of `{name}`
- **Franchise Fee Breakdown**: Enhanced to include dynamic franchisor name and fee info
- **Scheduling Offer**: Updated to support dynamic person and time slot data
- **Qualification Questions**: Refined to match exact workflow specification

### 6. Response Generation Updates
**File**: `app/agents/sms_assistant.py`

Updated multiple response generation methods:
- `_response_generation_node()` - Enhanced template responses
- `_generate_fallback_response()` - Added dynamic name fetching
- Added `handle_objections` action support

## Workflow Compliance

### New Workflow Structure
1. **Introduction**: Dynamic name fetching + specific message format
2. **Qualification 1**: Work background (corporate vs trades identification)
3. **Qualification 2**: Motivation (8 categories with specific questions)
4. **Qualification 3**: Budget (dynamic budget confirmation)
5. **Scheduling**: Dynamic person and time slot offering
6. **Confirmation**: Meeting confirmation
7. **Objection Handling**: Dynamic convincing messages throughout

### Key Features Maintained
- ✅ RAG functionality intact
- ✅ Existing database integration preserved
- ✅ Meeting booking system unchanged
- ✅ Lead context management preserved
- ✅ Conversation history tracking maintained
- ✅ All existing API endpoints functional

### New Features Added
- ✅ Dynamic lead name fetching in introduction
- ✅ Enhanced objection handling with convincing messages
- ✅ RAG-based dynamic data for objections
- ✅ Improved conversation flow matching specification
- ✅ Better acknowledgment patterns ("Great", "That sounds interesting")

## Testing Results
- ✅ Templates render correctly with dynamic data
- ✅ Andy SMS Assistant initializes successfully
- ✅ All new methods are properly integrated
- ✅ Existing functionality preserved

## Files Modified
1. `app/agents/sms_assistant.py` - Main implementation file
   - Updated templates and workflow logic
   - Added new methods for dynamic data fetching
   - Enhanced objection handling
   - Improved response generation

## Backward Compatibility
- ✅ All existing functionality preserved
- ✅ No breaking changes to API
- ✅ Database schema unchanged
- ✅ Existing tests should continue to pass
- ✅ No new dependencies added

## Next Steps
1. Test the updated workflow with real conversations
2. Monitor objection handling effectiveness
3. Fine-tune convincing messages based on performance
4. Update any related documentation if needed

The Andy AI system now follows the exact workflow specification while maintaining all existing functionality and adding enhanced dynamic data capabilities.
