"""
File loading and validation system for the document ingestion pipeline.

This module handles file loading from various sources (local files, S3),
validates file integrity and type, and provides safe I/O operations.
"""

import tempfile
from pathlib import Path
from typing import Optional, Union
from urllib.parse import urlparse

import boto3
from botocore.exceptions import ClientError, NoCredentialsError

from .core import (
    FileNotFoundError,
    FileType,
    IngestionConfig,
    NetworkError,
)
from .utils import FileValidatorImpl, get_logger

logger = get_logger(__name__)


class FileLoader:
    """
    Handles file loading from various sources with validation and safety checks.
    
    Supports:
    - Local file paths
    - S3 URLs (s3://bucket/key)
    - File validation and type detection
    - Safe temporary file handling
    """
    
    def __init__(self, config: IngestionConfig):
        """
        Initialize the file loader.
        
        Args:
            config: Ingestion configuration
        """
        self.config = config
        self.validator = FileValidatorImpl()
        self._s3_client: Optional[boto3.client] = None
        
        # Supported file types based on configuration
        self.supported_types = [
            FileType.PDF,
            FileType.DOCX,
            FileType.DOC,
            FileType.PPTX,
            FileType.PPT,
            FileType.CSV,
            FileType.TXT,
            FileType.MD,
            FileType.HTML,
            FileType.HTM,
            FileType.JPG,
            FileType.JPEG,
            FileType.PNG,
            FileType.WEBP,
            FileType.GIF,
            FileType.ZIP,
        ]
    
    @property
    def s3_client(self) -> boto3.client:
        """Lazy initialization of S3 client."""
        if self._s3_client is None:
            try:
                self._s3_client = boto3.client('s3')
                logger.debug("S3 client initialized successfully")
            except NoCredentialsError as e:
                raise NetworkError(
                    "AWS credentials not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY or configure AWS CLI.",
                    cause=e,
                )
        return self._s3_client
    
    def load_file(self, source: Union[str, Path]) -> Path:
        """
        Load a file from various sources and return a local path.
        
        Args:
            source: File source (local path or S3 URL)
            
        Returns:
            Path to the loaded file (may be temporary for remote files)
            
        Raises:
            FileNotFoundError: If file cannot be found
            UnsupportedFileTypeError: If file type is not supported
            NetworkError: If S3 download fails
        """
        source_str = str(source)
        
        logger.info(
            "Loading file",
            source=source_str,
            max_size_mb=self.config.max_file_size_mb,
        )
        
        # Determine if source is S3 URL, HTTPS URL, or local path
        if source_str.startswith('s3://'):
            file_path = self._download_from_s3(source_str)
        elif self.is_https_url(source_str):
            file_path = self._download_from_https(source_str)
        else:
            file_path = Path(source_str)
            if not file_path.exists():
                raise FileNotFoundError(
                    f"Local file not found: {source_str}",
                    file_path=source_str,
                )
        
        # Validate the loaded file
        self._validate_file(file_path)
        
        logger.info(
            "File loaded successfully",
            source=source_str,
            local_path=str(file_path),
            file_info=self.validator.get_file_info(file_path),
        )
        
        return file_path
    
    def _download_from_s3(self, s3_url: str) -> Path:
        """
        Download a file from S3 to a temporary location.
        
        Args:
            s3_url: S3 URL in format s3://bucket/key
            
        Returns:
            Path to the downloaded temporary file
            
        Raises:
            NetworkError: If download fails
        """
        try:
            # Parse S3 URL
            parsed = urlparse(s3_url)
            bucket = parsed.netloc
            key = parsed.path.lstrip('/')
            
            if not bucket or not key:
                raise NetworkError(
                    f"Invalid S3 URL format: {s3_url}",
                    file_path=s3_url,
                )
            
            logger.debug(
                "Downloading from S3",
                bucket=bucket,
                key=key,
                s3_url=s3_url,
            )
            
            # Create temporary file with appropriate extension
            file_extension = Path(key).suffix
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=file_extension,
            )
            temp_path = Path(temp_file.name)
            temp_file.close()
            
            # Download file
            self.s3_client.download_file(bucket, key, str(temp_path))
            
            logger.debug(
                "S3 download completed",
                bucket=bucket,
                key=key,
                local_path=str(temp_path),
                file_size=temp_path.stat().st_size,
            )
            
            return temp_path
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'NoSuchKey':
                raise FileNotFoundError(
                    f"S3 object not found: {s3_url}",
                    file_path=s3_url,
                    cause=e,
                )
            elif error_code == 'NoSuchBucket':
                raise FileNotFoundError(
                    f"S3 bucket not found: {bucket}",
                    file_path=s3_url,
                    cause=e,
                )
            else:
                raise NetworkError(
                    f"S3 download failed: {e}",
                    file_path=s3_url,
                    cause=e,
                )
        except Exception as e:
            raise NetworkError(
                f"Unexpected error during S3 download: {e}",
                file_path=s3_url,
                cause=e,
            )
    
    def _validate_file(self, file_path: Path) -> None:
        """
        Validate a loaded file.
        
        Args:
            file_path: Path to the file to validate
            
        Raises:
            FileSizeExceededError: If file is too large
            UnsupportedFileTypeError: If file type is not supported
            FileCorruptedError: If file appears corrupted
        """
        # Validate file size
        self.validator.validate_file_size(file_path, self.config.max_file_size_mb)
        
        # Validate file type
        self.validator.validate_file_type(file_path, self.supported_types)
        
        # Validate file integrity
        self.validator.validate_file_integrity(file_path)
    
    def cleanup_temp_file(self, file_path: Path) -> None:
        """
        Clean up temporary files safely.
        
        Args:
            file_path: Path to the temporary file
        """
        try:
            if file_path.exists() and str(file_path).startswith(tempfile.gettempdir()):
                file_path.unlink()
                logger.debug("Temporary file cleaned up", file_path=str(file_path))
        except Exception as e:
            logger.warning(
                "Failed to clean up temporary file",
                file_path=str(file_path),
                error=str(e),
            )
    
    def is_s3_url(self, source: str) -> bool:
        """Check if source is an S3 URL."""
        return source.startswith('s3://')

    def is_https_url(self, source: str) -> bool:
        """Check if source is an HTTPS URL."""
        return source.startswith('https://') or source.startswith('http://')
    
    def get_file_type(self, file_path: Path) -> FileType:
        """Get the detected file type for a file."""
        return self.validator._detect_file_type(file_path)
