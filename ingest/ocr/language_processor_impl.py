"""
Language processing implementation with detection and translation.

This module provides language detection and translation capabilities
using langdetect and OpenAI GPT models.
"""

from typing import Optional

try:
    import langdetect
    import openai
    LANG_LIBS_AVAILABLE = True
except ImportError:
    LANG_LIBS_AVAILABLE = False

from ..core import LanguageCode, LanguageDetectionError, LanguageProcessor, TranslationError
from ..utils import get_logger

logger = get_logger(__name__)


class LanguageProcessorImpl(LanguageProcessor):
    """
    Language processor implementation with detection and translation.
    
    Features:
    - Automatic language detection using langdetect
    - Translation using OpenAI GPT models
    - Confidence scoring for language detection
    - Batch processing support
    """
    
    def __init__(self, openai_api_key: Optional[str] = None, translation_model: str = "gpt-4o"):
        """
        Initialize language processor.
        
        Args:
            openai_api_key: OpenAI API key for translation
            translation_model: OpenAI model to use for translation
        """
        self.translation_model = translation_model
        self.translation_available = False
        
        # Initialize OpenAI client for translation
        if LANG_LIBS_AVAILABLE:
            try:
                if openai_api_key:
                    self.openai_client = openai.OpenAI(api_key=openai_api_key)
                else:
                    self.openai_client = openai.OpenAI()  # Uses OPENAI_API_KEY env var
                
                self.translation_available = True
                logger.info(
                    "Language processor initialized",
                    translation_model=translation_model,
                    translation_available=True,
                )
            except Exception as e:
                logger.warning(
                    "OpenAI client initialization failed, translation disabled",
                    error=str(e),
                )
        else:
            logger.warning("Language processing libraries not available")
    
    def detect_language(self, text: str) -> LanguageCode:
        """
        Detect the language of the given text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Detected language code
            
        Raises:
            LanguageDetectionError: If detection fails
        """
        if not LANG_LIBS_AVAILABLE:
            raise LanguageDetectionError("langdetect library not available")
        
        try:
            # Clean text for better detection
            cleaned_text = self._clean_text_for_detection(text)
            
            if len(cleaned_text.strip()) < 10:
                logger.debug("Text too short for reliable language detection")
                return LanguageCode.UNKNOWN
            
            # Detect language
            detected = langdetect.detect(cleaned_text)
            
            # Map langdetect codes to our LanguageCode enum
            language_mapping = {
                'en': LanguageCode.ENGLISH,
                'es': LanguageCode.SPANISH,
                'fr': LanguageCode.FRENCH,
                'de': LanguageCode.GERMAN,
                'it': LanguageCode.ITALIAN,
                'pt': LanguageCode.PORTUGUESE,
                'ru': LanguageCode.RUSSIAN,
                'zh': LanguageCode.CHINESE,
                'ja': LanguageCode.JAPANESE,
                'ko': LanguageCode.KOREAN,
                'ar': LanguageCode.ARABIC,
                'hi': LanguageCode.HINDI,
            }
            
            result = language_mapping.get(detected, LanguageCode.UNKNOWN)
            
            logger.debug(
                "Language detected",
                detected_code=detected,
                mapped_language=result,
                text_length=len(text),
            )
            
            return result
            
        except Exception as e:
            raise LanguageDetectionError(
                f"Language detection failed: {e}",
                cause=e,
            )
    
    def translate_text(
        self,
        text: str,
        target_language: LanguageCode,
        source_language: Optional[LanguageCode] = None,
    ) -> str:
        """
        Translate text to the target language.
        
        Args:
            text: Text to translate
            target_language: Target language code
            source_language: Source language code (auto-detected if None)
            
        Returns:
            Translated text
            
        Raises:
            TranslationError: If translation fails
        """
        if not self.translation_available:
            raise TranslationError("Translation not available - OpenAI client not initialized")
        
        try:
            # Auto-detect source language if not provided
            if source_language is None:
                source_language = self.detect_language(text)
            
            # Check if translation is needed
            if not self.is_translation_needed(source_language, target_language):
                return text
            
            # Create translation prompt
            prompt = self._create_translation_prompt(text, source_language, target_language)
            
            # Call OpenAI API
            response = self.openai_client.chat.completions.create(
                model=self.translation_model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional translator. Translate the given text accurately while preserving meaning, tone, and formatting."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=len(text.split()) * 2,  # Rough estimate for translation length
                temperature=0.1,  # Low temperature for consistent translation
            )
            
            translated_text = response.choices[0].message.content.strip()
            
            logger.info(
                "Text translation completed",
                source_language=source_language,
                target_language=target_language,
                original_length=len(text),
                translated_length=len(translated_text),
            )
            
            return translated_text
            
        except Exception as e:
            raise TranslationError(
                f"Translation failed: {e}",
                cause=e,
            )
    
    def is_translation_needed(
        self,
        source_language: LanguageCode,
        target_language: LanguageCode,
    ) -> bool:
        """
        Check if translation is needed.
        
        Args:
            source_language: Source language code
            target_language: Target language code
            
        Returns:
            True if translation is needed
        """
        # No translation needed if languages are the same
        if source_language == target_language:
            return False
        
        # No translation needed if source is unknown
        if source_language == LanguageCode.UNKNOWN:
            return False
        
        # No translation needed if target is unknown
        if target_language == LanguageCode.UNKNOWN:
            return False
        
        return True
    
    def detect_language_with_confidence(self, text: str) -> tuple[LanguageCode, float]:
        """
        Detect language with confidence score.
        
        Args:
            text: Text to analyze
            
        Returns:
            Tuple of (language_code, confidence_score)
        """
        if not LANG_LIBS_AVAILABLE:
            return LanguageCode.UNKNOWN, 0.0
        
        try:
            cleaned_text = self._clean_text_for_detection(text)
            
            if len(cleaned_text.strip()) < 10:
                return LanguageCode.UNKNOWN, 0.0
            
            # Get language probabilities
            lang_probs = langdetect.detect_langs(cleaned_text)
            
            if lang_probs:
                best_lang = lang_probs[0]
                
                # Map to our language codes
                language_mapping = {
                    'en': LanguageCode.ENGLISH,
                    'es': LanguageCode.SPANISH,
                    'fr': LanguageCode.FRENCH,
                    'de': LanguageCode.GERMAN,
                    'it': LanguageCode.ITALIAN,
                    'pt': LanguageCode.PORTUGUESE,
                    'ru': LanguageCode.RUSSIAN,
                    'zh': LanguageCode.CHINESE,
                    'ja': LanguageCode.JAPANESE,
                    'ko': LanguageCode.KOREAN,
                    'ar': LanguageCode.ARABIC,
                    'hi': LanguageCode.HINDI,
                }
                
                detected_lang = language_mapping.get(best_lang.lang, LanguageCode.UNKNOWN)
                confidence = best_lang.prob
                
                return detected_lang, confidence
            
            return LanguageCode.UNKNOWN, 0.0
            
        except Exception as e:
            logger.warning(
                "Language detection with confidence failed",
                error=str(e),
            )
            return LanguageCode.UNKNOWN, 0.0
    
    def _clean_text_for_detection(self, text: str) -> str:
        """
        Clean text for better language detection.
        
        Args:
            text: Raw text
            
        Returns:
            Cleaned text
        """
        # Remove excessive whitespace
        cleaned = ' '.join(text.split())
        
        # Remove common non-linguistic elements
        import re
        
        # Remove URLs
        cleaned = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', cleaned)
        
        # Remove email addresses
        cleaned = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', cleaned)
        
        # Remove excessive punctuation
        cleaned = re.sub(r'[^\w\s]', ' ', cleaned)
        
        # Remove numbers (they don't help with language detection)
        cleaned = re.sub(r'\b\d+\b', '', cleaned)
        
        return cleaned.strip()
    
    def _create_translation_prompt(
        self,
        text: str,
        source_language: LanguageCode,
        target_language: LanguageCode,
    ) -> str:
        """
        Create translation prompt for OpenAI.
        
        Args:
            text: Text to translate
            source_language: Source language
            target_language: Target language
            
        Returns:
            Formatted prompt
        """
        # Map language codes to full names
        language_names = {
            LanguageCode.ENGLISH: "English",
            LanguageCode.SPANISH: "Spanish",
            LanguageCode.FRENCH: "French",
            LanguageCode.GERMAN: "German",
            LanguageCode.ITALIAN: "Italian",
            LanguageCode.PORTUGUESE: "Portuguese",
            LanguageCode.RUSSIAN: "Russian",
            LanguageCode.CHINESE: "Chinese",
            LanguageCode.JAPANESE: "Japanese",
            LanguageCode.KOREAN: "Korean",
            LanguageCode.ARABIC: "Arabic",
            LanguageCode.HINDI: "Hindi",
        }
        
        source_name = language_names.get(source_language, "the source language")
        target_name = language_names.get(target_language, "the target language")
        
        prompt = f"""Translate the following text from {source_name} to {target_name}.

Preserve the original meaning, tone, and formatting as much as possible.
If there are technical terms or proper nouns, keep them as appropriate for the target language.

Text to translate:
{text}

Translation:"""
        
        return prompt
