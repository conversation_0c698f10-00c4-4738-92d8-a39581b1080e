"""
OCR processing implementation using pytesseract.

This module provides OCR capabilities with image preprocessing
for improved text extraction accuracy.
"""

import numpy as np
from typing import Optional

try:
    import cv2
    import pytesseract
    from PIL import Image
    OCR_LIBS_AVAILABLE = True
except ImportError:
    OCR_LIBS_AVAILABLE = False

from ..core import LanguageCode, OCRError, OCRProcessor
from ..utils import get_logger

logger = get_logger(__name__)


class OCRProcessorImpl(OCRProcessor):
    """
    OCR processor implementation using pytesseract with advanced preprocessing.
    
    Features:
    - Image preprocessing for better OCR accuracy
    - Multiple language support
    - Confidence scoring
    - Error handling and fallback strategies
    """
    
    def __init__(self, tesseract_cmd: Optional[str] = None):
        """
        Initialize OCR processor.
        
        Args:
            tesseract_cmd: Path to tesseract executable (if not in PATH)
        """
        if not OCR_LIBS_AVAILABLE:
            logger.warning("OCR libraries not available. Install with: pip install pytesseract opencv-python")
            self.available = False
            return
        
        self.available = True
        
        # Configure tesseract
        if tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = tesseract_cmd
        
        # Test tesseract availability
        try:
            pytesseract.get_tesseract_version()
            logger.info("OCR processor initialized successfully")
        except Exception as e:
            logger.error(
                "Failed to initialize tesseract",
                error=str(e),
            )
            self.available = False
    
    def extract_text(self, image: Image.Image) -> str:
        """
        Extract text from an image using OCR.
        
        Args:
            image: PIL Image to process
            
        Returns:
            Extracted text
            
        Raises:
            OCRError: If OCR processing fails
        """
        if not self.available:
            raise OCRError("OCR libraries not available")
        
        try:
            # Preprocess image for better OCR
            processed_image = self.preprocess_image(image)
            
            # Extract text with confidence
            data = pytesseract.image_to_data(
                processed_image,
                output_type=pytesseract.Output.DICT,
                config='--psm 6'  # Uniform block of text
            )
            
            # Filter out low-confidence text
            text_parts = []
            for i, conf in enumerate(data['conf']):
                if int(conf) > 30:  # Confidence threshold
                    text = data['text'][i].strip()
                    if text:
                        text_parts.append(text)
            
            extracted_text = ' '.join(text_parts)
            
            # Fallback to simple extraction if confidence filtering yields little text
            if len(extracted_text) < 10:
                extracted_text = pytesseract.image_to_string(processed_image)
            
            logger.debug(
                "OCR text extraction completed",
                text_length=len(extracted_text),
                image_size=image.size,
            )
            
            return extracted_text.strip()
            
        except Exception as e:
            raise OCRError(
                f"OCR text extraction failed: {e}",
                cause=e,
            )
    
    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image to improve OCR accuracy.
        
        Args:
            image: PIL Image to preprocess
            
        Returns:
            Preprocessed PIL Image
        """
        try:
            # Convert PIL to OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply noise reduction
            denoised = cv2.medianBlur(gray, 3)
            
            # Apply adaptive thresholding for better text contrast
            binary = cv2.adaptiveThreshold(
                denoised,
                255,
                cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY,
                11,
                2
            )
            
            # Apply morphological operations to clean up the image
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # Convert back to PIL Image
            processed_image = Image.fromarray(cleaned)
            
            return processed_image
            
        except Exception as e:
            logger.warning(
                "Image preprocessing failed, using original image",
                error=str(e),
            )
            return image
    
    def detect_language(self, text: str) -> LanguageCode:
        """
        Detect the language of extracted text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Detected language code
        """
        try:
            import langdetect
            
            if len(text.strip()) < 10:
                return LanguageCode.UNKNOWN
            
            detected = langdetect.detect(text)
            
            # Map langdetect codes to our LanguageCode enum
            language_mapping = {
                'en': LanguageCode.ENGLISH,
                'es': LanguageCode.SPANISH,
                'fr': LanguageCode.FRENCH,
                'de': LanguageCode.GERMAN,
                'it': LanguageCode.ITALIAN,
                'pt': LanguageCode.PORTUGUESE,
                'ru': LanguageCode.RUSSIAN,
                'zh': LanguageCode.CHINESE,
                'ja': LanguageCode.JAPANESE,
                'ko': LanguageCode.KOREAN,
                'ar': LanguageCode.ARABIC,
                'hi': LanguageCode.HINDI,
            }
            
            return language_mapping.get(detected, LanguageCode.UNKNOWN)
            
        except ImportError:
            logger.warning("langdetect not available for language detection")
            return LanguageCode.UNKNOWN
        except Exception as e:
            logger.warning(
                "Language detection failed",
                error=str(e),
                text_preview=text[:100],
            )
            return LanguageCode.UNKNOWN
    
    def extract_text_with_layout(self, image: Image.Image) -> dict:
        """
        Extract text with layout information (bounding boxes, confidence).
        
        Args:
            image: PIL Image to process
            
        Returns:
            Dictionary with text and layout information
        """
        if not self.available:
            raise OCRError("OCR libraries not available")
        
        try:
            processed_image = self.preprocess_image(image)
            
            # Get detailed OCR data
            data = pytesseract.image_to_data(
                processed_image,
                output_type=pytesseract.Output.DICT,
                config='--psm 6'
            )
            
            # Organize data by text blocks
            blocks = []
            current_block = []
            
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > 30:  # Confidence threshold
                    text = data['text'][i].strip()
                    if text:
                        word_info = {
                            'text': text,
                            'confidence': int(data['conf'][i]),
                            'bbox': (
                                data['left'][i],
                                data['top'][i],
                                data['width'][i],
                                data['height'][i]
                            ),
                            'block_num': data['block_num'][i],
                            'par_num': data['par_num'][i],
                            'line_num': data['line_num'][i],
                        }
                        current_block.append(word_info)
                
                # Start new block when block number changes
                if i < len(data['text']) - 1 and data['block_num'][i] != data['block_num'][i + 1]:
                    if current_block:
                        blocks.append(current_block)
                        current_block = []
            
            # Add final block
            if current_block:
                blocks.append(current_block)
            
            return {
                'blocks': blocks,
                'full_text': ' '.join([word['text'] for block in blocks for word in block]),
                'total_words': sum(len(block) for block in blocks),
            }
            
        except Exception as e:
            raise OCRError(
                f"OCR layout extraction failed: {e}",
                cause=e,
            )
    
    def is_available(self) -> bool:
        """Check if OCR processing is available."""
        return self.available
    
    def get_supported_languages(self) -> list:
        """
        Get list of languages supported by tesseract.
        
        Returns:
            List of language codes supported by tesseract
        """
        if not self.available:
            return []
        
        try:
            langs = pytesseract.get_languages()
            return langs
        except Exception as e:
            logger.warning(
                "Failed to get supported languages",
                error=str(e),
            )
            return []
