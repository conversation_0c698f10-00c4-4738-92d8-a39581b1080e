"""
Text chunking implementation for optimal vector embedding.

This module provides intelligent text chunking with overlap
for better context preservation in vector embeddings.
"""

import re
from typing import List

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False

from ..core import Chun<PERSON><PERSON><PERSON><PERSON>, TextChunker
from ..utils import get_logger

logger = get_logger(__name__)


class TextChunkerImpl(TextChunker):
    """
    Text chunker implementation with intelligent splitting and overlap.
    
    Features:
    - Token-aware chunking using tiktoken
    - Sentence and paragraph boundary preservation
    - Configurable overlap for context preservation
    - Multiple chunking strategies
    """
    
    def __init__(self, encoding_name: str = "cl100k_base"):
        """
        Initialize text chunker.
        
        Args:
            encoding_name: Tiktoken encoding name for token counting
        """
        self.encoding_name = encoding_name
        
        if TIKTOKEN_AVAILABLE:
            try:
                self.encoding = tiktoken.get_encoding(encoding_name)
                self.token_counting_available = True
                logger.info(
                    "Text chunker initialized with tiktoken",
                    encoding=encoding_name,
                )
            except Exception as e:
                logger.warning(
                    "Failed to initialize tiktoken, falling back to word counting",
                    error=str(e),
                )
                self.token_counting_available = False
        else:
            logger.warning("tiktoken not available, using word-based chunking")
            self.token_counting_available = False
    
    def chunk_text(
        self,
        text: str,
        chunk_size: int = 400,
        overlap: int = 50,
    ) -> List[str]:
        """
        Split text into overlapping chunks.
        
        Args:
            text: Text to chunk
            chunk_size: Maximum tokens per chunk
            overlap: Number of tokens to overlap between chunks
            
        Returns:
            List of text chunks
            
        Raises:
            ChunkingError: If chunking fails
        """
        try:
            if not text.strip():
                return []
            
            # Choose chunking strategy based on available tools
            if self.token_counting_available:
                chunks = self._chunk_by_tokens(text, chunk_size, overlap)
            else:
                chunks = self._chunk_by_words(text, chunk_size, overlap)
            
            # Post-process chunks
            chunks = self._post_process_chunks(chunks)
            
            logger.debug(
                "Text chunking completed",
                original_length=len(text),
                chunks_created=len(chunks),
                chunk_size=chunk_size,
                overlap=overlap,
            )
            
            return chunks
            
        except Exception as e:
            raise ChunkingError(
                f"Text chunking failed: {e}",
                cause=e,
            )
    
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in the given text.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        if self.token_counting_available:
            try:
                return len(self.encoding.encode(text))
            except Exception as e:
                logger.warning(
                    "Token counting failed, falling back to word count",
                    error=str(e),
                )
        
        # Fallback to word counting
        return len(text.split())
    
    def _chunk_by_tokens(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """
        Chunk text by token count using tiktoken.
        
        Args:
            text: Text to chunk
            chunk_size: Maximum tokens per chunk
            overlap: Token overlap between chunks
            
        Returns:
            List of text chunks
        """
        # Encode text to tokens
        tokens = self.encoding.encode(text)
        
        if len(tokens) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(tokens):
            # Calculate end position
            end = min(start + chunk_size, len(tokens))
            
            # Extract chunk tokens
            chunk_tokens = tokens[start:end]
            
            # Decode back to text
            chunk_text = self.encoding.decode(chunk_tokens)
            
            # Try to break at sentence boundaries if possible
            if end < len(tokens):  # Not the last chunk
                chunk_text = self._adjust_chunk_boundary(chunk_text)
            
            chunks.append(chunk_text)
            
            # Move start position with overlap
            if end >= len(tokens):
                break
            
            start = end - overlap
        
        return chunks
    
    def _chunk_by_words(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """
        Chunk text by word count (fallback method).
        
        Args:
            text: Text to chunk
            chunk_size: Maximum words per chunk
            overlap: Word overlap between chunks
            
        Returns:
            List of text chunks
        """
        words = text.split()
        
        if len(words) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(words):
            # Calculate end position
            end = min(start + chunk_size, len(words))
            
            # Extract chunk words
            chunk_words = words[start:end]
            chunk_text = ' '.join(chunk_words)
            
            # Try to break at sentence boundaries if possible
            if end < len(words):  # Not the last chunk
                chunk_text = self._adjust_chunk_boundary(chunk_text)
            
            chunks.append(chunk_text)
            
            # Move start position with overlap
            if end >= len(words):
                break
            
            start = end - overlap
        
        return chunks
    
    def _adjust_chunk_boundary(self, chunk_text: str) -> str:
        """
        Adjust chunk boundary to break at sentence or paragraph boundaries.
        
        Args:
            chunk_text: Raw chunk text
            
        Returns:
            Adjusted chunk text
        """
        # Try to break at paragraph boundaries first
        paragraphs = chunk_text.split('\n\n')
        if len(paragraphs) > 1:
            # Keep all but the last incomplete paragraph
            return '\n\n'.join(paragraphs[:-1])
        
        # Try to break at sentence boundaries
        sentences = re.split(r'[.!?]+\s+', chunk_text)
        if len(sentences) > 1:
            # Keep all but the last incomplete sentence
            complete_sentences = sentences[:-1]
            # Add back the punctuation
            result = '. '.join(complete_sentences)
            if result and not result.endswith(('.', '!', '?')):
                result += '.'
            return result
        
        # If no good break point found, return original
        return chunk_text
    
    def _post_process_chunks(self, chunks: List[str]) -> List[str]:
        """
        Post-process chunks to clean up and validate.
        
        Args:
            chunks: Raw chunks
            
        Returns:
            Cleaned chunks
        """
        processed_chunks = []
        
        for chunk in chunks:
            # Strip whitespace
            chunk = chunk.strip()
            
            # Skip empty chunks
            if not chunk:
                continue
            
            # Skip chunks that are too short to be meaningful
            if len(chunk.split()) < 3:
                continue
            
            processed_chunks.append(chunk)
        
        return processed_chunks
    
    def chunk_with_metadata(
        self,
        text: str,
        chunk_size: int = 400,
        overlap: int = 50,
        source_metadata: dict = None,
    ) -> List[dict]:
        """
        Chunk text and return with metadata.
        
        Args:
            text: Text to chunk
            chunk_size: Maximum tokens per chunk
            overlap: Token overlap between chunks
            source_metadata: Metadata to include with each chunk
            
        Returns:
            List of dictionaries with chunk text and metadata
        """
        chunks = self.chunk_text(text, chunk_size, overlap)
        
        result = []
        for i, chunk in enumerate(chunks):
            chunk_data = {
                'text': chunk,
                'chunk_index': i,
                'token_count': self.count_tokens(chunk),
                'character_count': len(chunk),
                'word_count': len(chunk.split()),
            }
            
            # Add source metadata if provided
            if source_metadata:
                chunk_data.update(source_metadata)
            
            result.append(chunk_data)
        
        return result
    
    def estimate_chunks(self, text: str, chunk_size: int = 400, overlap: int = 50) -> int:
        """
        Estimate number of chunks without actually chunking.
        
        Args:
            text: Text to analyze
            chunk_size: Maximum tokens per chunk
            overlap: Token overlap between chunks
            
        Returns:
            Estimated number of chunks
        """
        total_tokens = self.count_tokens(text)
        
        if total_tokens <= chunk_size:
            return 1
        
        # Calculate with overlap
        effective_chunk_size = chunk_size - overlap
        estimated_chunks = (total_tokens - overlap) // effective_chunk_size + 1
        
        return max(1, estimated_chunks)
