"""
Core data models for the document ingestion system.

This module defines the fundamental data structures used throughout the ingestion pipeline.
All models use Pydantic for validation and serialization.
"""

from __future__ import annotations

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

import numpy as np
from pydantic import BaseModel, Field, validator


class LanguageCode(str, Enum):
    """ISO 639-1 language codes for supported languages."""
    
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    ITALIAN = "it"
    PORTUGUESE = "pt"
    RUSSIAN = "ru"
    CHINESE = "zh"
    JAPANESE = "ja"
    KOREAN = "ko"
    ARABIC = "ar"
    HINDI = "hi"
    UNKNOWN = "unknown"


class FileType(str, Enum):
    """Supported file types for ingestion."""
    
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    PPTX = "pptx"
    PPT = "ppt"
    CSV = "csv"
    XLSX = "xlsx"
    XLS = "xls"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    HTM = "htm"
    JPG = "jpg"
    JPEG = "jpeg"
    PNG = "png"
    WEBP = "webp"
    GIF = "gif"
    ZIP = "zip"
    UNKNOWN = "unknown"


class ChartType(str, Enum):
    """Types of charts that can be detected and extracted."""
    
    BAR = "bar"
    LINE = "line"
    PIE = "pie"
    SCATTER = "scatter"
    AREA = "area"
    HISTOGRAM = "histogram"
    BOX = "box"
    HEATMAP = "heatmap"
    TABLE = "table"
    DIAGRAM = "diagram"
    UNKNOWN = "unknown"


class ChartCaption(BaseModel):
    """AI-generated caption for an extracted chart."""
    
    chart_type: ChartType = ChartType.UNKNOWN
    description: str = Field(..., min_length=1, max_length=2000)
    data_insights: List[str] = Field(default_factory=list)
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    extraction_method: str = Field(default="unknown")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class DocumentChunk(BaseModel):
    """A chunk of processed document content with metadata."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    text: str = Field(..., min_length=1)
    source: str = Field(..., min_length=1)
    file_type: FileType = FileType.UNKNOWN
    language: LanguageCode = LanguageCode.UNKNOWN
    start_page: Optional[int] = Field(default=None, ge=1)
    end_page: Optional[int] = Field(default=None, ge=1)
    chunk_index: int = Field(default=0, ge=0)
    token_count: int = Field(default=0, ge=0)
    chart_captions: List[ChartCaption] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('end_page')
    def validate_page_range(cls, v, values):
        """Ensure end_page >= start_page if both are provided."""
        if v is not None and values.get('start_page') is not None:
            if v < values['start_page']:
                raise ValueError("end_page must be >= start_page")
        return v
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class VectorEntry(BaseModel):
    """A vector embedding with associated metadata for storage in FAISS."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    vector: np.ndarray = Field(..., description="Dense vector embedding")
    chunk_id: str = Field(..., min_length=1)
    source_file: str = Field(..., min_length=1)
    page_number: Optional[int] = Field(default=None, ge=1)
    language: LanguageCode = LanguageCode.UNKNOWN
    chart_caption: Optional[str] = Field(default=None)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            np.ndarray: lambda v: v.tolist(),
        }


class ProcessingResult(BaseModel):
    """Result of processing a single file."""
    
    success: bool = True
    file_path: str = Field(..., min_length=1)
    file_type: FileType = FileType.UNKNOWN
    chunks: List[DocumentChunk] = Field(default_factory=list)
    error_message: Optional[str] = None
    processing_time: float = Field(default=0.0, ge=0.0)
    charts_extracted: int = Field(default=0, ge=0)
    pages_processed: int = Field(default=0, ge=0)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class IngestionConfig(BaseModel):
    """Configuration for the ingestion process with maximum OpenAI integration."""

    # OpenAI API Configuration
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-4o"
    openai_vision_model: str = "gpt-4o"
    openai_embedding_model: str = "text-embedding-3-small"

    # AI Features (all enabled by default for maximum OpenAI usage)
    extract_charts: bool = True
    translate: bool = True
    target_language: LanguageCode = LanguageCode.ENGLISH
    use_gpt_for_text_analysis: bool = True
    use_gpt_for_summarization: bool = True
    use_gpt_for_classification: bool = True
    use_gpt_for_metadata: bool = True
    use_gpt_for_quality_check: bool = True
    use_gpt_for_content_enhancement: bool = True

    # Vector and Embedding Configuration (maximum OpenAI vector usage)
    generate_embeddings: bool = True
    embedding_batch_size: int = 100
    vector_similarity_threshold: float = 0.7
    use_semantic_search: bool = True
    store_vectors: bool = True

    # Processing Parameters
    chunk_size: int = Field(default=400, ge=50, le=2000)
    chunk_overlap: int = Field(default=50, ge=0, le=500)
    max_file_size_mb: int = Field(default=100, ge=1, le=1000)
    timeout_seconds: int = Field(default=30, ge=5, le=300)
    concurrent_workers: int = Field(default=4, ge=1, le=16)
    
    @validator('chunk_overlap')
    def validate_chunk_overlap(cls, v, values):
        """Ensure chunk_overlap < chunk_size."""
        if 'chunk_size' in values and v >= values['chunk_size']:
            raise ValueError("chunk_overlap must be less than chunk_size")
        return v
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
