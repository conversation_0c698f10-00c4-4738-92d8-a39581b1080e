"""
Abstract interfaces for the document ingestion system.

This module defines the contracts that all components must implement,
ensuring modularity and testability.
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Optional, Tuple

import numpy as np
from PIL import Image

from .models import (
    ChartCaption,
    FileType,
    IngestionConfig,
    LanguageCode,
    ProcessingResult,
    VectorEntry,
)


class FileHandler(ABC):
    """Abstract base class for file type handlers."""
    
    @property
    @abstractmethod
    def supported_types(self) -> List[FileType]:
        """Return list of supported file types."""
        pass
    
    @abstractmethod
    def can_handle(self, file_path: Path, mime_type: str) -> bool:
        """Check if this handler can process the given file."""
        pass
    
    @abstractmethod
    def extract_content(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> ProcessingResult:
        """Extract content from the file."""
        pass
    
    @abstractmethod
    def validate_file(self, file_path: Path) -> bool:
        """Validate that the file is not corrupted and can be processed."""
        pass


class ChartExtractor(ABC):
    """Abstract base class for chart extraction from documents."""
    
    @abstractmethod
    def detect_charts(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """
        Detect and extract chart images from a document.
        
        Returns:
            List of tuples containing (chart_image, metadata)
        """
        pass
    
    @abstractmethod
    def extract_chart_from_image(self, image: Image.Image) -> Optional[Image.Image]:
        """Extract chart region from a larger image."""
        pass
    
    @abstractmethod
    def generate_caption(
        self,
        chart_image: Image.Image,
        context: str = "",
    ) -> ChartCaption:
        """Generate AI-powered caption for a chart image."""
        pass


class OCRProcessor(ABC):
    """Abstract base class for OCR processing."""
    
    @abstractmethod
    def extract_text(self, image: Image.Image) -> str:
        """Extract text from an image using OCR."""
        pass
    
    @abstractmethod
    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR results."""
        pass
    
    @abstractmethod
    def detect_language(self, text: str) -> LanguageCode:
        """Detect the language of extracted text."""
        pass


class LanguageProcessor(ABC):
    """Abstract base class for language processing and translation."""
    
    @abstractmethod
    def detect_language(self, text: str) -> LanguageCode:
        """Detect the language of the given text."""
        pass
    
    @abstractmethod
    def translate_text(
        self,
        text: str,
        target_language: LanguageCode,
        source_language: Optional[LanguageCode] = None,
    ) -> str:
        """Translate text to the target language."""
        pass
    
    @abstractmethod
    def is_translation_needed(
        self,
        source_language: LanguageCode,
        target_language: LanguageCode,
    ) -> bool:
        """Check if translation is needed."""
        pass


class TextChunker(ABC):
    """Abstract base class for text chunking."""
    
    @abstractmethod
    def chunk_text(
        self,
        text: str,
        chunk_size: int = 400,
        overlap: int = 50,
    ) -> List[str]:
        """Split text into overlapping chunks."""
        pass
    
    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """Count tokens in the given text."""
        pass


class VectorEmbedder(ABC):
    """Abstract base class for generating vector embeddings."""
    
    @abstractmethod
    def embed_text(self, text: str) -> np.ndarray:
        """Generate vector embedding for text."""
        pass
    
    @abstractmethod
    def embed_batch(self, texts: List[str]) -> List[np.ndarray]:
        """Generate vector embeddings for a batch of texts."""
        pass
    
    @property
    @abstractmethod
    def embedding_dimension(self) -> int:
        """Return the dimension of the embeddings."""
        pass


class VectorStorage(ABC):
    """Abstract base class for vector storage and retrieval."""
    
    @abstractmethod
    def store_vectors(self, vectors: List[VectorEntry]) -> bool:
        """Store vector entries in the index."""
        pass
    
    @abstractmethod
    def search_similar(
        self,
        query_vector: np.ndarray,
        top_k: int = 10,
    ) -> List[Tuple[VectorEntry, float]]:
        """Search for similar vectors."""
        pass
    
    @abstractmethod
    def get_index_stats(self) -> dict:
        """Get statistics about the vector index."""
        pass
    
    @abstractmethod
    def save_index(self, path: Path) -> bool:
        """Save the index to disk."""
        pass
    
    @abstractmethod
    def load_index(self, path: Path) -> bool:
        """Load the index from disk."""
        pass


class FileValidator(ABC):
    """Abstract base class for file validation."""
    
    @abstractmethod
    def validate_file_size(self, file_path: Path, max_size_mb: int) -> bool:
        """Validate file size is within limits."""
        pass
    
    @abstractmethod
    def validate_file_type(self, file_path: Path, allowed_types: List[FileType]) -> bool:
        """Validate file type is supported."""
        pass
    
    @abstractmethod
    def validate_file_integrity(self, file_path: Path) -> bool:
        """Validate file is not corrupted."""
        pass
    
    @abstractmethod
    def get_file_info(self, file_path: Path) -> dict:
        """Get comprehensive file information."""
        pass
