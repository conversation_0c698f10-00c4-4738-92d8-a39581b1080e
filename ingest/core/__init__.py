"""Core interfaces and models for the document ingestion system."""

from .exceptions import (
    ChartExtractionError,
    ChunkingError,
    ConfigurationError,
    EmbeddingError,
    ExtractionError,
    FileCorruptedError,
    FileNotFoundError,
    FileSizeExceededError,
    IngestionError,
    LanguageDetectionError,
    NetworkError,
    OCRError,
    TimeoutError,
    TranslationError,
    UnsupportedFileTypeError,
    ValidationError,
    VectorStorageError,
)
from .interfaces import (
    ChartExtractor,
    FileHandler,
    FileValidator,
    LanguageProcessor,
    OCRProcessor,
    TextChunker,
    VectorEmbedder,
    VectorStorage,
)
from .models import (
    ChartCaption,
    ChartType,
    DocumentChunk,
    FileType,
    IngestionConfig,
    LanguageCode,
    ProcessingResult,
    VectorEntry,
)

__all__ = [
    # Models
    "DocumentChunk",
    "VectorEntry",
    "ChartCaption",
    "ChartType",
    "FileType",
    "LanguageCode",
    "ProcessingResult",
    "IngestionConfig",
    # Interfaces
    "FileHandler",
    "ChartExtractor",
    "OCRProcessor",
    "LanguageProcessor",
    "TextChunker",
    "VectorEmbedder",
    "VectorStorage",
    "FileValidator",
    # Exceptions
    "IngestionError",
    "FileNotFoundError",
    "UnsupportedFileTypeError",
    "FileCorruptedError",
    "FileSizeExceededError",
    "ExtractionError",
    "ChartExtractionError",
    "OCRError",
    "LanguageDetectionError",
    "TranslationError",
    "EmbeddingError",
    "VectorStorageError",
    "ConfigurationError",
    "TimeoutError",
    "NetworkError",
    "ValidationError",
    "ChunkingError",
]
