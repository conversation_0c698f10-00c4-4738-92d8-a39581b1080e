"""
Custom exceptions for the document ingestion system.

This module defines specific exceptions that provide clear error handling
and debugging information throughout the ingestion pipeline.
"""

from typing import Optional


class IngestionError(Exception):
    """Base exception for all ingestion-related errors."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, cause: Optional[Exception] = None):
        self.message = message
        self.file_path = file_path
        self.cause = cause
        super().__init__(self.message)
    
    def __str__(self) -> str:
        parts = [self.message]
        if self.file_path:
            parts.append(f"File: {self.file_path}")
        if self.cause:
            parts.append(f"Caused by: {self.cause}")
        return " | ".join(parts)


class FileNotFoundError(IngestionError):
    """Raised when a file cannot be found."""
    pass


class UnsupportedFileTypeError(IngestionError):
    """Raised when a file type is not supported."""
    pass


class FileCorruptedError(IngestionError):
    """Raised when a file is corrupted or cannot be read."""
    pass


class FileSizeExceededError(IngestionError):
    """Raised when a file exceeds the maximum allowed size."""
    pass


class ExtractionError(IngestionError):
    """Raised when content extraction fails."""
    pass


class ChartExtractionError(IngestionError):
    """Raised when chart extraction fails."""
    pass


class OCRError(IngestionError):
    """Raised when OCR processing fails."""
    pass


class LanguageDetectionError(IngestionError):
    """Raised when language detection fails."""
    pass


class TranslationError(IngestionError):
    """Raised when translation fails."""
    pass


class EmbeddingError(IngestionError):
    """Raised when vector embedding generation fails."""
    pass


class VectorStorageError(IngestionError):
    """Raised when vector storage operations fail."""
    pass


class ConfigurationError(IngestionError):
    """Raised when configuration is invalid."""
    pass


class TimeoutError(IngestionError):
    """Raised when an operation times out."""
    pass


class NetworkError(IngestionError):
    """Raised when network operations fail (e.g., S3 download)."""
    pass


class ValidationError(IngestionError):
    """Raised when data validation fails."""
    pass


class ChunkingError(IngestionError):
    """Raised when text chunking fails."""
    pass
