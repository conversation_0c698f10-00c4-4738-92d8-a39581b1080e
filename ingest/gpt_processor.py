"""
GPT-powered content processor for maximum OpenAI integration.

This module provides comprehensive content processing using OpenAI GPT models
for text analysis, summarization, classification, metadata generation, and more.
"""

from typing import Dict, Optional

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from .core import DocumentChunk
from .utils import get_logger

logger = get_logger(__name__)


class GPTContentProcessor:
    """
    GPT-powered content processor for comprehensive document analysis.
    
    Uses OpenAI GPT models for:
    - Content analysis and understanding
    - Text summarization
    - Content classification
    - Metadata generation
    - Quality assessment
    - Content enhancement
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gpt-4o",
        max_tokens: int = 4000,
    ):
        """
        Initialize GPT content processor.
        
        Args:
            api_key: OpenAI API key
            model: GPT model to use
            max_tokens: Maximum tokens per request
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI library not available. Install with: pip install openai")
        
        self.model = model
        self.max_tokens = max_tokens
        
        # Initialize OpenAI client
        try:
            if api_key:
                self.client = openai.OpenAI(api_key=api_key)
            else:
                self.client = openai.OpenAI()  # Uses OPENAI_API_KEY env var
            
            logger.info(
                "GPT content processor initialized",
                model=model,
                max_tokens=max_tokens,
            )
            
        except Exception as e:
            raise RuntimeError(f"Failed to initialize OpenAI client: {e}")
    
    def analyze_content(self, text: str) -> Dict[str, any]:
        """
        Comprehensive content analysis using GPT.
        
        Args:
            text: Text content to analyze
            
        Returns:
            Dictionary with analysis results
        """
        try:
            prompt = f"""
Analyze the following text content and provide a comprehensive analysis in JSON format:

Text to analyze:
{text[:3000]}...

Please provide analysis in this exact JSON structure:
{{
    "content_type": "document_type (e.g., business_report, academic_paper, manual, etc.)",
    "main_topics": ["topic1", "topic2", "topic3"],
    "key_entities": ["entity1", "entity2", "entity3"],
    "sentiment": "positive/negative/neutral",
    "complexity_level": "basic/intermediate/advanced",
    "language_style": "formal/informal/technical",
    "primary_purpose": "inform/persuade/instruct/entertain",
    "target_audience": "general/professional/academic/technical",
    "key_insights": ["insight1", "insight2", "insight3"],
    "confidence_score": 0.85
}}

Respond only with valid JSON.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert content analyst. Provide detailed, accurate analysis in the requested JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=0.1,
            )
            
            analysis_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            import json
            analysis = json.loads(analysis_text)
            
            logger.info(
                "Content analysis completed",
                content_type=analysis.get("content_type"),
                confidence=analysis.get("confidence_score"),
                text_length=len(text),
            )
            
            return analysis
            
        except Exception as e:
            logger.error(
                "Content analysis failed",
                error=str(e),
                text_length=len(text),
            )
            return {
                "content_type": "unknown",
                "main_topics": [],
                "key_entities": [],
                "sentiment": "neutral",
                "complexity_level": "unknown",
                "language_style": "unknown",
                "primary_purpose": "unknown",
                "target_audience": "unknown",
                "key_insights": [],
                "confidence_score": 0.0,
                "error": str(e)
            }
    
    def generate_summary(self, text: str, max_length: int = 200) -> str:
        """
        Generate intelligent summary using GPT.
        
        Args:
            text: Text to summarize
            max_length: Maximum summary length in words
            
        Returns:
            Generated summary
        """
        try:
            prompt = f"""
Create a comprehensive yet concise summary of the following text in approximately {max_length} words.
Focus on the most important information, key points, and main conclusions.

Text to summarize:
{text[:4000]}...

Summary:
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at creating clear, informative summaries that capture the essence of documents."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=min(self.max_tokens, max_length * 2),
                temperature=0.2,
            )
            
            summary = response.choices[0].message.content.strip()
            
            logger.info(
                "Summary generated",
                original_length=len(text),
                summary_length=len(summary),
                target_words=max_length,
            )
            
            return summary
            
        except Exception as e:
            logger.error(
                "Summary generation failed",
                error=str(e),
                text_length=len(text),
            )
            return f"Summary generation failed: {str(e)}"
    
    def classify_content(self, text: str) -> Dict[str, any]:
        """
        Classify content using GPT.
        
        Args:
            text: Text to classify
            
        Returns:
            Classification results
        """
        try:
            prompt = f"""
Classify the following text content and provide classification in JSON format:

Text to classify:
{text[:2000]}...

Provide classification in this exact JSON structure:
{{
    "document_category": "business/academic/technical/legal/medical/other",
    "content_format": "report/manual/article/presentation/correspondence/other",
    "industry_sector": "technology/finance/healthcare/education/manufacturing/other",
    "urgency_level": "low/medium/high",
    "action_required": "none/review/approval/implementation",
    "classification_confidence": 0.85,
    "tags": ["tag1", "tag2", "tag3"]
}}

Respond only with valid JSON.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert document classifier. Provide accurate classification in the requested JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=500,
                temperature=0.1,
            )
            
            classification_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            import json
            classification = json.loads(classification_text)
            
            logger.info(
                "Content classification completed",
                category=classification.get("document_category"),
                confidence=classification.get("classification_confidence"),
            )
            
            return classification
            
        except Exception as e:
            logger.error(
                "Content classification failed",
                error=str(e),
                text_length=len(text),
            )
            return {
                "document_category": "other",
                "content_format": "other",
                "industry_sector": "other",
                "urgency_level": "low",
                "action_required": "none",
                "classification_confidence": 0.0,
                "tags": [],
                "error": str(e)
            }
    
    def generate_metadata(self, text: str, source_file: str) -> Dict[str, any]:
        """
        Generate comprehensive metadata using GPT.
        
        Args:
            text: Text content
            source_file: Source file name
            
        Returns:
            Generated metadata
        """
        try:
            prompt = f"""
Generate comprehensive metadata for the following document content:

Source file: {source_file}
Content preview:
{text[:2000]}...

Provide metadata in this exact JSON structure:
{{
    "title": "Extracted or inferred document title",
    "author": "Author name if mentioned",
    "creation_date": "Date if mentioned (YYYY-MM-DD format)",
    "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
    "description": "Brief description of the document",
    "subject_matter": "Main subject or topic",
    "document_purpose": "Purpose of the document",
    "reading_time_minutes": 5,
    "word_count_estimate": 1000,
    "complexity_score": 0.7,
    "relevance_tags": ["tag1", "tag2", "tag3"]
}}

Respond only with valid JSON.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at generating comprehensive document metadata. Be accurate and thorough."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=800,
                temperature=0.2,
            )
            
            metadata_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            import json
            metadata = json.loads(metadata_text)
            
            logger.info(
                "Metadata generation completed",
                title=metadata.get("title", "Unknown")[:50],
                keywords_count=len(metadata.get("keywords", [])),
            )
            
            return metadata
            
        except Exception as e:
            logger.error(
                "Metadata generation failed",
                error=str(e),
                source_file=source_file,
            )
            return {
                "title": f"Document from {source_file}",
                "author": "Unknown",
                "creation_date": None,
                "keywords": [],
                "description": "Metadata generation failed",
                "subject_matter": "Unknown",
                "document_purpose": "Unknown",
                "reading_time_minutes": 0,
                "word_count_estimate": len(text.split()),
                "complexity_score": 0.5,
                "relevance_tags": [],
                "error": str(e)
            }
    
    def enhance_chunk(self, chunk: DocumentChunk) -> DocumentChunk:
        """
        Enhance document chunk with GPT-powered analysis.
        
        Args:
            chunk: Document chunk to enhance
            
        Returns:
            Enhanced document chunk
        """
        try:
            # Generate comprehensive analysis
            analysis = self.analyze_content(chunk.text)
            classification = self.classify_content(chunk.text)
            metadata = self.generate_metadata(chunk.text, chunk.source)
            summary = self.generate_summary(chunk.text, max_length=100)
            
            # Update chunk metadata with GPT analysis
            enhanced_metadata = {
                **chunk.metadata,
                "gpt_analysis": analysis,
                "gpt_classification": classification,
                "gpt_metadata": metadata,
                "gpt_summary": summary,
                "enhanced_by_gpt": True,
                "gpt_model": self.model,
            }
            
            # Create enhanced chunk
            enhanced_chunk = DocumentChunk(
                text=chunk.text,
                source=chunk.source,
                file_type=chunk.file_type,
                language=chunk.language,
                start_page=chunk.start_page,
                end_page=chunk.end_page,
                chunk_index=chunk.chunk_index,
                token_count=chunk.token_count,
                chart_captions=chunk.chart_captions,
                metadata=enhanced_metadata,
            )
            
            logger.info(
                "Chunk enhanced with GPT analysis",
                chunk_id=chunk.id,
                content_type=analysis.get("content_type"),
                confidence=analysis.get("confidence_score"),
            )
            
            return enhanced_chunk
            
        except Exception as e:
            logger.error(
                "Chunk enhancement failed",
                chunk_id=chunk.id,
                error=str(e),
            )
            return chunk
