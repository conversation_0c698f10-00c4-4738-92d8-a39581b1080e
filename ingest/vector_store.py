"""
Vector storage and embedding system using OpenAI and FAISS.

This module provides vector embedding generation and storage capabilities
for document chunks using OpenAI's text-embedding models and FAISS indexing.
"""

import json
from pathlib import Path
from typing import List, Optional, Tuple

import numpy as np

try:
    import faiss
    import openai
    VECTOR_LIBS_AVAILABLE = True
except ImportError:
    VECTOR_LIBS_AVAILABLE = False

from .core import (
    DocumentChunk,
    EmbeddingError,
    VectorEntry,
    VectorEmbedder,
    VectorStorage,
    VectorStorageError,
)
from .ocr import TextChunkerImpl
from .utils import get_logger

logger = get_logger(__name__)


class OpenAIEmbedder(VectorEmbedder):
    """OpenAI text embedding implementation."""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "text-embedding-3-small",
        batch_size: int = 100,
    ):
        """
        Initialize OpenAI embedder.
        
        Args:
            api_key: OpenAI API key
            model: Embedding model to use
            batch_size: Batch size for API calls
        """
        if not VECTOR_LIBS_AVAILABLE:
            raise EmbeddingError("Required libraries not available. Install with: pip install openai faiss-cpu")
        
        self.model = model
        self.batch_size = batch_size
        
        # Initialize OpenAI client
        try:
            if api_key:
                self.client = openai.OpenAI(api_key=api_key)
            else:
                self.client = openai.OpenAI()  # Uses OPENAI_API_KEY env var
            
            # Test the connection and get embedding dimension
            test_response = self.client.embeddings.create(
                model=self.model,
                input=["test"],
            )
            self._embedding_dimension = len(test_response.data[0].embedding)
            
            logger.info(
                "OpenAI embedder initialized",
                model=self.model,
                embedding_dimension=self._embedding_dimension,
                batch_size=self.batch_size,
            )
            
        except Exception as e:
            raise EmbeddingError(
                f"Failed to initialize OpenAI embedder: {e}",
                cause=e,
            )
    
    def embed_text(self, text: str) -> np.ndarray:
        """
        Generate vector embedding for text.
        
        Args:
            text: Text to embed
            
        Returns:
            Vector embedding as numpy array
        """
        try:
            response = self.client.embeddings.create(
                model=self.model,
                input=[text],
            )
            
            embedding = np.array(response.data[0].embedding, dtype=np.float32)
            return embedding
            
        except Exception as e:
            raise EmbeddingError(
                f"Failed to generate embedding: {e}",
                cause=e,
            )
    
    def embed_batch(self, texts: List[str]) -> List[np.ndarray]:
        """
        Generate vector embeddings for a batch of texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of vector embeddings
        """
        try:
            embeddings = []
            
            # Process in batches
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                
                response = self.client.embeddings.create(
                    model=self.model,
                    input=batch,
                )
                
                batch_embeddings = [
                    np.array(item.embedding, dtype=np.float32)
                    for item in response.data
                ]
                embeddings.extend(batch_embeddings)
            
            logger.info(
                "Batch embedding completed",
                total_texts=len(texts),
                batches_processed=(len(texts) + self.batch_size - 1) // self.batch_size,
            )
            
            return embeddings
            
        except Exception as e:
            raise EmbeddingError(
                f"Failed to generate batch embeddings: {e}",
                cause=e,
            )
    
    @property
    def embedding_dimension(self) -> int:
        """Return the dimension of the embeddings."""
        return self._embedding_dimension


class FAISSVectorStorage(VectorStorage):
    """FAISS-based vector storage implementation."""
    
    def __init__(self, index_path: Optional[Path] = None, dimension: int = 1536):
        """
        Initialize FAISS vector storage.
        
        Args:
            index_path: Path to store/load the index
            dimension: Dimension of the vectors
        """
        if not VECTOR_LIBS_AVAILABLE:
            raise VectorStorageError("FAISS not available. Install with: pip install faiss-cpu")
        
        self.index_path = index_path
        self.dimension = dimension
        
        # Initialize FAISS index
        self.index = faiss.IndexFlatIP(dimension)  # Inner product (cosine similarity)
        self.metadata_store = []  # Store metadata separately
        
        # Load existing index if path provided
        if index_path and index_path.exists():
            self.load_index(index_path)
        
        logger.info(
            "FAISS vector storage initialized",
            dimension=dimension,
            index_path=str(index_path) if index_path else None,
            vectors_loaded=self.index.ntotal,
        )
    
    def store_vectors(self, vectors: List[VectorEntry]) -> bool:
        """
        Store vector entries in the index.
        
        Args:
            vectors: List of vector entries to store
            
        Returns:
            True if storage was successful
        """
        try:
            if not vectors:
                return True
            
            # Prepare vectors and metadata
            vector_array = np.vstack([v.vector for v in vectors]).astype(np.float32)
            
            # Normalize vectors for cosine similarity
            faiss.normalize_L2(vector_array)
            
            # Add to index
            self.index.add(vector_array)
            
            # Store metadata
            for vector in vectors:
                metadata = {
                    'id': vector.id,
                    'chunk_id': vector.chunk_id,
                    'source_file': vector.source_file,
                    'page_number': vector.page_number,
                    'language': vector.language,
                    'chart_caption': vector.chart_caption,
                    'metadata': vector.metadata,
                    'created_at': vector.created_at.isoformat(),
                }
                self.metadata_store.append(metadata)
            
            logger.info(
                "Vectors stored successfully",
                vectors_added=len(vectors),
                total_vectors=self.index.ntotal,
            )
            
            return True
            
        except Exception as e:
            raise VectorStorageError(
                f"Failed to store vectors: {e}",
                cause=e,
            )
    
    def search_similar(
        self,
        query_vector: np.ndarray,
        top_k: int = 10,
    ) -> List[Tuple[VectorEntry, float]]:
        """
        Search for similar vectors.
        
        Args:
            query_vector: Query vector
            top_k: Number of results to return
            
        Returns:
            List of tuples (VectorEntry, similarity_score)
        """
        try:
            if self.index.ntotal == 0:
                return []
            
            # Normalize query vector
            query_vector = query_vector.astype(np.float32).reshape(1, -1)
            faiss.normalize_L2(query_vector)
            
            # Search
            scores, indices = self.index.search(query_vector, min(top_k, self.index.ntotal))
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and idx < len(self.metadata_store):
                    metadata = self.metadata_store[idx]
                    
                    # Reconstruct VectorEntry (without the actual vector)
                    vector_entry = VectorEntry(
                        id=metadata['id'],
                        vector=np.array([]),  # Empty vector to save memory
                        chunk_id=metadata['chunk_id'],
                        source_file=metadata['source_file'],
                        page_number=metadata['page_number'],
                        language=metadata['language'],
                        chart_caption=metadata['chart_caption'],
                        metadata=metadata['metadata'],
                    )
                    
                    results.append((vector_entry, float(score)))
            
            logger.debug(
                "Vector search completed",
                query_dimension=query_vector.shape[1],
                results_found=len(results),
                top_k=top_k,
            )
            
            return results
            
        except Exception as e:
            raise VectorStorageError(
                f"Vector search failed: {e}",
                cause=e,
            )
    
    def get_index_stats(self) -> dict:
        """Get statistics about the vector index."""
        return {
            'total_vectors': self.index.ntotal,
            'dimension': self.dimension,
            'index_type': type(self.index).__name__,
            'metadata_entries': len(self.metadata_store),
            'index_path': str(self.index_path) if self.index_path else None,
        }
    
    def save_index(self, path: Path) -> bool:
        """
        Save the index to disk.
        
        Args:
            path: Path to save the index
            
        Returns:
            True if save was successful
        """
        try:
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save FAISS index
            faiss.write_index(self.index, str(path / "index.faiss"))
            
            # Save metadata
            with open(path / "metadata.json", 'w') as f:
                json.dump(self.metadata_store, f, indent=2)
            
            # Save configuration
            config = {
                'dimension': self.dimension,
                'total_vectors': self.index.ntotal,
                'index_type': type(self.index).__name__,
            }
            with open(path / "config.json", 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(
                "Index saved successfully",
                path=str(path),
                total_vectors=self.index.ntotal,
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to save index",
                path=str(path),
                error=str(e),
            )
            return False
    
    def load_index(self, path: Path) -> bool:
        """
        Load the index from disk.
        
        Args:
            path: Path to load the index from
            
        Returns:
            True if load was successful
        """
        try:
            # Load FAISS index
            index_file = path / "index.faiss"
            if index_file.exists():
                self.index = faiss.read_index(str(index_file))
            
            # Load metadata
            metadata_file = path / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    self.metadata_store = json.load(f)
            
            # Load configuration
            config_file = path / "config.json"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    self.dimension = config.get('dimension', self.dimension)
            
            logger.info(
                "Index loaded successfully",
                path=str(path),
                total_vectors=self.index.ntotal,
                metadata_entries=len(self.metadata_store),
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to load index",
                path=str(path),
                error=str(e),
            )
            return False


class VectorStore:
    """
    High-level vector store interface with maximum OpenAI integration.

    This class provides comprehensive vector operations using OpenAI embeddings
    with advanced semantic search and GPT-powered query enhancement.
    """

    def __init__(
        self,
        index_path: Optional[Path] = None,
        openai_api_key: Optional[str] = None,
        embedding_model: str = "text-embedding-3-small",
        gpt_model: str = "gpt-4o",
    ):
        """
        Initialize vector store with maximum OpenAI integration.

        Args:
            index_path: Path to store the vector index
            openai_api_key: OpenAI API key
            embedding_model: OpenAI embedding model to use
            gpt_model: GPT model for query enhancement
        """
        self.embedder = OpenAIEmbedder(api_key=openai_api_key, model=embedding_model)
        self.storage = FAISSVectorStorage(
            index_path=index_path,
            dimension=self.embedder.embedding_dimension,
        )
        self.chunker = TextChunkerImpl()

        # Initialize GPT for query enhancement
        try:
            if openai_api_key:
                self.openai_client = openai.OpenAI(api_key=openai_api_key)
            else:
                self.openai_client = openai.OpenAI()
            self.gpt_model = gpt_model
            self.gpt_available = True
        except Exception as e:
            logger.warning(f"GPT not available for query enhancement: {e}")
            self.gpt_available = False

        logger.info(
            "Vector store initialized with maximum OpenAI integration",
            embedding_model=embedding_model,
            gpt_model=gpt_model,
            embedding_dimension=self.embedder.embedding_dimension,
            index_path=str(index_path) if index_path else None,
            gpt_available=self.gpt_available,
        )
    
    def embed_chunks(self, chunks: List[DocumentChunk]) -> List[VectorEntry]:
        """
        Generate embeddings for document chunks.
        
        Args:
            chunks: List of document chunks
            
        Returns:
            List of vector entries
        """
        try:
            # Extract text from chunks
            texts = [chunk.text for chunk in chunks]
            
            # Generate embeddings
            embeddings = self.embedder.embed_batch(texts)
            
            # Create vector entries
            vector_entries = []
            for chunk, embedding in zip(chunks, embeddings):
                vector_entry = VectorEntry(
                    vector=embedding,
                    chunk_id=chunk.id,
                    source_file=chunk.source,
                    page_number=chunk.start_page,
                    language=chunk.language,
                    chart_caption=chunk.chart_captions[0].description if chunk.chart_captions else None,
                    metadata={
                        'file_type': chunk.file_type,
                        'chunk_index': chunk.chunk_index,
                        'token_count': chunk.token_count,
                        **chunk.metadata,
                    },
                )
                vector_entries.append(vector_entry)
            
            logger.info(
                "Chunk embedding completed",
                chunks_processed=len(chunks),
                vectors_created=len(vector_entries),
            )
            
            return vector_entries
            
        except Exception as e:
            raise EmbeddingError(
                f"Failed to embed chunks: {e}",
                cause=e,
            )
    
    def store_in_faiss(self, vectors: List[VectorEntry], path: Optional[Path] = None) -> bool:
        """
        Store vector entries in FAISS index.
        
        Args:
            vectors: List of vector entries
            path: Optional path to save the index
            
        Returns:
            True if storage was successful
        """
        success = self.storage.store_vectors(vectors)
        
        if success and path:
            self.storage.save_index(path)
        
        return success
    
    def search(self, query: str, top_k: int = 10) -> List[Tuple[VectorEntry, float]]:
        """
        Search for similar content using text query with GPT enhancement.

        Args:
            query: Text query
            top_k: Number of results to return

        Returns:
            List of tuples (VectorEntry, similarity_score)
        """
        # Enhance query with GPT if available
        enhanced_query = self.enhance_query_with_gpt(query) if self.gpt_available else query

        # Generate embedding for enhanced query
        query_embedding = self.embedder.embed_text(enhanced_query)

        # Search in vector store
        results = self.storage.search_similar(query_embedding, top_k)

        # Re-rank results using GPT if available
        if self.gpt_available and results:
            results = self.rerank_results_with_gpt(query, results)

        return results
    
    def get_stats(self) -> dict:
        """Get vector store statistics."""
        stats = self.storage.get_index_stats()
        stats.update({
            'gpt_available': self.gpt_available,
            'gpt_model': self.gpt_model if self.gpt_available else None,
            'embedding_model': self.embedder.model,
        })
        return stats

    def enhance_query_with_gpt(self, query: str) -> str:
        """
        Enhance search query using GPT for better semantic matching.

        Args:
            query: Original query

        Returns:
            Enhanced query
        """
        try:
            prompt = f"""
Enhance the following search query to improve semantic search results in a document database.
Add relevant synonyms, related terms, and context that would help find relevant documents.
Keep the enhanced query concise but comprehensive.

Original query: {query}

Enhanced query:"""

            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at enhancing search queries for better document retrieval. Expand queries with relevant terms while maintaining focus."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=200,
                temperature=0.3,
            )

            enhanced_query = response.choices[0].message.content.strip()

            logger.debug(
                "Query enhanced with GPT",
                original_query=query,
                enhanced_query=enhanced_query,
            )

            return enhanced_query

        except Exception as e:
            logger.warning(
                "Query enhancement failed, using original query",
                error=str(e),
                query=query,
            )
            return query

    def rerank_results_with_gpt(
        self,
        query: str,
        results: List[Tuple[VectorEntry, float]]
    ) -> List[Tuple[VectorEntry, float]]:
        """
        Re-rank search results using GPT for better relevance.

        Args:
            query: Original search query
            results: Initial search results

        Returns:
            Re-ranked results
        """
        try:
            if not results:
                return results

            # Prepare content for GPT analysis
            content_snippets = []
            for i, (entry, score) in enumerate(results[:10]):  # Limit to top 10 for GPT
                # Get content from metadata or use chunk_id
                content = entry.metadata.get('text_preview', f"Document chunk {entry.chunk_id}")
                content_snippets.append(f"{i}: {content[:200]}...")

            prompt = f"""
Rank the following document snippets by relevance to the query: "{query}"
Return only the numbers (0-{len(content_snippets)-1}) in order of relevance, separated by commas.

Document snippets:
{chr(10).join(content_snippets)}

Ranking (most relevant first):"""

            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at ranking document relevance. Provide only the ranking numbers."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=100,
                temperature=0.1,
            )

            ranking_text = response.choices[0].message.content.strip()

            # Parse ranking
            try:
                ranking_indices = [int(x.strip()) for x in ranking_text.split(',')]

                # Reorder results based on GPT ranking
                reranked_results = []
                for idx in ranking_indices:
                    if 0 <= idx < len(results):
                        reranked_results.append(results[idx])

                # Add any remaining results
                used_indices = set(ranking_indices)
                for i, result in enumerate(results):
                    if i not in used_indices:
                        reranked_results.append(result)

                logger.debug(
                    "Results re-ranked with GPT",
                    query=query,
                    original_count=len(results),
                    reranked_count=len(reranked_results),
                )

                return reranked_results

            except (ValueError, IndexError) as e:
                logger.warning(
                    "Failed to parse GPT ranking, using original order",
                    error=str(e),
                    ranking_text=ranking_text,
                )
                return results

        except Exception as e:
            logger.warning(
                "Result re-ranking failed, using original order",
                error=str(e),
                query=query,
            )
            return results

    def semantic_search_with_context(
        self,
        query: str,
        context: str = "",
        top_k: int = 10
    ) -> List[Tuple[VectorEntry, float, str]]:
        """
        Perform semantic search with additional context and GPT-generated explanations.

        Args:
            query: Search query
            context: Additional context for the search
            top_k: Number of results to return

        Returns:
            List of tuples (VectorEntry, similarity_score, gpt_explanation)
        """
        # Combine query with context
        enhanced_query = f"{query} {context}".strip()

        # Perform search
        results = self.search(enhanced_query, top_k)

        # Generate explanations with GPT if available
        if self.gpt_available:
            explained_results = []
            for entry, score in results:
                explanation = self.generate_result_explanation(query, entry)
                explained_results.append((entry, score, explanation))
            return explained_results
        else:
            # Return without explanations
            return [(entry, score, "") for entry, score in results]

    def generate_result_explanation(self, query: str, entry: VectorEntry) -> str:
        """
        Generate explanation for why a result is relevant using GPT.

        Args:
            query: Search query
            entry: Vector entry

        Returns:
            Explanation text
        """
        try:
            content = entry.metadata.get('text_preview', f"Content from {entry.source_file}")

            prompt = f"""
Explain in 1-2 sentences why this document excerpt is relevant to the query: "{query}"

Document excerpt:
{content[:500]}...

Explanation:"""

            response = self.openai_client.chat.completions.create(
                model=self.gpt_model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at explaining document relevance. Be concise and specific."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=150,
                temperature=0.2,
            )

            explanation = response.choices[0].message.content.strip()
            return explanation

        except Exception as e:
            logger.warning(
                "Failed to generate result explanation",
                error=str(e),
                query=query,
            )
            return "Relevant content found."
