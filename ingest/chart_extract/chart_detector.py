"""
Chart detection using computer vision techniques.

This module provides chart detection capabilities using OpenCV
for layout analysis and pattern recognition.
"""

from typing import List, <PERSON>ple
import numpy as np

try:
    import cv2
    from PIL import Image
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from ..core import ChartType
from ..utils import get_logger

logger = get_logger(__name__)


class ChartDetector:
    """
    Detects charts and diagrams in images using computer vision.
    
    Uses various techniques including:
    - Edge detection
    - Contour analysis
    - Line detection (Hough transform)
    - Shape analysis
    """
    
    def __init__(self):
        """Initialize chart detector."""
        if not CV2_AVAILABLE:
            logger.warning("OpenCV not available. Chart detection will be limited.")
        
        # Detection parameters
        self.min_chart_area = 5000  # Minimum area for chart detection
        self.edge_threshold_low = 50
        self.edge_threshold_high = 150
        self.hough_threshold = 100
        self.min_line_length = 50
        self.max_line_gap = 10
    
    def detect_charts_in_image(self, image: Image.Image) -> List[Tuple[Image.Image, dict]]:
        """
        Detect charts in an image and return cropped chart regions.
        
        Args:
            image: PIL Image to analyze
            
        Returns:
            List of tuples containing (chart_image, metadata)
        """
        if not CV2_AVAILABLE:
            logger.warning("OpenCV not available, skipping chart detection")
            return []
        
        try:
            # Convert PIL to OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Detect potential chart regions
            chart_regions = self._find_chart_regions(opencv_image)
            
            charts = []
            for i, (x, y, w, h, chart_type, confidence) in enumerate(chart_regions):
                # Crop chart region
                chart_crop = image.crop((x, y, x + w, y + h))
                
                metadata = {
                    'chart_type': chart_type,
                    'confidence': confidence,
                    'bbox': (x, y, w, h),
                    'chart_index': i,
                    'detection_method': 'computer_vision',
                }
                
                charts.append((chart_crop, metadata))
            
            logger.info(
                "Chart detection completed",
                charts_found=len(charts),
                image_size=image.size,
            )
            
            return charts
            
        except Exception as e:
            logger.error(
                "Chart detection failed",
                error=str(e),
                image_size=image.size,
            )
            return []
    
    def _find_chart_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int, ChartType, float]]:
        """
        Find potential chart regions in the image.
        
        Args:
            image: OpenCV image array
            
        Returns:
            List of tuples (x, y, width, height, chart_type, confidence)
        """
        regions = []
        
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply edge detection
            edges = cv2.Canny(gray, self.edge_threshold_low, self.edge_threshold_high)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                
                if area > self.min_chart_area:
                    # Get bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Analyze region to determine chart type
                    chart_type, confidence = self._analyze_chart_region(
                        gray[y:y+h, x:x+w], edges[y:y+h, x:x+w]
                    )
                    
                    if confidence > 0.3:  # Minimum confidence threshold
                        regions.append((x, y, w, h, chart_type, confidence))
            
            # Sort by confidence and return top candidates
            regions.sort(key=lambda x: x[5], reverse=True)
            return regions[:5]  # Return top 5 candidates
            
        except Exception as e:
            logger.warning(
                "Failed to find chart regions",
                error=str(e),
            )
            return []
    
    def _analyze_chart_region(self, gray_region: np.ndarray, edge_region: np.ndarray) -> Tuple[ChartType, float]:
        """
        Analyze a region to determine chart type and confidence.
        
        Args:
            gray_region: Grayscale image region
            edge_region: Edge-detected image region
            
        Returns:
            Tuple of (chart_type, confidence_score)
        """
        try:
            # Detect lines using Hough transform
            lines = cv2.HoughLinesP(
                edge_region,
                1,
                np.pi/180,
                threshold=self.hough_threshold,
                minLineLength=self.min_line_length,
                maxLineGap=self.max_line_gap
            )
            
            # Analyze line patterns
            if lines is not None:
                line_count = len(lines)
                
                # Analyze line orientations
                horizontal_lines = 0
                vertical_lines = 0
                diagonal_lines = 0
                
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
                    
                    if abs(angle) < 15 or abs(angle) > 165:
                        horizontal_lines += 1
                    elif abs(angle - 90) < 15 or abs(angle + 90) < 15:
                        vertical_lines += 1
                    else:
                        diagonal_lines += 1
                
                # Determine chart type based on line patterns
                if horizontal_lines > 2 and vertical_lines > 2:
                    if line_count > 10:
                        return ChartType.BAR, 0.8
                    else:
                        return ChartType.TABLE, 0.7
                elif line_count > 5:
                    return ChartType.LINE, 0.6
            
            # Detect circular patterns (for pie charts)
            circles = cv2.HoughCircles(
                gray_region,
                cv2.HOUGH_GRADIENT,
                dp=1,
                minDist=30,
                param1=50,
                param2=30,
                minRadius=20,
                maxRadius=200
            )
            
            if circles is not None and len(circles[0]) > 0:
                return ChartType.PIE, 0.7
            
            # Detect rectangular patterns
            contours, _ = cv2.findContours(edge_region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            rect_count = 0
            
            for contour in contours:
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                if len(approx) == 4:  # Rectangle
                    rect_count += 1
            
            if rect_count > 3:
                return ChartType.BAR, 0.6
            
            # Default to unknown with low confidence
            return ChartType.UNKNOWN, 0.2
            
        except Exception as e:
            logger.warning(
                "Failed to analyze chart region",
                error=str(e),
            )
            return ChartType.UNKNOWN, 0.0
    
    def is_likely_chart(self, image: Image.Image) -> bool:
        """
        Quick check if image is likely to contain a chart.
        
        Args:
            image: PIL Image to check
            
        Returns:
            True if image likely contains a chart
        """
        if not CV2_AVAILABLE:
            return False
        
        try:
            # Convert to OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Count edge pixels
            edge_pixels = np.sum(edges > 0)
            total_pixels = edges.shape[0] * edges.shape[1]
            edge_ratio = edge_pixels / total_pixels
            
            # Charts typically have more edges than regular photos
            return edge_ratio > 0.05
            
        except Exception:
            return False
