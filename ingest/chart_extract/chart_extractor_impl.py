"""
Chart extraction implementation combining detection and captioning.

This module provides the main chart extraction interface that combines
computer vision detection with AI-powered captioning.
"""

from pathlib import Path
from typing import List, Tuple, Optional

from PIL import Image

from ..core import ChartCaption, ChartExtractor, ChartType
from ..utils import get_logger
from .chart_detector import ChartDetector
from .vision_captioner import VisionCaptioner

logger = get_logger(__name__)


class ChartExtractorImpl(ChartExtractor):
    """
    Implementation of chart extraction combining detection and captioning.
    
    Workflow:
    1. Detect chart regions using computer vision
    2. Extract chart images
    3. Generate AI-powered captions
    4. Associate with surrounding context
    """
    
    def __init__(self, openai_api_key: Optional[str] = None, vision_model: str = "gpt-4o"):
        """
        Initialize chart extractor.
        
        Args:
            openai_api_key: OpenAI API key for vision captioning
            vision_model: OpenAI model to use for vision analysis
        """
        self.detector = ChartDetector()
        self.captioner = VisionCaptioner(api_key=openai_api_key, model=vision_model)
        
        logger.info(
            "Chart extractor initialized",
            detector_available=True,  # ChartDetector is always available
            captioner_available=self.captioner.is_available(),
            vision_model=vision_model,
        )
    
    def detect_charts(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """
        Detect and extract chart images from a document.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            List of tuples containing (chart_image, metadata)
        """
        try:
            # Handle different file types
            if file_path.suffix.lower() == '.pdf':
                return self._extract_charts_from_pdf(file_path)
            elif file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                return self._extract_charts_from_image(file_path)
            elif file_path.suffix.lower() in ['.docx', '.pptx']:
                return self._extract_charts_from_office(file_path)
            else:
                logger.warning(
                    "Chart extraction not supported for file type",
                    file_path=str(file_path),
                    file_type=file_path.suffix,
                )
                return []
                
        except Exception as e:
            logger.error(
                "Chart detection failed",
                file_path=str(file_path),
                error=str(e),
            )
            return []
    
    def extract_chart_from_image(self, image: Image.Image) -> Optional[Image.Image]:
        """
        Extract chart region from a larger image.
        
        Args:
            image: PIL Image containing a chart
            
        Returns:
            Extracted chart image or None if no chart found
        """
        try:
            # Use detector to find chart regions
            charts = self.detector.detect_charts_in_image(image)
            
            if charts:
                # Return the chart with highest confidence
                best_chart = max(charts, key=lambda x: x[1].get('confidence', 0))
                return best_chart[0]
            
            # If no specific chart region found, check if entire image is a chart
            if self.detector.is_likely_chart(image):
                return image
            
            return None
            
        except Exception as e:
            logger.warning(
                "Chart extraction from image failed",
                error=str(e),
                image_size=image.size,
            )
            return None
    
    def generate_caption(
        self,
        chart_image: Image.Image,
        context: str = "",
    ) -> ChartCaption:
        """
        Generate AI-powered caption for a chart image.
        
        Args:
            chart_image: PIL Image of the chart
            context: Additional context about the chart
            
        Returns:
            ChartCaption with description and insights
        """
        try:
            # First, try to detect chart type using computer vision
            chart_type_hint = self._detect_chart_type_cv(chart_image)
            
            # Generate caption using AI
            caption = self.captioner.generate_caption(
                chart_image,
                context=context,
                chart_type_hint=chart_type_hint,
            )
            
            # If AI didn't detect chart type, use CV detection
            if caption.chart_type == ChartType.UNKNOWN and chart_type_hint != ChartType.UNKNOWN:
                caption.chart_type = chart_type_hint
            
            return caption
            
        except Exception as e:
            logger.error(
                "Chart caption generation failed",
                error=str(e),
                context_length=len(context),
            )
            
            return ChartCaption(
                description=f"Failed to generate caption: {str(e)}",
                extraction_method="error",
            )
    
    def _extract_charts_from_pdf(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """Extract charts from PDF file."""
        charts = []
        
        try:
            import fitz  # PyMuPDF
            
            doc = fitz.open(str(file_path))
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Get page as image
                mat = fitz.Matrix(2, 2)  # 2x zoom for better quality
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Convert to PIL Image
                from io import BytesIO
                page_image = Image.open(BytesIO(img_data))
                
                # Detect charts in page
                page_charts = self.detector.detect_charts_in_image(page_image)
                
                # Add page context to metadata
                for chart_image, metadata in page_charts:
                    metadata.update({
                        'source_page': page_num + 1,
                        'source_file': file_path.name,
                        'extraction_method': 'pdf_page_analysis',
                    })
                    charts.append((chart_image, metadata))
            
            doc.close()
            
        except ImportError:
            logger.warning("PyMuPDF not available for PDF chart extraction")
        except Exception as e:
            logger.error(
                "PDF chart extraction failed",
                file_path=str(file_path),
                error=str(e),
            )
        
        return charts
    
    def _extract_charts_from_image(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """Extract charts from image file."""
        try:
            image = Image.open(file_path)
            charts = self.detector.detect_charts_in_image(image)
            
            # Add file context to metadata
            for chart_image, metadata in charts:
                metadata.update({
                    'source_file': file_path.name,
                    'extraction_method': 'image_analysis',
                })
            
            return charts
            
        except Exception as e:
            logger.error(
                "Image chart extraction failed",
                file_path=str(file_path),
                error=str(e),
            )
            return []
    
    def _extract_charts_from_office(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """Extract charts from Office documents (DOCX, PPTX)."""
        charts = []
        
        try:
            if file_path.suffix.lower() == '.pptx':
                charts = self._extract_charts_from_pptx(file_path)
            elif file_path.suffix.lower() == '.docx':
                charts = self._extract_charts_from_docx(file_path)
            
        except Exception as e:
            logger.error(
                "Office document chart extraction failed",
                file_path=str(file_path),
                error=str(e),
            )
        
        return charts
    
    def _extract_charts_from_pptx(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """Extract charts from PowerPoint presentation."""
        charts = []
        
        try:
            from pptx import Presentation
            from pptx.enum.shapes import MSO_SHAPE_TYPE
            
            prs = Presentation(str(file_path))
            
            for slide_num, slide in enumerate(prs.slides, 1):
                for shape_num, shape in enumerate(slide.shapes):
                    if shape.shape_type == MSO_SHAPE_TYPE.CHART:
                        # This is a placeholder - actual chart image extraction
                        # would require more complex processing
                        metadata = {
                            'source_slide': slide_num,
                            'source_file': file_path.name,
                            'shape_index': shape_num,
                            'extraction_method': 'pptx_chart_shape',
                            'chart_type': ChartType.UNKNOWN,
                        }
                        
                        # For now, we'll note that a chart was found
                        # In a full implementation, you'd extract the chart image
                        logger.info(
                            "Chart shape detected in PowerPoint",
                            slide=slide_num,
                            shape=shape_num,
                        )
            
        except ImportError:
            logger.warning("python-pptx not available for PowerPoint chart extraction")
        except Exception as e:
            logger.error(
                "PowerPoint chart extraction failed",
                error=str(e),
            )
        
        return charts
    
    def _extract_charts_from_docx(self, file_path: Path) -> List[Tuple[Image.Image, dict]]:
        """Extract charts from Word document."""
        # Similar to PPTX, this would require extracting embedded chart objects
        # This is a placeholder for the implementation
        logger.info(
            "Word document chart extraction not yet implemented",
            file_path=str(file_path),
        )
        return []
    
    def _detect_chart_type_cv(self, image: Image.Image) -> ChartType:
        """
        Detect chart type using computer vision.
        
        Args:
            image: Chart image
            
        Returns:
            Detected chart type
        """
        try:
            # Use the detector's analysis method
            charts = self.detector.detect_charts_in_image(image)
            
            if charts:
                # Return the chart type with highest confidence
                best_chart = max(charts, key=lambda x: x[1].get('confidence', 0))
                return best_chart[1].get('chart_type', ChartType.UNKNOWN)
            
            return ChartType.UNKNOWN
            
        except Exception:
            return ChartType.UNKNOWN
