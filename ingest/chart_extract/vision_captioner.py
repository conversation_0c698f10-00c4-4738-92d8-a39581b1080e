"""
AI-powered chart captioning using OpenAI GPT-4 Vision.

This module provides chart description and analysis capabilities
using large language models with vision capabilities.
"""

import base64
import io
from typing import Optional

try:
    import openai
    from PIL import Image
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from ..core import ChartCaption, ChartType
from ..utils import get_logger

logger = get_logger(__name__)


class VisionCaptioner:
    """
    Generates AI-powered captions for charts using GPT-4 Vision.
    
    Provides detailed descriptions of charts including:
    - Chart type identification
    - Data insights and trends
    - Key findings and patterns
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4o"):
        """
        Initialize vision captioner.
        
        Args:
            api_key: OpenAI API key (if not set in environment)
            model: OpenAI model to use for vision analysis
        """
        if not OPENAI_AVAILABLE:
            logger.warning("OpenAI library not available. Install with: pip install openai")
            self.available = False
            return
        
        self.model = model
        self.available = True
        
        # Initialize OpenAI client
        try:
            if api_key:
                self.client = openai.OpenAI(api_key=api_key)
            else:
                self.client = openai.OpenAI()  # Uses OPENAI_API_KEY env var
            
            logger.info(
                "Vision captioner initialized",
                model=self.model,
            )
            
        except Exception as e:
            logger.error(
                "Failed to initialize OpenAI client",
                error=str(e),
            )
            self.available = False
    
    def generate_caption(
        self,
        chart_image: Image.Image,
        context: str = "",
        chart_type_hint: Optional[ChartType] = None,
    ) -> ChartCaption:
        """
        Generate a detailed caption for a chart image.
        
        Args:
            chart_image: PIL Image of the chart
            context: Additional context about the chart
            chart_type_hint: Hint about the chart type
            
        Returns:
            ChartCaption with description and insights
        """
        if not self.available:
            return ChartCaption(
                description="Vision captioning not available",
                extraction_method="unavailable",
            )
        
        try:
            # Convert image to base64
            image_base64 = self._image_to_base64(chart_image)
            
            # Create prompt
            prompt = self._create_analysis_prompt(context, chart_type_hint)
            
            # Call OpenAI Vision API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.1,  # Low temperature for consistent analysis
            )
            
            # Parse response
            analysis_text = response.choices[0].message.content
            
            # Extract structured information from response
            caption = self._parse_analysis_response(analysis_text)
            caption.extraction_method = f"openai_{self.model}"
            
            logger.info(
                "Chart caption generated successfully",
                chart_type=caption.chart_type,
                confidence=caption.confidence_score,
                description_length=len(caption.description),
            )
            
            return caption
            
        except Exception as e:
            logger.error(
                "Failed to generate chart caption",
                error=str(e),
                model=self.model,
            )
            
            return ChartCaption(
                description=f"Failed to analyze chart: {str(e)}",
                extraction_method=f"openai_{self.model}_error",
            )
    
    def _image_to_base64(self, image: Image.Image) -> str:
        """
        Convert PIL Image to base64 string.
        
        Args:
            image: PIL Image
            
        Returns:
            Base64 encoded image string
        """
        # Resize image if too large (OpenAI has size limits)
        max_size = 1024
        if max(image.size) > max_size:
            ratio = max_size / max(image.size)
            new_size = tuple(int(dim * ratio) for dim in image.size)
            image = image.resize(new_size, Image.Resampling.LANCZOS)
        
        # Convert to PNG format
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        buffer.seek(0)
        
        # Encode to base64
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return image_base64
    
    def _create_analysis_prompt(
        self,
        context: str = "",
        chart_type_hint: Optional[ChartType] = None,
    ) -> str:
        """
        Create analysis prompt for the vision model.
        
        Args:
            context: Additional context
            chart_type_hint: Hint about chart type
            
        Returns:
            Formatted prompt string
        """
        prompt_parts = [
            "Analyze this chart or diagram and provide a detailed description.",
            "",
            "Please identify:",
            "1. Chart type (bar, line, pie, scatter, table, etc.)",
            "2. Main data insights and trends",
            "3. Key findings or patterns",
            "4. Any notable data points or outliers",
            "",
            "Format your response as:",
            "CHART_TYPE: [type]",
            "DESCRIPTION: [detailed description]",
            "INSIGHTS: [key insights separated by semicolons]",
            "CONFIDENCE: [confidence score 0.0-1.0]",
        ]
        
        if chart_type_hint:
            prompt_parts.insert(1, f"This appears to be a {chart_type_hint.value} chart.")
        
        if context:
            prompt_parts.insert(-1, f"Additional context: {context}")
        
        return "\n".join(prompt_parts)
    
    def _parse_analysis_response(self, response_text: str) -> ChartCaption:
        """
        Parse the structured response from the vision model.
        
        Args:
            response_text: Raw response text
            
        Returns:
            Parsed ChartCaption
        """
        try:
            lines = response_text.strip().split('\n')
            
            chart_type = ChartType.UNKNOWN
            description = response_text  # Fallback to full response
            insights = []
            confidence = 0.5  # Default confidence
            
            for line in lines:
                line = line.strip()
                
                if line.startswith('CHART_TYPE:'):
                    type_str = line.split(':', 1)[1].strip().lower()
                    # Map common chart type names
                    type_mapping = {
                        'bar': ChartType.BAR,
                        'line': ChartType.LINE,
                        'pie': ChartType.PIE,
                        'scatter': ChartType.SCATTER,
                        'area': ChartType.AREA,
                        'histogram': ChartType.HISTOGRAM,
                        'box': ChartType.BOX,
                        'heatmap': ChartType.HEATMAP,
                        'table': ChartType.TABLE,
                        'diagram': ChartType.DIAGRAM,
                    }
                    chart_type = type_mapping.get(type_str, ChartType.UNKNOWN)
                
                elif line.startswith('DESCRIPTION:'):
                    description = line.split(':', 1)[1].strip()
                
                elif line.startswith('INSIGHTS:'):
                    insights_str = line.split(':', 1)[1].strip()
                    insights = [insight.strip() for insight in insights_str.split(';') if insight.strip()]
                
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        confidence = max(0.0, min(1.0, confidence))  # Clamp to [0,1]
                    except ValueError:
                        confidence = 0.5
            
            return ChartCaption(
                chart_type=chart_type,
                description=description,
                data_insights=insights,
                confidence_score=confidence,
            )
            
        except Exception as e:
            logger.warning(
                "Failed to parse analysis response",
                error=str(e),
                response_preview=response_text[:200],
            )
            
            return ChartCaption(
                description=response_text,
                confidence_score=0.3,
            )
    
    def is_available(self) -> bool:
        """Check if vision captioning is available."""
        return self.available
    
    def test_connection(self) -> bool:
        """
        Test connection to OpenAI API.
        
        Returns:
            True if connection is working
        """
        if not self.available:
            return False
        
        try:
            # Create a simple test image
            test_image = Image.new('RGB', (100, 100), color='white')
            
            # Try to generate a caption
            caption = self.generate_caption(test_image, "Test image")
            
            return caption.extraction_method != "unavailable"
            
        except Exception as e:
            logger.warning(
                "Vision captioner connection test failed",
                error=str(e),
            )
            return False
