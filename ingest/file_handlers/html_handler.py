"""
HTML file handler for web content extraction.

This module handles HTML files with support for:
- Clean text extraction using readability
- Structure preservation
- Metadata extraction
- Link and image detection
"""

import chardet
from pathlib import Path
from typing import List

try:
    from readability import Document
    from bs4 import BeautifulSoup
    HTML_LIBS_AVAILABLE = True
except ImportError:
    HTML_LIBS_AVAILABLE = False
    # Create dummy classes for type hints
    class BeautifulSoup:
        pass
    class Document:
        pass

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class HTMLHandler(BaseFileHandler):
    """Handler for HTML files."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.HTML, FileType.HTM]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type in [
            "text/html",
            "application/xhtml+xml",
        ]
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate HTML file can be parsed."""
        if not HTML_LIBS_AVAILABLE:
            self.logger.warning(
                "HTML processing libraries not available. Install with: pip install readability-lxml beautifulsoup4",
                file_path=str(file_path),
            )
            return False
        
        try:
            # Try to read and parse HTML
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)
            
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read(1000)
            
            # Try to parse with BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from HTML file."""
        if not HTML_LIBS_AVAILABLE:
            raise ExtractionError(
                "HTML processing libraries not available. Install with: pip install readability-lxml beautifulsoup4",
                file_path=str(file_path),
            )
        
        chunks = []
        
        try:
            # Detect encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            
            # Read HTML content
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                html_content = f.read()
            
            self.logger.info(
                "Processing HTML file",
                file_path=str(file_path),
                encoding=encoding,
                content_size=len(html_content),
            )
            
            # Extract main content using readability
            doc = Document(html_content)
            main_content = doc.summary()
            title = doc.title()
            
            # Parse with BeautifulSoup for structure
            soup = BeautifulSoup(main_content, 'html.parser')
            
            # Extract text content by sections
            chunks = self._extract_html_sections(soup, file_path.name, title)
            
            # Extract metadata and links if needed
            if config.extract_charts:
                metadata_chunk = self._extract_html_metadata(
                    BeautifulSoup(html_content, 'html.parser'),
                    file_path.name,
                    len(chunks),
                )
                if metadata_chunk:
                    chunks.append(metadata_chunk)
            
            self.logger.info(
                "HTML processing completed",
                file_path=str(file_path),
                chunks_created=len(chunks),
                title=title,
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from HTML file: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _extract_html_sections(
        self,
        soup: BeautifulSoup,
        source: str,
        title: str,
    ) -> List[DocumentChunk]:
        """
        Extract content sections from HTML.
        
        Args:
            soup: BeautifulSoup object
            source: Source file name
            title: Document title
            
        Returns:
            List of document chunks
        """
        chunks = []
        chunk_index = 0
        
        # Add title as first chunk if available
        if title and title.strip():
            chunk = self._create_chunk(
                text=title.strip(),
                source=source,
                file_type=FileType.HTML,
                chunk_index=chunk_index,
                element_type="title",
            )
            chunks.append(chunk)
            chunk_index += 1
        
        # Extract content by headings and paragraphs
        current_section = []
        current_heading = None
        
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'article', 'section']):
            if element.name.startswith('h'):
                # Save previous section
                if current_section:
                    section_text = '\n'.join(current_section).strip()
                    if section_text:
                        chunk = self._create_chunk(
                            text=section_text,
                            source=source,
                            file_type=FileType.HTML,
                            chunk_index=chunk_index,
                            element_type="section",
                            heading=current_heading,
                        )
                        chunks.append(chunk)
                        chunk_index += 1
                
                # Start new section
                current_heading = element.get_text().strip()
                current_section = [current_heading]
            
            else:
                # Add content to current section
                text = element.get_text().strip()
                if text:
                    current_section.append(text)
        
        # Save final section
        if current_section:
            section_text = '\n'.join(current_section).strip()
            if section_text:
                chunk = self._create_chunk(
                    text=section_text,
                    source=source,
                    file_type=FileType.HTML,
                    chunk_index=chunk_index,
                    element_type="section",
                    heading=current_heading,
                )
                chunks.append(chunk)
        
        # If no structured content found, extract all text
        if not chunks:
            all_text = soup.get_text().strip()
            if all_text:
                chunk = self._create_chunk(
                    text=all_text,
                    source=source,
                    file_type=FileType.HTML,
                    chunk_index=0,
                    element_type="content",
                )
                chunks.append(chunk)
        
        return chunks
    
    def _extract_html_metadata(
        self,
        soup: BeautifulSoup,
        source: str,
        chunk_index: int,
    ) -> DocumentChunk:
        """
        Extract metadata from HTML document.
        
        Args:
            soup: BeautifulSoup object
            source: Source file name
            chunk_index: Chunk index
            
        Returns:
            Document chunk with metadata
        """
        metadata_parts = []
        
        # Extract meta tags
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            name = meta.get('name') or meta.get('property')
            content = meta.get('content')
            if name and content:
                metadata_parts.append(f"{name}: {content}")
        
        # Extract links
        links = soup.find_all('a', href=True)
        if links:
            link_texts = [f"Link: {link.get_text().strip()} ({link['href']})" 
                         for link in links[:10]]  # Limit to first 10 links
            metadata_parts.extend(link_texts)
        
        # Extract images
        images = soup.find_all('img', src=True)
        if images:
            image_texts = [f"Image: {img.get('alt', 'No alt text')} ({img['src']})" 
                          for img in images[:5]]  # Limit to first 5 images
            metadata_parts.extend(image_texts)
        
        if metadata_parts:
            metadata_text = '\n'.join(metadata_parts)
            return self._create_chunk(
                text=metadata_text,
                source=source,
                file_type=FileType.HTML,
                chunk_index=chunk_index,
                element_type="metadata",
                link_count=len(links),
                image_count=len(images),
            )
        
        return None
