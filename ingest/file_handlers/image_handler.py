"""
Image file handler for visual content extraction.

This module handles image files with support for:
- OCR text extraction using pytesseract
- Image preprocessing for better OCR
- Chart and diagram detection
- Metadata extraction
"""

from pathlib import Path
from typing import List, Optional

try:
    import cv2
    import numpy as np
    from PIL import Image
    import pytesseract
    IMAGE_LIBS_AVAILABLE = True
except ImportError:
    IMAGE_LIBS_AVAILABLE = False
    # Create dummy classes for type hints
    import numpy as np
    class Image:
        pass

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class ImageHandler(BaseFileHandler):
    """Handler for image files with OCR capabilities."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.JPG, FileType.JPEG, FileType.PNG, FileType.WEBP, FileType.GIF]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type.startswith('image/')
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate image file can be opened."""
        if not IMAGE_LIBS_AVAILABLE:
            self.logger.warning(
                "Image processing libraries not available. Install with: pip install opencv-python pillow pytesseract",
                file_path=str(file_path),
            )
            return False
        
        try:
            # Try to open with PIL
            with Image.open(file_path) as img:
                img.verify()
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from image file."""
        if not IMAGE_LIBS_AVAILABLE:
            raise ExtractionError(
                "Image processing libraries not available. Install with: pip install opencv-python pillow pytesseract",
                file_path=str(file_path),
            )
        
        chunks = []
        
        try:
            # Load image
            image = Image.open(file_path)
            
            self.logger.info(
                "Processing image file",
                file_path=str(file_path),
                image_size=image.size,
                image_mode=image.mode,
            )
            
            # Extract text using OCR
            ocr_text = self._extract_text_with_ocr(image)
            
            if ocr_text.strip():
                chunk = self._create_chunk(
                    text=ocr_text,
                    source=file_path.name,
                    file_type=self._get_file_type(file_path),
                    chunk_index=0,
                    element_type="ocr_text",
                    image_width=image.size[0],
                    image_height=image.size[1],
                    image_mode=image.mode,
                )
                chunks.append(chunk)
            
            # Detect if image contains charts/diagrams
            if config.extract_charts:
                chart_info = self._analyze_image_content(image)
                if chart_info:
                    chart_chunk = self._create_chunk(
                        text=f"[IMAGE ANALYSIS: {chart_info}]",
                        source=file_path.name,
                        file_type=self._get_file_type(file_path),
                        chunk_index=len(chunks),
                        element_type="image_analysis",
                        analysis_result=chart_info,
                    )
                    chunks.append(chart_chunk)
            
            # If no text found, create a basic description chunk
            if not chunks:
                description = f"Image file: {file_path.name} ({image.size[0]}x{image.size[1]} pixels)"
                chunk = self._create_chunk(
                    text=description,
                    source=file_path.name,
                    file_type=self._get_file_type(file_path),
                    chunk_index=0,
                    element_type="image_description",
                    image_width=image.size[0],
                    image_height=image.size[1],
                )
                chunks.append(chunk)
            
            self.logger.info(
                "Image processing completed",
                file_path=str(file_path),
                chunks_created=len(chunks),
                ocr_text_length=len(ocr_text) if ocr_text else 0,
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from image file: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _extract_text_with_ocr(self, image: Image.Image) -> str:
        """
        Extract text from image using OCR.
        
        Args:
            image: PIL Image object
            
        Returns:
            Extracted text
        """
        try:
            # Preprocess image for better OCR
            processed_image = self._preprocess_image_for_ocr(image)
            
            # Extract text using pytesseract
            text = pytesseract.image_to_string(processed_image, lang='eng')
            
            return text.strip()
            
        except Exception as e:
            self.logger.warning(
                "OCR text extraction failed",
                error=str(e),
            )
            return ""
    
    def _preprocess_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image to improve OCR accuracy.
        
        Args:
            image: PIL Image object
            
        Returns:
            Preprocessed image
        """
        try:
            # Convert to OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply noise reduction
            denoised = cv2.medianBlur(gray, 3)
            
            # Apply thresholding to get binary image
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Convert back to PIL Image
            processed_image = Image.fromarray(binary)
            
            return processed_image
            
        except Exception as e:
            self.logger.warning(
                "Image preprocessing failed, using original image",
                error=str(e),
            )
            return image
    
    def _analyze_image_content(self, image: Image.Image) -> Optional[str]:
        """
        Analyze image content to detect charts, diagrams, etc.
        
        Args:
            image: PIL Image object
            
        Returns:
            Analysis description or None
        """
        try:
            # Convert to OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Detect edges
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Analyze contours to detect shapes
            shapes_detected = []
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 1000:  # Filter small contours
                    # Approximate contour
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    # Classify shape based on number of vertices
                    vertices = len(approx)
                    if vertices == 3:
                        shapes_detected.append("triangle")
                    elif vertices == 4:
                        shapes_detected.append("rectangle")
                    elif vertices > 8:
                        shapes_detected.append("circle")
                    else:
                        shapes_detected.append("polygon")
            
            # Detect lines (potential chart axes)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=50, maxLineGap=10)
            
            analysis_parts = []
            
            if shapes_detected:
                shape_counts = {}
                for shape in shapes_detected:
                    shape_counts[shape] = shape_counts.get(shape, 0) + 1
                
                shape_desc = ", ".join([f"{count} {shape}(s)" for shape, count in shape_counts.items()])
                analysis_parts.append(f"Shapes detected: {shape_desc}")
            
            if lines is not None and len(lines) > 5:
                analysis_parts.append(f"Multiple lines detected ({len(lines)}), possibly a chart or diagram")
            
            if analysis_parts:
                return "; ".join(analysis_parts)
            
            return None
            
        except Exception as e:
            self.logger.warning(
                "Image content analysis failed",
                error=str(e),
            )
            return None
    
    def get_image_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from image file.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Dictionary with image metadata
        """
        try:
            with Image.open(file_path) as image:
                metadata = {
                    "width": image.size[0],
                    "height": image.size[1],
                    "mode": image.mode,
                    "format": image.format,
                }
                
                # Extract EXIF data if available
                if hasattr(image, '_getexif') and image._getexif():
                    exif_data = image._getexif()
                    metadata["exif"] = exif_data
                
                return metadata
                
        except Exception as e:
            self.logger.warning(
                "Failed to extract image metadata",
                file_path=str(file_path),
                error=str(e),
            )
            return {}
