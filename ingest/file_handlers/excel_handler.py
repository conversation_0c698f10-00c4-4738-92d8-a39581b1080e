"""
Excel file handler for processing .xlsx and .xls files.

This module provides comprehensive Excel file processing with support for
multiple sheets, data analysis, and intelligent content extraction.
"""

from pathlib import Path
from typing import List

try:
    import pandas as pd
    import openpyxl
    EXCEL_LIBS_AVAILABLE = True
except ImportError:
    EXCEL_LIBS_AVAILABLE = False

from ..core import DocumentChunk, FileType, IngestionConfig, ProcessingResult
from ..utils import get_logger
from .base_handler import BaseHandler

logger = get_logger(__name__)


class ExcelHandler(BaseHandler):
    """
    Handler for Excel files (.xlsx, .xls) with comprehensive data processing.
    
    Features:
    - Multi-sheet processing
    - Data type detection and analysis
    - Table structure preservation
    - Statistical summaries
    - Chart and graph detection
    """
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.XLSX, FileType.XLS]
    
    def can_handle(self, file_path: Path, mime_type: str = None) -> bool:
        """Check if this handler can process the file."""
        if not EXCEL_LIBS_AVAILABLE:
            return False
        
        # Check file extension
        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            return True
        
        # Check MIME type
        excel_mime_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',  # .xlsx
            'application/vnd.ms-excel',  # .xls
        ]
        
        return mime_type in excel_mime_types if mime_type else False
    
    def validate_file(self, file_path: Path) -> bool:
        """Validate Excel file."""
        if not EXCEL_LIBS_AVAILABLE:
            logger.warning("Excel processing libraries not available")
            return False
        
        try:
            # Try to read the Excel file
            if file_path.suffix.lower() == '.xlsx':
                # Use openpyxl for .xlsx
                workbook = openpyxl.load_workbook(file_path, read_only=True)
                workbook.close()
            else:
                # Use pandas for .xls (uses xlrd)
                pd.read_excel(file_path, sheet_name=None, nrows=1)
            
            return True
            
        except Exception as e:
            logger.error(
                "Excel file validation failed",
                file_path=str(file_path),
                error=str(e),
            )
            return False
    
    def extract_content(self, file_path: Path, config: IngestionConfig) -> ProcessingResult:
        """Extract content from Excel file."""
        if not EXCEL_LIBS_AVAILABLE:
            return ProcessingResult(
                success=False,
                file_path=str(file_path),
                error_message="Excel processing libraries not available. Install with: pip install pandas openpyxl xlrd",
            )
        
        try:
            logger.info(
                "Starting Excel content extraction",
                file_path=str(file_path),
            )
            
            # Read all sheets
            excel_data = pd.read_excel(file_path, sheet_name=None)
            
            chunks = []
            total_rows = 0
            total_sheets = len(excel_data)
            
            for sheet_name, df in excel_data.items():
                logger.debug(
                    "Processing Excel sheet",
                    sheet_name=sheet_name,
                    rows=len(df),
                    columns=len(df.columns),
                )
                
                # Process sheet data
                sheet_chunks = self._process_sheet(df, sheet_name, file_path, config)
                chunks.extend(sheet_chunks)
                total_rows += len(df)
            
            # Create summary chunk
            summary_chunk = self._create_summary_chunk(excel_data, file_path, config)
            chunks.insert(0, summary_chunk)  # Add summary at the beginning
            
            logger.info(
                "Excel content extraction completed",
                file_path=str(file_path),
                sheets_processed=total_sheets,
                total_rows=total_rows,
                chunks_created=len(chunks),
            )
            
            return ProcessingResult(
                success=True,
                file_path=str(file_path),
                file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                chunks=chunks,
                pages_processed=total_sheets,  # Use sheets as "pages"
            )
            
        except Exception as e:
            logger.error(
                "Excel content extraction failed",
                file_path=str(file_path),
                error=str(e),
            )
            
            return ProcessingResult(
                success=False,
                file_path=str(file_path),
                file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                error_message=f"Excel extraction failed: {str(e)}",
            )
    
    def _process_sheet(self, df, sheet_name: str, file_path: Path, config: IngestionConfig) -> List[DocumentChunk]:
        """Process a single Excel sheet."""
        chunks = []
        
        try:
            # Sheet overview
            overview_text = self._create_sheet_overview(df, sheet_name)
            
            # Create overview chunk
            overview_chunk = DocumentChunk(
                text=overview_text,
                source=str(file_path),
                file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                start_page=None,
                end_page=None,
                chunk_index=len(chunks),
                metadata={
                    'sheet_name': sheet_name,
                    'content_type': 'sheet_overview',
                    'rows': len(df),
                    'columns': len(df.columns),
                    'extraction_method': 'pandas',
                },
            )
            chunks.append(overview_chunk)
            
            # Process data in chunks if large
            if len(df) > 100:  # Large sheet
                chunk_size = 50  # Rows per chunk
                for i in range(0, len(df), chunk_size):
                    chunk_df = df.iloc[i:i+chunk_size]
                    data_text = self._dataframe_to_text(chunk_df, f"{sheet_name} (rows {i+1}-{min(i+chunk_size, len(df))})")
                    
                    data_chunk = DocumentChunk(
                        text=data_text,
                        source=str(file_path),
                        file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                        chunk_index=len(chunks),
                        metadata={
                            'sheet_name': sheet_name,
                            'content_type': 'data_chunk',
                            'row_start': i + 1,
                            'row_end': min(i + chunk_size, len(df)),
                            'extraction_method': 'pandas',
                        },
                    )
                    chunks.append(data_chunk)
            else:
                # Small sheet - process as single chunk
                data_text = self._dataframe_to_text(df, sheet_name)
                
                data_chunk = DocumentChunk(
                    text=data_text,
                    source=str(file_path),
                    file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                    chunk_index=len(chunks),
                    metadata={
                        'sheet_name': sheet_name,
                        'content_type': 'complete_sheet',
                        'rows': len(df),
                        'columns': len(df.columns),
                        'extraction_method': 'pandas',
                    },
                )
                chunks.append(data_chunk)
            
            return chunks
            
        except Exception as e:
            logger.error(
                "Sheet processing failed",
                sheet_name=sheet_name,
                error=str(e),
            )
            return []
    
    def _create_sheet_overview(self, df, sheet_name: str) -> str:
        """Create a text overview of the sheet."""
        try:
            overview_parts = [
                f"Excel Sheet: {sheet_name}",
                f"Dimensions: {len(df)} rows × {len(df.columns)} columns",
                "",
                "Column Information:",
            ]
            
            # Column details
            for col in df.columns:
                col_info = f"- {col}: {df[col].dtype}"
                if df[col].dtype in ['int64', 'float64']:
                    # Add statistics for numeric columns
                    stats = df[col].describe()
                    col_info += f" (min: {stats['min']:.2f}, max: {stats['max']:.2f}, mean: {stats['mean']:.2f})"
                elif df[col].dtype == 'object':
                    # Add info for text columns
                    unique_count = df[col].nunique()
                    col_info += f" ({unique_count} unique values)"
                
                overview_parts.append(col_info)
            
            # Data sample
            if len(df) > 0:
                overview_parts.extend([
                    "",
                    "Data Sample (first 3 rows):",
                    self._dataframe_to_text(df.head(3), "Sample Data"),
                ])
            
            # Summary statistics
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                overview_parts.extend([
                    "",
                    "Numeric Summary:",
                    str(df[numeric_cols].describe()),
                ])
            
            return "\n".join(overview_parts)
            
        except Exception as e:
            return f"Sheet: {sheet_name}\nError creating overview: {str(e)}"
    
    def _dataframe_to_text(self, df, title: str) -> str:
        """Convert DataFrame to readable text format."""
        try:
            text_parts = [f"=== {title} ===", ""]
            
            if len(df) == 0:
                text_parts.append("(No data)")
                return "\n".join(text_parts)
            
            # Convert to string representation
            # Use a more readable format
            for idx, row in df.iterrows():
                row_parts = []
                for col, value in row.items():
                    if pd.isna(value):
                        value_str = "(empty)"
                    else:
                        value_str = str(value)
                    row_parts.append(f"{col}: {value_str}")
                
                text_parts.append(f"Row {idx + 1}: {' | '.join(row_parts)}")
            
            return "\n".join(text_parts)
            
        except Exception as e:
            return f"{title}\nError converting data: {str(e)}"
    
    def _create_summary_chunk(self, excel_data: dict, file_path: Path, config: IngestionConfig) -> DocumentChunk:
        """Create a summary chunk for the entire Excel file."""
        try:
            summary_parts = [
                f"Excel File Summary: {file_path.name}",
                f"Total Sheets: {len(excel_data)}",
                "",
                "Sheet Overview:",
            ]
            
            total_rows = 0
            total_cols = 0
            
            for sheet_name, df in excel_data.items():
                summary_parts.append(f"- {sheet_name}: {len(df)} rows × {len(df.columns)} columns")
                total_rows += len(df)
                total_cols += len(df.columns)
            
            summary_parts.extend([
                "",
                f"Total Data Points: {total_rows} rows across all sheets",
                f"Average Columns per Sheet: {total_cols / len(excel_data):.1f}",
            ])
            
            # Identify data types across all sheets
            all_dtypes = set()
            for df in excel_data.values():
                all_dtypes.update(df.dtypes.astype(str).unique())
            
            summary_parts.extend([
                "",
                f"Data Types Found: {', '.join(sorted(all_dtypes))}",
            ])
            
            summary_text = "\n".join(summary_parts)
            
            return DocumentChunk(
                text=summary_text,
                source=str(file_path),
                file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                chunk_index=0,
                metadata={
                    'content_type': 'file_summary',
                    'total_sheets': len(excel_data),
                    'total_rows': total_rows,
                    'extraction_method': 'pandas',
                },
            )
            
        except Exception as e:
            return DocumentChunk(
                text=f"Excel File: {file_path.name}\nError creating summary: {str(e)}",
                source=str(file_path),
                file_type=FileType.XLSX if file_path.suffix.lower() == '.xlsx' else FileType.XLS,
                chunk_index=0,
                metadata={
                    'content_type': 'error_summary',
                    'error': str(e),
                },
            )
