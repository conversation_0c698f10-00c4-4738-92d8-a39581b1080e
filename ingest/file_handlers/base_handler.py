"""
Base file handler implementation with common functionality.

This module provides the base class for all file type handlers,
implementing common patterns and utilities.
"""

import time
from pathlib import Path
from typing import List, Optional

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileHandler,
    FileType,
    IngestionConfig,
    ProcessingResult,
)
from ..utils import get_logger

logger = get_logger(__name__)


class BaseFileHandler(FileHandler):
    """
    Base implementation for file handlers with common functionality.
    
    Provides:
    - Error handling and logging
    - Performance tracking
    - Common validation patterns
    - Result formatting
    """
    
    def __init__(self):
        """Initialize the base handler."""
        self.logger = get_logger(self.__class__.__name__)
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return list of supported file types. Must be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement supported_types")
    
    def can_handle(self, file_path: Path, mime_type: str) -> bool:
        """
        Check if this handler can process the given file.
        
        Args:
            file_path: Path to the file
            mime_type: MIME type of the file
            
        Returns:
            True if this handler can process the file
        """
        # Check by file extension
        extension = file_path.suffix.lower()
        for file_type in self.supported_types:
            if extension == f".{file_type.value}":
                return True
        
        # Check by MIME type (can be overridden by subclasses)
        return self._can_handle_mime_type(mime_type)
    
    def extract_content(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> ProcessingResult:
        """
        Extract content from the file with error handling and performance tracking.
        
        Args:
            file_path: Path to the file
            config: Ingestion configuration
            
        Returns:
            Processing result with extracted content or error information
        """
        start_time = time.time()
        
        try:
            self.logger.info(
                "Starting content extraction",
                file_path=str(file_path),
                handler=self.__class__.__name__,
            )
            
            # Validate file before processing
            if not self.validate_file(file_path):
                raise ExtractionError(
                    f"File validation failed for {file_path}",
                    file_path=str(file_path),
                )
            
            # Extract content (implemented by subclasses)
            chunks = self._extract_content_impl(file_path, config)
            
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                success=True,
                file_path=str(file_path),
                file_type=self._get_file_type(file_path),
                chunks=chunks,
                processing_time=processing_time,
                pages_processed=self._count_pages(chunks),
            )
            
            self.logger.info(
                "Content extraction completed successfully",
                file_path=str(file_path),
                chunks_created=len(chunks),
                processing_time=processing_time,
                pages_processed=result.pages_processed,
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            self.logger.error(
                "Content extraction failed",
                file_path=str(file_path),
                error_type=type(e).__name__,
                error_message=str(e),
                processing_time=processing_time,
            )
            
            return ProcessingResult(
                success=False,
                file_path=str(file_path),
                file_type=self._get_file_type(file_path),
                error_message=str(e),
                processing_time=processing_time,
            )
    
    def validate_file(self, file_path: Path) -> bool:
        """
        Validate that the file is not corrupted and can be processed.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is valid
        """
        try:
            # Basic validation - file exists and is readable
            if not file_path.exists():
                return False
            
            if not file_path.is_file():
                return False
            
            # Try to open the file
            with open(file_path, 'rb') as f:
                f.read(1)
            
            # Additional validation can be implemented by subclasses
            return self._validate_file_impl(file_path)
            
        except Exception as e:
            self.logger.warning(
                "File validation failed",
                file_path=str(file_path),
                error=str(e),
            )
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """
        Implementation-specific content extraction.
        Must be implemented by subclasses.
        
        Args:
            file_path: Path to the file
            config: Ingestion configuration
            
        Returns:
            List of extracted document chunks
        """
        raise NotImplementedError("Subclasses must implement _extract_content_impl")
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """
        Implementation-specific file validation.
        Can be overridden by subclasses for additional validation.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is valid
        """
        return True
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """
        Check if this handler can process the given MIME type.
        Can be overridden by subclasses.
        
        Args:
            mime_type: MIME type to check
            
        Returns:
            True if MIME type is supported
        """
        return False
    
    def _get_file_type(self, file_path: Path) -> FileType:
        """
        Get the file type for the given file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Detected file type
        """
        extension = file_path.suffix.lower()
        for file_type in self.supported_types:
            if extension == f".{file_type.value}":
                return file_type
        return FileType.UNKNOWN
    
    def _count_pages(self, chunks: List[DocumentChunk]) -> int:
        """
        Count the number of pages processed based on chunks.
        
        Args:
            chunks: List of document chunks
            
        Returns:
            Number of pages processed
        """
        if not chunks:
            return 0
        
        pages = set()
        for chunk in chunks:
            if chunk.start_page is not None:
                pages.add(chunk.start_page)
            if chunk.end_page is not None:
                pages.add(chunk.end_page)
        
        return len(pages) if pages else 1
    
    def _create_chunk(
        self,
        text: str,
        source: str,
        file_type: FileType,
        page_number: Optional[int] = None,
        chunk_index: int = 0,
        **metadata,
    ) -> DocumentChunk:
        """
        Create a document chunk with common fields.
        
        Args:
            text: Extracted text content
            source: Source file name
            file_type: Type of the source file
            page_number: Page number (if applicable)
            chunk_index: Index of this chunk
            **metadata: Additional metadata
            
        Returns:
            Created document chunk
        """
        return DocumentChunk(
            text=text.strip(),
            source=source,
            file_type=file_type,
            start_page=page_number,
            end_page=page_number,
            chunk_index=chunk_index,
            token_count=len(text.split()),  # Simple token count
            metadata=metadata,
        )
