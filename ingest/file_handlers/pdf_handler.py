"""
PDF file handler using PyMuPDF for text and image extraction.

This module handles PDF files with support for:
- Text extraction from all pages
- Image and chart detection
- Table extraction
- Metadata extraction
"""

from pathlib import Path
from typing import List

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class PDFHandler(BaseFileHandler):
    """Handler for PDF files using PyMuPDF."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.PDF]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type == "application/pdf"
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate PDF file can be opened."""
        if not PYMUPDF_AVAILABLE:
            raise ExtractionError(
                "PyMuPDF is not available. Install with: pip install PyMuPDF",
                file_path=str(file_path),
            )
        
        try:
            doc = fitz.open(str(file_path))
            doc.close()
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from PDF file."""
        if not PYMUPDF_AVAILABLE:
            raise ExtractionError(
                "PyMuPDF is not available. Install with: pip install PyMuPDF",
                file_path=str(file_path),
            )
        
        chunks = []
        
        try:
            # Open PDF document
            doc = fitz.open(str(file_path))
            
            self.logger.info(
                "Processing PDF",
                file_path=str(file_path),
                page_count=len(doc),
            )
            
            # Extract text from each page
            for page_num in range(len(doc)):
                page = doc[page_num]

                # Extract text content
                text = page.get_text()

                if text.strip():
                    chunk = self._create_chunk(
                        text=text,
                        source=file_path.name,
                        file_type=FileType.PDF,
                        page_number=page_num + 1,
                        chunk_index=len(chunks),
                        page_width=page.rect.width,
                        page_height=page.rect.height,
                    )
                    chunks.append(chunk)

                # Extract images and charts if enabled
                if config.extract_charts:
                    image_chunks = self._extract_images_from_page(
                        page, file_path.name, page_num + 1, len(chunks)
                    )
                    chunks.extend(image_chunks)
            
            doc.close()
            
            self.logger.info(
                "PDF processing completed",
                file_path=str(file_path),
                pages_processed=len(doc),
                chunks_created=len(chunks),
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from PDF: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _extract_images_from_page(
        self,
        page,
        source: str,
        page_number: int,
        chunk_start_index: int,
    ) -> List[DocumentChunk]:
        """Extract images from a PDF page."""
        chunks = []
        
        try:
            # Get list of images on the page
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                try:
                    # Extract image data
                    xref = img[0]
                    base_image = page.parent.extract_image(xref)
                    image_bytes = base_image["image"]
                    image_ext = base_image["ext"]
                    
                    # Create a chunk for the image
                    # Note: This would typically be processed by chart extraction
                    chunk = self._create_chunk(
                        text=f"[IMAGE: {image_ext.upper()} image on page {page_number}]",
                        source=source,
                        file_type=FileType.PDF,
                        page_number=page_number,
                        chunk_index=chunk_start_index + len(chunks),
                        image_index=img_index,
                        image_format=image_ext,
                        image_size=len(image_bytes),
                    )
                    chunks.append(chunk)
                    
                except Exception as e:
                    self.logger.warning(
                        "Failed to extract image from PDF page",
                        page_number=page_number,
                        image_index=img_index,
                        error=str(e),
                    )
            
        except Exception as e:
            self.logger.warning(
                "Failed to process images on PDF page",
                page_number=page_number,
                error=str(e),
            )
        
        return chunks
    
    def extract_tables(self, file_path: Path) -> List[dict]:
        """
        Extract tables from PDF (placeholder for future implementation).
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            List of extracted tables
        """
        # This could be implemented using libraries like:
        # - tabula-py
        # - camelot-py
        # - pdfplumber
        
        self.logger.info(
            "Table extraction not yet implemented for PDF",
            file_path=str(file_path),
        )
        return []
    
    def get_pdf_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from PDF file.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary with PDF metadata
        """
        if not PYMUPDF_AVAILABLE:
            return {}
        
        try:
            doc = fitz.open(str(file_path))
            metadata = doc.metadata
            doc.close()
            
            return {
                "title": metadata.get("title", ""),
                "author": metadata.get("author", ""),
                "subject": metadata.get("subject", ""),
                "creator": metadata.get("creator", ""),
                "producer": metadata.get("producer", ""),
                "creation_date": metadata.get("creationDate", ""),
                "modification_date": metadata.get("modDate", ""),
            }
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract PDF metadata",
                file_path=str(file_path),
                error=str(e),
            )
            return {}
