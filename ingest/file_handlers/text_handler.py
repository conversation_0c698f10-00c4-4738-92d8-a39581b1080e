"""
Text file handler for TXT and Markdown files.

This module handles plain text and Markdown files with support for:
- Text extraction with encoding detection
- Markdown structure parsing
- Line-by-line processing
"""

import chardet
from pathlib import Path
from typing import List

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class TextHandler(BaseFileHandler):
    """Handler for plain text and Markdown files."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.TXT, FileType.MD]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type in [
            "text/plain",
            "text/markdown",
            "text/x-markdown",
        ]
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate text file can be read."""
        try:
            # Try to detect encoding and read a small portion
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)
            
            # Detect encoding
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            
            # Try to read with detected encoding
            with open(file_path, 'r', encoding=encoding) as f:
                f.read(100)
            
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from text file."""
        chunks = []
        
        try:
            # Detect file encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            confidence = encoding_result.get('confidence', 0.0)
            
            self.logger.info(
                "Processing text file",
                file_path=str(file_path),
                encoding=encoding,
                encoding_confidence=confidence,
                file_size=len(raw_data),
            )
            
            # Read file content
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                content = f.read()
            
            # Determine file type
            file_type = self._get_file_type(file_path)
            
            if file_type == FileType.MD:
                # Process Markdown file with structure awareness
                chunks = self._process_markdown(content, file_path.name)
            else:
                # Process as plain text
                chunks = self._process_plain_text(content, file_path.name)
            
            self.logger.info(
                "Text file processing completed",
                file_path=str(file_path),
                chunks_created=len(chunks),
                encoding_used=encoding,
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from text file: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _process_plain_text(self, content: str, source: str) -> List[DocumentChunk]:
        """
        Process plain text content.
        
        Args:
            content: Text content
            source: Source file name
            
        Returns:
            List of document chunks
        """
        chunks = []
        
        # Split content into paragraphs (double newlines)
        paragraphs = content.split('\n\n')
        
        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            if paragraph:
                chunk = self._create_chunk(
                    text=paragraph,
                    source=source,
                    file_type=FileType.TXT,
                    chunk_index=i,
                    paragraph_number=i + 1,
                )
                chunks.append(chunk)
        
        # If no paragraphs found, treat entire content as one chunk
        if not chunks and content.strip():
            chunk = self._create_chunk(
                text=content.strip(),
                source=source,
                file_type=FileType.TXT,
                chunk_index=0,
            )
            chunks.append(chunk)
        
        return chunks
    
    def _process_markdown(self, content: str, source: str) -> List[DocumentChunk]:
        """
        Process Markdown content with structure awareness.
        
        Args:
            content: Markdown content
            source: Source file name
            
        Returns:
            List of document chunks
        """
        chunks = []
        lines = content.split('\n')
        
        current_section = []
        current_heading = None
        chunk_index = 0
        
        for line in lines:
            line = line.rstrip()
            
            # Check if line is a heading
            if line.startswith('#'):
                # Save previous section if it exists
                if current_section:
                    section_text = '\n'.join(current_section).strip()
                    if section_text:
                        chunk = self._create_chunk(
                            text=section_text,
                            source=source,
                            file_type=FileType.MD,
                            chunk_index=chunk_index,
                            heading=current_heading,
                            section_type="content",
                        )
                        chunks.append(chunk)
                        chunk_index += 1
                
                # Start new section
                current_heading = line.lstrip('#').strip()
                current_section = [line]
            
            else:
                current_section.append(line)
        
        # Save final section
        if current_section:
            section_text = '\n'.join(current_section).strip()
            if section_text:
                chunk = self._create_chunk(
                    text=section_text,
                    source=source,
                    file_type=FileType.MD,
                    chunk_index=chunk_index,
                    heading=current_heading,
                    section_type="content",
                )
                chunks.append(chunk)
        
        # If no structured content found, fall back to plain text processing
        if not chunks:
            return self._process_plain_text(content, source)
        
        return chunks
    
    def extract_markdown_metadata(self, content: str) -> dict:
        """
        Extract metadata from Markdown front matter.
        
        Args:
            content: Markdown content
            
        Returns:
            Dictionary with extracted metadata
        """
        metadata = {}
        
        # Check for YAML front matter
        if content.startswith('---\n'):
            try:
                end_index = content.find('\n---\n', 4)
                if end_index != -1:
                    front_matter = content[4:end_index]
                    
                    # Simple YAML parsing (for basic key: value pairs)
                    for line in front_matter.split('\n'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            metadata[key.strip()] = value.strip()
            
            except Exception as e:
                self.logger.warning(
                    "Failed to parse Markdown front matter",
                    error=str(e),
                )
        
        return metadata
