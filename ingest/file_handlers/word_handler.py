"""
Word document handler for DOCX and DOC files.

This module handles Microsoft Word documents with support for:
- Text extraction from paragraphs
- Table extraction
- Image and shape detection
- Metadata extraction
"""

from pathlib import Path
from typing import List

try:
    from docx import Document
    from docx.oxml.table import CT_Tbl
    from docx.oxml.text.paragraph import CT_P
    from docx.table import _Cell, Table
    from docx.text.paragraph import Paragraph
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class WordHandler(BaseFileHandler):
    """Handler for Microsoft Word documents (DOCX and DOC)."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.DOCX, FileType.DOC]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type in [
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
        ]
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate Word document can be opened."""
        if not PYTHON_DOCX_AVAILABLE:
            raise ExtractionError(
                "python-docx is not available. Install with: pip install python-docx",
                file_path=str(file_path),
            )
        
        try:
            # Only DOCX files are supported by python-docx
            if file_path.suffix.lower() == '.doc':
                self.logger.warning(
                    "DOC files require conversion to DOCX. Consider using LibreOffice or similar.",
                    file_path=str(file_path),
                )
                return False
            
            doc = Document(str(file_path))
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from Word document."""
        if not PYTHON_DOCX_AVAILABLE:
            raise ExtractionError(
                "python-docx is not available. Install with: pip install python-docx",
                file_path=str(file_path),
            )
        
        if file_path.suffix.lower() == '.doc':
            raise ExtractionError(
                "DOC files are not supported. Please convert to DOCX format.",
                file_path=str(file_path),
            )
        
        chunks = []
        
        try:
            # Open Word document
            doc = Document(str(file_path))
            
            self.logger.info(
                "Processing Word document",
                file_path=str(file_path),
                paragraph_count=len(doc.paragraphs),
                table_count=len(doc.tables),
            )
            
            # Extract content in document order
            chunk_index = 0
            
            for element in doc.element.body:
                if isinstance(element, CT_P):
                    # Paragraph
                    paragraph = Paragraph(element, doc)
                    text = paragraph.text.strip()
                    
                    if text:
                        chunk = self._create_chunk(
                            text=text,
                            source=file_path.name,
                            file_type=FileType.DOCX,
                            chunk_index=chunk_index,
                            element_type="paragraph",
                            style=paragraph.style.name if paragraph.style else None,
                        )
                        chunks.append(chunk)
                        chunk_index += 1
                
                elif isinstance(element, CT_Tbl):
                    # Table
                    table = Table(element, doc)
                    table_text = self._extract_table_text(table)
                    
                    if table_text.strip():
                        chunk = self._create_chunk(
                            text=table_text,
                            source=file_path.name,
                            file_type=FileType.DOCX,
                            chunk_index=chunk_index,
                            element_type="table",
                            table_rows=len(table.rows),
                            table_columns=len(table.columns) if table.rows else 0,
                        )
                        chunks.append(chunk)
                        chunk_index += 1
            
            # Extract images and shapes if enabled
            if config.extract_charts:
                image_chunks = self._extract_images(doc, file_path.name, chunk_index)
                chunks.extend(image_chunks)
            
            self.logger.info(
                "Word document processing completed",
                file_path=str(file_path),
                chunks_created=len(chunks),
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from Word document: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _extract_table_text(self, table: Table) -> str:
        """
        Extract text from a Word table.
        
        Args:
            table: Word table object
            
        Returns:
            Formatted table text
        """
        table_text = []
        
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                row_text.append(cell_text)
            
            if any(row_text):  # Only add non-empty rows
                table_text.append(" | ".join(row_text))
        
        return "\n".join(table_text)
    
    def _extract_images(
        self,
        doc: Document,
        source: str,
        chunk_start_index: int,
    ) -> List[DocumentChunk]:
        """
        Extract images and shapes from Word document.
        
        Args:
            doc: Word document object
            source: Source file name
            chunk_start_index: Starting index for chunks
            
        Returns:
            List of image chunks
        """
        chunks = []
        
        try:
            # Extract inline shapes (images, charts, etc.)
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    chunk = self._create_chunk(
                        text=f"[IMAGE: {rel.target_ref}]",
                        source=source,
                        file_type=FileType.DOCX,
                        chunk_index=chunk_start_index + len(chunks),
                        element_type="image",
                        image_ref=rel.target_ref,
                    )
                    chunks.append(chunk)
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract images from Word document",
                error=str(e),
            )
        
        return chunks
    
    def get_document_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from Word document.
        
        Args:
            file_path: Path to Word document
            
        Returns:
            Dictionary with document metadata
        """
        if not PYTHON_DOCX_AVAILABLE:
            return {}
        
        try:
            doc = Document(str(file_path))
            core_props = doc.core_properties
            
            return {
                "title": core_props.title or "",
                "author": core_props.author or "",
                "subject": core_props.subject or "",
                "keywords": core_props.keywords or "",
                "comments": core_props.comments or "",
                "created": core_props.created.isoformat() if core_props.created else "",
                "modified": core_props.modified.isoformat() if core_props.modified else "",
                "last_modified_by": core_props.last_modified_by or "",
                "revision": core_props.revision or 0,
            }
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract Word document metadata",
                file_path=str(file_path),
                error=str(e),
            )
            return {}
