"""
ZIP file handler for archive extraction and recursive processing.

This module handles ZIP archives with support for:
- Recursive file extraction
- Nested archive handling
- File type filtering
- Safe extraction with size limits
"""

import tempfile
import zipfile
from pathlib import Path
from typing import List

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class ZipHandler(BaseFileHandler):
    """Handler for ZIP archive files with recursive processing."""
    
    def __init__(self):
        """Initialize ZIP handler."""
        super().__init__()
        self.max_extraction_size = 500 * 1024 * 1024  # 500MB limit
        self.max_files = 1000  # Maximum files to extract
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.ZIP]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type in [
            "application/zip",
            "application/x-zip-compressed",
        ]
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate ZIP file can be opened."""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # Test the ZIP file
                zip_file.testzip()
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from ZIP archive."""
        chunks = []
        
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # Get file list and check limits
                file_list = zip_file.filelist
                
                self.logger.info(
                    "Processing ZIP archive",
                    file_path=str(file_path),
                    files_in_archive=len(file_list),
                )
                
                # Check extraction limits
                total_size = sum(info.file_size for info in file_list)
                if total_size > self.max_extraction_size:
                    raise ExtractionError(
                        f"ZIP archive too large: {total_size} bytes exceeds limit of {self.max_extraction_size} bytes",
                        file_path=str(file_path),
                    )
                
                if len(file_list) > self.max_files:
                    self.logger.warning(
                        "ZIP archive contains too many files, processing first %d files",
                        self.max_files,
                        file_path=str(file_path),
                    )
                    file_list = file_list[:self.max_files]
                
                # Create temporary directory for extraction
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # Extract and process files
                    processed_files = 0
                    for file_info in file_list:
                        if file_info.is_dir():
                            continue
                        
                        try:
                            # Extract file
                            extracted_path = self._extract_file_safely(
                                zip_file, file_info, temp_path
                            )
                            
                            if extracted_path:
                                # Process extracted file
                                file_chunks = self._process_extracted_file(
                                    extracted_path, file_info.filename, config, len(chunks)
                                )
                                chunks.extend(file_chunks)
                                processed_files += 1
                        
                        except Exception as e:
                            self.logger.warning(
                                "Failed to process file from ZIP",
                                zip_file=str(file_path),
                                file_name=file_info.filename,
                                error=str(e),
                            )
            
            self.logger.info(
                "ZIP processing completed",
                file_path=str(file_path),
                files_processed=processed_files,
                chunks_created=len(chunks),
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from ZIP archive: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _extract_file_safely(
        self,
        zip_file: zipfile.ZipFile,
        file_info: zipfile.ZipInfo,
        temp_path: Path,
    ) -> Path:
        """
        Safely extract a file from ZIP archive.
        
        Args:
            zip_file: ZipFile object
            file_info: File info from ZIP
            temp_path: Temporary extraction directory
            
        Returns:
            Path to extracted file or None if extraction failed
        """
        try:
            # Sanitize filename to prevent directory traversal
            safe_filename = self._sanitize_filename(file_info.filename)
            if not safe_filename:
                return None
            
            # Create extraction path
            extract_path = temp_path / safe_filename
            extract_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Extract file
            with zip_file.open(file_info) as source:
                with open(extract_path, 'wb') as target:
                    # Read in chunks to avoid memory issues
                    while True:
                        chunk = source.read(8192)
                        if not chunk:
                            break
                        target.write(chunk)
            
            return extract_path
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract file from ZIP",
                file_name=file_info.filename,
                error=str(e),
            )
            return None
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to prevent directory traversal attacks.
        
        Args:
            filename: Original filename from ZIP
            
        Returns:
            Sanitized filename or empty string if unsafe
        """
        # Remove directory traversal attempts
        safe_name = filename.replace('..', '').replace('/', '_').replace('\\', '_')
        
        # Remove leading/trailing whitespace and dots
        safe_name = safe_name.strip('. ')
        
        # Ensure filename is not empty and not too long
        if not safe_name or len(safe_name) > 255:
            return ""
        
        return safe_name
    
    def _process_extracted_file(
        self,
        file_path: Path,
        original_name: str,
        config: IngestionConfig,
        chunk_start_index: int,
    ) -> List[DocumentChunk]:
        """
        Process an extracted file using appropriate handler.
        
        Args:
            file_path: Path to extracted file
            original_name: Original filename in ZIP
            config: Ingestion configuration
            chunk_start_index: Starting chunk index
            
        Returns:
            List of document chunks
        """
        try:
            # Import handlers here to avoid circular imports
            from . import (
                CSVHandler, HTMLHandler, ImageHandler, PDFHandler,
                PPTXHandler, TextHandler, WordHandler
            )
            
            # Determine file type and select handler
            file_extension = file_path.suffix.lower()
            handler = None
            
            if file_extension == '.pdf':
                handler = PDFHandler()
            elif file_extension in ['.docx', '.doc']:
                handler = WordHandler()
            elif file_extension in ['.pptx', '.ppt']:
                handler = PPTXHandler()
            elif file_extension in ['.txt', '.md']:
                handler = TextHandler()
            elif file_extension == '.csv':
                handler = CSVHandler()
            elif file_extension in ['.html', '.htm']:
                handler = HTMLHandler()
            elif file_extension in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                handler = ImageHandler()
            
            if handler and handler.validate_file(file_path):
                # Process file with appropriate handler
                result = handler.extract_content(file_path, config)
                
                if result.success:
                    # Update chunk metadata to include ZIP context
                    for chunk in result.chunks:
                        chunk.chunk_index += chunk_start_index
                        chunk.metadata.update({
                            'extracted_from_zip': True,
                            'original_zip_path': original_name,
                        })
                    
                    return result.chunks
            
            # If no specific handler, create a basic chunk
            return [
                self._create_chunk(
                    text=f"[FILE: {original_name} extracted from ZIP archive]",
                    source=original_name,
                    file_type=FileType.UNKNOWN,
                    chunk_index=chunk_start_index,
                    extracted_from_zip=True,
                )
            ]
            
        except Exception as e:
            self.logger.warning(
                "Failed to process extracted file",
                file_path=str(file_path),
                original_name=original_name,
                error=str(e),
            )
            return []
    
    def analyze_zip_structure(self, file_path: Path) -> dict:
        """
        Analyze ZIP archive structure.
        
        Args:
            file_path: Path to ZIP file
            
        Returns:
            Dictionary with ZIP analysis
        """
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                file_list = zip_file.filelist
                
                # Analyze file types
                file_types = {}
                total_size = 0
                
                for file_info in file_list:
                    if not file_info.is_dir():
                        extension = Path(file_info.filename).suffix.lower()
                        file_types[extension] = file_types.get(extension, 0) + 1
                        total_size += file_info.file_size
                
                return {
                    "total_files": len([f for f in file_list if not f.is_dir()]),
                    "total_directories": len([f for f in file_list if f.is_dir()]),
                    "total_size": total_size,
                    "file_types": file_types,
                    "compression_ratio": zip_file.fp.seek(0, 2) / total_size if total_size > 0 else 0,
                }
                
        except Exception as e:
            self.logger.warning(
                "Failed to analyze ZIP structure",
                file_path=str(file_path),
                error=str(e),
            )
            return {"error": str(e)}
