"""
PowerPoint presentation handler for PPTX files.

This module handles Microsoft PowerPoint presentations with support for:
- Text extraction from slides
- Chart and image detection
- Table extraction
- Slide-level processing
"""

from pathlib import Path
from typing import List

try:
    from pptx import Presentation
    from pptx.enum.shapes import MSO_SHAPE_TYPE
    PYTHON_PPTX_AVAILABLE = True
except ImportError:
    PYTHON_PPTX_AVAILABLE = False

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class PPTXHandler(BaseFileHandler):
    """Handler for Microsoft PowerPoint presentations (PPTX)."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.PPTX, FileType.PPT]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type in [
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "application/vnd.ms-powerpoint",
        ]
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate PowerPoint presentation can be opened."""
        if not PYTHON_PPTX_AVAILABLE:
            raise ExtractionError(
                "python-pptx is not available. Install with: pip install python-pptx",
                file_path=str(file_path),
            )
        
        try:
            # Only PPTX files are supported by python-pptx
            if file_path.suffix.lower() == '.ppt':
                self.logger.warning(
                    "PPT files require conversion to PPTX. Consider using LibreOffice or similar.",
                    file_path=str(file_path),
                )
                return False
            
            prs = Presentation(str(file_path))
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from PowerPoint presentation."""
        if not PYTHON_PPTX_AVAILABLE:
            raise ExtractionError(
                "python-pptx is not available. Install with: pip install python-pptx",
                file_path=str(file_path),
            )
        
        if file_path.suffix.lower() == '.ppt':
            raise ExtractionError(
                "PPT files are not supported. Please convert to PPTX format.",
                file_path=str(file_path),
            )
        
        chunks = []
        
        try:
            # Open PowerPoint presentation
            prs = Presentation(str(file_path))
            
            self.logger.info(
                "Processing PowerPoint presentation",
                file_path=str(file_path),
                slide_count=len(prs.slides),
            )
            
            # Process each slide
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_chunks = self._extract_slide_content(
                    slide, file_path.name, slide_num, len(chunks), config
                )
                chunks.extend(slide_chunks)
            
            self.logger.info(
                "PowerPoint processing completed",
                file_path=str(file_path),
                slides_processed=len(prs.slides),
                chunks_created=len(chunks),
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from PowerPoint presentation: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _extract_slide_content(
        self,
        slide,
        source: str,
        slide_number: int,
        chunk_start_index: int,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """
        Extract content from a single slide.
        
        Args:
            slide: PowerPoint slide object
            source: Source file name
            slide_number: Slide number (1-based)
            chunk_start_index: Starting index for chunks
            config: Ingestion configuration
            
        Returns:
            List of chunks from this slide
        """
        chunks = []
        chunk_index = chunk_start_index
        
        # Extract text from all shapes
        slide_text_parts = []
        
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                slide_text_parts.append(shape.text.strip())
            
            # Handle tables
            if shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                table_text = self._extract_table_from_shape(shape)
                if table_text.strip():
                    chunk = self._create_chunk(
                        text=table_text,
                        source=source,
                        file_type=FileType.PPTX,
                        page_number=slide_number,
                        chunk_index=chunk_index,
                        element_type="table",
                        slide_number=slide_number,
                    )
                    chunks.append(chunk)
                    chunk_index += 1
            
            # Handle charts and images if enabled
            elif config.extract_charts and shape.shape_type in [
                MSO_SHAPE_TYPE.CHART,
                MSO_SHAPE_TYPE.PICTURE,
            ]:
                shape_chunk = self._extract_chart_or_image(
                    shape, source, slide_number, chunk_index
                )
                if shape_chunk:
                    chunks.append(shape_chunk)
                    chunk_index += 1
        
        # Create a chunk for the slide's text content
        if slide_text_parts:
            slide_text = "\n\n".join(slide_text_parts)
            chunk = self._create_chunk(
                text=slide_text,
                source=source,
                file_type=FileType.PPTX,
                page_number=slide_number,
                chunk_index=chunk_index,
                element_type="slide_text",
                slide_number=slide_number,
            )
            chunks.append(chunk)
        
        return chunks
    
    def _extract_table_from_shape(self, shape) -> str:
        """
        Extract text from a table shape.
        
        Args:
            shape: Table shape object
            
        Returns:
            Formatted table text
        """
        table_text = []
        
        try:
            table = shape.table
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_text.append(cell_text)
                
                if any(row_text):  # Only add non-empty rows
                    table_text.append(" | ".join(row_text))
        
        except Exception as e:
            self.logger.warning(
                "Failed to extract table from shape",
                error=str(e),
            )
        
        return "\n".join(table_text)
    
    def _extract_chart_or_image(
        self,
        shape,
        source: str,
        slide_number: int,
        chunk_index: int,
    ) -> DocumentChunk:
        """
        Extract information from chart or image shape.
        
        Args:
            shape: Chart or image shape object
            source: Source file name
            slide_number: Slide number
            chunk_index: Chunk index
            
        Returns:
            Document chunk for the chart/image
        """
        try:
            if shape.shape_type == MSO_SHAPE_TYPE.CHART:
                # Extract chart information
                chart = shape.chart
                chart_type = chart.chart_type if hasattr(chart, 'chart_type') else "unknown"
                
                return self._create_chunk(
                    text=f"[CHART: {chart_type} chart on slide {slide_number}]",
                    source=source,
                    file_type=FileType.PPTX,
                    page_number=slide_number,
                    chunk_index=chunk_index,
                    element_type="chart",
                    slide_number=slide_number,
                    chart_type=str(chart_type),
                )
            
            elif shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                # Extract image information
                return self._create_chunk(
                    text=f"[IMAGE: Picture on slide {slide_number}]",
                    source=source,
                    file_type=FileType.PPTX,
                    page_number=slide_number,
                    chunk_index=chunk_index,
                    element_type="image",
                    slide_number=slide_number,
                )
        
        except Exception as e:
            self.logger.warning(
                "Failed to extract chart/image from shape",
                slide_number=slide_number,
                error=str(e),
            )
        
        return None
    
    def get_presentation_metadata(self, file_path: Path) -> dict:
        """
        Extract metadata from PowerPoint presentation.
        
        Args:
            file_path: Path to PowerPoint presentation
            
        Returns:
            Dictionary with presentation metadata
        """
        if not PYTHON_PPTX_AVAILABLE:
            return {}
        
        try:
            prs = Presentation(str(file_path))
            core_props = prs.core_properties
            
            return {
                "title": core_props.title or "",
                "author": core_props.author or "",
                "subject": core_props.subject or "",
                "keywords": core_props.keywords or "",
                "comments": core_props.comments or "",
                "created": core_props.created.isoformat() if core_props.created else "",
                "modified": core_props.modified.isoformat() if core_props.modified else "",
                "last_modified_by": core_props.last_modified_by or "",
                "revision": core_props.revision or 0,
                "slide_count": len(prs.slides),
            }
            
        except Exception as e:
            self.logger.warning(
                "Failed to extract PowerPoint metadata",
                file_path=str(file_path),
                error=str(e),
            )
            return {}
