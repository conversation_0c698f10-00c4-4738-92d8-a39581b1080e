"""
CSV file handler for structured data extraction.

This module handles CSV files with support for:
- Automatic delimiter detection
- Header row detection
- Data type inference
- Large file processing
"""

import csv
import chardet
from pathlib import Path
from typing import List

from ..core import (
    DocumentChunk,
    ExtractionError,
    FileType,
    IngestionConfig,
)
from .base_handler import BaseFileHandler


class CSVHandler(BaseFileHandler):
    """Handler for CSV files."""
    
    @property
    def supported_types(self) -> List[FileType]:
        """Return supported file types."""
        return [FileType.CSV]
    
    def _can_handle_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        return mime_type in [
            "text/csv",
            "application/csv",
            "text/comma-separated-values",
        ]
    
    def _validate_file_impl(self, file_path: Path) -> bool:
        """Validate CSV file can be read."""
        try:
            # Try to detect encoding and read a few lines
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)
            
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            
            # Try to read and parse a few lines
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f)
                for i, row in enumerate(reader):
                    if i >= 3:  # Read first 3 rows
                        break
            
            return True
        except Exception:
            return False
    
    def _extract_content_impl(
        self,
        file_path: Path,
        config: IngestionConfig,
    ) -> List[DocumentChunk]:
        """Extract content from CSV file."""
        chunks = []
        
        try:
            # Detect file encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read(8192)  # Read more data for better detection
            
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            
            # Detect CSV dialect
            with open(file_path, 'r', encoding=encoding) as f:
                sample = f.read(1024)
                dialect = csv.Sniffer().sniff(sample)
            
            self.logger.info(
                "Processing CSV file",
                file_path=str(file_path),
                encoding=encoding,
                delimiter=repr(dialect.delimiter),
                quotechar=repr(dialect.quotechar),
            )
            
            # Read CSV content
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f, dialect=dialect)
                
                # Read header row
                try:
                    headers = next(reader)
                    has_header = True
                except StopIteration:
                    # Empty file
                    return chunks
                
                # Process data rows
                rows_processed = 0
                current_chunk_rows = []
                chunk_size = 100  # Process in chunks of 100 rows
                
                for row_num, row in enumerate(reader, 1):
                    current_chunk_rows.append(row)
                    rows_processed += 1
                    
                    # Create chunk when we reach chunk_size or end of file
                    if len(current_chunk_rows) >= chunk_size:
                        chunk = self._create_csv_chunk(
                            headers, current_chunk_rows, file_path.name, len(chunks)
                        )
                        chunks.append(chunk)
                        current_chunk_rows = []
                
                # Process remaining rows
                if current_chunk_rows:
                    chunk = self._create_csv_chunk(
                        headers, current_chunk_rows, file_path.name, len(chunks)
                    )
                    chunks.append(chunk)
            
            self.logger.info(
                "CSV processing completed",
                file_path=str(file_path),
                rows_processed=rows_processed,
                chunks_created=len(chunks),
                columns=len(headers) if has_header else 0,
            )
            
            return chunks
            
        except Exception as e:
            raise ExtractionError(
                f"Failed to extract content from CSV file: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def _create_csv_chunk(
        self,
        headers: List[str],
        rows: List[List[str]],
        source: str,
        chunk_index: int,
    ) -> DocumentChunk:
        """
        Create a document chunk from CSV data.
        
        Args:
            headers: Column headers
            rows: Data rows
            source: Source file name
            chunk_index: Chunk index
            
        Returns:
            Document chunk with formatted CSV data
        """
        # Format CSV data as text
        text_lines = []
        
        # Add header
        if headers:
            text_lines.append(" | ".join(headers))
            text_lines.append("-" * len(" | ".join(headers)))
        
        # Add data rows
        for row in rows:
            # Pad row to match header length
            padded_row = row + [""] * (len(headers) - len(row))
            text_lines.append(" | ".join(padded_row[:len(headers)]))
        
        text_content = "\n".join(text_lines)
        
        return self._create_chunk(
            text=text_content,
            source=source,
            file_type=FileType.CSV,
            chunk_index=chunk_index,
            row_count=len(rows),
            column_count=len(headers),
            start_row=chunk_index * 100 + 1,  # Approximate row numbers
            end_row=chunk_index * 100 + len(rows),
        )
    
    def analyze_csv_structure(self, file_path: Path) -> dict:
        """
        Analyze CSV file structure and provide statistics.
        
        Args:
            file_path: Path to CSV file
            
        Returns:
            Dictionary with CSV analysis
        """
        try:
            # Detect encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read(8192)
            
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
            
            # Detect dialect
            with open(file_path, 'r', encoding=encoding) as f:
                sample = f.read(1024)
                dialect = csv.Sniffer().sniff(sample)
            
            # Analyze structure
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f, dialect=dialect)
                
                # Read header
                try:
                    headers = next(reader)
                    column_count = len(headers)
                except StopIteration:
                    return {"error": "Empty file"}
                
                # Count rows and analyze data
                row_count = 0
                for row in reader:
                    row_count += 1
                    if row_count > 10000:  # Limit for large files
                        break
            
            return {
                "encoding": encoding,
                "delimiter": dialect.delimiter,
                "quotechar": dialect.quotechar,
                "column_count": column_count,
                "row_count": row_count,
                "headers": headers,
                "estimated_size": file_path.stat().st_size,
            }
            
        except Exception as e:
            self.logger.warning(
                "Failed to analyze CSV structure",
                file_path=str(file_path),
                error=str(e),
            )
            return {"error": str(e)}
