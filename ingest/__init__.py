"""
Production-grade document ingestion system with chart extraction and vector storage.

This module provides comprehensive document processing capabilities including:
- Multi-format file support (PDF, DOCX, PPTX, images, etc.)
- Chart detection and extraction with AI-powered captioning
- OCR and language detection
- Vector embeddings and FAISS storage
- Concurrent processing with timeout handling
"""

from .core.models import DocumentChunk, VectorEntry, ChartCaption, IngestionConfig, ProcessingResult
from .core.interfaces import FileHandler, ChartExtractor, OCRProcessor
from .file_loader import FileLoader
from .vector_store import VectorStore
from .gpt_processor import GPTContentProcessor

__version__ = "1.0.0"
__author__ = "GrowthHive Team"

__all__ = [
    "DocumentChunk",
    "VectorEntry", 
    "ChartCaption",
    "FileHandler",
    "ChartExtractor",
    "OCRProcessor",
    "FileLoader",
    "VectorStore",
]
