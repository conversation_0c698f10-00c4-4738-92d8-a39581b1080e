"""
File validation utilities for the document ingestion system.

This module provides comprehensive file validation including size checks,
type detection, and integrity validation using multiple methods.
"""

import mimetypes
from pathlib import Path
from typing import Dict, List, Optional

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

from ..core import (
    FileCorruptedError,
    FileSizeExceededError,
    FileType,
    FileValidator,
    UnsupportedFileTypeError,
)
from .logger import get_logger

logger = get_logger(__name__)


class FileValidatorImpl(FileValidator):
    """Implementation of file validation using multiple detection methods."""
    
    # MIME type mappings for supported file types
    MIME_TYPE_MAPPING = {
        "application/pdf": FileType.PDF,
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": FileType.DOCX,
        "application/msword": FileType.DOC,
        "application/vnd.openxmlformats-officedocument.presentationml.presentation": FileType.PPTX,
        "application/vnd.ms-powerpoint": FileType.PPT,
        "text/csv": FileType.CSV,
        "text/plain": FileType.TXT,
        "text/markdown": FileType.MD,
        "text/html": FileType.HTML,
        "image/jpeg": FileType.JPEG,
        "image/jpg": FileType.JPG,
        "image/png": FileType.PNG,
        "image/webp": FileType.WEBP,
        "image/gif": FileType.GIF,
        "application/zip": FileType.ZIP,
    }
    
    # File extension mappings
    EXTENSION_MAPPING = {
        ".pdf": FileType.PDF,
        ".docx": FileType.DOCX,
        ".doc": FileType.DOC,
        ".pptx": FileType.PPTX,
        ".ppt": FileType.PPT,
        ".csv": FileType.CSV,
        ".txt": FileType.TXT,
        ".md": FileType.MD,
        ".markdown": FileType.MD,
        ".html": FileType.HTML,
        ".htm": FileType.HTM,
        ".jpg": FileType.JPG,
        ".jpeg": FileType.JPEG,
        ".png": FileType.PNG,
        ".webp": FileType.WEBP,
        ".gif": FileType.GIF,
        ".zip": FileType.ZIP,
    }
    
    def validate_file_size(self, file_path: Path, max_size_mb: int) -> bool:
        """
        Validate file size is within limits.
        
        Args:
            file_path: Path to the file
            max_size_mb: Maximum allowed size in MB
            
        Returns:
            True if file size is valid
            
        Raises:
            FileSizeExceededError: If file is too large
        """
        try:
            file_size = file_path.stat().st_size
            max_size_bytes = max_size_mb * 1024 * 1024
            
            if file_size > max_size_bytes:
                raise FileSizeExceededError(
                    f"File size {file_size / 1024 / 1024:.2f}MB exceeds limit of {max_size_mb}MB",
                    file_path=str(file_path),
                )
            
            logger.debug(
                "File size validation passed",
                file_path=str(file_path),
                file_size_mb=file_size / 1024 / 1024,
                max_size_mb=max_size_mb,
            )
            return True
            
        except OSError as e:
            raise FileCorruptedError(
                f"Cannot access file: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def validate_file_type(self, file_path: Path, allowed_types: List[FileType]) -> bool:
        """
        Validate file type is supported.
        
        Args:
            file_path: Path to the file
            allowed_types: List of allowed file types
            
        Returns:
            True if file type is supported
            
        Raises:
            UnsupportedFileTypeError: If file type is not supported
        """
        detected_type = self._detect_file_type(file_path)
        
        if detected_type not in allowed_types:
            raise UnsupportedFileTypeError(
                f"File type {detected_type} is not supported. Allowed types: {allowed_types}",
                file_path=str(file_path),
            )
        
        logger.debug(
            "File type validation passed",
            file_path=str(file_path),
            detected_type=detected_type,
        )
        return True
    
    def validate_file_integrity(self, file_path: Path) -> bool:
        """
        Validate file is not corrupted.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file appears to be intact
            
        Raises:
            FileCorruptedError: If file appears corrupted
        """
        try:
            # Basic checks
            if not file_path.exists():
                raise FileCorruptedError(
                    "File does not exist",
                    file_path=str(file_path),
                )
            
            if not file_path.is_file():
                raise FileCorruptedError(
                    "Path is not a regular file",
                    file_path=str(file_path),
                )
            
            # Check if file is readable
            with open(file_path, 'rb') as f:
                # Try to read first few bytes
                f.read(1024)
            
            logger.debug(
                "File integrity validation passed",
                file_path=str(file_path),
            )
            return True
            
        except (OSError, IOError) as e:
            raise FileCorruptedError(
                f"File integrity check failed: {e}",
                file_path=str(file_path),
                cause=e,
            )
    
    def get_file_info(self, file_path: Path) -> Dict:
        """
        Get comprehensive file information.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
        """
        try:
            stat = file_path.stat()
            
            info = {
                "path": str(file_path),
                "name": file_path.name,
                "size_bytes": stat.st_size,
                "size_mb": stat.st_size / 1024 / 1024,
                "modified_time": stat.st_mtime,
                "file_type": self._detect_file_type(file_path),
                "mime_type": self._get_mime_type(file_path),
            }
            
            if MAGIC_AVAILABLE:
                info["magic_type"] = self._get_magic_type(file_path)
            
            return info
            
        except Exception as e:
            logger.error(
                "Failed to get file info",
                file_path=str(file_path),
                error=str(e),
            )
            return {"path": str(file_path), "error": str(e)}
    
    def _detect_file_type(self, file_path: Path) -> FileType:
        """Detect file type using multiple methods."""
        # Try extension first
        extension = file_path.suffix.lower()
        if extension in self.EXTENSION_MAPPING:
            return self.EXTENSION_MAPPING[extension]
        
        # Try MIME type
        mime_type = self._get_mime_type(file_path)
        if mime_type in self.MIME_TYPE_MAPPING:
            return self.MIME_TYPE_MAPPING[mime_type]
        
        # Try magic if available
        if MAGIC_AVAILABLE:
            magic_type = self._get_magic_type(file_path)
            if magic_type in self.MIME_TYPE_MAPPING:
                return self.MIME_TYPE_MAPPING[magic_type]
        
        return FileType.UNKNOWN
    
    def _get_mime_type(self, file_path: Path) -> Optional[str]:
        """Get MIME type using mimetypes module."""
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type
    
    def _get_magic_type(self, file_path: Path) -> Optional[str]:
        """Get MIME type using python-magic if available."""
        if not MAGIC_AVAILABLE:
            return None
        
        try:
            return magic.from_file(str(file_path), mime=True)
        except Exception:
            return None
