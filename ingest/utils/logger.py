"""
Structured logging configuration for the document ingestion system.

This module provides centralized logging configuration using structlog
for consistent, structured logging throughout the application.
"""

import logging
import sys
from typing import Any

import structlog


def setup_logging(
    level: str = "INFO",
    format_json: bool = False,
    include_timestamp: bool = True,
) -> None:
    """
    Configure structured logging for the application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_json: Whether to output logs in JSON format
        include_timestamp: Whether to include timestamps in logs
    """
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, level.upper()),
    )
    
    # Configure structlog processors
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if include_timestamp:
        processors.insert(-1, structlog.processors.TimeStamper(fmt="iso"))
    
    if format_json:
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


def log_processing_start(
    logger: structlog.stdlib.BoundLogger,
    file_path: str,
    file_type: str,
    **kwargs: Any,
) -> None:
    """Log the start of file processing."""
    logger.info(
        "Starting file processing",
        file_path=file_path,
        file_type=file_type,
        **kwargs,
    )


def log_processing_success(
    logger: structlog.stdlib.BoundLogger,
    file_path: str,
    chunks_created: int,
    processing_time: float,
    **kwargs: Any,
) -> None:
    """Log successful file processing."""
    logger.info(
        "File processing completed successfully",
        file_path=file_path,
        chunks_created=chunks_created,
        processing_time_seconds=processing_time,
        **kwargs,
    )


def log_processing_error(
    logger: structlog.stdlib.BoundLogger,
    file_path: str,
    error: Exception,
    **kwargs: Any,
) -> None:
    """Log file processing error."""
    logger.error(
        "File processing failed",
        file_path=file_path,
        error_type=type(error).__name__,
        error_message=str(error),
        **kwargs,
    )


def log_chart_extraction(
    logger: structlog.stdlib.BoundLogger,
    file_path: str,
    charts_found: int,
    extraction_time: float,
    **kwargs: Any,
) -> None:
    """Log chart extraction results."""
    logger.info(
        "Chart extraction completed",
        file_path=file_path,
        charts_found=charts_found,
        extraction_time_seconds=extraction_time,
        **kwargs,
    )


def log_performance_metrics(
    logger: structlog.stdlib.BoundLogger,
    operation: str,
    duration: float,
    items_processed: int = 0,
    **kwargs: Any,
) -> None:
    """Log performance metrics for operations."""
    metrics = {
        "operation": operation,
        "duration_seconds": duration,
        "items_processed": items_processed,
    }
    
    if items_processed > 0:
        metrics["items_per_second"] = items_processed / duration
    
    logger.info("Performance metrics", **metrics, **kwargs)
