#!/usr/bin/env python3
"""
Lead Status Seeder Script
Seeds all canonical lead statuses into the database with proper names and colors.
<PERSON>les synonyms and ensures uniqueness.
"""

import asyncio
import sys
import os
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from sqlalchemy.dialects.postgresql import insert

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.core.database.connection import get_db
from app.models.lead_reference import LeadStatus
from app.core.logging import logger


class LeadStatusSeeder:
    """Seeder for lead statuses with canonical names and synonyms"""
    
    def __init__(self):
        """Initialize the seeder with canonical lead statuses"""
        self.canonical_statuses = [
            {
                "name": "New Lead",
                "colour": "#28a745",  # Green
                "synonyms": ["New", "new"]
            },
            {
                "name": "Contacted",
                "colour": "#17a2b8",  # Info blue
                "synonyms": ["contacted"]
            },
            {
                "name": "Qualified",
                "colour": "#007bff",  # Primary blue
                "synonyms": ["qualified"]
            },
            {
                "name": "Unqualified",
                "colour": "#6c757d",  # Secondary gray
                "synonyms": ["Not Qualified", "Unqualified", "unqualified", "Not Interested"]
            },
            {
                "name": "Not Interested",
                "colour": "#dc3545",  # Danger red
                "synonyms": []
            },
            {
                "name": "Follow up Required",
                "colour": "#ffc107",  # Warning yellow
                "synonyms": []
            },
            {
                "name": "Call Back",
                "colour": "#fd7e14",  # Orange
                "synonyms": ["callback"]
            },
            {
                "name": "1st Call - No Answer",
                "colour": "#6f42c1",  # Purple
                "synonyms": []
            },
            {
                "name": "2nd Call - No Answer",
                "colour": "#6f42c1",  # Purple
                "synonyms": []
            },
            {
                "name": "3rd Call - No Answer",
                "colour": "#6f42c1",  # Purple
                "synonyms": []
            },
            {
                "name": "Wrong Number",
                "colour": "#495057",  # Dark gray
                "synonyms": ["wrong_number", "Wrong Number"]
            },
            {
                "name": "Region is not available",
                "colour": "#e83e8c",  # Pink
                "synonyms": []
            },
            {
                "name": "Out of Budget",
                "colour": "#fd7e14",  # Orange
                "synonyms": []
            },
            {
                "name": "Franchise Database",
                "colour": "#20c997",  # Teal
                "synonyms": []
            },
            {
                "name": "EOI/NDA Sent",
                "colour": "#0dcaf0",  # Cyan
                "synonyms": []
            },
            {
                "name": "EOI/NDA Signed",
                "colour": "#198754",  # Success green
                "synonyms": []
            },
            {
                "name": "Application Form Signed",
                "colour": "#0d6efd",  # Blue
                "synonyms": ["application_signed"]
            },
            {
                "name": "Deposit Paid",
                "colour": "#157347",  # Dark green
                "synonyms": []
            },
            {
                "name": "Franchise Sold",
                "colour": "#146c43",  # Darker green
                "synonyms": ["franchise_sold", "Converted"]
            },
            {
                "name": "Lost",
                "colour": "#dc3545",  # Red
                "synonyms": ["closed-lost"]
            }
        ]
    
    async def seed_statuses(self, db: AsyncSession) -> None:
        """Seed all canonical lead statuses"""
        logger.info("🌱 Starting lead status seeding...")
        
        try:
            # Use upsert to handle existing records
            for status_data in self.canonical_statuses:
                # Check if status already exists
                stmt = select(LeadStatus).where(LeadStatus.name == status_data["name"])
                result = await db.execute(stmt)
                existing_status = result.scalar_one_or_none()
                
                if existing_status:
                    # Update existing status
                    existing_status.colour = status_data["colour"]
                    existing_status.is_active = True
                    existing_status.is_deleted = False
                    logger.info(f"✅ Updated existing status: {status_data['name']}")
                else:
                    # Create new status
                    new_status = LeadStatus(
                        name=status_data["name"],
                        colour=status_data["colour"],
                        is_active=True,
                        is_deleted=False
                    )
                    db.add(new_status)
                    logger.info(f"✅ Created new status: {status_data['name']}")
            
            await db.commit()
            logger.info("🎉 Lead status seeding completed successfully!")
            
            # Log summary
            total_count = await self._get_status_count(db)
            logger.info(f"📊 Total lead statuses in database: {total_count}")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"❌ Error seeding lead statuses: {e}")
            raise
    
    async def _get_status_count(self, db: AsyncSession) -> int:
        """Get total count of active lead statuses"""
        stmt = select(LeadStatus).where(LeadStatus.is_active == True, LeadStatus.is_deleted == False)
        result = await db.execute(stmt)
        return len(result.scalars().all())
    
    def get_synonym_mapping(self) -> Dict[str, str]:
        """Get mapping of synonyms to canonical names"""
        mapping = {}
        for status in self.canonical_statuses:
            canonical_name = status["name"]
            for synonym in status["synonyms"]:
                mapping[synonym.lower()] = canonical_name
        return mapping


async def main():
    """Main function to run the seeder"""
    print("🌱 Lead Status Seeder")
    print("=" * 50)
    
    seeder = LeadStatusSeeder()
    
    try:
        async for db in get_db():
            await seeder.seed_statuses(db)
            break
    except Exception as e:
        logger.error(f"❌ Seeding failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
