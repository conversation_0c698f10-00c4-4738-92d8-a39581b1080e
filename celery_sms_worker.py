#!/usr/bin/env python3
"""
Celery SMS Worker Start<PERSON> Script
Dedicated worker for SMS-related background tasks
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app

if __name__ == "__main__":
    # Start Celery worker for SMS tasks
    celery_app.worker_main([
        "worker",
        "--loglevel=info",
        "--concurrency=2",
        "--queues=sms_queue,sms_onboarding_queue,andy_followup_queue",
        "--hostname=sms-worker@%h"
    ])
