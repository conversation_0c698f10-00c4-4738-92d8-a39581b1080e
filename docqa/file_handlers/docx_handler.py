"""
DOCX file handler using python-docx
"""

from pathlib import Path
from typing import List, Dict
import structlog
import zipfile

try:
    from docx import Document
    from docx.shared import Inches
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

from .base_handler import BaseFileHandler
from ..types import FileProcessingResult

from app.core.logging import logger


class DocxHandler(BaseFileHandler):
    """Handler for DOCX files using python-docx"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.docx', '.doc']
        
        if not DOCX_AVAILABLE:
            logger.warning("python-docx not available, DOCX processing will be limited")
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the file"""
        return file_path.suffix.lower() in self.supported_extensions
    
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """Process DOCX file and extract content"""
        if not self.validate_file(file_path):
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message="File validation failed"
            )
        
        # Handle .doc files (older format)
        if file_path.suffix.lower() == '.doc':
            return self._process_doc_file(file_path)
        
        # Handle .docx files
        return self._process_docx_file(file_path)
    
    def _process_docx_file(self, file_path: Path) -> FileProcessingResult:
        """Process DOCX file"""
        if not DOCX_AVAILABLE:
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message="python-docx library not available"
            )
        
        try:
            logger.info("Processing DOCX file", path=str(file_path))
            
            # Open document
            doc = Document(str(file_path))
            
            text_content = ""
            tables_extracted = []
            images_extracted = []
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content += paragraph.text + "\n"
            
            # Extract tables
            for table_idx, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                    table_data.append(row_data)
                
                if table_data:
                    tables_extracted.append({
                        'index': table_idx,
                        'type': 'table',
                        'data': table_data,
                        'row_count': len(table_data),
                        'col_count': len(table_data[0]) if table_data else 0
                    })
            
            # Extract images (basic info)
            try:
                images_info = self._extract_images_info(file_path)
                images_extracted.extend(images_info)
            except Exception as e:
                logger.warning("Failed to extract image info", error=str(e))
            
            # Clean the extracted text
            cleaned_text = self.clean_text(text_content)
            
            if not cleaned_text:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="No text content extracted from DOCX"
                )
            
            logger.info("DOCX processing completed",
                       path=str(file_path),
                       text_length=len(cleaned_text),
                       tables_count=len(tables_extracted),
                       images_count=len(images_extracted))
            
            return FileProcessingResult(
                success=True,
                text_content=cleaned_text,
                tables_extracted=tables_extracted,
                images_extracted=images_extracted
            )
            
        except Exception as e:
            logger.error("DOCX processing failed", 
                        path=str(file_path),
                        error=str(e))
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"DOCX processing failed: {str(e)}"
            )
    
    def _process_doc_file(self, file_path: Path) -> FileProcessingResult:
        """Process DOC file (legacy format) - basic text extraction"""
        try:
            logger.info("Processing DOC file (legacy format)", path=str(file_path))

            text_content = ""

            # Try multiple approaches for .doc files
            extraction_methods = [
                self._extract_with_textract,
                self._extract_with_antiword,
                self._extract_with_python_docx_fallback,
                self._extract_with_raw_text
            ]

            for method in extraction_methods:
                try:
                    text_content = method(file_path)
                    if text_content and text_content.strip():
                        logger.info(f"DOC extraction successful with {method.__name__}")
                        break
                except Exception as e:
                    logger.debug(f"DOC extraction failed with {method.__name__}: {e}")
                    continue

            if not text_content:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="Could not extract text from DOC file with any method"
                )

            cleaned_text = self.clean_text(text_content)

            if not cleaned_text:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="No meaningful text content extracted from DOC file"
                )

            logger.info("DOC processing completed",
                       path=str(file_path),
                       text_length=len(cleaned_text))

            return FileProcessingResult(
                success=True,
                text_content=cleaned_text
            )

        except Exception as e:
            logger.error("DOC processing failed",
                        path=str(file_path),
                        error=str(e))
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"DOC processing failed: {str(e)}"
            )
    
    def _extract_images_info(self, file_path: Path) -> List[Dict]:
        """Extract basic image information from DOCX file"""
        images_info = []
        
        try:
            # DOCX files are ZIP archives
            with zipfile.ZipFile(str(file_path), 'r') as docx_zip:
                # Look for images in the media folder
                media_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]
                
                for idx, media_file in enumerate(media_files):
                    try:
                        # Get image data
                        image_data = docx_zip.read(media_file)
                        
                        # Extract filename and extension
                        filename = media_file.split('/')[-1]
                        
                        images_info.append({
                            'index': idx,
                            'filename': filename,
                            'path': media_file,
                            'size': len(image_data),
                            'data': image_data
                        })
                        
                    except Exception as e:
                        logger.warning("Failed to extract image", 
                                     media_file=media_file,
                                     error=str(e))
        
        except Exception as e:
            logger.warning("Failed to extract images info", error=str(e))
        
        return images_info

    def _extract_with_textract(self, file_path: Path) -> str:
        """Extract text using textract library"""
        try:
            import textract
            raw_content = textract.process(str(file_path))
            return raw_content.decode('utf-8', errors='ignore')
        except ImportError:
            raise Exception("textract library not available")

    def _extract_with_antiword(self, file_path: Path) -> str:
        """Extract text using antiword command-line tool"""
        import subprocess
        result = subprocess.run(
            ['antiword', str(file_path)],
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            return result.stdout
        else:
            raise Exception(f"antiword failed: {result.stderr}")

    def _extract_with_python_docx_fallback(self, file_path: Path) -> str:
        """Try to extract using python-docx (sometimes works with .doc)"""
        if DOCX_AVAILABLE:
            doc = Document(str(file_path))
            text_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            return '\n'.join(text_parts)
        else:
            raise Exception("python-docx not available")

    def _extract_with_raw_text(self, file_path: Path) -> str:
        """Last resort: try to extract readable text from raw bytes"""
        with open(file_path, 'rb') as f:
            raw_content = f.read()

        # Try to find readable text in the binary data
        text_content = raw_content.decode('utf-8', errors='ignore')

        # Filter out non-printable characters and keep only meaningful text
        import re
        # Keep only printable ASCII and common Unicode characters
        cleaned = re.sub(r'[^\x20-\x7E\u00A0-\u024F\u1E00-\u1EFF]', ' ', text_content)

        # Remove excessive whitespace and short fragments
        lines = []
        for line in cleaned.split('\n'):
            line = ' '.join(line.split())  # Normalize whitespace
            if len(line) > 10:  # Only keep lines with substantial content
                lines.append(line)

        return '\n'.join(lines)
