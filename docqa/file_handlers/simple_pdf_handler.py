"""
Simple PDF file handler with basic text extraction and OCR
"""

import re
import io
from pathlib import Path
from typing import List, Dict, Any, Optional
import structlog

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

from .base_handler import BaseFileHandler
from ..types import FileProcessingResult

from app.core.logging import logger


class SimplePDFHandler(BaseFileHandler):
    """Simple PDF handler with basic text extraction and OCR"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.pdf']
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the given file"""
        return file_path.suffix.lower() == '.pdf'
    
    def validate_file(self, file_path: Path) -> bool:
        """Validate PDF file"""
        if not PYMUPDF_AVAILABLE:
            logger.error("PyMuPDF not available for PDF processing")
            return False
        
        if not file_path.exists():
            logger.error(f"PDF file does not exist: {file_path}")
            return False
            
        if file_path.stat().st_size == 0:
            logger.error(f"PDF file is empty: {file_path}")
            return False
        
        try:
            logger.info(f"Attempting to validate PDF: {file_path} (size: {file_path.stat().st_size} bytes)")
            doc = fitz.open(str(file_path))
            page_count = len(doc)
            doc.close()
            logger.info(f"PDF validation successful: {page_count} pages")
            return True
        except Exception as e:
            logger.error(f"PDF validation failed for {file_path}: {str(e)}")
            return False
    
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """Process PDF file with simple text extraction and OCR"""
        if not self.validate_file(file_path):
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message="File validation failed"
            )

        try:
            logger.info("Processing PDF with simple extraction", path=str(file_path))
            
            # Extract text from PDF
            text_content = self._extract_text_from_pdf(file_path)
            
            # Clean the extracted text
            cleaned_text = self._clean_text(text_content)

            if not cleaned_text:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="No text content extracted from PDF"
                )

            logger.info("Simple PDF processing completed",
                       path=str(file_path),
                       text_length=len(cleaned_text))

            return FileProcessingResult(
                success=True,
                text_content=cleaned_text
            )

        except Exception as e:
            logger.error("Simple PDF processing failed",
                        path=str(file_path),
                        error=str(e))
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"PDF processing failed: {str(e)}"
            )
    
    def _extract_text_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF with simple approach"""
        text_content = ""
        
        # Open PDF document
        doc = fitz.open(str(file_path))
        
        try:
            # Process each page
            for page_num in range(len(doc)):
                # Get page
                page = doc[page_num]
                
                # Extract text
                page_text = page.get_text("text")
                
                # Add page marker and text
                if page_text.strip():
                    text_content += f"\n=== PAGE {page_num + 1} ===\n"
                    text_content += page_text + "\n"
                
                # Try OCR on images if available
                if OCR_AVAILABLE:
                    try:
                        # Extract images
                        image_list = page.get_images()
                        
                        for img_index, img in enumerate(image_list):
                            try:
                                xref = img[0]
                                pix = fitz.Pixmap(doc, xref)
                                
                                if pix.n - pix.alpha < 4:  # GRAY or RGB
                                    # Try OCR on image
                                    ocr_text = self._extract_text_from_image(pix)
                                    
                                    if ocr_text.strip():
                                        text_content += f"\n[IMAGE TEXT - Page {page_num + 1}, Image {img_index + 1}]\n"
                                        text_content += ocr_text + "\n"
                                
                                pix = None  # Free memory
                            except Exception as e:
                                logger.debug(f"Image extraction failed: {str(e)}")
                    except Exception as e:
                        logger.debug(f"Image processing failed: {str(e)}")
        
        finally:
            # Always close the document
            try:
                doc.close()
            except:
                pass
        
        return text_content
    
    def _extract_text_from_image(self, pix) -> str:
        """Extract text from image using OCR"""
        if not OCR_AVAILABLE:
            return ""
        
        try:
            # Convert PyMuPDF pixmap to PIL Image
            img_data = pix.tobytes("png")
            pil_image = Image.open(io.BytesIO(img_data))
            
            # Perform OCR
            ocr_text = pytesseract.image_to_string(pil_image, lang='eng')
            
            # Clean OCR text
            cleaned_ocr = self._clean_ocr_text(ocr_text)
            
            return cleaned_ocr
            
        except Exception as e:
            logger.debug(f"OCR failed: {str(e)}")
            return ""
    
    def _clean_ocr_text(self, text: str) -> str:
        """Clean OCR-extracted text"""
        if not text:
            return ""
        
        # Remove common OCR artifacts
        text = re.sub(r'[^\w\s\.\,\!\?\:\;\-\(\)\[\]\"\'\/\&\%\$\#\@\+\=]', ' ', text)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove very short lines (likely artifacts)
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 2]
        
        return '\n'.join(cleaned_lines).strip()
    
    def _clean_text(self, text: str) -> str:
        """Clean extracted text"""
        if not text:
            return ""
        
        # Fix common PDF extraction issues
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Fix broken words across lines
        text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', text)
        
        # Fix sentences split across lines
        text = re.sub(r'([a-z,])\s*\n\s*([A-Z])', r'\1 \2', text)
        
        # Normalize line breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Remove page numbers and headers/footers
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                cleaned_lines.append('')
                continue
            
            # Skip obvious page numbers
            if re.match(r'^\d+$', line) and len(line) <= 3:
                continue
            
            # Skip common headers/footers
            if any(pattern in line.lower() for pattern in [
                'page ', 'confidential', 'proprietary', 'copyright', '©'
            ]) and len(line) < 50:
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines).strip()
