"""
Base file handler class
"""

from abc import ABC, abstractmethod
from typing import List
from pathlib import Path
import structlog

from ..types import FileProcessingResult
from ..config import get_config

from app.core.logging import logger


class BaseFileHandler(ABC):
    """Base class for file handlers"""
    
    def __init__(self):
        config = get_config()
        self.supported_extensions = []
        self.max_file_size = config.max_file_size_mb * 1024 * 1024  # Convert to bytes
    
    @abstractmethod
    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the file"""
        pass
    
    @abstractmethod
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """Process the file and extract content"""
        pass
    
    def validate_file(self, file_path: Path) -> bool:
        """Validate file before processing"""
        try:
            if not file_path.exists():
                logger.error("File does not exist", path=str(file_path))
                return False
            
            if not file_path.is_file():
                logger.error("Path is not a file", path=str(file_path))
                return False
            
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                logger.error("File too large", 
                           path=str(file_path),
                           size_mb=file_size / (1024 * 1024),
                           max_size_mb=config.max_file_size_mb)
                return False
            
            if file_size == 0:
                logger.error("File is empty", path=str(file_path))
                return False
            
            return True
            
        except Exception as e:
            logger.error("File validation failed", 
                        path=str(file_path),
                        error=str(e))
            return False
    
    def clean_text(self, text: str) -> str:
        """Clean extracted text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Remove leading/trailing whitespace
            cleaned_line = line.strip()
            
            # Skip empty lines and lines with only special characters
            if cleaned_line and len(cleaned_line) > 1:
                cleaned_lines.append(cleaned_line)
        
        # Join lines and normalize whitespace
        cleaned_text = '\n'.join(cleaned_lines)
        
        # Remove excessive newlines
        while '\n\n\n' in cleaned_text:
            cleaned_text = cleaned_text.replace('\n\n\n', '\n\n')
        
        return cleaned_text.strip()
    
    def chunk_text(self, text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
        """Split text into chunks with overlap"""
        if chunk_size is None:
            chunk_size = config.chunk_size
        if overlap is None:
            overlap = config.chunk_overlap
        
        if not text:
            return []
        
        # Split by sentences first, then by words if needed
        sentences = text.split('. ')
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            # Add sentence to current chunk
            test_chunk = current_chunk + ". " + sentence if current_chunk else sentence
            
            # If chunk is getting too long, start a new one
            if len(test_chunk.split()) > chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    
                    # Start new chunk with overlap
                    words = current_chunk.split()
                    if len(words) > overlap:
                        overlap_text = " ".join(words[-overlap:])
                        current_chunk = overlap_text + ". " + sentence
                    else:
                        current_chunk = sentence
                else:
                    # Single sentence is too long, split by words
                    words = sentence.split()
                    for i in range(0, len(words), chunk_size - overlap):
                        word_chunk = " ".join(words[i:i + chunk_size])
                        chunks.append(word_chunk)
                    current_chunk = ""
            else:
                current_chunk = test_chunk
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # Filter out very short chunks
        filtered_chunks = [chunk for chunk in chunks if len(chunk.split()) >= 10]
        
        logger.debug("Text chunked", 
                    original_length=len(text),
                    chunk_count=len(filtered_chunks),
                    avg_chunk_size=sum(len(c.split()) for c in filtered_chunks) / len(filtered_chunks) if filtered_chunks else 0)
        
        return filtered_chunks
