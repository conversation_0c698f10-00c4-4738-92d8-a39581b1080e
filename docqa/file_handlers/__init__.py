"""
File handlers for different document formats
"""

from .pdf_handler import <PERSON><PERSON><PERSON><PERSON>
from .enhanced_pdf_handler import <PERSON>hancedPDFHandler
from .simple_pdf_handler import SimplePDFHandler
from .docx_handler import DocxHandler
from .image_handler import ImageHandler
from .base_handler import BaseFileHandler
from .factory import (
    FileHandlerFactory,
    get_file_handler_factory,
    process_file,
    get_supported_extensions,
    is_supported_file
)

__all__ = [
    "PDFHandler",
    "EnhancedPDFHandler",
    "SimplePDFHandler",
    "DocxHandler",
    "ImageHandler",
    "BaseFileHandler",
    "FileHandlerFactory",
    "get_file_handler_factory",
    "process_file",
    "get_supported_extensions",
    "is_supported_file"
]
