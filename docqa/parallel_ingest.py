"""
Enhanced Parallel Document Ingestion Pipeline
High-performance document processing with concurrent operations and Redis integration
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import structlog
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import hashlib
import uuid

from .ingest import DocumentIngestionService
from .redis_store import get_redis_store
from .vector_store import PgVectorStore, EmbeddingService
from .chart_extract import ChartAnalyzer
from .text_processing.adaptive_chunker import Adaptive<PERSON>hun<PERSON>
from .types import DocumentChunk, IngestionResult

from app.core.logging import logger


class ParallelDocumentProcessor:
    """Enhanced document processor with parallel processing capabilities"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.base_ingestion_service = DocumentIngestionService()
        self.redis_store = None
        self.vector_store = None
        self.embedding_service = None
        self.chart_analyzer = None
        self.adaptive_chunker = None
        
        # Performance tracking
        self.processing_stats = {
            "documents_processed": 0,
            "total_chunks_created": 0,
            "total_processing_time": 0,
            "average_processing_time": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
    
    async def initialize(self):
        """Initialize async components"""
        if not self.redis_store:
            self.redis_store = await get_redis_store()
        
        if not self.vector_store:
            self.vector_store = PgVectorStore()
            await self.vector_store.connect()
        
        if not self.embedding_service:
            self.embedding_service = EmbeddingService()
        
        if not self.chart_analyzer:
            self.chart_analyzer = ChartAnalyzer()
        
        if not self.adaptive_chunker:
            from .config import get_config
            config = get_config()
            self.adaptive_chunker = AdaptiveChunker(
                base_chunk_size=config.chunk_size,
                max_chunk_size=config.chunk_size * 2,
                min_chunk_size=config.chunk_size // 2,
                overlap_ratio=config.chunk_overlap / config.chunk_size
            )
    
    async def process_document_parallel(
        self,
        source: str,
        force_table: Optional[str] = None,
        translate: bool = True,
        extract_charts: bool = True,
        processing_options: Optional[Dict[str, Any]] = None
    ) -> IngestionResult:
        """
        Process document with parallel operations and Redis integration
        
        Args:
            source: Document source URL or path
            force_table: Force routing to specific table
            translate: Enable translation
            extract_charts: Enable chart extraction
            processing_options: Additional processing options
            
        Returns:
            IngestionResult with processing details
        """
        await self.initialize()
        processing_options = processing_options or {}
        start_time = time.time()
        
        try:
            # Step 1: Check Redis cache for existing processing
            document_hash = self._generate_document_hash(source)
            
            if await self.redis_store.is_document_processed(source):
                self.processing_stats["cache_hits"] += 1
                logger.info(f"Document already processed (cached): {source}")
                
                return IngestionResult(
                    success=True,
                    document_hash=document_hash,
                    chunks_created=0,
                    processing_time=0,
                    message="Document already processed (cached)",
                    cached=True
                )
            
            self.processing_stats["cache_misses"] += 1
            
            # Step 2: Parallel content extraction and preprocessing
            extraction_result = await self._parallel_content_extraction(
                source, extract_charts, translate, processing_options
            )
            
            if not extraction_result["success"]:
                raise Exception(f"Content extraction failed: {extraction_result['error']}")
            
            # Step 3: Parallel chunking and embedding generation
            chunks_result = await self._parallel_chunk_processing(
                extraction_result["content"],
                extraction_result["metadata"],
                processing_options
            )
            
            # Step 4: Batch vector storage with optimized inserts
            storage_result = await self._batch_vector_storage(
                chunks_result["chunks"],
                source,
                force_table,
                document_hash
            )
            
            # Step 5: Update Redis cache
            await self.redis_store.mark_document_processed(
                url=source,
                document_hash=document_hash
            )
            
            # Update statistics
            processing_time = time.time() - start_time
            self.processing_stats["documents_processed"] += 1
            self.processing_stats["total_chunks_created"] += len(chunks_result["chunks"])
            self.processing_stats["total_processing_time"] += processing_time
            self.processing_stats["average_processing_time"] = (
                self.processing_stats["total_processing_time"] / 
                self.processing_stats["documents_processed"]
            )
            
            logger.info(f"Document processed successfully: {source}", 
                       chunks=len(chunks_result["chunks"]), 
                       processing_time=processing_time)
            
            return IngestionResult(
                success=True,
                document_hash=document_hash,
                chunks_created=len(chunks_result["chunks"]),
                processing_time=processing_time,
                message="Document processed successfully",
                cached=False,
                metadata={
                    "extraction_time": extraction_result.get("processing_time", 0),
                    "chunking_time": chunks_result.get("processing_time", 0),
                    "storage_time": storage_result.get("processing_time", 0),
                    "total_tokens": chunks_result.get("total_tokens", 0),
                    "charts_extracted": extraction_result.get("charts_count", 0)
                }
            )
            
        except Exception as e:
            logger.error(f"Document processing failed: {e}", source=source)
            return IngestionResult(
                success=False,
                error=str(e),
                processing_time=time.time() - start_time
            )
    
    async def _parallel_content_extraction(
        self,
        source: str,
        extract_charts: bool,
        translate: bool,
        processing_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract content using parallel processing"""
        start_time = time.time()
        
        try:
            # Use thread pool for CPU-intensive operations
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit content extraction task
                content_future = executor.submit(
                    self._extract_content_sync, source, processing_options
                )
                
                # Submit chart extraction task if enabled
                chart_future = None
                if extract_charts:
                    chart_future = executor.submit(
                        self._extract_charts_sync, source, processing_options
                    )
                
                # Wait for content extraction
                content_result = content_future.result()
                
                # Wait for chart extraction if enabled
                charts_result = None
                if chart_future:
                    charts_result = chart_future.result()
                
                # Combine results
                combined_content = content_result["content"]
                metadata = content_result["metadata"]
                
                if charts_result and charts_result["success"]:
                    # Append chart descriptions to content
                    chart_descriptions = "\n\n".join([
                        f"Chart: {chart['description']}" 
                        for chart in charts_result["charts"]
                    ])
                    combined_content += f"\n\nCharts and Diagrams:\n{chart_descriptions}"
                    metadata["charts_count"] = len(charts_result["charts"])
                else:
                    metadata["charts_count"] = 0
                
                # Translation if needed
                if translate and self._needs_translation(combined_content):
                    translation_future = executor.submit(
                        self._translate_content_sync, combined_content
                    )
                    translated_content = translation_future.result()
                    if translated_content["success"]:
                        combined_content = translated_content["content"]
                        metadata["translated"] = True
                
                return {
                    "success": True,
                    "content": combined_content,
                    "metadata": metadata,
                    "processing_time": time.time() - start_time
                }
                
        except Exception as e:
            logger.error(f"Content extraction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    async def _parallel_chunk_processing(
        self,
        content: str,
        metadata: Dict[str, Any],
        processing_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process content into chunks with parallel embedding generation"""
        start_time = time.time()
        
        try:
            # Step 1: Adaptive chunking
            chunks = self.adaptive_chunker.chunk_text(content)
            
            # Step 2: Parallel embedding generation
            chunk_objects = []
            total_tokens = 0
            
            # Process chunks in batches for optimal performance
            batch_size = processing_options.get("embedding_batch_size", 10)
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit embedding tasks in batches
                futures = []
                
                for i in range(0, len(chunks), batch_size):
                    batch = chunks[i:i + batch_size]
                    future = executor.submit(
                        self._generate_embeddings_batch_sync, batch, metadata
                    )
                    futures.append(future)
                
                # Collect results
                for future in as_completed(futures):
                    batch_result = future.result()
                    if batch_result["success"]:
                        chunk_objects.extend(batch_result["chunks"])
                        total_tokens += batch_result["tokens"]
                    else:
                        logger.error(f"Batch embedding failed: {batch_result['error']}")
            
            return {
                "success": True,
                "chunks": chunk_objects,
                "total_tokens": total_tokens,
                "processing_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"Chunk processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    async def _batch_vector_storage(
        self,
        chunks: List[DocumentChunk],
        source: str,
        force_table: Optional[str],
        document_hash: str
    ) -> Dict[str, Any]:
        """Store vectors in database with batch operations"""
        start_time = time.time()
        
        try:
            # Use bulk operations for better performance
            from .vector_store.bulk_operations import BulkVectorOperations
            
            bulk_ops = BulkVectorOperations(self.vector_store)
            
            # Determine target table
            target_table = force_table or self._determine_target_table(source)
            
            # Batch insert chunks
            result = await bulk_ops.batch_insert_chunks(
                chunks=chunks,
                table_name=target_table,
                document_hash=document_hash,
                source_url=source
            )
            
            return {
                "success": result["success"],
                "chunks_stored": result.get("chunks_stored", 0),
                "processing_time": time.time() - start_time
            }
            
        except Exception as e:
            logger.error(f"Vector storage failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    def _generate_document_hash(self, source: str) -> str:
        """Generate unique hash for document"""
        return hashlib.sha256(f"{source}_{datetime.utcnow().date()}".encode()).hexdigest()
    
    def _extract_content_sync(self, source: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronous content extraction wrapper"""
        try:
            result = self.base_ingestion_service.ingest_document(source, **options)
            return {
                "success": result.success,
                "content": getattr(result, 'content', ''),
                "metadata": getattr(result, 'metadata', {}),
                "error": getattr(result, 'error', None)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _extract_charts_sync(self, source: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronous chart extraction wrapper"""
        try:
            # Implement chart extraction logic
            return {"success": True, "charts": []}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _translate_content_sync(self, content: str) -> Dict[str, Any]:
        """Synchronous translation wrapper"""
        try:
            # Implement translation logic
            return {"success": True, "content": content}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _generate_embeddings_batch_sync(
        self, 
        chunks: List[str], 
        metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Synchronous batch embedding generation"""
        try:
            chunk_objects = []
            total_tokens = 0
            
            for i, chunk_text in enumerate(chunks):
                # Generate embedding
                embedding = self.embedding_service.generate_embedding(chunk_text)
                
                # Create chunk object
                chunk = DocumentChunk(
                    id=str(uuid.uuid4()),
                    content=chunk_text,
                    embedding=embedding,
                    metadata={
                        **metadata,
                        "chunk_index": i,
                        "chunk_length": len(chunk_text)
                    }
                )
                
                chunk_objects.append(chunk)
                total_tokens += len(chunk_text.split())
            
            return {
                "success": True,
                "chunks": chunk_objects,
                "tokens": total_tokens
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _needs_translation(self, content: str) -> bool:
        """Check if content needs translation"""
        # Simple heuristic - implement proper language detection
        return False
    
    def _determine_target_table(self, source: str) -> str:
        """Determine target table based on source"""
        if "franchisor" in source.lower():
            return "franchisors"
        return "documents"
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return self.processing_stats.copy()


# Global processor instance
_parallel_processor: Optional[ParallelDocumentProcessor] = None


async def get_parallel_processor() -> ParallelDocumentProcessor:
    """Get or create parallel processor instance"""
    global _parallel_processor
    
    if _parallel_processor is None:
        _parallel_processor = ParallelDocumentProcessor()
        await _parallel_processor.initialize()
    
    return _parallel_processor
