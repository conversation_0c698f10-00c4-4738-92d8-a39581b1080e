"""
Smart Chunking with Token Awareness

This module implements intelligent text chunking using tiktoken for precise token counting
and semantic preservation. Features include:
- 400-token chunks with 50-token overlap
- Semantic unit preservation (paragraphs, sentences)
- Section-aware chunking with context tagging
- Efficient batch processing
"""

import re
from typing import List, Dict, Any, Optional
import structlog
import tiktoken
from dataclasses import dataclass

from ..types import DocumentChunk, DocumentMetadata

from app.core.logging import logger


@dataclass
class ChunkingConfig:
    """Configuration for smart chunking"""
    chunk_size: int = 400  # Target tokens per chunk
    overlap_size: int = 50  # Overlap tokens between chunks
    min_chunk_size: int = 50  # Minimum chunk size
    max_chunk_size: int = 600  # Maximum chunk size
    preserve_sentences: bool = True
    preserve_paragraphs: bool = True
    section_aware: bool = True


@dataclass
class SemanticUnit:
    """Represents a semantic unit (sentence, paragraph, section)"""
    text: str
    unit_type: str  # 'sentence', 'paragraph', 'section'
    start_pos: int
    end_pos: int
    token_count: int
    metadata: Dict[str, Any] = None


class SmartChunker:
    """
    Smart text chunker with token awareness and semantic preservation
    """
    
    def __init__(self, config: Optional[ChunkingConfig] = None):
        """
        Initialize the smart chunker
        
        Args:
            config: Chunking configuration (uses defaults if None)
        """
        self.config = config or ChunkingConfig()
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
        logger.info("Smart chunker initialized", 
                   chunk_size=self.config.chunk_size,
                   overlap_size=self.config.overlap_size)
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken"""
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.debug("Token counting failed", error=str(e))
            # Fallback to approximate count
            return len(text.split()) * 1.3  # Rough approximation
    
    def chunk_text(
        self,
        text: str,
        metadata: Optional[DocumentMetadata] = None,
        sections: Optional[List[Dict[str, Any]]] = None
    ) -> List[DocumentChunk]:
        """
        Chunk text into semantically aware chunks with token limits
        
        Args:
            text: Text to chunk
            metadata: Document metadata
            sections: Detected sections for context tagging
            
        Returns:
            List of DocumentChunk objects
        """
        if not text or not text.strip():
            return []
        
        logger.info("Starting smart chunking", 
                   text_length=len(text),
                   target_chunk_size=self.config.chunk_size)
        
        # Parse text into semantic units
        semantic_units = self._parse_semantic_units(text, sections)
        
        # Create chunks from semantic units
        chunks = self._create_chunks_from_units(semantic_units, metadata)
        
        logger.info("Smart chunking completed", 
                   chunks_created=len(chunks),
                   avg_chunk_size=sum(c.token_count for c in chunks) / len(chunks) if chunks else 0)
        
        return chunks
    
    def _parse_semantic_units(
        self,
        text: str,
        sections: Optional[List[Dict[str, Any]]] = None
    ) -> List[SemanticUnit]:
        """Parse text into semantic units (sentences, paragraphs, sections)"""
        units = []
        
        if self.config.section_aware and sections:
            # Process by sections
            for section in sections:
                section_text = section.get('content', '')
                if section_text.strip():
                    section_units = self._parse_text_units(
                        section_text,
                        section_metadata={'section_title': section.get('title', '')}
                    )
                    units.extend(section_units)
        else:
            # Process entire text
            units = self._parse_text_units(text)
        
        return units
    
    def _parse_text_units(
        self,
        text: str,
        section_metadata: Optional[Dict[str, Any]] = None
    ) -> List[SemanticUnit]:
        """Parse text into paragraphs and sentences"""
        units = []
        
        if self.config.preserve_paragraphs:
            # Split by paragraphs first
            paragraphs = re.split(r'\n\s*\n', text.strip())
            
            current_pos = 0
            for paragraph in paragraphs:
                if not paragraph.strip():
                    continue
                
                paragraph = paragraph.strip()
                token_count = self.count_tokens(paragraph)
                
                # Find actual position in original text
                start_pos = text.find(paragraph, current_pos)
                if start_pos == -1:
                    start_pos = current_pos
                end_pos = start_pos + len(paragraph)
                current_pos = end_pos
                
                if token_count <= self.config.chunk_size:
                    # Paragraph fits in one chunk
                    units.append(SemanticUnit(
                        text=paragraph,
                        unit_type='paragraph',
                        start_pos=start_pos,
                        end_pos=end_pos,
                        token_count=token_count,
                        metadata=section_metadata
                    ))
                else:
                    # Split paragraph into sentences
                    sentence_units = self._split_into_sentences(
                        paragraph, start_pos, section_metadata
                    )
                    units.extend(sentence_units)
        else:
            # Split into sentences directly
            sentence_units = self._split_into_sentences(text, 0, section_metadata)
            units.extend(sentence_units)
        
        return units
    
    def _split_into_sentences(
        self,
        text: str,
        base_pos: int,
        section_metadata: Optional[Dict[str, Any]] = None
    ) -> List[SemanticUnit]:
        """Split text into sentences"""
        units = []
        
        # Improved sentence splitting pattern
        sentence_pattern = r'(?<=[.!?])\s+(?=[A-Z])'
        sentences = re.split(sentence_pattern, text)
        
        current_pos = base_pos
        for sentence in sentences:
            if not sentence.strip():
                continue
            
            sentence = sentence.strip()
            token_count = self.count_tokens(sentence)
            
            start_pos = current_pos
            end_pos = start_pos + len(sentence)
            current_pos = end_pos
            
            if token_count >= self.config.min_chunk_size:
                units.append(SemanticUnit(
                    text=sentence,
                    unit_type='sentence',
                    start_pos=start_pos,
                    end_pos=end_pos,
                    token_count=token_count,
                    metadata=section_metadata
                ))
        
        return units
    
    def _create_chunks_from_units(
        self,
        units: List[SemanticUnit],
        metadata: Optional[DocumentMetadata] = None
    ) -> List[DocumentChunk]:
        """Create chunks from semantic units with overlap"""
        if not units:
            return []
        
        chunks = []
        current_chunk_units = []
        current_token_count = 0
        
        i = 0
        while i < len(units):
            unit = units[i]
            
            # Check if adding this unit would exceed chunk size
            if (current_token_count + unit.token_count > self.config.chunk_size and 
                current_chunk_units):
                
                # Create chunk from current units
                chunk = self._create_chunk_from_units(
                    current_chunk_units, len(chunks), metadata
                )
                chunks.append(chunk)
                
                # Start new chunk with overlap
                overlap_units = self._get_overlap_units(
                    current_chunk_units, self.config.overlap_size
                )
                current_chunk_units = overlap_units
                current_token_count = sum(u.token_count for u in overlap_units)
                
                # Don't increment i, try to add the same unit to new chunk
                continue
            
            # Add unit to current chunk
            current_chunk_units.append(unit)
            current_token_count += unit.token_count
            i += 1
        
        # Create final chunk if there are remaining units
        if current_chunk_units:
            chunk = self._create_chunk_from_units(
                current_chunk_units, len(chunks), metadata
            )
            chunks.append(chunk)
        
        return chunks
    
    def _get_overlap_units(
        self,
        units: List[SemanticUnit],
        target_overlap_tokens: int
    ) -> List[SemanticUnit]:
        """Get units for overlap from the end of current chunk"""
        if not units:
            return []
        
        overlap_units = []
        overlap_tokens = 0
        
        # Start from the end and work backwards
        for unit in reversed(units):
            if overlap_tokens + unit.token_count <= target_overlap_tokens:
                overlap_units.insert(0, unit)
                overlap_tokens += unit.token_count
            else:
                break
        
        return overlap_units
    
    def _create_chunk_from_units(
        self,
        units: List[SemanticUnit],
        chunk_index: int,
        metadata: Optional[DocumentMetadata] = None
    ) -> DocumentChunk:
        """Create a DocumentChunk from semantic units"""
        if not units:
            raise ValueError("Cannot create chunk from empty units")
        
        # Combine text from all units
        chunk_text = ' '.join(unit.text for unit in units)
        
        # Calculate token count
        token_count = sum(unit.token_count for unit in units)
        
        # Gather metadata
        chunk_metadata = {
            'chunk_index': chunk_index,
            'unit_count': len(units),
            'unit_types': list(set(unit.unit_type for unit in units)),
            'start_pos': units[0].start_pos,
            'end_pos': units[-1].end_pos
        }
        
        # Add section information if available
        sections = set()
        for unit in units:
            if unit.metadata and 'section_title' in unit.metadata:
                sections.add(unit.metadata['section_title'])
        
        if sections:
            chunk_metadata['sections'] = list(sections)
            # Add section tag to text for better context
            section_tag = f"[SECTION: {', '.join(sections)}]\n"
            chunk_text = section_tag + chunk_text
        
        # Add document metadata if available
        if metadata:
            chunk_metadata.update({
                'document_id': metadata.document_id,
                'filename': metadata.filename,
                'file_type': metadata.file_type
            })
        
        return DocumentChunk(
            text=chunk_text,
            token_count=token_count,
            metadata=chunk_metadata
        )
