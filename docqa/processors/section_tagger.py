"""
Section-Aware Context Tagging for Enhanced RAG Accuracy

This module implements intelligent section detection and context tagging to improve
RAG answer fidelity by providing structured context information. Features include:
- Automatic section detection using multiple strategies
- Context tagging for better chunk organization
- Hierarchical section structure recognition
- Business document pattern recognition
"""

import re
from typing import List, Dict, Any, Optional
import structlog
from dataclasses import dataclass
from enum import Enum

from app.core.logging import logger


class SectionType(Enum):
    """Types of document sections"""
    TITLE = "title"
    HEADER = "header"
    SUBHEADER = "subheader"
    CONTENT = "content"
    LIST = "list"
    TABLE = "table"
    FOOTER = "footer"
    FEES = "fees"
    REQUIREMENTS = "requirements"
    BENEFITS = "benefits"
    PROCESS = "process"
    CONTACT = "contact"
    LEGAL = "legal"
    FINANCIAL = "financial"
    UNKNOWN = "unknown"


@dataclass
class Section:
    """Represents a document section with context"""
    title: str
    content: str
    section_type: SectionType
    level: int  # Hierarchical level (1 = main section, 2 = subsection, etc.)
    start_line: int
    end_line: int
    parent_section: Optional[str] = None
    subsections: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.subsections is None:
            self.subsections = []
        if self.metadata is None:
            self.metadata = {}


class SectionTagger:
    """
    Intelligent section detection and context tagging system
    """
    
    def __init__(self):
        """Initialize the section tagger with business document patterns"""
        
        # Business document section patterns
        self.business_patterns = {
            SectionType.FEES: [
                r'(?i)^(fees?|costs?|pricing|investment|financial requirements?)[:.]?\s*$',
                r'(?i)^(franchise fee|initial investment|ongoing fees?)[:.]?\s*$',
                r'(?i)^(what does it cost|how much|pricing structure)[:.]?\s*$'
            ],
            SectionType.REQUIREMENTS: [
                r'(?i)^(requirements?|qualifications?|criteria)[:.]?\s*$',
                r'(?i)^(who can apply|eligibility|prerequisites?)[:.]?\s*$',
                r'(?i)^(minimum requirements?|candidate profile)[:.]?\s*$'
            ],
            SectionType.BENEFITS: [
                r'(?i)^(benefits?|advantages?|why choose)[:.]?\s*$',
                r'(?i)^(what you get|support provided|our offer)[:.]?\s*$',
                r'(?i)^(key benefits|value proposition)[:.]?\s*$'
            ],
            SectionType.PROCESS: [
                r'(?i)^(process|procedure|how to|steps?)[:.]?\s*$',
                r'(?i)^(application process|getting started|next steps?)[:.]?\s*$',
                r'(?i)^(how it works|the journey|timeline)[:.]?\s*$'
            ],
            SectionType.CONTACT: [
                r'(?i)^(contact|reach us|get in touch)[:.]?\s*$',
                r'(?i)^(contact information|how to reach|connect with)[:.]?\s*$',
                r'(?i)^(phone|email|address|location)[:.]?\s*$'
            ],
            SectionType.LEGAL: [
                r'(?i)^(legal|terms|conditions|disclaimer)[:.]?\s*$',
                r'(?i)^(terms and conditions|legal notice|disclaimer)[:.]?\s*$',
                r'(?i)^(copyright|trademark|intellectual property)[:.]?\s*$'
            ],
            SectionType.FINANCIAL: [
                r'(?i)^(financial|earnings|revenue|profit)[:.]?\s*$',
                r'(?i)^(financial performance|earnings potential|roi)[:.]?\s*$',
                r'(?i)^(return on investment|profitability|financial projections?)[:.]?\s*$'
            ]
        }
        
        # General section patterns
        self.general_patterns = [
            # Numbered sections (1. Title, 2. Title, etc.)
            r'^\d+\.\s+([A-Z][^.]*?)[:.]?\s*$',
            
            # Lettered sections (A. Title, B. Title, etc.)
            r'^[A-Z]\.\s+([A-Z][^.]*?)[:.]?\s*$',
            
            # Roman numerals (I. Title, II. Title, etc.)
            r'^[IVX]+\.\s+([A-Z][^.]*?)[:.]?\s*$',
            
            # All caps headers
            r'^([A-Z\s]{3,}):?\s*$',
            
            # Title case headers with colons
            r'^([A-Z][A-Za-z\s]+):?\s*$',
            
            # Markdown-style headers
            r'^#{1,6}\s+(.+)$',
            
            # Underlined headers (detected by following line of dashes/equals)
            r'^(.+)\s*$\n^[-=]{3,}\s*$'
        ]
        
        logger.info("Section tagger initialized with business patterns")
    
    def detect_sections(self, text: str) -> List[Section]:
        """
        Detect sections in text using multiple strategies
        
        Args:
            text: Input text to analyze
            
        Returns:
            List of detected sections
        """
        if not text or not text.strip():
            return []
        
        logger.info("Starting section detection", text_length=len(text))
        
        lines = text.split('\n')
        sections = []
        
        # Strategy 1: Business-specific pattern matching
        business_sections = self._detect_business_sections(lines)
        sections.extend(business_sections)
        
        # Strategy 2: General structural pattern matching
        structural_sections = self._detect_structural_sections(lines)
        sections.extend(structural_sections)
        
        # Strategy 3: Content-based section detection
        content_sections = self._detect_content_sections(lines)
        sections.extend(content_sections)
        
        # Merge and organize sections
        organized_sections = self._organize_sections(sections, lines)
        
        logger.info("Section detection completed", 
                   sections_found=len(organized_sections))
        
        return organized_sections
    
    def _detect_business_sections(self, lines: List[str]) -> List[Section]:
        """Detect business-specific sections"""
        sections = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Check against business patterns
            for section_type, patterns in self.business_patterns.items():
                for pattern in patterns:
                    if re.match(pattern, line):
                        # Find section content
                        content_lines = self._extract_section_content(lines, i)
                        
                        section = Section(
                            title=line,
                            content='\n'.join(content_lines),
                            section_type=section_type,
                            level=1,
                            start_line=i,
                            end_line=i + len(content_lines),
                            metadata={'detection_method': 'business_pattern'}
                        )
                        sections.append(section)
                        break
        
        return sections
    
    def _detect_structural_sections(self, lines: List[str]) -> List[Section]:
        """Detect sections based on structural patterns"""
        sections = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Check against general patterns
            for pattern in self.general_patterns:
                match = re.match(pattern, line, re.MULTILINE)
                if match:
                    title = match.group(1) if match.groups() else line
                    
                    # Determine section level based on pattern
                    level = self._determine_section_level(line, pattern)
                    
                    # Extract section content
                    content_lines = self._extract_section_content(lines, i)
                    
                    section = Section(
                        title=title,
                        content='\n'.join(content_lines),
                        section_type=self._classify_section_type(title),
                        level=level,
                        start_line=i,
                        end_line=i + len(content_lines),
                        metadata={'detection_method': 'structural_pattern'}
                    )
                    sections.append(section)
                    break
        
        return sections
    
    def _detect_content_sections(self, lines: List[str]) -> List[Section]:
        """Detect sections based on content analysis"""
        sections = []
        
        # Look for implicit sections based on content patterns
        current_section_lines = []
        current_section_start = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Check if this line indicates a new section
            if self._is_section_boundary(line, lines, i):
                # Save previous section if it has content
                if current_section_lines:
                    content = '\n'.join(current_section_lines)
                    title = self._generate_section_title(content)
                    
                    section = Section(
                        title=title,
                        content=content,
                        section_type=self._classify_section_type(content),
                        level=2,  # Content-based sections are typically subsections
                        start_line=current_section_start,
                        end_line=i - 1,
                        metadata={'detection_method': 'content_analysis'}
                    )
                    sections.append(section)
                
                # Start new section
                current_section_lines = [line] if line else []
                current_section_start = i
            else:
                if line:  # Only add non-empty lines
                    current_section_lines.append(line)
        
        # Add final section
        if current_section_lines:
            content = '\n'.join(current_section_lines)
            title = self._generate_section_title(content)
            
            section = Section(
                title=title,
                content=content,
                section_type=self._classify_section_type(content),
                level=2,
                start_line=current_section_start,
                end_line=len(lines) - 1,
                metadata={'detection_method': 'content_analysis'}
            )
            sections.append(section)
        
        return sections
    
    def _extract_section_content(self, lines: List[str], header_index: int) -> List[str]:
        """Extract content lines for a section"""
        content_lines = []
        
        # Start from the line after the header
        for i in range(header_index + 1, len(lines)):
            line = lines[i].strip()
            
            # Stop if we hit another section header
            if self._looks_like_header(line):
                break
            
            # Add non-empty lines to content
            if line:
                content_lines.append(line)
            
            # Stop after reasonable section length
            if len(content_lines) > 50:  # Prevent overly long sections
                break
        
        return content_lines[:20]  # Limit section content length
    
    def _looks_like_header(self, line: str) -> bool:
        """Check if a line looks like a section header"""
        if not line:
            return False
        
        # Check against all patterns
        for pattern in self.general_patterns:
            if re.match(pattern, line):
                return True
        
        # Check against business patterns
        for patterns in self.business_patterns.values():
            for pattern in patterns:
                if re.match(pattern, line):
                    return True
        
        return False
    
    def _determine_section_level(self, line: str, pattern: str) -> int:
        """Determine hierarchical level of a section"""
        # Numbered sections are typically level 1
        if re.match(r'^\d+\.', line):
            return 1
        
        # Lettered sections are typically level 2
        if re.match(r'^[A-Z]\.', line):
            return 2
        
        # All caps are typically level 1
        if line.isupper():
            return 1
        
        # Markdown headers
        if line.startswith('#'):
            return line.count('#')
        
        # Default to level 2
        return 2
    
    def _classify_section_type(self, text: str) -> SectionType:
        """Classify section type based on content"""
        text_lower = text.lower()
        
        # Check for specific keywords
        if any(word in text_lower for word in ['fee', 'cost', 'price', 'investment', '$']):
            return SectionType.FEES
        elif any(word in text_lower for word in ['requirement', 'qualification', 'criteria']):
            return SectionType.REQUIREMENTS
        elif any(word in text_lower for word in ['benefit', 'advantage', 'support']):
            return SectionType.BENEFITS
        elif any(word in text_lower for word in ['process', 'step', 'procedure', 'how to']):
            return SectionType.PROCESS
        elif any(word in text_lower for word in ['contact', 'phone', 'email', 'address']):
            return SectionType.CONTACT
        elif any(word in text_lower for word in ['legal', 'terms', 'condition', 'disclaimer']):
            return SectionType.LEGAL
        elif any(word in text_lower for word in ['financial', 'earnings', 'revenue', 'profit']):
            return SectionType.FINANCIAL
        else:
            return SectionType.CONTENT
    
    def _is_section_boundary(self, line: str, lines: List[str], index: int) -> bool:
        """Determine if a line represents a section boundary"""
        if not line:
            return False
        
        # Check for paragraph breaks (empty line before and after content)
        if index > 0 and index < len(lines) - 1:
            prev_line = lines[index - 1].strip()
            next_line = lines[index + 1].strip() if index + 1 < len(lines) else ""
            
            # New paragraph after empty line
            if not prev_line and line and next_line:
                return True
        
        return False
    
    def _generate_section_title(self, content: str) -> str:
        """Generate a title for a content-based section"""
        lines = content.split('\n')
        if not lines:
            return "Untitled Section"
        
        # Use first line as title, truncated if too long
        first_line = lines[0].strip()
        if len(first_line) > 50:
            first_line = first_line[:47] + "..."
        
        return first_line or "Untitled Section"
    
    def _organize_sections(self, sections: List[Section], lines: List[str]) -> List[Section]:
        """Organize sections into hierarchical structure"""
        if not sections:
            return []
        
        # Sort sections by start line
        sections.sort(key=lambda s: s.start_line)
        
        # Remove duplicates and overlaps
        unique_sections = []
        for section in sections:
            # Check for overlap with existing sections
            overlaps = False
            for existing in unique_sections:
                if (section.start_line <= existing.end_line and 
                    section.end_line >= existing.start_line):
                    overlaps = True
                    break
            
            if not overlaps:
                unique_sections.append(section)
        
        # Establish parent-child relationships
        for i, section in enumerate(unique_sections):
            for j, potential_parent in enumerate(unique_sections):
                if (j < i and 
                    potential_parent.level < section.level and
                    potential_parent.start_line < section.start_line and
                    potential_parent.end_line > section.end_line):
                    section.parent_section = potential_parent.title
                    potential_parent.subsections.append(section.title)
                    break
        
        return unique_sections
    
    def create_context_tags(self, sections: List[Section]) -> Dict[str, Any]:
        """Create context tags for better RAG retrieval"""
        context_tags = {
            'sections': [],
            'section_types': [],
            'hierarchical_structure': {},
            'business_context': {}
        }
        
        for section in sections:
            section_info = {
                'title': section.title,
                'type': section.section_type.value,
                'level': section.level,
                'start_line': section.start_line,
                'end_line': section.end_line,
                'parent': section.parent_section,
                'subsections': section.subsections
            }
            context_tags['sections'].append(section_info)
            
            if section.section_type.value not in context_tags['section_types']:
                context_tags['section_types'].append(section.section_type.value)
        
        # Create hierarchical structure
        for section in sections:
            if section.level == 1:
                context_tags['hierarchical_structure'][section.title] = {
                    'type': section.section_type.value,
                    'subsections': section.subsections
                }
        
        # Create business context
        business_sections = [s for s in sections if s.section_type in [
            SectionType.FEES, SectionType.REQUIREMENTS, SectionType.BENEFITS,
            SectionType.PROCESS, SectionType.FINANCIAL
        ]]
        
        for section in business_sections:
            context_tags['business_context'][section.section_type.value] = {
                'title': section.title,
                'content_preview': section.content[:200] + "..." if len(section.content) > 200 else section.content
            }
        
        return context_tags
