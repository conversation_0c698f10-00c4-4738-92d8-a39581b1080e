"""
Production RAG Integration Module
Provides easy access to all document processing components for any document type
"""

import structlog
from typing import Dict, List, Any, Optional

# Import document processing components
from .brochure_rag_system import (
    BrochureTextNormalizer as DocumentTextNormalizer,
    BrochureMetadataExtractor as DocumentMetadataExtractor,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as DocumentChunker,
    BrochureSection as DocumentSection,
    BrochureMetadata as DocumentMetadata,
    DocumentChunk
)
from .brochure_qa_system import BrochureQASystem as DocumentQASystem
# Import from the production vector store
from .vector_store.production_embeddings import ProductionEmbeddingService
from .vector_store.production_vector_store import ProductionVectorStore

from app.core.logging import logger

class ProductionRAGSystem:
    """
    Production RAG System Integration

    This class provides a unified interface to all document processing components:
    - Dynamic text normalization and section extraction for any document type
    - Adaptive metadata extraction based on document content
    - Optimized chunking for various content types
    - Enhanced question answering for any document

    Usage:
        rag_system = ProductionRAGSystem()

        # Process any document
        sections, metadata = rag_system.extract_document_structure(text)
        chunks = rag_system.process_document(text, sections, metadata)

        # Store document
        rag_system.store_document(document_id, text, chunks, metadata)

        # Answer questions
        answer = await rag_system.answer_question(question, document_id)
    """
    
    def __init__(self):
        self.text_normalizer = DocumentTextNormalizer()
        self.metadata_extractor = DocumentMetadataExtractor()
        self.chunker = DocumentChunker(chunk_size=350, chunk_overlap=50)
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        self.qa_system = DocumentQASystem()
        
        logger.info("Production RAG System initialized")

    def extract_document_structure(self, text: str) -> tuple[List[DocumentSection], DocumentMetadata]:
        """
        Extract structured information from document text

        Args:
            text: Raw document text

        Returns:
            Tuple of (sections, metadata)
        """
        # Normalize text
        normalized_text = self.text_normalizer.normalize_brochure_text(text)

        # Extract sections
        sections = self.text_normalizer.extract_sections(normalized_text)

        # Extract metadata
        metadata = self.metadata_extractor.extract_metadata(normalized_text, sections)

        return sections, metadata
    
    def process_document(
        self,
        text: str,
        sections: List[DocumentSection] = None,
        metadata: DocumentMetadata = None
    ) -> List[DocumentChunk]:
        """
        Process document text into optimized chunks

        Args:
            text: Document text content
            sections: Optional pre-extracted sections
            metadata: Optional pre-extracted metadata

        Returns:
            List of document chunks with embeddings
        """
        # Extract structure if not provided
        if sections is None or metadata is None:
            sections, metadata = self.extract_document_structure(text)

        # Create chunks
        chunks = self.chunker.chunk_brochure(text, sections, metadata)

        # Generate embeddings
        for chunk in chunks:
            chunk.embedding = self.embedding_service.generate_embedding(chunk.text)

        return chunks
    
    def store_document(
        self,
        document_id: str,
        text: str,
        chunks: List[DocumentChunk] = None,
        metadata: DocumentMetadata = None
    ) -> bool:
        """
        Store document in vector store with enhanced metadata

        Args:
            document_id: Document/Entity ID
            text: Full document text
            chunks: Optional pre-processed chunks
            metadata: Optional document metadata

        Returns:
            True if successful
        """
        # Process document if chunks not provided
        if chunks is None:
            sections, extracted_metadata = self.extract_document_structure(text)
            chunks = self.process_document(text, sections, extracted_metadata)
            if metadata is None:
                metadata = extracted_metadata

        # Create comprehensive document summary
        document_summary = self._create_document_summary(text, metadata)

        # Generate embedding for full document
        embedding = self.embedding_service.generate_embedding(document_summary)
        
        # Enhanced metadata for storage
        storage_metadata = {
            'content_type': 'document',
            'company_name': metadata.company_name if metadata else None,
            'industry': metadata.industry if metadata else None,
            'location': metadata.location if metadata else None,
            'services_count': len(metadata.services) if metadata and metadata.services else 0,
            'contact_info': metadata.contact_info if metadata else {},
            'chunk_count': len(chunks),
            'text_length': len(text)
        }

        # Store in vector store
        return self.vector_store.store_franchisor_embedding(
            franchisor_id=document_id,
            text_content=document_summary,
            embedding=embedding,
            metadata=storage_metadata
        )
    
    async def answer_question(
        self,
        question: str,
        document_id: Optional[str] = None,
        similarity_threshold: float = 0.5,  # Configurable threshold
        top_k: int = 5,
        temperature: float = 0.3  # Balanced for general content
    ) -> Dict[str, Any]:
        """
        Answer question using document-optimized QA

        Args:
            question: User question about the document
            document_id: Optional document/entity filter
            similarity_threshold: Configurable threshold based on content type
            top_k: Number of chunks to retrieve
            temperature: Generation temperature

        Returns:
            Dict with answer, sources, and metadata
        """
        return await self.qa_system.answer_brochure_question(
            question=question,
            franchisor_id=document_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )

    async def answer_brochure_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        similarity_threshold: float = 0.4,  # Lower for marketing content
        top_k: int = 5,
        temperature: float = 0.2  # Balanced for marketing content
    ) -> Dict[str, Any]:
        """
        Backward compatibility method for brochure questions

        Args:
            question: User question about the document
            franchisor_id: Optional franchisor/document filter
            similarity_threshold: Lower threshold for marketing content
            top_k: Number of chunks to retrieve
            temperature: Generation temperature

        Returns:
            Dict with answer, sources, and metadata
        """
        return await self.answer_question(
            question=question,
            document_id=franchisor_id,
            similarity_threshold=similarity_threshold,
            top_k=top_k,
            temperature=temperature
        )

    def _create_document_summary(self, text: str, metadata: DocumentMetadata) -> str:
        """Create comprehensive summary for main embedding"""
        summary_parts = []

        # Company/Entity information
        if metadata and metadata.company_name:
            summary_parts.append(f"Entity: {metadata.company_name}")

        if metadata and metadata.industry:
            summary_parts.append(f"Category: {metadata.industry}")

        if metadata and metadata.location:
            summary_parts.append(f"Location: {metadata.location}")

        # Services/Topics
        if metadata and metadata.services:
            topics_text = "Topics: " + ", ".join(metadata.services[:5])
            summary_parts.append(topics_text)

        # Contact information
        if metadata and metadata.contact_info:
            contact_parts = []
            if metadata.phone:
                contact_parts.append(f"Phone: {metadata.phone}")
            if metadata.email:
                contact_parts.append(f"Email: {metadata.email}")
            if metadata.website:
                contact_parts.append(f"Website: {metadata.website}")

            if contact_parts:
                summary_parts.append("Contact: " + ", ".join(contact_parts))

        # Add normalized text preview
        normalized_text = self.text_normalizer.normalize_brochure_text(text)
        text_preview = normalized_text[:800] + "..." if len(normalized_text) > 800 else normalized_text
        summary_parts.append(f"Content: {text_preview}")

        return "\n".join(summary_parts)

# Create singleton instance with lazy initialization
_production_rag_instance = None

def get_production_rag():
    """Get the singleton instance of ProductionRAGSystem with lazy initialization"""
    global _production_rag_instance
    if _production_rag_instance is None:
        _production_rag_instance = ProductionRAGSystem()
    return _production_rag_instance

# Backward compatibility - create instance when accessed
def production_rag():
    """Backward compatibility function that returns the singleton instance"""
    return get_production_rag()

# Backward compatibility aliases
def brochure_rag():
    """Backward compatibility alias for existing code"""
    return get_production_rag()

BrochureRAGSystem = ProductionRAGSystem  # Class alias
