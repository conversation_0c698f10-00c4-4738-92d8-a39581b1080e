"""
Production-Grade Vector Store
Using pgvector with proper similarity calculation and metadata
"""

import os
import time
import psycopg
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import structlog
from dataclasses import dataclass

from ..config import get_config

from app.core.logging import logger


@dataclass
class RetrievalResult:
    """Clean retrieval result"""
    chunk_id: str
    text: str
    similarity_score: float
    metadata: Dict[str, Any]
    source_info: str


class ProductionVectorStore:
    """Production-grade vector storage with proper metadata"""
    
    def __init__(self):
        from docqa.config import get_config
        self.config = get_config()
        self.db_url = self.config.database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    def clear_franchisor_embeddings(self, franchisor_id: str) -> bool:
        """Clear existing embeddings for a franchisor"""
        try:
            conn = psycopg.connect(self.db_url)
            cur = conn.cursor()
            
            # Clear franchisor embedding
            cur.execute("""
                UPDATE franchisors 
                SET embedding = NULL, updated_at = NOW()
                WHERE id = %s
            """, (franchisor_id,))
            
            rows_updated = cur.rowcount
            conn.commit()
            
            print(f"✅ Cleared embeddings for franchisor {franchisor_id} ({rows_updated} rows updated)")
            return True
            
        except Exception as e:
            print(f"❌ Error clearing embeddings: {e}")
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()
    
    def store_franchisor_embedding(
        self, 
        franchisor_id: str, 
        text_content: str, 
        embedding: List[float],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Store franchisor embedding with proper validation"""
        try:
            # Validate embedding
            if len(embedding) != 1536:
                raise ValueError(f"Invalid embedding dimension: {len(embedding)}")
            
            conn = psycopg.connect(self.db_url)
            cur = conn.cursor()
            
            # Store embedding as proper vector
            cur.execute("""
                UPDATE franchisors 
                SET embedding = %s::vector, updated_at = NOW()
                WHERE id = %s
            """, (embedding, franchisor_id))
            
            if cur.rowcount == 0:
                print(f"❌ No franchisor found with ID: {franchisor_id}")
                return False
            
            conn.commit()
            print(f"✅ Stored embedding for franchisor {franchisor_id}")
            return True

        except Exception as e:
            print(f"❌ Error storing embedding: {e}")
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()

    def store_franchisor_chunks(
        self,
        franchisor_id: str,
        chunks: List,  # List of DocumentChunk objects
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Store franchisor chunks with embeddings in franchisor_chunks table"""
        try:
            conn = psycopg.connect(self.db_url)
            cur = conn.cursor()

            # Create franchisor_chunks table if it doesn't exist
            cur.execute("""
                CREATE TABLE IF NOT EXISTS franchisor_chunks (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    franchisor_id UUID NOT NULL,
                    text TEXT NOT NULL,
                    embedding vector(1536),
                    chunk_index INTEGER NOT NULL,
                    token_count INTEGER NOT NULL,
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    CONSTRAINT fk_franchisor_chunks_franchisor
                        FOREIGN KEY (franchisor_id) REFERENCES franchisors(id) ON DELETE CASCADE
                )
            """)

            # Create indexes if they don't exist
            try:
                cur.execute("""
                    CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_embedding
                    ON franchisor_chunks USING ivfflat (embedding vector_cosine_ops)
                    WITH (lists = 100)
                """)
            except Exception as e:
                print(f"⚠️  Could not create vector index: {e}")
                # Continue anyway - the index is for performance, not functionality

            cur.execute("""
                CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_franchisor_id
                ON franchisor_chunks (franchisor_id)
            """)

            # Delete existing chunks for this franchisor
            cur.execute(
                "DELETE FROM franchisor_chunks WHERE franchisor_id = %s",
                (franchisor_id,)
            )

            # Insert new chunks
            for i, chunk in enumerate(chunks):
                if not chunk.embedding:
                    print(f"⚠️  Chunk missing embedding, skipping: {chunk.id}")
                    continue

                # Validate embedding dimension
                if len(chunk.embedding) != 1536:
                    print(f"❌ Invalid embedding dimension for chunk {chunk.id}: {len(chunk.embedding)}")
                    continue

                chunk_metadata = {**(metadata or {}), **chunk.metadata}

                cur.execute("""
                    INSERT INTO franchisor_chunks
                    (franchisor_id, text, embedding, chunk_index, token_count, metadata)
                    VALUES (%s, %s, %s::vector, %s, %s, %s)
                """, (
                    franchisor_id,
                    chunk.text,
                    chunk.embedding,
                    i,
                    chunk.token_count,
                    chunk_metadata  # PostgreSQL will handle JSON serialization
                ))

            conn.commit()
            print(f"✅ Stored {len(chunks)} franchisor chunks for {franchisor_id}")
            return True

        except Exception as e:
            print(f"❌ Error storing franchisor chunks: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()
    
    def search_similar(
        self, 
        query_embedding: List[float], 
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        franchisor_id: Optional[str] = None
    ) -> List[RetrievalResult]:
        """
        Search for similar content with proper similarity calculation
        
        Args:
            query_embedding: 1536-dim query embedding
            top_k: Number of results to return (3-10 recommended)
            similarity_threshold: Minimum cosine similarity (0.7-0.8 recommended)
            franchisor_id: Optional franchisor filter
            
        Returns:
            List of RetrievalResult objects
        """
        try:
            # Validate query embedding
            if len(query_embedding) != 1536:
                raise ValueError(f"Invalid query embedding dimension: {len(query_embedding)}")
            
            conn = psycopg.connect(self.db_url)
            cur = conn.cursor()
            
            # First search franchisor chunks for detailed content
            results = []

            try:
                # Step 1: Search franchisor_chunks for detailed content (preferred for RAG)
                if franchisor_id:
                    cur.execute("""
                        SELECT fc.id, fc.text, f.name, f.region, f.brochure_url,
                               1 - (fc.embedding <=> %s::vector) as similarity_score
                        FROM franchisor_chunks fc
                        JOIN franchisors f ON fc.franchisor_id = f.id
                        WHERE fc.embedding IS NOT NULL
                            AND f.is_active = true
                            AND f.is_deleted = false
                            AND fc.franchisor_id = %s
                            AND (1 - (fc.embedding <=> %s::vector)) >= %s
                        ORDER BY fc.embedding <=> %s::vector
                        LIMIT %s
                    """, (query_embedding, franchisor_id, query_embedding, similarity_threshold, query_embedding, top_k))
                else:
                    cur.execute("""
                        SELECT fc.id, fc.text, f.name, f.region, f.brochure_url,
                               1 - (fc.embedding <=> %s::vector) as similarity_score
                        FROM franchisor_chunks fc
                        JOIN franchisors f ON fc.franchisor_id = f.id
                        WHERE fc.embedding IS NOT NULL
                            AND f.is_active = true
                            AND f.is_deleted = false
                            AND (1 - (fc.embedding <=> %s::vector)) >= %s
                        ORDER BY fc.embedding <=> %s::vector
                        LIMIT %s
                    """, (query_embedding, query_embedding, similarity_threshold, query_embedding, top_k))

                for row in cur.fetchall():
                    result = RetrievalResult(
                        chunk_id=row[0],
                        text=row[1],  # Actual chunk text for detailed answers
                        similarity_score=float(row[5]),  # similarity_score is at index 5
                        metadata={
                            'type': 'franchisor_chunk',
                            'name': row[2],
                            'region': row[3],
                            'brochure_url': row[4]
                        },
                        source_info=f"Franchisor: {row[2]}"
                    )
                    results.append(result)

            except Exception as e:
                print(f"⚠️  Could not search franchisors table: {e}")

            
            print(f"✅ Found {len(results)} results with similarity >= {similarity_threshold}")
            return results
            
        except Exception as e:
            print(f"❌ Error in similarity search: {e}")
            return []
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()