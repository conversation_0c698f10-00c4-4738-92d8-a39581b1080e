"""
Tests for the central serve module
"""

import pytest
from unittest.mock import Mo<PERSON>, patch
import json

from docqa.serve import ask_question, ask_question_stream, health_check, get_question_context
from docqa.types import RAGResponse


class TestAskQuestion:
    """Test the central ask_question function"""
    
    def test_ask_question_basic(self, mock_openai_client, mock_database, sample_search_results):
        """Test basic question answering"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            # Mock QA service
            mock_qa_service = Mock()
            mock_response = RAGResponse(
                answer="Test answer about franchises",
                sources=sample_search_results,
                processing_time=1.5,
                model_used="gpt-4-turbo"
            )
            mock_qa_service.ask_question.return_value = mock_response
            mock_get_service.return_value = mock_qa_service
            
            # Test the function
            result = ask_question("What franchises are available?")
            
            assert isinstance(result, str)
            assert "Test answer about franchises" in result
            mock_qa_service.ask_question.assert_called_once()
    
    def test_ask_question_with_metadata(self, mock_openai_client, mock_database, sample_search_results):
        """Test question answering with metadata included"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_qa_service = Mock()
            mock_response = RAGResponse(
                answer="Test answer",
                sources=sample_search_results,
                processing_time=1.0,
                model_used="gpt-4-turbo"
            )
            mock_qa_service.ask_question.return_value = mock_response
            mock_get_service.return_value = mock_qa_service
            
            result = ask_question("Test question", include_metadata=True)
            
            assert "--- Sources ---" in result
            assert "Franchisors:" in result
            assert "Test Franchise" in result
    
    def test_ask_question_json_format(self, mock_openai_client, mock_database, sample_search_results):
        """Test JSON format response"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_qa_service = Mock()
            mock_response = RAGResponse(
                answer="Test answer",
                sources=sample_search_results,
                processing_time=1.0,
                model_used="gpt-4-turbo"
            )
            mock_qa_service.ask_question.return_value = mock_response
            mock_get_service.return_value = mock_qa_service
            
            result = ask_question("Test question", format="json", include_metadata=True)
            
            # Should be valid JSON
            parsed = json.loads(result)
            assert parsed['answer'] == "Test answer"
            assert parsed['model_used'] == "gpt-4-turbo"
            assert 'sources' in parsed
    
    def test_ask_question_empty_input(self):
        """Test handling of empty question"""
        result = ask_question("")
        assert "Please provide a valid question" in result
        
        result = ask_question("   ")
        assert "Please provide a valid question" in result
    
    def test_ask_question_error_handling(self, mock_openai_client, mock_database):
        """Test error handling in ask_question"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_get_service.side_effect = Exception("Service error")
            
            with pytest.raises(Exception):
                ask_question("Test question")


class TestAskQuestionStream:
    """Test the streaming question answering"""
    
    def test_ask_question_stream_basic(self, mock_openai_client, mock_database):
        """Test basic streaming functionality"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_qa_service = Mock()
            mock_qa_service.stream_answer.return_value = iter(["Hello", " world", "!"])
            mock_get_service.return_value = mock_qa_service
            
            chunks = list(ask_question_stream("Test question"))
            
            assert chunks == ["Hello", " world", "!"]
            mock_qa_service.stream_answer.assert_called_once()
    
    def test_ask_question_stream_empty_input(self):
        """Test streaming with empty input"""
        chunks = list(ask_question_stream(""))
        assert len(chunks) == 1
        assert "Please provide a valid question" in chunks[0]
    
    def test_ask_question_stream_error(self, mock_openai_client, mock_database):
        """Test streaming error handling"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_get_service.side_effect = Exception("Stream error")
            
            chunks = list(ask_question_stream("Test question"))
            assert len(chunks) == 1
            assert "Error:" in chunks[0]


class TestHealthCheck:
    """Test the health check functionality"""
    
    def test_health_check_healthy(self, mock_openai_client, mock_database):
        """Test health check when system is healthy"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            # Mock embedding service
            mock_embedding_service = Mock()
            mock_embedding_service.generate_embedding.return_value = [0.1] * 1536
            
            # Mock vector store
            mock_vector_store = Mock()
            mock_vector_store._get_connection.return_value.__enter__.return_value = mock_database
            
            # Mock QA service
            mock_qa_service = Mock()
            mock_qa_service.embedding_service = mock_embedding_service
            mock_qa_service.vector_store = mock_vector_store
            mock_get_service.return_value = mock_qa_service
            
            result = health_check()
            
            assert result['status'] == 'healthy'
            assert result['embedding_service'] == 'ok'
            assert result['database'] == 'ok'
            assert 'config' in result
    
    def test_health_check_unhealthy(self, mock_openai_client, mock_database):
        """Test health check when system is unhealthy"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_get_service.side_effect = Exception("Service unavailable")
            
            result = health_check()
            
            assert result['status'] == 'unhealthy'
            assert 'error' in result


class TestGetQuestionContext:
    """Test the question context functionality"""
    
    def test_get_question_context_success(self, mock_openai_client, mock_database, sample_search_results):
        """Test successful context retrieval"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_qa_service = Mock()
            mock_context = {
                'question': 'Test question',
                'results_count': 2,
                'franchisor_results': [sample_search_results[0]],
                'document_results': [sample_search_results[1]],
                'context': 'Test context'
            }
            mock_qa_service.get_conversation_context.return_value = mock_context
            mock_get_service.return_value = mock_qa_service
            
            result = get_question_context("Test question")
            
            assert result['question'] == 'Test question'
            assert result['results_count'] == 2
            assert len(result['franchisor_results']) == 1
            assert len(result['document_results']) == 1
    
    def test_get_question_context_error(self, mock_openai_client, mock_database):
        """Test context retrieval error handling"""
        with patch('docqa.serve.get_qa_service') as mock_get_service:
            mock_get_service.side_effect = Exception("Context error")
            
            result = get_question_context("Test question")
            
            assert 'error' in result


class TestServiceManagement:
    """Test service lifecycle management"""
    
    def test_service_singleton(self, mock_openai_client, mock_database):
        """Test that service instance is reused (singleton pattern)"""
        from docqa.serve import get_qa_service, reset_service
        
        # Reset first
        reset_service()
        
        with patch('docqa.ask.QuestionAnsweringService') as mock_service_class:
            mock_instance = Mock()
            mock_service_class.return_value = mock_instance
            
            # Get service twice
            service1 = get_qa_service()
            service2 = get_qa_service()
            
            # Should be the same instance
            assert service1 is service2
            # Service class should only be called once
            mock_service_class.assert_called_once()
    
    def test_service_reset(self, mock_openai_client, mock_database):
        """Test service reset functionality"""
        from docqa.serve import get_qa_service, reset_service
        
        with patch('docqa.ask.QuestionAnsweringService') as mock_service_class:
            mock_instance = Mock()
            mock_service_class.return_value = mock_instance
            
            # Get service
            service1 = get_qa_service()
            
            # Reset service
            reset_service()
            
            # Get service again
            service2 = get_qa_service()
            
            # Should be different instances
            assert service1 is not service2
            # Service class should be called twice
            assert mock_service_class.call_count == 2
