"""
Pytest configuration and fixtures for DocQA tests
"""

import os
import pytest
from unittest.mock import Mock, patch
from pathlib import Path
import tempfile

# Set test environment variables
os.environ["OPENAI_API_KEY"] = "test-key-sk-1234567890"
os.environ["DATABASE_URL"] = "postgresql://test:test@localhost:5432/test_docqa"
os.environ["LOG_LEVEL"] = "DEBUG"


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing"""
    with patch('openai.OpenAI') as mock_client:
        # Mock embedding response
        mock_embedding_response = Mock()
        mock_embedding_response.data = [Mock(embedding=[0.1] * 1536)]
        mock_embedding_response.usage = Mock(total_tokens=100)
        
        # Mock chat response
        mock_chat_response = Mock()
        mock_chat_response.choices = [Mock(message=Mock(content="Test answer"))]
        mock_chat_response.usage = Mock(
            prompt_tokens=50,
            completion_tokens=25,
            total_tokens=75
        )
        
        # Configure mock client
        mock_instance = Mock()
        mock_instance.embeddings.create.return_value = mock_embedding_response
        mock_instance.chat.completions.create.return_value = mock_chat_response
        mock_client.return_value = mock_instance
        
        yield mock_instance


@pytest.fixture
def mock_database():
    """Mock database connection for testing"""
    with patch('psycopg.connect') as mock_connect:
        mock_conn = Mock()
        mock_cursor = Mock()
        
        # Configure cursor mock
        mock_cursor.fetchone.return_value = {'id': 'test-id', 'name': 'Test'}
        mock_cursor.fetchall.return_value = [
            {'id': 'test-id-1', 'name': 'Test 1'},
            {'id': 'test-id-2', 'name': 'Test 2'}
        ]
        mock_cursor.rowcount = 1
        
        # Configure connection mock
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_conn.cursor.return_value.__exit__.return_value = None
        mock_connect.return_value.__enter__.return_value = mock_conn
        mock_connect.return_value.__exit__.return_value = None
        
        yield mock_conn


@pytest.fixture
def sample_pdf_content():
    """Sample PDF content for testing"""
    return "This is a test PDF document with some sample content for testing purposes."


@pytest.fixture
def sample_image_data():
    """Sample image data for testing"""
    # Create a simple 10x10 RGB image
    import numpy as np
    return np.random.randint(0, 255, (10, 10, 3), dtype=np.uint8)


@pytest.fixture
def sample_document_chunks():
    """Sample document chunks for testing"""
    from docqa.types import DocumentChunk
    
    return [
        DocumentChunk(
            id="chunk-1",
            text="This is the first chunk of content.",
            embedding=[0.1] * 1536,
            metadata={'chunk_index': 0, 'document_id': 'test-doc'}
        ),
        DocumentChunk(
            id="chunk-2", 
            text="This is the second chunk of content.",
            embedding=[0.2] * 1536,
            metadata={'chunk_index': 1, 'document_id': 'test-doc'}
        )
    ]


@pytest.fixture
def sample_search_results():
    """Sample search results for testing"""
    from docqa.types import SearchResult
    
    return [
        SearchResult(
            chunk_id="chunk-1",
            text="Franchisor information about Test Franchise",
            similarity_score=0.95,
            metadata={
                'name': 'Test Franchise',
                'region': 'Melbourne',
                'brochure_url': 'https://example.com/brochure.pdf'
            },
            table_name='franchisors'
        ),
        SearchResult(
            chunk_id="chunk-2",
            text="Document content about franchise opportunities",
            similarity_score=0.85,
            metadata={
                'document_name': 'franchise_guide.pdf',
                'file_type': '.pdf'
            },
            table_name='documents'
        )
    ]


@pytest.fixture(autouse=True)
def reset_config():
    """Reset configuration before each test"""
    # Reset any global state
    from docqa import serve
    serve.reset_service()
    
    yield
    
    # Cleanup after test
    serve.reset_service()


@pytest.fixture
def mock_file_handlers():
    """Mock file handlers for testing"""
    with patch('docqa.file_handlers.PDFHandler') as mock_pdf, \
         patch('docqa.file_handlers.DocxHandler') as mock_docx, \
         patch('docqa.file_handlers.ImageHandler') as mock_image:
        
        # Configure mock handlers
        for handler in [mock_pdf, mock_docx, mock_image]:
            handler_instance = Mock()
            handler_instance.can_handle.return_value = True
            handler_instance.process_file.return_value = Mock(
                success=True,
                text_content="Sample extracted content",
                images_extracted=[],
                tables_extracted=[],
                error_message=None
            )
            handler.return_value = handler_instance
        
        yield {
            'pdf': mock_pdf,
            'docx': mock_docx, 
            'image': mock_image
        }


# Test data constants
TEST_QUESTION = "What franchise opportunities are available in Melbourne?"
TEST_DOCUMENT_URL = "https://example.com/test-document.pdf"
TEST_S3_URL = "s3://test-bucket/test-document.pdf"
