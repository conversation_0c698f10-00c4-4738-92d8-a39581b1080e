"""
Comprehensive Test Suite for Parallel Document Processing
Tests Redis caching, Celery tasks, and integration scenarios
"""

import pytest
import uuid
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from ..redis_store import RedisStore, JobStatus
from ..tasks import ingest_document_parallel, batch_ingest_documents
from ..central_api import ask_question
from ..parallel_ingest import ParallelDocumentProcessor
from ..monitoring.status_tracker import StatusTracker
from ..vector_store.enhanced_bulk_operations import EnhancedBulkVectorOperations


class TestRedisStore:
    """Test Redis caching and status tracking"""
    
    @pytest.fixture
    async def redis_store(self):
        """Create Redis store for testing"""
        store = RedisStore("redis://localhost:6379/1")  # Use test database
        await store.connect()
        yield store
        await store.disconnect()
    
    @pytest.mark.asyncio
    async def test_document_deduplication(self, redis_store):
        """Test document deduplication logic"""
        url = "https://example.com/test.pdf"
        document_hash = "test_hash_123"
        
        # Initially not processed
        assert not await redis_store.is_document_processed(url)
        
        # Mark as processed
        await redis_store.mark_document_processed(url, document_hash)
        
        # Should now be processed
        assert await redis_store.is_document_processed(url)
        
        # Get document info
        doc_info = await redis_store.get_document_info(url)
        assert doc_info["status"] == "processed"
        assert doc_info["document_hash"] == document_hash
    
    @pytest.mark.asyncio
    async def test_answer_caching(self, redis_store):
        """Test answer caching functionality"""
        question = "What is the franchise fee?"
        doc_id = "test_doc_123"
        answer = {
            "answer": "The franchise fee is $50,000",
            "confidence": 0.95,
            "sources": ["document1.pdf"]
        }
        
        # Initially no cached answer
        cached = await redis_store.get_cached_answer(question, doc_id)
        assert cached is None
        
        # Cache the answer
        await redis_store.cache_answer(question, doc_id, answer)
        
        # Should now be cached
        cached = await redis_store.get_cached_answer(question, doc_id)
        assert cached is not None
        assert cached["answer"]["answer"] == answer["answer"]
    
    @pytest.mark.asyncio
    async def test_job_status_tracking(self, redis_store):
        """Test job status tracking"""
        url = "https://example.com/test.pdf"
        job_id = str(uuid.uuid4())
        
        # Set initial status
        await redis_store.set_job_status(
            url=url,
            status=JobStatus.PROCESSING,
            job_id=job_id,
            metadata={"started_at": datetime.utcnow().isoformat()}
        )
        
        # Get status
        status = await redis_store.get_job_status(url)
        assert status["status"] == JobStatus.PROCESSING.value
        assert status["job_id"] == job_id
        
        # Update to completed
        await redis_store.set_job_status(
            url=url,
            status=JobStatus.COMPLETED,
            job_id=job_id,
            metadata={"completed_at": datetime.utcnow().isoformat()}
        )
        
        # Verify update
        status = await redis_store.get_job_status(url)
        assert status["status"] == JobStatus.COMPLETED.value
    
    @pytest.mark.asyncio
    async def test_job_progress_tracking(self, redis_store):
        """Test job progress tracking"""
        job_id = str(uuid.uuid4())
        
        # Set initial progress
        await redis_store.set_job_progress(
            job_id=job_id,
            progress=0,
            message="Starting processing..."
        )
        
        # Get progress
        progress = await redis_store.get_job_progress(job_id)
        assert progress["progress"] == 0
        assert progress["message"] == "Starting processing..."
        
        # Update progress
        await redis_store.set_job_progress(
            job_id=job_id,
            progress=50,
            message="Halfway complete..."
        )
        
        # Verify update
        progress = await redis_store.get_job_progress(job_id)
        assert progress["progress"] == 50
        assert progress["message"] == "Halfway complete..."


class TestParallelProcessing:
    """Test parallel document processing"""
    
    @pytest.fixture
    def parallel_processor(self):
        """Create parallel processor for testing"""
        return ParallelDocumentProcessor(max_workers=2)
    
    @pytest.mark.asyncio
    async def test_parallel_content_extraction(self, parallel_processor):
        """Test parallel content extraction"""
        with patch.object(parallel_processor, '_extract_content_sync') as mock_extract:
            mock_extract.return_value = {
                "success": True,
                "content": "Test document content",
                "metadata": {"pages": 5}
            }
            
            with patch.object(parallel_processor, '_extract_charts_sync') as mock_charts:
                mock_charts.return_value = {
                    "success": True,
                    "charts": [{"description": "Sales chart"}]
                }
                
                result = await parallel_processor._parallel_content_extraction(
                    source="test.pdf",
                    extract_charts=True,
                    translate=False,
                    processing_options={}
                )
                
                assert result["success"]
                assert "Test document content" in result["content"]
                assert "Charts and Diagrams" in result["content"]
                assert result["metadata"]["charts_count"] == 1
    
    @pytest.mark.asyncio
    async def test_parallel_chunk_processing(self, parallel_processor):
        """Test parallel chunk processing"""
        content = "This is a test document with multiple sentences. " * 100
        metadata = {"source": "test.pdf"}
        
        with patch.object(parallel_processor, '_generate_embeddings_batch_sync') as mock_embed:
            mock_embed.return_value = {
                "success": True,
                "chunks": [Mock(content="chunk1"), Mock(content="chunk2")],
                "tokens": 200
            }
            
            result = await parallel_processor._parallel_chunk_processing(
                content=content,
                metadata=metadata,
                processing_options={"embedding_batch_size": 5}
            )
            
            assert result["success"]
            assert len(result["chunks"]) == 2
            assert result["total_tokens"] == 200


class TestCeleryTasks:
    """Test Celery task processing"""
    
    @pytest.mark.asyncio
    async def test_ingest_document_parallel_task(self):
        """Test parallel document ingestion task"""
        with patch('docqa.tasks._process_document_async') as mock_process:
            mock_process.return_value = {
                "success": True,
                "message": "Document processed successfully",
                "source_url": "test.pdf",
                "document_hash": "hash123",
                "chunks_created": 10,
                "processing_time": 30.5,
                "cached": False
            }
            
            # Create mock task
            task = Mock()
            task.request.id = str(uuid.uuid4())
            
            result = ingest_document_parallel(
                task,
                source_url="test.pdf",
                force_table="documents",
                translate=True,
                extract_charts=True
            )
            
            assert result["success"]
            assert result["chunks_created"] == 10
            assert not result["cached"]
    
    @pytest.mark.asyncio
    async def test_batch_ingest_documents_task(self):
        """Test batch document ingestion"""
        document_urls = [
            "https://example.com/doc1.pdf",
            "https://example.com/doc2.pdf",
            "https://example.com/doc3.pdf"
        ]
        
        with patch('docqa.tasks._process_batch_async') as mock_batch:
            mock_batch.return_value = {
                "success": True,
                "message": "Batch processing completed",
                "total_documents": 3,
                "completed": 3,
                "failed": 0,
                "success_rate": 100.0,
                "results": [
                    {"url": url, "success": True, "result": {"chunks_created": 5}}
                    for url in document_urls
                ]
            }
            
            task = Mock()
            task.request.id = str(uuid.uuid4())
            
            result = batch_ingest_documents(
                task,
                document_urls=document_urls,
                batch_options={"max_concurrent": 2}
            )
            
            assert result["success"]
            assert result["total_documents"] == 3
            assert result["success_rate"] == 100.0


class TestCentralAPI:
    """Test central ask_question API"""
    
    @pytest.mark.asyncio
    async def test_ask_question_with_cache_hit(self):
        """Test ask_question with cached answer"""
        request = {
            "question": "What is the franchise fee?",
            "document_id": "test_doc_123"
        }
        
        with patch('docqa.central_api.DocumentQAService') as mock_service:
            mock_instance = AsyncMock()
            mock_service.return_value = mock_instance
            
            mock_instance.ask_question.return_value = {
                "success": True,
                "answer": "The franchise fee is $50,000",
                "cached": True,
                "cache_key": "document:test_doc_123"
            }
            
            result = await ask_question(request)
            
            assert result["success"]
            assert result["cached"]
            assert "franchise fee" in result["answer"]
    
    @pytest.mark.asyncio
    async def test_ask_question_with_document_processing(self):
        """Test ask_question triggering document processing"""
        request = {
            "question": "What are the requirements?",
            "source_url": "https://example.com/new_doc.pdf"
        }
        
        with patch('docqa.central_api.DocumentQAService') as mock_service:
            mock_instance = AsyncMock()
            mock_service.return_value = mock_instance
            
            mock_instance.ask_question.return_value = {
                "success": True,
                "answer": "Document is being processed. Please check back shortly.",
                "processing_status": "processing",
                "job_id": str(uuid.uuid4()),
                "cached": False
            }
            
            result = await ask_question(request)
            
            assert result["success"]
            assert not result["cached"]
            assert "being processed" in result["answer"]


class TestStatusTracker:
    """Test status tracking and monitoring"""
    
    @pytest.fixture
    async def status_tracker(self):
        """Create status tracker for testing"""
        tracker = StatusTracker()
        await tracker.initialize()
        yield tracker
        await tracker.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_job_tracking_lifecycle(self, status_tracker):
        """Test complete job tracking lifecycle"""
        job_id = str(uuid.uuid4())
        url = "https://example.com/test.pdf"
        
        # Start tracking
        await status_tracker.track_job_start(
            job_id=job_id,
            url=url,
            metadata={"priority": 5}
        )
        
        # Check initial status
        status = await status_tracker.get_job_status(job_id)
        assert status["job_id"] == job_id
        assert status["status"] == JobStatus.PROCESSING.value
        
        # Update progress
        await status_tracker.track_job_progress(
            job_id=job_id,
            progress=50,
            message="Halfway complete"
        )
        
        # Complete job
        await status_tracker.track_job_completion(
            job_id=job_id,
            success=True,
            chunks_created=15,
            tokens_processed=1000
        )
        
        # Check final status
        final_status = await status_tracker.get_job_status(job_id)
        assert final_status["status"] == JobStatus.COMPLETED.value
        assert final_status["chunks_created"] == 15
        assert final_status["tokens_processed"] == 1000
    
    @pytest.mark.asyncio
    async def test_system_metrics_collection(self, status_tracker):
        """Test system metrics collection"""
        # Add some mock job metrics
        job_id = str(uuid.uuid4())
        status_tracker.job_metrics[job_id] = Mock(
            status=JobStatus.COMPLETED.value,
            processing_time=30.5,
            chunks_created=10,
            tokens_processed=500,
            cache_hit=False
        )
        
        system_status = await status_tracker.get_system_status()
        
        assert "system_metrics" in system_status
        assert "cache_stats" in system_status
        assert "database_stats" in system_status
        assert system_status["active_jobs"] >= 0


class TestBulkOperations:
    """Test bulk vector operations"""
    
    @pytest.fixture
    async def bulk_ops(self):
        """Create bulk operations for testing"""
        ops = EnhancedBulkVectorOperations(max_connections=2, batch_size=5)
        await ops.initialize("postgresql://test:test@localhost/test_db")
        yield ops
        await ops.close()
    
    @pytest.mark.asyncio
    async def test_batch_insert_chunks(self, bulk_ops):
        """Test batch chunk insertion"""
        chunks = [
            Mock(
                content=f"Test chunk {i}",
                embedding=[0.1] * 1536,
                metadata={"chunk_index": i}
            )
            for i in range(10)
        ]
        
        with patch.object(bulk_ops, '_insert_batch') as mock_insert:
            mock_insert.return_value = {
                "success": True,
                "inserted_count": 5
            }
            
            result = await bulk_ops.batch_insert_chunks(
                chunks=chunks,
                table_name="documents",
                document_hash="test_hash",
                source_url="test.pdf"
            )
            
            assert result["success"]
            assert result["chunks_stored"] == 10  # 2 batches of 5
            assert result["batches_processed"] == 2


@pytest.mark.integration
class TestIntegration:
    """Integration tests for the complete system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_document_processing(self):
        """Test complete document processing flow"""
        # This would test the entire pipeline from document upload
        # through processing, caching, and querying
        pass
    
    @pytest.mark.asyncio
    async def test_concurrent_processing_performance(self):
        """Test system performance under concurrent load"""
        # This would test multiple documents being processed simultaneously
        pass
    
    @pytest.mark.asyncio
    async def test_redis_failover_handling(self):
        """Test system behavior when Redis is unavailable"""
        # This would test graceful degradation when Redis fails
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
