"""
Central Document Processing Method with All Optimizations

This module provides the unified central method for document processing that integrates:
- Background processing with immediate response
- Parallel document processing with PyMuPDF, OCR, and chart detection
- Smart chunking with token awareness
- Section-aware context tagging
- Bulk vector operations
- Caching and duplicate detection
- Progress tracking and monitoring
"""

import time
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Callable
import structlog

from .config import get_config
from .types import IngestionResult, DocumentMetadata
from .processors.background_processor import BackgroundProcessor, TaskStatus
from .processors.enhanced_processor import EnhancedDocumentProcessor
from .processors.smart_chunker import SmartChunker, ChunkingConfig
from .processors.section_tagger import SectionTagger
from .vector_store.bulk_operations import BulkVectorOperations
from .services.enhanced_openai_service import EnhancedOpenAIService

from app.core.logging import logger


class CentralDocumentProcessor:
    """
    Unified central processor with all optimizations integrated
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize the central processor with all components
        
        Args:
            max_workers: Maximum number of worker threads
        """
        config = get_config()
        self.max_workers = max_workers or min(16, (config.max_workers or 8))
        
        # Initialize all processing components
        self.background_processor = BackgroundProcessor(max_workers=self.max_workers)
        self.enhanced_processor = EnhancedDocumentProcessor(max_workers=self.max_workers)
        self.smart_chunker = SmartChunker()
        self.section_tagger = SectionTagger()
        self.bulk_operations = BulkVectorOperations()
        self.openai_service = EnhancedOpenAIService()
        
        logger.info("Central document processor initialized", 
                   max_workers=self.max_workers)
    
    def process_document(
        self,
        source: str,
        target_table: str = "documents",
        document_id: Optional[str] = None,
        background: bool = True,
        force_processing: bool = False,
        extract_charts: bool = True,
        extract_tables: bool = True,
        use_ocr: bool = True,
        enhance_ocr: bool = False,
        chunk_size: int = 400,
        chunk_overlap: int = 50,
        callback: Optional[Callable] = None
    ) -> Union[str, IngestionResult]:
        """
        Central method for document processing with all optimizations
        
        Args:
            source: Document source (URL, S3 path, or local file)
            target_table: Target table (documents or franchisors)
            document_id: Optional document ID (generates new if None)
            background: Whether to process in background (immediate response)
            force_processing: Force reprocessing even if already exists
            extract_charts: Whether to extract and analyze charts
            extract_tables: Whether to extract tables
            use_ocr: Whether to use OCR for images
            enhance_ocr: Whether to enhance OCR with GPT-4 Vision
            chunk_size: Target tokens per chunk
            chunk_overlap: Overlap tokens between chunks
            callback: Optional callback function for completion
            
        Returns:
            Task ID (if background=True) or IngestionResult (if background=False)
        """
        logger.info("Starting central document processing", 
                   source=source,
                   target_table=target_table,
                   background=background)
        
        if background:
            # Background processing with immediate response
            return self._process_background(
                source=source,
                target_table=target_table,
                document_id=document_id,
                force_processing=force_processing,
                extract_charts=extract_charts,
                extract_tables=extract_tables,
                use_ocr=use_ocr,
                enhance_ocr=enhance_ocr,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                callback=callback
            )
        else:
            # Synchronous processing
            return self._process_synchronous(
                source=source,
                target_table=target_table,
                document_id=document_id,
                force_processing=force_processing,
                extract_charts=extract_charts,
                extract_tables=extract_tables,
                use_ocr=use_ocr,
                enhance_ocr=enhance_ocr,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
    
    def _process_background(
        self,
        source: str,
        target_table: str,
        document_id: Optional[str],
        force_processing: bool,
        extract_charts: bool,
        extract_tables: bool,
        use_ocr: bool,
        enhance_ocr: bool,
        chunk_size: int,
        chunk_overlap: int,
        callback: Optional[Callable]
    ) -> str:
        """Process document in background with immediate response"""
        
        # Create enhanced callback that includes additional processing
        def enhanced_callback(result: IngestionResult):
            try:
                if callback:
                    callback(result)
            except Exception as e:
                logger.warning("User callback failed", error=str(e))
        
        # Submit to background processor with enhanced options
        task_id = self.background_processor.submit_document_processing(
            source=source,
            target_table=target_table,
            document_id=document_id,
            force_processing=force_processing,
            extract_charts=extract_charts,
            extract_tables=extract_tables,
            use_ocr=use_ocr,
            callback=enhanced_callback
        )
        
        logger.info("Background processing task submitted", 
                   task_id=task_id, source=source)
        
        return task_id
    
    def _process_synchronous(
        self,
        source: str,
        target_table: str,
        document_id: Optional[str],
        force_processing: bool,
        extract_charts: bool,
        extract_tables: bool,
        use_ocr: bool,
        enhance_ocr: bool,
        chunk_size: int,
        chunk_overlap: int
    ) -> IngestionResult:
        """Process document synchronously with all optimizations"""
        start_time = time.time()
        
        try:
            # Step 1: Prepare file
            file_path = self._prepare_file(source)
            if not file_path:
                return IngestionResult(
                    success=False,
                    document_id=document_id or "",
                    chunks_created=0,
                    table_name=target_table,
                    error_message="Failed to prepare file"
                )
            
            # Step 2: Check for existing document (with caching)
            if not force_processing:
                existing_doc = self.enhanced_processor.is_document_processed(file_path)
                if existing_doc:
                    return IngestionResult(
                        success=True,
                        document_id=existing_doc,
                        chunks_created=0,
                        table_name=target_table,
                        processing_time=time.time() - start_time,
                        message="Document already processed (cached)"
                    )
            
            # Step 3: Enhanced parallel document processing
            processing_result = self.enhanced_processor.process_document_parallel(
                file_path=file_path,
                extract_charts=extract_charts,
                extract_tables=extract_tables,
                use_ocr=use_ocr
            )
            
            if not processing_result.success:
                return IngestionResult(
                    success=False,
                    document_id=document_id or "",
                    chunks_created=0,
                    table_name=target_table,
                    error_message=processing_result.error_message
                )
            
            # Step 4: Enhanced OCR if requested
            if enhance_ocr and processing_result.images_extracted:
                processing_result = self._enhance_ocr_results(processing_result, file_path)
            
            # Step 5: Section-aware context tagging
            sections = self.section_tagger.detect_sections(processing_result.text_content)
            context_tags = self.section_tagger.create_context_tags(sections)
            
            # Step 6: Smart chunking with token awareness
            document_id = document_id or str(uuid.uuid4())
            metadata = DocumentMetadata(
                document_id=document_id,
                filename=file_path.name,
                file_type=file_path.suffix.lower(),
                file_size=file_path.stat().st_size,
                source_url=source if source.startswith(('http', 's3://')) else None
            )
            
            # Configure chunking
            chunking_config = ChunkingConfig(
                chunk_size=chunk_size,
                overlap_size=chunk_overlap,
                section_aware=True
            )
            self.smart_chunker.config = chunking_config
            
            chunks = self.smart_chunker.chunk_text(
                text=processing_result.text_content,
                metadata=metadata,
                sections=sections
            )
            
            if not chunks:
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=0,
                    table_name=target_table,
                    error_message="No content chunks created"
                )
            
            # Step 7: Add context tags to chunks
            for chunk in chunks:
                if chunk.metadata is None:
                    chunk.metadata = {}
                chunk.metadata.update({
                    'context_tags': context_tags,
                    'processing_method': 'central_processor',
                    'charts_detected': len(processing_result.charts_detected),
                    'tables_extracted': len(processing_result.tables_extracted),
                    'sections_detected': len(sections)
                })
            
            # Step 8: Bulk vector operations for performance
            ingestion_result = self.bulk_operations.bulk_ingest_chunks(
                chunks=chunks,
                document_id=document_id,
                table_name=target_table,
                metadata=metadata,
                source_url=source
            )
            
            if not ingestion_result.success:
                return ingestion_result
            
            # Step 9: Update cache
            doc_hash = self.enhanced_processor.get_document_hash(file_path)
            if doc_hash:
                self.enhanced_processor.document_cache[doc_hash] = document_id
            
            # Step 10: Enhanced result with additional metadata
            ingestion_result.processing_time = time.time() - start_time
            ingestion_result.metadata = {
                'sections_detected': len(sections),
                'charts_detected': len(processing_result.charts_detected),
                'tables_extracted': len(processing_result.tables_extracted),
                'images_processed': len(processing_result.images_extracted),
                'context_tags': context_tags,
                'processing_method': 'central_processor_sync'
            }
            
            logger.info("Synchronous processing completed", 
                       document_id=document_id,
                       chunks_created=ingestion_result.chunks_created,
                       processing_time=ingestion_result.processing_time)
            
            return ingestion_result
            
        except Exception as e:
            logger.error("Synchronous processing failed", 
                        source=source, error=str(e))
            return IngestionResult(
                success=False,
                document_id=document_id or "",
                chunks_created=0,
                table_name=target_table,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def _enhance_ocr_results(self, processing_result, file_path: Path):
        """Enhance OCR results using GPT-4 Vision"""
        try:
            
            enhanced_images = []
            
            for img_info in processing_result.images_extracted:
                if 'ocr_text' in img_info:
                    # Load image and enhance OCR
                    # This would require storing image data or re-extracting
                    # For now, we'll skip this enhancement
                    enhanced_images.append(img_info)
            
            processing_result.images_extracted = enhanced_images
            return processing_result
            
        except Exception as e:
            logger.debug("OCR enhancement failed", error=str(e))
            return processing_result
    
    def _prepare_file(self, source: str) -> Optional[Path]:
        """Prepare file for processing (reuse background processor logic)"""
        return self.background_processor._prepare_file(source)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get background task status"""
        return self.background_processor.get_task_status(task_id)
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """Get status of all background tasks"""
        return self.background_processor.get_all_tasks()
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a background task"""
        return self.background_processor.cancel_task(task_id)
    
    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Optional[IngestionResult]:
        """Wait for background task completion"""
        return self.background_processor.wait_for_task(task_id, timeout)
    
    def ask_question(
        self,
        question: str,
        top_k: int = 6,
        similarity_threshold: float = 0.7,
        include_context: bool = True
    ) -> Dict[str, Any]:
        """
        Ask question with enhanced context from processed documents
        
        Args:
            question: Question to ask
            top_k: Number of top results to retrieve
            similarity_threshold: Minimum similarity threshold
            include_context: Whether to include context tags in response
            
        Returns:
            Enhanced answer with context information
        """
        try:
            # Use existing ask service but with enhanced context
            from .ask import QuestionAnsweringService
            
            qa_service = QuestionAnsweringService()
            response = qa_service.ask_question(
                question=question,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            # Enhance response with context tags if available
            if include_context and hasattr(response, 'sources'):
                enhanced_sources = []
                for source in response.sources:
                    if hasattr(source, 'metadata') and source.metadata:
                        context_tags = source.metadata.get('context_tags', {})
                        if context_tags:
                            source.context_info = {
                                'section_types': context_tags.get('section_types', []),
                                'business_context': context_tags.get('business_context', {}),
                                'hierarchical_structure': context_tags.get('hierarchical_structure', {})
                            }
                    enhanced_sources.append(source)
                
                response.sources = enhanced_sources
            
            return response
            
        except Exception as e:
            logger.error("Question answering failed", error=str(e))
            return {
                'answer': f"Error processing question: {str(e)}",
                'sources': [],
                'error': str(e)
            }
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        background_tasks = self.get_all_tasks()
        
        stats = {
            'total_tasks': len(background_tasks),
            'completed_tasks': len([t for t in background_tasks if t['status'] == TaskStatus.COMPLETED.value]),
            'failed_tasks': len([t for t in background_tasks if t['status'] == TaskStatus.FAILED.value]),
            'processing_tasks': len([t for t in background_tasks if t['status'] == TaskStatus.PROCESSING.value]),
            'pending_tasks': len([t for t in background_tasks if t['status'] == TaskStatus.PENDING.value]),
            'average_processing_time': 0.0,
            'total_chunks_created': 0,
            'cache_hits': len(self.enhanced_processor.document_cache)
        }
        
        # Calculate averages
        completed_tasks = [t for t in background_tasks if t['status'] == TaskStatus.COMPLETED.value and t.get('processing_time')]
        if completed_tasks:
            stats['average_processing_time'] = sum(t['processing_time'] for t in completed_tasks) / len(completed_tasks)
            stats['total_chunks_created'] = sum(t.get('result', {}).get('chunks_created', 0) for t in completed_tasks)
        
        return stats
    
    def shutdown(self):
        """Shutdown the central processor and all components"""
        logger.info("Shutting down central document processor")
        
        self.background_processor.shutdown()
        self.bulk_operations.close()
        
        logger.info("Central document processor shutdown complete")
