"""
Intelligent caching system with memory and disk persistence
"""

import os
import json
import pickle
import hashlib
import time
from typing import Any, Dict, Optional, Union
import structlog

from app.core.logging import logger

# Global cache instance
_cache_instance = None


class IntelligentCache:
    """
    Intelligent caching system with memory and disk persistence
    """
    
    def __init__(self, 
                 max_memory_mb: int = 512, 
                 default_ttl: int = 3600,
                 persistence: bool = True,
                 cache_dir: str = None):
        """
        Initialize cache
        
        Args:
            max_memory_mb: Maximum memory usage in MB
            default_ttl: Default time-to-live in seconds
            persistence: Whether to persist cache to disk
            cache_dir: Directory for cache persistence
        """
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.persistence = persistence
        
        # Memory cache
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.current_memory_usage = 0
        
        # Disk cache
        self.cache_dir = cache_dir or os.path.join(os.path.dirname(__file__), "../../cache")
        if self.persistence:
            os.makedirs(self.cache_dir, exist_ok=True)
        
        logger.info("Intelligent cache initialized",
                   default_ttl=default_ttl,
                   max_memory_mb=max_memory_mb,
                   persistence=persistence)
    
    def get(self, key: str) -> Any:
        """Get item from cache"""
        # Check memory cache first
        if key in self.memory_cache:
            item = self.memory_cache[key]
            if self._is_item_valid(item):
                logger.debug("Cache hit (memory)", key=key)
                return item["value"]
            else:
                # Expired, remove from memory
                self._remove_from_memory(key)
        
        # Check disk cache if persistence enabled
        if self.persistence:
            disk_item = self._get_from_disk(key)
            if disk_item is not None:
                if self._is_item_valid(disk_item):
                    logger.debug("Cache hit (disk)", key=key)
                    # Add back to memory cache
                    self._add_to_memory(key, disk_item["value"], 
                                      disk_item["ttl"], 
                                      disk_item["created_at"])
                    return disk_item["value"]
                else:
                    # Expired, remove from disk
                    self._remove_from_disk(key)
        
        logger.debug("Cache miss", key=key)
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set item in cache"""
        ttl = ttl or self.default_ttl
        created_at = time.time()
        
        # Add to memory cache
        self._add_to_memory(key, value, ttl, created_at)
        
        # Add to disk cache if persistence enabled
        if self.persistence:
            self._add_to_disk(key, value, ttl, created_at)
    
    def delete(self, key: str) -> None:
        """Delete item from cache"""
        # Remove from memory
        if key in self.memory_cache:
            self._remove_from_memory(key)
        
        # Remove from disk
        if self.persistence:
            self._remove_from_disk(key)
    
    def clear(self) -> None:
        """Clear entire cache"""
        # Clear memory cache
        self.memory_cache = {}
        self.current_memory_usage = 0
        
        # Clear disk cache
        if self.persistence:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith(".cache"):
                    os.remove(os.path.join(self.cache_dir, filename))
    
    def _add_to_memory(self, key: str, value: Any, ttl: int, created_at: float) -> None:
        """Add item to memory cache"""
        # Calculate item size
        item_size = self._estimate_size(value)
        
        # Check if we need to make room
        if key not in self.memory_cache and self.current_memory_usage + item_size > self.max_memory_bytes:
            self._evict_items(item_size)
        
        # Store in memory
        self.memory_cache[key] = {
            "value": value,
            "ttl": ttl,
            "created_at": created_at,
            "size": item_size
        }
        
        # Update memory usage
        if key not in self.memory_cache:
            self.current_memory_usage += item_size
        
        logger.debug("Item cached to memory", key=key, size_bytes=item_size)
    
    def _remove_from_memory(self, key: str) -> None:
        """Remove item from memory cache"""
        if key in self.memory_cache:
            item_size = self.memory_cache[key]["size"]
            self.current_memory_usage -= item_size
            del self.memory_cache[key]
    
    def _add_to_disk(self, key: str, value: Any, ttl: int, created_at: float) -> None:
        """Add item to disk cache"""
        if not self.persistence:
            return
        
        cache_file = self._get_cache_file_path(key)
        
        try:
            # Create cache item
            cache_item = {
                "value": value,
                "ttl": ttl,
                "created_at": created_at
            }
            
            # Save to disk
            with open(cache_file, "wb") as f:
                pickle.dump(cache_item, f)
            
            logger.debug("Item cached to disk", key=key)
            
        except Exception as e:
            logger.warning("Failed to cache item to disk", 
                         key=key, 
                         error=str(e))
    
    def _get_from_disk(self, key: str) -> Optional[Dict[str, Any]]:
        """Get item from disk cache"""
        if not self.persistence:
            return None
        
        cache_file = self._get_cache_file_path(key)
        
        if not os.path.exists(cache_file):
            return None
        
        try:
            with open(cache_file, "rb") as f:
                cache_item = pickle.load(f)
            
            return cache_item
            
        except Exception as e:
            logger.warning("Failed to read cache item from disk", 
                         key=key, 
                         error=str(e))
            return None
    
    def _remove_from_disk(self, key: str) -> None:
        """Remove item from disk cache"""
        if not self.persistence:
            return
        
        cache_file = self._get_cache_file_path(key)
        
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
            except Exception as e:
                logger.warning("Failed to remove cache item from disk", 
                             key=key, 
                             error=str(e))
    
    def _get_cache_file_path(self, key: str) -> str:
        """Get cache file path for key"""
        # Create a safe filename from the key
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{safe_key}.cache")
    
    def _is_item_valid(self, item: Dict[str, Any]) -> bool:
        """Check if cache item is still valid"""
        if "ttl" not in item or "created_at" not in item:
            return False
        
        # Check if item has expired
        current_time = time.time()
        return current_time - item["created_at"] < item["ttl"]
    
    def _estimate_size(self, value: Any) -> int:
        """Estimate memory size of an object"""
        try:
            # Use pickle to estimate size
            return len(pickle.dumps(value))
        except:
            # Fallback to a rough estimate
            return 1024  # 1KB default
    
    def _evict_items(self, required_space: int) -> None:
        """Evict items from memory cache to make room"""
        # Sort items by age (oldest first)
        items = sorted(
            [(k, v["created_at"]) for k, v in self.memory_cache.items()],
            key=lambda x: x[1]
        )
        
        space_freed = 0
        for key, _ in items:
            if key in self.memory_cache:
                item_size = self.memory_cache[key]["size"]
                self._remove_from_memory(key)
                space_freed += item_size
                
                if space_freed >= required_space:
                    break


def get_cache() -> IntelligentCache:
    """Get global cache instance"""
    global _cache_instance
    
    if _cache_instance is None:
        _cache_instance = IntelligentCache()
    
    return _cache_instance
