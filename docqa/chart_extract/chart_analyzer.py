"""
Chart analysis using OpenAI GPT-4 Vision
"""

import base64
from typing import List, Dict, Any
import openai
import structlog
from io import BytesIO
from PIL import Image

from ..config import get_config

from app.core.logging import logger


class ChartAnalyzer:
    """Analyze charts and visual content using OpenAI GPT-4 Vision"""
    
    def __init__(self):
        config = get_config()
        self.client = openai.OpenAI(api_key=config.openai_api_key)
        self.vision_model = "gpt-4o"  # Updated to current vision model

        logger.info("Chart analyzer initialized")
    
    def analyze_chart(self, image_data: bytes, context: str = "") -> Dict[str, Any]:
        """
        Analyze a chart or visual content using GPT-4 Vision
        
        Args:
            image_data: Raw image data
            context: Additional context about the image
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Convert image to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            # Prepare the prompt
            prompt = self._create_analysis_prompt(context)
            
            # Call GPT-4 Vision
            response = self.client.chat.completions.create(
                model=self.vision_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            analysis_text = response.choices[0].message.content
            
            # Parse the analysis into structured data
            analysis = self._parse_analysis(analysis_text)
            analysis['raw_analysis'] = analysis_text
            analysis['model_used'] = self.vision_model
            
            logger.info("Chart analysis completed", 
                       has_data=bool(analysis.get('data_points')),
                       chart_type=analysis.get('chart_type'))
            
            return analysis
            
        except Exception as e:
            logger.error("Chart analysis failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'description': 'Chart analysis failed'
            }
    
    def analyze_multiple_charts(self, images_data: List[bytes], context: str = "") -> List[Dict[str, Any]]:
        """Analyze multiple charts"""
        results = []
        
        for i, image_data in enumerate(images_data):
            try:
                chart_context = f"{context} (Chart {i+1} of {len(images_data)})"
                analysis = self.analyze_chart(image_data, chart_context)
                analysis['chart_index'] = i
                results.append(analysis)
                
            except Exception as e:
                logger.error("Failed to analyze chart", 
                           chart_index=i,
                           error=str(e))
                results.append({
                    'chart_index': i,
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    def _create_analysis_prompt(self, context: str = "") -> str:
        """Create analysis prompt for GPT-4 Vision"""
        base_prompt = """
        Analyze this chart or visual content and provide a detailed description. Focus on:

        1. Chart type (bar chart, line chart, pie chart, table, diagram, etc.)
        2. Main data points and values (if visible)
        3. Axes labels and units (if applicable)
        4. Title and key insights
        5. Trends or patterns shown
        6. Any text content visible in the image

        Please provide a comprehensive description that would help someone understand the content without seeing the image.
        """
        
        if context:
            base_prompt += f"\n\nAdditional context: {context}"
        
        base_prompt += "\n\nFormat your response as a clear, detailed description."
        
        return base_prompt
    
    def _parse_analysis(self, analysis_text: str) -> Dict[str, Any]:
        """Parse GPT-4 Vision analysis into structured data"""
        analysis = {
            'success': True,
            'description': analysis_text,
            'chart_type': 'unknown',
            'data_points': [],
            'insights': [],
            'text_content': ''
        }
        
        try:
            # Extract chart type
            text_lower = analysis_text.lower()
            chart_types = [
                'bar chart', 'line chart', 'pie chart', 'scatter plot',
                'histogram', 'table', 'diagram', 'flowchart', 'graph'
            ]
            
            for chart_type in chart_types:
                if chart_type in text_lower:
                    analysis['chart_type'] = chart_type
                    break
            
            # Extract numerical data (simple regex approach)
            import re
            numbers = re.findall(r'\b\d+(?:\.\d+)?(?:%|\$|€|£)?\b', analysis_text)
            analysis['data_points'] = numbers[:10]  # Limit to first 10 numbers
            
            # Extract insights (sentences with key words)
            insight_keywords = ['trend', 'increase', 'decrease', 'shows', 'indicates', 'highest', 'lowest']
            sentences = analysis_text.split('.')
            
            for sentence in sentences:
                sentence = sentence.strip()
                if any(keyword in sentence.lower() for keyword in insight_keywords):
                    analysis['insights'].append(sentence)
            
            # Extract any quoted text or titles
            quoted_text = re.findall(r'"([^"]*)"', analysis_text)
            analysis['text_content'] = ' '.join(quoted_text)
            
        except Exception as e:
            logger.warning("Failed to parse analysis", error=str(e))
        
        return analysis
    
    def extract_text_from_image(self, image_data: bytes) -> str:
        """Extract text content from image using GPT-4 Vision"""
        try:
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            response = self.client.chat.completions.create(
                model=self.vision_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Extract all visible text from this image. Provide only the text content, maintaining the original structure and formatting as much as possible."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.0
            )
            
            extracted_text = response.choices[0].message.content
            
            logger.info("Text extraction completed", 
                       text_length=len(extracted_text))
            
            return extracted_text
            
        except Exception as e:
            logger.error("Text extraction failed", error=str(e))
            return ""
    
    def resize_image_for_analysis(self, image_data: bytes, max_size: int = 1024) -> bytes:
        """Resize image to reduce API costs while maintaining quality"""
        try:
            image = Image.open(BytesIO(image_data))
            
            # Calculate new size maintaining aspect ratio
            width, height = image.size
            if max(width, height) > max_size:
                if width > height:
                    new_width = max_size
                    new_height = int(height * max_size / width)
                else:
                    new_height = max_size
                    new_width = int(width * max_size / height)
                
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to JPEG to reduce size
            output = BytesIO()
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            image.save(output, format='JPEG', quality=85, optimize=True)
            return output.getvalue()
            
        except Exception as e:
            logger.warning("Image resize failed, using original", error=str(e))
            return image_data
