"""
Performance monitoring and metrics for DocQA system
"""

import time
import psutil
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import structlog

from app.core.logging import logger


@dataclass
class ProcessingMetrics:
    """Metrics for document processing"""
    document_id: str
    file_size_mb: float
    processing_time_seconds: float
    chunk_count: int
    embedding_time_seconds: float
    chart_analysis_time_seconds: float
    cache_hit_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float
    throughput_mb_per_second: float
    timestamp: datetime


@dataclass
class SystemMetrics:
    """System-wide performance metrics"""
    total_documents_processed: int
    avg_processing_time: float
    avg_throughput: float
    cache_hit_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float
    error_rate: float
    uptime_hours: float


class PerformanceMonitor:
    """Monitor and track DocQA system performance"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.processing_history: List[ProcessingMetrics] = []
        self.start_time = time.time()
        self.total_documents = 0
        self.total_errors = 0
        
        logger.info("Performance monitor initialized", max_history=max_history)
    
    def start_processing(self, document_id: str, file_size_mb: float) -> Dict[str, Any]:
        """Start monitoring a document processing session"""
        return {
            'document_id': document_id,
            'file_size_mb': file_size_mb,
            'start_time': time.time(),
            'start_memory': self._get_memory_usage(),
            'start_cpu': self._get_cpu_usage()
        }
    
    def end_processing(self, session: Dict[str, Any], 
                      chunk_count: int,
                      embedding_time: float,
                      chart_analysis_time: float,
                      cache_hit_rate: float,
                      success: bool = True) -> ProcessingMetrics:
        """End monitoring and record metrics"""
        end_time = time.time()
        processing_time = end_time - session['start_time']
        
        # Calculate throughput
        throughput = session['file_size_mb'] / processing_time if processing_time > 0 else 0
        
        # Create metrics
        metrics = ProcessingMetrics(
            document_id=session['document_id'],
            file_size_mb=session['file_size_mb'],
            processing_time_seconds=processing_time,
            chunk_count=chunk_count,
            embedding_time_seconds=embedding_time,
            chart_analysis_time_seconds=chart_analysis_time,
            cache_hit_rate=cache_hit_rate,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            throughput_mb_per_second=throughput,
            timestamp=datetime.now()
        )
        
        # Update counters
        self.total_documents += 1
        if not success:
            self.total_errors += 1
        
        # Store metrics
        self._store_metrics(metrics)
        
        # Log performance summary
        logger.info("Document processing completed",
                   document_id=session['document_id'],
                   processing_time=f"{processing_time:.2f}s",
                   throughput=f"{throughput:.2f} MB/s",
                   chunk_count=chunk_count,
                   cache_hit_rate=f"{cache_hit_rate:.1%}",
                   memory_mb=f"{metrics.memory_usage_mb:.1f}")
        
        return metrics
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system-wide metrics"""
        if not self.processing_history:
            return SystemMetrics(
                total_documents_processed=0,
                avg_processing_time=0,
                avg_throughput=0,
                cache_hit_rate=0,
                memory_usage_mb=self._get_memory_usage(),
                cpu_usage_percent=self._get_cpu_usage(),
                error_rate=0,
                uptime_hours=0
            )
        
        # Calculate averages from recent history
        recent_metrics = self.processing_history[-100:]  # Last 100 documents
        
        avg_processing_time = sum(m.processing_time_seconds for m in recent_metrics) / len(recent_metrics)
        avg_throughput = sum(m.throughput_mb_per_second for m in recent_metrics) / len(recent_metrics)
        avg_cache_hit_rate = sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics)
        
        error_rate = self.total_errors / self.total_documents if self.total_documents > 0 else 0
        uptime_hours = (time.time() - self.start_time) / 3600
        
        return SystemMetrics(
            total_documents_processed=self.total_documents,
            avg_processing_time=avg_processing_time,
            avg_throughput=avg_throughput,
            cache_hit_rate=avg_cache_hit_rate,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            error_rate=error_rate,
            uptime_hours=uptime_hours
        )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        system_metrics = self.get_system_metrics()
        
        # Performance trends (last hour)
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent_metrics = [
            m for m in self.processing_history 
            if m.timestamp >= one_hour_ago
        ]
        
        # Performance analysis
        performance_analysis = self._analyze_performance(recent_metrics)
        
        return {
            'system_metrics': asdict(system_metrics),
            'recent_performance': {
                'documents_last_hour': len(recent_metrics),
                'avg_processing_time_last_hour': sum(m.processing_time_seconds for m in recent_metrics) / len(recent_metrics) if recent_metrics else 0,
                'peak_throughput': max((m.throughput_mb_per_second for m in recent_metrics), default=0),
                'min_processing_time': min((m.processing_time_seconds for m in recent_metrics), default=0),
                'max_processing_time': max((m.processing_time_seconds for m in recent_metrics), default=0)
            },
            'performance_analysis': performance_analysis,
            'recommendations': self._get_recommendations(system_metrics, recent_metrics)
        }
    
    def _store_metrics(self, metrics: ProcessingMetrics) -> None:
        """Store metrics in history"""
        self.processing_history.append(metrics)
        
        # Maintain max history size
        if len(self.processing_history) > self.max_history:
            self.processing_history = self.processing_history[-self.max_history:]
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """Get current CPU usage percentage"""
        try:
            return psutil.cpu_percent(interval=0.1)
        except Exception:
            return 0.0
    
    def _analyze_performance(self, metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze performance patterns"""
        if not metrics:
            return {}
        
        # File size vs processing time correlation
        file_sizes = [m.file_size_mb for m in metrics]
        processing_times = [m.processing_time_seconds for m in metrics]
        
        # Simple correlation calculation
        if len(file_sizes) > 1:
            correlation = self._calculate_correlation(file_sizes, processing_times)
        else:
            correlation = 0
        
        # Performance categories
        fast_processing = [m for m in metrics if m.processing_time_seconds < 10]
        slow_processing = [m for m in metrics if m.processing_time_seconds > 60]
        
        return {
            'size_time_correlation': correlation,
            'fast_processing_rate': len(fast_processing) / len(metrics) if metrics else 0,
            'slow_processing_rate': len(slow_processing) / len(metrics) if metrics else 0,
            'avg_cache_hit_rate': sum(m.cache_hit_rate for m in metrics) / len(metrics),
            'memory_trend': 'stable',  # Could be enhanced with trend analysis
            'cpu_trend': 'stable'
        }
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """Calculate simple correlation coefficient"""
        if len(x) != len(y) or len(x) < 2:
            return 0
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        sum_y2 = sum(y[i] ** 2 for i in range(n))
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = ((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2)) ** 0.5
        
        return numerator / denominator if denominator != 0 else 0
    
    def _get_recommendations(self, system_metrics: SystemMetrics, recent_metrics: List[ProcessingMetrics]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        # Memory recommendations
        if system_metrics.memory_usage_mb > 1000:
            recommendations.append("Consider increasing cache eviction frequency - high memory usage detected")
        
        # CPU recommendations
        if system_metrics.cpu_usage_percent > 80:
            recommendations.append("Consider reducing parallel processing threads - high CPU usage detected")
        
        # Cache recommendations
        if system_metrics.cache_hit_rate < 0.3:
            recommendations.append("Consider increasing cache TTL or size - low cache hit rate detected")
        
        # Processing time recommendations
        if system_metrics.avg_processing_time > 30:
            recommendations.append("Consider optimizing chunk size or enabling more parallel processing")
        
        # Throughput recommendations
        if system_metrics.avg_throughput < 1.0:
            recommendations.append("Consider optimizing embedding batch size or file processing pipeline")
        
        return recommendations


# Global performance monitor instance
_global_monitor = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor
