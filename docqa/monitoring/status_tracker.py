"""
Comprehensive Status Tracking and Monitoring System
Real-time job status tracking with Redis pub/sub and performance monitoring
"""

import asyncio
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from dataclasses import dataclass, asdict
import structlog

from ..redis_store import get_redis_store, JobStatus
from ..vector_store import PgVectorStore

from app.core.logging import logger


@dataclass
class JobMetrics:
    """Job performance metrics"""
    job_id: str
    url: str
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None
    chunks_created: int = 0
    tokens_processed: int = 0
    cache_hit: bool = False
    error_message: Optional[str] = None
    retry_count: int = 0


@dataclass
class SystemMetrics:
    """System-wide performance metrics"""
    total_jobs: int = 0
    completed_jobs: int = 0
    failed_jobs: int = 0
    pending_jobs: int = 0
    processing_jobs: int = 0
    average_processing_time: float = 0.0
    cache_hit_rate: float = 0.0
    total_chunks_created: int = 0
    total_tokens_processed: int = 0
    uptime: float = 0.0


class StatusTracker:
    """Comprehensive status tracking and monitoring system"""
    
    def __init__(self):
        self.redis_store = None
        self.vector_store = None
        self.job_metrics: Dict[str, JobMetrics] = {}
        self.system_metrics = SystemMetrics()
        self.subscribers: List[Callable] = []
        self.monitoring_active = False
        self.start_time = datetime.utcnow()
    
    async def initialize(self):
        """Initialize monitoring system"""
        if not self.redis_store:
            self.redis_store = await get_redis_store()
        
        if not self.vector_store:
            self.vector_store = PgVectorStore()
            await self.vector_store.connect()
        
        logger.info("Status tracker initialized")
    
    async def start_monitoring(self):
        """Start real-time monitoring with pub/sub"""
        await self.initialize()
        
        if self.monitoring_active:
            logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        
        # Start pub/sub listener
        asyncio.create_task(self._listen_to_updates())
        
        # Start periodic metrics collection
        asyncio.create_task(self._collect_system_metrics())
        
        logger.info("Status monitoring started")
    
    async def stop_monitoring(self):
        """Stop monitoring system"""
        self.monitoring_active = False
        logger.info("Status monitoring stopped")
    
    async def track_job_start(
        self, 
        job_id: str, 
        url: str, 
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Track job start"""
        metrics = JobMetrics(
            job_id=job_id,
            url=url,
            status=JobStatus.PROCESSING.value,
            started_at=datetime.utcnow()
        )
        
        self.job_metrics[job_id] = metrics
        
        # Update Redis
        await self.redis_store.set_job_status(
            url=url,
            status=JobStatus.PROCESSING,
            job_id=job_id,
            metadata=metadata or {}
        )
        
        # Notify subscribers
        await self._notify_subscribers("job_started", {
            "job_id": job_id,
            "url": url,
            "started_at": metrics.started_at.isoformat()
        })
        
        logger.info(f"Job tracking started: {job_id}")
    
    async def track_job_progress(
        self, 
        job_id: str, 
        progress: int, 
        message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Track job progress"""
        if job_id in self.job_metrics:
            # Update Redis progress
            await self.redis_store.set_job_progress(
                job_id=job_id,
                progress=progress,
                message=message,
                metadata=metadata
            )
            
            # Notify subscribers
            await self._notify_subscribers("job_progress", {
                "job_id": job_id,
                "progress": progress,
                "message": message,
                "timestamp": datetime.utcnow().isoformat()
            })
    
    async def track_job_completion(
        self, 
        job_id: str, 
        success: bool,
        chunks_created: int = 0,
        tokens_processed: int = 0,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Track job completion"""
        if job_id not in self.job_metrics:
            logger.warning(f"Job metrics not found for completion: {job_id}")
            return
        
        metrics = self.job_metrics[job_id]
        metrics.completed_at = datetime.utcnow()
        metrics.chunks_created = chunks_created
        metrics.tokens_processed = tokens_processed
        metrics.error_message = error_message
        
        if metrics.started_at:
            metrics.processing_time = (
                metrics.completed_at - metrics.started_at
            ).total_seconds()
        
        if success:
            metrics.status = JobStatus.COMPLETED.value
            status = JobStatus.COMPLETED
        else:
            metrics.status = JobStatus.FAILED.value
            status = JobStatus.FAILED
        
        # Update Redis
        await self.redis_store.set_job_status(
            url=metrics.url,
            status=status,
            job_id=job_id,
            metadata={
                **(metadata or {}),
                "completed_at": metrics.completed_at.isoformat(),
                "processing_time": metrics.processing_time,
                "chunks_created": chunks_created,
                "tokens_processed": tokens_processed,
                "error_message": error_message
            }
        )
        
        # Update system metrics
        await self._update_system_metrics(metrics)
        
        # Notify subscribers
        await self._notify_subscribers("job_completed", {
            "job_id": job_id,
            "success": success,
            "processing_time": metrics.processing_time,
            "chunks_created": chunks_created,
            "tokens_processed": tokens_processed,
            "error_message": error_message,
            "completed_at": metrics.completed_at.isoformat()
        })
        
        logger.info(f"Job tracking completed: {job_id}, success: {success}")
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive job status"""
        # Check local metrics first
        if job_id in self.job_metrics:
            metrics = self.job_metrics[job_id]
            
            # Get latest progress from Redis
            progress_info = await self.redis_store.get_job_progress(job_id)
            
            return {
                "job_id": job_id,
                "url": metrics.url,
                "status": metrics.status,
                "started_at": metrics.started_at.isoformat() if metrics.started_at else None,
                "completed_at": metrics.completed_at.isoformat() if metrics.completed_at else None,
                "processing_time": metrics.processing_time,
                "chunks_created": metrics.chunks_created,
                "tokens_processed": metrics.tokens_processed,
                "cache_hit": metrics.cache_hit,
                "error_message": metrics.error_message,
                "retry_count": metrics.retry_count,
                "progress": progress_info.get("progress", 0) if progress_info else 0,
                "progress_message": progress_info.get("message") if progress_info else None
            }
        
        # Fallback to Redis
        redis_status = await self.redis_store.get_job_status(job_id)
        if redis_status:
            progress_info = await self.redis_store.get_job_progress(job_id)
            
            return {
                **redis_status,
                "progress": progress_info.get("progress", 0) if progress_info else 0,
                "progress_message": progress_info.get("message") if progress_info else None
            }
        
        return None
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        # Update system metrics
        await self._collect_system_metrics()
        
        # Get Redis cache stats
        cache_stats = await self.redis_store.get_cache_stats()
        
        # Get database stats
        db_stats = await self._get_database_stats()
        
        return {
            "system_metrics": asdict(self.system_metrics),
            "cache_stats": cache_stats,
            "database_stats": db_stats,
            "monitoring_active": self.monitoring_active,
            "uptime": (datetime.utcnow() - self.start_time).total_seconds(),
            "active_jobs": len([
                m for m in self.job_metrics.values() 
                if m.status == JobStatus.PROCESSING.value
            ])
        }
    
    async def get_job_history(
        self, 
        limit: int = 100,
        status_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get job history with optional filtering"""
        jobs = []
        
        for job_id, metrics in self.job_metrics.items():
            if status_filter and metrics.status != status_filter:
                continue
            
            job_data = {
                "job_id": job_id,
                "url": metrics.url,
                "status": metrics.status,
                "started_at": metrics.started_at.isoformat() if metrics.started_at else None,
                "completed_at": metrics.completed_at.isoformat() if metrics.completed_at else None,
                "processing_time": metrics.processing_time,
                "chunks_created": metrics.chunks_created,
                "tokens_processed": metrics.tokens_processed,
                "cache_hit": metrics.cache_hit,
                "error_message": metrics.error_message,
                "retry_count": metrics.retry_count
            }
            
            jobs.append(job_data)
        
        # Sort by start time (most recent first)
        jobs.sort(
            key=lambda x: x["started_at"] or "1970-01-01T00:00:00",
            reverse=True
        )
        
        return jobs[:limit]
    
    def subscribe_to_updates(self, callback: Callable):
        """Subscribe to status updates"""
        self.subscribers.append(callback)
        logger.info("New subscriber added to status updates")
    
    def unsubscribe_from_updates(self, callback: Callable):
        """Unsubscribe from status updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
            logger.info("Subscriber removed from status updates")
    
    async def _listen_to_updates(self):
        """Listen to Redis pub/sub updates"""
        try:
            await self.redis_store.subscribe_to_updates(self._handle_redis_update)
        except Exception as e:
            logger.error(f"Error in pub/sub listener: {e}")
            if self.monitoring_active:
                # Restart listener after delay
                await asyncio.sleep(5)
                asyncio.create_task(self._listen_to_updates())
    
    async def _handle_redis_update(self, channel: str, data: Dict[str, Any]):
        """Handle Redis pub/sub updates"""
        try:
            update_type = data.get("type")
            
            if update_type == "status_update":
                job_id = data.get("job_id")
                if job_id and job_id in self.job_metrics:
                    self.job_metrics[job_id].status = data.get("status")
            
            elif update_type == "progress_update":
                # Progress updates are handled separately
                pass
            
            # Notify local subscribers
            await self._notify_subscribers(f"redis_{update_type}", data)
            
        except Exception as e:
            logger.error(f"Error handling Redis update: {e}")
    
    async def _collect_system_metrics(self):
        """Collect system-wide metrics periodically"""
        while self.monitoring_active:
            try:
                # Calculate metrics from job history
                total_jobs = len(self.job_metrics)
                completed_jobs = len([
                    m for m in self.job_metrics.values() 
                    if m.status == JobStatus.COMPLETED.value
                ])
                failed_jobs = len([
                    m for m in self.job_metrics.values() 
                    if m.status == JobStatus.FAILED.value
                ])
                processing_jobs = len([
                    m for m in self.job_metrics.values() 
                    if m.status == JobStatus.PROCESSING.value
                ])
                
                # Calculate averages
                completed_metrics = [
                    m for m in self.job_metrics.values() 
                    if m.status == JobStatus.COMPLETED.value and m.processing_time
                ]
                
                avg_processing_time = 0.0
                if completed_metrics:
                    avg_processing_time = sum(
                        m.processing_time for m in completed_metrics
                    ) / len(completed_metrics)
                
                # Cache hit rate
                cache_hits = len([m for m in self.job_metrics.values() if m.cache_hit])
                cache_hit_rate = (cache_hits / total_jobs * 100) if total_jobs > 0 else 0.0
                
                # Update system metrics
                self.system_metrics = SystemMetrics(
                    total_jobs=total_jobs,
                    completed_jobs=completed_jobs,
                    failed_jobs=failed_jobs,
                    pending_jobs=total_jobs - completed_jobs - failed_jobs - processing_jobs,
                    processing_jobs=processing_jobs,
                    average_processing_time=avg_processing_time,
                    cache_hit_rate=cache_hit_rate,
                    total_chunks_created=sum(m.chunks_created for m in self.job_metrics.values()),
                    total_tokens_processed=sum(m.tokens_processed for m in self.job_metrics.values()),
                    uptime=(datetime.utcnow() - self.start_time).total_seconds()
                )
                
                # Sleep for 30 seconds before next collection
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(30)
    
    async def _update_system_metrics(self, job_metrics: JobMetrics):
        """Update system metrics when job completes"""
        # This is handled in _collect_system_metrics for consistency
        pass
    
    async def _get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            # Get table counts and sizes
            stats = await self.vector_store.get_database_stats()
            return stats
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {"error": str(e)}
    
    async def _notify_subscribers(self, event_type: str, data: Dict[str, Any]):
        """Notify all subscribers of updates"""
        if not self.subscribers:
            return
        
        notification = {
            "event_type": event_type,
            "timestamp": datetime.utcnow().isoformat(),
            "data": data
        }
        
        # Notify all subscribers
        for callback in self.subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(notification)
                else:
                    callback(notification)
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")


# Global status tracker instance
_status_tracker: Optional[StatusTracker] = None


async def get_status_tracker() -> StatusTracker:
    """Get or create status tracker instance"""
    global _status_tracker
    
    if _status_tracker is None:
        _status_tracker = StatusTracker()
        await _status_tracker.initialize()
    
    return _status_tracker
