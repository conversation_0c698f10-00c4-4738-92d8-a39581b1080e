# High-Performance Parallel Document Processing with RabbitMQ + Redis

## 🚀 Overview

This enhanced DocQA system provides high-performance, parallel document processing with intelligent caching and real-time status tracking. Built on top of the existing GrowthHive DocQA foundation, it adds:

- **RabbitMQ** for parallel task execution and job queues
- **Redis** for caching, deduplication, and status tracking  
- **Parallel Processing** with concurrent document ingestion
- **Smart Caching** to prevent duplicate processing and speed up queries
- **Real-time Monitoring** with job progress tracking and system metrics

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │───▶│  Central API    │───▶│  Redis Cache    │
│                 │    │ ask_question()  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   RabbitMQ      │───▶│ Celery Workers  │───▶│   pgvector      │
│   Task Queue    │    │ Parallel Proc.  │    │   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Key Components

### 1. Central API (`docqa/central_api.py`)
- **ask_question()** - Main entry point with intelligent routing
- **Redis Integration** - Automatic caching and deduplication
- **Background Processing** - Triggers document ingestion via RabbitMQ
- **Status Tracking** - Real-time job progress monitoring

### 2. Redis Store (`docqa/redis_store.py`)
- **Document Deduplication** - Prevents reprocessing of same documents
- **Answer Caching** - Caches Q&A responses with TTL
- **Job Status Tracking** - Tracks processing status and progress
- **Pub/Sub Notifications** - Real-time updates via Redis channels

### 3. Enhanced Celery Tasks (`docqa/tasks.py`)
- **Parallel Processing** - Multi-worker document ingestion
- **Batch Operations** - Process multiple documents concurrently
- **Priority Queues** - Route tasks based on priority levels
- **Error Handling** - Comprehensive retry and failure management

### 4. Parallel Ingestion (`docqa/parallel_ingest.py`)
- **Concurrent Operations** - Parallel content extraction and processing
- **Adaptive Chunking** - Intelligent text segmentation
- **Batch Embeddings** - Optimized OpenAI API usage
- **Performance Tracking** - Detailed processing metrics

### 5. Status Monitoring (`docqa/monitoring/status_tracker.py`)
- **Job Lifecycle Tracking** - From start to completion
- **System Metrics** - Performance and health monitoring
- **Real-time Updates** - Pub/sub notifications for status changes
- **Historical Data** - Job history and analytics

### 6. Bulk Vector Operations (`docqa/vector_store/enhanced_bulk_operations.py`)
- **Connection Pooling** - Optimized database connections
- **Batch Inserts** - High-performance vector storage
- **Transaction Support** - ACID compliance for data integrity
- **Performance Optimization** - Database tuning and monitoring

## 🚀 Quick Start

### 1. Start Background Services
```bash
# Start RabbitMQ, Redis, and Celery workers
./scripts/start_background_services.sh
```

### 2. Initialize the System
```python
from docqa.central_api import ask_question

# Ask a question with automatic document processing
response = await ask_question({
    "question": "What is the franchise fee?",
    "source_url": "s3://bucket/franchise-brochure.pdf",
    "top_k": 5,
    "similarity_threshold": 0.7
})

print(response['answer'])
```

### 3. Monitor System Status
```python
from docqa.monitoring.status_tracker import get_status_tracker

tracker = await get_status_tracker()
system_status = await tracker.get_system_status()
print(f"Active jobs: {system_status['active_jobs']}")
print(f"Cache hit rate: {system_status['system_metrics']['cache_hit_rate']}%")
```

## 📊 Performance Features

### Smart Caching Strategy
- **Document Deduplication**: Skip processing of already-processed documents
- **Answer Caching**: Cache Q&A responses with intelligent TTL
- **LRU Eviction**: Automatic cache management with memory limits
- **Cache Warming**: Pre-populate cache with common queries

### Parallel Processing Optimizations
- **Multi-Worker Processing**: Concurrent document ingestion
- **Batch Operations**: Process multiple documents simultaneously  
- **Connection Pooling**: Optimized database connections
- **Async Operations**: Non-blocking I/O for better throughput

### Real-time Monitoring
- **Job Progress Tracking**: 0-100% progress with detailed messages
- **System Metrics**: Performance, cache hit rates, processing times
- **Error Tracking**: Comprehensive error logging and alerting
- **Historical Analytics**: Job history and trend analysis

## 🔄 Usage Patterns

### 1. Single Document Processing
```python
response = await ask_question({
    "question": "What are the investment requirements?",
    "source_url": "https://example.com/franchise-info.pdf",
    "processing_options": {
        "extract_charts": True,
        "translate": True,
        "priority": 8  # High priority
    }
})
```

### 2. Batch Document Processing
```python
from docqa.tasks import batch_ingest_documents

result = batch_ingest_documents.delay(
    document_urls=[
        "s3://bucket/doc1.pdf",
        "s3://bucket/doc2.pdf", 
        "s3://bucket/doc3.pdf"
    ],
    batch_options={
        "max_concurrent": 3,
        "timeout_per_doc": 300
    }
)
```

### 3. Status Monitoring
```python
from docqa.monitoring.status_tracker import get_status_tracker

tracker = await get_status_tracker()

# Get job status
job_status = await tracker.get_job_status("job-id-123")
print(f"Progress: {job_status['progress']}%")

# Get system metrics
system_status = await tracker.get_system_status()
print(f"Total jobs processed: {system_status['system_metrics']['total_jobs']}")
```

## 🛠️ Configuration

### Environment Variables
```bash
# RabbitMQ Configuration
CELERY_BROKER_URL=amqp://growthhive:growthhive123@localhost:5672//
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Redis Configuration  
REDIS_URL=redis://localhost:6379/0

# Processing Configuration
DOCUMENT_PROCESSING_QUEUE=document_processing
DOCUMENT_PROCESSING_MAX_RETRIES=3
DOCUMENT_PROCESSING_RETRY_DELAY=60

# Performance Tuning
MAX_CONCURRENT_WORKERS=4
BATCH_SIZE=100
CONNECTION_POOL_SIZE=10
CACHE_TTL=3600
```

### Docker Compose Setup
The system includes an enhanced `docker-compose.rabbitmq.yml` with:
- RabbitMQ with management UI
- Redis with persistence
- Celery workers with auto-scaling
- Flower monitoring dashboard

## 📈 Monitoring & Metrics

### System Metrics
- **Processing Performance**: Average processing time, throughput
- **Cache Performance**: Hit rates, memory usage, eviction rates
- **Queue Health**: Queue lengths, worker utilization
- **Database Performance**: Connection pool usage, query performance

### Monitoring Endpoints
```python
# System status
GET /api/docqa/status

# Job status  
GET /api/docqa/jobs/{job_id}/status

# Performance metrics
GET /api/docqa/metrics

# Cache statistics
GET /api/docqa/cache/stats
```

### Alerting & Notifications
- **Job Failures**: Automatic retry with exponential backoff
- **System Health**: Monitor queue lengths and worker health
- **Performance Degradation**: Alert on slow processing times
- **Cache Issues**: Monitor cache hit rates and memory usage

## 🧪 Testing

### Run Test Suite
```bash
# Run all tests
pytest docqa/tests/test_parallel_processing.py -v

# Run specific test categories
pytest docqa/tests/test_parallel_processing.py::TestRedisStore -v
pytest docqa/tests/test_parallel_processing.py::TestParallelProcessing -v
pytest docqa/tests/test_parallel_processing.py::TestCeleryTasks -v

# Run integration tests
pytest docqa/tests/test_parallel_processing.py -m integration -v
```

### Performance Testing
```bash
# Load testing with multiple concurrent requests
python docqa/tests/performance_test.py --concurrent=10 --documents=100

# Memory usage testing
python docqa/tests/memory_test.py --duration=3600
```

## 🚀 Deployment

### Production Deployment
1. **Scale Workers**: Adjust Celery worker count based on load
2. **Redis Clustering**: Use Redis Cluster for high availability
3. **Database Optimization**: Tune PostgreSQL for vector operations
4. **Monitoring**: Set up Prometheus/Grafana for metrics
5. **Load Balancing**: Use multiple API instances behind load balancer

### Performance Tuning
- **Worker Concurrency**: Adjust based on CPU cores and memory
- **Batch Sizes**: Optimize for your document sizes and types
- **Cache TTL**: Balance between freshness and performance
- **Connection Pools**: Size based on concurrent load

## 🔍 Troubleshooting

### Common Issues
1. **High Memory Usage**: Adjust batch sizes and worker concurrency
2. **Slow Processing**: Check database connections and OpenAI API limits
3. **Cache Misses**: Verify Redis connectivity and TTL settings
4. **Queue Backlog**: Scale up Celery workers or optimize processing

### Debug Commands
```bash
# Check Redis connectivity
redis-cli ping

# Monitor RabbitMQ queues
rabbitmqctl list_queues

# Check Celery worker status
celery -A app.core.celery_app inspect active

# Monitor system resources
docker stats
```

## 📚 API Reference

### Central ask_question() Function
```python
async def ask_question(request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Args:
        request: {
            "question": str,                    # Required
            "source_url": Optional[str],        # Document to process
            "document_id": Optional[str],       # Specific document
            "franchisor_id": Optional[str],     # Specific franchisor
            "top_k": Optional[int] = 5,         # Results count
            "similarity_threshold": Optional[float] = 0.7,
            "force_refresh": Optional[bool] = False,
            "processing_options": Optional[Dict] = {}
        }
    
    Returns:
        {
            "success": bool,
            "answer": str,
            "cached": bool,
            "processing_status": Optional[str],
            "job_id": Optional[str],
            "context": Dict[str, Any],
            "timestamp": str
        }
    """
```

This enhanced system provides production-ready, high-performance document processing with intelligent caching, real-time monitoring, and comprehensive error handling. Perfect for scaling document Q&A operations in the GrowthHive platform!
