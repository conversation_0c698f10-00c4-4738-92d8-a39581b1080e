"""
Redis Store for DocQA System
Handles caching, deduplication, job status tracking, and pub/sub notifications
"""

import json
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
import redis.asyncio as redis
import structlog
from enum import Enum

from app.core.logging import logger


class JobStatus(str, Enum):
    """Job processing status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class RedisStore:
    """Redis-based caching and status tracking for DocQA system"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        """Initialize Redis connection"""
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub_client: Optional[redis.Redis] = None
        
        # Key patterns
        self.DOCUMENT_HASH_KEY = "document_hash:{url}"
        self.ANSWER_CACHE_KEY = "qa:{question_hash}:{doc_id}"
        self.JOB_STATUS_KEY = "ingest_status:{url}"
        self.JOB_PROGRESS_KEY = "ingest_progress:{job_id}"
        self.JOB_METADATA_KEY = "ingest_metadata:{job_id}"
        
        # Pub/Sub channels
        self.INGEST_CHANNEL = "ingest_notifications"
        self.STATUS_CHANNEL = "status_updates"
        
        # Default TTL values (in seconds)
        self.DEFAULT_CACHE_TTL = 3600  # 1 hour
        self.ANSWER_CACHE_TTL = 7200   # 2 hours
        self.STATUS_TTL = 86400        # 24 hours
        self.PROGRESS_TTL = 3600       # 1 hour
    
    async def connect(self):
        """Establish Redis connections"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Separate client for pub/sub
            self.pubsub_client = redis.from_url(
                self.redis_url,
                encoding="utf-8", 
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Close Redis connections"""
        if self.redis_client:
            await self.redis_client.close()
        if self.pubsub_client:
            await self.pubsub_client.close()
    
    def _generate_hash(self, content: str) -> str:
        """Generate SHA-256 hash for content"""
        return hashlib.sha256(content.encode()).hexdigest()
    
    def _generate_question_hash(self, question: str) -> str:
        """Generate hash for question (normalized)"""
        normalized = question.lower().strip()
        return self._generate_hash(normalized)
    
    # Document Deduplication Methods
    
    async def is_document_processed(self, url: str) -> bool:
        """Check if document has been processed"""
        key = self.DOCUMENT_HASH_KEY.format(url=self._generate_hash(url))
        status = await self.redis_client.get(key)
        return status == "processed"
    
    async def mark_document_processed(self, url: str, document_hash: str, ttl: Optional[int] = None):
        """Mark document as processed"""
        key = self.DOCUMENT_HASH_KEY.format(url=self._generate_hash(url))
        ttl = ttl or self.STATUS_TTL
        
        await self.redis_client.setex(
            key, 
            ttl,
            json.dumps({
                "status": "processed",
                "document_hash": document_hash,
                "processed_at": datetime.utcnow().isoformat(),
                "url": url
            })
        )
        
        logger.info(f"Marked document as processed: {url}")
    
    async def get_document_info(self, url: str) -> Optional[Dict[str, Any]]:
        """Get document processing information"""
        key = self.DOCUMENT_HASH_KEY.format(url=self._generate_hash(url))
        data = await self.redis_client.get(key)
        
        if data:
            return json.loads(data)
        return None
    
    # Answer Caching Methods
    
    async def get_cached_answer(self, question: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get cached answer for question"""
        question_hash = self._generate_question_hash(question)
        key = self.ANSWER_CACHE_KEY.format(question_hash=question_hash, doc_id=doc_id)
        
        cached_data = await self.redis_client.get(key)
        if cached_data:
            data = json.loads(cached_data)
            logger.info(f"Cache hit for question: {question[:50]}...")
            return data
        
        return None
    
    async def cache_answer(
        self, 
        question: str, 
        doc_id: str, 
        answer: Dict[str, Any], 
        ttl: Optional[int] = None
    ):
        """Cache answer for question"""
        question_hash = self._generate_question_hash(question)
        key = self.ANSWER_CACHE_KEY.format(question_hash=question_hash, doc_id=doc_id)
        ttl = ttl or self.ANSWER_CACHE_TTL
        
        cache_data = {
            "answer": answer,
            "question": question,
            "doc_id": doc_id,
            "cached_at": datetime.utcnow().isoformat(),
            "ttl": ttl
        }
        
        await self.redis_client.setex(key, ttl, json.dumps(cache_data))
        logger.info(f"Cached answer for question: {question[:50]}...")
    
    # Job Status Tracking Methods
    
    async def set_job_status(
        self, 
        url: str, 
        status: JobStatus, 
        job_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Set job processing status"""
        key = self.JOB_STATUS_KEY.format(url=self._generate_hash(url))
        
        status_data = {
            "status": status.value,
            "url": url,
            "job_id": job_id,
            "updated_at": datetime.utcnow().isoformat(),
            "metadata": metadata or {}
        }
        
        await self.redis_client.setex(key, self.STATUS_TTL, json.dumps(status_data))
        
        # Publish status update
        await self.publish_status_update(url, status, job_id, metadata)
        
        logger.info(f"Set job status: {status.value} for {url}")
    
    async def get_job_status(self, url: str) -> Optional[Dict[str, Any]]:
        """Get job processing status"""
        key = self.JOB_STATUS_KEY.format(url=self._generate_hash(url))
        data = await self.redis_client.get(key)
        
        if data:
            return json.loads(data)
        return None
    
    async def set_job_progress(
        self, 
        job_id: str, 
        progress: int, 
        message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Set job processing progress (0-100)"""
        key = self.JOB_PROGRESS_KEY.format(job_id=job_id)
        
        progress_data = {
            "progress": max(0, min(100, progress)),  # Clamp to 0-100
            "message": message,
            "job_id": job_id,
            "updated_at": datetime.utcnow().isoformat(),
            "metadata": metadata or {}
        }
        
        await self.redis_client.setex(key, self.PROGRESS_TTL, json.dumps(progress_data))
        
        # Publish progress update
        await self.publish_progress_update(job_id, progress, message, metadata)
    
    async def get_job_progress(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job processing progress"""
        key = self.JOB_PROGRESS_KEY.format(job_id=job_id)
        data = await self.redis_client.get(key)
        
        if data:
            return json.loads(data)
        return None
    
    # Pub/Sub Methods
    
    async def publish_status_update(
        self, 
        url: str, 
        status: JobStatus, 
        job_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Publish job status update"""
        message = {
            "type": "status_update",
            "url": url,
            "status": status.value,
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat(),
            "metadata": metadata or {}
        }
        
        await self.redis_client.publish(self.STATUS_CHANNEL, json.dumps(message))
    
    async def publish_progress_update(
        self, 
        job_id: str, 
        progress: int, 
        message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Publish job progress update"""
        update_message = {
            "type": "progress_update",
            "job_id": job_id,
            "progress": progress,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
            "metadata": metadata or {}
        }
        
        await self.redis_client.publish(self.INGEST_CHANNEL, json.dumps(update_message))
    
    async def subscribe_to_updates(self, callback_func):
        """Subscribe to status and progress updates"""
        pubsub = self.pubsub_client.pubsub()
        await pubsub.subscribe(self.STATUS_CHANNEL, self.INGEST_CHANNEL)
        
        try:
            async for message in pubsub.listen():
                if message["type"] == "message":
                    try:
                        data = json.loads(message["data"])
                        await callback_func(message["channel"], data)
                    except Exception as e:
                        logger.error(f"Error processing pub/sub message: {e}")
        finally:
            await pubsub.unsubscribe(self.STATUS_CHANNEL, self.INGEST_CHANNEL)
            await pubsub.close()
    
    # Cache Management Methods
    
    async def clear_cache_pattern(self, pattern: str) -> int:
        """Clear cache entries matching pattern"""
        keys = []
        async for key in self.redis_client.scan_iter(match=pattern):
            keys.append(key)
        
        if keys:
            deleted = await self.redis_client.delete(*keys)
            logger.info(f"Cleared {deleted} cache entries matching pattern: {pattern}")
            return deleted
        
        return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        info = await self.redis_client.info("memory")
        keyspace = await self.redis_client.info("keyspace")
        
        return {
            "memory_used": info.get("used_memory_human", "N/A"),
            "memory_peak": info.get("used_memory_peak_human", "N/A"),
            "total_keys": sum(db.get("keys", 0) for db in keyspace.values() if isinstance(db, dict)),
            "connected_clients": (await self.redis_client.info("clients")).get("connected_clients", 0),
            "uptime": (await self.redis_client.info("server")).get("uptime_in_seconds", 0)
        }


# Global Redis store instance
_redis_store: Optional[RedisStore] = None


async def get_redis_store() -> RedisStore:
    """Get or create Redis store instance"""
    global _redis_store
    
    if _redis_store is None:
        from app.core.config.settings import settings
        _redis_store = RedisStore(settings.CELERY_RESULT_BACKEND)
        await _redis_store.connect()
    
    return _redis_store


async def close_redis_store():
    """Close Redis store connection"""
    global _redis_store
    
    if _redis_store:
        await _redis_store.disconnect()
        _redis_store = None
