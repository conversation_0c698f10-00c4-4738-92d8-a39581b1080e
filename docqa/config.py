"""
Configuration management for DocQA system
"""

import os
from pathlib import Path
import structlog

from app.core.logging import logger


class Config:
    """Configuration class for DocQA system"""
    
    def __init__(self):
        # OpenAI Configuration
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.chat_model = os.getenv("CHAT_MODEL", "gpt-4-turbo")
        self.max_tokens = int(os.getenv("MAX_TOKENS", "1000"))
        self.temperature = float(os.getenv("TEMPERATURE", "0.1"))
        
        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        # Vector Search Configuration
        self.top_k = int(os.getenv("TOP_K", "6"))
        self.similarity_threshold = float(os.getenv("SIMILARITY_THRESHOLD", "0.7"))
        self.chunk_size = int(os.getenv("CHUNK_SIZE", "400"))
        self.chunk_overlap = int(os.getenv("CHUNK_OVERLAP", "50"))

        # Performance Optimization Configuration
        self.max_workers = int(os.getenv("MAX_WORKERS", "4"))  # Parallel processing threads
        self.batch_size = int(os.getenv("BATCH_SIZE", "10"))  # Embedding batch size
        self.enable_caching = os.getenv("ENABLE_CACHING", "false").lower() == "true"  # Disabled by default
        self.cache_ttl = int(os.getenv("CACHE_TTL", "3600"))  # Cache TTL in seconds
        self.enable_streaming = os.getenv("ENABLE_STREAMING", "true").lower() == "true"

        # File Processing Configuration
        self.max_file_size_mb = int(os.getenv("MAX_FILE_SIZE_MB", "500"))  # Increased limit
        self.supported_formats = [".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".txt", ".csv", ".xlsx"]
        self.enable_parallel_processing = os.getenv("ENABLE_PARALLEL_PROCESSING", "true").lower() == "true"
        
        # OCR Configuration
        self.tesseract_cmd = os.getenv("TESSERACT_CMD", "tesseract")
        self.ocr_languages = os.getenv("OCR_LANGUAGES", "eng")
        
        # Logging Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        
        # Temporary directory for file processing
        self.temp_dir = Path(os.getenv("TEMP_DIR", "/tmp/docqa"))
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("Configuration loaded", 
                   embedding_model=self.embedding_model,
                   chat_model=self.chat_model,
                   top_k=self.top_k,
                   chunk_size=self.chunk_size)
    
    def validate(self) -> bool:
        """Validate configuration settings"""
        try:
            # Check OpenAI API key format
            if not self.openai_api_key.startswith(('sk-', 'sk-proj-')):
                logger.warning("OpenAI API key format may be invalid")
            
            # Check database URL format
            if not self.database_url.startswith('postgresql'):
                logger.warning("Database URL should be PostgreSQL")
            
            # Check file size limits
            if self.max_file_size_mb > 500:
                logger.warning("Large file size limit may cause memory issues")
            
            # Check chunk size
            if self.chunk_size > 2000:
                logger.warning("Large chunk size may exceed token limits")
            
            return True
            
        except Exception as e:
            logger.error("Configuration validation failed", error=str(e))
            return False


# Global configuration instance - lazy initialization
_config = None

def get_config():
    """Get the global configuration instance with lazy initialization"""
    global _config
    if _config is None:
        _config = Config()
    return _config

# For backward compatibility - use lazy loading instead of immediate initialization
config = None

def get_config_safe():
    """Get config safely, returning None if not available"""
    try:
        return get_config()
    except ValueError:
        return None
