repos:
  # Ruff - Fast Python linter and formatter
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.6
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        types_or: [python, pyi]
      - id: ruff-format
        types_or: [python, pyi]

  # Black - Python code formatter
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11
        types_or: [python, pyi]

  # isort - Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
        types_or: [python, pyi]

  # MyPy - Static type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [
          pydantic>=2.5.0,
          sqlalchemy[asyncio]>=2.0.0,
          fastapi>=0.104.0,
          types-redis,
          types-requests,
          types-python-jose,
        ]
        args: [--strict, --ignore-missing-imports]
        exclude: ^(alembic/|tests/conftest\.py)

  # Bandit - Security linting
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-c, setup.cfg]
        exclude: ^tests/

  # GitLeaks - Secret detection
  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.18.0
    hooks:
      - id: gitleaks

  # Custom Semgrep rules for follow-up detection
  - repo: local
    hooks:
      - id: ban-followups
        name: Ban Follow-up Tokens
        entry: python scripts/ban_followups_check.py
        language: python
        files: \.py$
        exclude: ^(tests/|scripts/ban_followups_check\.py|docs/)
        additional_dependencies: []

  # Standard pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: \.md$
      - id: end-of-file-fixer
        exclude: \.md$
      - id: check-yaml
        args: [--unsafe]
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: name-tests-test
        args: [--pytest-test-first]
      - id: requirements-txt-fixer
      - id: fix-byte-order-marker
      - id: mixed-line-ending
        args: [--fix=lf]

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, markdown, json]
        exclude: ^(\.github/|alembic/)

  # SQL formatting (for migrations)
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.5
    hooks:
      - id: sqlfluff-lint
        files: \.sql$
        additional_dependencies: [sqlfluff-templater-jinja]
      - id: sqlfluff-fix
        files: \.sql$
        additional_dependencies: [sqlfluff-templater-jinja]

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck

  # Python security audit
  - repo: local
    hooks:
      - id: pip-audit
        name: pip-audit
        entry: pip-audit
        language: python
        additional_dependencies: [pip-audit]
        args: [--desc, --disable-pip]
        pass_filenames: false

# Global configuration
default_language_version:
  python: python3.11

# CI configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: [pip-audit, gitleaks]  # Skip expensive checks in CI
  submodules: false
