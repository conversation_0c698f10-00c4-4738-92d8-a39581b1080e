# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.cache

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv*/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.venv/
venv/
ENV/
env/
.venv*/
.venv_backup_python311/

# macOS system files
.DS_Store
.AppleDouble
.LSOverride

# Windows system files
Thumbs.db
ehthumbs.db
Desktop.ini

# Logs
logs/
*.log

# Environment variables
# .env
# .env.*

# Alembic
alembic/versions/__pycache__/
alembic/__pycache__/

# Docker
*.pid
docker-compose.override.yml

# VSCode
.vscode/

# PyCharm
.idea/

# Misc
*.swp
*.swo
*~

# Ignore compiled files in app/
app/__pycache__/
app/**/*.pyc
app/.DS_Store

# Ignore compiled files in submodules
**/__pycache__/
**/*.pyc

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/ 

misc/

.ruff_cache/