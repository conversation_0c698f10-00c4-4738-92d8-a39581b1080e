#!/usr/bin/env python3
"""
Simple Docker Setup for 1-Minute Follow-ups
Works with current Docker configuration
"""

import requests
import json
import time
from datetime import datetime
from uuid import uuid4

def test_webhook_with_followup():
    """Test webhook and follow-up scheduling"""
    print("🐳 Docker - Simple 1-Minute Follow-up Test")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    webhook_url = "http://localhost:8000/api/webhooks/kudosity"
    test_phone = f"61{uuid4().hex[:9]}"
    
    # Check webhook health first
    print("\n1️⃣ Checking Webhook Health")
    print("-" * 30)
    
    try:
        health_response = requests.get("http://localhost:8000/api/webhooks/health", timeout=10)
        if health_response.status_code == 200:
            print("✅ Webhook endpoint is healthy")
        else:
            print(f"❌ Webhook health check failed: {health_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot reach webhook endpoint: {str(e)}")
        return
    
    # Send test message
    print(f"\n2️⃣ Sending Test Message")
    print("-" * 30)
    
    payload = {
        "event_type": "SMS_INBOUND",
        "timestamp": datetime.now().isoformat() + "Z",
        "webhook_id": str(uuid4()),
        "webhook_name": "growthhive_webhook",
        "mo": {
            "type": "SMS",
            "id": str(uuid4()),
            "sender": test_phone,
            "recipient": "61430250079",
            "message": "Hi, I'm interested in franchise opportunities",
            "timestamp": datetime.now().isoformat() + "Z"
        }
    }
    
    try:
        print(f"📱 Sending message from: {test_phone}")
        response = requests.post(webhook_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            response_data = response.json()
            data = response_data.get("data", {})
            
            andy_response = data.get("andy_response", "")
            followup_info = data.get("followup_management", {})
            
            print(f"✅ Webhook processed successfully")
            print(f"📝 Andy response: {len(andy_response)} characters")
            if andy_response:
                print(f"   Preview: {andy_response[:100]}...")
            
            if followup_info:
                action = followup_info.get("action", "none")
                success = followup_info.get("success", False)
                message = followup_info.get("message", "")
                
                print(f"🔄 Follow-up info:")
                print(f"   Action: {action}")
                print(f"   Success: {'✅' if success else '❌'}")
                print(f"   Message: {message}")
                
                if action == "followup_scheduled" and success:
                    print(f"\n🎉 SUCCESS: Follow-up scheduled!")
                    print(f"📱 Test phone: {test_phone}")
                    
                    # Show what should happen next
                    print(f"\n⏰ Expected Timeline:")
                    current_time = datetime.now()
                    print(f"   Now: {current_time.strftime('%H:%M:%S')} - Test message sent")
                    
                    # Calculate expected follow-up times based on messaging rule
                    print(f"   Expected follow-up times depend on your messaging rule:")
                    print(f"   - If 1-minute rule: Follow-ups at {(current_time.replace(second=current_time.second+60)).strftime('%H:%M:%S')}, {(current_time.replace(second=current_time.second+120)).strftime('%H:%M:%S')}, {(current_time.replace(second=current_time.second+180)).strftime('%H:%M:%S')}")
                    print(f"   - If 1-hour rule: Follow-ups at {(current_time.replace(hour=current_time.hour+1)).strftime('%H:%M:%S')}, {(current_time.replace(hour=current_time.hour+2)).strftime('%H:%M:%S')}, etc.")
                    
                    print(f"\n👀 Monitor Celery logs:")
                    print(f"   docker logs -f growthhive-celery")
                    print(f"   Look for: 'Scheduled follow-up attempt #1'")
                    print(f"   Then: 'Follow-up message sent' (when executed)")
                    
                    return test_phone
                else:
                    print(f"❌ Follow-up not scheduled properly")
                    print(f"   This might mean:")
                    print(f"   - No messaging rule configured")
                    print(f"   - Lead not found/created")
                    print(f"   - Follow-up logic not triggered")
                    return None
            else:
                print(f"❌ No follow-up information in response")
                print(f"   This suggests follow-up logic didn't run")
                return None
        else:
            print(f"❌ Webhook failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return None
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return None

def monitor_celery_logs_simple(duration=60):
    """Simple Celery log monitoring"""
    print(f"\n3️⃣ Monitoring Celery Logs ({duration} seconds)")
    print("-" * 30)
    print("Looking for follow-up activity...")
    
    import subprocess
    
    try:
        # Get recent logs first
        result = subprocess.run([
            "docker", "logs", "--tail", "20", "growthhive-celery"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            recent_logs = result.stdout
            if any(keyword in recent_logs.lower() for keyword in ['follow-up', 'followup', 'scheduled']):
                print("✅ Recent follow-up activity found in logs!")
                print("Recent relevant log entries:")
                for line in recent_logs.split('\n'):
                    if any(keyword in line.lower() for keyword in ['follow-up', 'followup', 'scheduled']):
                        print(f"   {line}")
            else:
                print("ℹ️  No recent follow-up activity in logs")
        
        print(f"\n💡 To continue monitoring:")
        print(f"   docker logs -f growthhive-celery")
        print(f"   (Press Ctrl+C to stop)")
        
    except Exception as e:
        print(f"❌ Log monitoring failed: {str(e)}")

def main():
    # Run the test
    test_phone = test_webhook_with_followup()
    
    if test_phone:
        # Monitor logs briefly
        monitor_celery_logs_simple()
        
        print(f"\n🎉 Test Complete!")
        print("=" * 60)
        print(f"✅ Webhook processing: Working")
        print(f"✅ Andy AI responses: Working")
        print(f"✅ Follow-up scheduling: Working")
        print(f"📱 Test phone number: {test_phone}")
        
        print(f"\n🔍 Next Steps:")
        print(f"1. Monitor Celery logs for task execution:")
        print(f"   docker logs -f growthhive-celery")
        print(f"2. Look for follow-up messages being sent")
        print(f"3. Test cancellation by sending another message from {test_phone}")
        
        print(f"\n⚙️ If follow-ups aren't happening:")
        print(f"1. Check if messaging rule exists with short delays")
        print(f"2. Verify Celery worker is processing tasks")
        print(f"3. Check SMS configuration (KUDOSITY_SMS_ENABLED=true)")
        
    else:
        print(f"\n❌ Test Failed")
        print("=" * 60)
        print(f"Follow-up scheduling is not working properly.")
        print(f"Check the error messages above for specific issues.")

if __name__ == "__main__":
    main()
