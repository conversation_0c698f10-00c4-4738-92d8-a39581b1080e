# Enterprise RAG System Configuration

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Storage Configuration
STORAGE_PATH=./data/enterprise_rag
FAISS_INDEX_PATH=./data/faiss_index
CACHE_PATH=./data/cache

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379
REDIS_ENABLED=false

# Performance Configuration
MAX_WORKERS=6
CHUNK_SIZE=500
CHUNK_OVERLAP=50
TOP_K_RESULTS=5

# Distributed Configuration
NUM_SHARDS=4
ENABLE_DISTRIBUTED=false

# Monitoring Configuration
LOG_LEVEL=INFO
ENABLE_ANALYTICS=true
METRICS_ENABLED=false

# Security Configuration
ENABLE_AUTH=false
API_KEY_REQUIRED=false
