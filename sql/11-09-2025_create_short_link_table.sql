-- Create short_link table for branded URL shortening service (ghv.li)
-- Date: 2025-09-11
-- Description: Implements in-house short link service for GrowthHive

-- Create the short_link table
CREATE TABLE IF NOT EXISTS short_link (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    slug VARCHAR(16) UNIQUE NOT NULL,
    long_url TEXT NOT NULL,
    context_json JSONB DEFAULT '{}' NOT NULL,
    expires_at TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS ix_short_link_id ON short_link (id);
CREATE INDEX IF NOT EXISTS ix_short_link_slug ON short_link (slug);
CREATE INDEX IF NOT EXISTS ix_short_link_expires_at ON short_link (expires_at);

-- <PERSON>reate function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_short_link_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_update_short_link_updated_at
    BEFORE UPDATE ON short_link
    FOR EACH ROW
    EXECUTE FUNCTION update_short_link_updated_at();

-- Add comments for documentation
COMMENT ON TABLE short_link IS 'Stores short links for branded URL shortening service (ghv.li)';
COMMENT ON COLUMN short_link.id IS 'Primary key UUID';
COMMENT ON COLUMN short_link.slug IS 'Short slug (8-12 characters, base62 encoded)';
COMMENT ON COLUMN short_link.long_url IS 'Original URL to redirect to';
COMMENT ON COLUMN short_link.context_json IS 'Context metadata (JSONB for flexible storage)';
COMMENT ON COLUMN short_link.expires_at IS 'Optional expiration date';
COMMENT ON COLUMN short_link.created_at IS 'Creation timestamp';
COMMENT ON COLUMN short_link.updated_at IS 'Last update timestamp';

-- Insert sample data for testing (optional)
-- INSERT INTO short_link (slug, long_url, context_json, expires_at) VALUES
-- ('test123', 'https://example.com', '{"type": "test", "user_id": "123"}', NOW() + INTERVAL '7 days'),
-- ('meeting1', 'https://meet.zoho.com/join/abc123', '{"type": "meeting_join", "phone_number": "+1234567890"}', NOW() + INTERVAL '7 days');

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON short_link TO your_app_user;
-- GRANT USAGE ON SEQUENCE short_link_id_seq TO your_app_user;
