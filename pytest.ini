[tool:pytest]
# Pytest configuration for GrowthHive test suite
minversion = 6.0
addopts =
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:reports/coverage
    --cov-report=xml:reports/coverage.xml
    --junit-xml=reports/junit.xml
    --tb=short

testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    api: API endpoint tests
    sms: SMS functionality tests
    followup: Follow-up system tests
    booking: Meeting booking tests
    agent: Agent system tests
    database: Database tests
    slow: Slow running tests
    external: Tests that require external services
    cleanup: Tests that require data cleanup

# Test discovery
collect_ignore =
    setup.py
    migrations/
    alembic/
    docker/
    docs/
    scripts/
    misc/
    docqa/tests/

# Async test configuration
asyncio_mode = auto

# Logging configuration for tests
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test timeout
timeout = 300

# Parallel execution (included in main addopts above)

# Environment variables for testing
env =
    ENVIRONMENT=test
    DATABASE_URL=postgresql+asyncpg://postgres:root@localhost:5432/growthhive_test
    REDIS_URL=redis://localhost:6379/15
    CELERY_BROKER_URL=redis://localhost:6379/14
    CELERY_RESULT_BACKEND=redis://localhost:6379/13
    SMS_TEST_MODE=true
    KUDOSITY_SMS_ENABLED=false
    OPENAI_API_KEY=test-key-sk-1234567890
    LOG_LEVEL=DEBUG
