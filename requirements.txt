# GrowthHive FastAPI Backend Dependencies

# Core Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database & ORM
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.12.0
greenlet>=3.0.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
bcrypt>=4.0.1
python-multipart>=0.0.6
PyJWT>=2.8.0

# Data Validation & Settings
pydantic>=2.5.0
pydantic-settings>=2.1.0
email-validator>=2.0.0

# HTTP Client & Testing
httpx>=0.25.0
requests>=2.31.0
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
freezegun>=1.2.0
coverage>=7.0.0
moto[s3]>=4.2.0
aiohttp>=3.8.0

# File Handling & Storage
aiofiles>=24.1.0
boto3>=1.34.0
botocore>=1.34.0

# AI & Machine Learning
openai>=1.3.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Multi-Agent Framework
langgraph>=0.0.55
langgraph-checkpoint>=1.0.0
langchain>=0.1.0
langchain-openai>=0.0.8
langchain-community>=0.0.20
langchain-core>=0.1.0

# Vector Database & Embeddings
pgvector>=0.2.4
psycopg[binary]>=3.1.0
psycopg2-binary>=2.9.7

# Memory & Caching
redis>=5.0.0

# Additional AI Tools (OPTIMIZED)
tiktoken>=0.5.0

# Utilities
python-dotenv>=1.0.0
python-dateutil>=2.8.2
pytz>=2023.3
phonenumbers>=8.13.0
tenacity>=8.0.0

# Development & Code Quality
ruff>=0.1.0
mypy>=1.7.0
black>=23.0.0
isort>=5.12.0

# Logging & Monitoring
structlog>=23.2.0
loguru>=0.7.0

# DocQA CLI Dependencies (OPTIMIZED)
typer>=0.9.0
rich>=13.0.0
click>=8.0.0
pytesseract>=0.3.10
pillow>=10.0.0
PyMuPDF>=1.23.0
python-docx>=0.8.11

# Background Task Processing
celery>=5.3.0
kombu>=5.3.0
flower>=2.0.1
redis>=5.0.0

# Document Processing
python-magic>=0.4.27
