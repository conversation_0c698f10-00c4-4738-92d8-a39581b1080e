"""
Meeting Agent Repository
Database operations for meeting booking intents, bookings, and conversation storage
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy import select, insert, update, and_
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database.connection import get_db
from app.models.booking import Booking
from app.models.conversation_message import ConversationMessage
from app.models.lead import Lead

from app.core.logging import logger


class MeetingRepository:
    """Repository for meeting agent database operations"""
    
    async def store_conversation_messages(self, lead_id: str, user_message: str, 
                                        system_response: str, 
                                        franchisor_id: str = None) -> bool:
        """Store conversation messages in database"""
        try:
            async with get_db() as db:
                # Store user message
                user_msg = ConversationMessage(
                    lead_id=uuid.UUID(lead_id),
                    franchisor_id=uuid.UUID(franchisor_id) if franchisor_id else None,
                    sender="lead",
                    message=user_message,
                    message_metadata={"source": "meeting_agent", "type": "user_input"}
                )
                db.add(user_msg)
                
                # Store system response
                system_msg = ConversationMessage(
                    lead_id=uuid.UUID(lead_id),
                    franchisor_id=uuid.UUID(franchisor_id) if franchisor_id else None,
                    sender="system",
                    message=system_response,
                    message_metadata={"source": "meeting_agent", "type": "system_response"}
                )
                db.add(system_msg)
                
                await db.commit()
                logger.info(f"Stored meeting conversation for lead {lead_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error storing conversation messages: {e}")
            return False
    
    async def create_booking_intent(self, lead_id: str, intent_data: Dict[str, Any]) -> Optional[str]:
        """Create a booking intent record to track the booking process"""
        try:
            async with get_db() as db:
                # Create a booking record with 'intent' status
                booking = Booking(
                    lead_id=uuid.UUID(lead_id),
                    customer_name=intent_data.get("customer_name", "Lead Contact"),
                    customer_email=intent_data.get("customer_email"),
                    customer_phone=intent_data.get("customer_phone"),
                    service_type="lead_meeting",
                    staff_name=intent_data.get("staff_name", "Team Member"),
                    start_time=intent_data.get("start_time"),
                    end_time=intent_data.get("end_time"),
                    duration_minutes=intent_data.get("duration_minutes", 30),
                    timezone=intent_data.get("timezone", "Asia/Kolkata"),
                    status="intent",  # Special status for booking intents
                    booking_source="meeting_agent",
                    notes=intent_data.get("notes", "Meeting booking via AI assistant"),
                    booking_metadata={
                        "intent_data": intent_data,
                        "session_id": intent_data.get("session_id"),
                        "phone_number": intent_data.get("phone_number")
                    }
                )
                
                db.add(booking)
                await db.commit()
                await db.refresh(booking)
                
                logger.info(f"Created booking intent {booking.id} for lead {lead_id}")
                return str(booking.id)
                
        except Exception as e:
            logger.error(f"Error creating booking intent: {e}")
            return None
    
    async def update_booking_intent(self, booking_id: str, updates: Dict[str, Any]) -> bool:
        """Update booking intent with new information"""
        try:
            async with get_db() as db:
                stmt = (
                    update(Booking)
                    .where(Booking.id == uuid.UUID(booking_id))
                    .values(**updates)
                )
                
                await db.execute(stmt)
                await db.commit()
                
                logger.info(f"Updated booking intent {booking_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error updating booking intent: {e}")
            return False
    
    async def finalize_booking(self, booking_id: str, zoho_booking_data: Dict[str, Any]) -> bool:
        """Finalize booking with Zoho booking details"""
        try:
            async with get_db() as db:
                updates = {
                    "status": "scheduled",
                    "zoho_booking_id": zoho_booking_data.get("booking_id"),
                    "zoho_service_id": zoho_booking_data.get("service_id"),
                    "zoho_staff_id": zoho_booking_data.get("staff_id"),
                    "booking_url": zoho_booking_data.get("booking_url"),
                    "meeting_link": zoho_booking_data.get("meeting_link"),
                    "reschedule_url": zoho_booking_data.get("reschedule_url"),
                    "cancel_url": zoho_booking_data.get("cancel_url"),
                    "confirmation_sent": True,
                    "booking_metadata": {
                        **zoho_booking_data,
                        "finalized_at": datetime.utcnow().isoformat()
                    }
                }
                
                stmt = (
                    update(Booking)
                    .where(Booking.id == uuid.UUID(booking_id))
                    .values(**updates)
                )
                
                await db.execute(stmt)
                await db.commit()
                
                logger.info(f"Finalized booking {booking_id} with Zoho data")
                return True
                
        except Exception as e:
            logger.error(f"Error finalizing booking: {e}")
            return False
    
    async def cancel_booking(self, booking_id: str, reason: str = None) -> bool:
        """Cancel a booking"""
        try:
            async with get_db() as db:
                updates = {
                    "status": "cancelled",
                    "internal_notes": f"Cancelled via meeting agent. Reason: {reason or 'User requested'}",
                    "booking_metadata": {
                        "cancelled_at": datetime.utcnow().isoformat(),
                        "cancellation_reason": reason
                    }
                }
                
                stmt = (
                    update(Booking)
                    .where(Booking.id == uuid.UUID(booking_id))
                    .values(**updates)
                )
                
                await db.execute(stmt)
                await db.commit()
                
                logger.info(f"Cancelled booking {booking_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error cancelling booking: {e}")
            return False
    
    async def get_booking_by_id(self, booking_id: str) -> Optional[Dict[str, Any]]:
        """Get booking details by ID"""
        try:
            async with get_db() as db:
                stmt = select(Booking).where(Booking.id == uuid.UUID(booking_id))
                result = await db.execute(stmt)
                booking = result.scalar_one_or_none()
                
                if booking:
                    return {
                        "id": str(booking.id),
                        "lead_id": str(booking.lead_id),
                        "status": booking.status,
                        "start_time": booking.start_time,
                        "end_time": booking.end_time,
                        "staff_name": booking.staff_name,
                        "meeting_link": booking.meeting_link,
                        "booking_url": booking.booking_url,
                        "zoho_booking_id": booking.zoho_booking_id,
                        "customer_name": booking.customer_name,
                        "customer_phone": booking.customer_phone,
                        "timezone": booking.timezone,
                        "metadata": booking.booking_metadata
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting booking {booking_id}: {e}")
            return None
    
    async def get_active_booking_for_lead(self, lead_id: str) -> Optional[Dict[str, Any]]:
        """Get active booking for a lead"""
        try:
            async with get_db() as db:
                stmt = (
                    select(Booking)
                    .where(
                        and_(
                            Booking.lead_id == uuid.UUID(lead_id),
                            Booking.status.in_(["scheduled", "confirmed", "intent"])
                        )
                    )
                    .order_by(Booking.created_at.desc())
                )
                
                result = await db.execute(stmt)
                booking = result.scalar_one_or_none()
                
                if booking:
                    return {
                        "id": str(booking.id),
                        "status": booking.status,
                        "start_time": booking.start_time,
                        "end_time": booking.end_time,
                        "staff_name": booking.staff_name,
                        "meeting_link": booking.meeting_link,
                        "zoho_booking_id": booking.zoho_booking_id,
                        "timezone": booking.timezone
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting active booking for lead {lead_id}: {e}")
            return None
    
    async def get_lead_details(self, lead_id: str) -> Optional[Dict[str, Any]]:
        """Get lead details for booking"""
        try:
            async with get_db() as db:
                stmt = select(Lead).where(Lead.id == uuid.UUID(lead_id))
                result = await db.execute(stmt)
                lead = result.scalar_one_or_none()
                
                if lead:
                    return {
                        "id": str(lead.id),
                        "first_name": lead.first_name,
                        "last_name": lead.last_name,
                        "email": lead.email,
                        "phone": lead.phone,
                        "mobile": lead.mobile,
                        "location": lead.location,
                        "timezone": getattr(lead, 'timezone', 'Asia/Kolkata')
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting lead details {lead_id}: {e}")
            return None
    
    async def store_meeting_context_snapshot(self, phone_number: str, 
                                           context_data: Dict[str, Any]) -> bool:
        """Store meeting context snapshot in database for audit trail"""
        try:
            async with get_db() as db:
                # Find lead by phone
                stmt = select(Lead.id).where(
                    (Lead.phone == phone_number) | (Lead.mobile == phone_number)
                )
                result = await db.execute(stmt)
                lead = result.scalar_one_or_none()
                
                if not lead:
                    return False
                
                # Store as conversation message with metadata
                context_msg = ConversationMessage(
                    lead_id=lead.id,
                    sender="system",
                    message="Meeting context snapshot",
                    message_metadata={
                        "type": "meeting_context_snapshot",
                        "context_data": context_data,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )
                
                db.add(context_msg)
                await db.commit()
                
                logger.info(f"Stored meeting context snapshot for {phone_number}")
                return True
                
        except Exception as e:
            logger.error(f"Error storing meeting context snapshot: {e}")
            return False
    
    async def get_conversation_history(self, lead_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history for a lead"""
        try:
            async with get_db() as db:
                stmt = (
                    select(ConversationMessage)
                    .where(ConversationMessage.lead_id == uuid.UUID(lead_id))
                    .order_by(ConversationMessage.created_at.desc())
                    .limit(limit)
                )
                
                result = await db.execute(stmt)
                messages = result.scalars().all()
                
                history = []
                for msg in reversed(messages):  # Reverse to get chronological order
                    history.append({
                        "sender": msg.sender,
                        "message": msg.message,
                        "timestamp": msg.created_at.isoformat(),
                        "metadata": msg.message_metadata
                    })
                
                return history
                
        except Exception as e:
            logger.error(f"Error getting conversation history for lead {lead_id}: {e}")
            return []
