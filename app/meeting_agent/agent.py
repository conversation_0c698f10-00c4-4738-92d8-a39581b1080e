"""
Meeting Agent Orchestrator
Main orchestrator that handles the complete meeting booking flow using FSM, NLU, and Zoho integration
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import structlog

from app.core.config.settings import settings
from app.core.redis_client import get_redis_client
from app.services.zoho_bookings_service import ZohoBookingsService
from app.models.lead import Lead
from app.core.database.connection import get_db

from .fsm import MeetingBookingFSM, MeetingState, MeetingContext
from .nlu import MeetingNLU, MeetingIntent
from .timezone_utils import TimezoneHandler
from .repository import MeetingRepository
from .case_handlers import MeetingCaseHandlers

from app.core.logging import logger


class MeetingAgent:
    """
    Main Meeting Agent that orchestrates the complete meeting booking flow
    Integrates FSM, NLU, Zoho Bookings, and database operations
    """
    
    def __init__(self):
        self.enabled = settings.MEETING_AGENT_ENABLED
        self.provider = settings.MEETING_AGENT_PROVIDER
        self.redis_client = get_redis_client()
        
        # Initialize Zoho service if enabled
        if self.enabled and self.provider == "zoho":
            self.zoho_service = ZohoBookingsService()
        else:
            self.zoho_service = None

        # Initialize components
        self.fsm = MeetingBookingFSM(self.redis_client)
        self.nlu = MeetingNLU()
        self.timezone_handler = TimezoneHandler()
        self.repository = MeetingRepository()
        self.case_handlers = MeetingCaseHandlers(self.zoho_service, self.timezone_handler)
    
    async def process_message(self, message: str, phone_number: str, 
                            lead_id: str = None) -> Dict[str, Any]:
        """
        Process incoming message for meeting booking
        
        Args:
            message: User's message
            phone_number: User's phone number
            lead_id: Lead ID if available
            
        Returns:
            Dict with response and updated context
        """
        try:
            # Check if meeting agent is enabled
            if not self.enabled:
                return {
                    "response": "I'd be happy to help you schedule a meeting. Let me connect you with our team.",
                    "handled": False,
                    "reason": "Meeting agent disabled"
                }
            
            # Quick check if message is meeting-related
            if not self.nlu.is_meeting_related(message):
                return {
                    "response": None,
                    "handled": False,
                    "reason": "Not meeting-related"
                }
            
            # Get or create meeting context
            context = self.fsm.get_context(phone_number)
            if not context:
                if not lead_id:
                    # Try to find lead by phone
                    lead_id = await self._find_lead_by_phone(phone_number)
                
                if not lead_id:
                    return {
                        "response": "I'd be happy to help you schedule a meeting. Let me connect you with our team.",
                        "handled": False,
                        "reason": "Lead not found"
                    }
                
                context = self.fsm.create_context(lead_id, phone_number)
            
            # Check for loop prevention
            if await self._should_prevent_loop(context, message):
                return {
                    "response": "I want to make sure I understand correctly. Let me connect you with our team to help schedule this meeting.",
                    "handled": False,
                    "reason": "Loop prevention triggered"
                }

            # Analyze message with NLU
            analysis = await self.nlu.analyze_message(
                message,
                context.to_dict()
            )

            # Process based on current state and intent
            result = await self._process_state_and_intent(
                context, analysis, message
            )
            
            # Add turn to conversation history
            context.add_turn(message, result["response"])
            self.fsm.save_context(context)
            
            # Store conversation in database
            await self._store_conversation_turn(
                context.lead_id, message, result["response"]
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing meeting message: {e}")
            return {
                "response": "I'm having trouble with the booking system right now. Let me connect you with our team.",
                "handled": False,
                "error": str(e)
            }
    
    async def _process_state_and_intent(self, context: MeetingContext, 
                                      analysis: Dict[str, Any], 
                                      message: str) -> Dict[str, Any]:
        """Process message based on current state and detected intent"""
        current_state = context.current_state
        intent = analysis.get("intent")
        slots = analysis.get("extracted_slots", {})
        
        logger.info(f"Processing: State={current_state.value}, Intent={intent}")
        
        # Handle different states
        if current_state == MeetingState.IDLE:
            return await self._handle_idle_state(context, intent, slots, message)
        
        elif current_state == MeetingState.COLLECT_DAY:
            return await self._handle_collect_day_state(context, intent, slots, message)
        
        elif current_state == MeetingState.COLLECT_TIME:
            return await self._handle_collect_time_state(context, intent, slots, message)
        
        elif current_state == MeetingState.SHOW_SLOTS:
            return await self._handle_show_slots_state(context, intent, slots, message)
        
        elif current_state == MeetingState.AWAIT_CONFIRM:
            return await self._handle_await_confirm_state(context, intent, slots, message)
        
        elif current_state == MeetingState.BOOKED:
            return await self._handle_booked_state(context, intent, slots, message)
        
        else:
            # Fallback for other states
            return {
                "response": "Let me help you with your meeting. What day would work best for you?",
                "handled": True,
                "next_state": MeetingState.COLLECT_DAY
            }
    
    async def _handle_idle_state(self, context: MeetingContext, intent: str,
                               slots: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle IDLE state - initial meeting request using case handlers"""

        # Determine which case this matches and delegate to appropriate handler
        analysis = {"intent": intent, "extracted_slots": slots, "message": message}

        # Check for specific cases
        has_date = "date_preference" in slots or "parsed_date" in slots
        has_time = "time_preference" in slots or "parsed_time" in slots

        if has_date and has_time:
            # Case 1: Clear date & time
            result = await self.case_handlers.handle_case_1_clear_date_time(context, analysis)
        elif "anytime" in message.lower() or "any time" in message.lower():
            # Case 4: Anytime
            result = await self.case_handlers.handle_case_4_anytime(context, analysis)
        elif any(phrase in message.lower() for phrase in ["what do you have", "when are you", "what times", "available"]):
            # Case 5: Availability question
            result = await self.case_handlers.handle_case_5_availability_question(context, analysis)
        elif "next week" in message.lower():
            # Case 7: Next week
            result = await self.case_handlers.handle_case_7_next_week(context, analysis)
        elif has_date:
            # Case 6: Date only
            result = await self.case_handlers.handle_case_6_date_only(context, analysis)
        elif has_time:
            # Case 3: Time only
            result = await self.case_handlers.handle_case_3_time_only(context, analysis)
        elif any(word in message.lower() for word in ["through", "between", "monday", "tuesday", "wednesday", "thursday", "friday"]):
            # Case 2: Date range
            result = await self.case_handlers.handle_case_2_date_range(context, analysis)
        else:
            # Default case
            self.fsm.transition_to(context, MeetingState.COLLECT_DAY)
            result = {
                "response": "I'd be happy to help you schedule a meeting. What day works best for you?",
                "handled": True
            }

        # Update FSM state if specified
        if result.get("next_state"):
            self.fsm.transition_to(context, result["next_state"])

        return result
    
    async def _handle_collect_day_state(self, context: MeetingContext, intent: str,
                                      slots: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle COLLECT_DAY state - collecting date information"""
        
        if "parsed_date" in slots:
            context.target_date_local = slots["parsed_date"]["date"]
            context.reset_clarifications()
            
            # Check if we also have time info
            if context.target_time_local or "parsed_time" in slots:
                if "parsed_time" in slots:
                    context.target_time_local = slots["parsed_time"]["time_str"]
                
                self.fsm.transition_to(context, MeetingState.SHOW_SLOTS)
                return await self._show_available_slots(context)
            else:
                self.fsm.transition_to(context, MeetingState.COLLECT_TIME)
                date_formatted = slots["parsed_date"]["formatted"]
                return {
                    "response": f"Perfect! What time on {date_formatted} would work for you?",
                    "handled": True
                }
        else:
            # Couldn't parse date - ask for clarification
            if context.increment_clarification():
                self.fsm.transition_to(context, MeetingState.FAILED)
                return {
                    "response": "I'm having trouble understanding the date. Let me connect you with our team to schedule this.",
                    "handled": True
                }
            
            return {
                "response": "Could you please specify which day you'd like to meet? For example, 'tomorrow', 'next Friday', or a specific date.",
                "handled": True
            }
    
    async def _handle_collect_time_state(self, context: MeetingContext, intent: str,
                                       slots: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle COLLECT_TIME state - collecting time information"""
        
        if "parsed_time" in slots:
            context.target_time_local = slots["parsed_time"]["time_str"]
            context.reset_clarifications()
            
            self.fsm.transition_to(context, MeetingState.SHOW_SLOTS)
            return await self._show_available_slots(context)
        else:
            # Couldn't parse time - ask for clarification
            if context.increment_clarification():
                self.fsm.transition_to(context, MeetingState.FAILED)
                return {
                    "response": "I'm having trouble understanding the time. Let me connect you with our team to schedule this.",
                    "handled": True
                }
            
            return {
                "response": "What time would work best? You can say something like '2pm', 'morning', or 'anytime'.",
                "handled": True
            }
    
    async def _handle_show_slots_state(self, context: MeetingContext, intent: str,
                                     slots: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle SHOW_SLOTS state - showing available slots"""
        
        # Check if user is selecting a slot or wants different options
        if intent == MeetingIntent.CONFIRM or self.nlu.extract_confirmation_intent(message):
            # User confirmed - book the first available slot
            if context.candidate_slots_local:
                context.selected_slot = context.candidate_slots_local[0]
                self.fsm.transition_to(context, MeetingState.BOOKING)
                return await self._book_meeting(context)
            
        elif intent == MeetingIntent.REJECT or self.nlu.extract_rejection_intent(message):
            # User wants different options
            self.fsm.transition_to(context, MeetingState.COLLECT_DAY)
            return {
                "response": "No problem! What other day would work better for you?",
                "handled": True
            }
        
        # Try to show slots again or handle slot selection
        return await self._show_available_slots(context)
    
    async def _handle_await_confirm_state(self, context: MeetingContext, intent: str,
                                        slots: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle AWAIT_CONFIRM state - awaiting booking confirmation"""
        
        if intent == MeetingIntent.CONFIRM or self.nlu.extract_confirmation_intent(message):
            self.fsm.transition_to(context, MeetingState.BOOKING)
            return await self._book_meeting(context)
        
        elif intent == MeetingIntent.REJECT or self.nlu.extract_rejection_intent(message):
            self.fsm.transition_to(context, MeetingState.SHOW_SLOTS)
            return {
                "response": "No problem! Let me show you other available times.",
                "handled": True
            }
        
        elif intent == MeetingIntent.DEFER:
            return {
                "response": "No problem! I'll keep these options available. Just let me know when you're ready to confirm.",
                "handled": True
            }
        
        else:
            return {
                "response": "Would you like me to book this meeting for you? Just say 'yes' to confirm or 'no' for other options.",
                "handled": True
            }
    
    async def _handle_booked_state(self, context: MeetingContext, intent: str,
                                 slots: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle BOOKED state - meeting already booked"""
        
        if intent == MeetingIntent.RESCHEDULE:
            self.fsm.transition_to(context, MeetingState.RESCHEDULING)
            return {
                "response": "I can help you reschedule. What day would work better for you?",
                "handled": True
            }
        
        elif intent == MeetingIntent.CANCEL:
            self.fsm.transition_to(context, MeetingState.CANCELING)
            return await self._cancel_meeting(context)
        
        else:
            # Show current booking info
            if context.meeting_link:
                return {
                    "response": f"Your meeting is already scheduled. Here's your meeting link: {context.meeting_link}",
                    "handled": True
                }
            else:
                return {
                    "response": "Your meeting is already scheduled. You should have received the details.",
                    "handled": True
                }
    
    async def _show_available_slots(self, context: MeetingContext) -> Dict[str, Any]:
        """Show available slots for the requested date/time"""
        try:
            if not self.zoho_service:
                return {
                    "response": "I'm unable to check availability right now. Let me connect you with our team.",
                    "handled": False
                }
            
            # Get available slots from Zoho
            # This is a simplified version - you'll need to implement the actual slot fetching
            slots = await self._fetch_available_slots(context)
            
            if not slots:
                # No slots available - suggest alternatives
                next_day = self.timezone_handler.get_next_business_day()
                return {
                    "response": f"I don't have any availability on that day. How about {next_day.strftime('%A, %B %d')}?",
                    "handled": True
                }
            
            context.candidate_slots_local = slots
            self.fsm.transition_to(context, MeetingState.AWAIT_CONFIRM)
            
            # Format slots for display
            slot_text = self._format_slots_for_display(slots)
            return {
                "response": f"I have these times available: {slot_text}. Which one works for you?",
                "handled": True
            }
            
        except Exception as e:
            logger.error(f"Error showing available slots: {e}")
            return {
                "response": "I'm having trouble checking availability. Let me connect you with our team.",
                "handled": False
            }
    
    async def _fetch_available_slots(self, context: MeetingContext) -> List[Dict[str, Any]]:
        """Fetch available slots from Zoho Bookings"""
        try:
            if not self.zoho_service:
                return []

            # Parse the target date
            if context.target_date_local:
                from datetime import datetime
                target_date = datetime.fromisoformat(context.target_date_local)
            else:
                # Default to tomorrow
                target_date = datetime.now() + timedelta(days=1)

            # Get available slots
            if context.target_time_local:
                # Get slots matching time preference
                slots = await self.zoho_service.get_slots_for_time_preference(
                    date_preference=target_date,
                    time_preference=context.target_time_local,
                    timezone=context.timezone,
                    service_type="lead_meeting"
                )
            else:
                # Get all slots for the date
                slots = await self.zoho_service.get_available_slots_for_date(
                    target_date=target_date,
                    service_type="lead_meeting"
                )

            # Convert to dict format for context storage
            slot_dicts = []
            for slot in slots[:5]:  # Limit to 5 slots
                slot_dict = {
                    "staff_id": slot.staff_id,
                    "staff_name": slot.staff_name,
                    "start_time": slot.start_time.isoformat(),
                    "end_time": slot.end_time.isoformat(),
                    "service_id": slot.service_id,
                    "duration_minutes": slot.duration_minutes,
                    "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                }
                slot_dicts.append(slot_dict)

            logger.info(f"Fetched {len(slot_dicts)} available slots")
            return slot_dicts

        except Exception as e:
            logger.error(f"Error fetching available slots: {e}")
            return []
    
    def _format_slots_for_display(self, slots: List[Dict[str, Any]]) -> str:
        """Format slots for user-friendly display"""
        if not slots:
            return "no times"
        
        # Format first few slots
        formatted_slots = []
        for slot in slots[:3]:  # Show max 3 slots
            time_str = slot.get("start_time", "")
            formatted_slots.append(time_str)
        
        if len(slots) > 3:
            return ", ".join(formatted_slots) + f" and {len(slots) - 3} more"
        else:
            return " or ".join(formatted_slots)
    
    async def _book_meeting(self, context: MeetingContext) -> Dict[str, Any]:
        """Book the selected meeting slot"""
        try:
            if not self.zoho_service or not context.selected_slot:
                raise Exception("Missing Zoho service or selected slot")

            # Get lead details
            lead_data = await self.repository.get_lead_details(context.lead_id)
            if not lead_data:
                raise Exception("Lead details not found")

            # Convert slot dict back to BookingSlot object
            from app.services.zoho_bookings_service import BookingSlot
            from datetime import datetime

            slot_data = context.selected_slot
            slot = BookingSlot(
                staff_id=slot_data["staff_id"],
                staff_name=slot_data["staff_name"],
                start_time=datetime.fromisoformat(slot_data["start_time"]),
                end_time=datetime.fromisoformat(slot_data["end_time"]),
                service_id=slot_data["service_id"],
                service_name="Lead Meeting",
                duration_minutes=slot_data["duration_minutes"],
                booking_url=""
            )

            # Book the appointment
            result = await self.zoho_service.book_slot_for_meeting_agent(
                slot=slot,
                lead_data=lead_data,
                timezone=context.timezone
            )

            if result.success:
                # Update context with booking details
                context.booking_id = result.booking_id
                context.zoho_booking_id = result.booking_id
                context.meeting_link = result.meeting_link or result.booking_url

                # Create booking intent in database
                booking_intent_data = {
                    "customer_name": f"{lead_data.get('first_name', '')} {lead_data.get('last_name', '')}".strip(),
                    "customer_email": lead_data.get('email'),
                    "customer_phone": lead_data.get('phone') or lead_data.get('mobile'),
                    "start_time": slot.start_time,
                    "end_time": slot.end_time,
                    "duration_minutes": slot.duration_minutes,
                    "timezone": context.timezone,
                    "staff_name": slot.staff_name,
                    "session_id": context.session_id,
                    "phone_number": context.phone_number,
                    "notes": "Meeting booked via AI assistant"
                }

                booking_db_id = await self.repository.create_booking_intent(
                    context.lead_id, booking_intent_data
                )

                if booking_db_id:
                    # Finalize the booking with Zoho details
                    await self.repository.finalize_booking(booking_db_id, {
                        "booking_id": result.booking_id,
                        "service_id": slot.service_id,
                        "staff_id": slot.staff_id,
                        "booking_url": result.booking_url,
                        "meeting_link": result.meeting_link,
                        "staff_name": slot.staff_name
                    })

                self.fsm.transition_to(context, MeetingState.BOOKED)

                # Format response with meeting details
                formatted_time = self.zoho_service.format_slot_for_display(slot, context.timezone)
                response = f"Perfect! Your meeting is booked for {formatted_time} with {slot.staff_name}."

                if context.meeting_link:
                    response += f" Here's your meeting link: {context.meeting_link}"

                return {
                    "response": response,
                    "handled": True
                }
            else:
                raise Exception(f"Booking failed: {result.error_message}")

        except Exception as e:
            logger.error(f"Error booking meeting: {e}")
            self.fsm.transition_to(context, MeetingState.FAILED)
            return {
                "response": "I'm having trouble booking the meeting right now. Let me connect you with our team.",
                "handled": False
            }
    
    async def _cancel_meeting(self, context: MeetingContext) -> Dict[str, Any]:
        """Cancel the booked meeting"""
        try:
            # Implement actual cancellation logic
            # This is a placeholder
            
            self.fsm.transition_to(context, MeetingState.IDLE)
            context.booking_id = None
            context.meeting_link = None
            
            return {
                "response": "Your meeting has been cancelled. Let me know if you'd like to schedule a new one.",
                "handled": True
            }
            
        except Exception as e:
            logger.error(f"Error cancelling meeting: {e}")
            return {
                "response": "I'm having trouble cancelling the meeting. Let me connect you with our team.",
                "handled": False
            }
    
    async def _find_lead_by_phone(self, phone_number: str) -> Optional[str]:
        """Find lead ID by phone number"""
        try:
            async with get_db() as db:
                from sqlalchemy import select
                result = await db.execute(
                    select(Lead.id).where(
                        (Lead.phone == phone_number) | (Lead.mobile == phone_number)
                    )
                )
                lead = result.first()
                return str(lead.id) if lead else None
        except Exception as e:
            logger.error(f"Error finding lead by phone: {e}")
            return None
    
    async def _store_conversation_turn(self, lead_id: str, user_message: str, 
                                     system_response: str):
        """Store conversation turn in database"""
        try:
            await self.repository.store_conversation_messages(
                lead_id, user_message, system_response
            )
        except Exception as e:
            logger.error(f"Error storing conversation: {e}")
    
    def is_enabled(self) -> bool:
        """Check if meeting agent is enabled"""
        return self.enabled

    async def _should_prevent_loop(self, context: MeetingContext, message: str) -> bool:
        """Check if we should prevent loops by detecting repetitive patterns"""
        try:
            # Check if user has exceeded max clarifications
            if context.clarification_count >= context.max_clarifications:
                logger.warning(f"Max clarifications exceeded for {context.phone_number}")
                return True

            # Check for repetitive messages
            recent_messages = [turn.get("user", "") for turn in context.recent_turns_window[-3:]]

            # Simple similarity check - if the same message appears multiple times
            message_lower = message.lower().strip()
            similar_count = sum(1 for msg in recent_messages if self._messages_similar(message_lower, msg.lower().strip()))

            if similar_count >= 2:
                logger.warning(f"Repetitive message pattern detected for {context.phone_number}")
                return True

            # Check if context has expired
            if context.is_expired():
                logger.info(f"Context expired for {context.phone_number}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error in loop prevention check: {e}")
            return False

    def _messages_similar(self, msg1: str, msg2: str) -> bool:
        """Check if two messages are similar (simple implementation)"""
        if not msg1 or not msg2:
            return False

        # Simple word overlap check
        words1 = set(msg1.split())
        words2 = set(msg2.split())

        if len(words1) == 0 or len(words2) == 0:
            return False

        overlap = len(words1.intersection(words2))
        total_unique = len(words1.union(words2))

        # Consider similar if >70% word overlap
        similarity = overlap / total_unique if total_unique > 0 else 0
        return similarity > 0.7

    async def _handle_booking_error(self, context: MeetingContext, error: Exception) -> Dict[str, Any]:
        """Handle booking errors with appropriate fallbacks"""
        try:
            error_msg = str(error).lower()

            if "network" in error_msg or "timeout" in error_msg:
                return {
                    "response": "I'm having trouble connecting to our booking system. Let me try again in a moment, or I can connect you with our team.",
                    "handled": False,
                    "error_type": "network"
                }
            elif "slot" in error_msg and "unavailable" in error_msg:
                return {
                    "response": "That time slot is no longer available. Let me find you some other options.",
                    "handled": True,
                    "error_type": "slot_unavailable"
                }
            elif "authentication" in error_msg or "token" in error_msg:
                return {
                    "response": "I'm having trouble accessing our calendar system. Let me connect you with our team to complete the booking.",
                    "handled": False,
                    "error_type": "auth"
                }
            else:
                return {
                    "response": "I encountered an issue while booking. Let me connect you with our team to ensure this gets scheduled properly.",
                    "handled": False,
                    "error_type": "general"
                }

        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return {
                "response": "Let me connect you with our team to help with scheduling.",
                "handled": False,
                "error_type": "handler_error"
            }
