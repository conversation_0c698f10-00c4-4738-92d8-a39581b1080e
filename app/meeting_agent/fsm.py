"""
Meeting Booking Finite State Machine
Handles the conversation flow for meeting booking with robust state management
"""

import json
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
import structlog

from app.core.config.settings import settings

from app.core.logging import logger


class MeetingState(Enum):
    """Meeting booking states"""
    IDLE = "idle"
    COLLECT_DAY = "collect_day"
    COLLECT_TIME = "collect_time"
    SHOW_SLOTS = "show_slots"
    AWAIT_CONFIRM = "await_confirm"
    BOOKING = "booking"
    BOOKED = "booked"
    RESCHEDULING = "rescheduling"
    CANCELING = "canceling"
    FAILED = "failed"


@dataclass
class MeetingContext:
    """Context for meeting booking conversation"""
    # Core identifiers
    lead_id: str
    phone_number: str
    session_id: str
    
    # Current state
    current_state: MeetingState = MeetingState.IDLE
    
    # Date/time preferences
    target_date_local: Optional[str] = None
    target_time_local: Optional[str] = None
    target_datetime_utc: Optional[str] = None
    
    # Available slots
    candidate_slots_local: List[Dict[str, Any]] = None
    selected_slot: Optional[Dict[str, Any]] = None
    
    # Booking details
    provider_payload: Optional[Dict[str, Any]] = None
    meeting_link: Optional[str] = None
    booking_id: Optional[str] = None
    zoho_booking_id: Optional[str] = None
    
    # Conversation tracking
    recent_turns_window: List[Dict[str, str]] = None
    clarification_count: int = 0
    max_clarifications: int = settings.MEETING_AGENT_MAX_CLARIFICATIONS
    
    # Metadata
    timezone: str = settings.MEETING_AGENT_DEFAULT_TIMEZONE
    locale: str = settings.MEETING_AGENT_LOCALE
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def __post_init__(self):
        """Initialize default values"""
        if self.candidate_slots_local is None:
            self.candidate_slots_local = []
        if self.recent_turns_window is None:
            self.recent_turns_window = []
        if self.created_at is None:
            self.created_at = datetime.utcnow().isoformat()
        self.updated_at = datetime.utcnow().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        data = asdict(self)
        data['current_state'] = self.current_state.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MeetingContext':
        """Create from dictionary"""
        if 'current_state' in data:
            data['current_state'] = MeetingState(data['current_state'])
        return cls(**data)
    
    def add_turn(self, user_message: str, system_response: str):
        """Add conversation turn to recent window"""
        turn = {
            "user": user_message,
            "system": system_response,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.recent_turns_window.append(turn)
        
        # Keep only last 10 turns
        if len(self.recent_turns_window) > 10:
            self.recent_turns_window = self.recent_turns_window[-10:]
        
        self.updated_at = datetime.utcnow().isoformat()
    
    def increment_clarification(self) -> bool:
        """Increment clarification count and check if limit exceeded"""
        self.clarification_count += 1
        return self.clarification_count >= self.max_clarifications
    
    def reset_clarifications(self):
        """Reset clarification count"""
        self.clarification_count = 0
    
    def is_expired(self, timeout_seconds: int = None) -> bool:
        """Check if context has expired"""
        if timeout_seconds is None:
            timeout_seconds = settings.MEETING_AGENT_CONVERSATION_TIMEOUT
        
        if not self.updated_at:
            return False
        
        updated = datetime.fromisoformat(self.updated_at)
        return (datetime.utcnow() - updated).total_seconds() > timeout_seconds


class MeetingBookingFSM:
    """
    Finite State Machine for meeting booking conversations
    Handles state transitions and conversation flow
    """
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.context_key_pattern = "meeting_context:{phone_number}"
        self.context_ttl = settings.MEETING_AGENT_CONVERSATION_TIMEOUT
        
        # State transition rules
        self.transitions = {
            MeetingState.IDLE: [
                MeetingState.COLLECT_DAY,
                MeetingState.COLLECT_TIME,
                MeetingState.SHOW_SLOTS,
                MeetingState.FAILED
            ],
            MeetingState.COLLECT_DAY: [
                MeetingState.COLLECT_TIME,
                MeetingState.SHOW_SLOTS,
                MeetingState.COLLECT_DAY,  # Re-clarify
                MeetingState.FAILED
            ],
            MeetingState.COLLECT_TIME: [
                MeetingState.SHOW_SLOTS,
                MeetingState.COLLECT_TIME,  # Re-clarify
                MeetingState.COLLECT_DAY,   # Back to day
                MeetingState.FAILED
            ],
            MeetingState.SHOW_SLOTS: [
                MeetingState.AWAIT_CONFIRM,
                MeetingState.COLLECT_DAY,   # Different day
                MeetingState.COLLECT_TIME,  # Different time
                MeetingState.FAILED
            ],
            MeetingState.AWAIT_CONFIRM: [
                MeetingState.BOOKING,
                MeetingState.SHOW_SLOTS,    # Different slot
                MeetingState.COLLECT_DAY,   # Different day
                MeetingState.FAILED
            ],
            MeetingState.BOOKING: [
                MeetingState.BOOKED,
                MeetingState.FAILED
            ],
            MeetingState.BOOKED: [
                MeetingState.RESCHEDULING,
                MeetingState.CANCELING,
                MeetingState.IDLE  # New booking
            ],
            MeetingState.RESCHEDULING: [
                MeetingState.COLLECT_DAY,
                MeetingState.COLLECT_TIME,
                MeetingState.SHOW_SLOTS,
                MeetingState.BOOKED,  # Success
                MeetingState.FAILED
            ],
            MeetingState.CANCELING: [
                MeetingState.IDLE,  # Cancelled
                MeetingState.BOOKED,  # Keep booking
                MeetingState.FAILED
            ],
            MeetingState.FAILED: [
                MeetingState.IDLE  # Reset
            ]
        }
    
    def get_context(self, phone_number: str) -> Optional[MeetingContext]:
        """Retrieve meeting context from storage"""
        if not self.redis_client:
            return None
        
        try:
            key = self.context_key_pattern.format(phone_number=phone_number)
            data = self.redis_client.get(key)
            
            if data:
                context_dict = json.loads(data)
                context = MeetingContext.from_dict(context_dict)
                
                # Check if expired
                if context.is_expired():
                    logger.info(f"Meeting context expired for {phone_number}")
                    self.clear_context(phone_number)
                    return None
                
                return context
            
        except Exception as e:
            logger.error(f"Error retrieving meeting context: {e}")
        
        return None
    
    def save_context(self, context: MeetingContext) -> bool:
        """Save meeting context to storage"""
        if not self.redis_client:
            return False
        
        try:
            key = self.context_key_pattern.format(phone_number=context.phone_number)
            context.updated_at = datetime.utcnow().isoformat()
            
            data = json.dumps(context.to_dict())
            self.redis_client.setex(key, self.context_ttl, data)
            
            logger.info(f"Saved meeting context for {context.phone_number} in state {context.current_state.value}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving meeting context: {e}")
            return False
    
    def clear_context(self, phone_number: str) -> bool:
        """Clear meeting context from storage"""
        if not self.redis_client:
            return False
        
        try:
            key = self.context_key_pattern.format(phone_number=phone_number)
            self.redis_client.delete(key)
            logger.info(f"Cleared meeting context for {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing meeting context: {e}")
            return False
    
    def can_transition(self, from_state: MeetingState, to_state: MeetingState) -> bool:
        """Check if state transition is valid"""
        return to_state in self.transitions.get(from_state, [])
    
    def transition_to(self, context: MeetingContext, new_state: MeetingState, 
                     reason: str = None) -> bool:
        """Transition to new state if valid"""
        if not self.can_transition(context.current_state, new_state):
            logger.warning(f"Invalid transition from {context.current_state.value} to {new_state.value}")
            return False
        
        old_state = context.current_state
        context.current_state = new_state
        context.updated_at = datetime.utcnow().isoformat()
        
        logger.info(f"State transition: {old_state.value} -> {new_state.value}" + 
                   (f" ({reason})" if reason else ""))
        
        return self.save_context(context)
    
    def create_context(self, lead_id: str, phone_number: str, 
                      session_id: str = None) -> MeetingContext:
        """Create new meeting context"""
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        context = MeetingContext(
            lead_id=lead_id,
            phone_number=phone_number,
            session_id=session_id
        )
        
        self.save_context(context)
        return context
