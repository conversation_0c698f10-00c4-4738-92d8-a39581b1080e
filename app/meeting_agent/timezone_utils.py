"""
Timezone Utilities for Meeting Agent
Handles timezone-aware date/time parsing, conversion, and formatting
"""

import re
from datetime import datetime, timedelta, time, date
from typing import Dict, Any, Optional, List, Tuple, Union
import pytz
from dateutil import parser as date_parser
import structlog

from app.core.config.settings import settings

from app.core.logging import logger


class TimezoneHandler:
    """
    Handles timezone-aware operations for meeting booking
    Supports natural language date/time parsing with proper timezone handling
    """
    
    def __init__(self, default_timezone: str = None):
        self.default_timezone = default_timezone or settings.MEETING_AGENT_DEFAULT_TIMEZONE
        self.default_tz = pytz.timezone(self.default_timezone)
        
        # Common timezone mappings
        self.timezone_aliases = {
            "ist": "Asia/Kolkata",
            "aest": "Australia/Sydney", 
            "aedt": "Australia/Sydney",
            "pst": "America/Los_Angeles",
            "est": "America/New_York",
            "gmt": "GMT",
            "utc": "UTC"
        }
        
        # Weekday mappings
        self.weekdays = {
            "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
            "friday": 4, "saturday": 5, "sunday": 6,
            "mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6
        }
        
        # Time period mappings
        self.time_periods = {
            "morning": (9, 12),
            "afternoon": (12, 17),
            "evening": (17, 20),
            "night": (20, 23)
        }
    
    def get_timezone(self, timezone_str: str = None) -> pytz.BaseTzInfo:
        """Get timezone object from string"""
        if not timezone_str:
            return self.default_tz
        
        # Check aliases first
        tz_lower = timezone_str.lower()
        if tz_lower in self.timezone_aliases:
            timezone_str = self.timezone_aliases[tz_lower]
        
        try:
            return pytz.timezone(timezone_str)
        except pytz.UnknownTimeZoneError:
            logger.warning(f"Unknown timezone: {timezone_str}, using default")
            return self.default_tz
    
    async def parse_date_expression(self, date_expr: str, timezone: str = None) -> Optional[Dict[str, Any]]:
        """
        Parse natural language date expressions
        
        Args:
            date_expr: Date expression like "tomorrow", "next Friday", "March 15"
            timezone: Target timezone
            
        Returns:
            Dict with parsed date information
        """
        try:
            tz = self.get_timezone(timezone)
            now = datetime.now(tz)
            date_expr_lower = date_expr.lower().strip()
            
            # Handle relative dates
            if "today" in date_expr_lower:
                target_date = now.date()
            elif "tomorrow" in date_expr_lower:
                target_date = (now + timedelta(days=1)).date()
            elif "next week" in date_expr_lower:
                # Next Monday
                days_ahead = 7 - now.weekday()
                target_date = (now + timedelta(days=days_ahead)).date()
            elif "this week" in date_expr_lower:
                # This Friday if it's before Friday, otherwise next Friday
                friday = 4  # Friday is day 4
                if now.weekday() <= friday:
                    days_ahead = friday - now.weekday()
                else:
                    days_ahead = 7 - now.weekday() + friday
                target_date = (now + timedelta(days=days_ahead)).date()
            else:
                # Try to parse weekday names
                weekday_match = None
                for day_name, day_num in self.weekdays.items():
                    if day_name in date_expr_lower:
                        weekday_match = day_num
                        break
                
                if weekday_match is not None:
                    # Find next occurrence of this weekday
                    days_ahead = (weekday_match - now.weekday()) % 7
                    if days_ahead == 0:  # Today is the weekday
                        days_ahead = 7 if "next" in date_expr_lower else 0
                    target_date = (now + timedelta(days=days_ahead)).date()
                else:
                    # Try dateutil parser for absolute dates
                    try:
                        parsed = date_parser.parse(date_expr, default=now)
                        target_date = parsed.date()
                    except:
                        return None
            
            return {
                "date": target_date.isoformat(),
                "date_obj": target_date,
                "weekday": target_date.strftime("%A"),
                "formatted": target_date.strftime("%B %d, %Y"),
                "timezone": timezone or self.default_timezone,
                "is_relative": any(word in date_expr_lower for word in ["today", "tomorrow", "next", "this"])
            }
            
        except Exception as e:
            logger.error(f"Error parsing date expression '{date_expr}': {e}")
            return None
    
    async def parse_time_expression(self, time_expr: str, timezone: str = None) -> Optional[Dict[str, Any]]:
        """
        Parse natural language time expressions
        
        Args:
            time_expr: Time expression like "2pm", "morning", "between 2-4pm"
            timezone: Target timezone
            
        Returns:
            Dict with parsed time information
        """
        try:
            tz = self.get_timezone(timezone)
            time_expr_lower = time_expr.lower().strip()
            
            # Handle time periods
            for period, (start_hour, end_hour) in self.time_periods.items():
                if period in time_expr_lower:
                    return {
                        "time_period": period,
                        "start_time": time(start_hour, 0),
                        "end_time": time(end_hour, 0),
                        "start_time_str": f"{start_hour}:00",
                        "end_time_str": f"{end_hour}:00",
                        "formatted": period.title(),
                        "timezone": timezone or self.default_timezone,
                        "is_range": True
                    }
            
            # Handle "anytime"
            if "anytime" in time_expr_lower or "any time" in time_expr_lower:
                return {
                    "time_period": "anytime",
                    "start_time": time(9, 0),
                    "end_time": time(17, 0),
                    "start_time_str": "09:00",
                    "end_time_str": "17:00",
                    "formatted": "Anytime",
                    "timezone": timezone or self.default_timezone,
                    "is_flexible": True
                }
            
            # Handle time ranges like "2-4pm", "between 2 and 4"
            range_patterns = [
                r'(\d{1,2})\s*-\s*(\d{1,2})\s*(am|pm)?',
                r'between\s+(\d{1,2})\s+and\s+(\d{1,2})\s*(am|pm)?',
                r'from\s+(\d{1,2})\s+to\s+(\d{1,2})\s*(am|pm)?'
            ]
            
            for pattern in range_patterns:
                match = re.search(pattern, time_expr_lower)
                if match:
                    start_hour = int(match.group(1))
                    end_hour = int(match.group(2))
                    period = match.group(3) if len(match.groups()) >= 3 else None
                    
                    # Adjust for AM/PM
                    if period == "pm" and start_hour < 12:
                        start_hour += 12
                        end_hour += 12
                    elif period == "am" and start_hour == 12:
                        start_hour = 0
                    
                    return {
                        "start_time": time(start_hour, 0),
                        "end_time": time(end_hour, 0),
                        "start_time_str": f"{start_hour:02d}:00",
                        "end_time_str": f"{end_hour:02d}:00",
                        "formatted": f"{start_hour}:00 - {end_hour}:00",
                        "timezone": timezone or self.default_timezone,
                        "is_range": True
                    }
            
            # Handle specific times like "2pm", "14:30", "2:30pm"
            time_patterns = [
                r'(\d{1,2}):(\d{2})\s*(am|pm)?',
                r'(\d{1,2})\s*(am|pm)',
                r'(\d{1,2})\.(\d{2})\s*(am|pm)?'
            ]
            
            for pattern in time_patterns:
                match = re.search(pattern, time_expr_lower)
                if match:
                    hour = int(match.group(1))
                    minute = int(match.group(2)) if len(match.groups()) >= 2 and match.group(2) else 0
                    period = match.group(3) if len(match.groups()) >= 3 else None
                    
                    # Adjust for AM/PM
                    if period == "pm" and hour < 12:
                        hour += 12
                    elif period == "am" and hour == 12:
                        hour = 0
                    
                    target_time = time(hour, minute)
                    return {
                        "time": target_time,
                        "time_str": f"{hour:02d}:{minute:02d}",
                        "formatted": target_time.strftime("%I:%M %p"),
                        "timezone": timezone or self.default_timezone,
                        "is_specific": True
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing time expression '{time_expr}': {e}")
            return None
    
    def combine_date_time(self, date_info: Dict[str, Any], time_info: Dict[str, Any], 
                         timezone: str = None) -> Optional[datetime]:
        """Combine parsed date and time information into datetime"""
        try:
            tz = self.get_timezone(timezone)
            
            # Get date
            if "date_obj" in date_info:
                target_date = date_info["date_obj"]
            else:
                target_date = datetime.fromisoformat(date_info["date"]).date()
            
            # Get time
            if "time" in time_info:
                target_time = time_info["time"]
            elif "start_time" in time_info:
                target_time = time_info["start_time"]
            else:
                target_time = time(9, 0)  # Default to 9 AM
            
            # Combine and localize
            naive_dt = datetime.combine(target_date, target_time)
            localized_dt = tz.localize(naive_dt)
            
            return localized_dt
            
        except Exception as e:
            logger.error(f"Error combining date/time: {e}")
            return None
    
    def to_utc(self, dt: datetime) -> datetime:
        """Convert datetime to UTC"""
        if dt.tzinfo is None:
            # Assume default timezone
            dt = self.default_tz.localize(dt)
        return dt.astimezone(pytz.UTC)
    
    def to_local(self, dt: datetime, timezone: str = None) -> datetime:
        """Convert datetime to local timezone"""
        tz = self.get_timezone(timezone)
        if dt.tzinfo is None:
            dt = pytz.UTC.localize(dt)
        return dt.astimezone(tz)
    
    def format_datetime_local(self, dt: datetime, timezone: str = None, 
                            include_timezone: bool = True) -> str:
        """Format datetime in local timezone for user display"""
        local_dt = self.to_local(dt, timezone)
        
        format_str = "%A, %B %d at %I:%M %p"
        if include_timezone:
            format_str += " %Z"
        
        return local_dt.strftime(format_str)
    
    def is_business_hours(self, dt: datetime, timezone: str = None) -> bool:
        """Check if datetime falls within business hours"""
        local_dt = self.to_local(dt, timezone)
        
        # Business hours: Monday-Friday, 9 AM - 5 PM
        if local_dt.weekday() >= 5:  # Weekend
            return False
        
        hour = local_dt.hour
        return 9 <= hour < 17
    
    def get_next_business_day(self, from_date: date = None, timezone: str = None) -> date:
        """Get next business day from given date"""
        if from_date is None:
            tz = self.get_timezone(timezone)
            from_date = datetime.now(tz).date()
        
        next_day = from_date + timedelta(days=1)
        
        # Skip weekends
        while next_day.weekday() >= 5:
            next_day += timedelta(days=1)
        
        return next_day
    
    def parse_duration(self, duration_str: str) -> Optional[timedelta]:
        """Parse duration string like '30 minutes', '1 hour', '1.5 hours'"""
        try:
            duration_lower = duration_str.lower().strip()
            
            # Pattern for "X minutes"
            minutes_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:min|minute|minutes)', duration_lower)
            if minutes_match:
                minutes = float(minutes_match.group(1))
                return timedelta(minutes=minutes)
            
            # Pattern for "X hours"
            hours_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:hr|hour|hours)', duration_lower)
            if hours_match:
                hours = float(hours_match.group(1))
                return timedelta(hours=hours)
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing duration '{duration_str}': {e}")
            return None
