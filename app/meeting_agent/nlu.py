"""
Meeting NLU (Natural Language Understanding) Service
Handles intent detection, slot extraction, and natural language generation for meeting booking
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import structlog
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from app.core.config.settings import settings
from .timezone_utils import TimezoneHandler

from app.core.logging import logger


class MeetingIntent:
    """Meeting booking intents"""
    SCHEDULE = "schedule"
    RESCHEDULE = "reschedule"
    CANCEL = "cancel"
    CHECK_AVAILABILITY = "check_availability"
    CONFIRM = "confirm"
    REJECT = "reject"
    CLARIFY_DATE = "clarify_date"
    CLARIFY_TIME = "clarify_time"
    DEFER = "defer"
    UNKNOWN = "unknown"


class MeetingNLU:
    """
    Natural Language Understanding for meeting booking conversations
    Uses OpenAI for robust intent detection and slot extraction
    """
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=0.1,  # Low temperature for consistent parsing
            api_key=settings.OPENAI_API_KEY
        )
        self.timezone_handler = TimezoneHandler()
    
    async def analyze_message(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Analyze user message for meeting booking intent and extract slots
        
        Args:
            message: User's message
            context: Conversation context including recent turns
            
        Returns:
            Dict with intent, confidence, extracted slots, and suggested response
        """
        try:
            # Build context for better understanding
            conversation_context = ""
            if context and context.get("recent_turns_window"):
                recent_turns = context["recent_turns_window"][-3:]  # Last 3 turns
                for turn in recent_turns:
                    conversation_context += f"User: {turn.get('user', '')}\nSystem: {turn.get('system', '')}\n"
            
            # Create analysis prompt
            system_prompt = self._build_analysis_prompt()
            user_prompt = self._build_user_prompt(message, conversation_context, context)
            
            # Get OpenAI analysis
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            result = self._parse_analysis_response(response.content)
            
            # Enhance with timezone-aware date parsing
            if result.get("extracted_slots"):
                result["extracted_slots"] = await self._enhance_date_time_slots(
                    result["extracted_slots"], 
                    context.get("timezone", settings.MEETING_AGENT_DEFAULT_TIMEZONE)
                )
            
            logger.info(f"NLU Analysis: {message[:50]}... -> Intent: {result.get('intent')}")
            return result
            
        except Exception as e:
            logger.error(f"Error in NLU analysis: {e}")
            return {
                "intent": MeetingIntent.UNKNOWN,
                "confidence": 0.0,
                "extracted_slots": {},
                "suggested_response": "I'm having trouble understanding. Could you please rephrase that?",
                "error": str(e)
            }
    
    def _build_analysis_prompt(self) -> str:
        """Build system prompt for meeting analysis"""
        return """You are an expert meeting booking assistant. Analyze user messages to determine their intent and extract relevant information.

INTENTS:
- schedule: User wants to book a new meeting
- reschedule: User wants to change existing meeting
- cancel: User wants to cancel existing meeting  
- check_availability: User asks what times are available
- confirm: User confirms a proposed time/slot
- reject: User rejects a proposed time/slot
- clarify_date: User provides date information when asked
- clarify_time: User provides time information when asked
- defer: User wants to postpone decision ("I'll get back to you")
- unknown: Cannot determine intent

SLOT EXTRACTION:
Extract these slots when present:
- date_preference: Specific dates, weekdays, "tomorrow", "next week", etc.
- time_preference: Specific times, time ranges, "morning", "afternoon", "evening", "anytime"
- urgency: "urgent", "asap", "soon", "flexible"
- duration_preference: "30 minutes", "1 hour", etc.
- availability_type: "any day", "weekdays only", "weekends", etc.

RESPONSE GUIDELINES:
- Be conversational and human-like
- No emojis
- Ask minimal clarifying questions
- Acknowledge user preferences
- Use contractions naturally

Return JSON format:
{
    "intent": "intent_name",
    "confidence": 0.0-1.0,
    "extracted_slots": {
        "date_preference": "extracted_date_info",
        "time_preference": "extracted_time_info",
        "urgency": "urgency_level",
        "duration_preference": "duration",
        "availability_type": "availability_type"
    },
    "reasoning": "brief explanation of analysis",
    "suggested_response": "natural response to user"
}"""
    
    def _build_user_prompt(self, message: str, conversation_context: str, context: Dict[str, Any]) -> str:
        """Build user prompt with message and context"""
        current_state = context.get("current_state", "idle") if context else "idle"
        
        prompt = f"""CURRENT CONVERSATION STATE: {current_state}

RECENT CONVERSATION:
{conversation_context}

CURRENT USER MESSAGE: "{message}"

Analyze this message considering the conversation context and current state. Extract intent and relevant slots."""
        
        return prompt
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse OpenAI response into structured format"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # Validate and clean result
                result["intent"] = result.get("intent", MeetingIntent.UNKNOWN)
                result["confidence"] = float(result.get("confidence", 0.0))
                result["extracted_slots"] = result.get("extracted_slots", {})
                result["suggested_response"] = result.get("suggested_response", "")
                
                return result
            else:
                # Fallback parsing
                return self._fallback_parse(response)
                
        except json.JSONDecodeError:
            return self._fallback_parse(response)
    
    def _fallback_parse(self, response: str) -> Dict[str, Any]:
        """Fallback parsing when JSON extraction fails"""
        # Simple keyword-based intent detection
        message_lower = response.lower()
        
        if any(word in message_lower for word in ["book", "schedule", "meeting", "appointment"]):
            intent = MeetingIntent.SCHEDULE
        elif any(word in message_lower for word in ["reschedule", "change", "move"]):
            intent = MeetingIntent.RESCHEDULE
        elif any(word in message_lower for word in ["cancel", "delete"]):
            intent = MeetingIntent.CANCEL
        elif any(word in message_lower for word in ["available", "availability", "free"]):
            intent = MeetingIntent.CHECK_AVAILABILITY
        elif any(word in message_lower for word in ["yes", "confirm", "ok", "sounds good"]):
            intent = MeetingIntent.CONFIRM
        elif any(word in message_lower for word in ["no", "not", "different"]):
            intent = MeetingIntent.REJECT
        else:
            intent = MeetingIntent.UNKNOWN
        
        return {
            "intent": intent,
            "confidence": 0.5,
            "extracted_slots": {},
            "suggested_response": "Let me help you with that.",
            "reasoning": "Fallback keyword-based parsing"
        }
    
    async def _enhance_date_time_slots(self, slots: Dict[str, Any], timezone: str) -> Dict[str, Any]:
        """Enhance extracted slots with timezone-aware date/time parsing"""
        enhanced_slots = slots.copy()
        
        # Parse date preference
        if "date_preference" in slots:
            date_info = await self.timezone_handler.parse_date_expression(
                slots["date_preference"], timezone
            )
            if date_info:
                enhanced_slots["parsed_date"] = date_info
        
        # Parse time preference  
        if "time_preference" in slots:
            time_info = await self.timezone_handler.parse_time_expression(
                slots["time_preference"], timezone
            )
            if time_info:
                enhanced_slots["parsed_time"] = time_info
        
        return enhanced_slots
    
    async def generate_response(self, intent: str, slots: Dict[str, Any], 
                              context: Dict[str, Any] = None) -> str:
        """
        Generate natural language response based on intent and context
        
        Args:
            intent: Detected intent
            slots: Extracted slots
            context: Conversation context
            
        Returns:
            Natural language response
        """
        try:
            # Build response generation prompt
            system_prompt = """You are Andy, a friendly meeting booking assistant. Generate natural, conversational responses.

STYLE GUIDELINES:
- Be warm and professional
- Use contractions naturally (I'll, you're, that's, etc.)
- No emojis
- Keep responses concise but friendly
- Acknowledge user preferences
- Ask minimal clarifying questions when needed

RESPONSE PATTERNS:
- Confirmation: "Great! I'll book that for you."
- Clarification: "Just to confirm, you'd like to meet on..."
- Options: "I have these times available..."
- Acknowledgment: "That sounds perfect."
"""
            
            user_prompt = f"""INTENT: {intent}
EXTRACTED_SLOTS: {json.dumps(slots)}
CONTEXT: {json.dumps(context) if context else 'None'}

Generate an appropriate response for this situation."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I'd be happy to help you schedule a meeting. What day works best for you?"
    
    def is_meeting_related(self, message: str) -> bool:
        """Quick check if message is meeting-related"""
        meeting_keywords = [
            "meeting", "appointment", "schedule", "book", "calendar", "time", "date",
            "available", "free", "busy", "reschedule", "cancel", "confirm",
            "tomorrow", "today", "next week", "monday", "tuesday", "wednesday", 
            "thursday", "friday", "saturday", "sunday", "morning", "afternoon", 
            "evening", "am", "pm"
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in meeting_keywords)
    
    def extract_confirmation_intent(self, message: str) -> bool:
        """Extract confirmation intent from message"""
        confirmation_patterns = [
            r'\b(yes|yeah|yep|sure|ok|okay|sounds good|perfect|great)\b',
            r'\b(that works|works for me|i\'ll take it)\b',
            r'\b(book it|confirm|let\'s do it)\b'
        ]
        
        message_lower = message.lower()
        return any(re.search(pattern, message_lower) for pattern in confirmation_patterns)
    
    def extract_rejection_intent(self, message: str) -> bool:
        """Extract rejection intent from message"""
        rejection_patterns = [
            r'\b(no|nope|not|different|another|other)\b',
            r'\b(doesn\'t work|won\'t work|can\'t do)\b',
            r'\b(different time|another day|other options)\b'
        ]
        
        message_lower = message.lower()
        return any(re.search(pattern, message_lower) for pattern in rejection_patterns)
