"""
Meeting Booking Case Handlers
Implements the 11 specific meeting booking cases as outlined in the requirements
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import structlog

from .fsm import MeetingContext, MeetingState
from .nlu import MeetingIntent
from .timezone_utils import TimezoneHandler

from app.core.logging import logger


class MeetingCaseHandlers:
    """
    Handles the 11 specific meeting booking cases:
    1. Clear date & time
    2. Range of dates  
    3. Time-only
    4. 'Anytime'
    5. 'What do you have available?' (availability questions)
    6. Date-only
    7. 'Next week'
    8. User defers confirmation
    9. Holiday/no slots
    10. Reschedule existing meeting
    11. Cancel existing meeting
    """
    
    def __init__(self, zoho_service=None, timezone_handler=None):
        self.zoho_service = zoho_service
        self.timezone_handler = timezone_handler or TimezoneHandler()
    
    async def handle_case_1_clear_date_time(self, context: MeetingContext, 
                                          analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 1: Clear date & time
        Example: "Can we meet on Friday at 2pm?"
        """
        try:
            slots = analysis.get("extracted_slots", {})
            
            if "parsed_date" in slots and "parsed_time" in slots:
                date_info = slots["parsed_date"]
                time_info = slots["parsed_time"]
                
                # Combine date and time
                target_datetime = self.timezone_handler.combine_date_time(
                    date_info, time_info, context.timezone
                )
                
                if target_datetime:
                    context.target_datetime_utc = self.timezone_handler.to_utc(target_datetime).isoformat()
                    context.target_date_local = date_info["date"]
                    context.target_time_local = time_info.get("time_str", time_info.get("formatted"))
                    
                    # Get available slots around that time
                    available_slots = await self._get_slots_near_time(context, target_datetime)
                    
                    if available_slots:
                        context.candidate_slots_local = available_slots
                        formatted_time = self.timezone_handler.format_datetime_local(
                            target_datetime, context.timezone
                        )
                        
                        return {
                            "response": f"Great! I have availability around {formatted_time}. Would you like me to book that for you?",
                            "next_state": MeetingState.AWAIT_CONFIRM,
                            "handled": True
                        }
                    else:
                        # No slots available - suggest alternatives
                        return await self._suggest_alternative_times(context, target_datetime)
            
            return {
                "response": "I couldn't understand the specific date and time. Could you please clarify?",
                "handled": False
            }
            
        except Exception as e:
            logger.error(f"Error handling case 1 (clear date/time): {e}")
            return {"response": "Let me help you find a suitable time.", "handled": False}
    
    async def handle_case_2_date_range(self, context: MeetingContext, 
                                     analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 2: Range of dates
        Example: "I'm available Monday through Wednesday" or "Any day next week"
        """
        try:
            message = analysis.get("message", "").lower()
            
            # Detect range patterns
            range_patterns = [
                r'(\w+day)\s+(?:through|to|until|\-)\s+(\w+day)',
                r'(\w+day)\s*\-\s*(\w+day)',
                r'between\s+(\w+day)\s+and\s+(\w+day)',
                r'any\s+day\s+(?:this|next)\s+week'
            ]
            
            for pattern in range_patterns:
                match = re.search(pattern, message)
                if match:
                    if "any day" in message:
                        # Handle "any day this/next week"
                        return await self._handle_any_day_week(context, message)
                    else:
                        # Handle specific day range
                        start_day = match.group(1)
                        end_day = match.group(2)
                        return await self._handle_day_range(context, start_day, end_day)
            
            return {
                "response": "I'd be happy to find times in your preferred date range. Could you be more specific about which days work best?",
                "handled": False
            }
            
        except Exception as e:
            logger.error(f"Error handling case 2 (date range): {e}")
            return {"response": "Let me help you find suitable dates.", "handled": False}
    
    async def handle_case_3_time_only(self, context: MeetingContext, 
                                    analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 3: Time-only
        Example: "2pm works for me" or "How about in the morning?"
        """
        try:
            slots = analysis.get("extracted_slots", {})
            
            if "parsed_time" in slots:
                time_info = slots["parsed_time"]
                context.target_time_local = time_info.get("time_str", time_info.get("formatted"))
                
                # Need to ask for date
                time_formatted = time_info.get("formatted", context.target_time_local)
                
                return {
                    "response": f"Perfect! {time_formatted} sounds good. Which day would work best for you?",
                    "next_state": MeetingState.COLLECT_DAY,
                    "handled": True
                }
            
            return {
                "response": "What time would work best for you?",
                "handled": False
            }
            
        except Exception as e:
            logger.error(f"Error handling case 3 (time only): {e}")
            return {"response": "What time would be convenient for you?", "handled": False}
    
    async def handle_case_4_anytime(self, context: MeetingContext, 
                                  analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 4: 'Anytime'
        Example: "I'm flexible, anytime works" or "Any time is fine"
        """
        try:
            # Get next available business slots
            if self.zoho_service:
                next_slots = await self.zoho_service.get_next_available_business_slots(
                    max_slots=3,
                    timezone=context.timezone,
                    service_type="lead_meeting"
                )
                
                if next_slots:
                    context.candidate_slots_local = [
                        {
                            "staff_id": slot.staff_id,
                            "staff_name": slot.staff_name,
                            "start_time": slot.start_time.isoformat(),
                            "end_time": slot.end_time.isoformat(),
                            "service_id": slot.service_id,
                            "duration_minutes": slot.duration_minutes,
                            "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                        }
                        for slot in next_slots
                    ]
                    
                    # Format slot options
                    slot_options = []
                    for slot_data in context.candidate_slots_local:
                        slot_options.append(slot_data["formatted_time"])
                    
                    options_text = ", ".join(slot_options[:2])
                    if len(slot_options) > 2:
                        options_text += f", or {slot_options[2]}"
                    
                    return {
                        "response": f"Great! I have these times available: {options_text}. Which one works best for you?",
                        "next_state": MeetingState.AWAIT_CONFIRM,
                        "handled": True
                    }
            
            # Fallback if no Zoho service
            return {
                "response": "Perfect! I have availability tomorrow at 2pm or Friday at 10am. Which would you prefer?",
                "next_state": MeetingState.AWAIT_CONFIRM,
                "handled": True
            }
            
        except Exception as e:
            logger.error(f"Error handling case 4 (anytime): {e}")
            return {"response": "Let me check our availability and get back to you.", "handled": False}
    
    async def handle_case_5_availability_question(self, context: MeetingContext, 
                                                 analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 5: 'What do you have available?' (availability questions)
        Example: "What times do you have?" or "When are you free?"
        """
        try:
            # Check if they specified a day
            slots = analysis.get("extracted_slots", {})
            
            if "parsed_date" in slots:
                # They asked about a specific day
                date_info = slots["parsed_date"]
                target_date = datetime.fromisoformat(date_info["date"])
                
                if self.zoho_service:
                    available_slots = await self.zoho_service.get_available_slots_for_date(
                        target_date=target_date,
                        service_type="lead_meeting"
                    )
                    
                    if available_slots:
                        context.candidate_slots_local = [
                            {
                                "staff_id": slot.staff_id,
                                "staff_name": slot.staff_name,
                                "start_time": slot.start_time.isoformat(),
                                "end_time": slot.end_time.isoformat(),
                                "service_id": slot.service_id,
                                "duration_minutes": slot.duration_minutes,
                                "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                            }
                            for slot in available_slots[:3]
                        ]
                        
                        slot_times = [slot_data["formatted_time"] for slot_data in context.candidate_slots_local]
                        times_text = ", ".join(slot_times)
                        
                        return {
                            "response": f"On {date_info['formatted']}, I have these times available: {times_text}. Which one works for you?",
                            "next_state": MeetingState.AWAIT_CONFIRM,
                            "handled": True
                        }
                    else:
                        return {
                            "response": f"I don't have any availability on {date_info['formatted']}. How about the next day?",
                            "next_state": MeetingState.COLLECT_DAY,
                            "handled": True
                        }
            else:
                # General availability question
                return await self.handle_case_4_anytime(context, analysis)
            
        except Exception as e:
            logger.error(f"Error handling case 5 (availability question): {e}")
            return {"response": "Let me check our availability for you.", "handled": False}
    
    async def handle_case_6_date_only(self, context: MeetingContext, 
                                    analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 6: Date-only
        Example: "How about Friday?" or "Tomorrow works"
        """
        try:
            slots = analysis.get("extracted_slots", {})
            
            if "parsed_date" in slots:
                date_info = slots["parsed_date"]
                context.target_date_local = date_info["date"]
                
                return {
                    "response": f"Great! What time on {date_info['formatted']} would work best for you?",
                    "next_state": MeetingState.COLLECT_TIME,
                    "handled": True
                }
            
            return {
                "response": "Which day would work best for you?",
                "handled": False
            }
            
        except Exception as e:
            logger.error(f"Error handling case 6 (date only): {e}")
            return {"response": "What day would be convenient?", "handled": False}
    
    async def handle_case_7_next_week(self, context: MeetingContext, 
                                    analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 7: 'Next week'
        Example: "Next week would be better" or "How about next week?"
        """
        try:
            # Calculate next week's date range
            now = datetime.now(self.timezone_handler.get_timezone(context.timezone))
            days_until_next_monday = 7 - now.weekday()
            next_monday = now + timedelta(days=days_until_next_monday)
            next_friday = next_monday + timedelta(days=4)
            
            if self.zoho_service:
                # Get slots for next week
                next_week_slots = await self.zoho_service.get_available_slots_for_date_range(
                    start_date=next_monday,
                    end_date=next_friday,
                    timezone=context.timezone,
                    service_type="lead_meeting"
                )
                
                if next_week_slots:
                    # Group by day and show options
                    context.candidate_slots_local = [
                        {
                            "staff_id": slot.staff_id,
                            "staff_name": slot.staff_name,
                            "start_time": slot.start_time.isoformat(),
                            "end_time": slot.end_time.isoformat(),
                            "service_id": slot.service_id,
                            "duration_minutes": slot.duration_minutes,
                            "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                        }
                        for slot in next_week_slots[:3]
                    ]
                    
                    slot_options = [slot_data["formatted_time"] for slot_data in context.candidate_slots_local]
                    options_text = ", ".join(slot_options)
                    
                    return {
                        "response": f"Perfect! Next week I have these times available: {options_text}. Which one works for you?",
                        "next_state": MeetingState.AWAIT_CONFIRM,
                        "handled": True
                    }
            
            # Fallback
            return {
                "response": "Great! Next week works well. I have availability on Tuesday at 10am or Thursday at 2pm. Which would you prefer?",
                "next_state": MeetingState.AWAIT_CONFIRM,
                "handled": True
            }
            
        except Exception as e:
            logger.error(f"Error handling case 7 (next week): {e}")
            return {"response": "Next week sounds good. What day would work best?", "handled": False}
    
    async def handle_case_8_user_defers(self, context: MeetingContext, 
                                      analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 8: User defers confirmation
        Example: "Let me think about it" or "I'll get back to you"
        """
        try:
            # Store current context but don't book
            defer_responses = [
                "No problem! I'll keep these options available for you. Just let me know when you're ready to confirm.",
                "That's perfectly fine. Take your time, and reach out when you'd like to schedule.",
                "Of course! I'll hold these times for now. Just message me when you're ready to book."
            ]
            
            # Use different responses based on context
            if context.candidate_slots_local:
                response = defer_responses[0]
            else:
                response = defer_responses[1]
            
            return {
                "response": response,
                "next_state": MeetingState.AWAIT_CONFIRM,  # Keep in same state
                "handled": True
            }
            
        except Exception as e:
            logger.error(f"Error handling case 8 (user defers): {e}")
            return {"response": "No problem! Let me know when you're ready to schedule.", "handled": True}
    
    # Helper methods
    
    async def _get_slots_near_time(self, context: MeetingContext, 
                                 target_datetime: datetime) -> List[Dict[str, Any]]:
        """Get available slots near the target datetime"""
        try:
            if not self.zoho_service:
                return []
            
            # Get slots for the target date
            slots = await self.zoho_service.get_available_slots_for_date(
                target_date=target_datetime,
                service_type="lead_meeting"
            )
            
            # Filter slots within 2 hours of target time
            target_hour = target_datetime.hour
            nearby_slots = []
            
            for slot in slots:
                slot_hour = slot.start_time.hour
                if abs(slot_hour - target_hour) <= 2:
                    nearby_slots.append({
                        "staff_id": slot.staff_id,
                        "staff_name": slot.staff_name,
                        "start_time": slot.start_time.isoformat(),
                        "end_time": slot.end_time.isoformat(),
                        "service_id": slot.service_id,
                        "duration_minutes": slot.duration_minutes,
                        "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                    })
            
            return nearby_slots[:3]  # Return max 3 slots
            
        except Exception as e:
            logger.error(f"Error getting slots near time: {e}")
            return []
    
    async def _suggest_alternative_times(self, context: MeetingContext, 
                                       original_datetime: datetime) -> Dict[str, Any]:
        """Suggest alternative times when requested time is not available"""
        try:
            # Try next day
            next_day = original_datetime + timedelta(days=1)
            
            if self.zoho_service:
                alt_slots = await self.zoho_service.get_available_slots_for_date(
                    target_date=next_day,
                    service_type="lead_meeting"
                )
                
                if alt_slots:
                    context.candidate_slots_local = [
                        {
                            "staff_id": slot.staff_id,
                            "staff_name": slot.staff_name,
                            "start_time": slot.start_time.isoformat(),
                            "end_time": slot.end_time.isoformat(),
                            "service_id": slot.service_id,
                            "duration_minutes": slot.duration_minutes,
                            "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                        }
                        for slot in alt_slots[:2]
                    ]
                    
                    alt_times = [slot_data["formatted_time"] for slot_data in context.candidate_slots_local]
                    times_text = " or ".join(alt_times)
                    
                    original_formatted = self.timezone_handler.format_datetime_local(
                        original_datetime, context.timezone
                    )
                    
                    return {
                        "response": f"I don't have availability at {original_formatted}, but I do have {times_text}. Would either of those work?",
                        "next_state": MeetingState.AWAIT_CONFIRM,
                        "handled": True
                    }
            
            return {
                "response": "That time isn't available, but I have other options. What other days might work for you?",
                "next_state": MeetingState.COLLECT_DAY,
                "handled": True
            }
            
        except Exception as e:
            logger.error(f"Error suggesting alternatives: {e}")
            return {"response": "Let me find some alternative times for you.", "handled": False}

    async def handle_case_9_holiday_no_slots(self, context: MeetingContext,
                                           analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 9: Holiday/no slots available
        Example: When user requests a holiday or when no slots are available
        """
        try:
            slots = analysis.get("extracted_slots", {})

            if "parsed_date" in slots:
                date_info = slots["parsed_date"]
                requested_date = datetime.fromisoformat(date_info["date"])

                # Check if it's a weekend
                if requested_date.weekday() >= 5:  # Saturday or Sunday
                    next_monday = requested_date + timedelta(days=(7 - requested_date.weekday()))
                    next_monday_formatted = next_monday.strftime("%A, %B %d")

                    return {
                        "response": f"{date_info['formatted']} is a weekend when we're closed. How about {next_monday_formatted}?",
                        "next_state": MeetingState.COLLECT_TIME,
                        "handled": True
                    }

                # Check if no slots available (simulate holiday check)
                if self.zoho_service:
                    available_slots = await self.zoho_service.get_available_slots_for_date(
                        target_date=requested_date,
                        service_type="lead_meeting"
                    )

                    if not available_slots:
                        # Find next available day
                        next_available = await self._find_next_available_day(requested_date, context.timezone)
                        if next_available:
                            return {
                                "response": f"{date_info['formatted']} isn't available. I do have availability on {next_available}. Would that work?",
                                "next_state": MeetingState.COLLECT_TIME,
                                "handled": True
                            }

            return {
                "response": "That day isn't available. Let me find the next available day for you.",
                "next_state": MeetingState.COLLECT_DAY,
                "handled": True
            }

        except Exception as e:
            logger.error(f"Error handling case 9 (holiday/no slots): {e}")
            return {"response": "Let me check our availability and find a good time.", "handled": False}

    async def handle_case_10_reschedule(self, context: MeetingContext,
                                      analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 10: Reschedule existing meeting
        Example: "I need to reschedule our meeting" or "Can we move the meeting?"
        """
        try:
            # Check if there's an existing booking
            if context.booking_id or context.zoho_booking_id:
                return {
                    "response": "I can help you reschedule. What day would work better for you?",
                    "next_state": MeetingState.RESCHEDULING,
                    "handled": True
                }
            else:
                # No existing booking found
                return {
                    "response": "I don't see an existing meeting to reschedule. Would you like to schedule a new meeting?",
                    "next_state": MeetingState.COLLECT_DAY,
                    "handled": True
                }

        except Exception as e:
            logger.error(f"Error handling case 10 (reschedule): {e}")
            return {"response": "I can help you reschedule. What day would work better?", "handled": True}

    async def handle_case_11_cancel(self, context: MeetingContext,
                                  analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Case 11: Cancel existing meeting
        Example: "I need to cancel our meeting" or "Cancel my appointment"
        """
        try:
            # Check if there's an existing booking
            if context.booking_id or context.zoho_booking_id:
                # Confirm cancellation
                return {
                    "response": "I can cancel your meeting for you. Are you sure you'd like to proceed with the cancellation?",
                    "next_state": MeetingState.CANCELING,
                    "handled": True
                }
            else:
                # No existing booking found
                return {
                    "response": "I don't see an existing meeting to cancel. Is there anything else I can help you with?",
                    "next_state": MeetingState.IDLE,
                    "handled": True
                }

        except Exception as e:
            logger.error(f"Error handling case 11 (cancel): {e}")
            return {"response": "I can help you cancel your meeting. Are you sure you want to proceed?", "handled": True}

    # Additional helper methods

    async def _handle_any_day_week(self, context: MeetingContext, message: str) -> Dict[str, Any]:
        """Handle 'any day this/next week' requests"""
        try:
            now = datetime.now(self.timezone_handler.get_timezone(context.timezone))

            if "next week" in message:
                # Next week
                days_until_next_monday = 7 - now.weekday()
                start_date = now + timedelta(days=days_until_next_monday)
            else:
                # This week
                days_until_monday = -now.weekday()
                start_date = now + timedelta(days=days_until_monday)

            end_date = start_date + timedelta(days=4)  # Monday to Friday

            if self.zoho_service:
                week_slots = await self.zoho_service.get_available_slots_for_date_range(
                    start_date=start_date,
                    end_date=end_date,
                    timezone=context.timezone,
                    service_type="lead_meeting"
                )

                if week_slots:
                    context.candidate_slots_local = [
                        {
                            "staff_id": slot.staff_id,
                            "staff_name": slot.staff_name,
                            "start_time": slot.start_time.isoformat(),
                            "end_time": slot.end_time.isoformat(),
                            "service_id": slot.service_id,
                            "duration_minutes": slot.duration_minutes,
                            "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                        }
                        for slot in week_slots[:3]
                    ]

                    slot_options = [slot_data["formatted_time"] for slot_data in context.candidate_slots_local]
                    options_text = ", ".join(slot_options)

                    week_text = "next week" if "next week" in message else "this week"

                    return {
                        "response": f"Great! For {week_text}, I have these times available: {options_text}. Which one works for you?",
                        "next_state": MeetingState.AWAIT_CONFIRM,
                        "handled": True
                    }

            return {
                "response": "I have several options available. What day of the week would you prefer?",
                "next_state": MeetingState.COLLECT_DAY,
                "handled": True
            }

        except Exception as e:
            logger.error(f"Error handling any day week: {e}")
            return {"response": "Let me check our weekly availability.", "handled": False}

    async def _handle_day_range(self, context: MeetingContext, start_day: str, end_day: str) -> Dict[str, Any]:
        """Handle specific day range like 'Monday through Wednesday'"""
        try:
            # Map day names to numbers
            start_day_num = self.timezone_handler.weekdays.get(start_day.lower())
            end_day_num = self.timezone_handler.weekdays.get(end_day.lower())

            if start_day_num is not None and end_day_num is not None:
                now = datetime.now(self.timezone_handler.get_timezone(context.timezone))

                # Find the next occurrence of start_day
                days_ahead = (start_day_num - now.weekday()) % 7
                if days_ahead == 0 and now.hour >= 17:  # If it's today but after business hours
                    days_ahead = 7

                start_date = now + timedelta(days=days_ahead)

                # Calculate end date
                if end_day_num >= start_day_num:
                    end_date = start_date + timedelta(days=(end_day_num - start_day_num))
                else:
                    # Handle week wrap-around
                    end_date = start_date + timedelta(days=(7 - start_day_num + end_day_num))

                if self.zoho_service:
                    range_slots = await self.zoho_service.get_available_slots_for_date_range(
                        start_date=start_date,
                        end_date=end_date,
                        timezone=context.timezone,
                        service_type="lead_meeting"
                    )

                    if range_slots:
                        context.candidate_slots_local = [
                            {
                                "staff_id": slot.staff_id,
                                "staff_name": slot.staff_name,
                                "start_time": slot.start_time.isoformat(),
                                "end_time": slot.end_time.isoformat(),
                                "service_id": slot.service_id,
                                "duration_minutes": slot.duration_minutes,
                                "formatted_time": self.zoho_service.format_slot_for_display(slot, context.timezone)
                            }
                            for slot in range_slots[:3]
                        ]

                        slot_options = [slot_data["formatted_time"] for slot_data in context.candidate_slots_local]
                        options_text = ", ".join(slot_options)

                        return {
                            "response": f"Perfect! Between {start_day.title()} and {end_day.title()}, I have: {options_text}. Which works best?",
                            "next_state": MeetingState.AWAIT_CONFIRM,
                            "handled": True
                        }

            return {
                "response": f"I'd be happy to find times between {start_day.title()} and {end_day.title()}. What time of day would you prefer?",
                "next_state": MeetingState.COLLECT_TIME,
                "handled": True
            }

        except Exception as e:
            logger.error(f"Error handling day range: {e}")
            return {"response": "Let me check availability for those days.", "handled": False}

    async def _find_next_available_day(self, from_date: datetime, timezone: str) -> Optional[str]:
        """Find the next available day with slots"""
        try:
            if not self.zoho_service:
                return None

            # Check next 14 days
            for days_ahead in range(1, 15):
                check_date = from_date + timedelta(days=days_ahead)

                # Skip weekends
                if check_date.weekday() >= 5:
                    continue

                slots = await self.zoho_service.get_available_slots_for_date(
                    target_date=check_date,
                    service_type="lead_meeting"
                )

                if slots:
                    return check_date.strftime("%A, %B %d")

            return None

        except Exception as e:
            logger.error(f"Error finding next available day: {e}")
            return None
