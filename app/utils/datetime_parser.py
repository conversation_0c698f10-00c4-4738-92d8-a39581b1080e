"""
Enhanced DateTime Parser for Natural Language Processing
Handles various date and time formats for SMS booking system
"""

import re
from datetime import datetime, timedelta, time, date
from typing import Dict, Optional, Tuple, List, Union
import calendar
from dateutil import parser as dateutil_parser
import pytz


class DateTimeParser:
    """Enhanced date time parser for natural language processing"""
    
    def __init__(self, timezone: str = "Australia/Sydney"):
        self.timezone = pytz.timezone(timezone)
        self.now = datetime.now(self.timezone)
        
        # Extended day mappings
        self.day_mappings = {
            "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
            "friday": 4, "saturday": 5, "sunday": 6,
            "mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6,
            "tues": 1, "thurs": 3, "weds": 2
        }
        
        # Time period mappings
        self.time_periods = {
            "morning": (9, 0), "afternoon": (14, 0), "evening": (18, 0),
            "lunch": (12, 0), "lunchtime": (12, 0)
        }
        
        # Relative day mappings
        self.relative_days = {
            "today": 0, "tomorrow": 1, "tmr": 1, "tmrw": 1,
            "yesterday": -1, "day after tomorrow": 2,
            "next week": 7, "this week": 0
        }

    def parse_datetime_from_message(self, message: str) -> Optional[Dict[str, Union[datetime, str, List[str]]]]:
        """
        Parse datetime information from a natural language message
        
        Args:
            message: The user's message containing date/time information
            
        Returns:
            Dict containing parsed datetime, confidence, and alternatives
        """
        try:
            message_lower = message.lower().strip()
            
            # Try multiple parsing strategies
            strategies = [
                self._parse_specific_datetime,
                self._parse_relative_datetime,
                self._parse_day_and_time,
                self._parse_time_only,
                self._parse_day_only,
                self._parse_date_formats
            ]
            
            results = []
            for strategy in strategies:
                result = strategy(message_lower)
                if result:
                    results.append(result)
            
            # Return the most confident result
            if results:
                best_result = max(results, key=lambda x: x.get('confidence', 0))
                return best_result
                
            return None
            
        except Exception as e:
            print(f"Error parsing datetime: {e}")
            return None

    def _parse_specific_datetime(self, message: str) -> Optional[Dict]:
        """Parse specific datetime patterns like 'Tuesday 3pm' or 'next Tuesday at 3:30pm'"""
        try:
            # Pattern: next/this [day] at [time]
            pattern = r'(next|this)?\s*(monday|tuesday|wednesday|thursday|friday|saturday|sunday|mon|tue|wed|thu|fri|sat|sun)\s*(?:at)?\s*(\d{1,2}(?::\d{2})?\s*(?:am|pm))'
            match = re.search(pattern, message)
            
            if match:
                relative = match.group(1) or "this"
                day_name = match.group(2)
                time_str = match.group(3)
                
                # Get target date
                target_date = self._get_target_date_for_day(day_name, relative)
                if not target_date:
                    return None
                
                # Parse time
                parsed_time = self._parse_time_string(time_str)
                if not parsed_time:
                    return None
                
                # Combine date and time
                target_datetime = datetime.combine(target_date, parsed_time)
                target_datetime = self.timezone.localize(target_datetime)
                
                return {
                    'datetime': target_datetime,
                    'confidence': 0.9,
                    'source': f"{relative} {day_name} at {time_str}",
                    'alternatives': []
                }
                
        except Exception as e:
            print(f"Error in specific datetime parsing: {e}")
        
        return None

    def _parse_relative_datetime(self, message: str) -> Optional[Dict]:
        """Parse relative datetime like 'tomorrow at 3pm' or 'today 2:30pm'"""
        try:
            # Pattern: tomorrow/today/etc at time
            pattern = r'(tomorrow|today|tmr|tmrw|yesterday)\s*(?:at)?\s*(\d{1,2}(?::\d{2})?\s*(?:am|pm))'
            match = re.search(pattern, message)
            
            if match:
                relative_day = match.group(1)
                time_str = match.group(2)
                
                # Get target date
                days_offset = self.relative_days.get(relative_day, 0)
                target_date = self.now.date() + timedelta(days=days_offset)
                
                # Parse time
                parsed_time = self._parse_time_string(time_str)
                if not parsed_time:
                    return None
                
                # Combine
                target_datetime = datetime.combine(target_date, parsed_time)
                target_datetime = self.timezone.localize(target_datetime)
                
                return {
                    'datetime': target_datetime,
                    'confidence': 0.85,
                    'source': f"{relative_day} at {time_str}",
                    'alternatives': []
                }
                
        except Exception as e:
            print(f"Error in relative datetime parsing: {e}")
        
        return None

    def _parse_day_and_time(self, message: str) -> Optional[Dict]:
        """Parse day and time separately from message"""
        try:
            # Extract day
            day_name = None
            for day, num in self.day_mappings.items():
                if day in message:
                    day_name = day
                    break
            
            if not day_name:
                return None
            
            # Extract time
            time_patterns = [
                r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 3:30 pm
                r'(\d{1,2})\s*(am|pm)',         # 3 pm
                r'(\d{1,2}):(\d{2})',           # 15:30 (24hr)
                r'(\d{1,2})(?:\s*o\'?clock)?'   # 3 o'clock
            ]
            
            parsed_time = None
            time_source = ""
            
            for pattern in time_patterns:
                match = re.search(pattern, message)
                if match:
                    parsed_time = self._extract_time_from_match(match)
                    time_source = match.group(0)
                    break
            
            if not parsed_time:
                # Try time periods
                for period, (hour, minute) in self.time_periods.items():
                    if period in message:
                        parsed_time = time(hour, minute)
                        time_source = period
                        break
            
            if not parsed_time:
                return None
            
            # Get target date for the day
            target_date = self._get_target_date_for_day(day_name)
            if not target_date:
                return None
            
            # Combine
            target_datetime = datetime.combine(target_date, parsed_time)
            target_datetime = self.timezone.localize(target_datetime)
            
            return {
                'datetime': target_datetime,
                'confidence': 0.8,
                'source': f"{day_name} {time_source}",
                'alternatives': []
            }
            
        except Exception as e:
            print(f"Error in day and time parsing: {e}")
        
        return None

    def _parse_time_only(self, message: str) -> Optional[Dict]:
        """Parse time only (assume today or next available)"""
        try:
            time_patterns = [
                r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 3:30 pm
                r'(\d{1,2})\s*(am|pm)',         # 3 pm
            ]
            
            for pattern in time_patterns:
                match = re.search(pattern, message)
                if match:
                    parsed_time = self._extract_time_from_match(match)
                    if parsed_time:
                        # Use today if time is in future, otherwise tomorrow
                        target_date = self.now.date()
                        if parsed_time <= self.now.time():
                            target_date += timedelta(days=1)
                        
                        target_datetime = datetime.combine(target_date, parsed_time)
                        target_datetime = self.timezone.localize(target_datetime)
                        
                        return {
                            'datetime': target_datetime,
                            'confidence': 0.7,
                            'source': match.group(0),
                            'alternatives': []
                        }
            
        except Exception as e:
            print(f"Error in time only parsing: {e}")
        
        return None

    def _parse_day_only(self, message: str) -> Optional[Dict]:
        """Parse day only (assume business hours)"""
        try:
            for day, num in self.day_mappings.items():
                if day in message:
                    target_date = self._get_target_date_for_day(day)
                    if target_date:
                        # Default to 10 AM for day-only requests
                        default_time = time(10, 0)
                        target_datetime = datetime.combine(target_date, default_time)
                        target_datetime = self.timezone.localize(target_datetime)
                        
                        return {
                            'datetime': target_datetime,
                            'confidence': 0.6,
                            'source': day,
                            'alternatives': [
                                target_datetime.replace(hour=14),  # 2 PM
                                target_datetime.replace(hour=16)   # 4 PM
                            ]
                        }
            
        except Exception as e:
            print(f"Error in day only parsing: {e}")
        
        return None

    def _parse_date_formats(self, message: str) -> Optional[Dict]:
        """Parse various date formats using dateutil"""
        try:
            # Try dateutil parser as fallback
            parsed = dateutil_parser.parse(message, fuzzy=True, default=self.now)
            
            if parsed:
                # Ensure timezone
                if parsed.tzinfo is None:
                    parsed = self.timezone.localize(parsed)
                
                return {
                    'datetime': parsed,
                    'confidence': 0.5,
                    'source': 'dateutil_parser',
                    'alternatives': []
                }
                
        except Exception as e:
            print(f"Error in date format parsing: {e}")
        
        return None

    def _parse_time_string(self, time_str: str) -> Optional[time]:
        """Parse time string into time object"""
        try:
            # Remove extra spaces
            time_str = re.sub(r'\s+', ' ', time_str.strip())
            
            # Pattern matching
            patterns = [
                r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 3:30 pm
                r'(\d{1,2})\s*(am|pm)',         # 3 pm
                r'(\d{1,2}):(\d{2})',           # 15:30
            ]
            
            for pattern in patterns:
                match = re.search(pattern, time_str.lower())
                if match:
                    return self._extract_time_from_match(match)
            
            return None
            
        except Exception as e:
            print(f"Error parsing time string: {e}")
            return None

    def _extract_time_from_match(self, match) -> Optional[time]:
        """Extract time object from regex match"""
        try:
            groups = match.groups()
            
            if len(groups) >= 3:  # hour, minute, am/pm
                hour = int(groups[0])
                minute = int(groups[1])
                period = groups[2].lower()
                
                if period == 'pm' and hour != 12:
                    hour += 12
                elif period == 'am' and hour == 12:
                    hour = 0
                    
            elif len(groups) == 2:  # hour, am/pm
                hour = int(groups[0])
                minute = 0
                period = groups[1].lower()
                
                if period == 'pm' and hour != 12:
                    hour += 12
                elif period == 'am' and hour == 12:
                    hour = 0
                    
            else:  # 24-hour format or hour only
                hour = int(groups[0])
                minute = int(groups[1]) if len(groups) > 1 else 0
            
            return time(hour, minute)
            
        except Exception as e:
            print(f"Error extracting time from match: {e}")
            return None

    def _get_target_date_for_day(self, day_name: str, relative: str = "next") -> Optional[date]:
        """Get target date for a given day name"""
        try:
            if day_name not in self.day_mappings:
                return None

            target_weekday = self.day_mappings[day_name]
            current_weekday = self.now.weekday()
            
            if relative == "this":
                # Same week if day hasn't passed, next week if it has
                days_ahead = (target_weekday - current_weekday) % 7
                if days_ahead == 0 and self.now.hour >= 17:  # After business hours
                    days_ahead = 7
                elif days_ahead == 0:  # Same day, earlier time
                    days_ahead = 0  # Allow same day if still early
            else:  # "next" or default
                # For "next Tuesday" - find the very next occurrence of that day
                days_ahead = (target_weekday - current_weekday) % 7
                if days_ahead == 0:  # Today is the target day
                    if self.now.hour < 17:  # If it's still business hours today
                        days_ahead = 0  # Use today
                    else:
                        days_ahead = 7  # Use next week
                # If days_ahead > 0, it means the day is later this week
                # This is correct for "next Tuesday" meaning the coming Tuesday
            
            return self.now.date() + timedelta(days=days_ahead)
            
        except Exception as e:
            print(f"Error getting target date: {e}")
            return None

    def get_day_from_date(self, target_date: Union[date, datetime, str]) -> Optional[str]:
        """Get day name from date"""
        try:
            if isinstance(target_date, str):
                target_date = dateutil_parser.parse(target_date).date()
            elif isinstance(target_date, datetime):
                target_date = target_date.date()
            
            weekday = target_date.weekday()
            day_names = ["monday", "tuesday", "wednesday", "thursday", 
                        "friday", "saturday", "sunday"]
            
            return day_names[weekday]
            
        except Exception as e:
            print(f"Error getting day from date: {e}")
            return None

    def get_date_from_day(self, day_name: str, relative: str = "next") -> Optional[date]:
        """Get date from day name"""
        try:
            return self._get_target_date_for_day(day_name.lower(), relative)
            
        except Exception as e:
            print(f"Error getting date from day: {e}")
            return None

    def format_datetime_naturally(self, dt: datetime) -> str:
        """Format datetime in natural language"""
        try:
            # Ensure timezone
            if dt.tzinfo is None:
                dt = self.timezone.localize(dt)
            elif dt.tzinfo != self.timezone:
                dt = dt.astimezone(self.timezone)
            
            # Get relative day description
            days_diff = (dt.date() - self.now.date()).days
            
            if days_diff == 0:
                day_desc = "today"
            elif days_diff == 1:
                day_desc = "tomorrow"
            elif days_diff == -1:
                day_desc = "yesterday"
            elif 0 < days_diff <= 7:
                day_desc = dt.strftime("%A")  # Monday, Tuesday, etc.
            else:
                day_desc = dt.strftime("%A, %B %d")  # Monday, September 5
            
            time_desc = dt.strftime("%I:%M %p").lstrip('0').lower()
            
            return f"{day_desc} at {time_desc}"
            
        except Exception as e:
            print(f"Error formatting datetime: {e}")
            return str(dt)

    def get_business_hours_alternatives(self, target_date: date) -> List[datetime]:
        """Get business hour alternatives for a given date"""
        try:
            alternatives = []
            business_hours = [9, 10, 11, 14, 15, 16, 17]  # 9am-5pm
            
            for hour in business_hours:
                dt = datetime.combine(target_date, time(hour, 0))
                dt = self.timezone.localize(dt)
                alternatives.append(dt)
            
            return alternatives
            
        except Exception as e:
            print(f"Error getting business hours: {e}")
            return []

    def is_business_hours(self, dt: datetime) -> bool:
        """Check if datetime is within business hours"""
        try:
            # Ensure timezone
            if dt.tzinfo is None:
                dt = self.timezone.localize(dt)
            elif dt.tzinfo != self.timezone:
                dt = dt.astimezone(self.timezone)
            
            # Check weekday (Monday = 0, Sunday = 6)
            if dt.weekday() >= 5:  # Saturday or Sunday
                return False
            
            # Check time (9 AM to 6 PM)
            return 9 <= dt.hour < 18
            
        except Exception as e:
            print(f"Error checking business hours: {e}")
            return False


# Convenience functions for the SMS assistant
def parse_datetime_from_message(message: str) -> Optional[Dict]:
    """Parse datetime from message using default settings"""
    parser = DateTimeParser()
    return parser.parse_datetime_from_message(message)

def get_day_from_date(target_date: Union[date, datetime, str]) -> Optional[str]:
    """Get day name from date"""
    parser = DateTimeParser()
    return parser.get_day_from_date(target_date)

def get_date_from_day(day_name: str, relative: str = "next") -> Optional[date]:
    """Get date from day name"""
    parser = DateTimeParser()
    return parser.get_date_from_day(day_name, relative)

def format_datetime_naturally(dt: datetime) -> str:
    """Format datetime naturally"""
    parser = DateTimeParser()
    return parser.format_datetime_naturally(dt)
