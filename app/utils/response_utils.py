"""
Response utilities for standardized API responses
"""

from typing import Any, Dict, List, Optional, Union
from app.core.app_rules import (
    ApiResponse,
    success_response,
    error_response,
    paginated_response
)
from fastapi.encoders import jsonable_encoder

def create_success_response(
    data: Union[Dict[str, Any], List[Any], str, int, float, bool],
    title: str = "Success",
    description: str = "Operation completed successfully"
) -> ApiResponse:
    """
    Create a standardized success response
    
    Args:
        data: The data to include in the response
        title: Optional title for the message
        description: Optional description for the message
        
    Returns:
        ApiResponse: Standardized success response
    """
    return success_response(
        details=jsonable_encoder(data),
        title=title,
        description=description
    )

def create_error_response(
    error_code: int,
    title: str,
    description: str,
    http_success: bool = False
) -> ApiResponse:
    """
    Create a standardized error response
    
    Args:
        error_code: Numeric error code
        title: Error title
        description: Error description
        http_success: Whether the HTTP request was successful
        
    Returns:
        ApiResponse: Standardized error response
    """
    return error_response(
        error_code=error_code,
        title=title,
        description=description,
        http_success=http_success
    )

def create_paginated_response(
    data: List[Any],
    current_page: int,
    total_pages: int,
    items_per_page: int,
    total_items: int,
    title: str = "Success",
    description: str = "Paginated data retrieved successfully"
) -> ApiResponse:
    """
    Create a standardized paginated response
    
    Args:
        data: List of items to paginate
        current_page: Current page number
        total_pages: Total number of pages
        items_per_page: Number of items per page
        total_items: Total number of items
        title: Optional title for the message
        description: Optional description for the message
        
    Returns:
        ApiResponse: Standardized paginated response
    """
    return paginated_response(
        details=jsonable_encoder(data),
        current_page=current_page,
        total_pages=total_pages,
        items_per_page=items_per_page,
        total_items=total_items,
        title=title,
        description=description
    )

def create_message_response(
    title: str,
    description: str,
    data: Optional[Dict[str, Any]] = None
) -> ApiResponse:
    """
    Create a standardized message response
    
    Args:
        title: Message title
        description: Message description
        data: Optional additional data
        
    Returns:
        ApiResponse: Standardized message response
    """
    return success_response(
        details=jsonable_encoder(data or {}),
        title=title,
        description=description
    ) 