"""
SMS Scheduler utility for scheduling SMS messages using Celery
"""

from datetime import datetime, timedelta
from typing import Optional
import structlog
from app.tasks.sms_tasks import send_scheduled_sms

logger = structlog.get_logger(__name__)


def schedule_sms(lead_id: str, message: str, eta: datetime) -> str:
    """
    Schedule an SMS message to be sent at a specific datetime

    Args:
        lead_id (str): The ID of the lead to send SMS to
        message (str): The message content to send
        eta (datetime): The datetime when the message should be sent

    Returns:
        str: Task ID of the scheduled task
    """
    try:
        # Check if follow-up suppression is enabled via environment variable
        import os
        followup_suppression_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"

        if followup_suppression_enabled:
            logger.info(
                f"SMS scheduling suppressed for lead {lead_id}",
                reason="WORKFLOW_ANDY_NO_FOLLOWUPS enabled",
                scheduled_for=eta.isoformat(),
                suppressed=True
            )
            # Return a mock task ID to maintain compatibility
            return f"suppressed_task_{lead_id}_{eta.strftime('%H%M%S')}"

        # Schedule the task using apply_async with eta parameter
        result = send_scheduled_sms.apply_async(
            args=[lead_id, message],
            eta=eta
        )
        
        logger.info("SMS scheduled successfully", 
                   lead_id=lead_id,
                   message=message,
                   scheduled_for=eta.isoformat(),
                   task_id=result.id)
        
        print(f"✅ SMS scheduled for lead {lead_id} at {eta.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📋 Task ID: {result.id}")
        
        return result.id
        
    except Exception as e:
        logger.error("Error scheduling SMS", 
                    lead_id=lead_id,
                    error=str(e))
        raise


def schedule_test_sms() -> str:
    """
    Example usage: Schedule a test SMS message for 2 minutes from now
    
    Returns:
        str: Task ID of the scheduled test task
    """
    # Calculate eta as 2 minutes from now
    eta = datetime.now() + timedelta(minutes=2)
    
    # Schedule test message
    test_lead_id = "test_lead_123"
    test_message = "Hello! This is a test scheduled SMS message from Andy."
    
    print(f"🧪 Scheduling test SMS...")
    print(f"📅 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏰ Scheduled for: {eta.strftime('%Y-%m-%d %H:%M:%S')}")
    
    task_id = schedule_sms(test_lead_id, test_message, eta)
    
    print(f"🎯 Test SMS scheduled! Check terminal in 2 minutes for execution.")
    
    return task_id


if __name__ == "__main__":
    # Run the test scheduling when this file is executed directly
    schedule_test_sms()
