"""
Forgot Password Service
Handles OTP generation, verification, and password reset functionality
"""

import secrets
import random
from datetime import datetime, timedelta, timezone
from typing import Op<PERSON>, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from passlib.context import CryptContext

from app.models.user import User
from app.models.otp import OTP
from app.services.real_email_service import real_email_service
from app.core.logging import logger


class ForgotPasswordService:
    """Service for handling forgot password functionality"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.otp_expiry_minutes = 5  # OTP expires in 5 minutes
    
    def _generate_otp(self) -> str:
        """Generate a 6-digit OTP"""
        return str(random.randint(100000, 999999))
    
    def _generate_reset_code(self) -> str:
        """Generate a secure reset code (24 bytes = 32 chars when base64 encoded)"""
        return secrets.token_urlsafe(24)
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return self.pwd_context.hash(password)
    
    async def initiate_password_reset(
        self, 
        email: str, 
        db: AsyncSession
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Initiate password reset by sending OTP to email
        
        Args:
            email: User's email address
            db: Database session
            
        Returns:
            Tuple[bool, str, Optional[str]]: (success, message, user_id)
        """
        try:
            # Find user by email
            stmt = select(User).where(
                and_(
                    User.email == email,
                    User.is_active == True,
                    User.is_deleted == False
                )
            )
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                return False, "User not found with this email address", None
            
            # Generate OTP
            otp_code = self._generate_otp()
            expires_at = datetime.now(timezone.utc) + timedelta(minutes=self.otp_expiry_minutes)
            
            # Check if OTP already exists for this user
            existing_otp_stmt = select(OTP).where(
                and_(
                    OTP.user_id == user.id,
                    OTP.is_used == False
                )
            )
            existing_otp_result = await db.execute(existing_otp_stmt)
            existing_otp = existing_otp_result.scalar_one_or_none()
            
            if existing_otp:
                # Update existing OTP
                existing_otp.otp_code = otp_code
                existing_otp.expires_at = expires_at
                existing_otp.updated_at = datetime.now(timezone.utc)
                logger.info(f"Updated existing OTP for user {user.id}")
            else:
                # Create new OTP record
                new_otp = OTP(
                    user_id=user.id,
                    otp_code=otp_code,
                    email=email,
                    expires_at=expires_at
                )
                db.add(new_otp)
                logger.info(f"Created new OTP for user {user.id}")
            
            # Commit to database
            await db.commit()
            
            # Send OTP email using real email service
            user_name = f"{user.first_name} {user.last_name}".strip() if user.first_name or user.last_name else "User"
            email_sent = await real_email_service.send_otp_email(email, otp_code, user_name)

            if not email_sent:
                logger.error(f"Failed to send OTP email to {email}")
                return False, "Failed to send OTP email", None
            
            return True, "OTP sent successfully to your email", str(user.id)
            
        except Exception as e:
            logger.error(f"Error initiating password reset for {email}: {str(e)}")
            await db.rollback()
            return False, "An error occurred while processing your request", None
    
    async def verify_otp(
        self, 
        user_id: str, 
        otp_code: str, 
        db: AsyncSession
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Verify OTP and return reset code
        
        Args:
            user_id: User ID
            otp_code: 4-digit OTP code
            db: Database session
            
        Returns:
            Tuple[bool, str, Optional[str]]: (success, message, reset_code)
        """
        try:
            # Find valid OTP
            stmt = select(OTP).where(
                and_(
                    OTP.user_id == user_id,
                    OTP.otp_code == otp_code,
                    OTP.is_used == False,
                    OTP.expires_at > datetime.now(timezone.utc)
                )
            )
            result = await db.execute(stmt)
            otp_record = result.scalar_one_or_none()
            
            if not otp_record:
                return False, "Invalid or expired OTP", None
            
            # Generate reset code
            reset_code = self._generate_reset_code()
            
            # Mark OTP as used and store reset code
            otp_record.is_used = True
            otp_record.reset_code = reset_code
            otp_record.updated_at = datetime.now(timezone.utc)
            
            await db.commit()
            
            logger.info(f"OTP verified successfully for user {user_id}")
            return True, "OTP verified successfully", reset_code
            
        except Exception as e:
            logger.error(f"Error verifying OTP for user {user_id}: {str(e)}")
            await db.rollback()
            return False, "An error occurred while verifying OTP", None
    
    async def reset_password(
        self, 
        reset_code: str, 
        user_id: str, 
        new_password: str, 
        db: AsyncSession
    ) -> Tuple[bool, str]:
        """
        Reset user password using reset code
        
        Args:
            reset_code: Reset code from OTP verification
            user_id: User ID
            new_password: New password
            db: Database session
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Find valid reset code
            stmt = select(OTP).where(
                and_(
                    OTP.user_id == user_id,
                    OTP.reset_code == reset_code,
                    OTP.is_used == True,  # OTP should be used (verified)
                    OTP.expires_at > datetime.now(timezone.utc) - timedelta(hours=1)  # Allow 1 hour for password reset
                )
            )
            result = await db.execute(stmt)
            otp_record = result.scalar_one_or_none()
            
            if not otp_record:
                return False, "Invalid or expired reset code"
            
            # Find user
            user_stmt = select(User).where(User.id == user_id)
            user_result = await db.execute(user_stmt)
            user = user_result.scalar_one_or_none()
            
            if not user:
                return False, "User not found"
            
            # Hash new password
            hashed_password = self._hash_password(new_password)
            
            # Update user password
            user.password_hash = hashed_password
            user.updated_at = datetime.now(timezone.utc)
            
            # Remove the used OTP record
            await db.delete(otp_record)
            
            await db.commit()
            
            logger.info(f"Password reset successfully for user {user_id}")
            return True, "Password reset successfully"
            
        except Exception as e:
            logger.error(f"Error resetting password for user {user_id}: {str(e)}")
            await db.rollback()
            return False, "An error occurred while resetting password"

    async def _send_actual_email(self, email: str, otp_code: str, user: User) -> bool:
        """Send actual email with OTP"""
        try:
            import smtplib
            import ssl
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart

            # Email configuration - using a free SMTP service
            smtp_server = "smtp.gmail.com"
            smtp_port = 587

            # For testing, I'll use a temporary email service
            # You should replace these with your own email credentials
            sender_email = "<EMAIL>"  # Replace with your email
            sender_password = "your-app-password"  # Replace with your app password

            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = "GrowthHive - Password Reset OTP"
            message["From"] = sender_email
            message["To"] = email

            # Create the HTML content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #4CAF50; color: white; padding: 20px; text-align: center; }}
                    .content {{ padding: 20px; background-color: #f9f9f9; }}
                    .otp-code {{
                        font-size: 32px;
                        font-weight: bold;
                        color: #4CAF50;
                        text-align: center;
                        padding: 20px;
                        background-color: white;
                        border: 2px dashed #4CAF50;
                        margin: 20px 0;
                    }}
                    .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Password Reset Request</h1>
                    </div>
                    <div class="content">
                        <p>Hello {user.first_name or 'User'},</p>
                        <p>You have requested to reset your password for your GrowthHive account.</p>
                        <p>Please use the following OTP code:</p>

                        <div class="otp-code">{otp_code}</div>

                        <p><strong>Important:</strong></p>
                        <ul>
                            <li>This OTP is valid for 5 minutes only</li>
                            <li>Do not share this code with anyone</li>
                            <li>If you didn't request this, please ignore this email</li>
                        </ul>
                    </div>
                    <div class="footer">
                        <p>&copy; 2025 GrowthHive. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # Create plain text version
            text_content = f"""
GrowthHive Password Reset

Hello {user.first_name or 'User'},

You have requested to reset your password for your GrowthHive account.

Your OTP code is: {otp_code}

This code is valid for 5 minutes only.
Do not share this code with anyone.
If you didn't request this, please ignore this email.

Best regards,
GrowthHive Team
            """

            # Create MIMEText objects
            text_part = MIMEText(text_content, "plain")
            html_part = MIMEText(html_content, "html")

            # Add parts to message
            message.attach(text_part)
            message.attach(html_part)

            # Log the attempt
            logger.info(f"[EMAIL] Attempting to send OTP {otp_code} to {email}")

            # For development without actual SMTP credentials, use a mock service
            if sender_email == "<EMAIL>":
                return await self._mock_email_send(email, otp_code)

            # Try to send actual email
            try:
                context = ssl.create_default_context()
                with smtplib.SMTP(smtp_server, smtp_port) as server:
                    server.starttls(context=context)
                    server.login(sender_email, sender_password)
                    server.sendmail(sender_email, email, message.as_string())

                logger.info(f"[EMAIL] Successfully sent OTP to {email}")
                return True

            except Exception as smtp_error:
                logger.error(f"[EMAIL] SMTP error: {smtp_error}")
                return await self._mock_email_send(email, otp_code)

        except Exception as e:
            logger.error(f"[EMAIL] Error sending email: {e}")
            return await self._mock_email_send(email, otp_code)

    async def _mock_email_send(self, email: str, otp_code: str) -> bool:
        """Mock email sending for development/testing"""
        try:
            logger.info("=" * 80)
            logger.info("📧 EMAIL SENT (MOCK MODE)")
            logger.info("=" * 80)
            logger.info(f"TO: {email}")
            logger.info(f"SUBJECT: GrowthHive - Password Reset OTP")
            logger.info(f"OTP CODE: {otp_code}")
            logger.info("")
            logger.info("EMAIL CONTENT:")
            logger.info(f"Your password reset OTP is: {otp_code}")
            logger.info("This code expires in 5 minutes.")
            logger.info("")

            if "mailinator.com" in email.lower():
                mailbox = email.split("@")[0]
                logger.info("📬 MAILINATOR TESTING:")
                logger.info(f"1. Go to https://www.mailinator.com/")
                logger.info(f"2. Enter mailbox: {mailbox}")
                logger.info(f"3. Look for OTP: {otp_code}")
                logger.info("")

            logger.info("To enable actual email sending:")
            logger.info("1. Get Gmail app password: https://support.google.com/accounts/answer/185833")
            logger.info("2. Update sender_email and sender_password in the code")
            logger.info("3. Or configure SMTP settings in environment variables")
            logger.info("=" * 80)

            return True

        except Exception as e:
            logger.error(f"Mock email error: {e}")
            return False


# Global service instance
forgot_password_service = ForgotPasswordService()
