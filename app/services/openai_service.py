"""
OpenAI Service
Provides OpenAI API integration for the Coochie Hydrogreen workflow.
Handles text completion, chat completion, and conversation generation.
"""

import os
import asyncio
from typing import Dict, Any, Optional, List
import structlog
import openai
from openai import Async<PERSON>penAI

from app.core.logging import logger


class OpenAIService:
    """
    Service for OpenAI API integration with async support
    """
    
    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        # Initialize async client
        self.client = AsyncOpenAI(api_key=self.api_key)
        
        # Default settings
        self.default_model = "gpt-4"
        self.default_temperature = 0.3
        self.default_max_tokens = 500
        
        logger.info("OpenAI service initialized", model=self.default_model)
    
    async def get_completion(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_message: Optional[str] = None
    ) -> str:
        """
        Get completion from OpenAI using chat completion API
        
        Args:
            prompt: User prompt
            model: Model to use (defaults to gpt-4)
            temperature: Sampling temperature (0-2)
            max_tokens: Maximum tokens in response
            system_message: Optional system message
            
        Returns:
            Generated text response
        """
        try:
            # Use defaults if not specified
            model = model or self.default_model
            temperature = temperature if temperature is not None else self.default_temperature
            max_tokens = max_tokens or self.default_max_tokens
            
            # Build messages
            messages = []
            if system_message:
                messages.append({"role": "system", "content": system_message})
            messages.append({"role": "user", "content": prompt})
            
            # Make API call
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # Extract and return content
            content = response.choices[0].message.content
            
            # Log usage (no PII)
            if response.usage:
                logger.debug(
                    "OpenAI completion generated",
                    model=model,
                    prompt_tokens=response.usage.prompt_tokens,
                    completion_tokens=response.usage.completion_tokens,
                    total_tokens=response.usage.total_tokens,
                    temperature=temperature
                )
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI completion failed: {str(e)}")
            raise
    
    async def get_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        Get chat completion with message history
        
        Args:
            messages: List of message dicts with 'role' and 'content'
            model: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Returns:
            Generated response
        """
        try:
            model = model or self.default_model
            temperature = temperature if temperature is not None else self.default_temperature
            max_tokens = max_tokens or self.default_max_tokens
            
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            content = response.choices[0].message.content
            
            # Log usage
            if response.usage:
                logger.debug(
                    "OpenAI chat completion generated",
                    model=model,
                    messages_count=len(messages),
                    total_tokens=response.usage.total_tokens
                )
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI chat completion failed: {str(e)}")
            raise
    
    async def analyze_intent(
        self,
        message: str,
        context: str,
        expected_fields: List[str]
    ) -> Dict[str, Any]:
        """
        Analyze user intent and extract structured data
        
        Args:
            message: User message to analyze
            context: Context for the analysis
            expected_fields: List of fields to extract
            
        Returns:
            Dict with extracted fields
        """
        try:
            fields_description = ", ".join(expected_fields)
            
            prompt = f"""
            Analyze this message in the context of {context}:
            Message: "{message}"
            
            Extract the following fields: {fields_description}
            
            Return a JSON object with the extracted information.
            If a field cannot be determined, set it to null.
            """
            
            response = await self.get_completion(
                prompt,
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=300
            )
            
            # Try to parse JSON response
            import json
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON response: {response}")
                # Return empty dict with expected fields
                return {field: None for field in expected_fields}
                
        except Exception as e:
            logger.error(f"Intent analysis failed: {str(e)}")
            return {field: None for field in expected_fields}
    
    async def generate_acknowledgment(
        self,
        user_message: str,
        acknowledgment_phrases: Optional[List[str]] = None
    ) -> str:
        """
        Generate a natural acknowledgment for user message
        
        Args:
            user_message: Message to acknowledge
            acknowledgment_phrases: Optional list of preferred phrases
            
        Returns:
            Natural acknowledgment phrase
        """
        try:
            default_phrases = [
                "Great", "That sounds interesting", "That makes sense",
                "Okay", "Right", "I see", "Perfect", "Understood"
            ]
            
            phrases = acknowledgment_phrases or default_phrases
            phrases_text = ", ".join([f'"{phrase}"' for phrase in phrases])
            
            prompt = f"""
            Generate a brief, natural acknowledgment for this message:
            "{user_message}"
            
            Choose from these phrases or similar: {phrases_text}
            
            Return only the acknowledgment phrase (maximum 4 words).
            No emojis. Keep it conversational and warm.
            """
            
            response = await self.get_completion(
                prompt,
                temperature=0.3,
                max_tokens=20
            )
            
            # Clean and validate response
            acknowledgment = response.strip().strip('"').strip("'")
            
            # Fallback if response is too long or empty
            if not acknowledgment or len(acknowledgment.split()) > 4:
                acknowledgment = phrases[0]  # Use first phrase as fallback
            
            return acknowledgment
            
        except Exception as e:
            logger.error(f"Acknowledgment generation failed: {str(e)}")
            return "Great"  # Safe fallback
    
    async def enhance_response_with_workflow(
        self,
        user_message: str,
        workflow_sentence: str,
        conversation_stage: str,
        conversation_style: Dict[str, str]
    ) -> str:
        """
        Enhance a workflow response to be more natural and conversational
        
        Args:
            user_message: What the user said
            workflow_sentence: Exact sentence from workflow to include
            conversation_stage: Current stage of conversation
            conversation_style: Style guidelines
            
        Returns:
            Enhanced natural response
        """
        try:
            style_description = "\n".join([f"- {key}: {value}" for key, value in conversation_style.items()])
            
            prompt = f"""
            You are Andy, a Lead Qualification Specialist with 5+ years experience in the Australian franchise industry.
            
            Conversation Style Guidelines:
            {style_description}
            
            User just said: "{user_message}"
            Current conversation stage: {conversation_stage}
            
            You MUST include this exact sentence in your response: "{workflow_sentence}"
            
            Create a natural response that:
            1. Acknowledges what they shared first
            2. Incorporates the exact workflow sentence naturally
            3. Maintains conversational flow
            4. Follows the style guidelines above
            
            Keep it concise and natural. No emojis.
            """
            
            response = await self.get_completion(
                prompt,
                temperature=0.3,
                max_tokens=250
            )
            
            # Remove any emojis that might have slipped through
            import re
            emoji_pattern = re.compile(
                "["
                "\U0001F600-\U0001F64F"  # emoticons
                "\U0001F300-\U0001F5FF"  # symbols & pictographs
                "\U0001F680-\U0001F6FF"  # transport & map symbols
                "\U0001F1E0-\U0001F1FF"  # flags (iOS)
                "\U00002702-\U000027B0"
                "\U000024C2-\U0001F251"
                "]+",
                flags=re.UNICODE
            )
            response = emoji_pattern.sub('', response).strip()
            
            return response
            
        except Exception as e:
            logger.error(f"Response enhancement failed: {str(e)}")
            # Fallback to workflow sentence
            return workflow_sentence
    
    def validate_api_key(self) -> bool:
        """
        Validate that the OpenAI API key is working
        
        Returns:
            True if API key is valid, False otherwise
        """
        try:
            # Make a simple test call
            import asyncio
            
            async def test_call():
                return await self.get_completion(
                    "Test",
                    max_tokens=5,
                    temperature=0
                )
            
            # Run the test
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, create a task
                task = asyncio.create_task(test_call())
                return True  # Assume it will work
            else:
                # Run in new event loop
                asyncio.run(test_call())
                return True
                
        except Exception as e:
            logger.error(f"OpenAI API key validation failed: {str(e)}")
            return False


# Global service instance
openai_service = OpenAIService()


# Export main components
__all__ = [
    "OpenAIService",
    "openai_service"
]
