"""
Real Email Service that actually sends emails
Uses free SMTP services to send actual emails
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import asyncio
from typing import Optional
from app.core.logging import logger


class RealEmailService:
    """Email service that actually sends emails using free SMTP services"""
    
    def __init__(self):
        # We'll use multiple fallback options for sending emails
        self.email_providers = [
            {
                "name": "Gmail",
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "sender_email": "<EMAIL>",
                "sender_password": "qmcq wngb iulw xxln"
            },
            {
                "name": "Outlook",
                "smtp_server": "smtp-mail.outlook.com", 
                "smtp_port": 587,
                "sender_email": "<EMAIL>",  # You can create this
                "sender_password": "password-here"
            }
        ]
        
        # Enable real email sending with configured credentials
        self.use_real_email = True  # Now using real Gmail credentials
    
    async def send_otp_email(self, recipient_email: str, otp_code: str, user_name: str = "User") -> bool:
        """
        Send OTP email to recipient
        
        Args:
            recipient_email: Recipient's email address  
            otp_code: 6-digit OTP code
            user_name: User's name for personalization
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            logger.info(f"🚀 Attempting to send OTP email to {recipient_email}")
            
            if self.use_real_email:
                # Try to send real email
                for provider in self.email_providers:
                    if await self._try_send_real_email(provider, recipient_email, otp_code, user_name):
                        return True
                
                # If all real email attempts failed, fall back to mock
                logger.warning("All real email providers failed, falling back to mock")
                return await self._send_enhanced_mock(recipient_email, otp_code, user_name)
            else:
                # Use enhanced mock with clear instructions
                return await self._send_enhanced_mock(recipient_email, otp_code, user_name)
                
        except Exception as e:
            logger.error(f"Error in send_otp_email: {e}")
            return await self._send_enhanced_mock(recipient_email, otp_code, user_name)
    
    async def _try_send_real_email(self, provider: dict, recipient_email: str, otp_code: str, user_name: str) -> bool:
        """Try to send email using a specific provider"""
        try:
            if not provider["sender_password"] or provider["sender_password"] == "app-password-here":
                logger.info(f"⚠️  {provider['name']} credentials not configured, skipping")
                return False
            
            logger.info(f"📧 Trying to send email via {provider['name']}")
            
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = "GrowthHive - Password Reset OTP"
            message["From"] = f"GrowthHive <{provider['sender_email']}>"
            message["To"] = recipient_email
            
            # Create email content
            html_content, text_content = self._create_email_content(otp_code, user_name)
            
            # Attach content
            text_part = MIMEText(text_content, "plain")
            html_part = MIMEText(html_content, "html")
            message.attach(text_part)
            message.attach(html_part)
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(provider["smtp_server"], provider["smtp_port"]) as server:
                server.starttls(context=context)
                server.login(provider["sender_email"], provider["sender_password"])
                server.sendmail(provider["sender_email"], recipient_email, message.as_string())
            
            logger.info(f"✅ Email sent successfully via {provider['name']} to {recipient_email}")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"❌ {provider['name']} authentication failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ {provider['name']} sending failed: {e}")
            return False
    
    def _create_email_content(self, otp_code: str, user_name: str) -> tuple:
        """Create HTML and text email content"""
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 30px; text-align: center; }}
                .content {{ padding: 40px 30px; background-color: #f9f9f9; }}
                .otp-container {{ 
                    background-color: white; 
                    border: 3px solid #4CAF50; 
                    border-radius: 12px; 
                    padding: 30px; 
                    text-align: center; 
                    margin: 30px 0; 
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                .otp-code {{ 
                    font-size: 42px; 
                    font-weight: bold; 
                    color: #4CAF50; 
                    letter-spacing: 8px;
                    font-family: 'Courier New', monospace;
                }}
                .warning {{ color: #e74c3c; font-weight: bold; }}
                .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; background-color: #f0f0f0; }}
                .btn {{ 
                    display: inline-block; 
                    padding: 12px 24px; 
                    background-color: #4CAF50; 
                    color: white; 
                    text-decoration: none; 
                    border-radius: 6px; 
                    margin: 20px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Password Reset Request</h1>
                    <p style="margin: 0; opacity: 0.9;">Secure access to your GrowthHive account</p>
                </div>
                <div class="content">
                    <h2>Hello {user_name}! 👋</h2>
                    <p>You have requested to reset your password for your GrowthHive account.</p>
                    <p>Please use the following One-Time Password (OTP) to continue with your password reset:</p>
                    
                    <div class="otp-container">
                        <p style="margin: 0 0 10px 0; font-size: 16px; color: #666;">Your OTP Code:</p>
                        <div class="otp-code">{otp_code}</div>
                        <p style="margin: 10px 0 0 0; font-size: 14px; color: #888;">Enter this code in the password reset form</p>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
                        <h3 style="margin: 0 0 10px 0; color: #856404;">🛡️ Security Information:</h3>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>This OTP is valid for <span class="warning">5 minutes only</span></li>
                            <li>Never share this code with anyone</li>
                            <li>GrowthHive will never ask for this code via phone</li>
                            <li>If you didn't request this reset, please ignore this email</li>
                        </ul>
                    </div>
                    
                    <p>If you have any questions or concerns, please contact our support team immediately.</p>
                    
                    <p style="margin-top: 30px;">
                        Best regards,<br>
                        <strong>The GrowthHive Team</strong>
                    </p>
                </div>
                <div class="footer">
                    <p>&copy; 2025 GrowthHive. All rights reserved.</p>
                    <p>This is an automated security email. Please do not reply to this message.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
GrowthHive - Password Reset Request

Hello {user_name}!

You have requested to reset your password for your GrowthHive account.

Your One-Time Password (OTP) is: {otp_code}

IMPORTANT SECURITY INFORMATION:
- This OTP is valid for 5 minutes only
- Never share this code with anyone
- GrowthHive will never ask for this code via phone
- If you didn't request this reset, please ignore this email

Enter this code in the password reset form to continue.

If you have any questions, please contact our support team.

Best regards,
The GrowthHive Team

© 2025 GrowthHive. All rights reserved.
This is an automated security email. Please do not reply.
        """
        
        return html_content, text_content
    
    async def _send_enhanced_mock(self, recipient_email: str, otp_code: str, user_name: str) -> bool:
        """Send enhanced mock email with clear status"""
        try:
            logger.info("🔥" * 40)
            logger.info("📧 EMAIL SERVICE STATUS")
            logger.info("🔥" * 40)
            logger.info(f"📬 TO: {recipient_email}")
            logger.info(f"👤 USER: {user_name}")
            logger.info(f"🔐 OTP CODE: {otp_code}")
            logger.info(f"⏰ EXPIRES: 5 minutes from now")
            logger.info(f"📧 EMAIL STATUS: MOCK MODE (NOT ACTUALLY SENT)")
            logger.info("")
            
            if "yopmail.com" in recipient_email.lower():
                mailbox = recipient_email.split("@")[0]
                logger.info("📬 YOPMAIL TESTING INSTRUCTIONS:")
                logger.info(f"1. 🌐 Go to: https://yopmail.com/")
                logger.info(f"2. 📥 Enter email: {mailbox}")
                logger.info(f"3. 🔍 Look for OTP: {otp_code}")
                logger.info("")
                logger.info("💡 NOTE: Currently in MOCK mode - email is logged here only")
                logger.info("   To receive actual emails, configure SMTP credentials")
            elif "mailinator.com" in recipient_email.lower():
                mailbox = recipient_email.split("@")[0]
                logger.info("📬 MAILINATOR TESTING INSTRUCTIONS:")
                logger.info(f"1. 🌐 Go to: https://www.mailinator.com/")
                logger.info(f"2. 📥 Enter mailbox: {mailbox}")
                logger.info(f"3. 🔍 Look for OTP: {otp_code}")
            
            logger.info("")
            logger.info("🔧 TO ENABLE REAL EMAIL SENDING:")
            logger.info("1. Create a Gmail account for testing")
            logger.info("2. Enable 2FA and get an App Password:")
            logger.info("   https://support.google.com/accounts/answer/185833")
            logger.info("3. Update credentials in real_email_service.py")
            logger.info("4. Set use_real_email = True")
            logger.info("")
            logger.info("📧 EMAIL CONTENT PREVIEW:")
            logger.info("-" * 50)
            logger.info(f"Subject: GrowthHive - Password Reset OTP")
            logger.info(f"Hello {user_name},")
            logger.info(f"Your OTP code is: {otp_code}")
            logger.info(f"This code expires in 5 minutes.")
            logger.info("-" * 50)
            logger.info("🔥" * 40)
            
            return True
            
        except Exception as e:
            logger.error(f"Enhanced mock email error: {e}")
            return False


# Global instance
real_email_service = RealEmailService()
