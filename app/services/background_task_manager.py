"""
Background Task Manager Service
Centralized service for managing background tasks with RabbitMQ/Celery
"""

from typing import Optional, Dict, Any, List
from enum import Enum

import structlog
from celery.result import AsyncResult

from app.core.celery_app import celery_app
from app.tasks.document_processing import process_document_task
from app.tasks.docqa_processing import process_docqa_task

logger = structlog.get_logger(__name__)


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    RETRY = "retry"


class TaskType(str, Enum):
    """Task type enumeration"""
    DOCUMENT_PROCESSING = "document_processing"
    DOCQA_PROCESSING = "docqa_processing"


class BackgroundTaskManager:
    """Service for managing background tasks"""
    
    def __init__(self):
        self.celery_app = celery_app
    
    def queue_document_processing(
        self,
        document_id: str,
        file_url: str,
        processing_options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Queue document processing task
        
        Args:
            document_id: Document ID to process
            file_url: S3 URL of the document
            processing_options: Optional processing configuration
            
        Returns:
            Task ID for tracking
        """
        try:
            # Queue the task
            task = process_document_task.delay(
                document_id=document_id,
                file_url=file_url,
                processing_options=processing_options or {}
            )
            
            logger.info("Document processing task queued",
                       document_id=document_id,
                       task_id=task.id,
                       file_url=file_url)
            
            return task.id
            
        except Exception as e:
            logger.error("Failed to queue document processing task",
                        document_id=document_id,
                        error=str(e))
            raise
    
    def queue_docqa_processing(
        self,
        franchisor_id: str,
        brochure_url: str,
        processing_options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Queue DocQA processing task for franchisor brochure
        
        Args:
            franchisor_id: Franchisor ID
            brochure_url: S3 URL of the brochure
            processing_options: Optional processing configuration
            
        Returns:
            Task ID for tracking
        """
        try:
            # Queue the task
            task = process_docqa_task.delay(
                franchisor_id=franchisor_id,
                brochure_url=brochure_url,
                processing_options=processing_options or {}
            )
            
            logger.info("DocQA processing task queued",
                       franchisor_id=franchisor_id,
                       task_id=task.id,
                       brochure_url=brochure_url)
            
            return task.id
            
        except Exception as e:
            logger.error("Failed to queue DocQA processing task",
                        franchisor_id=franchisor_id,
                        error=str(e))
            raise
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get task status and result
        
        Args:
            task_id: Celery task ID
            
        Returns:
            Dict with task status information
        """
        try:
            result = AsyncResult(task_id, app=self.celery_app)
            
            status_info = {
                "task_id": task_id,
                "status": result.status,
                "ready": result.ready(),
                "successful": result.successful() if result.ready() else None,
                "failed": result.failed() if result.ready() else None,
                "result": None,
                "error": None,
                "traceback": None
            }
            
            if result.ready():
                if result.successful():
                    status_info["result"] = result.result
                elif result.failed():
                    status_info["error"] = str(result.result)
                    status_info["traceback"] = result.traceback
            
            return status_info
            
        except Exception as e:
            logger.error("Failed to get task status",
                        task_id=task_id,
                        error=str(e))
            return {
                "task_id": task_id,
                "status": "UNKNOWN",
                "error": str(e)
            }
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a running task
        
        Args:
            task_id: Celery task ID
            
        Returns:
            True if cancellation was successful
        """
        try:
            self.celery_app.control.revoke(task_id, terminate=True)
            logger.info("Task cancelled", task_id=task_id)
            return True
            
        except Exception as e:
            logger.error("Failed to cancel task",
                        task_id=task_id,
                        error=str(e))
            return False
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        Get list of active tasks
        
        Returns:
            List of active task information
        """
        try:
            inspect = self.celery_app.control.inspect()
            active_tasks = inspect.active()
            
            if not active_tasks:
                return []
            
            # Flatten tasks from all workers
            all_tasks = []
            for worker, tasks in active_tasks.items():
                for task in tasks:
                    task_info = {
                        "task_id": task["id"],
                        "name": task["name"],
                        "worker": worker,
                        "args": task.get("args", []),
                        "kwargs": task.get("kwargs", {}),
                        "time_start": task.get("time_start")
                    }
                    all_tasks.append(task_info)
            
            return all_tasks
            
        except Exception as e:
            logger.error("Failed to get active tasks", error=str(e))
            return []
    
    def get_queue_length(self, queue_name: str = None) -> int:
        """
        Get queue length
        
        Args:
            queue_name: Queue name (default: document processing queue)
            
        Returns:
            Number of tasks in queue
        """
        try:
            from app.core.config.settings import settings
            
            queue_name = queue_name or settings.DOCUMENT_PROCESSING_QUEUE
            inspect = self.celery_app.control.inspect()
            
            # Get reserved tasks (queued but not yet started)
            reserved = inspect.reserved()
            if not reserved:
                return 0
            
            total_queued = 0
            for worker, tasks in reserved.items():
                # Filter by queue if specified
                for task in tasks:
                    if queue_name in task.get("delivery_info", {}).get("routing_key", ""):
                        total_queued += 1
            
            return total_queued
            
        except Exception as e:
            logger.error("Failed to get queue length",
                        queue_name=queue_name,
                        error=str(e))
            return 0


# Singleton instance
_background_task_manager = None


def get_background_task_manager() -> BackgroundTaskManager:
    """Get background task manager instance"""
    global _background_task_manager
    if _background_task_manager is None:
        _background_task_manager = BackgroundTaskManager()
    return _background_task_manager
