"""
SMS Outbound Service - <PERSON><PERSON> sending introductory SMS messages to new leads
Integrates with working_andy_chat logic and Kudosity SMS API
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List
from uuid import UUID
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.services.kudosity_sms_service import (
    get_kudosity_sms_service, 
    SMSMessage, 
    SMSResponse
)
from app.agents.sms_assistant import get_sms_assistant, AndyTemplates
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.models.messaging_rule import MessagingRule
from app.core.database.connection import get_db
from app.core.utils.exception_manager.custom_exceptions import (
    BusinessLogicError,
    ValidationError
)

from app.core.logging import logger


class SMSOutboundService:
    """Service for sending outbound SMS messages to leads"""
    
    def __init__(self):
        """Initialize SMS outbound service"""
        self.kudosity_service = get_kudosity_sms_service()
        self.sms_assistant = get_sms_assistant()
    
    async def send_introductory_sms(
        self, 
        lead_id: str, 
        db: AsyncSession,
        custom_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send introductory SMS message to a lead
        
        Args:
            lead_id: Lead ID to send message to
            db: Database session
            custom_message: Optional custom message (if not provided, uses Andy's template)
            
        Returns:
            Dict containing success status and details
        """
        try:
            # Get lead information
            lead = await self._get_lead_by_id(db, lead_id)
            if not lead:
                return {
                    "success": False,
                    "error": f"Lead not found: {lead_id}",
                    "lead_id": lead_id
                }
            
            # Check if lead has a phone number
            phone_number = lead.mobile or lead.phone
            if not phone_number:
                return {
                    "success": False,
                    "error": "Lead has no phone number",
                    "lead_id": lead_id
                }
            
            # Generate introductory message
            if custom_message:
                message_text = custom_message
            else:
                message_text = await self._generate_intro_message(lead)
            
            # Create SMS message
            sms_message = SMSMessage(
                to=phone_number,
                message=message_text
            )
            
            # 📱 PRINT MESSAGE TO TERMINAL
            print("\n" + "="*60)
            print("📱 SENDING SMS MESSAGE")
            print("="*60)
            print(f"📞 To: {phone_number}")
            print(f"👤 Lead: {lead.first_name} {lead.last_name} (ID: {lead_id})")
            print(f"💬 Message:")
            print(f"   {message_text}")
            print("="*60)

            # Send SMS via Kudosity
            sms_response = await self.kudosity_service.send_sms(sms_message)

            if sms_response.success:
                # Store conversation message in database
                await self._store_outbound_message(
                    db=db,
                    lead_id=lead_id,
                    message=message_text,
                    phone_number=phone_number,
                    message_id=sms_response.message_id
                )
                
                logger.info(
                    f"Introductory SMS sent successfully",
                    lead_id=lead_id,
                    phone_number=phone_number,
                    message_id=sms_response.message_id
                )
                
                return {
                    "success": True,
                    "message_id": sms_response.message_id,
                    "lead_id": lead_id,
                    "phone_number": phone_number,
                    "message": message_text
                }
            else:
                logger.error(
                    f"Failed to send introductory SMS",
                    lead_id=lead_id,
                    phone_number=phone_number,
                    error=sms_response.error
                )
                
                return {
                    "success": False,
                    "error": sms_response.error,
                    "lead_id": lead_id,
                    "phone_number": phone_number
                }
                
        except Exception as e:
            logger.error(
                f"Error sending introductory SMS: {str(e)}",
                lead_id=lead_id,
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e),
                "lead_id": lead_id
            }
    

    
    async def _get_lead_by_id(self, db: AsyncSession, lead_id: str) -> Optional[Lead]:
        """Get lead by ID from database"""
        try:
            query = select(Lead).where(
                and_(
                    Lead.id == UUID(lead_id),
                    Lead.is_active == True,
                    Lead.is_deleted == False
                )
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting lead by ID: {str(e)}")
            return None
    
    async def _generate_intro_message(self, lead: Lead) -> str:
        """Generate introductory message using Andy's templates"""
        try:
            # Use lead's first name if available
            if lead.first_name:
                return AndyTemplates.INTRODUCTION.format(name=lead.first_name)
            else:
                return AndyTemplates.INTRODUCTION_NO_NAME
        except Exception as e:
            logger.error(f"Error generating intro message: {str(e)}")
            return AndyTemplates.INTRODUCTION_NO_NAME
    

    
    async def _store_outbound_message(
        self,
        db: AsyncSession,
        lead_id: str,
        message: str,
        phone_number: str,
        message_id: Optional[str] = None
    ) -> None:
        """Store outbound message in conversation_message table"""
        try:
            # Get franchisor_id from lead
            from app.services.lead_service import LeadService
            lead_service = LeadService()
            lead = await lead_service.get_lead_by_id(lead_id)
            if not lead or not lead.franchisor_id:
                logger.error(f"Lead {lead_id} not found or missing franchisor_id")
                return False
            
            conversation_message = ConversationMessage(
                lead_id=UUID(lead_id),
                franchisor_id=lead.franchisor_id,
                sender="system",
                message=message,
                is_active=True,
                is_deleted=False
            )
            
            db.add(conversation_message)
            await db.commit()
            
            logger.info(
                f"Stored outbound message in database",
                lead_id=lead_id,
                phone_number=phone_number,
                message_id=message_id
            )
            
        except Exception as e:
            logger.error(f"Error storing outbound message: {str(e)}")
            await db.rollback()


# Singleton instance
_sms_outbound_service = None


def get_sms_outbound_service() -> SMSOutboundService:
    """Get singleton instance of SMS outbound service"""
    global _sms_outbound_service
    if _sms_outbound_service is None:
        _sms_outbound_service = SMSOutboundService()
    return _sms_outbound_service
