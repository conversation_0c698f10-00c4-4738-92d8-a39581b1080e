"""
Conversation Message Service
Business logic for conversation message operations with AI agent processing
"""

import uuid
import time
from typing import Optional, List, Dict, Any, Union, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.conversation_message_repository import ConversationMessageRepository
from app.services.lead_service import LeadService
from app.services.franchisor_service import FranchisorService
from app.schemas.conversation_message import (
    ConversationMessageCreateRequest,
    ConversationMessageUpdateRequest,
    ConversationMessageResponse,
    ConversationMessageBulkCreateRequest,
    ConversationParseRequest,
    ConversationParseResponse,
    SenderType
)
from app.core.logging import logger
from app.core.utils.exception_manager.custom_exceptions import (
    DatabaseError,
    ValidationError,
    BusinessLogicError
)


class ConversationMessageService:
    """Service for conversation message operations with AI agent processing"""
    
    def __init__(self, repository: ConversationMessageRepository, lead_service: LeadService, franchisor_service: FranchisorService):
        self.repository = repository
        self.lead_service = lead_service
        self.franchisor_service = franchisor_service
    
    async def create_message(self, message_data: ConversationMessageCreateRequest) -> ConversationMessageResponse:
        """Create a single conversation message"""
        try:
            logger.info(f"Creating conversation message for lead {message_data.lead_id}")
            
            # Create the message
            created_message = await self.repository.create(message_data)
            
            # Convert to response model
            return ConversationMessageResponse.model_validate(created_message)
            
        except Exception as e:
            logger.error(f"Error creating conversation message: {e}")
            raise DatabaseError(f"Failed to create conversation message: {str(e)}")
    
    async def get_message_by_id(self, message_id: Union[str, uuid.UUID]) -> Optional[ConversationMessageResponse]:
        """Get a conversation message by ID"""
        try:
            message = await self.repository.get_by_id(message_id)
            if message:
                return ConversationMessageResponse.model_validate(message)
            return None
            
        except Exception as e:
            logger.error(f"Error getting conversation message {message_id}: {e}")
            raise DatabaseError(f"Failed to get conversation message: {str(e)}")
    
    async def get_messages_by_lead_id(
        self,
        lead_id: Union[str, uuid.UUID],
        skip: int = 0,
        limit: int = 100,
        order_by_created_at: str = "asc"
    ) -> Tuple[List[ConversationMessageResponse], int]:
        """Get conversation messages by lead ID with pagination"""
        try:
            messages, total_count = await self.repository.get_by_lead_id(
                lead_id=lead_id,
                skip=skip,
                limit=limit,
                order_by_created_at=order_by_created_at
            )

            # Get lead information
            lead = await self.lead_service.get_lead_by_id(str(lead_id))
            lead_first_name = lead.first_name if lead else None
            lead_last_name = lead.last_name if lead else None

            # Get unique franchisor IDs from messages
            franchisor_ids = {str(msg.franchisor_id) for msg in messages if msg.franchisor_id}

            # Fetch franchisor information for all unique franchisor IDs
            franchisor_cache = {}
            for franchisor_id in franchisor_ids:
                try:
                    franchisor = await self.franchisor_service.get_franchisor_by_id(franchisor_id)
                    franchisor_cache[franchisor_id] = franchisor.name if franchisor else None
                except Exception as e:
                    logger.warning(f"Failed to fetch franchisor {franchisor_id}: {e}")
                    franchisor_cache[franchisor_id] = None

            # Convert to response models with lead and franchisor information
            message_responses = []
            for message in messages:
                message_dict = message.__dict__.copy()
                message_dict['lead_first_name'] = lead_first_name
                message_dict['lead_last_name'] = lead_last_name

                # Add franchisor name if franchisor_id exists
                if message.franchisor_id:
                    franchisor_id_str = str(message.franchisor_id)
                    message_dict['franchisor_name'] = franchisor_cache.get(franchisor_id_str)
                else:
                    message_dict['franchisor_name'] = None

                message_responses.append(ConversationMessageResponse.model_validate(message_dict))

            return message_responses, total_count

        except Exception as e:
            logger.error(f"Error getting messages for lead {lead_id}: {e}")
            raise DatabaseError(f"Failed to get messages for lead: {str(e)}")
    
    async def get_conversation_between_lead_and_franchisor(
        self,
        lead_id: Union[str, uuid.UUID],
        franchisor_id: Union[str, uuid.UUID],
        skip: int = 0,
        limit: int = 100,
        order_by_created_at: str = "asc"
    ) -> Tuple[List[ConversationMessageResponse], int]:
        """Get conversation messages between a specific lead and franchisor"""
        try:
            messages, total_count = await self.repository.get_conversation_between_lead_and_franchisor(
                lead_id=lead_id,
                franchisor_id=franchisor_id,
                skip=skip,
                limit=limit,
                order_by_created_at=order_by_created_at
            )
            
            # Get lead information
            lead = await self.lead_service.get_lead_by_id(str(lead_id))
            lead_first_name = lead.first_name if lead else None
            lead_last_name = lead.last_name if lead else None

            # Get franchisor information
            franchisor_name = None
            try:
                franchisor = await self.franchisor_service.get_franchisor_by_id(str(franchisor_id))
                franchisor_name = franchisor.name if franchisor else None
            except Exception as e:
                logger.warning(f"Failed to fetch franchisor {franchisor_id}: {e}")

            # Convert to response models with lead and franchisor information
            message_responses = []
            for message in messages:
                message_dict = message.__dict__.copy()
                message_dict['lead_first_name'] = lead_first_name
                message_dict['lead_last_name'] = lead_last_name
                message_dict['franchisor_name'] = franchisor_name
                message_responses.append(ConversationMessageResponse.model_validate(message_dict))

            return message_responses, total_count
            
        except Exception as e:
            logger.error(f"Error getting conversation between lead {lead_id} and franchisor {franchisor_id}: {e}")
            raise DatabaseError(f"Failed to get conversation: {str(e)}")
    
    async def update_message(
        self,
        message_id: Union[str, uuid.UUID],
        update_data: ConversationMessageUpdateRequest
    ) -> Optional[ConversationMessageResponse]:
        """Update a conversation message"""
        try:
            # Get existing message
            existing_message = await self.repository.get_by_id(message_id)
            if not existing_message:
                return None
            
            # Update the message
            updated_message = await self.repository.update(existing_message, update_data)
            
            return ConversationMessageResponse.model_validate(updated_message)
            
        except Exception as e:
            logger.error(f"Error updating conversation message {message_id}: {e}")
            raise DatabaseError(f"Failed to update conversation message: {str(e)}")
    
    async def soft_delete_message(self, message_id: Union[str, uuid.UUID]) -> bool:
        """Soft delete a conversation message"""
        try:
            return await self.repository.soft_delete(message_id)
            
        except Exception as e:
            logger.error(f"Error soft deleting conversation message {message_id}: {e}")
            raise DatabaseError(f"Failed to soft delete conversation message: {str(e)}")
    
    async def create_bulk_messages(
        self,
        bulk_data: ConversationMessageBulkCreateRequest
    ) -> List[ConversationMessageResponse]:
        """Create multiple conversation messages in bulk"""
        try:
            logger.info(f"Creating {len(bulk_data.messages)} conversation messages in bulk")
            
            # Create messages in bulk
            created_messages = await self.repository.create_bulk(bulk_data.messages)
            
            # Convert to response models
            return [
                ConversationMessageResponse.model_validate(message)
                for message in created_messages
            ]
            
        except Exception as e:
            logger.error(f"Error creating bulk conversation messages: {e}")
            raise DatabaseError(f"Failed to create bulk conversation messages: {str(e)}")
    
    async def get_conversation_stats(
        self,
        lead_id: Optional[Union[str, uuid.UUID]] = None
    ) -> Dict[str, Any]:
        """Get conversation statistics"""
        try:
            return await self.repository.get_conversation_stats(lead_id)
            
        except Exception as e:
            logger.error(f"Error getting conversation stats: {e}")
            raise DatabaseError(f"Failed to get conversation stats: {str(e)}")
    
    # AI Agent Methods for Conversation Processing
    
    async def parse_and_store_conversation(
        self,
        parse_request: ConversationParseRequest
    ) -> ConversationParseResponse:
        """
        AI Agent method to parse conversation data and store messages
        This is the main method that processes webhook conversation data
        """
        start_time = time.time()
        
        try:
            logger.info(f"AI Agent processing conversation with {len(parse_request.conversation_data)} messages")
            
            # Parse conversation data using AI agent logic
            parsed_messages = await self._parse_conversation_with_ai(
                conversation_data=parse_request.conversation_data,
                lead_id=parse_request.lead_id,
                franchisor_id=parse_request.franchisor_id
            )
            
            # Store parsed messages in bulk
            if parsed_messages:
                created_messages = await self.repository.create_bulk(parsed_messages)
                logger.info(f"Successfully stored {len(created_messages)} conversation messages")
            else:
                created_messages = []
                logger.warning("No messages were parsed from conversation data")
            
            # Calculate processing time
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Prepare response
            response_messages = [
                ConversationMessageResponse.model_validate(msg) for msg in created_messages
            ]
            
            processing_metadata = {
                "processing_time_ms": processing_time_ms,
                "ai_confidence": 0.95,  # High confidence for rule-based parsing
                "detected_patterns": self._analyze_conversation_patterns(parse_request.conversation_data),
                "total_input_messages": len(parse_request.conversation_data),
                "successfully_parsed": len(parsed_messages),
                "successfully_stored": len(created_messages)
            }
            
            return ConversationParseResponse(
                parsed_messages=parsed_messages,
                total_parsed=len(parsed_messages),
                processing_metadata=processing_metadata
            )
            
        except Exception as e:
            logger.error(f"Error in AI agent conversation parsing: {e}")
            raise BusinessLogicError(f"Failed to parse and store conversation: {str(e)}")
    
    async def _parse_conversation_with_ai(
        self,
        conversation_data: List[Dict[str, Any]],
        lead_id: Optional[uuid.UUID] = None,
        franchisor_id: Optional[uuid.UUID] = None
    ) -> List[ConversationMessageCreateRequest]:
        """
        AI Agent logic to parse conversation data and identify speakers
        """
        parsed_messages = []
        
        for message_data in conversation_data:
            try:
                # Extract message content
                message_content = message_data.get("message", "").strip()
                if not message_content:
                    continue
                
                # Determine sender using AI logic
                sender = self._identify_sender(message_data)
                
                # Validate required fields
                if not lead_id:
                    logger.warning("No lead_id provided, skipping message")
                    continue
                
                # Create parsed message
                parsed_message = ConversationMessageCreateRequest(
                    lead_id=lead_id,
                    franchisor_id=franchisor_id,
                    sender=sender,
                    message=message_content
                )
                
                parsed_messages.append(parsed_message)
                
            except Exception as e:
                logger.error(f"Error parsing individual message: {e}")
                continue
        
        return parsed_messages
    
    def _identify_sender(self, message_data: Dict[str, Any]) -> SenderType:
        """
        AI Agent logic to identify if the sender is 'lead' or 'system'
        """
        # Check for explicit sender information
        sender_info = message_data.get("sender_info", "").lower()
        
        # Rule-based identification
        if sender_info in ["system", "bot", "agent", "assistant"]:
            return SenderType.SYSTEM
        
        # Check for phone number patterns (indicates lead)
        if sender_info and (sender_info.startswith("+") or sender_info.isdigit()):
            return SenderType.LEAD
        
        # Check message content patterns for system messages
        message_content = message_data.get("message", "").lower()
        
        # System message patterns
        system_patterns = [
            "thank you for your interest",
            "may i know your",
            "what is your",
            "please provide",
            "great! ",
            "excellent!",
            "perfect!",
            "i understand",
            "let me help you",
            "would you like to",
            "shall we",
            "i can assist",
            "our franchise",
            "the investment",
            "training program",
            "support provided"
        ]
        
        # Lead message patterns
        lead_patterns = [
            "i'm interested",
            "tell me more",
            "how much",
            "what are the requirements",
            "i want to know",
            "can you explain",
            "my name is",
            "i am",
            "yes",
            "no",
            "maybe",
            "sure",
            "okay",
            "thanks"
        ]
        
        # Check for system patterns
        for pattern in system_patterns:
            if pattern in message_content:
                return SenderType.SYSTEM
        
        # Check for lead patterns
        for pattern in lead_patterns:
            if pattern in message_content:
                return SenderType.LEAD
        
        # Default to lead if uncertain (most messages in webhook are from leads)
        return SenderType.LEAD
    
    def _analyze_conversation_patterns(self, conversation_data: List[Dict[str, Any]]) -> List[str]:
        """Analyze conversation data to identify patterns"""
        patterns = []
        
        if not conversation_data:
            return patterns
        
        # Check for greeting patterns
        first_message = conversation_data[0].get("message", "").lower()
        if any(greeting in first_message for greeting in ["hello", "hi", "hey", "good morning", "good afternoon"]):
            patterns.append("greeting")
        
        # Check for interest expression
        for msg in conversation_data:
            content = msg.get("message", "").lower()
            if any(interest in content for interest in ["interested", "franchise", "opportunity", "business"]):
                patterns.append("interest_expression")
                break
        
        # Check for question patterns
        question_count = sum(1 for msg in conversation_data if "?" in msg.get("message", ""))
        if question_count > 0:
            patterns.append("questions_asked")
        
        # Check for information sharing
        for msg in conversation_data:
            content = msg.get("message", "").lower()
            if any(info in content for info in ["my name", "i am", "i have", "my budget", "my location"]):
                patterns.append("information_sharing")
                break
        
        return patterns
