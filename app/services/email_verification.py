"""
Email verification manager service for handling email verification
"""
from datetime import datetime, timezone
from typing import Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.user import User
from app.core.config.settings import settings
from app.core.logging import logger

class EmailVerificationManager:
    """Email verification manager service for handling email verification"""

    async def send_verification_email(
        self,
        email: str,
        token: str
    ) -> bool:
        """Send verification email to user"""
        try:
            # TODO: Implement actual email sending logic
            # For now, just log the verification link
            verification_url = f"{settings.FRONTEND_URL}/verify-email/{token}"
            logger.info(f"Verification email sent to {email} with link: {verification_url}")
            return True
        except Exception as e:
            logger.error(f"Error sending verification email to {email}: {str(e)}")
            return False

    async def send_password_reset_email(
        self,
        email: str,
        token: str
    ) -> bool:
        """Send password reset email to user"""
        try:
            # TODO: Implement actual email sending logic
            # For now, just log the reset link
            reset_url = f"{settings.FRONTEND_URL}/reset-password/{token}"
            logger.info(f"Password reset email sent to {email} with link: {reset_url}")
            return True
        except Exception as e:
            logger.error(f"Error sending password reset email to {email}: {str(e)}")
            return False

    async def verify_email_token(
        self,
        token: str,
        db: AsyncSession = None
    ) -> Optional[str]:
        """Verify email verification token and return user email if valid"""
        stmt = select(User).where(
            User.email_verification_token == token,
            User.email_verification_expires > datetime.now(timezone.utc)
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            return None

        # Mark email as verified
        user.is_email_verified = True
        user.clear_email_verification_token()
        await db.commit()

        return user.email

    async def verify_password_reset_token(
        self,
        token: str,
        db: AsyncSession = None
    ) -> Optional[str]:
        """Verify password reset token and return user email if valid"""
        stmt = select(User).where(
            User.password_reset_token == token,
            User.password_reset_expires > datetime.now(timezone.utc)
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if not user:
            return None

        return user.email 