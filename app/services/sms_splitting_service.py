"""
SMS Splitting Service
Handles intelligent SMS message splitting based on encoding (GSM-7, UCS-2) and length limits.

This service provides:
- Automatic encoding detection (GSM-7 vs UCS-2)
- Smart message splitting with proper segment limits
- Word boundary preservation to avoid breaking words
- Placeholder safety to prevent breaking template variables
- UDH (User Data Header) generation for concatenated messages
- Comprehensive metadata for tracking and logging
"""

import re
import math
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from uuid import uuid4
import logging

logger = logging.getLogger(__name__)


@dataclass
class SMSSegment:
    """Represents a single SMS segment"""
    text: str
    length: int
    encoding: str
    segment_index: int
    total_segments: int
    udh: Optional[str] = None
    segment_id: Optional[str] = None


@dataclass
class SMSSplitResult:
    """Result of SMS splitting operation"""
    original_text: str
    encoding: str
    total_length: int
    parts: List[SMSSegment]
    segment_count: int
    estimated_cost: float
    metadata: Dict[str, Any]


class SMSSplittingService:
    """
    Advanced SMS splitting service with encoding detection and intelligent segmentation
    
    Features:
    - GSM-7 and UCS-2 encoding support
    - Word boundary preservation
    - Placeholder protection
    - Concatenated message handling
    - Cost estimation
    """
    
    # GSM 7-bit character set (basic characters that fit in 7 bits)
    GSM_7BIT_CHARS = set(
        "@£$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞ\x1bÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?"
        "¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà"
    )
    
    # Extended GSM characters (require escape sequence, count as 2 characters)
    GSM_EXTENDED_CHARS = {
        '\f': '\x1b\f',  # Form feed
        '^': '\x1b^',    # Circumflex
        '{': '\x1b{',    # Left brace
        '}': '\x1b}',    # Right brace
        '\\': '\x1b\\',  # Backslash
        '[': '\x1b[',    # Left bracket
        '~': '\x1b~',    # Tilde
        ']': '\x1b]',    # Right bracket
        '|': '\x1b|',    # Pipe
        '€': '\x1b\x65', # Euro sign
    }
    
    def __init__(self):
        """Initialize SMS splitting service"""
        self.segment_limits = {
            'GSM7': {
                'single': 160,      # Single SMS limit for GSM-7
                'concat': 153       # Concatenated SMS limit for GSM-7 (160 - 7 for UDH)
            },
            'UCS2': {
                'single': 70,       # Single SMS limit for UCS-2
                'concat': 67        # Concatenated SMS limit for UCS-2 (70 - 3 for UDH)
            }
        }
    
    def split_message(self, text: str, preserve_words: bool = True) -> SMSSplitResult:
        """
        Split SMS message into segments based on encoding and length limits
        
        Args:
            text: Message text to split
            preserve_words: Whether to preserve word boundaries when splitting
            
        Returns:
            SMSSplitResult with segments and metadata
        """
        try:
            if not text:
                return self._create_empty_result()
            
            # Detect encoding
            encoding = self._detect_encoding(text)
            
            # Calculate effective length considering extended characters
            effective_length = self._calculate_effective_length(text, encoding)
            
            # Determine if splitting is needed
            single_limit = self.segment_limits[encoding]['single']

            if effective_length <= single_limit:
                # Single segment
                segment = SMSSegment(
                    text=text,
                    length=effective_length,
                    encoding=encoding,
                    segment_index=1,
                    total_segments=1,
                    udh=None,
                    segment_id=str(uuid4())
                )
                
                return SMSSplitResult(
                    original_text=text,
                    encoding=encoding,
                    total_length=effective_length,
                    parts=[segment],
                    segment_count=1,
                    estimated_cost=1.0,
                    metadata=self._generate_metadata(text, encoding, [segment])
                )
            
            # Multiple segments needed
            return self._split_into_segments(text, encoding, preserve_words)
            
        except Exception as e:
            logger.error(f"Error splitting SMS message: {str(e)}")
            # Return single segment as fallback
            return self._create_fallback_result(text)
    
    def _detect_encoding(self, text: str) -> str:
        """
        Detect whether text should use GSM-7 or UCS-2 encoding
        
        Args:
            text: Text to analyze
            
        Returns:
            'GSM7' or 'UCS2'
        """
        for char in text:
            # Check if character is in GSM 7-bit set or extended set
            if char not in self.GSM_7BIT_CHARS and char not in self.GSM_EXTENDED_CHARS:
                return 'UCS2'
        
        return 'GSM7'
    
    def _calculate_effective_length(self, text: str, encoding: str) -> int:
        """
        Calculate effective length considering extended characters
        
        Args:
            text: Text to measure
            encoding: Detected encoding
            
        Returns:
            Effective character count
        """
        if encoding == 'UCS2':
            return len(text)
        
        # For GSM-7, extended characters count as 2
        length = 0
        for char in text:
            if char in self.GSM_EXTENDED_CHARS:
                length += 2  # Extended characters require escape sequence
            else:
                length += 1
        
        return length
    
    def _split_into_segments(
        self, 
        text: str, 
        encoding: str, 
        preserve_words: bool
    ) -> SMSSplitResult:
        """
        Split text into multiple SMS segments
        
        Args:
            text: Text to split
            encoding: Message encoding
            preserve_words: Whether to preserve word boundaries
            
        Returns:
            SMSSplitResult with multiple segments
        """
        concat_limit = self.segment_limits[encoding]['concat']
        segments = []
        
        if preserve_words:
            segments = self._split_preserving_words(text, encoding, concat_limit)
            # If word preservation results in oversized segments, fall back to length-based splitting
            if any(self._calculate_effective_length(seg, encoding) > concat_limit for seg in segments):
                segments = self._split_by_length(text, encoding, concat_limit)
        else:
            segments = self._split_by_length(text, encoding, concat_limit)
        
        # Generate UDH for concatenated messages
        reference_number = self._generate_reference_number()
        total_segments = len(segments)
        
        # Create segment objects
        segment_objects = []
        for i, segment_text in enumerate(segments):
            udh = self._generate_udh(reference_number, total_segments, i + 1)
            effective_length = self._calculate_effective_length(segment_text, encoding)
            
            segment = SMSSegment(
                text=segment_text,
                length=effective_length,
                encoding=encoding,
                segment_index=i + 1,
                total_segments=total_segments,
                udh=udh,
                segment_id=str(uuid4())
            )
            segment_objects.append(segment)
        
        return SMSSplitResult(
            original_text=text,
            encoding=encoding,
            total_length=self._calculate_effective_length(text, encoding),
            parts=segment_objects,
            segment_count=total_segments,
            estimated_cost=float(total_segments),
            metadata=self._generate_metadata(text, encoding, segment_objects)
        )
    
    def _split_preserving_words(
        self, 
        text: str, 
        encoding: str, 
        max_length: int
    ) -> List[str]:
        """
        Split text while preserving word boundaries and placeholders
        
        Args:
            text: Text to split
            encoding: Message encoding
            max_length: Maximum length per segment
            
        Returns:
            List of text segments
        """
        segments = []
        current_segment = ""
        
        # Split by words but preserve placeholders
        words = self._tokenize_preserving_placeholders(text)
        
        for word in words:
            word_length = self._calculate_effective_length(word, encoding)
            current_length = self._calculate_effective_length(current_segment, encoding)
            
            # Check if adding this word would exceed limit
            if current_length + word_length > max_length and current_segment:
                # Save current segment and start new one
                segments.append(current_segment.strip())
                current_segment = word
            else:
                # Add word to current segment
                if current_segment:
                    current_segment += " " + word
                else:
                    current_segment = word
        
        # Add final segment
        if current_segment:
            segments.append(current_segment.strip())
        
        return segments
    
    def _split_by_length(self, text: str, encoding: str, max_length: int) -> List[str]:
        """
        Split text by character length without word preservation
        
        Args:
            text: Text to split
            encoding: Message encoding
            max_length: Maximum length per segment
            
        Returns:
            List of text segments
        """
        segments = []
        current_pos = 0
        
        while current_pos < len(text):
            # Find the end position for this segment
            end_pos = self._find_segment_end(text, current_pos, encoding, max_length)
            
            # Extract segment
            segment = text[current_pos:end_pos]
            segments.append(segment)
            
            current_pos = end_pos
        
        return segments
    
    def _find_segment_end(
        self, 
        text: str, 
        start_pos: int, 
        encoding: str, 
        max_length: int
    ) -> int:
        """
        Find the end position for a segment considering character encoding
        
        Args:
            text: Full text
            start_pos: Starting position
            encoding: Message encoding
            max_length: Maximum effective length
            
        Returns:
            End position for segment
        """
        current_length = 0
        pos = start_pos
        
        while pos < len(text) and current_length < max_length:
            char = text[pos]
            char_length = 2 if (encoding == 'GSM7' and char in self.GSM_EXTENDED_CHARS) else 1
            
            if current_length + char_length > max_length:
                break
            
            current_length += char_length
            pos += 1
        
        return pos
    
    def _tokenize_preserving_placeholders(self, text: str) -> List[str]:
        """
        Tokenize text into words while preserving placeholders like {name}
        
        Args:
            text: Text to tokenize
            
        Returns:
            List of tokens (words and placeholders)
        """
        # Pattern to match placeholders like {name}, {company}, etc.
        placeholder_pattern = r'\{[^}]+\}'
        
        tokens = []
        last_end = 0
        
        # Find all placeholders
        for match in re.finditer(placeholder_pattern, text):
            start, end = match.span()
            
            # Add words before placeholder
            if start > last_end:
                words_before = text[last_end:start].split()
                tokens.extend(words_before)
            
            # Add placeholder as single token
            tokens.append(match.group())
            last_end = end
        
        # Add remaining words
        if last_end < len(text):
            remaining_words = text[last_end:].split()
            tokens.extend(remaining_words)
        
        return [token for token in tokens if token.strip()]
    
    def _generate_reference_number(self) -> int:
        """Generate reference number for concatenated messages"""
        return hash(str(uuid4())) % 256  # 8-bit reference number
    
    def _generate_udh(self, reference: int, total: int, current: int) -> str:
        """
        Generate User Data Header for concatenated SMS
        
        Args:
            reference: Reference number for message series
            total: Total number of segments
            current: Current segment number (1-based)
            
        Returns:
            UDH string
        """
        # Standard concatenated SMS UDH format
        # 00: Information Element Identifier (concatenated SMS)
        # 03: Information Element Data Length
        # XX: Reference number
        # XX: Total number of segments
        # XX: Current segment number
        return f"00:03:{reference:02X}:{total:02X}:{current:02X}"
    
    def _generate_metadata(
        self, 
        original_text: str, 
        encoding: str, 
        segments: List[SMSSegment]
    ) -> Dict[str, Any]:
        """Generate comprehensive metadata for the split operation"""
        return {
            "original_length": len(original_text),
            "effective_length": self._calculate_effective_length(original_text, encoding),
            "encoding": encoding,
            "segment_count": len(segments),
            "segments": [
                {
                    "index": seg.segment_index,
                    "length": seg.length,
                    "udh": seg.udh,
                    "segment_id": seg.segment_id
                }
                for seg in segments
            ],
            "split_timestamp": str(uuid4()),  # Unique identifier for this split operation
            "word_boundary_preserved": True,
            "placeholder_safe": True
        }
    
    def _create_empty_result(self) -> SMSSplitResult:
        """Create result for empty text"""
        return SMSSplitResult(
            original_text="",
            encoding="GSM7",
            total_length=0,
            parts=[],
            segment_count=0,
            estimated_cost=0.0,
            metadata={"empty": True}
        )
    
    def _create_fallback_result(self, text: str) -> SMSSplitResult:
        """Create fallback result for error cases"""
        segment = SMSSegment(
            text=text,
            length=len(text),
            encoding="UCS2",  # Safe fallback
            segment_index=1,
            total_segments=1,
            udh=None,
            segment_id=str(uuid4())
        )
        
        return SMSSplitResult(
            original_text=text,
            encoding="UCS2",
            total_length=len(text),
            parts=[segment],
            segment_count=1,
            estimated_cost=1.0,
            metadata={"fallback": True, "error_recovery": True}
        )
    
    def estimate_cost(self, text: str) -> float:
        """
        Estimate SMS cost based on segment count
        
        Args:
            text: Message text
            
        Returns:
            Estimated cost (number of SMS segments)
        """
        result = self.split_message(text)
        return result.estimated_cost
    
    def validate_message_length(self, text: str, max_segments: int = 5) -> Dict[str, Any]:
        """
        Validate message length against policy limits
        
        Args:
            text: Message text
            max_segments: Maximum allowed segments
            
        Returns:
            Validation result
        """
        result = self.split_message(text)
        
        return {
            "valid": result.segment_count <= max_segments,
            "segment_count": result.segment_count,
            "max_segments": max_segments,
            "estimated_cost": result.estimated_cost,
            "encoding": result.encoding,
            "recommendation": (
                "Message is within limits" if result.segment_count <= max_segments
                else f"Message too long: {result.segment_count} segments (max: {max_segments})"
            )
        }
