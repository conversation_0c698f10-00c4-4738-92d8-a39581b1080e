"""
Lead Status Update Service
Handles atomic status updates with database transactions, locking, audit trails, and idempotency.
"""

import uuid
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func
from sqlalchemy.orm import selectinload
import structlog

from app.models.lead import Lead
from app.models.lead_reference import LeadStatus, LeadStatusHistory
from app.services.lead_status_classification_service import (
    lead_classifier, 
    StatusContext, 
    ClassificationResult
)
from app.services.lead_status_normalization_service import status_normalizer
from app.core.exceptions import NotFoundError, CustomValidationError

from app.core.logging import logger


@dataclass
class StatusUpdateResult:
    """Result of a status update operation"""
    old_status: Optional[str]
    new_status: str
    changed: bool
    confidence: float
    rationale: str
    lead_id: str
    history_id: Optional[str] = None


class LeadStatusUpdateService:
    """Service for updating lead status with full audit trail and safety checks"""
    
    def __init__(self, db: AsyncSession):
        """Initialize the service with database session"""
        self.db = db
    
    async def update_status_from_message(
        self,
        lead_id: str,
        message_text: str,
        context: Optional[StatusContext] = None,
        changed_by: str = "system",
        source: str = "andy"
    ) -> StatusUpdateResult:
        """
        Update lead status based on message analysis
        
        Args:
            lead_id: UUID of the lead to update
            message_text: Message content to analyze
            context: Additional context for classification
            changed_by: Who/what is making the change
            source: Source of the change (andy, manual, etc.)
            
        Returns:
            StatusUpdateResult with details of the update
        """
        try:
            # Convert string ID to UUID
            lead_uuid = uuid.UUID(lead_id)
        except ValueError:
            raise CustomValidationError(
                title="Invalid Lead ID",
                description=f"Lead ID must be a valid UUID: {lead_id}"
            )
        
        try:
            # Step 1: Lock the lead record for update
            lead = await self._lock_lead(lead_uuid)
            if not lead:
                raise NotFoundError(
                    detail=f"No lead found with ID: {lead_id}"
                )
            
            # Step 2: Get current status information
            current_status_name = None
            current_status_id = None
            if lead.lead_status_id and lead.lead_status_rel:
                current_status_name = lead.lead_status_rel.name
                current_status_id = lead.lead_status_id
            
            # Step 3: Classify the message to determine new status
            if context is None:
                context = StatusContext(current_status=current_status_name)
            
            classification = await lead_classifier.classify_message(message_text, context)
            
            # Step 4: Resolve new status to database ID
            new_status_id = await self._get_status_id(classification.label)
            if not new_status_id:
                raise CustomValidationError(
                    title="Invalid Status",
                    description=f"Status '{classification.label}' not found in database"
                )
            
            # Step 5: Check if status actually changed (idempotency)
            if current_status_id == new_status_id:
                logger.info(f"Status unchanged for lead {lead_id}: {classification.label}")
                return StatusUpdateResult(
                    old_status=current_status_name,
                    new_status=classification.label,
                    changed=False,
                    confidence=classification.confidence,
                    rationale=classification.rationale,
                    lead_id=lead_id
                )
            
            # Step 6: Update the lead status
            await self.db.execute(
                update(Lead)
                .where(Lead.id == lead_uuid)
                .values(
                    lead_status_id=new_status_id,
                    updated_at=func.now()
                )
            )
            
            # Step 7: Create audit history record
            history_record = await self._create_history_record(
                lead_id=lead_uuid,
                from_status_id=current_status_id,
                to_status_id=new_status_id,
                reason=f"Message analysis: {classification.method}",
                confidence=classification.confidence,
                rationale=classification.rationale,
                message_excerpt=message_text[:500],  # Limit excerpt length
                changed_by=changed_by,
                source=source,
                review_needed=classification.confidence < 0.6
            )
            
            # Step 8: Commit transaction
            await self.db.commit()
            
            logger.info(
                f"Status updated for lead {lead_id}: {current_status_name} -> {classification.label}",
                extra={
                    "lead_id": lead_id,
                    "old_status": current_status_name,
                    "new_status": classification.label,
                    "confidence": classification.confidence,
                    "method": classification.method
                }
            )
            
            return StatusUpdateResult(
                old_status=current_status_name,
                new_status=classification.label,
                changed=True,
                confidence=classification.confidence,
                rationale=classification.rationale,
                lead_id=lead_id,
                history_id=str(history_record.id)
            )

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating lead status: {e}")
            raise
    
    async def _lock_lead(self, lead_id: uuid.UUID) -> Optional[Lead]:
        """Lock lead record for update and return with relationships"""
        stmt = (
            select(Lead)
            .options(selectinload(Lead.lead_status_rel))
            .where(Lead.id == lead_id)
            .with_for_update()  # SELECT ... FOR UPDATE
        )
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def _get_status_id(self, status_name: str) -> Optional[uuid.UUID]:
        """Get status ID by name (case-insensitive)"""
        stmt = select(LeadStatus.id).where(
            func.lower(LeadStatus.name) == func.lower(status_name),
            LeadStatus.is_active == True,
            LeadStatus.is_deleted == False
        )
        
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def _create_history_record(
        self,
        lead_id: uuid.UUID,
        from_status_id: Optional[uuid.UUID],
        to_status_id: uuid.UUID,
        reason: str,
        confidence: float,
        rationale: str,
        message_excerpt: str,
        changed_by: str,
        source: str,
        review_needed: bool
    ) -> LeadStatusHistory:
        """Create audit history record"""
        from datetime import datetime

        history = LeadStatusHistory(
            lead_id=lead_id,
            from_status_id=from_status_id,
            to_status_id=to_status_id,
            reason=reason,
            confidence=confidence,
            rationale=rationale,
            message_excerpt=message_excerpt,
            changed_by=changed_by,
            source=source,
            review_needed=review_needed,
            is_active=True,
            is_deleted=False,
            created_at=datetime.now(),  # Explicitly set created_at
            updated_at=datetime.now()   # Explicitly set updated_at
        )

        self.db.add(history)
        await self.db.flush()  # Get the ID without committing
        return history
    
    async def get_status_history(
        self, 
        lead_id: str, 
        limit: int = 10
    ) -> list[Dict[str, Any]]:
        """Get status change history for a lead"""
        try:
            lead_uuid = uuid.UUID(lead_id)
        except ValueError:
            raise CustomValidationError(
                title="Invalid Lead ID",
                description=f"Lead ID must be a valid UUID: {lead_id}"
            )
        
        stmt = (
            select(LeadStatusHistory)
            .options(
                selectinload(LeadStatusHistory.from_status),
                selectinload(LeadStatusHistory.to_status)
            )
            .where(
                LeadStatusHistory.lead_id == lead_uuid,
                LeadStatusHistory.is_deleted == False
            )
            .order_by(LeadStatusHistory.created_at.desc())
            .limit(limit)
        )
        
        result = await self.db.execute(stmt)
        history_records = result.scalars().all()
        
        return [
            {
                "id": str(record.id),
                "from_status": record.from_status.name if record.from_status else None,
                "to_status": record.to_status.name if record.to_status else "Unknown",
                "reason": record.reason,
                "confidence": record.confidence,
                "rationale": record.rationale,
                "message_excerpt": record.message_excerpt,
                "changed_by": record.changed_by,
                "source": record.source,
                "review_needed": record.review_needed,
                "created_at": record.created_at.isoformat()
            }
            for record in history_records
        ]
    
    async def manual_status_update(
        self,
        lead_id: str,
        new_status_name: str,
        reason: str,
        changed_by: str = "manual"
    ) -> StatusUpdateResult:
        """
        Manually update lead status (bypassing message analysis)
        
        Args:
            lead_id: UUID of the lead to update
            new_status_name: Name of the new status
            reason: Reason for the manual change
            changed_by: Who is making the change
            
        Returns:
            StatusUpdateResult with details of the update
        """
        try:
            lead_uuid = uuid.UUID(lead_id)
        except ValueError:
            raise CustomValidationError(
                title="Invalid Lead ID",
                description=f"Lead ID must be a valid UUID: {lead_id}"
            )
        
        # Normalize the status name
        normalized_status, confidence = status_normalizer.normalize_status_name(new_status_name)
        if confidence < 0.9:
            raise CustomValidationError(
                title="Invalid Status Name",
                description=f"Status '{new_status_name}' could not be normalized to a valid canonical status"
            )
        
        try:
            # Lock the lead
            lead = await self._lock_lead(lead_uuid)
            if not lead:
                raise NotFoundError(
                    detail=f"No lead found with ID: {lead_id}"
                )
            
            # Get current and new status IDs
            current_status_name = None
            current_status_id = None
            if lead.lead_status_id and lead.lead_status_rel:
                current_status_name = lead.lead_status_rel.name
                current_status_id = lead.lead_status_id
            
            new_status_id = await self._get_status_id(normalized_status)
            if not new_status_id:
                raise CustomValidationError(
                    title="Invalid Status",
                    description=f"Status '{normalized_status}' not found in database"
                )
            
            # Check if status changed
            if current_status_id == new_status_id:
                return StatusUpdateResult(
                    old_status=current_status_name,
                    new_status=normalized_status,
                    changed=False,
                    confidence=1.0,
                    rationale=f"Manual update: {reason}",
                    lead_id=lead_id
                )
            
            # Update the lead
            await self.db.execute(
                update(Lead)
                .where(Lead.id == lead_uuid)
                .values(
                    lead_status_id=new_status_id,
                    updated_at=func.now()
                )
            )
            
            # Create history record
            history_record = await self._create_history_record(
                lead_id=lead_uuid,
                from_status_id=current_status_id,
                to_status_id=new_status_id,
                reason=f"Manual update: {reason}",
                confidence=1.0,
                rationale=f"Manual status change by {changed_by}",
                message_excerpt="",
                changed_by=changed_by,
                source="manual",
                review_needed=False
            )
            
            await self.db.commit()
            
            logger.info(
                f"Manual status update for lead {lead_id}: {current_status_name} -> {normalized_status}",
                extra={
                    "lead_id": lead_id,
                    "old_status": current_status_name,
                    "new_status": normalized_status,
                    "changed_by": changed_by,
                    "reason": reason
                }
            )
            
            return StatusUpdateResult(
                old_status=current_status_name,
                new_status=normalized_status,
                changed=True,
                confidence=1.0,
                rationale=f"Manual update: {reason}",
                lead_id=lead_id,
                history_id=str(history_record.id)
            )

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error in manual status update: {e}")
            raise
