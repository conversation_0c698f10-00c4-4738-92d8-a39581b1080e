"""
Async Document Processing Service

Handles background processing of documents for DocQA integration
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from sqlalchemy import select, update
import structlog

from app.core.database.connection import get_db
from app.models.document import Document
from app.services.docqa_integration_service import get_docqa_integration_service

from app.core.logging import logger
app_logger = logging.getLogger(__name__)


class AsyncDocumentProcessor:
    """Service for processing documents asynchronously"""
    
    def __init__(self):
        self.docqa_service = get_docqa_integration_service()
        self._processing_tasks: Dict[str, asyncio.Task] = {}
    
    async def start_processing(self, document_id: str, file_url: str) -> bool:
        """
        Start async processing of a document
        
        Args:
            document_id: Document ID to process
            file_url: S3 URL of the document
            
        Returns:
            bool: True if processing started successfully
        """
        try:
            # Check if already processing
            if document_id in self._processing_tasks:
                logger.warning("Document already being processed", document_id=document_id)
                return False
            
            # Update status to processing
            await self._update_processing_status(
                document_id=document_id,
                status="processing",
                progress=0,
                message="Starting document processing...",
                started_at=datetime.now(timezone.utc)
            )
            
            # Create and start processing task
            task = asyncio.create_task(
                self._process_document_async(document_id, file_url)
            )
            self._processing_tasks[document_id] = task
            
            logger.info("Document processing started", document_id=document_id)
            return True
            
        except Exception as e:
            logger.error("Failed to start document processing", 
                        document_id=document_id, error=str(e))
            await self._update_processing_status(
                document_id=document_id,
                status="failed",
                message="Failed to start processing",
                error=str(e),
                completed_at=datetime.now(timezone.utc)
            )
            return False
    
    async def _process_document_async(self, document_id: str, file_url: str):
        """
        Internal method to process document asynchronously
        """
        try:
            logger.info("Processing document", document_id=document_id, file_url=file_url)
            
            # Update progress
            await self._update_processing_status(
                document_id=document_id,
                progress=25,
                message="Downloading and analyzing document..."
            )
            
            # Process document with DocQA
            result = await self.docqa_service.process_document(
                document_id=document_id,
                file_url=file_url
            )
            
            # Update progress
            await self._update_processing_status(
                document_id=document_id,
                progress=75,
                message="Generating embeddings and storing vectors..."
            )
            
            # Check if processing was successful
            if result and result.success:
                await self._update_processing_status(
                    document_id=document_id,
                    status="processed",
                    progress=100,
                    message=f"Successfully processed. Created {result.chunks_created} chunks.",
                    completed_at=datetime.now(timezone.utc)
                )
                logger.info("Document processing completed successfully",
                           document_id=document_id,
                           chunks_created=result.chunks_created)
            else:
                error_msg = "Document processing failed or returned no result"
                await self._update_processing_status(
                    document_id=document_id,
                    status="failed",
                    progress=100,
                    message="Processing failed",
                    error=error_msg,
                    completed_at=datetime.now(timezone.utc)
                )
                logger.error("Document processing failed",
                           document_id=document_id, error=error_msg)
            
        except Exception as e:
            logger.error("Document processing error", 
                        document_id=document_id, error=str(e))
            await self._update_processing_status(
                document_id=document_id,
                status="failed",
                progress=100,
                message="Processing failed with exception",
                error=str(e),
                completed_at=datetime.now(timezone.utc)
            )
        finally:
            # Clean up task reference
            if document_id in self._processing_tasks:
                del self._processing_tasks[document_id]
    
    async def _update_processing_status(
        self,
        document_id: str,
        status: Optional[str] = None,
        progress: Optional[int] = None,
        message: Optional[str] = None,
        error: Optional[str] = None,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None
    ):
        """Update document processing status in database"""
        try:
            async for db in get_db():
                # Build update values
                update_values = {}
                if status is not None:
                    update_values["processing_status"] = status
                if progress is not None:
                    update_values["processing_progress"] = progress
                if message is not None:
                    update_values["processing_message"] = message
                if error is not None:
                    update_values["processing_error"] = error
                if started_at is not None:
                    update_values["processing_started_at"] = started_at
                if completed_at is not None:
                    update_values["processing_completed_at"] = completed_at
                
                # Update document
                await db.execute(
                    update(Document)
                    .where(Document.id == document_id)
                    .values(**update_values)
                )
                await db.commit()
                
                logger.debug("Updated processing status", 
                           document_id=document_id, 
                           status=status, 
                           progress=progress)
                
        except Exception as e:
            logger.error("Failed to update processing status", 
                        document_id=document_id, error=str(e))
    
    async def get_processing_status(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get current processing status for a document"""
        try:
            async for db in get_db():
                result = await db.execute(
                    select(
                        Document.processing_status,
                        Document.processing_progress,
                        Document.processing_message,
                        Document.processing_error,
                        Document.processing_started_at,
                        Document.processing_completed_at
                    ).where(Document.id == document_id)
                )
                row = result.first()
                
                if row:
                    return {
                        "document_id": document_id,
                        "status": row.processing_status,
                        "progress": row.processing_progress,
                        "message": row.processing_message,
                        "error": row.processing_error,
                        "started_at": row.processing_started_at,
                        "completed_at": row.processing_completed_at,
                        "is_processing": document_id in self._processing_tasks
                    }
                return None
                
        except Exception as e:
            logger.error("Failed to get processing status", 
                        document_id=document_id, error=str(e))
            return None
    
    def is_processing(self, document_id: str) -> bool:
        """Check if document is currently being processed"""
        return document_id in self._processing_tasks
    
    async def cancel_processing(self, document_id: str) -> bool:
        """Cancel processing for a document"""
        if document_id in self._processing_tasks:
            task = self._processing_tasks[document_id]
            task.cancel()
            del self._processing_tasks[document_id]
            
            await self._update_processing_status(
                document_id=document_id,
                status="failed",
                message="Processing cancelled",
                error="Processing was cancelled by user",
                completed_at=datetime.now(timezone.utc)
            )
            
            logger.info("Document processing cancelled", document_id=document_id)
            return True
        return False


# Global instance
_async_processor = None

def get_async_document_processor() -> AsyncDocumentProcessor:
    """Get global async document processor instance"""
    global _async_processor
    if _async_processor is None:
        _async_processor = AsyncDocumentProcessor()
    return _async_processor
