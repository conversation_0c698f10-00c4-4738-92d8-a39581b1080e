"""User service for business logic"""
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import Session
import logging

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.core.security import password_hasher

logger = logging.getLogger(__name__)


class UserService:
    """Service for user-related business logic"""
    
    def __init__(self, db: Session):
        """Initialize user service"""
        self.db = db
    
    def create_user(self, user: UserCreate):
        user_dict = user.dict()
        user_dict["password_hash"] = password_hasher.hash_password(user_dict.pop("password"))
        user_dict["role"] = user_dict.get("role", "ADMIN").upper()  # Always uppercase
        db_user = User(**user_dict)
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        return UserResponse.model_validate(db_user)

    async def get_user(self, user_identifier: str):
        """Get user by ID (UUID) or email"""
        from sqlalchemy import select
        import uuid

        # Try to parse as UUID first
        try:
            user_uuid = uuid.UUID(user_identifier)
            # If successful, search by ID
            result = await self.db.execute(select(User).where(User.id == user_uuid))
        except ValueError:
            # If not a valid UUID, search by email
            result = await self.db.execute(select(User).where(User.email == user_identifier))

        return result.scalar_one_or_none()

    def get_users(self, skip: int = 0, limit: int = 100):
        return self.db.query(User).offset(skip).limit(limit).all()

    def update_user(self, user_id: str, user: UserUpdate):
        db_user = self.db.query(User).filter(User.id == user_id).first()
        if db_user:
            update_data = user.dict(exclude_unset=True)
            if "password" in update_data:
                update_data["password_hash"] = password_hasher.hash_password(update_data.pop("password"))
            for key, value in update_data.items():
                setattr(db_user, key, value)
            self.db.commit()
            self.db.refresh(db_user)
        return db_user

    def delete_user(self, user_id: str):
        db_user = self.db.query(User).filter(User.id == user_id).first()
        if db_user:
            self.db.delete(db_user)
            self.db.commit()
        return db_user

    async def get_user_by_email(
        self, 
        db: AsyncSession, 
        email: str
    ) -> Optional[User]:
        """
        Get user by email
        
        Args:
            db: Database session
            email: User email
            
        Returns:
            Optional[User]: User if found
        """
        try:
            result = await db.execute(
                select(User).where(User.email == email)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get user by email {email}: {e}")
            raise
    
    async def verify_password(
        self, 
        plain_password: str, 
        hashed_password: str
    ) -> bool:
        """
        Verify password
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            bool: True if password matches
        """
        return password_hasher.verify_password(plain_password, hashed_password)
    
    async def authenticate_user(
        self, 
        db: AsyncSession, 
        email: str, 
        password: str
    ) -> Optional[User]:
        """
        Authenticate user
        
        Args:
            db: Database session
            email: User email
            password: User password
            
        Returns:
            Optional[User]: User if authenticated
        """
        try:
            user = await self.get_user_by_email(db, email)
            if not user:
                return None
            
            if not await self.verify_password(password, user.password_hash):
                return None
            
            if not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to authenticate user {email}: {e}")
            raise
