"""
Zoho CRM Service for Australian franchise lead management
Handles integration with Zoho CRM for lead data sync
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from app.core.config.settings import settings
import httpx
from app.core.logging import logger

class ZohoService:
    """Zoho CRM service for Australian franchise management"""
    
    def __init__(self):
        self.settings = settings
        self.base_url = "https://www.zohoapis.com/crm/v3"
        self.headers = {
            "Authorization": f"Zoho-oauthtoken {self.settings.ZOHO_ACCESS_TOKEN}",
            "Content-Type": "application/json"
        }

    async def create_lead(self, lead_data: Dict[str, Any]) -> Optional[str]:
        """Create a new lead in Zoho CRM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/Leads",
                    headers=self.headers,
                    json={"data": [lead_data]}
                )
                response.raise_for_status()
                data = response.json()
                return data["data"][0]["details"]["id"]
        except Exception as e:
            logger.error(f"Error creating lead in Zoho: {e}")
            return None

    async def update_lead(self, zoho_id: str, lead_data: Dict[str, Any]) -> bool:
        """Update a lead in Zoho CRM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.put(
                    f"{self.base_url}/Leads/{zoho_id}",
                    headers=self.headers,
                    json={"data": [lead_data]}
                )
                response.raise_for_status()
                return True
        except Exception as e:
            logger.error(f"Error updating lead in Zoho: {e}")
            return False

    async def get_lead(self, zoho_id: str) -> Optional[Dict[str, Any]]:
        """Get a lead from Zoho CRM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/Leads/{zoho_id}",
                    headers=self.headers
                )
                response.raise_for_status()
                data = response.json()
                return data["data"][0]
        except Exception as e:
            logger.error(f"Error getting lead from Zoho: {e}")
            return None

    async def search_leads(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search leads in Zoho CRM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/Leads/search",
                    headers=self.headers,
                    params=criteria
                )
                response.raise_for_status()
                data = response.json()
                return data["data"]
        except Exception as e:
            logger.error(f"Error searching leads in Zoho: {e}")
            return []

    async def delete_lead(self, zoho_id: str) -> bool:
        """Delete a lead from Zoho CRM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{self.base_url}/Leads/{zoho_id}",
                    headers=self.headers
                )
                response.raise_for_status()
                return True
        except Exception as e:
            logger.error(f"Error deleting lead from Zoho: {e}")
            return False

    async def get_lead_notes(self, zoho_id: str) -> List[Dict[str, Any]]:
        """Get notes for a lead"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/Leads/{zoho_id}/Notes",
                    headers=self.headers
                )
                response.raise_for_status()
                data = response.json()
                return data["data"]
        except Exception as e:
            logger.error(f"Error getting lead notes: {e}")
            return []

    async def add_note_to_lead(
        self,
        zoho_id: str,
        note_content: str,
        note_title: str = "Note"
    ) -> Dict[str, Any]:
        """Add a note to a lead"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/Leads/{zoho_id}/Notes",
                    headers=self.headers,
                    json={
                        "data": [{
                            "Note_Title": note_title,
                            "Note_Content": note_content
                        }]
                    }
                )
                response.raise_for_status()
                data = response.json()
                return data["data"][0]
        except Exception as e:
            logger.error(f"Error adding note to lead: {e}")
            return {}

    async def get_leads(
        self,
        page: int = 1,
        per_page: int = 200,
        modified_since: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Get leads from Zoho CRM"""
        try:
            params = {
                "page": page,
                "per_page": per_page,
                "fields": "id,Full_Name,Phone,Mobile,Email,Lead_Source,Lead_Status,Company,Annual_Revenue,City,State,Country,Description,Created_Time,Modified_Time"
            }
            
            if modified_since:
                params["If-Modified-Since"] = modified_since.isoformat()
            
            response = await self._make_request("GET", "Leads", params)
            
            leads = []
            if "data" in response:
                for lead_data in response["data"]:
                    # Format lead data for our system
                    lead = {
                        "zoho_id": lead_data.get("id"),
                        "full_name": lead_data.get("Full_Name"),
                        "phone": lead_data.get("Mobile") or lead_data.get("Phone"),
                        "email": lead_data.get("Email"),
                        "company": lead_data.get("Company"),
                        "lead_source": lead_data.get("Lead_Source"),
                        "lead_status": lead_data.get("Lead_Status"),
                        "city": lead_data.get("City"),
                        "state": lead_data.get("State"),
                        "country": lead_data.get("Country"),
                        "annual_revenue": lead_data.get("Annual_Revenue"),
                        "description": lead_data.get("Description"),
                        "created_time": lead_data.get("Created_Time"),
                        "modified_time": lead_data.get("Modified_Time")
                    }
                    leads.append(lead)
            
            logger.info(f"Retrieved {len(leads)} leads from Zoho CRM")
            return leads
            
        except Exception as e:
            logger.error(f"Error getting leads from Zoho: {e}")
            raise
    
    async def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """Make authenticated request to Zoho CRM API"""
        try:
            url = f"{self.base_url}/{endpoint}"
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=self.headers, params=data)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=self.headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=self.headers, json=data)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error making Zoho request: {e}")
            raise
