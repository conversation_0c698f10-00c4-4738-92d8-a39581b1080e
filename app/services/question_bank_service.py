"""
Question Bank Service
Business logic for question bank operations
"""

from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from app.repositories.question_bank_repository import QuestionBankRepository
from app.schemas.question_bank import (
    QuestionCreateRequest,
    QuestionUpdateRequest,
    QuestionReorderRequest
)
from app.core.app_rules import success_response, error_response
from app.core.responses.models import ErrorCodes
import logging
import uuid

logger = logging.getLogger(__name__)

class QuestionBankService:
    """Service for question bank operations."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = QuestionBankRepository(db)

    async def get_franchisors_for_dropdown(self) -> Dict[str, Any]:
        """
        Get franchisors for dropdown selection.
        
        Returns:
            Success response with franchisor list
        """
        try:
            franchisors = await self.repository.get_franchisors_for_dropdown()
            
            return success_response(
                details={"franchisors": franchisors},
                title="Franchisors Retrieved",
                description=f"Successfully retrieved {len(franchisors)} franchisors for dropdown"
            )
        except Exception as e:
            logger.error(f"Error in get_franchisors_for_dropdown: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Retrieve Franchisors",
                description="An error occurred while fetching franchisors"
            )

    async def create_question(self, question_data: QuestionCreateRequest) -> Dict[str, Any]:
        """
        Create a new question with auto-calculated order.
        
        Args:
            question_data: Question creation data
            
        Returns:
            Success response with created question details
        """
        try:
            # Validate franchisor exists
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor_ids = [f["id"] for f in franchisors]
            
            if question_data.franchisor_id not in franchisor_ids:
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Franchisor",
                    description="The specified franchisor does not exist"
                )
            
            # Create question
            question = await self.repository.create_question(question_data)
            
            # Get franchisor details
            franchisor = next((f for f in franchisors if f["id"] == question.franchisor_id), None)
            
            response_data = {
                "id": question.id,
                "franchisor_id": question.franchisor_id,
                "franchisor_name": franchisor["name"] if franchisor else None,
                "franchisor_category": franchisor["industry"] if franchisor else None,
                "question_text": question.question_text,
                "question_internal_text": question.question_internal_text,
                "question_type": question.question_type,
                "order_sequence": question.order_sequence,
                "is_active": question.is_active,
                "expected_answer": question.expected_answers if question.expected_answers else None,
                "created_at": question.created_at,
                "updated_at": question.updated_at,

                # New fields for pre-qualification questions
                "question_id": question.question_id,
                "category": question.category,
                "score_weight": question.score_weight,
                "context_info": question.context_info,
                "qualification_weight": question.qualification_weight,
                "expected_answer_type": question.expected_answer_type,
                "answer_options": question.answer_options,
                "passing_criteria": question.passing_criteria,
                "validation_rules": question.validation_rules,
                "requires_follow_up": question.requires_follow_up,
                "follow_up_logic": question.follow_up_logic,
                "is_required": question.is_required,
                "is_deleted": question.is_deleted,
                "deleted_at": question.deleted_at
            }
            
            return success_response(
                details=response_data,
                title="Question Created",
                description="Question created successfully with auto-calculated order"
            )
        except ValueError as e:
            # Handle duplicate validation errors
            logger.warning(f"Validation error in create_question: {e}")
            return error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                title="Duplicate Question Text",
                description=str(e)
            )
        except Exception as e:
            logger.error(f"Error in create_question: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Create Question",
                description="An error occurred while creating the question"
            )

    async def update_question(self, question_id: uuid.UUID, question_data: QuestionUpdateRequest) -> Dict[str, Any]:
        """
        Update a question (excluding order and question_internal_text).
        
        Args:
            question_id: Question ID
            question_data: Updated question data
            
        Returns:
            Success response with updated question details
        """
        try:
            # Update question
            question = await self.repository.update_question(question_id, question_data)
            
            if not question:
                return error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    title="Question Not Found",
                    description="The specified question does not exist"
                )
            
            # Get franchisor details
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor = next((f for f in franchisors if f["id"] == question.franchisor_id), None)
            
            response_data = {
                "id": question.id,
                "franchisor_id": question.franchisor_id,
                "franchisor_name": franchisor["name"] if franchisor else None,
                "franchisor_category": franchisor["industry"] if franchisor else None,
                "question_text": question.question_text,
                "question_internal_text": question.question_internal_text,
                "question_type": question.question_type,
                "order_sequence": question.order_sequence,
                "is_active": question.is_active,
                "expected_answer": question.expected_answers if question.expected_answers else None,
                "created_at": question.created_at,
                "updated_at": question.updated_at,

                # New fields for pre-qualification questions
                "question_id": question.question_id,
                "category": question.category,
                "score_weight": question.score_weight,
                "context_info": question.context_info,
                "qualification_weight": question.qualification_weight,
                "expected_answer_type": question.expected_answer_type,
                "answer_options": question.answer_options,
                "passing_criteria": question.passing_criteria,
                "validation_rules": question.validation_rules,
                "requires_follow_up": question.requires_follow_up,
                "follow_up_logic": question.follow_up_logic,
                "is_required": question.is_required,
                "is_deleted": question.is_deleted,
                "deleted_at": question.deleted_at
            }
            
            return success_response(
                details=response_data,
                title="Question Updated",
                description="Question updated successfully"
            )
        except ValueError as e:
            # Handle duplicate validation errors
            logger.warning(f"Validation error in update_question: {e}")
            return error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                title="Duplicate Question Text",
                description=str(e)
            )
        except Exception as e:
            logger.error(f"Error in update_question: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Update Question",
                description="An error occurred while updating the question"
            )

    async def delete_question(self, question_id: uuid.UUID) -> Dict[str, Any]:
        """
        Soft delete a question and reorder subsequent questions.

        When a question is deleted, all questions with higher order_sequence
        in the same franchisor will have their order decremented by 1.

        Args:
            question_id: Question ID to delete

        Returns:
            Success response indicating deletion and reordering
        """
        try:
            # Get question details before deletion for better response
            question = await self.repository.get_question_by_id(question_id)
            if not question:
                return error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    title="Question Not Found",
                    description="The specified question does not exist or is already deleted"
                )

            deleted_order = question.order_sequence
            franchisor_id = question.franchisor_id

            # Perform the deletion with automatic reordering
            success = await self.repository.soft_delete_question(question_id)

            if not success:
                return error_response(
                    error_code=ErrorCodes.UNKNOWN_ERROR,
                    title="Delete Failed",
                    description="Failed to delete the question"
                )

            return success_response(
                details={
                    "question_id": question_id,
                    "deleted_order": deleted_order,
                    "franchisor_id": franchisor_id,
                    "reordered": True
                },
                title="Question Deleted",
                description=f"Question at position {deleted_order} deleted successfully. Subsequent questions have been reordered automatically."
            )
        except Exception as e:
            logger.error(f"Error in delete_question: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Delete Question",
                description="An error occurred while deleting the question"
            )

    async def toggle_question_status(self, question_id: uuid.UUID) -> Dict[str, Any]:
        """
        Toggle question status between active and inactive.
        
        Args:
            question_id: Question ID to toggle
            
        Returns:
            Success response with updated question details
        """
        try:
            question = await self.repository.toggle_question_status(question_id)
            
            if not question:
                return error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    title="Question Not Found",
                    description="The specified question does not exist"
                )
            
            # Get franchisor details
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor = next((f for f in franchisors if f["id"] == question.franchisor_id), None)
            
            response_data = {
                "id": question.id,
                "franchisor_id": question.franchisor_id,
                "franchisor_name": franchisor["name"] if franchisor else None,
                "industry": franchisor["industry"] if franchisor else None,
                "question_text": question.question_text,
                "question_internal_text": question.question_internal_text,
                "question_type": question.question_type,
                "order_sequence": question.order_sequence,
                "is_active": question.is_active,
                "created_at": question.created_at,
                "updated_at": question.updated_at
            }
            
            status_text = "activated" if question.is_active else "deactivated"  # Fixed: Use is_active instead of status
            
            return success_response(
                details=response_data,
                title="Question Status Toggled",
                description=f"Question {status_text} successfully"
            )
        except Exception as e:
            logger.error(f"Error in toggle_question_status: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Toggle Question Status",
                description="An error occurred while toggling the question status"
            )

    async def get_questions_by_franchisor(self, franchisor_id: uuid.UUID) -> Dict[str, Any]:
        """
        Get all questions for a specific franchisor.
        
        Args:
            franchisor_id: Franchisor ID
            
        Returns:
            Success response with questions list
        """
        try:
            # Validate franchisor exists
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor_ids = [f["id"] for f in franchisors]
            print("franchisor_ids", franchisor_id)
            if franchisor_id not in franchisor_ids:
                print("franchisor_id not in franchisor_ids")
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Franchisor",
                    description="The specified franchisor does not exist"
                )
            print("franchisor_id", franchisor_id)
            questions = await self.repository.get_questions_by_franchisor(franchisor_id)
            print("questions", questions)
            return success_response(
                details={"questions": questions},
                title="Questions Retrieved",
                description=f"Successfully retrieved {len(questions)} questions for franchisor"
            )
        except Exception as e:
            logger.error(f"Error in get_questions_by_franchisor: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Retrieve Questions",
                description="An error occurred while fetching questions"
            )

    async def get_all_questions_paginated(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        franchisor_id: Optional[uuid.UUID] = None,
        is_active: Optional[str] = None,
        sort_by: Optional[str] = "created_at",
        sort_order: Optional[str] = "desc"
    ) -> Dict[str, Any]:
        """
        Get all questions with pagination, filtering, and sorting.

        Args:
            page: Page number
            size: Page size
            search: Search term
            franchisor_id: Filter by franchisor ID
            is_active: Filter by active status
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            Success response with paginated questions
        """
        try:
            result = await self.repository.get_all_questions_paginated(
                page=page,
                size=size,
                search=search,
                franchisor_id=franchisor_id,
                is_active=is_active,
                sort_by=sort_by,
                sort_order=sort_order
            )
            
            return success_response(
                details=result,
                title="Questions Retrieved",
                description=f"Successfully retrieved {len(result['items'])} questions"
            )
        except Exception as e:
            logger.error(f"Error in get_all_questions_paginated: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Retrieve Questions",
                description="An error occurred while fetching questions"
            )

    async def reorder_questions(self, reorder_data: QuestionReorderRequest) -> Dict[str, Any]:
        """
        Reorder questions for a specific franchisor.
        
        Args:
            reorder_data: Reorder request data
            
        Returns:
            Success response indicating reorder completion
        """
        try:
            # Validate franchisor exists
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor_ids = [f["id"] for f in franchisors]
            
            if reorder_data.franchisor_id not in franchisor_ids:
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Franchisor",
                    description="The specified franchisor does not exist"
                )
            
            # Convert to repository format
            question_orders = [
                {"question_id": item.question_id, "order": item.order_sequence}
                for item in reorder_data.questions
            ]
            
            # Validate order numbers are unique and sequential
            orders = [item["order"] for item in question_orders]

            # Check for duplicates
            if len(orders) != len(set(orders)):
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Duplicate Order Numbers",
                    description="Order numbers must be unique. Each question must have a different order position."
                )

            # Check for sequential ordering (no gaps)
            sorted_orders = sorted(orders)
            expected_orders = list(range(1, len(orders) + 1))

            if sorted_orders != expected_orders:
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Order Sequence",
                    description=f"Order numbers must be sequential starting from 1. Expected: {expected_orders}, Got: {sorted_orders}"
                )

            # Verify that all questions belong to the specified franchisor
            question_ids = [item["question_id"] for item in question_orders]
            existing_questions = await self.repository.get_questions_by_franchisor(reorder_data.franchisor_id)
            existing_question_ids = {q["id"] for q in existing_questions}

            provided_question_ids = set(question_ids)

            # Check if all provided question IDs exist for this franchisor
            if not provided_question_ids.issubset(existing_question_ids):
                invalid_ids = provided_question_ids - existing_question_ids
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Question IDs",
                    description=f"Some questions do not belong to this franchisor: {list(invalid_ids)}"
                )

            # Check if all existing questions are included in the reorder request
            if len(provided_question_ids) != len(existing_question_ids):
                missing_count = len(existing_question_ids) - len(provided_question_ids)
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Incomplete Reorder",
                    description=f"All {len(existing_question_ids)} questions must be included in reorder request. Missing {missing_count} questions."
                )
            
            # Reorder questions
            success = await self.repository.reorder_questions(
                reorder_data.franchisor_id,
                question_orders
            )
            
            if not success:
                return error_response(
                    error_code=ErrorCodes.UNKNOWN_ERROR,
                    title="Reorder Failed",
                    description="Failed to reorder questions"
                )
            
            return success_response(
                details={
                    "franchisor_id": reorder_data.franchisor_id,
                    "reordered_questions": len(question_orders)
                },
                title="Questions Reordered",
                description=f"Successfully reordered {len(question_orders)} questions"
            )
        except ValueError as e:
            return error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                title="Validation Error",
                description=str(e)
            )
        except Exception as e:
            logger.error(f"Error in reorder_questions: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Reorder Questions",
                description="An error occurred while reordering questions"
            )

    async def validate_question_order_integrity(self, franchisor_id: uuid.UUID) -> Dict[str, Any]:
        """
        Validate order sequence integrity for a franchisor's questions.

        Args:
            franchisor_id: Franchisor ID to validate

        Returns:
            Validation results with success/error response
        """
        try:
            # Validate franchisor exists
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor_ids = [f["id"] for f in franchisors]

            if franchisor_id not in franchisor_ids:
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Franchisor",
                    description="The specified franchisor does not exist"
                )

            validation_result = await self.repository.validate_order_sequence_integrity(franchisor_id)

            if validation_result["is_valid"]:
                return success_response(
                    details=validation_result,
                    title="Order Sequence Valid",
                    description=f"All {validation_result['total_questions']} questions have valid sequential order numbers"
                )
            else:
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Order Sequence Issues Found",
                    description=f"Found {len(validation_result['issues'])} issues with question order sequence",
                    details=validation_result
                )

        except Exception as e:
            logger.error(f"Error validating question order integrity: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Validation Failed",
                description="An error occurred while validating question order integrity"
            )

    async def fix_question_order_integrity(self, franchisor_id: uuid.UUID) -> Dict[str, Any]:
        """
        Fix order sequence integrity for a franchisor's questions.

        Args:
            franchisor_id: Franchisor ID to fix

        Returns:
            Fix results with success/error response
        """
        try:
            # Validate franchisor exists
            franchisors = await self.repository.get_franchisors_for_dropdown()
            franchisor_ids = [f["id"] for f in franchisors]

            if franchisor_id not in franchisor_ids:
                return error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    title="Invalid Franchisor",
                    description="The specified franchisor does not exist"
                )

            # Check current state
            validation_before = await self.repository.validate_order_sequence_integrity(franchisor_id)

            if validation_before["is_valid"]:
                return success_response(
                    details=validation_before,
                    title="No Fix Needed",
                    description="Question order sequence is already valid"
                )

            # Fix the issues
            success = await self.repository.fix_order_sequence_gaps(franchisor_id)

            if not success:
                return error_response(
                    error_code=ErrorCodes.UNKNOWN_ERROR,
                    title="Fix Failed",
                    description="Failed to fix question order sequence"
                )

            # Validate after fix
            validation_after = await self.repository.validate_order_sequence_integrity(franchisor_id)

            return success_response(
                details={
                    "before": validation_before,
                    "after": validation_after,
                    "fixed": True
                },
                title="Order Sequence Fixed",
                description=f"Successfully fixed order sequence for {validation_after['total_questions']} questions"
            )

        except Exception as e:
            logger.error(f"Error fixing question order integrity: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Fix Failed",
                description="An error occurred while fixing question order integrity"
            )