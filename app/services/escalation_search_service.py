"""
Escalation Search Service
Handles search and retrieval of escalation questions for Andy AI
"""

import structlog
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from datetime import datetime

from app.core.database.connection import get_db
from app.models.escalation_question import EscalationQuestion

from app.core.logging import logger


class EscalationSearchService:
    """
    Service for searching and managing escalation questions
    """
    
    def __init__(self, db_session: Optional[AsyncSession] = None):
        self.db_session = db_session
        self.confidence_threshold = 0.8
        
        logger.info("Escalation search service initialized")
    
    async def search_similar_questions(
        self, 
        query: str, 
        confidence_threshold: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar escalation questions
        
        Args:
            query: Search query
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            List of similar questions with confidence scores
        """
        try:
            threshold = confidence_threshold or self.confidence_threshold
            
            # Get database session
            if self.db_session:
                session = self.db_session
            else:
                async with get_db() as session:
                    return await self._perform_search(session, query, threshold)
            
            return await self._perform_search(session, query, threshold)
            
        except Exception as e:
            logger.error(f"Error searching similar questions: {str(e)}")
            return []
    
    async def _perform_search(
        self, 
        session: AsyncSession, 
        query: str, 
        threshold: float
    ) -> List[Dict[str, Any]]:
        """
        Perform the actual search in database
        
        Args:
            session: Database session
            query: Search query
            threshold: Confidence threshold
            
        Returns:
            List of matching questions
        """
        try:
            # Simple text-based search for now
            # In production, this would use vector similarity search
            stmt = select(EscalationQuestion).where(
                and_(
                    EscalationQuestion.is_active == True,
                    EscalationQuestion.is_deleted == False,
                    or_(
                        EscalationQuestion.question.ilike(f"%{query}%"),
                        EscalationQuestion.answer.ilike(f"%{query}%")
                    )
                )
            ).order_by(EscalationQuestion.created_at.desc()).limit(10)
            
            result = await session.execute(stmt)
            questions = result.scalars().all()
            
            # Format results
            formatted_results = []
            for question in questions:
                formatted_results.append({
                    "id": str(question.id),
                    "question": question.question,
                    "answer": question.answer,
                    "confidence_score": question.confidence_score,
                    "status": question.status,
                    "created_at": question.created_at.isoformat(),
                    "similarity_score": self._calculate_similarity(query, question.question)
                })
            
            # Sort by similarity score
            formatted_results.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            # Filter by threshold
            filtered_results = [
                result for result in formatted_results 
                if result["similarity_score"] >= threshold
            ]
            
            logger.info(
                f"Found {len(filtered_results)} similar questions",
                query_length=len(query),
                threshold=threshold
            )
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"Error performing escalation search: {str(e)}")
            return []
    
    def _calculate_similarity(self, query: str, question: str) -> float:
        """
        Calculate similarity between query and question
        Simple implementation for testing - in production would use embeddings
        
        Args:
            query: Search query
            question: Question to compare
            
        Returns:
            Similarity score between 0 and 1
        """
        try:
            query_lower = query.lower()
            question_lower = question.lower()
            
            # Simple word overlap similarity
            query_words = set(query_lower.split())
            question_words = set(question_lower.split())
            
            if not query_words or not question_words:
                return 0.0
            
            intersection = query_words.intersection(question_words)
            union = query_words.union(question_words)
            
            # Jaccard similarity
            similarity = len(intersection) / len(union) if union else 0.0
            
            # Boost for exact substring matches
            if query_lower in question_lower or question_lower in query_lower:
                similarity = min(1.0, similarity + 0.3)
            
            return similarity
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0
    
    async def get_escalation_by_id(self, escalation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get escalation question by ID
        
        Args:
            escalation_id: Escalation question ID
            
        Returns:
            Escalation question data or None
        """
        try:
            if self.db_session:
                session = self.db_session
            else:
                async with get_db() as session:
                    return await self._get_by_id(session, escalation_id)
            
            return await self._get_by_id(session, escalation_id)
            
        except Exception as e:
            logger.error(f"Error getting escalation by ID: {str(e)}")
            return None
    
    async def _get_by_id(self, session: AsyncSession, escalation_id: str) -> Optional[Dict[str, Any]]:
        """Get escalation by ID from database"""
        try:
            stmt = select(EscalationQuestion).where(
                and_(
                    EscalationQuestion.id == escalation_id,
                    EscalationQuestion.is_active == True,
                    EscalationQuestion.is_deleted == False
                )
            )
            
            result = await session.execute(stmt)
            question = result.scalar_one_or_none()
            
            if not question:
                return None
            
            return {
                "id": str(question.id),
                "question": question.question,
                "answer": question.answer,
                "confidence_score": question.confidence_score,
                "status": question.status,
                "lead_id": str(question.lead_id) if question.lead_id else None,
                "created_at": question.created_at.isoformat(),
                "updated_at": question.updated_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting escalation from database: {str(e)}")
            return None
    
    async def create_escalation_question(
        self, 
        question: str, 
        lead_id: Optional[str] = None,
        confidence_score: float = 0.0
    ) -> Optional[Dict[str, Any]]:
        """
        Create new escalation question
        
        Args:
            question: Question text
            lead_id: Associated lead ID
            confidence_score: Initial confidence score
            
        Returns:
            Created escalation question data
        """
        try:
            if self.db_session:
                session = self.db_session
            else:
                async with get_db() as session:
                    return await self._create_escalation(session, question, lead_id, confidence_score)
            
            return await self._create_escalation(session, question, lead_id, confidence_score)
            
        except Exception as e:
            logger.error(f"Error creating escalation question: {str(e)}")
            return None
    
    async def _create_escalation(
        self, 
        session: AsyncSession, 
        question: str, 
        lead_id: Optional[str], 
        confidence_score: float
    ) -> Optional[Dict[str, Any]]:
        """Create escalation in database"""
        try:
            escalation = EscalationQuestion(
                question=question,
                lead_id=lead_id,
                confidence_score=confidence_score,
                status="pending"
            )
            
            session.add(escalation)
            await session.commit()
            await session.refresh(escalation)
            
            logger.info(f"Created escalation question: {escalation.id}")
            
            return {
                "id": str(escalation.id),
                "question": escalation.question,
                "confidence_score": escalation.confidence_score,
                "status": escalation.status,
                "lead_id": str(escalation.lead_id) if escalation.lead_id else None,
                "created_at": escalation.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating escalation in database: {str(e)}")
            await session.rollback()
            return None
    
    async def update_escalation_answer(
        self, 
        escalation_id: str, 
        answer: str
    ) -> bool:
        """
        Update escalation question with answer
        
        Args:
            escalation_id: Escalation question ID
            answer: Answer text
            
        Returns:
            True if updated successfully
        """
        try:
            if self.db_session:
                session = self.db_session
            else:
                async with get_db() as session:
                    return await self._update_answer(session, escalation_id, answer)
            
            return await self._update_answer(session, escalation_id, answer)
            
        except Exception as e:
            logger.error(f"Error updating escalation answer: {str(e)}")
            return False
    
    async def _update_answer(self, session: AsyncSession, escalation_id: str, answer: str) -> bool:
        """Update answer in database"""
        try:
            stmt = select(EscalationQuestion).where(
                EscalationQuestion.id == escalation_id
            )
            
            result = await session.execute(stmt)
            question = result.scalar_one_or_none()
            
            if not question:
                return False
            
            question.answer = answer
            question.status = "answered"
            question.updated_at = datetime.utcnow()
            
            await session.commit()
            
            logger.info(f"Updated escalation answer: {escalation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating answer in database: {str(e)}")
            await session.rollback()
            return False


# Global service instance
escalation_search_service = EscalationSearchService()


# Export main components
__all__ = [
    "EscalationSearchService",
    "escalation_search_service"
]
