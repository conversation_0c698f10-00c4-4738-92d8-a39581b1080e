"""
Session manager service for handling user sessions
"""
from datetime import datetime, timezone
from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.session import Session

class SessionManager:
    """Session manager service for handling user sessions"""

    async def create_session(
        self,
        user_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        db: AsyncSession = None
    ) -> Session:
        """Create a new session for a user"""
        session = Session(
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            is_active=True,
            last_activity=datetime.now(timezone.utc)
        )
        db.add(session)
        await db.commit()
        await db.refresh(session)
        return session

    async def end_session(
        self,
        user_id: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        db: AsyncSession = None
    ) -> bool:
        """End a specific session for a user"""
        stmt = select(Session).where(
            Session.user_id == user_id,
            Session.ip_address == ip_address,
            Session.user_agent == user_agent,
            Session.is_active
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()

        if session:
            session.is_active = False
            await db.commit()
            return True
        return False

    async def end_session_by_id(
        self,
        session_id: int,
        user_id: int,
        db: AsyncSession = None
    ) -> bool:
        """End a session by its ID"""
        stmt = select(Session).where(
            Session.id == session_id,
            Session.user_id == user_id,
            Session.is_active
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()

        if session:
            session.is_active = False
            await db.commit()
            return True
        return False

    async def get_user_sessions(
        self,
        user_id: int,
        db: AsyncSession = None
    ) -> List[Session]:
        """Get all active sessions for a user"""
        stmt = select(Session).where(
            Session.user_id == user_id,
            Session.is_active
        ).order_by(Session.last_activity.desc())
        result = await db.execute(stmt)
        return result.scalars().all()

    async def update_session_activity(
        self,
        session_id: int,
        db: AsyncSession = None
    ) -> bool:
        """Update the last activity timestamp for a session"""
        stmt = select(Session).where(
            Session.id == session_id,
            Session.is_active
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()

        if session:
            session.last_activity = datetime.now(timezone.utc)
            await db.commit()
            return True
        return False

    async def cleanup_expired_sessions(
        self,
        db: AsyncSession = None
    ) -> int:
        """Clean up expired sessions"""
        # Get sessions that haven't been active for more than 24 hours
        expiry_time = datetime.now(timezone.utc) - datetime.timedelta(hours=24)
        stmt = select(Session).where(
            Session.is_active,
            Session.last_activity < expiry_time
        )
        result = await db.execute(stmt)
        expired_sessions = result.scalars().all()

        for session in expired_sessions:
            session.is_active = False

        await db.commit()
        return len(expired_sessions) 