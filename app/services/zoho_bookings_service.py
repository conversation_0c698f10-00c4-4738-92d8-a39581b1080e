"""
Zoho Bookings Service
Handles integration with Zoho Bookings API for appointment scheduling
"""

import asyncio
import aiohttp
import json
import pytz
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from app.core.config.settings import settings
from app.core.logging import logger


@dataclass
class BookingSlot:
    """Represents an available booking slot"""
    staff_id: str
    staff_name: str
    start_time: datetime
    end_time: datetime
    service_id: str
    service_name: str
    duration_minutes: int
    booking_url: str


@dataclass
class BookingResult:
    """Represents a booking result"""
    success: bool
    booking_id: Optional[str] = None
    booking_url: Optional[str] = None
    meeting_link: Optional[str] = None
    staff_name: Optional[str] = None
    error_message: Optional[str] = None


class ZohoBookingsService:
    """Service for Zoho Bookings API integration with enhanced meeting agent support"""

    # Class-level cache for access token
    _access_token_cache = None
    _token_expires_at = None
    _staff_timezone_cache = {}  # Cache for staff timezones
    _service_cache = {}  # Cache for service details
    
    def __init__(self):
        self.client_id = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_ID', '') or settings.ZOHO_CLIENT_ID
        self.client_secret = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_SECRET', '') or settings.ZOHO_CLIENT_SECRET
        self.refresh_token = getattr(settings, 'ZOHO_BOOKINGS_REFRESH_TOKEN', '')
        self.base_url = getattr(settings, 'ZOHO_BOOKINGS_BASE_URL', 'https://www.zohoapis.com.au/bookings/v1/json')
        self.auth_url = getattr(settings, 'ZOHO_BOOKINGS_AUTH_URL', 'https://accounts.zoho.com.au/oauth/v2/token')
        self.access_token = getattr(settings, 'ZOHO_BOOKINGS_ACCESS_TOKEN', None)
        self.token_expires_at = None

        # Actual service IDs from your Zoho Bookings setup
        self.lead_meeting_service_id = "*****************"  # Lead Meeting (30 mins)
        self.workspace_id = "*****************"  # Growthhive workspace

        # Enhanced logging
        logger.info("🔧 Initializing Zoho Bookings Service")
        logger.info(f"   Base URL: {self.base_url}")
        logger.info(f"   Auth URL: {self.auth_url}")
        logger.info(f"   Client ID: {self.client_id[:20]}..." if self.client_id else "   Client ID: Not configured")
        logger.info(f"   Refresh Token: {'✅ Configured' if self.refresh_token else '❌ Missing'}")
        logger.info(f"   Access Token: {'✅ Available' if self.access_token else '❌ Will be generated'}")
        logger.info(f"   Lead Meeting Service ID: {self.lead_meeting_service_id}")
        logger.info(f"   Workspace ID: {self.workspace_id}")
        
        # Actual sales team configuration from your Zoho Bookings
        self.sales_team = {
            "saumil": {
                "name": "Saumil",
                "email": "<EMAIL>",
                "zoho_staff_id": "26044000000040008",
                "timezone": "Australia/Sydney",
                "specialties": ["franchise_consultation", "business_planning"]
            },
            "frank": {
                "name": "Frank",
                "email": "<EMAIL>",
                "zoho_staff_id": "26044000000186094",
                "timezone": "Australia/Sydney",
                "specialties": ["investment_planning", "roi_analysis"]
            },
            "andy": {
                "name": "Andy",
                "email": "<EMAIL>",
                "zoho_staff_id": "26044000000000000",  # Will be updated dynamically
                "timezone": "Australia/Sydney",
                "specialties": ["business_development", "client_relations"]
            }
        }

        # Dynamic staff list (will be populated from Zoho API)
        self._dynamic_staff_list = []
        
        # Actual service types from your Zoho Bookings
        self.services = {
            "lead_meeting": {
                "name": "Lead Meeting",
                "duration": 30,
                "zoho_service_id": "*****************"
            },
            "saumil_consultation": {
                "name": "Franchise Consultation with Saumil",
                "duration": 20,
                "zoho_service_id": "26044000000040220"
            },
            "frank_consultation": {
                "name": "Franchise Consultation with Frank",
                "duration": 20,
                "zoho_service_id": "26044000000326012"
            }
        }

    async def get_access_token(self) -> str:
        """Get or refresh access token"""
        logger.debug("🔑 Checking access token status")

        # Check class-level cache first
        if ZohoBookingsService._access_token_cache and ZohoBookingsService._token_expires_at:
            if datetime.utcnow() < ZohoBookingsService._token_expires_at:
                remaining_time = (ZohoBookingsService._token_expires_at - datetime.utcnow()).total_seconds()
                logger.debug(f"✅ Using cached access token (expires in {remaining_time:.0f} seconds)")
                return ZohoBookingsService._access_token_cache

        # Check instance-level cache
        if self.access_token and self.token_expires_at:
            if datetime.utcnow() < self.token_expires_at:
                remaining_time = (self.token_expires_at - datetime.utcnow()).total_seconds()
                logger.debug(f"✅ Using existing access token (expires in {remaining_time:.0f} seconds)")
                return self.access_token

        logger.info("🔄 Refreshing Zoho Bookings access token")

        # Validate required configuration
        if not self.refresh_token:
            error_msg = "Zoho Bookings refresh token is not configured"
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)
        
        if not self.client_id or not self.client_secret:
            error_msg = "Zoho Bookings client credentials are not configured"
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)

        # Refresh token
        token_data = {
            'refresh_token': self.refresh_token,
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'refresh_token'
        }

        try:
            logger.debug(f"📤 Sending token refresh request to: {self.auth_url}")
            async with aiohttp.ClientSession() as session:
                async with session.post(self.auth_url, data=token_data) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        try:
                            token_info = await response.json()
                        except Exception as json_error:
                            error_msg = f"Failed to parse token response JSON: {json_error}"
                            logger.error(f"❌ {error_msg}")
                            raise ValueError(error_msg)
                        
                        if 'access_token' not in token_info:
                            error_msg = "Access token not found in response"
                            logger.error(f"❌ {error_msg}")
                            logger.error(f"   Response: {token_info}")
                            raise ValueError(error_msg)
                        
                        # Store in both class-level and instance-level cache
                        access_token = token_info['access_token']
                        expires_in = token_info.get('expires_in', 3600)
                        expires_at = datetime.utcnow() + timedelta(seconds=expires_in - 300)
                        
                        # Update class-level cache
                        ZohoBookingsService._access_token_cache = access_token
                        ZohoBookingsService._token_expires_at = expires_at
                        
                        # Update instance-level cache
                        self.access_token = access_token
                        self.token_expires_at = expires_at

                        logger.info("✅ Zoho Bookings access token refreshed successfully")
                        logger.debug(f"   Token expires in: {expires_in} seconds")
                        logger.debug(f"   Scope: {token_info.get('scope', 'Not provided')}")
                        logger.debug(f"   API Domain: {token_info.get('api_domain', 'Not provided')}")

                        return access_token
                    else:
                        error_msg = f"Token refresh failed with status {response.status}"
                        logger.error(f"❌ {error_msg}")
                        logger.error(f"   Response: {response_text}")
                        
                        # Try to parse error response
                        try:
                            error_data = json.loads(response_text)
                            if 'error' in error_data:
                                error_msg = f"Token refresh failed: {error_data['error']}"
                                if 'error_description' in error_data:
                                    error_msg += f" - {error_data['error_description']}"
                        except:
                            pass
                        
                        raise ValueError(error_msg)
        except aiohttp.ClientError as e:
            error_msg = f"Network error during token refresh: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise ConnectionError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error during token refresh: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise

    async def _make_api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """Make authenticated API request to Zoho Bookings"""
        try:
            access_token = await self.get_access_token()
        except (ValueError, ConnectionError) as e:
            error_msg = f"Failed to obtain access token: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)
        
        url = f"{self.base_url}/{endpoint}"

        headers = {
            'Authorization': f'Zoho-oauthtoken {access_token}'
        }

        logger.debug(f"📤 Making {method} request to Zoho Bookings API")
        logger.debug(f"   URL: {url}")
        logger.debug(f"   Headers: {dict(headers)}")
        if data:
            logger.debug(f"   Data: {data}")

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=600)) as session:
                start_time = datetime.utcnow()

                if method == "GET":
                    async with session.get(url, headers=headers) as response:
                        response_time = (datetime.utcnow() - start_time).total_seconds()
                        response_text = await response.text()

                        logger.debug(f"📥 Received response in {response_time:.2f}s")
                        logger.debug(f"   Status: {response.status}")
                        logger.debug(f"   Content-Type: {response.headers.get('Content-Type', 'Not provided')}")
                        logger.debug(f"   Content-Length: {len(response_text)} chars")

                        if response.status == 200:
                            try:
                                result = await response.json(content_type=None)  # Allow any content type
                                logger.info(f"✅ {method} {endpoint} - Success ({response_time:.2f}s)")
                                return result
                            except Exception as e:
                                logger.warning(f"⚠️ JSON parsing error, trying manual parse: {e}")
                                # Try to parse manually
                                try:
                                    import json
                                    result = json.loads(response_text)
                                    logger.info(f"✅ {method} {endpoint} - Success with manual JSON parse ({response_time:.2f}s)")
                                    return result
                                except json.JSONDecodeError as json_error:
                                    error_msg = f"Failed to parse API response as JSON: {json_error}"
                                    logger.error(f"❌ {error_msg}")
                                    logger.error(f"   Response text: {response_text[:500]}...")
                                    raise ValueError(error_msg)
                        else:
                            error_msg = f"API request failed with status {response.status}"
                            logger.error(f"❌ {method} {endpoint} - {error_msg}")
                            logger.error(f"   Response: {response_text[:500]}...")
                            
                            # Try to parse error response for better error message
                            try:
                                error_data = json.loads(response_text)
                                if 'response' in error_data and 'errormessage' in error_data['response']:
                                    error_msg = f"Zoho API Error: {error_data['response']['errormessage']}"
                                elif 'error' in error_data:
                                    error_msg = f"Zoho API Error: {error_data['error']}"
                            except:
                                pass
                            
                            raise ValueError(error_msg)

                elif method == "POST":
                    # Zoho Bookings expects form data for POST requests
                    async with session.post(url, headers=headers, data=data) as response:
                        response_time = (datetime.utcnow() - start_time).total_seconds()
                        response_text = await response.text()

                        logger.debug(f"📥 Received POST response in {response_time:.2f}s")
                        logger.debug(f"   Status: {response.status}")
                        logger.debug(f"   Content-Type: {response.headers.get('Content-Type', 'Not provided')}")
                        logger.debug(f"   Content-Length: {len(response_text)} chars")

                        if response.status == 200:
                            try:
                                result = await response.json(content_type=None)  # Allow any content type
                                logger.info(f"✅ {method} {endpoint} - Success ({response_time:.2f}s)")
                                return result
                            except Exception as e:
                                logger.warning(f"⚠️ JSON parsing error, trying manual parse: {e}")
                                # Try to parse manually
                                try:
                                    import json
                                    result = json.loads(response_text)
                                    logger.info(f"✅ {method} {endpoint} - Success with manual JSON parse ({response_time:.2f}s)")
                                    return result
                                except json.JSONDecodeError as json_error:
                                    error_msg = f"Failed to parse API response as JSON: {json_error}"
                                    logger.error(f"❌ {error_msg}")
                                    logger.error(f"   Response text: {response_text[:500]}...")
                                    raise ValueError(error_msg)
                        else:
                            error_msg = f"API request failed with status {response.status}"
                            logger.error(f"❌ {method} {endpoint} - {error_msg}")
                            logger.error(f"   Response: {response_text[:500]}...")
                            
                            # Try to parse error response for better error message
                            try:
                                error_data = json.loads(response_text)
                                if 'response' in error_data and 'errormessage' in error_data['response']:
                                    error_msg = f"Zoho API Error: {error_data['response']['errormessage']}"
                                elif 'error' in error_data:
                                    error_msg = f"Zoho API Error: {error_data['error']}"
                            except:
                                pass
                            
                            raise ValueError(error_msg)

        except aiohttp.ClientError as e:
            error_msg = f"Network error during API request to {endpoint}: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise ConnectionError(error_msg)
        except asyncio.TimeoutError as e:
            error_msg = f"Request timeout for {endpoint}: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise TimeoutError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error making {method} request to {endpoint}: {str(e)}"
            logger.error(f"❌ {error_msg}")
            raise

    async def get_available_slots(
        self,
        date_from: datetime,
        date_to: datetime,
        service_type: str = "lead_meeting",
        preferred_staff: Optional[str] = None
    ) -> List[BookingSlot]:
        """Get available booking slots for the specified date range"""
        logger.info(f"🔍 Checking availability for {service_type}")
        logger.info(f"   Date range: {date_from.strftime('%Y-%m-%d')} to {date_to.strftime('%Y-%m-%d')}")
        logger.info(f"   Preferred staff: {preferred_staff or 'Any'}")

        try:
            # Validate service type
            if service_type not in self.services:
                error_msg = f"Invalid service type '{service_type}'. Available services: {list(self.services.keys())}"
                logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)
            
            # Validate date range
            if date_from > date_to:
                error_msg = f"Invalid date range: start date ({date_from}) is after end date ({date_to})"
                logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)
            
            # Check if dates are in the past
            now = datetime.now()
            
            # Convert date_from to naive datetime for comparison if it's timezone-aware
            date_from_naive = date_from
            if date_from.tzinfo is not None:
                date_from_naive = date_from.replace(tzinfo=None)
            
            if date_from_naive < now.replace(hour=0, minute=0, second=0, microsecond=0):
                logger.warning(f"⚠️ Start date {date_from.strftime('%Y-%m-%d')} is in the past")
            
            service_config = self.services.get(service_type, self.services["lead_meeting"])
            logger.debug(f"   Using service: {service_config['name']} (ID: {service_config['zoho_service_id']})")

            all_slots = []

            # Zoho Bookings API requires single date queries, so we iterate through each date
            current_date = date_from
            while current_date <= date_to:
                logger.debug(f"   Checking availability for {current_date.strftime('%d-%b-%Y')}")

                # Ensure current_date is naive for string formatting
                current_date_naive = current_date
                if current_date.tzinfo is not None:
                    current_date_naive = current_date.replace(tzinfo=None)

                # Build query parameters for Zoho Bookings API (correct format)
                params = {
                    'service_id': service_config["zoho_service_id"],
                    'selected_date': current_date_naive.strftime('%d-%b-%Y')  # Zoho format: dd-MMM-yyyy
                }

                if preferred_staff and preferred_staff in self.sales_team:
                    staff_id = self.sales_team[preferred_staff]["zoho_staff_id"]
                    params['staff_id'] = staff_id
                    logger.debug(f"   Filtering by staff: {preferred_staff} (ID: {staff_id})")
                elif preferred_staff:
                    logger.warning(f"   ⚠️ Preferred staff '{preferred_staff}' not found in sales team")

                logger.debug(f"   Query parameters: {params}")

                try:
                    # Make API request to get availability
                    endpoint = f"availableslots?{self._build_query_string(params)}"
                    logger.debug(f"   Making API request to: {endpoint}")
                    response = await self._make_api_request("GET", endpoint)
                    logger.debug(f"   API response received: {response}")

                    # Parse response and create BookingSlot objects for this date
                    if response.get('response', {}).get('status') == 'success':
                        slot_times = response['response']['returnvalue'].get('data', [])
                        logger.debug(f"   Raw slot times for {current_date_naive.strftime('%d-%b-%Y')}: {slot_times}")

                        for time_slot in slot_times:
                            try:
                                # Parse time slot (format: "HH:MM AM/PM" like "10:00 AM", "02:30 PM")
                                if isinstance(time_slot, str) and ':' in time_slot:
                                    # Combine date and time - ensure we use naive date
                                    start_time_str = f"{current_date_naive.strftime('%d-%b-%Y')} {time_slot}"
                                    logger.debug(f"   Parsing time slot: {start_time_str}")

                                    # Try different time formats
                                    time_formats = [
                                        '%d-%b-%Y %I:%M %p',  # "07-Aug-2025 10:00 AM"
                                        '%d-%b-%Y %H:%M',     # "07-Aug-2025 10:00" (24-hour)
                                        '%d-%b-%Y %I:%M%p',   # "07-Aug-2025 10:00AM" (no space)
                                    ]

                                    start_time = None
                                    for time_format in time_formats:
                                        try:
                                            # Always create naive datetime objects
                                            start_time = datetime.strptime(start_time_str, time_format)
                                            # Ensure it's naive
                                            if start_time.tzinfo is not None:
                                                start_time = start_time.replace(tzinfo=None)
                                            logger.debug(f"   Successfully parsed with format {time_format}: {start_time}")
                                            break
                                        except ValueError:
                                            continue

                                    if start_time is None:
                                        logger.warning(f"   ⚠️ Could not parse time format: {time_slot}")
                                        continue

                                    # BUSINESS HOURS FILTER: Only include slots between 9 AM and 6 PM
                                    hour = start_time.hour
                                    if hour < 9 or hour >= 18:  # Before 9 AM or after 6 PM
                                        logger.debug(f"   ⏰ Skipping slot outside business hours: {start_time.strftime('%I:%M %p')}")
                                        continue

                                    # Ensure end_time is also naive
                                    end_time = start_time + timedelta(minutes=service_config["duration"])
                                    if end_time.tzinfo is not None:
                                        end_time = end_time.replace(tzinfo=None)

                                    # DYNAMIC STAFF SELECTION: Get staff from Zoho API with random selection
                                    staff_id = ""
                                    staff_name = "Available Staff"

                                    if preferred_staff and preferred_staff in self.sales_team:
                                        # Use specific requested staff
                                        staff_id = self.sales_team[preferred_staff]["zoho_staff_id"]
                                        staff_name = self.sales_team[preferred_staff]["name"]
                                    else:
                                        # RANDOM STAFF SELECTION from static list (for now)
                                        import random
                                        staff_keys = list(self.sales_team.keys())
                                        random_staff_key = random.choice(staff_keys)
                                        staff_id = self.sales_team[random_staff_key]["zoho_staff_id"]
                                        staff_name = self.sales_team[random_staff_key]["name"]
                                        logger.info(f"🎲 Randomly selected staff: {staff_name} (ID: {staff_id})")

                                    slot = BookingSlot(
                                        staff_id=staff_id,
                                        staff_name=staff_name,
                                        start_time=start_time,
                                        end_time=end_time,
                                        service_id=service_config["zoho_service_id"],
                                        service_name=service_config["name"],
                                        duration_minutes=service_config["duration"],
                                        booking_url=''
                                    )
                                    all_slots.append(slot)
                                    logger.debug(f"   ✅ Parsed slot: {start_time.strftime('%Y-%m-%d %H:%M')} with {staff_name}")

                            except Exception as slot_error:
                                logger.warning(f"   ⚠️ Failed to parse time slot: {time_slot} - Error: {slot_error}")
                                logger.debug(f"   Slot error details: {type(slot_error).__name__}: {str(slot_error)}")
                                continue
                    else:
                        error_msg = response.get('response', {}).get('errormessage', 'Unknown API error')
                        logger.warning(f"   ⚠️ API response indicates failure for {current_date_naive.strftime('%d-%b-%Y')}: {error_msg}")
                        logger.debug(f"   Response: {response}")

                except (ValueError, ConnectionError, TimeoutError) as api_error:
                    logger.error(f"   ❌ API request failed for {current_date_naive.strftime('%d-%b-%Y')}: {api_error}")
                    # Continue with other dates instead of failing completely
                    continue

                # Move to next date
                current_date += timedelta(days=1)
                # Ensure current_date remains naive for the next iteration
                if current_date.tzinfo is not None:
                    current_date = current_date.replace(tzinfo=None)

            logger.info(f"✅ Found {len(all_slots)} available slots for {service_type}")
            if all_slots:
                logger.info("   Available slots:")
                for i, slot in enumerate(all_slots[:5], 1):  # Show first 5
                    logger.info(f"     {i}. {slot.start_time.strftime('%A, %B %d at %I:%M %p')} with {slot.staff_name}")
                if len(all_slots) > 5:
                    logger.info(f"     ... and {len(all_slots) - 5} more slots")

            return all_slots

        except ValueError as e:
            logger.error(f"❌ Validation error in get_available_slots: {e}")
            raise
        except Exception as e:
            error_msg = f"Unexpected error getting available slots for {service_type}: {str(e)}"
            logger.error(f"❌ {error_msg}")
            import traceback
            logger.debug(f"   Traceback: {traceback.format_exc()}")
            raise RuntimeError(error_msg)

    async def get_available_slots_for_date(
        self,
        target_date: datetime,
        service_type: str = "lead_meeting",
        preferred_staff: Optional[str] = None
    ) -> List[BookingSlot]:
        """Get available booking slots for a specific date"""
        # Ensure target_date is naive for the API call
        target_date_naive = target_date
        if hasattr(target_date, 'tzinfo') and target_date.tzinfo is not None:
            target_date_naive = target_date.replace(tzinfo=None)
        
        return await self.get_available_slots(
            date_from=target_date_naive,
            date_to=target_date_naive,
            service_type=service_type,
            preferred_staff=preferred_staff
        )

    async def get_next_available_slots(
        self,
        max_days_ahead: int = 14,
        max_slots: int = 10,
        service_type: str = "lead_meeting",
        preferred_staff: Optional[str] = None
    ) -> List[BookingSlot]:
        """Get the next available slots within the specified number of days"""
        logger.info(f"🔍 Finding next {max_slots} available slots within {max_days_ahead} days")

        date_from = datetime.now()
        date_to = date_from + timedelta(days=max_days_ahead)

        all_slots = await self.get_available_slots(
            date_from=date_from,
            date_to=date_to,
            service_type=service_type,
            preferred_staff=preferred_staff
        )

        # Filter out past slots (in case current time is in the middle of the day)
        now = datetime.now()
        future_slots = []
        for slot in all_slots:
            # Ensure slot.start_time is naive for comparison
            slot_start_naive = slot.start_time
            if slot.start_time.tzinfo is not None:
                slot_start_naive = slot.start_time.replace(tzinfo=None)
            
            if slot_start_naive > now:
                future_slots.append(slot)

        # Return up to max_slots
        return future_slots[:max_slots]

    async def is_time_slot_available(
        self,
        target_datetime: datetime,
        service_type: str = "lead_meeting",
        preferred_staff: Optional[str] = None
    ) -> bool:
        """Check if a specific time slot is available"""
        logger.info(f"🔍 Checking if {target_datetime.strftime('%Y-%m-%d %H:%M')} is available")

        # Ensure target_datetime is naive for comparison
        target_datetime_naive = target_datetime
        if target_datetime.tzinfo is not None:
            target_datetime_naive = target_datetime.replace(tzinfo=None)

        # Get slots for the target date
        target_date = target_datetime_naive.replace(hour=0, minute=0, second=0, microsecond=0)
        available_slots = await self.get_available_slots_for_date(
            target_date=target_date,
            service_type=service_type,
            preferred_staff=preferred_staff
        )

        # Check if the specific time is in the available slots
        for slot in available_slots:
            # Ensure slot.start_time is also naive for comparison
            slot_start_naive = slot.start_time
            if slot.start_time.tzinfo is not None:
                slot_start_naive = slot.start_time.replace(tzinfo=None)
            
            if slot_start_naive == target_datetime_naive:
                logger.info(f"✅ Time slot {target_datetime_naive.strftime('%Y-%m-%d %H:%M')} is available")
                return True

        logger.info(f"❌ Time slot {target_datetime_naive.strftime('%Y-%m-%d %H:%M')} is not available")
        return False

    async def _get_lead_details(self, lead_id: str):
        """
        Get lead details from database.
        
        Args:
            lead_id: Lead ID
            
        Returns:
            Lead object or None if not found
        """
        try:
            from app.models.lead import Lead
            from app.core.database.connection import AsyncSessionLocal

            async with AsyncSessionLocal() as db:
                lead = await db.get(Lead, lead_id)
                if not lead:
                    logger.error(f"❌ Lead with ID '{lead_id}' not found in database")
                    return None
                
                # Validate lead data
                if not lead.first_name or not lead.last_name:
                    logger.error(f"❌ Lead {lead_id} is missing required name information")
                    return None
                
                if not lead.email and not lead.phone:
                    logger.error(f"❌ Lead {lead_id} is missing both email and phone contact information")
                    return None
                
                return lead
                
        except ImportError as e:
            logger.error(f"❌ Database models not available: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ Database error while fetching lead {lead_id}: {str(e)}")
            return None

    async def book_appointment_for_lead(
        self,
        lead_id: str,
        preferred_start_time: datetime,
        timezone: str = "Australia/Sydney",
        service_type: str = "lead_meeting",
        phone_number: str = None
    ) -> BookingResult:
        """
        Book an appointment for a lead at the nearest available time.
        
        Args:
            lead_id: Lead ID
            preferred_start_time: Preferred start time
            timezone: Timezone for the booking
            service_type: Type of service to book
            
        Returns:
            BookingResult with success status and details
        """
        try:
            logger.info(f"Booking request for lead {lead_id} at {preferred_start_time}")
            
            # Get lead details with fallback for database issues
            lead = await self._get_lead_details(lead_id)
            if not lead:
                logger.warning(f"⚠️ Lead not found in database: {lead_id}, using fallback details")

                # Create fallback lead details for testing/development
                from types import SimpleNamespace
                actual_phone = phone_number or "+61400000000"
                lead = SimpleNamespace(
                    id=lead_id,
                    first_name="Lead",
                    last_name="Contact",
                    email="<EMAIL>",
                    phone=actual_phone,
                    mobile=actual_phone
                )
                logger.info(f"🔄 Using fallback lead details for booking")
            
            # EXACT TIME SLOT MATCHING: Find the exact requested time
            logger.info(f"🎯 Finding EXACT time slot for: {preferred_start_time}")

            # Get available slots for the requested date
            target_date = preferred_start_time.date()
            start_of_day = datetime.combine(target_date, datetime.min.time())
            end_of_day = start_of_day + timedelta(days=1)

            available_slots = await self.get_available_slots(
                date_from=start_of_day,
                date_to=end_of_day,
                service_type=service_type
            )

            logger.info(f"🔍 Found {len(available_slots)} available slots for {target_date}")

            # Find EXACT matching slots (there might be multiple staff for same time)
            matching_slots = []
            logger.info(f"🔍 Looking for EXACT match: {preferred_start_time}")

            for i, slot in enumerate(available_slots):
                logger.info(f"🔍 Slot {i+1}: {slot.start_time} vs {preferred_start_time}")

                # Exact datetime match
                if slot.start_time == preferred_start_time:
                    matching_slots.append(slot)
                    logger.info(f"✅ Found EXACT matching slot: {slot.start_time} with {slot.staff_name}")

                # Also try time component match (same hour, minute, date)
                elif (slot.start_time.hour == preferred_start_time.hour and
                      slot.start_time.minute == preferred_start_time.minute and
                      slot.start_time.date() == preferred_start_time.date()):
                    matching_slots.append(slot)
                    logger.info(f"✅ Found EXACT time match: {slot.start_time} with {slot.staff_name}")

            # RANDOM SELECTION from matching slots
            exact_slot = None
            if matching_slots:
                import random
                exact_slot = random.choice(matching_slots)
                logger.info(f"🎲 Randomly selected from {len(matching_slots)} matching slots: {exact_slot.staff_name}")
            else:
                logger.info(f"❌ No exact matches found")

            if not exact_slot:
                # Show available slots for user to choose from
                available_times = []
                for slot in available_slots[:5]:
                    time_str = slot.start_time.strftime('%I:%M %p').replace(' 0', ' ')
                    available_times.append(time_str)

                if available_times:
                    times_list = ", ".join(available_times)
                    error_msg = f"The exact time {preferred_start_time.strftime('%I:%M %p')} is not available. Available times: {times_list}"
                else:
                    error_msg = f"No slots available for {preferred_start_time.strftime('%A, %B %d')}"

                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )

            # Use the EXACT slot found
            slot = exact_slot
            logger.info(f"✅ Booking EXACT slot: {slot.start_time} with {slot.staff_name}")

            # Book the appointment
            try:
                return await self.book_appointment(
                    slot=slot,
                    customer_name=f"{lead.first_name} {lead.last_name}",
                    customer_email=lead.email,
                    customer_phone=lead.phone,
                    notes=f"Booking for lead: {lead_id}",
                    lead_id=lead_id,
                    timezone=timezone
                )
            except Exception as e:
                error_msg = f"Failed to book appointment: {str(e)}"
                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )

        except Exception as e:
            error_msg = f"Unexpected error booking appointment for lead: {str(e)}"
            logger.error(f"❌ {error_msg}")
            import traceback
            logger.debug(f"   Traceback: {traceback.format_exc()}")
            return BookingResult(
                success=False,
                error_message=error_msg
            )

    async def book_appointment_with_exact_slot(
        self,
        slot: BookingSlot,
        lead_id: str,
        phone_number: str = None
    ) -> BookingResult:
        """
        Book an appointment using an exact slot from available slots.
        This ensures we use the correct staff and exact timing.
        """
        try:
            logger.info(f"🎯 Booking exact slot: {slot.start_time} with {slot.staff_name}")

            # Get lead details with fallback
            lead = await self._get_lead_details(lead_id)
            if not lead:
                logger.warning(f"⚠️ Lead not found in database: {lead_id}, using fallback details")
                from types import SimpleNamespace
                actual_phone = phone_number or "+61400000000"
                lead = SimpleNamespace(
                    id=lead_id,
                    first_name="Lead",
                    last_name="Contact",
                    email="<EMAIL>",
                    phone=actual_phone,
                    mobile=actual_phone
                )

            # Book the appointment using the exact slot
            return await self.book_appointment(
                slot=slot,
                customer_name=f"{lead.first_name} {lead.last_name}",
                customer_email=lead.email,
                customer_phone=lead.phone,
                notes=f"Booking for lead: {lead_id}",
                lead_id=lead_id,
                timezone="Australia/Sydney"
            )

        except Exception as e:
            error_msg = f"Error booking exact slot: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return BookingResult(
                success=False,
                error_message=error_msg
            )

    async def find_closest_available_slot(
        self,
        preferred_start_time: datetime,
        service_type: str = "consultation",
        timezone: str = "Australia/Sydney",
        search_window_hours: int = 24
    ) -> Optional[BookingSlot]:
        """Find the closest available slot to the preferred time"""
        try:
            # Ensure preferred_start_time is naive
            if preferred_start_time.tzinfo is not None:
                preferred_start_time = preferred_start_time.replace(tzinfo=None)
            
            # Search within window around preferred time
            date_from = preferred_start_time - timedelta(hours=search_window_hours//2)
            date_to = preferred_start_time + timedelta(hours=search_window_hours//2)

            logger.info(f"🔍 Searching for closest slot to {preferred_start_time.strftime('%Y-%m-%d %H:%M')}")
            logger.info(f"   Search window: {date_from.strftime('%Y-%m-%d %H:%M')} to {date_to.strftime('%Y-%m-%d %H:%M')}")

            # Get all available slots in the window
            slots = await self.get_available_slots(
                date_from=date_from,
                date_to=date_to,
                service_type=service_type
            )

            # Filter slots to only include those within working hours (09:00 AM - 06:00 PM)
            working_hours_slots = []
            for slot in slots:
                slot_hour = slot.start_time.hour
                if 9 <= slot_hour < 18:  # 09:00 AM to 06:00 PM (18:00)
                    working_hours_slots.append(slot)
            
            if not working_hours_slots:
                logger.info("   No slots found within working hours (09:00 AM - 06:00 PM)")
                return None
            
            logger.info(f"   Found {len(working_hours_slots)} slots within working hours out of {len(slots)} total slots")
            slots = working_hours_slots

            if not slots:
                logger.info("   No slots found in search window, trying to get next available slot")
                # If no slots in window, get next available
                return await self.get_next_available_slot(service_type=service_type)

            # Find slot closest to preferred time
            def time_distance(slot: BookingSlot) -> float:
                # Ensure both datetimes are naive for comparison
                slot_start_naive = slot.start_time
                if slot.start_time.tzinfo is not None:
                    slot_start_naive = slot.start_time.replace(tzinfo=None)
                
                return abs((slot_start_naive - preferred_start_time).total_seconds())

            closest_slot = min(slots, key=time_distance)
            
            # Calculate time difference
            closest_slot_naive = closest_slot.start_time
            if closest_slot.start_time.tzinfo is not None:
                closest_slot_naive = closest_slot.start_time.replace(tzinfo=None)
            
            time_diff_hours = abs((closest_slot_naive - preferred_start_time).total_seconds() / 3600)
            
            logger.info(f"✅ Found closest slot: {closest_slot.start_time.strftime('%Y-%m-%d %H:%M')} (diff: {time_diff_hours:.1f} hours)")
            return closest_slot

        except Exception as e:
            error_msg = f"Unexpected error finding closest available slot: {str(e)}"
            logger.error(f"❌ {error_msg}")
            import traceback
            logger.debug(f"   Traceback: {traceback.format_exc()}")
            return None

    async def book_appointment(
        self,
        slot: BookingSlot,
        customer_name: str,
        customer_email: str,
        customer_phone: str,
        notes: Optional[str] = None,
        lead_id: Optional[str] = None,
        timezone: str = "Australia/Sydney"
    ) -> BookingResult:
        """Book an appointment for the specified slot"""
        try:
            # Validate slot
            if not slot or not isinstance(slot, BookingSlot):
                error_msg = "Invalid slot provided"
                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )
            
            # Validate customer information
            if not customer_name or not customer_name.strip():
                error_msg = "Customer name is required"
                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )
            
            if not customer_email and not customer_phone:
                error_msg = "Either customer email or phone is required"
                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )
            
            # Validate email format if provided
            if customer_email:
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, customer_email):
                    error_msg = f"Invalid email format: {customer_email}"
                    logger.error(f"❌ {error_msg}")
                    return BookingResult(
                        success=False,
                        error_message=error_msg
                    )
            
            # Validate phone format if provided
            if customer_phone:
                # Basic phone validation - should contain digits and be reasonable length
                digits_only = ''.join(filter(str.isdigit, customer_phone))
                if len(digits_only) < 7 or len(digits_only) > 15:
                    error_msg = f"Invalid phone number format: {customer_phone}"
                    logger.error(f"❌ {error_msg}")
                    return BookingResult(
                        success=False,
                        error_message=error_msg
                    )
            
            # Check if slot is in the past
            now = datetime.now()
            slot_start_naive = slot.start_time
            if slot.start_time.tzinfo is not None:
                slot_start_naive = slot.start_time.replace(tzinfo=None)
            
            if slot_start_naive < now:
                error_msg = f"Slot time {slot_start_naive.strftime('%Y-%m-%d %H:%M')} is in the past"
                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )

            # Use the timezone from the request (let Zoho handle conversion)
            logger.info(f"   Using timezone for booking: {timezone}")
            
            # Use the EXACT format specified: 30-Apr-2019 22:00:00
            from_time = slot.start_time.strftime('%d-%b-%Y %H:%M:%S')
            logger.info(f"🎯 Using exact specified format: {from_time}")

            booking_data = {
                'service_id': slot.service_id,
                'from_time': from_time,
                'timezone': timezone,
                'customer_details': json.dumps({
                    'name': customer_name.strip(),
                    'email': customer_email.strip() if customer_email else '',
                    'phone_number': customer_phone.strip() if customer_phone else ''
                }),
                'notes': notes or f"Booking for lead: {lead_id}" if lead_id else "Booking via GrowthHive API",
                'workspace_id': self.workspace_id
            }

            # Remove empty fields to avoid API issues
            booking_data = {k: v for k, v in booking_data.items() if v}

            logger.info(f"📅 Booking appointment for {customer_name}")
            logger.info(f"   Service: {slot.service_name}")
            logger.info(f"   Time: {slot.start_time.strftime('%Y-%m-%d %H:%M')}")
            logger.info(f"   Staff: {slot.staff_name}")
            logger.info(f"   Staff ID: {slot.staff_id}")
            logger.info(f"   Service ID: {slot.service_id}")
            logger.info(f"   Staff Timezone: {timezone}")
            logger.info(f"   Booking data being sent to Zoho: {booking_data}")

            try:
                # Make the booking request
                response = await self._make_api_request(
                    method="POST",
                    endpoint="appointment",  # Revert back to appointment endpoint
                    data=booking_data
                )
                
                # Log the full response for debugging
                logger.info(f"📥 Zoho booking response: {response}")
                
            except (ValueError, ConnectionError, TimeoutError) as api_error:
                error_msg = f"API request failed: {str(api_error)}"
                logger.error(f"❌ {error_msg}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )

            # Check for success in the response
            if response.get('response', {}).get('status') == 'success':
                booking_info = response['response']['returnvalue']
                logger.info(f"📋 Booking info from Zoho: {booking_info}")
                logger.info(f"📋 Response structure: {type(booking_info)}")
                
                # Check if the inner status indicates failure
                if isinstance(booking_info, dict) and booking_info.get('status') == 'failure':
                    error_msg = booking_info.get('message', 'Inner booking failure')
                    logger.error(f"❌ Inner booking failure: {error_msg}")
                    return BookingResult(
                        success=False,
                        error_message=error_msg
                    )
                
                # Extract booking details
                booking_id = None
                booking_url = None
                meeting_link = None
                staff_name = None

                if isinstance(booking_info, dict):
                    # Try different possible field names for booking ID
                    booking_id = (booking_info.get('booking_id') or
                                 booking_info.get('id') or
                                 booking_info.get('appointment_id') or
                                 booking_info.get('bookingId'))
                    booking_url = (booking_info.get('summary_url') or
                                  booking_info.get('booking_url') or
                                  booking_info.get('url'))
                    meeting_link = (booking_info.get('meeting_link') or
                                   booking_info.get('meeting_url') or
                                   booking_info.get('link'))
                    staff_name = booking_info.get('staff_name')
                    logger.info(f"📋 Extracted booking_id: {booking_id}")
                    logger.info(f"📋 Extracted booking_url: {booking_url}")
                    logger.info(f"📋 Extracted meeting_link: {meeting_link}")
                    logger.info(f"📋 Extracted staff_name: {staff_name}")
                elif isinstance(booking_info, str):
                    booking_id = booking_info
                    logger.info(f"📋 Booking ID (string): {booking_id}")
                else:
                    logger.warning(f"⚠️ Unexpected booking_info type: {type(booking_info)}")
                    logger.warning(f"⚠️ Booking info content: {booking_info}")
                
                # Check if we actually got a booking ID
                if not booking_id:
                    # Try to check if the slot is still available (indicating booking failed)
                    logger.info("🔍 Checking if slot is still available to verify booking status...")
                    try:
                        slot_still_available = await self.is_time_slot_available(
                            target_datetime=slot.start_time,
                            service_type=slot.service_name.lower().replace(' ', '_'),
                            preferred_staff=None
                        )
                        if slot_still_available:
                            error_msg = "Zoho booking succeeded but slot is still available - booking may not have been created"
                            logger.error(f"❌ {error_msg}")
                            return BookingResult(
                                success=False,
                                error_message=error_msg
                            )
                        else:
                            logger.info("✅ Slot is no longer available - booking was likely successful")

                            # Follow-up functionality has been removed from Andy AI
                            logger.info("Meeting booked successfully - follow-up system disabled")

                            # Even without booking ID, if slot is no longer available, booking was successful
                            return BookingResult(
                                success=True,
                                booking_id="BOOKING_CREATED_NO_ID",  # Placeholder
                                booking_url=booking_url,
                                meeting_link=meeting_link
                            )
                    except Exception as check_error:
                        logger.warning(f"⚠️ Could not verify slot availability: {check_error}")
                        error_msg = "Zoho booking succeeded but no booking ID returned and could not verify slot status"
                        logger.error(f"❌ {error_msg}")
                        return BookingResult(
                            success=False,
                            error_message=error_msg
                        )
                
                # Follow-up functionality has been removed from Andy AI
                logger.info(f"Meeting booked successfully with ID {booking_id} - follow-up system disabled")

                return BookingResult(
                    success=True,
                    booking_id=booking_id,
                    booking_url=booking_url,
                    meeting_link=meeting_link,
                    staff_name=staff_name
                )
            else:
                # Check for inner status failure (Zoho sometimes returns success=true but inner failure)
                if response.get('response', {}).get('returnvalue', {}).get('status') == 'failure':
                    error_msg = response.get('response', {}).get('returnvalue', {}).get('errormessage', 'Inner booking failure')
                    logger.error(f"❌ Inner booking failure: {error_msg}")
                    return BookingResult(
                        success=False,
                        error_message=error_msg
                    )
                
                error_msg = response.get('response', {}).get('errormessage', 'Unknown booking error')
                logger.error(f"❌ Booking failed: {error_msg}")
                logger.error(f"❌ Full response: {response}")
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )

        except Exception as e:
            error_msg = f"Unexpected error booking appointment: {str(e)}"
            logger.error(f"❌ {error_msg}")
            import traceback
            logger.debug(f"   Traceback: {traceback.format_exc()}")
            return BookingResult(
                success=False,
                error_message=error_msg
            )

    async def get_next_available_slot(
        self, 
        service_type: str = "consultation",
        preferred_staff: Optional[str] = None
    ) -> Optional[BookingSlot]:
        """Get the next available slot (within next 7 days)"""
        date_from = datetime.now()
        date_to = date_from + timedelta(days=7)
        
        slots = await self.get_available_slots(
            date_from=date_from,
            date_to=date_to,
            service_type=service_type,
            preferred_staff=preferred_staff
        )
        
        if slots:
            # Return the earliest available slot
            def get_naive_start_time(slot: BookingSlot) -> datetime:
                slot_start_naive = slot.start_time
                if slot.start_time.tzinfo is not None:
                    slot_start_naive = slot.start_time.replace(tzinfo=None)
                return slot_start_naive
            
            return min(slots, key=get_naive_start_time)
        return None

    def _get_staff_name_by_id(self, staff_id: str) -> str:
        """Get staff name by Zoho staff ID"""
        for staff_key, staff_info in self.sales_team.items():
            if staff_info["zoho_staff_id"] == staff_id:
                return staff_info["name"]
        return "Unknown Staff"

    def _build_query_string(self, params: Dict) -> str:
        """Build query string from parameters"""
        return "&".join([f"{k}={v}" for k, v in params.items()])

    async def cancel_booking(self, booking_id: str, reason: Optional[str] = None) -> bool:
        """Cancel a booking"""
        try:
            cancel_data = {
                'booking_id': booking_id,
                'reason': reason or 'Cancelled by customer'
            }
            
            response = await self._make_api_request("DELETE", f"bookings/{booking_id}", cancel_data)
            return response.get('status') == 'success'
            
        except Exception as e:
            logger.error(f"Error cancelling booking: {e}")
            return False

    async def get_staff_availability_summary(self) -> Dict[str, Any]:
        """Get availability summary for all staff members"""
        try:
            date_from = datetime.now()
            date_to = date_from + timedelta(days=7)
            
            summary = {}
            for staff_key, staff_info in self.sales_team.items():
                slots = await self.get_available_slots(
                    date_from=date_from,
                    date_to=date_to,
                    preferred_staff=staff_key
                )
                
                summary[staff_key] = {
                    'name': staff_info['name'],
                    'email': staff_info['email'],
                    'available_slots': len(slots),
                    'next_available': slots[0].start_time.isoformat() if slots else None,
                    'specialties': staff_info['specialties']
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting staff availability summary: {e}")
            return {}

    async def get_services(self) -> List[Dict]:
        """Get all available services from Zoho Bookings"""
        try:
            response = await self._make_api_request("GET", "services")
            if response and response.get("response", {}).get("status") == "success":
                services = response["response"]["returnvalue"]["data"]
                logger.info(f"Retrieved {len(services)} services from Zoho Bookings")
                return services
            else:
                logger.error("Failed to get services from Zoho Bookings")
                return []
        except Exception as e:
            logger.error(f"Error getting services: {e}")
            return []

    async def get_staff(self) -> List[Dict]:
        """Get all staff members from Zoho Bookings"""
        try:
            response = await self._make_api_request("GET", "staff")
            if response and response.get("response", {}).get("status") == "success":
                staff = response["response"]["returnvalue"]["data"]
                logger.info(f"Retrieved {len(staff)} staff members from Zoho Bookings")
                return staff
            else:
                logger.warning("Failed to get staff from Zoho Bookings, using configured staff")
                return []
        except Exception as e:
            logger.error(f"Error getting staff: {e}")
            return []

    async def get_staff_timezone(self, staff_id: str) -> str:
        """Get staff timezone from Zoho Bookings API or intelligent mapping"""
        try:
            # Check cache first
            if staff_id in ZohoBookingsService._staff_timezone_cache:
                logger.debug(f"📋 Using cached timezone for staff {staff_id}: {ZohoBookingsService._staff_timezone_cache[staff_id]}")
                return ZohoBookingsService._staff_timezone_cache[staff_id]
            
            # Try to get timezone from staff mapping first
            staff_timezone = self._get_staff_timezone_from_mapping(staff_id)
            if staff_timezone:
                ZohoBookingsService._staff_timezone_cache[staff_id] = staff_timezone
                logger.info(f"✅ Using mapped timezone for staff {staff_id}: {staff_timezone}")
                return staff_timezone
            
            # Try to get from workspace as fallback
            workspace_timezone = await self._get_workspace_timezone()
            if workspace_timezone:
                ZohoBookingsService._staff_timezone_cache[staff_id] = workspace_timezone
                logger.info(f"✅ Using workspace timezone for staff {staff_id}: {workspace_timezone}")
                return workspace_timezone
            
            # Final fallback to default timezone
            default_timezone = "Australia/Sydney"
            ZohoBookingsService._staff_timezone_cache[staff_id] = default_timezone
            logger.info(f"✅ Using default timezone for staff {staff_id}: {default_timezone}")
            return default_timezone
                
        except Exception as e:
            logger.error(f"❌ Error getting staff timezone for {staff_id}: {e}")
            # Fallback to default timezone
            default_timezone = "Australia/Sydney"
            ZohoBookingsService._staff_timezone_cache[staff_id] = default_timezone
            return default_timezone

    def _get_staff_timezone_from_mapping(self, staff_id: str) -> Optional[str]:
        """Get staff timezone from local mapping"""
        # Staff timezone mapping based on known staff members
        staff_timezone_mapping = {
            "26044000000040008": "Australia/Sydney",  # Saumil
            "26044000000186094": "Australia/Sydney",  # Frank
        }
        
        return staff_timezone_mapping.get(staff_id)

    async def _get_workspace_timezone(self) -> Optional[str]:
        """Get workspace timezone from Zoho"""
        try:
            workspace = await self.get_workspace()
            if workspace:
                # Try to get timezone from workspace data
                timezone = (workspace.get('timezone') or 
                          workspace.get('time_zone') or 
                          workspace.get('timeZone') or
                          workspace.get('location', {}).get('timezone') or
                          workspace.get('settings', {}).get('timezone'))
                
                if timezone:
                    logger.info(f"✅ Retrieved workspace timezone: {timezone}")
                    return timezone
                else:
                    logger.warning("⚠️ No timezone found in workspace data")
                    return None
            else:
                logger.warning("⚠️ Failed to get workspace data")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting workspace timezone: {e}")
            return None

    async def get_workspace(self) -> Optional[Dict]:
        """Get workspace information from Zoho Bookings"""
        try:
            response = await self._make_api_request("GET", "workspaces")
            if response and response.get("response", {}).get("status") == "success":
                workspaces = response["response"]["returnvalue"]["data"]
                if workspaces:
                    workspace = workspaces[0]  # Use first workspace
                    logger.info(f"Retrieved workspace: {workspace['name']}")
                    return workspace
            return None
        except Exception as e:
            logger.error(f"Error getting workspace: {e}")
            return None


    # Enhanced methods for meeting agent integration

    async def get_available_slots_for_date_range(self, start_date: datetime, end_date: datetime,
                                               timezone: str = "Asia/Kolkata",
                                               service_type: str = "lead_meeting") -> List[BookingSlot]:
        """
        Get available slots for a date range with timezone awareness
        Enhanced method for meeting agent integration
        """
        try:
            logger.info(f"Getting available slots from {start_date} to {end_date} in {timezone}")

            # Convert dates to target timezone
            tz = pytz.timezone(timezone)
            if start_date.tzinfo is None:
                start_date = tz.localize(start_date)
            if end_date.tzinfo is None:
                end_date = tz.localize(end_date)

            all_slots = []
            current_date = start_date.date()
            end_date_only = end_date.date()

            while current_date <= end_date_only:
                # Get slots for each day
                day_start = datetime.combine(current_date, datetime.min.time())
                day_start = tz.localize(day_start)

                daily_slots = await self.get_available_slots_for_date(
                    target_date=day_start,
                    service_type=service_type
                )

                if daily_slots:
                    all_slots.extend(daily_slots)

                current_date += timedelta(days=1)

            # Sort slots by start time
            all_slots.sort(key=lambda slot: slot.start_time)

            logger.info(f"Found {len(all_slots)} total slots in date range")
            return all_slots

        except Exception as e:
            logger.error(f"Error getting slots for date range: {e}")
            return []

    async def book_slot_for_meeting_agent(self, slot: BookingSlot, lead_data: Dict[str, Any],
                                        timezone: str = "Asia/Kolkata") -> BookingResult:
        """
        Book a slot specifically for the meeting agent with enhanced error handling
        """
        try:
            logger.info(f"Booking slot for meeting agent: {slot.start_time} with {slot.staff_name}")

            # Extract lead information
            customer_name = f"{lead_data.get('first_name', 'Lead')} {lead_data.get('last_name', 'Contact')}"
            customer_email = lead_data.get('email', '<EMAIL>')
            customer_phone = lead_data.get('phone') or lead_data.get('mobile', '+61400000000')

            # Book the appointment
            result = await self.book_appointment(
                slot=slot,
                customer_name=customer_name,
                customer_email=customer_email,
                customer_phone=customer_phone,
                notes=f"Meeting booked via AI assistant for lead {lead_data.get('id', 'unknown')}",
                lead_id=lead_data.get('id'),
                timezone=timezone
            )

            if result.success:
                logger.info(f"Successfully booked meeting for {customer_name}")
            else:
                logger.error(f"Failed to book meeting: {result.error_message}")

            return result

        except Exception as e:
            logger.error(f"Error booking slot for meeting agent: {e}")
            return BookingResult(
                success=False,
                error_message=f"Booking failed: {str(e)}"
            )

    def format_slot_for_display(self, slot: BookingSlot, timezone: str = "Asia/Kolkata") -> str:
        """
        Format a booking slot for user-friendly display
        """
        try:
            # Convert to local timezone
            local_time = slot.start_time.astimezone(pytz.timezone(timezone))

            # Format as "Monday, March 15 at 2:00 PM"
            formatted = local_time.strftime("%A, %B %d at %I:%M %p")

            return formatted

        except Exception as e:
            logger.error(f"Error formatting slot for display: {e}")
            return str(slot.start_time)


# Dependency injection
_service_instance = None

def get_zoho_bookings_service() -> ZohoBookingsService:
    """Get Zoho Bookings service instance (singleton)"""
    global _service_instance
    if _service_instance is None:
        _service_instance = ZohoBookingsService()
    return _service_instance
