"""
SMS Webhook Service - Integration point for SMS Assistant
Handles incoming SMS messages and routes them through the AI assistant
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.agents.sms_assistant import get_sms_assistant
from app.core.memory.sms_memory import get_sms_memory_manager
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.core.database.connection import get_db
from app.services.question_extraction_service import get_question_extraction_service

from app.core.logging import logger


class SMSWebhookService:
    """Service for handling SMS webhook integration with AI assistant"""
    
    def __init__(self):
        self.sms_assistant = get_sms_assistant()
        self.memory_manager = get_sms_memory_manager()
    
    async def process_inbound_sms(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process inbound SMS message through AI assistant
        
        Expected webhook format:
        {
            "event_type": "SMS_INBOUND",
            "timestamp": "2024-01-15T14:30:00Z",
            "mo": {
                "id": "msg_123",
                "sender": "+1234567890",
                "recipient": "+1987654321",
                "message": "Hi, I'm interested in franchise opportunities",
                "timestamp": "2024-01-15T14:30:00Z"
            }
        }
        """
        try:
            # Extract message data
            mo_data = webhook_data.get("mo", {})
            phone_number = mo_data.get("sender", "").strip()
            message = mo_data.get("message", "").strip()
            
            if not phone_number or not message:
                return {
                    "success": False,
                    "error": "Missing phone number or message",
                    "response": "Invalid message format"
                }
            
            # Clean phone number
            phone_number = self._clean_phone_number(phone_number)
            
            logger.info(f"Processing SMS from {phone_number}: {message[:50]}...")
            
            # Process through AI assistant
            result = await self.sms_assistant.process_sms(phone_number, message)
            
            if result["success"]:
                # Log successful processing
                logger.info(
                    f"SMS processed successfully for {phone_number}",
                    processing_time=result.get("processing_time", 0),
                    execution_path=result.get("execution_path", [])
                )
                
                # Sync with database periodically
                if result.get("context_updated"):
                    await self.memory_manager.sync_with_database(phone_number)
                
                return {
                    "success": True,
                    "response": result["response"],
                    "data": {
                        "answer": result["response"],
                        "lead_id": result.get("lead_id"),
                        "next_action": result.get("next_action"),
                        "processing_time": result.get("processing_time"),
                        "extracted_info": result.get("extracted_info", {}),
                        "context_updated": result.get("context_updated", False),
                        "question_extraction": {
                            "questions_found": result.get('question_extraction_result', {}).get('questions_found', 0),
                            "questions_stored": result.get('question_extraction_result', {}).get('questions_stored', 0),
                            "extraction_success": result.get('question_extraction_result', {}).get('success', False)
                        }
                    }
                }
            else:
                logger.error(f"SMS processing failed for {phone_number}: {result.get('error')}")
                return {
                    "success": False,
                    "error": result.get("error", "Processing failed"),
                    "response": result.get("response", "I apologize, but I encountered an error. Please try again.")
                }
                
        except Exception as e:
            logger.error(f"SMS webhook processing error: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "I apologize, but I encountered an error. Please try again."
            }
    
    async def get_conversation_history(self, phone_number: str, limit: int = 50) -> Dict[str, Any]:
        """Get conversation history for a phone number"""
        try:
            phone_number = self._clean_phone_number(phone_number)
            
            # Get from Redis first
            redis_history = self.memory_manager.get_conversation_history(phone_number, limit)
            
            # Get lead context
            lead_context = self.memory_manager.get_lead_context(phone_number)
            
            # Get from database if we have lead_id
            db_history = []
            if lead_context and lead_context.get("lead_id"):
                db_history = await self._get_db_conversation_history(lead_context["lead_id"], limit)
            
            return {
                "success": True,
                "data": {
                    "phone_number": phone_number,
                    "lead_context": lead_context,
                    "redis_history": redis_history,
                    "database_history": db_history,
                    "total_messages": len(redis_history)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_lead_summary(self, phone_number: str) -> Dict[str, Any]:
        """Get comprehensive lead summary"""
        try:
            phone_number = self._clean_phone_number(phone_number)
            
            # Get lead context
            lead_context = self.memory_manager.get_lead_context(phone_number)
            if not lead_context:
                return {
                    "success": False,
                    "error": "No lead context found"
                }
            
            # Get engagement metrics
            engagement_metrics = self.memory_manager.get_engagement_metrics(phone_number)
            
            # Get qualification status
            qualification_status = self.memory_manager.get_qualification_status(phone_number)
            
            # Get recent conversation
            recent_conversation = self.memory_manager.get_conversation_history(phone_number, 10)
            
            # Get database lead info if available
            db_lead_info = None
            if lead_context.get("lead_id"):
                db_lead_info = await self._get_db_lead_info(lead_context["lead_id"])
            
            return {
                "success": True,
                "data": {
                    "lead_context": lead_context,
                    "engagement_metrics": engagement_metrics,
                    "qualification_status": qualification_status,
                    "recent_conversation": recent_conversation,
                    "database_lead": db_lead_info,
                    "summary_generated_at": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting lead summary: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_lead_context(self, phone_number: str, context_updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update lead context manually"""
        try:
            phone_number = self._clean_phone_number(phone_number)
            
            # Get existing context
            existing_context = self.memory_manager.get_lead_context(phone_number)
            if not existing_context:
                return {
                    "success": False,
                    "error": "No existing context found"
                }
            
            # Update context
            updated_context = {**existing_context, **context_updates}
            updated_context["last_updated"] = datetime.now().isoformat()
            
            # Store updated context
            success = self.memory_manager.store_lead_context(phone_number, updated_context)
            
            if success:
                # Sync with database
                await self.memory_manager.sync_with_database(phone_number)
                
                return {
                    "success": True,
                    "data": {
                        "updated_context": updated_context,
                        "changes": context_updates
                    }
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to update context"
                }
                
        except Exception as e:
            logger.error(f"Error updating lead context: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def simulate_conversation(self, phone_number: str, messages: List[str]) -> Dict[str, Any]:
        """Simulate a conversation for testing"""
        try:
            phone_number = self._clean_phone_number(phone_number)
            conversation_log = []
            
            for message in messages:
                result = await self.sms_assistant.process_sms(phone_number, message)
                
                conversation_log.append({
                    "user_message": message,
                    "ai_response": result.get("response", "Error processing message"),
                    "success": result.get("success", False),
                    "extracted_info": result.get("extracted_info", {}),
                    "processing_time": result.get("processing_time", 0)
                })
                
                # Small delay to simulate real conversation
                import asyncio
                await asyncio.sleep(0.1)
            
            # Get final lead context
            final_context = self.memory_manager.get_lead_context(phone_number)
            
            return {
                "success": True,
                "data": {
                    "conversation_log": conversation_log,
                    "final_lead_context": final_context,
                    "total_messages": len(messages)
                }
            }
            
        except Exception as e:
            logger.error(f"Error simulating conversation: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _clean_phone_number(self, phone_number: str) -> str:
        """Clean and normalize phone number"""
        # Remove all non-digit characters
        cleaned = ''.join(filter(str.isdigit, phone_number))
        
        # Add country code if missing
        if len(cleaned) == 10:
            cleaned = "1" + cleaned
        
        # Add + prefix
        if not phone_number.startswith("+"):
            cleaned = "+" + cleaned
        else:
            cleaned = "+" + cleaned
        
        return cleaned
    
    async def _get_db_conversation_history(self, lead_id: str, limit: int) -> List[Dict[str, Any]]:
        """Get conversation history from database"""
        try:
            async for session in get_db():
                result = await session.execute(
                    select(ConversationMessage)
                    .where(
                        and_(
                            ConversationMessage.lead_id == lead_id,
                            ConversationMessage.is_active == True,
                            ConversationMessage.is_deleted == False
                        )
                    )
                    .order_by(ConversationMessage.created_at.desc())
                    .limit(limit)
                )
                
                messages = result.scalars().all()
                
                return [
                    {
                        "id": str(msg.id),
                        "sender": msg.sender,
                        "message": msg.message,
                        "created_at": msg.created_at.isoformat(),
                        "lead_id": str(msg.lead_id)
                    }
                    for msg in messages
                ]
                
        except Exception as e:
            logger.error(f"Error getting DB conversation history: {e}")
            return []
    
    async def _get_db_lead_info(self, lead_id: str) -> Optional[Dict[str, Any]]:
        """Get lead information from database"""
        try:
            async with get_db() as session:
                result = await session.execute(
                    select(Lead)
                    .where(
                        and_(
                            Lead.id == lead_id,
                            Lead.is_active == True,
                            Lead.is_deleted == False
                        )
                    )
                )
                
                lead = result.scalar_one_or_none()
                
                if lead:
                    return {
                        "id": str(lead.id),
                        "first_name": lead.first_name,
                        "last_name": lead.last_name,
                        "phone": lead.phone,
                        "email": lead.email,
                        "location": lead.location,
                        "budget_preference": lead.budget_preference,
                        "created_at": lead.created_at.isoformat(),
                        "updated_at": lead.updated_at.isoformat()
                    }
                
        except Exception as e:
            logger.error(f"Error getting DB lead info: {e}")
        
        return None


# Global service instance
_sms_webhook_service: Optional[SMSWebhookService] = None


def get_sms_webhook_service() -> SMSWebhookService:
    """Get or create SMS webhook service instance"""
    global _sms_webhook_service
    
    if _sms_webhook_service is None:
        _sms_webhook_service = SMSWebhookService()
    
    return _sms_webhook_service
