"""
Kudosity SMS Service - Integration with Kudosity SMS API
Handles sending SMS messages through Kudosity's Send SMS JSON API
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog
from pydantic import BaseModel, Field
import openai

from app.core.utils.service_clients.api_client import APIClient
from app.core.config.settings import settings
import os
from app.core.utils.exception_manager.custom_exceptions import (
    BusinessLogicError,
    ValidationError
)

from app.core.logging import logger


class SMSMessage(BaseModel):
    """SMS message model for sending"""
    to: str = Field(..., description="Recipient phone number")
    message: str = Field(..., description="Message content")  # No length limit here, will be handled in processing
    from_number: Optional[str] = Field(None, description="Sender number (optional)")


class SMSResponse(BaseModel):
    """SMS API response model"""
    success: bool
    message_id: Optional[str] = None
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    provider_response: Optional[Dict[str, Any]] = None


class KudositySMSService:
    """Service for sending SMS messages via Kudosity API"""
    
    def __init__(self):
        """Initialize Kudosity SMS service"""
        self.api_client = APIClient(
            base_url=getattr(settings, 'KUDOSITY_API_BASE_URL', 'https://api.transmitsms.com'),
            timeout=30
        )
        self.api_key = getattr(settings, 'KUDOSITY_API_KEY', None)
        self.api_secret = getattr(settings, 'KUDOSITY_API_SECRET', None)
        self.default_from_number = getattr(settings, 'KUDOSITY_FROM_NUMBER', None)

        # Test mode - skip actual SMS sending for testing
        self.test_mode = os.getenv('SMS_TEST_MODE', 'true').lower() == 'true'

        # SMS enabled flag - master switch for SMS sending
        self.sms_enabled = getattr(settings, 'KUDOSITY_SMS_ENABLED', False)

        # OpenAI configuration for intelligent splitting
        self.openai_api_key = getattr(settings, 'OPENAI_API_KEY', None)
        if self.openai_api_key:
            openai.api_key = self.openai_api_key

        if not self.api_key or not self.api_secret:
            logger.warning("Kudosity API credentials not configured")

        if self.test_mode:
            logger.info("🧪 SMS TEST MODE ENABLED - No real SMS will be sent")

        if not self.sms_enabled:
            logger.info("📱 SMS SENDING DISABLED - KUDOSITY_SMS_ENABLED=false")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Kudosity API"""
        import base64
        
        if not self.api_key or not self.api_secret:
            raise ValidationError("Kudosity API credentials not configured")
        
        # Create basic auth header
        credentials = f"{self.api_key}:{self.api_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        return {
            "Authorization": f"Basic {encoded_credentials}",
            "Content-Type": "application/json"
        }
    
    async def send_sms(self, sms_message: SMSMessage) -> SMSResponse:
        """
        Send SMS message via Kudosity API
        
        Args:
            sms_message: SMS message to send
            
        Returns:
            SMSResponse: API response with success status and message ID
        """
        try:
            # Validate message
            if not sms_message.to or not sms_message.message:
                raise ValidationError("Phone number and message are required")

            # Check if SMS sending is enabled
            if not self.sms_enabled:
                logger.info(
                    f"SMS sending disabled - would have sent to {sms_message.to}",
                    message_length=len(sms_message.message),
                    kudosity_sms_enabled=False
                )
                return SMSResponse(
                    success=True,
                    message_id="disabled_mock_id",
                    error=None,
                    provider_response={"status": "disabled", "message": "SMS sending disabled via KUDOSITY_SMS_ENABLED=false"}
                )

            # Clean phone number
            phone_number = self._clean_phone_number(sms_message.to)

            # Validate phone number
            is_valid, validation_error = self._validate_phone_number(phone_number)
            if not is_valid:
                logger.error(f"Phone number validation failed: {validation_error}", phone_number=phone_number)
                return SMSResponse(
                    success=False,
                    error=f"Invalid phone number: {validation_error}"
                )

            # Clean message content
            cleaned_message = self._prepare_message_content(sms_message.message)

            # Use enhanced rule-based splitting for better readability
            segments = self._enhanced_rule_based_split(
                cleaned_message,
                "GSM-7",  # Default encoding
                160,      # Single limit
                153       # Concat limit
            )
            message_parts = [segment["text"] for segment in segments]

            # If message needs to be split, log it
            if len(message_parts) > 1:
                split_method = segments[0].get("split_method", "enhanced_rule_based") if segments else "enhanced_rule_based"
                logger.info(
                    f"Message split into {len(message_parts)} parts using {split_method}",
                    original_length=len(cleaned_message),
                    parts_count=len(message_parts),
                    split_method=split_method,
                    to=phone_number
                )

            # Send each part as a separate SMS in sequence with proper delays and retry logic
            results = []
            for i, part in enumerate(message_parts, 1):
                # Add proper delay between parts to ensure sequential delivery
                if i > 1:
                    await asyncio.sleep(1.0)  # 1 second delay between SMS parts for better sequencing

                # Prepare request payload for this part
                payload = {
                    "to": phone_number,
                    "message": part
                }

                # Add from number if specified
                if sms_message.from_number:
                    payload["from"] = sms_message.from_number
                elif self.default_from_number:
                    payload["from"] = self.default_from_number

                # Add additional parameters that might be required by Kudosity API
                payload.update({
                    "message_type": "SMS",
                    "delivery_report": "true",
                    "validity_period": "72"  # 72 hours validity
                })

                # Log each part being sent with sequence info
                part_info = f"part {i}/{len(message_parts)}" if len(message_parts) > 1 else "single message"
                logger.info(
                    f"Sending SMS via Kudosity API ({part_info}) - SEQUENCE {i}",
                    to=phone_number,
                    message_length=len(part),
                    part_number=i,
                    total_parts=len(message_parts),
                    sequence_order=i,
                    from_number=payload.get("from")
                )

                # SMS DELIVERY MONITORING
                print(f"\nSMS API CALL ({part_info}) - SEQUENCE {i}")
                print(f"To: {phone_number}")
                print(f"From: {payload.get('from', 'Default')}")
                print(f"Message Length: {len(part)} chars")
                if len(message_parts) > 1:
                    print(f"Part {i}/{len(message_parts)} of split message")
                    print(f"Sequence Order: {i} (with 2.5s delay)")
                print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # 🧪 TEST MODE - Skip actual API call
                if self.test_mode:
                    print(f"🧪 TEST MODE: Simulating SMS send (no real SMS sent)")
                    print(f"📱 Would send to: {phone_number}")
                    print(f"💬 Message content: {part}")

                    # Return simulated success response
                    fake_message_id = f"test_msg_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}"
                    response = {
                        "message_id": fake_message_id,
                        "status": "sent",
                        "test_mode": True,
                        "part_number": i,
                        "total_parts": len(message_parts)
                    }
                else:
                    # Make actual API request using form data
                    headers = self._get_auth_headers()
                    # Remove Content-Type header to let requests set it for form data
                    if "Content-Type" in headers:
                        del headers["Content-Type"]

                    # Log the request details for debugging
                    logger.info(
                        f"Sending SMS API request - Part {i}/{len(message_parts)}",
                        payload=payload,
                        headers={k: v[:20] + "..." if k == "Authorization" else v for k, v in headers.items()},
                        endpoint="/send-sms.json"
                    )

                    try:
                        response = await self.api_client.post(
                            endpoint="/send-sms.json",
                            data=payload,
                            headers=headers,
                            timeout=30,
                            use_form_data=True
                        )

                        # Log successful response
                        logger.info(f"SMS API response received for part {i}", response=response)

                    except Exception as api_error:
                        error_message = str(api_error)

                        # Check for specific error types and provide better messages
                        if "Insufficient funds" in error_message:
                            error_message = "SMS sending failed: Insufficient funds in Kudosity account. Please top up your account balance."
                        elif "Authentication failed" in error_message:
                            error_message = "SMS sending failed: Invalid API credentials. Please check KUDOSITY_API_KEY and KUDOSITY_API_SECRET."
                        elif "Bad request" in error_message:
                            error_message = f"SMS sending failed: {error_message}"

                        logger.error(
                            f"SMS API request failed for part {i}",
                            error=error_message,
                            payload=payload,
                            part_number=i
                        )
                        # Return error response for this part
                        results.append({
                            "success": False,
                            "error": error_message,
                            "part_number": i,
                            "message_id": None
                        })
                        continue

                # Parse response for this part
                error_info = response.get("error", {})
                error_code = error_info.get("code") if isinstance(error_info, dict) else None

                # Check if this is actually an error (not SUCCESS)
                if error_code and error_code != "SUCCESS":
                    error_message = error_info.get("description", str(error_info))
                    logger.error(
                        f"Kudosity API error for part {i}/{len(message_parts)}: {error_message}",
                        response=response,
                        part_number=i
                    )
                    # For multi-part messages, continue with other parts even if one fails
                    results.append({
                        "success": False,
                        "error": error_message,
                        "part_number": i,
                        "message_id": None
                    })
                    continue

                # Success response for this part
                message_id = response.get("message_id") or response.get("id")
                message_id_str = str(message_id) if message_id is not None else None

                logger.info(
                    f"SMS part {i}/{len(message_parts)} sent successfully via Kudosity",
                    message_id=message_id,
                    to=phone_number,
                    sequence_order=i,
                    delivery_status="sent"
                )

                # Add successful result
                results.append({
                    "success": True,
                    "message_id": message_id_str,
                    "part_number": i,
                    "message_length": len(part)
                })

            # Return consolidated results
            successful_parts = [r for r in results if r["success"]]
            failed_parts = [r for r in results if not r["success"]]

            # If all parts succeeded
            if len(successful_parts) == len(message_parts):
                # Return success with details about all parts
                return SMSResponse(
                    success=True,
                    message_id=successful_parts[0]["message_id"],  # Primary message ID
                    details={
                        "total_parts": len(message_parts),
                        "successful_parts": len(successful_parts),
                        "failed_parts": len(failed_parts),
                        "message_ids": [r["message_id"] for r in successful_parts],
                        "original_length": len(cleaned_message),
                        "split_required": len(message_parts) > 1
                    }
                )
            else:
                # Some parts failed
                return SMSResponse(
                    success=False,
                    error=f"Failed to send {len(failed_parts)} out of {len(message_parts)} message parts",
                    details={
                        "total_parts": len(message_parts),
                        "successful_parts": len(successful_parts),
                        "failed_parts": len(failed_parts),
                        "successful_message_ids": [r["message_id"] for r in successful_parts],
                        "failed_parts_details": failed_parts
                    }
                )
            
        except Exception as e:
            logger.error(
                f"Failed to send SMS via Kudosity API: {str(e)}",
                to=sms_message.to,
                error=str(e)
            )
            return SMSResponse(
                success=False,
                error=str(e)
            )
    
    async def send_bulk_sms(self, messages: List[SMSMessage]) -> List[SMSResponse]:
        """
        Send multiple SMS messages
        
        Args:
            messages: List of SMS messages to send
            
        Returns:
            List[SMSResponse]: List of API responses
        """
        results = []
        
        for message in messages:
            try:
                # Add small delay between messages to avoid rate limiting
                if results:
                    await asyncio.sleep(0.1)
                
                result = await self.send_sms(message)
                results.append(result)
                
            except Exception as e:
                logger.error(f"Failed to send bulk SMS message: {str(e)}")
                results.append(SMSResponse(
                    success=False,
                    error=str(e)
                ))
        
        return results

    def _prepare_message_content(self, message: str) -> str:
        """
        Prepare message content for SMS delivery
        - Remove emojis
        - Clean whitespace

        Args:
            message: Raw message content

        Returns:
            str: Cleaned message content suitable for SMS
        """
        # Remove emojis using regex
        import re
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"  # dingbats
            "\U000024C2-\U0001F251"
            "]+",
            flags=re.UNICODE
        )

        # Clean the message
        cleaned_message = emoji_pattern.sub('', message)
        cleaned_message = cleaned_message.strip()

        # Remove extra whitespace
        cleaned_message = ' '.join(cleaned_message.split())

        return cleaned_message

    def _detect_encoding(self, text: str) -> tuple[str, int, int]:
        """
        Detect SMS encoding and return limits

        Returns:
            tuple: (encoding, single_limit, concat_limit)
        """
        # GSM 7-bit character set
        gsm7_chars = set(
            "@£$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞ\x1bÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?"
            "¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà"
        )

        # Extended GSM 7-bit characters (require escape sequence)
        gsm7_extended = set("^{}\\[~]|€")

        # Check if all characters are GSM 7-bit compatible
        for char in text:
            if char not in gsm7_chars and char not in gsm7_extended:
                # Contains non-GSM characters, use UCS-2
                return ("UCS-2", 70, 67)

        # All characters are GSM 7-bit compatible
        return ("GSM-7", 160, 153)

    def split_sms(self, text: str) -> List[dict]:
        """
        Split SMS text according to GSM-7/UCS-2 encoding rules

        Args:
            text: The message to split

        Returns:
            List[dict]: List of segments with metadata
        """
        if not text:
            return []

        encoding, single_limit, concat_limit = self._detect_encoding(text)

        # If message fits in single SMS
        if len(text) <= single_limit:
            return [{
                "text": text,
                "encoding": encoding,
                "segment_index": 1,
                "total_segments": 1,
                "length": len(text)
            }]

        # Split into multiple segments
        segments = []
        remaining_text = text
        segment_index = 1

        while remaining_text:
            if len(remaining_text) <= concat_limit:
                # Last segment
                segments.append({
                    "text": remaining_text,
                    "encoding": encoding,
                    "segment_index": segment_index,
                    "total_segments": 0,  # Will be updated after all segments
                    "length": len(remaining_text)
                })
                break

            # Find best split point
            split_point = concat_limit

            # Don't break placeholders like {name}, {company}
            placeholder_start = remaining_text.rfind('{', 0, concat_limit)
            if placeholder_start != -1:
                placeholder_end = remaining_text.find('}', placeholder_start)
                if placeholder_end > concat_limit:
                    # Placeholder would be broken, split before it
                    split_point = placeholder_start

            if split_point == concat_limit:
                # Try to split at sentence boundary
                sentence_end = remaining_text.rfind('. ', 0, concat_limit)
                if sentence_end > concat_limit * 0.7:
                    split_point = sentence_end + 2
                else:
                    # Try to split at word boundary
                    word_boundary = remaining_text.rfind(' ', 0, concat_limit)
                    if word_boundary > concat_limit * 0.8:
                        split_point = word_boundary

            # Extract segment
            segment_text = remaining_text[:split_point].strip()
            if segment_text:
                segments.append({
                    "text": segment_text,
                    "encoding": encoding,
                    "segment_index": segment_index,
                    "total_segments": 0,  # Will be updated
                    "length": len(segment_text)
                })
                segment_index += 1

            # Update remaining text
            remaining_text = remaining_text[split_point:].strip()

        # Update total_segments for all segments
        total_segments = len(segments)
        for segment in segments:
            segment["total_segments"] = total_segments

        return segments

    async def intelligent_split_sms(self, text: str) -> List[dict]:
        """
        Use OpenAI to intelligently split SMS messages for maximum readability

        Args:
            text: The message to split

        Returns:
            List[dict]: List of segments with metadata
        """
        if not text:
            return []

        encoding, single_limit, concat_limit = self._detect_encoding(text)

        # If message fits in single SMS, no splitting needed
        if len(text) <= single_limit:
            return [{
                "text": text,
                "encoding": encoding,
                "segment_index": 1,
                "total_segments": 1,
                "length": len(text),
                "split_method": "single"
            }]

        # Try OpenAI intelligent splitting first (disabled for now)
        use_openai = False  # Temporarily disabled
        if self.openai_api_key and use_openai:
            try:
                ai_segments = await self._openai_split_message(text, concat_limit)
                if ai_segments:
                    # Add metadata to AI-generated segments
                    for i, segment in enumerate(ai_segments, 1):
                        segment.update({
                            "encoding": encoding,
                            "segment_index": i,
                            "total_segments": len(ai_segments),
                            "length": len(segment["text"]),
                            "split_method": "openai_intelligent"
                        })
                    return ai_segments
            except Exception as e:
                logger.warning(f"OpenAI splitting failed, falling back to rule-based: {e}")

        # Use enhanced rule-based splitting
        segments = self._enhanced_rule_based_split(text, encoding, single_limit, concat_limit)
        for segment in segments:
            segment["split_method"] = "enhanced_rule_based"

        return segments

    def _enhanced_rule_based_split(self, text: str, encoding: str, single_limit: int, concat_limit: int) -> List[dict]:
        """
        Enhanced rule-based splitting that prioritizes readability
        """
        if len(text) <= single_limit:
            return [{
                "text": text,
                "encoding": encoding,
                "segment_index": 1,
                "total_segments": 1,
                "length": len(text)
            }]

        segments = []
        remaining_text = text
        segment_index = 1

        while remaining_text:
            if len(remaining_text) <= concat_limit:
                # Last segment
                segments.append({
                    "text": remaining_text,
                    "encoding": encoding,
                    "segment_index": segment_index,
                    "total_segments": 0,  # Will be updated
                    "length": len(remaining_text)
                })
                break

            # Find the best split point for readability
            split_point = self._find_best_split_point(remaining_text, concat_limit)

            # Extract segment
            segment_text = remaining_text[:split_point].strip()
            if segment_text:
                segments.append({
                    "text": segment_text,
                    "encoding": encoding,
                    "segment_index": segment_index,
                    "total_segments": 0,  # Will be updated
                    "length": len(segment_text)
                })
                segment_index += 1

            # Update remaining text
            remaining_text = remaining_text[split_point:].strip()

        # Update total_segments for all segments
        total_segments = len(segments)
        for segment in segments:
            segment["total_segments"] = total_segments

        return segments

    def _find_best_split_point(self, text: str, max_length: int) -> int:
        """
        Find the best point to split text for maximum readability
        """
        if len(text) <= max_length:
            return len(text)

        # Priority 1: Split after sentence endings
        sentence_endings = ['. ', '! ', '? ']
        best_sentence_split = -1
        for ending in sentence_endings:
            pos = text.rfind(ending, 0, max_length - 10)  # Leave some buffer
            if pos > max_length * 0.6:  # Must be reasonably close to limit
                best_sentence_split = max(best_sentence_split, pos + len(ending))

        if best_sentence_split > 0:
            return best_sentence_split

        # Priority 2: Split after commas or semicolons
        punctuation_splits = [', ', '; ', ': ']
        best_punct_split = -1
        for punct in punctuation_splits:
            pos = text.rfind(punct, 0, max_length - 5)
            if pos > max_length * 0.7:
                best_punct_split = max(best_punct_split, pos + len(punct))

        if best_punct_split > 0:
            return best_punct_split

        # Priority 3: Split at word boundaries
        word_boundary = text.rfind(' ', 0, max_length)
        if word_boundary > max_length * 0.8:
            return word_boundary + 1

        # Priority 4: Don't break placeholders like {name}, {company}
        placeholder_start = text.rfind('{', 0, max_length)
        if placeholder_start != -1:
            placeholder_end = text.find('}', placeholder_start)
            if placeholder_end > max_length:
                # Would break placeholder, split before it
                return placeholder_start

        # Last resort: hard split at max_length
        return max_length

    async def _openai_split_message(self, text: str, max_segment_length: int) -> Optional[List[dict]]:
        """
        Use OpenAI to intelligently split a message into readable segments
        """
        try:
            prompt = f"""
You are an expert at splitting SMS messages to maintain readability and natural flow.

TASK: Split this message into segments of maximum {max_segment_length} characters each.

RULES:
1. Each segment must be ≤ {max_segment_length} characters
2. Split at natural break points (sentences, clauses, thoughts)
3. Each segment should make sense on its own
4. Maintain conversational flow between segments
5. Don't break words, names, or important phrases
6. Preserve the original tone and meaning
7. Don't add numbering like (1/3) - the system handles that

MESSAGE TO SPLIT:
"{text}"

Return a JSON array of segments like this:
[
    {{"text": "First segment text here"}},
    {{"text": "Second segment text here"}},
    {{"text": "Final segment text here"}}
]

IMPORTANT: Return ONLY the JSON array, no other text.
"""

            # Use the newer OpenAI client
            from openai import AsyncOpenAI
            client = AsyncOpenAI(api_key=self.openai_api_key)

            response = await client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert SMS message splitter. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )

            result_text = response.choices[0].message.content.strip()

            # Parse JSON response
            segments = json.loads(result_text)

            # Validate segments
            if not isinstance(segments, list):
                raise ValueError("Response is not a list")

            total_length = 0
            for segment in segments:
                if not isinstance(segment, dict) or "text" not in segment:
                    raise ValueError("Invalid segment format")

                segment_text = segment["text"]
                if len(segment_text) > max_segment_length:
                    raise ValueError(f"Segment too long: {len(segment_text)} > {max_segment_length}")

                total_length += len(segment_text)

            # Verify we didn't lose content (allow for minor whitespace differences)
            original_clean = text.replace(" ", "").replace("\n", "")
            segments_clean = "".join(s["text"] for s in segments).replace(" ", "").replace("\n", "")

            if abs(len(original_clean) - len(segments_clean)) > 10:  # Allow small differences
                raise ValueError("Content length mismatch after splitting")

            logger.info(
                f"OpenAI successfully split message",
                original_length=len(text),
                segments_count=len(segments),
                total_segments_length=total_length
            )

            return segments

        except Exception as e:
            logger.error(f"OpenAI message splitting failed: {e}")
            return None

    def _split_message_into_parts(self, message: str, max_length: int = 300) -> List[str]:
        """
        Legacy method - now uses intelligent splitting
        """
        # Use async wrapper for intelligent splitting
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            segments = loop.run_until_complete(self.intelligent_split_sms(message))
        except RuntimeError:
            # If no event loop, create one
            segments = asyncio.run(self.intelligent_split_sms(message))

        return [segment["text"] for segment in segments]

    def _clean_phone_number(self, phone_number: str) -> str:
        """
        Clean and format phone number for Kudosity API

        Args:
            phone_number: Raw phone number

        Returns:
            str: Cleaned phone number (WITHOUT + prefix for Kudosity API)
        """
        if not phone_number:
            return ""

        # Remove all non-digit characters
        cleaned = ''.join(c for c in phone_number if c.isdigit())

        # Handle different formats
        if cleaned.startswith('0'):
            # Australian mobile starting with 0 -> remove 0 and add 61
            cleaned = f"61{cleaned[1:]}"
        elif len(cleaned) == 10 and not cleaned.startswith(('61', '91', '1', '44', '33', '49')):
            # If 10 digits and no country code, assume Australian
            cleaned = f"61{cleaned}"
        elif len(cleaned) < 10:
            # Too short, likely missing country code - assume Australian if starts with 4
            if cleaned.startswith('4'):
                cleaned = f"61{cleaned}"
            else:
                logger.warning(f"Phone number too short: {cleaned}")

        # Validate final format
        if len(cleaned) < 10 or len(cleaned) > 15:
            logger.warning(f"Phone number length invalid: {cleaned} (length: {len(cleaned)})")

        # Log the cleaning process for debugging
        logger.info(f"Phone number cleaned: {phone_number} -> {cleaned}")

        # Return without + prefix (Kudosity API requirement)
        return cleaned

    def _validate_phone_number(self, phone_number: str) -> tuple[bool, str]:
        """
        Validate phone number for SMS sending

        Args:
            phone_number: Cleaned phone number

        Returns:
            tuple: (is_valid, error_message)
        """
        if not phone_number:
            return False, "Phone number is empty"

        if not phone_number.isdigit():
            return False, "Phone number contains non-digit characters"

        if len(phone_number) < 10:
            return False, f"Phone number too short: {len(phone_number)} digits"

        if len(phone_number) > 15:
            return False, f"Phone number too long: {len(phone_number)} digits"

        # Check for known country codes
        known_country_codes = ['61', '91', '1', '44', '33', '49', '86', '81', '82', '65', '852', '853']
        is_known_country = any(phone_number.startswith(code) for code in known_country_codes)

        if not is_known_country:
            logger.warning(f"Unknown country code for phone number: {phone_number}")

        return True, ""
    
    async def get_message_status(self, message_id: str) -> Dict[str, Any]:
        """
        Get delivery status of sent message
        
        Args:
            message_id: Message ID from send response
            
        Returns:
            Dict: Message status information
        """
        try:
            headers = self._get_auth_headers()
            response = await self.api_client.post(
                endpoint="/get-sms-delivery-status.json",
                data={"message_id": message_id},
                headers=headers
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to get message status: {str(e)}")
            return {"error": str(e)}


# Singleton instance
_kudosity_sms_service = None


def get_kudosity_sms_service() -> KudositySMSService:
    """Get singleton instance of Kudosity SMS service"""
    global _kudosity_sms_service
    if _kudosity_sms_service is None:
        _kudosity_sms_service = KudositySMSService()
    return _kudosity_sms_service
