"""
Coochie Conversation Service
Provides OpenAI-powered natural conversation responses for the Coochie Hydrogreen workflow.
Implements acknowledge-then-progress conversation style with exact workflow sentences.
"""

import os
import json
from typing import Dict, Any, Optional, List
import structlog
from app.services.openai_service import OpenAIService

from app.core.logging import logger


class CoochieConversationService:
    """
    Service for generating natural, human-like conversation responses
    using OpenAI while maintaining exact workflow sentences.
    """
    
    def __init__(self):
        self.openai_service = OpenAIService()
        self.conversation_style = {
            "tone": "conversational, warm, approachable",
            "style": "colloquial expressions like 'so', 'yeah', 'okay'",
            "contractions": "use contractions like 'you're' instead of 'you are'",
            "acknowledgments": "Great, That sounds interesting, That makes sense",
            "no_emojis": "never use emojis in responses",
            "acknowledge_then_progress": "always acknowledge what they shared, then proceed"
        }
    
    async def generate_natural_response(
        self, 
        user_message: str, 
        workflow_response: str,
        context: Dict[str, Any] = None
    ) -> str:
        """
        Generate a natural response that incorporates the exact workflow sentence
        while maintaining conversational flow.
        
        Args:
            user_message: What the user said
            workflow_response: Exact sentence from workflow to include
            context: Additional context about the conversation stage
            
        Returns:
            Natural response that includes the workflow sentence
        """
        
        context = context or {}
        stage = context.get("stage", "general")
        
        prompt = f"""
        You are Andy, a Lead Qualification Specialist with 5+ years experience in the Australian franchise industry.
        
        Conversation Style:
        - Conversational tone like talking to a friend in a relaxed but respectful setting
        - Use colloquial expressions: "so", "yeah", "okay"
        - Use contractions: "you're" not "you are"
        - Warm and approachable with everyday vocabulary
        - NEVER use emojis
        - Always acknowledge what they shared first, then proceed
        
        User just said: "{user_message}"
        
        You MUST include this exact sentence in your response: "{workflow_response}"
        
        Current conversation stage: {stage}
        
        Generate a natural response that:
        1. Acknowledges what they shared (use phrases like "Great", "That sounds interesting", "That makes sense")
        2. Includes the exact workflow sentence naturally
        3. Maintains conversational flow
        
        Keep it concise and natural. No emojis.
        """
        
        try:
            # Use the enhanced OpenAI service method
            response = await self.openai_service.enhance_response_with_workflow(
                user_message=user_message,
                workflow_sentence=workflow_response,
                conversation_stage=stage,
                conversation_style=self.conversation_style
            )

            # Ensure no emojis made it through
            response = self._remove_emojis(response)

            # Log the generation (no PII)
            logger.info(
                "Natural response generated",
                stage=stage,
                workflow_sentence_included=workflow_response[:30] + "..." if len(workflow_response) > 30 else workflow_response,
                response_length=len(response)
            )

            return response

        except Exception as e:
            logger.error(f"Error generating natural response: {str(e)}")
            # Fallback to workflow response
            return workflow_response
    
    async def generate_acknowledgment(self, user_message: str) -> str:
        """
        Generate a natural acknowledgment for the user's message.
        
        Args:
            user_message: What the user said
            
        Returns:
            Natural acknowledgment phrase
        """
        
        prompt = f"""
        Generate a brief, natural acknowledgment for this message:
        "{user_message}"
        
        Use phrases like:
        - "Great"
        - "That sounds interesting" 
        - "That makes sense"
        - "Okay"
        - "Right"
        - "I see"
        
        Keep it conversational and warm. Return only the acknowledgment phrase.
        No emojis. Maximum 4 words.
        """
        
        try:
            # Use the enhanced OpenAI service method
            fallback_acknowledgments = [
                "Great", "That sounds interesting", "That makes sense",
                "Okay", "Right", "I see"
            ]

            acknowledgment = await self.openai_service.generate_acknowledgment(
                user_message=user_message,
                acknowledgment_phrases=fallback_acknowledgments
            )

            # Ensure no emojis
            acknowledgment = self._remove_emojis(acknowledgment)

            return acknowledgment

        except Exception as e:
            logger.error(f"Error generating acknowledgment: {str(e)}")
            return "Great"
    
    async def enhance_objection_response(
        self, 
        user_objection: str, 
        workflow_objection_response: str
    ) -> str:
        """
        Enhance objection responses while keeping exact workflow text.
        
        Args:
            user_objection: The objection raised by the user
            workflow_objection_response: Exact response from workflow
            
        Returns:
            Enhanced but exact response
        """
        
        # For objections, we use exact workflow responses
        # But we can add natural acknowledgment
        acknowledgment = await self.generate_acknowledgment(user_objection)
        
        # Combine acknowledgment with exact workflow response
        if acknowledgment.lower() not in workflow_objection_response.lower():
            return f"{acknowledgment}. {workflow_objection_response}"
        else:
            return workflow_objection_response
    
    async def generate_scheduling_options_text(
        self, 
        options: Dict[str, str],
        base_text: str
    ) -> str:
        """
        Generate natural scheduling options text.
        
        Args:
            options: Dictionary with time/day options
            base_text: Base workflow text to enhance
            
        Returns:
            Natural scheduling text
        """
        
        time1 = options.get("time1", "2:00 PM")
        day1 = options.get("day1", "tomorrow")
        time2 = options.get("time2", "10:00 AM")
        day2 = options.get("day2", "day after")
        
        # Format the base text with options
        formatted_text = base_text.format(time=time1, day=day1)
        
        # Add second option naturally
        second_option = f"Or {time2} on {day2}?"
        
        return f"{formatted_text} {second_option}"
    
    def _remove_emojis(self, text: str) -> str:
        """
        Remove any emojis from text to ensure compliance with no-emoji rule.
        
        Args:
            text: Text that might contain emojis
            
        Returns:
            Text with emojis removed
        """
        import re
        
        # Remove emoji characters
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+",
            flags=re.UNICODE
        )
        
        return emoji_pattern.sub('', text).strip()
    
    async def validate_response_compliance(self, response: str) -> Dict[str, Any]:
        """
        Validate that a response complies with workflow requirements.
        
        Args:
            response: Generated response to validate
            
        Returns:
            Validation results
        """
        
        validation = {
            "compliant": True,
            "issues": [],
            "has_emojis": False,
            "tone_appropriate": True
        }
        
        # Check for emojis
        if self._has_emojis(response):
            validation["compliant"] = False
            validation["has_emojis"] = True
            validation["issues"].append("Contains emojis")
        
        # Check length (should be reasonable)
        if len(response) > 500:
            validation["issues"].append("Response too long")
        
        # Check for inappropriate content
        inappropriate_words = ["fuck", "shit", "damn", "hell"]
        if any(word in response.lower() for word in inappropriate_words):
            validation["compliant"] = False
            validation["tone_appropriate"] = False
            validation["issues"].append("Inappropriate language")
        
        return validation
    
    def _has_emojis(self, text: str) -> bool:
        """Check if text contains emojis"""
        import re
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"
            "\U0001F300-\U0001F5FF"
            "\U0001F680-\U0001F6FF"
            "\U0001F1E0-\U0001F1FF"
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+",
            flags=re.UNICODE
        )
        return bool(emoji_pattern.search(text))


# Global service instance
coochie_conversation_service = CoochieConversationService()


# Export main components
__all__ = [
    "CoochieConversationService",
    "coochie_conversation_service"
]
