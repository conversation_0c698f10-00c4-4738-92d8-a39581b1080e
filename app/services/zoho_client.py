"""
Zoho CRM API Client
Handles authentication and API calls to Zoho CRM
"""

import json
import aiohttp
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from app.core.config.settings import settings
from app.core.logging import logger


class ZohoCRMClient:
    """Zoho CRM API client for handling authentication and API calls"""
    
    def __init__(self):
        self.client_id = settings.ZOHO_CLIENT_ID
        self.client_secret = settings.ZOHO_CLIENT_SECRET
        self.refresh_token = settings.ZOHO_REFRESH_TOKEN
        self.base_url = settings.ZOHO_BASE_URL
        self.auth_url = settings.ZOHO_AUTH_URL
        self.access_token = None
        self.token_expires_at = None
        self.last_request_time = None
        self.min_request_interval = 2.0  # Minimum 2 seconds between requests
        self.rate_limit_reset_time = None
        self.token_refresh_in_progress = False  # Prevent multiple simultaneous refreshes
    
    async def get_access_token(self) -> str:
        """Get access token using your lifetime refresh token"""
        try:
            # Check if current token is still valid (with 5 minute buffer)
            if (self.access_token and self.token_expires_at and
                datetime.utcnow() < self.token_expires_at - timedelta(minutes=5)):
                logger.debug("Using cached access token (still valid)")
                return self.access_token

            # Prevent multiple simultaneous token requests
            if self.token_refresh_in_progress:
                logger.info("Access token request already in progress, waiting...")
                await asyncio.sleep(2)
                if self.access_token:
                    return self.access_token

            self.token_refresh_in_progress = True
            logger.info("Getting new access token using lifetime refresh token...")

            # Use your lifetime refresh token to get access token
            data = {
                "refresh_token": self.refresh_token,
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "refresh_token"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.auth_url, data=data) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        logger.info(f"Zoho token response: {token_data}")

                        # Check if access_token exists in response
                        if "access_token" not in token_data:
                            logger.error(f"No access_token in response. Available keys: {list(token_data.keys())}")
                            raise Exception(f"No access_token in response: {token_data}")

                        self.access_token = token_data["access_token"]
                        expires_in = token_data.get("expires_in", 3600)  # Default 1 hour
                        self.token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in)

                        # Note: We keep your lifetime refresh token from .env file
                        # Don't update it even if Zoho returns a new one
                        logger.info(f"✅ Successfully obtained access token (expires in {expires_in} seconds)")
                        logger.info(f"✅ Using your lifetime refresh token from .env file")

                        self.token_refresh_in_progress = False
                        return self.access_token
                    else:
                        try:
                            error_data = await response.json()
                            error = error_data.get("error", "unknown_error")
                            error_description = error_data.get("error_description", "No description")

                            if error == "invalid_client":
                                logger.error("Invalid Zoho client credentials. Please check ZOHO_CLIENT_ID and ZOHO_CLIENT_SECRET")
                            elif error == "invalid_grant":
                                logger.error("Invalid refresh token. The refresh token may have expired and needs to be regenerated")
                            elif error == "Access Denied":
                                logger.error(f"Zoho access denied: {error_description}")
                            else:
                                logger.error(f"Zoho token error: {error} - {error_description}")

                            raise Exception(f"Failed to get access token: {response.status} - {error}: {error_description}")
                        except json.JSONDecodeError:
                            error_text = await response.text()
                            logger.error(f"Failed to get Zoho access token: {response.status} - {error_text}")
                            raise Exception(f"Failed to get access token: {response.status} - {error_text}")
                        
        except Exception as e:
            self.token_refresh_in_progress = False  # Reset flag on error
            logger.error(f"Error getting Zoho access token: {e}")
            # Provide helpful guidance for different errors
            if "invalid_client" in str(e).lower():
                logger.error("🔧 SOLUTION: Your Zoho client credentials are incorrect:")
                logger.error("1. Check ZOHO_CLIENT_ID in your .env file")
                logger.error("2. Check ZOHO_CLIENT_SECRET in your .env file")
                logger.error("3. Make sure they match your Zoho app credentials")
            elif "invalid_grant" in str(e).lower():
                logger.error("🔧 SOLUTION: Your Zoho refresh token is invalid:")
                logger.error("1. Check ZOHO_REFRESH_TOKEN in your .env file")
                logger.error("2. Make sure it's the correct lifetime refresh token")
                logger.error("3. Regenerate if necessary from Zoho Developer Console")
            raise

    def set_access_token(self, access_token: str, expires_in_seconds: int = 3600):
        """Manually set a valid access token to avoid refresh calls"""
        self.access_token = access_token
        self.token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in_seconds)
        logger.info(f"Manually set access token, expires at: {self.token_expires_at}")

    def is_token_valid(self) -> bool:
        """Check if current token is still valid"""
        return (self.access_token and self.token_expires_at and
                datetime.utcnow() < self.token_expires_at - timedelta(minutes=5))

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                          params: Optional[Dict] = None) -> Dict:
        """Make authenticated request to Zoho CRM API with rate limiting"""
        try:
            # Check if we're in a rate limit cooldown period
            if self.rate_limit_reset_time and datetime.utcnow().timestamp() < self.rate_limit_reset_time:
                wait_time = self.rate_limit_reset_time - datetime.utcnow().timestamp()
                logger.warning(f"Rate limit cooldown: waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                self.rate_limit_reset_time = None

            # Rate limiting: Wait if needed
            if self.last_request_time:
                time_since_last = datetime.utcnow().timestamp() - self.last_request_time
                if time_since_last < self.min_request_interval:
                    wait_time = self.min_request_interval - time_since_last
                    logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds")
                    await asyncio.sleep(wait_time)

            self.last_request_time = datetime.utcnow().timestamp()

            access_token = await self.get_access_token()
            url = f"{self.base_url}/{endpoint}"

            headers = {
                "Authorization": f"Zoho-oauthtoken {access_token}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                ) as response:
                    response_text = await response.text()

                    if response.status in [200, 201]:
                        return json.loads(response_text) if response_text else {}
                    elif response.status == 401:
                        # Token expired or invalid - try to regenerate once
                        logger.warning("Zoho token expired/invalid, attempting to regenerate...")
                        self.access_token = None  # Force token refresh
                        self.token_expires_at = None

                        # Retry the request with new token (only once to avoid infinite loop)
                        return await self._make_request_with_retry(method, endpoint, data, params)
                    elif response.status == 429 or "too many requests" in response_text.lower():
                        # Rate limited - set cooldown period
                        self.rate_limit_reset_time = datetime.utcnow().timestamp() + 60  # 1 minute cooldown
                        logger.error(f"Rate limited by Zoho API. Setting 60-second cooldown.")
                        raise Exception(f"Rate limited: {response_text}")
                    else:
                        logger.error(f"Zoho API error: {response.status} - {response_text}")
                        raise Exception(f"Zoho API error: {response.status} - {response_text}")
                        
        except Exception as e:
            logger.error(f"Error making Zoho API request: {e}")
            raise

    async def _make_request_with_retry(self, method: str, endpoint: str, data: Optional[Dict] = None,
                                     params: Optional[Dict] = None) -> Dict:
        """Retry request with fresh token (called only once to avoid infinite loop)"""
        try:
            # Get fresh access token
            access_token = await self.get_access_token()
            url = f"{self.base_url}/{endpoint}"

            headers = {
                "Authorization": f"Zoho-oauthtoken {access_token}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                ) as response:
                    response_text = await response.text()

                    if response.status in [200, 201]:
                        logger.info("Successfully regenerated token and completed request")
                        return json.loads(response_text) if response_text else {}
                    else:
                        logger.error(f"Zoho API error after token refresh: {response.status} - {response_text}")
                        raise Exception(f"Zoho API error after token refresh: {response.status} - {response_text}")

        except Exception as e:
            logger.error(f"Error making retry request: {e}")
            raise

    async def get_leads(self, params: Optional[Dict] = None) -> List[Dict]:
        """Get leads from Zoho CRM"""
        try:
            default_params = {
                "per_page": 200,
                "page": 1
            }
            if params:
                default_params.update(params)
            
            response = await self._make_request("GET", "Leads", params=default_params)
            return response.get("data", [])
            
        except Exception as e:
            logger.error(f"Error fetching leads from Zoho: {e}")
            raise
    
    async def create_lead(self, lead_data: Dict) -> Dict:
        """Create a single lead in Zoho CRM"""
        try:
            data = {"data": [lead_data]}
            response = await self._make_request("POST", "Leads", data=data)
            return response

        except Exception as e:
            logger.error(f"Error creating lead in Zoho: {e}")
            raise

    async def create_leads(self, leads_data: List[Dict]) -> List[Dict]:
        """Create leads in Zoho CRM"""
        try:
            data = {"data": leads_data}
            response = await self._make_request("POST", "Leads", data=data)
            return response.get("data", [])

        except Exception as e:
            logger.error(f"Error creating leads in Zoho: {e}")
            raise
    
    async def update_leads(self, leads_data: List[Dict]) -> List[Dict]:
        """Update leads in Zoho CRM"""
        try:
            data = {"data": leads_data}
            response = await self._make_request("PUT", "Leads", data=data)
            return response.get("data", [])
            
        except Exception as e:
            logger.error(f"Error updating leads in Zoho: {e}")
            raise
    
    async def get_lead_by_id(self, zoho_lead_id: str) -> Optional[Dict]:
        """Get a specific lead by Zoho ID"""
        try:
            response = await self._make_request("GET", f"Leads/{zoho_lead_id}")
            data = response.get("data", [])
            return data[0] if data else None
            
        except Exception as e:
            logger.error(f"Error fetching lead {zoho_lead_id} from Zoho: {e}")
            raise
    
    async def delete_lead(self, zoho_lead_id: str) -> bool:
        """Delete a lead in Zoho CRM"""
        try:
            await self._make_request("DELETE", f"Leads/{zoho_lead_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting lead {zoho_lead_id} in Zoho: {e}")
            raise
    
    async def search_leads(self, criteria: str) -> List[Dict]:
        """Search leads in Zoho CRM"""
        try:
            params = {"criteria": criteria}
            response = await self._make_request("GET", "Leads/search", params=params)
            return response.get("data", [])

        except Exception as e:
            logger.error(f"Error searching leads in Zoho: {e}")
            raise

    # Franchisor methods
    async def get_franchisor(self, params: Optional[Dict] = None) -> List[Dict]:
        """Get franchisor from Zoho CRM"""
        try:
            default_params = {
                "per_page": 200,
                "page": 1
            }
            if params:
                default_params.update(params)

            response = await self._make_request("GET", "Franchisor", params=default_params)
            return response.get("data", [])

        except Exception as e:
            logger.error(f"Error fetching franchisor from Zoho: {e}")
            raise

    async def create_franchisor(self, franchisor_data: Dict) -> Dict:
        """Create a single franchisor in Zoho CRM"""
        try:
            data = {"data": [franchisor_data]}
            response = await self._make_request("POST", "Franchisor", data=data)
            return response

        except Exception as e:
            logger.error(f"Error creating franchisor in Zoho: {e}")
            raise

    async def create_franchisor_batch(self, franchisor_data: List[Dict]) -> List[Dict]:
        """Create multiple franchisor in Zoho CRM (batch operation)"""
        try:
            # Zoho allows up to 100 records per batch
            batch_size = 100
            all_results = []

            for i in range(0, len(franchisor_data), batch_size):
                batch = franchisor_data[i:i + batch_size]
                logger.info(f"Creating batch of {len(batch)} franchisor (batch {i//batch_size + 1})")

                data = {"data": batch}
                response = await self._make_request("POST", "Franchisor", data=data)

                if response.get("data"):
                    all_results.extend(response["data"])

                # Small delay between batches
                if i + batch_size < len(franchisor_data):
                    await asyncio.sleep(1)

            return all_results

        except Exception as e:
            logger.error(f"Error creating franchisor batch in Zoho: {e}")
            raise

    async def update_franchisor(self, franchisor_data: Dict) -> Dict:
        """Update a franchisor in Zoho CRM"""
        try:
            data = {"data": [franchisor_data]}
            response = await self._make_request("PUT", "Franchisor", data=data)
            return response

        except Exception as e:
            logger.error(f"Error updating franchisor in Zoho: {e}")
            raise

    async def get_franchisor_by_id(self, zoho_franchisor_id: str) -> Optional[Dict]:
        """Get a specific franchisor by Zoho ID"""
        try:
            response = await self._make_request("GET", f"Franchisor/{zoho_franchisor_id}")
            data = response.get("data", [])
            return data[0] if data else None

        except Exception as e:
            logger.error(f"Error fetching franchisor {zoho_franchisor_id} from Zoho: {e}")
            raise


# Global client instance
zoho_client = ZohoCRMClient()
