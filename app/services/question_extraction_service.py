"""
Question Extraction Service
Automatically analyzes lead messages and extracts questions for the question_bank table
Integrates with <PERSON>'s existing agent logic for intelligent question detection
"""

import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from langchain_core.messages import SystemMessage
from langchain_openai import ChatOpenAI

from app.core.config.settings import settings
from app.core.database.connection import get_db
from app.models.lead_reference import QuestionBank
from app.models.conversation_message import ConversationMessage

from app.core.logging import logger


class QuestionExtractionService:
    """Service for extracting and storing questions from lead conversations"""
    
    def __init__(self):
        try:
            self.llm = ChatOpenAI(
                model=settings.OPENAI_MODEL,
                temperature=0.1,  # Lower temperature for more consistent extraction
                max_tokens=1000
            )
            self.ai_enabled = True
            logger.info("Question Extraction Service initialized with AI")
        except Exception as e:
            logger.warning(f"OpenAI not available, using fallback extraction: {str(e)}")
            self.llm = None
            self.ai_enabled = False
            logger.info("Question Extraction Service initialized with fallback only")
    
    async def analyze_and_extract_questions(
        self, 
        message: str, 
        lead_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        conversation_context: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """
        Analyze a lead's message and extract valid questions
        
        Args:
            message: The lead's message to analyze
            lead_id: UUID of the lead (optional)
            franchisor_id: UUID of the franchisor (optional)
            phone_number: Phone number for context (optional)
            conversation_context: Recent conversation history for context
            
        Returns:
            Dict containing extraction results and stored questions
        """
        try:
            logger.info(
                "Starting question extraction analysis",
                message_length=len(message),
                lead_id=lead_id,
                franchisor_id=franchisor_id
            )
            
            # Extract questions using AI analysis
            extracted_questions = await self._extract_questions_with_ai(
                message, conversation_context
            )
            
            if not extracted_questions:
                return {
                    "success": True,
                    "questions_found": 0,
                    "questions_stored": 0,
                    "extracted_questions": [],
                    "message": "No valid questions detected in message"
                }
            
            # Store valid questions in database
            stored_questions = await self._store_questions_in_database(
                extracted_questions,
                lead_id,
                franchisor_id,
                message
            )
            
            logger.info(
                "Question extraction completed",
                questions_found=len(extracted_questions),
                questions_stored=len(stored_questions),
                lead_id=lead_id
            )
            
            return {
                "success": True,
                "questions_found": len(extracted_questions),
                "questions_stored": len(stored_questions),
                "extracted_questions": extracted_questions,
                "stored_questions": stored_questions,
                "message": f"Successfully extracted and stored {len(stored_questions)} questions"
            }
            
        except Exception as e:
            logger.error("Question extraction failed", error=str(e), lead_id=lead_id)
            return {
                "success": False,
                "error": str(e),
                "questions_found": 0,
                "questions_stored": 0,
                "message": "Failed to extract questions from message"
            }
    
    async def _extract_questions_with_ai(
        self, 
        message: str, 
        conversation_context: Optional[List[Dict]] = None
    ) -> List[Dict[str, Any]]:
        """
        Use AI to intelligently extract questions from the message
        
        Args:
            message: The message to analyze
            conversation_context: Recent conversation for context
            
        Returns:
            List of extracted question dictionaries
        """
        try:
            # If AI is not available, use fallback immediately
            if not self.ai_enabled or not self.llm:
                logger.info("AI not available, using fallback extraction")
                return await self._fallback_question_extraction(message)

            # Build context from conversation history
            context_str = ""
            if conversation_context:
                recent_messages = conversation_context[-5:]  # Last 5 messages
                context_str = "\n".join([
                    f"{'Lead' if msg.get('sender') == 'lead' else 'Andy'}: {msg.get('message', '')}"
                    for msg in recent_messages
                ])

            # Create AI prompt for question extraction
            extraction_prompt = f"""You are an expert at analyzing lead conversations and extracting meaningful questions.

TASK: Analyze the lead's message and extract any genuine questions they are asking about franchise opportunities.

CONTEXT (Recent conversation):
{context_str if context_str else "No previous context"}

LEAD'S CURRENT MESSAGE: "{message}"

EXTRACTION RULES:
1. Only extract genuine questions (sentences ending with ? or clear question intent)
2. Ignore rhetorical questions, greetings, or casual expressions
3. Focus on franchise-related questions (investment, costs, training, support, etc.)
4. Combine related questions into single entries if they're about the same topic
5. Ensure questions are clear and meaningful for franchise qualification

RESPONSE FORMAT: Return a JSON array of question objects. Each question should have:
- "question": The extracted question text (cleaned and formatted)
- "category": The question category (financial, operational, training, support, general, etc.)
- "priority": Priority level (high, medium, low) based on franchise qualification importance
- "intent": The underlying intent (seeking_info, comparing_options, ready_to_invest, etc.)

EXAMPLES:
- "What are the startup costs?" → Valid question
- "How much training do you provide?" → Valid question  
- "Hello there" → Not a question
- "That sounds good" → Not a question
- "Can I afford this?" → Valid question (financial concern)

Return only the JSON array, no other text."""

            # Get AI response
            response = await self.llm.ainvoke([SystemMessage(content=extraction_prompt)])
            response_text = response.content.strip()
            
            # Clean and parse JSON response
            if response_text.startswith("```"):
                response_text = response_text.replace("```json", "").replace("```", "").strip()
            
            try:
                questions = json.loads(response_text)
                if isinstance(questions, list):
                    # Validate and clean extracted questions
                    valid_questions = []
                    for q in questions:
                        if isinstance(q, dict) and q.get("question"):
                            # Ensure required fields
                            valid_q = {
                                "question": str(q.get("question", "")).strip(),
                                "category": str(q.get("category", "general")).lower(),
                                "priority": str(q.get("priority", "medium")).lower(),
                                "intent": str(q.get("intent", "seeking_info")).lower()
                            }
                            if valid_q["question"]:
                                valid_questions.append(valid_q)
                    
                    logger.info(f"AI extracted {len(valid_questions)} valid questions")
                    return valid_questions
                else:
                    logger.warning("AI response was not a list format")
                    return []
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse AI JSON response: {e}")
                # Fallback to pattern-based extraction
                return await self._fallback_question_extraction(message)
                
        except Exception as e:
            logger.error(f"AI question extraction failed: {e}")
            # Fallback to pattern-based extraction
            return await self._fallback_question_extraction(message)
    
    async def _fallback_question_extraction(self, message: str) -> List[Dict[str, Any]]:
        """
        Fallback pattern-based question extraction when AI fails
        
        Args:
            message: The message to analyze
            
        Returns:
            List of extracted questions using pattern matching
        """
        questions = []
        message_lower = message.lower()
        
        # Common question patterns for franchise inquiries
        question_patterns = [
            (r"what.*cost", "financial", "high"),
            (r"how much.*investment", "financial", "high"),
            (r"what.*fee", "financial", "high"),
            (r"how.*training", "training", "medium"),
            (r"what.*support", "support", "medium"),
            (r"how.*profit", "financial", "high"),
            (r"what.*requirement", "operational", "medium"),
            (r"how.*start", "operational", "medium"),
            (r"when.*open", "operational", "low"),
            (r"where.*location", "operational", "medium")
        ]
        
        # Look for question marks and question words
        sentences = message.split('.')
        for sentence in sentences:
            sentence = sentence.strip()
            if '?' in sentence or any(word in sentence.lower() for word in ['what', 'how', 'when', 'where', 'why', 'can i', 'do you']):
                # Categorize based on patterns
                category = "general"
                priority = "medium"
                
                for pattern, cat, pri in question_patterns:
                    import re
                    if re.search(pattern, sentence.lower()):
                        category = cat
                        priority = pri
                        break
                
                if len(sentence) > 10:  # Avoid very short questions
                    questions.append({
                        "question": sentence.strip(),
                        "category": category,
                        "priority": priority,
                        "intent": "seeking_info"
                    })
        
        logger.info(f"Fallback extraction found {len(questions)} questions")
        return questions
    
    async def _store_questions_in_database(
        self,
        questions: List[Dict[str, Any]],
        lead_id: Optional[str],
        franchisor_id: Optional[str],
        original_message: str
    ) -> List[Dict[str, Any]]:
        """
        Store extracted questions in the question_bank table
        
        Args:
            questions: List of extracted questions
            lead_id: UUID of the lead
            franchisor_id: UUID of the franchisor
            original_message: Original message for context
            
        Returns:
            List of successfully stored questions with their database IDs
        """
        stored_questions = []
        
        try:
            async for db in get_db():
                for question_data in questions:
                    try:
                        # Create question bank entry
                        question_entry = QuestionBank(
                            id=uuid.uuid4(),
                            name=question_data["question"],
                            lead_id=uuid.UUID(lead_id) if lead_id else None,
                            franchisor_id=uuid.UUID(franchisor_id) if franchisor_id else None,
                            is_active=True,
                            is_deleted=False
                        )
                        
                        db.add(question_entry)
                        await db.commit()
                        
                        stored_questions.append({
                            "id": str(question_entry.id),
                            "question": question_data["question"],
                            "category": question_data["category"],
                            "priority": question_data["priority"],
                            "intent": question_data["intent"],
                            "lead_id": lead_id,
                            "franchisor_id": franchisor_id,
                            "created_at": question_entry.created_at.isoformat()
                        })
                        
                        logger.info(
                            "Question stored in database",
                            question_id=str(question_entry.id),
                            question=question_data["question"][:50] + "...",
                            lead_id=lead_id
                        )
                        
                    except Exception as e:
                        logger.error(
                            "Failed to store individual question",
                            error=str(e),
                            question=question_data.get("question", "")[:50]
                        )
                        await db.rollback()
                        continue
                
                break  # Exit the async for loop
                
        except Exception as e:
            logger.error("Database storage failed", error=str(e))
            
        return stored_questions


# Global service instance
_question_extraction_service: Optional[QuestionExtractionService] = None


def get_question_extraction_service() -> QuestionExtractionService:
    """Get or create Question Extraction Service instance"""
    global _question_extraction_service
    
    if _question_extraction_service is None:
        _question_extraction_service = QuestionExtractionService()
    
    return _question_extraction_service
