"""
General Message Service
Business logic for general message operations
"""

from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from app.repositories.general_message_repository import GeneralMessageRepository
from app.schemas.general_message import GeneralMessageUpdateRequest
from app.core.app_rules import success_response, error_response
from app.core.responses.models import ErrorCodes
import logging

logger = logging.getLogger(__name__)


class GeneralMessageService:
    """Service for general message operations."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = GeneralMessageRepository(db)

    async def fetch_general_msg_by_type(self, message_type: str) -> Dict[str, Any]:
        """
        Fetch a general message by message_type.
        
        Args:
            message_type: Message type to fetch
            
        Returns:
            Success response with message details or error response
        """
        try:
            message = await self.repository.get_message_by_type(message_type)
            
            if not message:
                return error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    title="Message Not Found",
                    description=f"No general message found for type '{message_type}'"
                )
            
            response_data = {
                "id": message.id,
                "message": message.message,
                "message_type": message.message_type
            }
            
            return success_response(
                details=response_data,
                title="General Message Retrieved",
                description=f"Successfully retrieved general message for type '{message_type}'"
            )
        except Exception as e:
            logger.error(f"Error in fetch_general_msg_by_type: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Fetch General Message",
                description="An error occurred while fetching the general message"
            )

    async def update_general_message_by_type(self, message_type: str, message_data: GeneralMessageUpdateRequest) -> Dict[str, Any]:
        """
        Update a general message by message_type.
        
        Args:
            message_type: Message type to update
            message_data: Updated message data
            
        Returns:
            Success response with updated message details
        """
        try:
            message = await self.repository.update_message_by_type(message_type, message_data)
            
            if not message:
                return error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    title="Message Not Found",
                    description=f"No general message found for type '{message_type}' to update"
                )
            
            response_data = {
                "id": message.id,
                "message": message.message,
                "message_type": message.message_type
            }
            
            return success_response(
                details=response_data,
                title="General Message Updated",
                description=f"Successfully updated general message for type '{message_type}'"
            )
        except Exception as e:
            logger.error(f"Error in update_general_message_by_type: {e}")
            return error_response(
                error_code=ErrorCodes.UNKNOWN_ERROR,
                title="Failed to Update General Message",
                description="An error occurred while updating the general message"
            )
