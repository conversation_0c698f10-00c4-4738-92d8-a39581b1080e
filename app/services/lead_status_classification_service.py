"""
Lead Status Classification Service
Handles rule-based keyword/regex matching and OpenAI LLM fallback for complex cases.
"""

import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import openai
import structlog

from app.core.config.settings import settings
from app.services.lead_status_normalization_service import status_normalizer

from app.core.logging import logger


@dataclass
class ClassificationResult:
    """Result of lead status classification"""
    label: str
    confidence: float
    rationale: str
    method: str  # "rule_based" or "llm"


class StatusContext:
    """Context information for status classification"""
    def __init__(
        self,
        current_status: Optional[str] = None,
        last_outbound_at: Optional[str] = None,
        last_inbound_at: Optional[str] = None,
        territory_available: bool = True,
        min_budget: Optional[float] = None,
        call_attempts: int = 0
    ):
        self.current_status = current_status
        self.last_outbound_at = last_outbound_at
        self.last_inbound_at = last_inbound_at
        self.territory_available = territory_available
        self.min_budget = min_budget
        self.call_attempts = call_attempts


class LeadStatusClassificationService:
    """Service for classifying lead messages into appropriate statuses"""
    
    def __init__(self):
        """Initialize the classification service"""
        self.openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        self.confidence_threshold = 0.6
        
        # Rule-based patterns (high confidence, no LLM needed)
        self.rule_patterns = {
            "Not Interested": [
                r'\b(stop|do not contact|unsubscribe|not interested|no longer interested|remove me)\b',
                r'\b(not for me|don\'t want|no thanks|not looking)\b',
                r'\b(already have)\b'
            ],
            "Wrong Number": [
                r'\b(wrong number|you have the wrong|this is not|never inquired)\b',
                r'\b(don\'t know|not me|mistake|wrong person)\b'
            ],
            "Call Back": [
                r'\b(call me later|text.*tomorrow|not now|busy now)\b',
                r'\b(tomorrow|next week|call back|later today|after \d+)\b',
                r'\b(at work|maybe next week|call me after)\b'
            ],
            "Follow up Required": [
                r'\b(send details|please email|share info|i\'ll check)\b',
                r'\b(send me|email me|more information|tell me more)\b',
                r'\b(what\'s involved|how much|pricing|cost)\b'
            ],
            "Region is not available": [
                r'\b(my city not available|region not available|no territory)\b',
                r'\b(area taken|location not available|not in my area)\b'
            ],
            "Out of Budget": [
                r'\b(too expensive|can\'t afford|out of.*budget)\b',
                r'\b(budget.*lower|too much money|beyond.*budget)\b',
                r'\b(cheaper|less expensive|looking for something cheaper)\b'
            ],
            "EOI/NDA Sent": [
                r'\b(send eoi|please send eoi|send nda|share eoi|send the forms)\b',
                r'\b(email the documents|forward the paperwork)\b'
            ],
            "EOI/NDA Signed": [
                r'\b(signed eoi|signed nda|eoi completed|returned the forms)\b',
                r'\b(documents signed|paperwork done)\b'
            ],
            "Application Form Signed": [
                r'\b(submitted application|application done|application signed)\b',
                r'\b(completed the application|sent the application)\b'
            ],
            "Deposit Paid": [
                r'\b(paid deposit|payment made|deposit done|transferred the money)\b',
                r'\b(payment sent|deposit transferred)\b'
            ],
            "Franchise Sold": [
                r'\b(proceeding with purchase|deal closed|i\'m buying)\b',
                r'\b(going ahead|ready to proceed|let\'s do this)\b',
                r'\b(yes to the franchise|accept the offer)\b'
            ],
            "Qualified": [
                r'\b(yes.*interested|i\'m interested|sounds good|keen to proceed)\b',
                r'\b(let\'s book.*call|ready to move forward|want to proceed)\b',
                r'\b(this looks good|i\'m keen|very interested)\b'
            ]
        }
        
        # Status precedence (highest first)
        self.status_precedence = [
            "Franchise Sold", "Deposit Paid", "Application Form Signed",
            "EOI/NDA Signed", "EOI/NDA Sent", "Qualified", "Not Interested",
            "Wrong Number", "Out of Budget", "Region is not available",
            "Call Back", "Follow up Required", "Contacted", "New Lead"
        ]
    
    async def classify_message(
        self, 
        message_text: str, 
        context: Optional[StatusContext] = None
    ) -> ClassificationResult:
        """
        Classify a lead message to determine appropriate status
        
        Args:
            message_text: The message content to classify
            context: Additional context for classification
            
        Returns:
            ClassificationResult with label, confidence, and rationale
        """
        if not message_text or not message_text.strip():
            return ClassificationResult(
                label="New Lead",
                confidence=0.0,
                rationale="Empty message provided",
                method="rule_based"
            )
        
        # Step 1: Try rule-based classification first
        rule_result = self._classify_with_rules(message_text, context)
        if rule_result.confidence >= 0.8:  # High confidence rule match
            logger.info(f"Rule-based classification: {rule_result.label} (confidence: {rule_result.confidence})")
            return rule_result
        
        # Step 2: Use LLM for complex cases
        try:
            llm_result = await self._classify_with_llm(message_text, context)
            
            # If LLM confidence is low, return rule result if available
            if llm_result.confidence < self.confidence_threshold and rule_result.confidence > 0:
                logger.info(f"LLM confidence too low, using rule result: {rule_result.label}")
                return rule_result
            
            logger.info(f"LLM classification: {llm_result.label} (confidence: {llm_result.confidence})")
            return llm_result
            
        except Exception as e:
            logger.error(f"LLM classification failed: {e}")
            # Fallback to rule result or default
            if rule_result.confidence > 0:
                return rule_result
            
            return ClassificationResult(
                label="Follow up Required",
                confidence=0.3,
                rationale="Classification failed, defaulting to follow up",
                method="fallback"
            )
    
    def _classify_with_rules(
        self, 
        message_text: str, 
        context: Optional[StatusContext] = None
    ) -> ClassificationResult:
        """Classify using rule-based patterns"""
        message_lower = message_text.lower()
        matched_statuses = []
        
        # Check each rule pattern
        for status, patterns in self.rule_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower, re.IGNORECASE):
                    matched_statuses.append(status)
                    break  # One match per status is enough
        
        if not matched_statuses:
            return ClassificationResult(
                label="Follow up Required",
                confidence=0.2,
                rationale="No specific patterns matched",
                method="rule_based"
            )
        
        # Apply precedence if multiple matches
        for status in self.status_precedence:
            if status in matched_statuses:
                confidence = 0.9 if len(matched_statuses) == 1 else 0.8
                rationale = f"Matched rule patterns for '{status}'"
                if len(matched_statuses) > 1:
                    rationale += f" (also matched: {', '.join([s for s in matched_statuses if s != status])})"
                
                return ClassificationResult(
                    label=status,
                    confidence=confidence,
                    rationale=rationale,
                    method="rule_based"
                )
        
        # Fallback (shouldn't reach here)
        return ClassificationResult(
            label=matched_statuses[0],
            confidence=0.7,
            rationale=f"Matched patterns for {matched_statuses[0]}",
            method="rule_based"
        )
    
    async def _classify_with_llm(
        self, 
        message_text: str, 
        context: Optional[StatusContext] = None
    ) -> ClassificationResult:
        """Classify using OpenAI LLM with few-shot examples"""
        
        # Build context information
        context_info = ""
        if context:
            context_info = f"""
Current Status: {context.current_status or 'Unknown'}
Territory Available: {context.territory_available}
Call Attempts: {context.call_attempts}
"""
        
        # Few-shot examples from specification
        few_shot_examples = '''
Input: "hey, I'm at work now. Can we talk tomorrow evening?"
Label: Call Back • Confidence: 0.86 • Rationale: explicit request to talk later.

Input: "Not interested, please stop messaging."
Label: Not Interested • Confidence: 0.99 • Rationale: opt-out phrase.

Input: "This is Priya. You have the wrong number."
Label: Wrong Number • Confidence: 0.98 • Rationale: wrong recipient.

Input: "Budget is around 5 lakhs; your brochure says minimum 15."
Label: Out of Budget • Confidence: 0.88 • Rationale: budget below minimum requirement.

Input: "Send me the EOI please."
Label: EOI/NDA Sent • Confidence: 0.9 • Rationale: requesting EOI documents.

Input: "I've signed and returned the NDA."
Label: EOI/NDA Signed • Confidence: 0.95 • Rationale: completed NDA process.

Input: "Application submitted yesterday."
Label: Application Form Signed • Confidence: 0.92 • Rationale: application completed.

Input: "Paid the initial deposit just now."
Label: Deposit Paid • Confidence: 0.96 • Rationale: deposit payment confirmed.

Input: "We're going ahead with this franchise."
Label: Franchise Sold • Confidence: 0.93 • Rationale: commitment to purchase.

Input: "Can you share more info and pricing?"
Label: Follow up Required • Confidence: 0.7 • Rationale: requesting more information.

Input: "Yes I'm keen, let's book a call."
Label: Qualified • Confidence: 0.82 • Rationale: expressing strong interest.

Input: "Yes, I'm interested in learning more."
Label: Qualified • Confidence: 0.85 • Rationale: clear expression of interest.

Input: "This sounds good, tell me more."
Label: Qualified • Confidence: 0.80 • Rationale: positive response showing interest.
'''
        
        prompt = f"""You are an expert at classifying lead messages for franchise sales. 

Available status labels: {', '.join(status_normalizer.get_all_canonical_statuses())}

Context: {context_info}

Examples:
{few_shot_examples}

Now classify this message:
Input: "{message_text}"

Respond in this exact format:
Label: [STATUS] • Confidence: [0.0-1.0] • Rationale: [brief explanation]"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a lead status classification expert."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content.strip()
            return self._parse_llm_response(result_text)
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    def _parse_llm_response(self, response_text: str) -> ClassificationResult:
        """Parse LLM response into ClassificationResult"""
        try:
            # Expected format: "Label: STATUS • Confidence: 0.XX • Rationale: explanation"
            parts = response_text.split(' • ')
            
            if len(parts) != 3:
                raise ValueError(f"Invalid response format: {response_text}")
            
            label = parts[0].replace("Label: ", "").strip()
            confidence_str = parts[1].replace("Confidence: ", "").strip()
            rationale = parts[2].replace("Rationale: ", "").strip()
            
            confidence = float(confidence_str)
            
            # Normalize the label
            normalized_label, norm_confidence = status_normalizer.normalize_status_name(label)
            if norm_confidence < 0.9:
                logger.warning(f"LLM returned non-canonical status: {label}, normalized to: {normalized_label}")
            
            return ClassificationResult(
                label=normalized_label,
                confidence=confidence,
                rationale=rationale,
                method="llm"
            )
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {response_text}, error: {e}")
            return ClassificationResult(
                label="Follow up Required",
                confidence=0.3,
                rationale="Failed to parse LLM response",
                method="llm"
            )


# Global instance
lead_classifier = LeadStatusClassificationService()
