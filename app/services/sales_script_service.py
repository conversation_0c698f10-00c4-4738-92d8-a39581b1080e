"""
Sales Script Service
Business logic for sales script operations
"""

import logging
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException

from app.models.sales_script import SalesScript
from app.repositories.sales_script_repository import SalesScriptRepository
from app.schemas.sales_script import (
    SalesScriptCreate,
    SalesScriptUpdate,
    SalesScriptResponse,
    SalesScriptListResponse
)

from app.core.utils.exception_manager.custom_exceptions import DatabaseError

logger = logging.getLogger(__name__)


class SalesScriptService:
    """Service for handling sales script operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = SalesScriptRepository(db)
    
    def _convert_to_response(self, script: SalesScript) -> SalesScriptResponse:
        """Convert SalesScript model to response schema"""
        return SalesScriptResponse(
            id=str(script.id),
            script_title=script.script_title,
            script_content=script.script_content,
            script_stage=script.script_stage,
            order_sequence=script.order_sequence,
            has_variables=script.has_variables,
            variable_schema=script.variable_schema,
            is_active=script.is_active,
            is_deleted=script.is_deleted,
            created_at=script.created_at,
            updated_at=script.updated_at,
            deleted_at=script.deleted_at
        )
    
    async def get_scripts_with_pagination(
        self,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        script_stage: Optional[str] = None,
        is_active: Optional[bool] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "asc"
    ) -> SalesScriptListResponse:
        """Get sales scripts with filtering, search, and pagination"""
        try:
            scripts, total_count = await self.repository.get_all_with_filters(
                skip=skip,
                limit=limit,
                search=search,
                script_stage=script_stage,
                is_active=is_active,
                sort_by=sort_by,
                sort_order=sort_order
            )
            
            # Convert to response format
            script_responses = []
            for script in scripts:
                script_response = self._convert_to_response(script)
                script_responses.append(script_response)
            
            return SalesScriptListResponse(
                items=script_responses,
                total_count=total_count
            )
            
        except Exception as e:
            logger.error(f"Error getting sales scripts: {e}")
            raise DatabaseError(
                error_key="SALES_SCRIPTS_RETRIEVAL_FAILED",
                message="Failed to retrieve sales scripts"
            )
    
    async def get_script_by_id(self, script_id: str) -> Optional[SalesScriptResponse]:
        """Get sales script by ID"""
        try:
            script = await self.repository.get_by_id(script_id)
            
            if not script:
                return None
            
            return self._convert_to_response(script)
            
        except Exception as e:
            logger.error(f"Error getting sales script by ID {script_id}: {e}")
            raise DatabaseError(
                error_key="SALES_SCRIPT_RETRIEVAL_FAILED",
                message="Failed to retrieve sales script"
            )
    
    async def create_script(self, script_data: SalesScriptCreate) -> SalesScriptResponse:
        """Create a new sales script"""
        try:
            # Check if script with same title already exists
            existing_script = await self.repository.get_by_title(script_data.script_title)
            if existing_script:
                raise HTTPException(
                    status_code=400,
                    detail="Sales script with this title already exists"
                )
            
            script = await self.repository.create_script(script_data)
            return self._convert_to_response(script)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating sales script: {e}")
            raise DatabaseError(
                error_key="SALES_SCRIPT_CREATION_FAILED",
                message="Failed to create sales script"
            )
    
    async def update_script(self, script_id: str, script_data: SalesScriptUpdate) -> Optional[SalesScriptResponse]:
        """Update a sales script"""
        try:
            # Check if updating title and it already exists
            if script_data.script_title:
                existing_script = await self.repository.get_by_title(script_data.script_title)
                if existing_script and str(existing_script.id) != script_id:
                    raise HTTPException(
                        status_code=400,
                        detail="Sales script with this title already exists"
                    )
            
            script = await self.repository.update_script(script_id, script_data)
            
            if not script:
                return None
            
            return self._convert_to_response(script)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating sales script {script_id}: {e}")
            raise DatabaseError(
                error_key="SALES_SCRIPT_UPDATE_FAILED",
                message="Failed to update sales script"
            )
    
    async def delete_script(self, script_id: str) -> bool:
        """Delete a sales script"""
        try:
            return await self.repository.delete_script(script_id)
            
        except Exception as e:
            logger.error(f"Error deleting sales script {script_id}: {e}")
            raise DatabaseError(
                error_key="SALES_SCRIPT_DELETION_FAILED",
                message="Failed to delete sales script"
            )
    
    async def get_scripts_by_stage(self, script_stage: str, is_active: bool = True) -> List[SalesScriptResponse]:
        """Get sales scripts by stage"""
        try:
            scripts = await self.repository.get_by_stage(script_stage, is_active)
            
            script_responses = []
            for script in scripts:
                script_response = self._convert_to_response(script)
                script_responses.append(script_response)
            
            return script_responses
            
        except Exception as e:
            logger.error(f"Error getting sales scripts by stage {script_stage}: {e}")
            raise DatabaseError(
                error_key="SALES_SCRIPTS_BY_STAGE_FAILED",
                message="Failed to retrieve sales scripts by stage"
            )
