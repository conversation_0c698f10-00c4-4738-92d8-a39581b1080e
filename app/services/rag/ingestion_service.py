"""
Franchisor-scoped document ingestion service
"""

from typing import List, Dict, Any, Optional
from uuid import uuid4
import json
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import AsyncSessionLocal
from app.core.logging import logger
from app.models.franchisor_chunk import FranchisorChunk
from app.models.franchisor import Franchisor  # Import to ensure metadata is loaded
from app.services.rag.franchisor_embedding_service import franchisor_embedding_service

# Import DocQA components for text processing
from docqa.text_processing.production_text_processor import ProductionTextNormalizer, ProductionChunker
from docqa.types import DocumentChunk, DocumentMetadata
from docqa.vector_store.production_embeddings import ProductionEmbeddingService


class FranchisorDocumentIngestionService:
    """
    Service for ingesting documents into franchisor_chunks with proper scoping
    """
    
    def __init__(self):
        self.normalizer = ProductionTextNormalizer()
        self.chunker = ProductionChunker()
        self.embedding_service = ProductionEmbeddingService()

    async def ingest_franchisor_document(
        self,
        franchisor_id: Optional[str],
        content: str,
        source_url: Optional[str] = None,
        source_title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        clear_existing: bool = False
    ) -> Dict[str, Any]:
        """
        Ingest a document for a specific franchisor or as global content
        
        Args:
            franchisor_id: Target franchisor UUID (None for global chunks)
            content: Document text content
            source_url: Source URL or file path
            source_title: Document title
            metadata: Additional metadata
            clear_existing: Whether to clear existing chunks for this franchisor
            
        Returns:
            Ingestion result with statistics
        """
        async with AsyncSessionLocal() as session:
            try:
                # Clear existing chunks if requested
                if clear_existing and franchisor_id:
                    await self._clear_franchisor_chunks(session, franchisor_id)
                
                # Normalize and chunk the document
                normalized_content = self.normalizer.normalize_text(content)
                document_metadata = DocumentMetadata(
                    document_id=str(uuid4()),
                    filename=source_title or "Unknown Document",
                    file_type="pdf",
                    file_size=0,  # Will be set later if needed
                    source_url=source_url
                )
                
                chunks = self.chunker.chunk_text(normalized_content, metadata or {})
                
                if not chunks:
                    logger.warning(f"No chunks generated from document for franchisor {franchisor_id}")
                    return {
                        "success": False,
                        "error": "No chunks generated",
                        "chunks_created": 0
                    }
                
                # Generate embeddings for all chunks
                chunk_embeddings = []
                for chunk in chunks:
                    embedding = self.embedding_service.generate_embedding(chunk.text)
                    chunk_embeddings.append(embedding)
                
                # Store chunks in database
                created_chunks = await self._store_chunks(
                    session,
                    franchisor_id,
                    chunks,
                    chunk_embeddings,
                    source_url,
                    source_title,
                    metadata or {}
                )
                
                # Update franchisor summary embedding if this is franchisor-specific content
                if franchisor_id:
                    await franchisor_embedding_service.update_franchisor_summary_embedding(
                        franchisor_id,
                        force_recompute=True
                    )
                
                logger.info(f"Successfully ingested {len(created_chunks)} chunks for franchisor {franchisor_id}")
                
                return {
                    "success": True,
                    "chunks_created": len(created_chunks),
                    "franchisor_id": franchisor_id,
                    "source_url": source_url,
                    "source_title": source_title,
                    "chunk_ids": [str(chunk.id) for chunk in created_chunks]
                }
                
            except Exception as e:
                logger.error(f"Document ingestion failed for franchisor {franchisor_id}: {e}")
                await session.rollback()
                return {
                    "success": False,
                    "error": str(e),
                    "chunks_created": 0
                }

    async def _clear_franchisor_chunks(
        self,
        session: AsyncSession,
        franchisor_id: str
    ) -> None:
        """Clear existing chunks for a franchisor"""
        try:
            result = await session.execute(
                text("DELETE FROM franchisor_chunks WHERE franchisor_id = :fid"),
                {"fid": franchisor_id}
            )
            deleted_count = result.rowcount
            await session.commit()
            
            if deleted_count > 0:
                logger.info(f"Cleared {deleted_count} existing chunks for franchisor {franchisor_id}")
                
        except Exception as e:
            logger.error(f"Failed to clear existing chunks for franchisor {franchisor_id}: {e}")
            raise

    async def _store_chunks(
        self,
        session: AsyncSession,
        franchisor_id: Optional[str],
        chunks: List[DocumentChunk],
        embeddings: List[List[float]],
        source_url: Optional[str],
        source_title: Optional[str],
        metadata: Dict[str, Any]
    ) -> List[FranchisorChunk]:
        """Store chunks and their embeddings in the database using raw SQL"""
        try:
            created_chunks = []
            
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                # Extract page number from chunk metadata if available
                page_no = None
                if hasattr(chunk, 'metadata') and chunk.metadata:
                    page_no = chunk.metadata.get('page_number')
                
                # Extract section from chunk metadata if available
                section = None
                if hasattr(chunk, 'metadata') and chunk.metadata:
                    section = chunk.metadata.get('section')
                
                # Prepare chunk metadata
                chunk_metadata = {
                    **metadata,
                    "chunk_index": i,
                    "token_count": getattr(chunk, 'token_count', len(chunk.text.split())),
                    "chunk_type": getattr(chunk, 'chunk_type', 'text'),
                    **(chunk.metadata if hasattr(chunk, 'metadata') and chunk.metadata else {})
                }
                
                # Generate UUID for this chunk
                chunk_id = uuid4()
                
                # Store additional metadata in the metadata JSON field
                full_metadata = {
                    **chunk_metadata,
                    "source_url": source_url,
                    "source_title": source_title,
                    "page_no": page_no,
                    "section": section
                }
                
                # Use raw SQL to insert the chunk to bypass SQLAlchemy FK validation
                await session.execute(
                    text("""
                        INSERT INTO franchisor_chunks (
                            id, franchisor_id, text, metadata, embedding, chunk_index, token_count
                        ) VALUES (
                            :id, :franchisor_id, :text, :metadata, :embedding, :chunk_index, :token_count
                        )
                    """),
                    {
                        "id": chunk_id,
                        "franchisor_id": franchisor_id,
                        "text": chunk.text,
                        "metadata": json.dumps(full_metadata),  # Convert dict to JSON string
                        "embedding": str(embedding),  # Convert list to string for PostgreSQL vector
                        "chunk_index": i,
                        "token_count": getattr(chunk, 'token_count', len(chunk.text.split()))
                    }
                )
                
                # Create a FranchisorChunk object for return (without adding to session)
                franchisor_chunk = FranchisorChunk(
                    id=chunk_id,
                    franchisor_id=franchisor_id,
                    text=chunk.text,
                    chunk_metadata=full_metadata,
                    embedding=embedding
                )
                created_chunks.append(franchisor_chunk)
            
            await session.commit()
            logger.info(f"Stored {len(created_chunks)} chunks in database using raw SQL")
            
            return created_chunks
            
        except Exception as e:
            logger.error(f"Failed to store chunks in database: {e}")
            await session.rollback()
            raise

    async def ingest_brochure_from_url(
        self,
        franchisor_id: str,
        brochure_url: str,
        clear_existing: bool = True
    ) -> Dict[str, Any]:
        """
        Ingest a brochure from S3 URL for a specific franchisor
        
        Args:
            franchisor_id: Target franchisor UUID
            brochure_url: S3 URL of the brochure
            clear_existing: Whether to clear existing brochure chunks
            
        Returns:
            Ingestion result
        """
        try:
            # Use existing DocQA file handling capabilities
            import tempfile
            import requests
            from pathlib import Path
            from docqa.file_handlers.factory import FileHandlerFactory
            
            # Download the document temporarily
            response = requests.get(brochure_url, timeout=30)
            response.raise_for_status()
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
                tmp_file.write(response.content)
                tmp_file.flush()  # Ensure content is written to disk
                tmp_path = Path(tmp_file.name)
            
            try:
                # Verify file exists and has content
                if not tmp_path.exists():
                    return {
                        "success": False,
                        "error": "Temporary file was not created properly",
                        "chunks_created": 0
                    }
                
                file_size = tmp_path.stat().st_size
                logger.info(f"Downloaded PDF file: {tmp_path}, size: {file_size} bytes")
                
                if file_size == 0:
                    return {
                        "success": False,
                        "error": "Downloaded file is empty",
                        "chunks_created": 0
                    }
                
                # Use DocQA file handler to extract text
                handler_factory = FileHandlerFactory()
                handler = handler_factory.get_handler(tmp_path)
                
                # Process the file to extract text content
                processing_result = handler.process_file(tmp_path)
                
                if not processing_result.success:
                    error_msg = processing_result.error_message or "Unknown processing error"
                    logger.error(f"Document processing failed: {error_msg}")
                    return {
                        "success": False,
                        "error": f"Failed to process document: {error_msg}",
                        "chunks_created": 0
                    }
                
                # Extract text content from the processing result
                document_content = processing_result.text_content
                
            finally:
                # Clean up temporary file
                tmp_path.unlink(missing_ok=True)
            
            if not document_content:
                return {
                    "success": False,
                    "error": "Failed to load document content from URL",
                    "chunks_created": 0
                }
            
            # Extract title from URL or use default
            source_title = brochure_url.split('/')[-1] if '/' in brochure_url else "Franchise Brochure"
            
            # Ingest the document
            result = await self.ingest_franchisor_document(
                franchisor_id=franchisor_id,
                content=document_content,
                source_url=brochure_url,
                source_title=source_title,
                metadata={
                    "document_type": "brochure",
                    "ingestion_source": "brochure_upload_api"
                },
                clear_existing=clear_existing
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Brochure ingestion failed for franchisor {franchisor_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "chunks_created": 0
            }

    async def ingest_global_content(
        self,
        content: str,
        source_title: str,
        source_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Ingest global content that applies to all franchisors
        
        Args:
            content: Text content
            source_title: Document title
            source_url: Source URL (optional)
            metadata: Additional metadata
            
        Returns:
            Ingestion result
        """
        return await self.ingest_franchisor_document(
            franchisor_id=None,  # NULL for global content
            content=content,
            source_url=source_url,
            source_title=source_title,
            metadata={
                **(metadata or {}),
                "content_type": "global",
                "applicable_to": "all_franchisors"
            },
            clear_existing=False
        )

    async def get_ingestion_statistics(
        self, 
        franchisor_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get ingestion statistics for a franchisor or globally"""
        async with AsyncSessionLocal() as session:
            try:
                if franchisor_id:
                    # Franchisor-specific stats
                    result = await session.execute(
                        text("""
                            SELECT 
                                COUNT(*) as total_chunks,
                                COUNT(DISTINCT source_url) as unique_sources,
                                COUNT(DISTINCT source_title) as unique_documents,
                                MIN(created_at) as first_ingestion,
                                MAX(created_at) as last_ingestion
                            FROM franchisor_chunks
                            WHERE franchisor_id = :fid
                        """),
                        {"fid": franchisor_id}
                    )
                else:
                    # Global stats
                    result = await session.execute(
                        text("""
                            SELECT 
                                COUNT(*) as total_chunks,
                                COUNT(DISTINCT source_url) as unique_sources,
                                COUNT(DISTINCT source_title) as unique_documents,
                                MIN(created_at) as first_ingestion,
                                MAX(created_at) as last_ingestion
                            FROM franchisor_chunks
                            WHERE franchisor_id IS NULL
                        """)
                    )
                
                stats = result.fetchone()
                
                return {
                    "franchisor_id": franchisor_id,
                    "total_chunks": stats.total_chunks or 0,
                    "unique_sources": stats.unique_sources or 0,
                    "unique_documents": stats.unique_documents or 0,
                    "first_ingestion": stats.first_ingestion.isoformat() if stats.first_ingestion else None,
                    "last_ingestion": stats.last_ingestion.isoformat() if stats.last_ingestion else None
                }
                
            except Exception as e:
                logger.error(f"Failed to get ingestion statistics: {e}")
                return {
                    "franchisor_id": franchisor_id,
                    "total_chunks": 0,
                    "unique_sources": 0,
                    "unique_documents": 0,
                    "error": str(e)
                }


# Global instance
franchisor_ingestion_service = FranchisorDocumentIngestionService()
