"""
Service for managing franchisor summary embeddings
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import AsyncSessionLocal
from app.core.logging import logger
from app.models.franchisor import Franchisor
from docqa.vector_store.production_embeddings import ProductionEmbeddingService


class FranchisorEmbeddingService:
    """
    Service for computing and updating franchisor summary embeddings
    """
    
    def __init__(self):
        self.embedding_service = ProductionEmbeddingService()

    async def recompute_franchisor_embedding(
        self, 
        session: AsyncSession, 
        franchisor_id: str
    ) -> Optional[List[float]]:
        """
        Recompute franchisor summary embedding from all its chunks
        
        Args:
            session: Database session
            franchisor_id: Franchisor UUID
            
        Returns:
            New summary embedding vector or None if failed
        """
        try:
            # Get all embedding vectors for this franchisor
            result = await session.execute(
                text("""
                    SELECT embedding
                    FROM franchisor_chunks
                    WHERE franchisor_id = :fid
                    ORDER BY created_at
                """),
                {"fid": franchisor_id}
            )
            
            raw_embeddings = [row[0] for row in result.fetchall()]
            
            if raw_embeddings:
                # Convert PostgreSQL vector format to Python lists
                embeddings = []
                for raw_emb in raw_embeddings:
                    if isinstance(raw_emb, str):
                        # Parse string representation: "[1.0, 2.0, 3.0]"
                        emb_str = raw_emb.strip('[]')
                        emb_list = [float(x.strip()) for x in emb_str.split(',')]
                        embeddings.append(emb_list)
                    elif hasattr(raw_emb, '__iter__'):
                        # Already a list/array
                        embeddings.append(list(raw_emb))
                    else:
                        logger.error(f"Unexpected embedding format: {type(raw_emb)}")
                        continue
                
                if embeddings:
                    # Mean pooling of all chunk embeddings
                    embedding_dim = len(embeddings[0])
                    summary_embedding = [
                        sum(emb[i] for emb in embeddings) / len(embeddings)
                        for i in range(embedding_dim)
                    ]
                    
                    logger.info(f"Computed summary embedding from {len(embeddings)} chunks for franchisor {franchisor_id}")
                    return summary_embedding
                else:
                    logger.warning(f"No valid embeddings found after conversion for franchisor {franchisor_id}")
                    return None
            else:
                # Cold start: create embedding from fallback profile
                profile_text = await self._build_fallback_profile_text(session, franchisor_id)
                if profile_text:
                    summary_embedding = await self.embedding_service.embed_documents([profile_text])
                    if summary_embedding:
                        logger.info(f"Generated cold-start embedding for franchisor {franchisor_id}")
                        return summary_embedding[0]  # Return first (and only) embedding
                    else:
                        logger.error(f"Failed to generate cold-start embedding for franchisor {franchisor_id}")
                        return None
                else:
                    logger.warning(f"No chunks or profile data found for franchisor {franchisor_id}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to recompute embedding for franchisor {franchisor_id}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    async def _build_fallback_profile_text(
        self, 
        session: AsyncSession, 
        franchisor_id: str
    ) -> Optional[str]:
        """
        Build fallback profile text for cold-start embedding generation
        """
        try:
            # Get franchisor basic information
            result = await session.execute(
                text("""
                    SELECT 
                        name,
                        region,
                        budget,
                        contactfirstname,
                        contactlastname,
                        email,
                        phone
                    FROM franchisors
                    WHERE id = :fid
                """),
                {"fid": franchisor_id}
            )
            
            row = result.fetchone()
            if not row:
                return None
            
            # Build descriptive profile text
            profile_parts = []
            
            if row.name:
                profile_parts.append(f"Franchise brand: {row.name}")
            
            if row.region:
                profile_parts.append(f"Operating region: {row.region}")
            
            if row.budget:
                profile_parts.append(f"Investment range: ${row.budget:,.0f}")
            
            if row.contactfirstname and row.contactlastname:
                profile_parts.append(f"Contact: {row.contactfirstname} {row.contactlastname}")
            
            if row.email:
                profile_parts.append(f"Email: {row.email}")
            
            # Add generic franchise description
            profile_parts.append(f"{row.name} is a franchise opportunity offering business ownership and support.")
            profile_parts.append("This franchise provides training, marketing support, and ongoing business assistance.")
            
            profile_text = " ".join(profile_parts)
            logger.info(f"Built fallback profile for franchisor {franchisor_id}: {len(profile_text)} characters")
            
            return profile_text
            
        except Exception as e:
            logger.error(f"Failed to build fallback profile for franchisor {franchisor_id}: {e}")
            return None

    async def update_franchisor_summary_embedding(
        self, 
        franchisor_id: str,
        force_recompute: bool = False
    ) -> bool:
        """
        Update the franchisor's summary embedding in the database
        
        Args:
            franchisor_id: Franchisor UUID
            force_recompute: Force recomputation even if embedding exists
            
        Returns:
            True if successful, False otherwise
        """
        async with AsyncSessionLocal() as session:
            try:
                # Check if embedding already exists (unless forcing recompute)
                if not force_recompute:
                    existing_result = await session.execute(
                        text("SELECT embedding FROM franchisors WHERE id = :fid"),
                        {"fid": franchisor_id}
                    )
                    existing_embedding = existing_result.scalar()
                    if existing_embedding is not None:
                        logger.info(f"Franchisor {franchisor_id} already has summary embedding, skipping")
                        return True
                
                # Recompute the summary embedding
                summary_embedding = await self.recompute_franchisor_embedding(session, franchisor_id)
                
                if summary_embedding:
                    # Update the franchisor record using direct parameter substitution for vector
                    # This avoids SQLAlchemy parameter binding issues with PostgreSQL vector type
                    embedding_str = str(summary_embedding)
                    
                    await session.execute(
                        text(f"""
                            UPDATE franchisors 
                            SET embedding = '{embedding_str}'::vector,
                                embedding_model = :model,
                                brochure_processed_at = NOW(),
                                updated_at = NOW()
                            WHERE id = :franchisor_id
                        """),
                        {
                            "franchisor_id": franchisor_id,
                            "model": "text-embedding-3-small"
                        }
                    )
                    
                    await session.commit()
                    logger.info(f"Updated summary embedding for franchisor {franchisor_id}")
                    return True
                else:
                    logger.warning(f"Failed to generate summary embedding for franchisor {franchisor_id}")
                    return False
                    
            except Exception as e:
                logger.error(f"Failed to update summary embedding for franchisor {franchisor_id}: {e}")
                await session.rollback()
                return False

    async def batch_update_all_franchisors(self, force_recompute: bool = False) -> Dict[str, Any]:
        """
        Update summary embeddings for all active franchisors
        
        Args:
            force_recompute: Force recomputation for all franchisors
            
        Returns:
            Statistics about the update process
        """
        async with AsyncSessionLocal() as session:
            try:
                # Get all active franchisor IDs
                if force_recompute:
                    condition = "is_active = true AND is_deleted = false"
                else:
                    condition = "is_active = true AND is_deleted = false AND embedding IS NULL"
                
                result = await session.execute(
                    text(f"SELECT id, name FROM franchisors WHERE {condition}")
                )
                
                franchisors = result.fetchall()
                
                stats = {
                    "total_franchisors": len(franchisors),
                    "updated": 0,
                    "failed": 0,
                    "skipped": 0,
                    "errors": []
                }
                
                for franchisor_row in franchisors:
                    franchisor_id = str(franchisor_row.id)
                    franchisor_name = franchisor_row.name
                    
                    try:
                        success = await self.update_franchisor_summary_embedding(
                            franchisor_id, 
                            force_recompute
                        )
                        
                        if success:
                            stats["updated"] += 1
                            logger.info(f"Updated embedding for {franchisor_name} ({franchisor_id})")
                        else:
                            stats["failed"] += 1
                            stats["errors"].append(f"Failed to update {franchisor_name} ({franchisor_id})")
                            
                    except Exception as e:
                        stats["failed"] += 1
                        error_msg = f"Error updating {franchisor_name} ({franchisor_id}): {str(e)}"
                        stats["errors"].append(error_msg)
                        logger.error(error_msg)
                
                logger.info(f"Batch embedding update completed: {stats}")
                return stats
                
            except Exception as e:
                logger.error(f"Batch embedding update failed: {e}")
                return {
                    "total_franchisors": 0,
                    "updated": 0,
                    "failed": 0,
                    "skipped": 0,
                    "errors": [f"Batch process failed: {str(e)}"]
                }

    async def get_embedding_statistics(self) -> Dict[str, Any]:
        """Get statistics about franchisor embeddings"""
        async with AsyncSessionLocal() as session:
            try:
                # Count franchisors with/without summary embeddings
                summary_result = await session.execute(
                    text("""
                        SELECT 
                            COUNT(*) as total,
                            COUNT(CASE WHEN embedding IS NOT NULL THEN 1 END) as with_embedding,
                            COUNT(CASE WHEN embedding IS NULL THEN 1 END) as without_embedding
                        FROM franchisors 
                        WHERE is_active = true AND is_deleted = false
                    """)
                )
                summary_stats = summary_result.fetchone()
                
                # Count chunks per franchisor
                chunk_result = await session.execute(
                    text("""
                        SELECT 
                            COUNT(DISTINCT franchisor_id) as franchisors_with_chunks,
                            COUNT(*) as total_chunks,
                            COUNT(CASE WHEN franchisor_id IS NULL THEN 1 END) as global_chunks
                        FROM franchisor_chunks
                    """)
                )
                chunk_stats = chunk_result.fetchone()
                
                return {
                    "total_franchisors": summary_stats.total,
                    "with_summary_embedding": summary_stats.with_embedding,
                    "without_summary_embedding": summary_stats.without_embedding,
                    "franchisors_with_chunks": chunk_stats.franchisors_with_chunks or 0,
                    "total_chunks": chunk_stats.total_chunks or 0,
                    "global_chunks": chunk_stats.global_chunks or 0,
                    "embedding_coverage": (summary_stats.with_embedding / summary_stats.total * 100) if summary_stats.total > 0 else 0
                }
                
            except Exception as e:
                logger.error(f"Failed to get embedding statistics: {e}")
                return {
                    "error": str(e),
                    "total_franchisors": 0,
                    "with_summary_embedding": 0,
                    "without_summary_embedding": 0,
                    "franchisors_with_chunks": 0,
                    "total_chunks": 0,
                    "global_chunks": 0,
                    "embedding_coverage": 0
                }


# Global instance
franchisor_embedding_service = FranchisorEmbeddingService()
