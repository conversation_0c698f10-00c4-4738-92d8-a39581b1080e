"""
Franchisor-scoped RAG retriever with global fallback
"""

from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import AsyncSessionLocal
from app.core.logging import logger
# LeadService import removed - using direct database queries instead

# Import embedding service
from docqa.vector_store.production_embeddings import ProductionEmbeddingService

# Configuration
TOP_K = 8
K_A_DEFAULT = 6  # Franchisor-specific chunks
MIN_SCORE_THRESHOLD = 0.3  # Minimum similarity score


class FranchisorRAGRetriever:
    """
    Franchisor-scoped RAG retriever with strict tenant isolation
    """
    
    def __init__(self):
        self.embedding_service = ProductionEmbeddingService()

    async def embed(self, text: str) -> List[float]:
        """Generate embedding for query text"""
        try:
            return self.embedding_service.generate_embedding(text)
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    async def get_franchisor_id_from_lead(self, session: AsyncSession, lead_id: str) -> str:
        """Get franchisor_id from lead data (using brand_preference column)"""
        try:
            result = await session.execute(
                text("SELECT brand_preference FROM leads WHERE id = :lid"),
                {"lid": lead_id}
            )
            fid = result.scalar()
            if not fid:
                raise ValueError(f"Lead {lead_id} not found or has no brand_preference (franchisor_id)")
            return str(fid)
        except Exception as e:
            logger.error(f"Failed to get franchisor_id for lead {lead_id}: {e}")
            raise

    async def fetch_franchisor_chunks(
        self, 
        session: AsyncSession, 
        franchisor_id: str, 
        query_vector: List[float], 
        limit: int,
        min_score: float = MIN_SCORE_THRESHOLD
    ) -> List[Dict[str, Any]]:
        """
        Fetch franchisor-specific chunks using vector similarity
        """
        if limit <= 0:
            return []
        
        try:
            # Convert query vector to string format for PostgreSQL and use direct interpolation
            query_vector_str = str(query_vector)
            
            result = await session.execute(
                text(f"""
                    SELECT 
                        id AS chunk_id,
                        text as content,
                        '' as source_title,
                        '' as source_url,
                        NULL as page_no,
                        '' as section,
                        metadata,
                        1.0 - (embedding <#> '{query_vector_str}'::vector) AS score,
                        'A' AS tier
                    FROM franchisor_chunks
                    WHERE franchisor_id = :fid
                    AND (1.0 - (embedding <#> '{query_vector_str}'::vector)) >= :min_score
                    ORDER BY embedding <#> '{query_vector_str}'::vector
                    LIMIT :lim
                """),
                {
                    "fid": franchisor_id,
                    "lim": limit,
                    "min_score": min_score
                }
            )
            
            chunks = [dict(row._mapping) for row in result]
            logger.info(f"Retrieved {len(chunks)} franchisor-specific chunks for {franchisor_id}")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to fetch franchisor chunks: {e}")
            return []

    async def fetch_global_chunks(
        self, 
        session: AsyncSession, 
        query_vector: List[float], 
        limit: int,
        min_score: float = MIN_SCORE_THRESHOLD
    ) -> List[Dict[str, Any]]:
        """
        Fetch global chunks (franchisor_id IS NULL) as fallback
        """
        if limit <= 0:
            return []
        
        try:
            # Convert query vector to string format for PostgreSQL and use direct interpolation
            query_vector_str = str(query_vector)
            
            result = await session.execute(
                text(f"""
                    SELECT 
                        id AS chunk_id,
                        text as content,
                        '' as source_title,
                        '' as source_url,
                        NULL as page_no,
                        '' as section,
                        metadata,
                        1.0 - (embedding <#> '{query_vector_str}'::vector) AS score,
                        'B' AS tier
                    FROM franchisor_chunks
                    WHERE franchisor_id IS NULL
                    AND (1.0 - (embedding <#> '{query_vector_str}'::vector)) >= :min_score
                    ORDER BY embedding <#> '{query_vector_str}'::vector
                    LIMIT :lim
                """),
                {
                    "lim": limit,
                    "min_score": min_score
                }
            )
            
            chunks = [dict(row._mapping) for row in result]
            logger.info(f"Retrieved {len(chunks)} global fallback chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to fetch global chunks: {e}")
            return []

    async def retrieve_for_lead(
        self,
        lead_id: str,
        query_text: str,
        top_k: int = TOP_K,
        k_a: int = K_A_DEFAULT,
        min_score: float = MIN_SCORE_THRESHOLD
    ) -> Tuple[List[Dict[str, Any]], str]:
        """
        Retrieve context chunks for a lead's query with franchisor scoping
        
        Args:
            lead_id: Lead identifier
            query_text: User's question
            top_k: Total number of chunks to return
            k_a: Maximum franchisor-specific chunks to fetch
            min_score: Minimum similarity score threshold
            
        Returns:
            Tuple of (chunks, franchisor_id)
            
        Raises:
            ValueError: If lead not found or missing franchisor_id
        """
        async with AsyncSessionLocal() as session:
            try:
                # Get franchisor_id for lead (fail closed if not found)
                franchisor_id = await self.get_franchisor_id_from_lead(session, lead_id)
                
                # Generate query embedding
                query_vector = await self.embed(query_text)
                
                # Fetch franchisor-specific chunks (Tier A)
                tier_a_chunks = await self.fetch_franchisor_chunks(
                    session, 
                    franchisor_id, 
                    query_vector, 
                    min(k_a, top_k),
                    min_score
                )
                
                # Calculate remaining slots for global fallback
                remaining_slots = max(0, top_k - len(tier_a_chunks))
                
                # Fetch global chunks if needed (Tier B)
                tier_b_chunks = []
                if remaining_slots > 0:
                    tier_b_chunks = await self.fetch_global_chunks(
                        session, 
                        query_vector, 
                        remaining_slots,
                        min_score
                    )
                
                # Combine and sort by score
                all_chunks = tier_a_chunks + tier_b_chunks
                all_chunks.sort(key=lambda x: x["score"], reverse=True)
                
                # Limit to top_k
                final_chunks = all_chunks[:top_k]
                
                # Log retrieval metrics
                logger.info(
                    "RAG retrieval completed",
                    extra={
                        "event": "rag.retrieve",
                        "lead_id": lead_id,
                        "franchisor_id": franchisor_id,
                        "query_length": len(query_text),
                        "top_k": top_k,
                        "a_count": len(tier_a_chunks),
                        "b_count": len(tier_b_chunks),
                        "final_count": len(final_chunks),
                        "used_fallback": len(tier_b_chunks) > 0
                    }
                )
                
                return final_chunks, franchisor_id
                
            except Exception as e:
                logger.error(f"RAG retrieval failed for lead {lead_id}: {e}")
                raise

    async def check_chunk_isolation(
        self, 
        lead_id: str, 
        chunk_ids: List[str]
    ) -> bool:
        """
        Verify that all chunks belong to the lead's franchisor (for testing)
        """
        async with AsyncSessionLocal() as session:
            try:
                franchisor_id = await self.get_franchisor_id_from_lead(session, lead_id)
                
                for chunk_id in chunk_ids:
                    result = await session.execute(
                        text("""
                            SELECT 1
                            FROM franchisor_chunks
                            WHERE id = :cid 
                            AND (franchisor_id = :fid OR franchisor_id IS NULL)
                        """),
                        {"cid": chunk_id, "fid": franchisor_id}
                    )
                    
                    if not result.scalar():
                        logger.warning(f"Chunk {chunk_id} violates isolation for franchisor {franchisor_id}")
                        return False
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to check chunk isolation: {e}")
                return False

    async def get_retrieval_stats(self, franchisor_id: str) -> Dict[str, Any]:
        """Get statistics about available chunks for a franchisor"""
        async with AsyncSessionLocal() as session:
            try:
                # Count franchisor-specific chunks
                franchisor_result = await session.execute(
                    text("SELECT COUNT(*) FROM franchisor_chunks WHERE franchisor_id = :fid"),
                    {"fid": franchisor_id}
                )
                franchisor_count = franchisor_result.scalar() or 0
                
                # Count global chunks
                global_result = await session.execute(
                    text("SELECT COUNT(*) FROM franchisor_chunks WHERE franchisor_id IS NULL")
                )
                global_count = global_result.scalar() or 0
                
                return {
                    "franchisor_id": franchisor_id,
                    "franchisor_chunks": franchisor_count,
                    "global_chunks": global_count,
                    "total_available": franchisor_count + global_count
                }
                
            except Exception as e:
                logger.error(f"Failed to get retrieval stats: {e}")
                return {
                    "franchisor_id": franchisor_id,
                    "franchisor_chunks": 0,
                    "global_chunks": 0,
                    "total_available": 0,
                    "error": str(e)
                }


# Global instance
retriever = FranchisorRAGRetriever()
