"""
Andy AI RAG Service using franchisor-scoped retrieval
"""

from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import text
from app.core.logging import logger
from app.services.rag.retriever import retriever
# LeadService import removed - using direct database queries instead

# Import OpenAI for answer synthesis
try:
    from openai import AsyncOpenAI
    from app.core.config.settings import settings
    openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
except ImportError:
    logger.warning("OpenAI not available for answer synthesis")
    openai_client = None


class AndyRAGService:
    """
    RAG service specifically designed for Andy AI with franchisor scoping
    """
    
    def __init__(self):
        self.retriever = retriever

    async def answer_lead_question(
        self,
        lead_id: str,
        user_query: str,
        top_k: int = 8,
        k_a: int = 6,
        min_score: float = 0.3,
        temperature: float = 0.2
    ) -> Dict[str, Any]:
        """
        Answer a lead's question using franchisor-scoped RAG
        
        Args:
            lead_id: Lead identifier
            user_query: User's question
            top_k: Total chunks to retrieve
            k_a: Max franchisor-specific chunks
            min_score: Minimum similarity score
            temperature: OpenAI temperature for response generation
            
        Returns:
            Dict with answer, contexts, and metadata
        """
        try:
            # Retrieve relevant contexts with franchisor scoping
            contexts, franchisor_id = await self.retriever.retrieve_for_lead(
                lead_id=lead_id,
                query_text=user_query,
                top_k=top_k,
                k_a=k_a,
                min_score=min_score
            )
            
            # Get franchisor name for context
            franchisor_name = await self._get_franchisor_name(franchisor_id)
            
            if not contexts:
                return {
                    "success": False,
                    "answer": "I don't have enough information to answer that question about our franchise opportunity. Could you ask something else, or I'd be happy to connect you with someone who can provide more details?",
                    "contexts": [],
                    "franchisor_id": franchisor_id,
                    "franchisor_name": franchisor_name,
                    "used_fallback": False,
                    "retrieval_stats": {"total_chunks": 0, "franchisor_chunks": 0, "global_chunks": 0}
                }
            
            # Count chunk types
            franchisor_chunks = len([c for c in contexts if c["tier"] == "A"])
            global_chunks = len([c for c in contexts if c["tier"] == "B"])
            
            # Synthesize answer using OpenAI
            answer, citations = await self._synthesize_answer(user_query, contexts, temperature, franchisor_id)
            
            # Determine if fallback was used
            used_fallback = any(c["tier"] == "B" for c in contexts)
            
            logger.info(f"RAG answer generated for lead {lead_id}: {len(contexts)} contexts, fallback: {used_fallback}")
            
            return {
                "success": True,
                "answer": answer,
                "contexts": citations or contexts,
                "franchisor_id": franchisor_id,
                "franchisor_name": franchisor_name,
                "used_fallback": used_fallback,
                "retrieval_stats": {
                    "total_chunks": len(contexts),
                    "franchisor_chunks": franchisor_chunks,
                    "global_chunks": global_chunks,
                    "min_score": min_score,
                    "query_length": len(user_query)
                }
            }
            
        except ValueError as e:
            # Lead not found or missing franchisor_id
            logger.error(f"RAG failed for lead {lead_id}: {e}")
            return {
                "success": False,
                "answer": "I'm sorry, I couldn't process your question right now. Please try again or contact support.",
                "contexts": [],
                "franchisor_id": None,
                "used_fallback": False,
                "error": str(e)
            }
            
        except Exception as e:
            logger.error(f"RAG processing failed for lead {lead_id}: {e}")
            return {
                "success": False,
                "answer": "I'm experiencing technical difficulties. Please try again in a moment.",
                "contexts": [],
                "franchisor_id": None,
                "used_fallback": False,
                "error": str(e)
            }

    async def _synthesize_answer(
        self,
        query: str,
        contexts: List[Dict[str, Any]],
        temperature: float = 0.2,
        franchisor_id: Optional[str] = None
    ) -> Tuple[str, Optional[List[Dict[str, Any]]]]:
        """
        Synthesize an answer from retrieved contexts using OpenAI
        
        Args:
            query: User's question
            contexts: Retrieved context chunks
            temperature: OpenAI temperature
            
        Returns:
            Tuple of (answer, citations)
        """
        if not openai_client:
            # Fallback to simple context concatenation
            context_text = "\n\n".join([c["content"][:500] for c in contexts[:3]])
            return f"Based on the available information: {context_text}", None
        
        try:
            # Prepare context text
            context_parts = []
            for i, ctx in enumerate(contexts[:5]):  # Limit to top 5 contexts
                source_info = ""
                if ctx.get("source_title"):
                    source_info = f" (Source: {ctx['source_title']}"
                    if ctx.get("page_no"):
                        source_info += f", Page {ctx['page_no']}"
                    source_info += ")"
                
                context_parts.append(f"Context {i+1}{source_info}:\n{ctx['content']}")
            
            context_text = "\n\n".join(context_parts)
            
            # Create system prompt for Andy with franchisor-scoped context
            system_prompt = f"""You are Andy, the Lead Qualification Specialist AI representing the franchisor associated with this lead. 

CRITICAL BEHAVIOR RULES:
- Always work in the context of the specific franchisor for this lead
- Use "we" and "our" language when referring to services, prices, processes, and franchise opportunities
- Answer as if you represent that franchisor directly - you ARE their representative
- Keep answers clear, simple, and conversational, like explaining to a friend
- NEVER mix content from different franchisors or leak information from other franchisors
- Use only the knowledge base provided (franchisor-specific + optional global content)

RESPONSE GUIDELINES:
- Answer questions directly using ONLY the provided context information
- Be warm, conversational, and encouraging while remaining professional
- If context doesn't contain enough information, acknowledge politely and offer to help with related topics
- Focus on franchise fees, investment requirements, support, training, and business model details
- Maintain a helpful, optimistic tone that encourages further questions
- Keep responses very concise for SMS (maximum 1-2 sentences, under 160 characters total)
- Use Australian colloquial expressions naturally: "so", "yeah", "okay", contractions like "you're"
- Don't repeat the franchisor name - use "we", "our", or "the business" instead

LANGUAGE EXAMPLES:
✅ CORRECT: "Our franchise fee is $10K + GST. You'll need $50K liquid assets for setup."
✅ CORRECT: "We provide 4 weeks training and ongoing support."
✅ CORRECT: "Deposit required on successful application."
❌ WRONG: "This franchisor offers..." or "Another franchise has..."

Remember: You represent THIS specific franchisor. Speak as their direct representative using "we/our" language."""

            # Create user prompt with franchisor context
            # Note: franchisor_name is not available in this scope, but franchisor_id provides the context
            franchisor_context = f"\nFranchisor Context: You are representing franchisor ID: {franchisor_id}\n"
            
            user_prompt = f"""Context Information:
{context_text}{franchisor_context}

Question: {query}

Please provide a very concise SMS-friendly answer (under 160 characters) as a representative of this specific franchisor, using "we" and "our" language. Focus on key facts only. Base your response only on the context above."""

            # Generate response
            response = await openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=temperature,
                max_tokens=500
            )
            
            answer = response.choices[0].message.content.strip()
            
            # Prepare citations
            citations = []
            for ctx in contexts:
                citation = {
                    "chunk_id": ctx["chunk_id"],
                    "content_preview": ctx["content"][:100] + "...",
                    "source_title": ctx.get("source_title"),
                    "source_url": ctx.get("source_url"),
                    "page_no": ctx.get("page_no"),
                    "score": ctx["score"],
                    "tier": ctx["tier"]
                }
                citations.append(citation)
            
            return answer, citations
            
        except Exception as e:
            logger.error(f"Answer synthesis failed: {e}")
            # Fallback to simple context summary
            fallback_answer = "Based on the franchise information available: " + contexts[0]["content"][:200] + "..."
            return fallback_answer, None

    async def check_franchisor_availability(self, lead_id: str) -> Dict[str, Any]:
        """
        Check if franchisor has content available for RAG
        
        Args:
            lead_id: Lead identifier
            
        Returns:
            Availability status and statistics
        """
        try:
            # Get franchisor_id from lead
            from app.core.database.connection import AsyncSessionLocal
            async with AsyncSessionLocal() as session:
                franchisor_id = await self.retriever.get_franchisor_id_from_lead(session, lead_id)
            
            # Get retrieval statistics
            stats = await self.retriever.get_retrieval_stats(franchisor_id)
            
            has_content = stats["franchisor_chunks"] > 0 or stats["global_chunks"] > 0
            
            return {
                "lead_id": lead_id,
                "franchisor_id": franchisor_id,
                "has_content": has_content,
                "content_available": has_content,
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"Failed to check franchisor availability for lead {lead_id}: {e}")
            return {
                "lead_id": lead_id,
                "franchisor_id": None,
                "has_content": False,
                "content_available": False,
                "error": str(e)
            }

    async def get_franchisor_topics(self, lead_id: str, limit: int = 10) -> List[str]:
        """
        Get common topics/sections available for a franchisor
        
        Args:
            lead_id: Lead identifier
            limit: Maximum topics to return
            
        Returns:
            List of available topics/sections
        """
        try:
            async with self.retriever.AsyncSessionLocal() as session:
                franchisor_id = await self.retriever.get_franchisor_id_from_lead(session, lead_id)
                
                # Get unique sections/topics from chunks
                result = await session.execute(
                    text("""
                        SELECT DISTINCT section
                        FROM franchisor_chunks
                        WHERE franchisor_id = :fid
                        AND section IS NOT NULL
                        ORDER BY section
                        LIMIT :limit
                    """),
                    {"fid": franchisor_id, "limit": limit}
                )
                
                topics = [row[0] for row in result.fetchall() if row[0]]
                return topics
                
        except Exception as e:
            logger.error(f"Failed to get topics for lead {lead_id}: {e}")
            return []

    def get_suggested_questions(self, franchisor_id: Optional[str] = None) -> List[str]:
        """
        Get suggested questions for franchise inquiries
        
        Args:
            franchisor_id: Optional franchisor ID for specific suggestions
            
        Returns:
            List of suggested questions
        """
        # Generic franchise questions
        questions = [
            "What is the total investment required?",
            "What franchise fees do I need to pay?",
            "What kind of training and support is provided?",
            "What are the ongoing royalty fees?",
            "How long does it take to break even?",
            "What territory rights do I get?",
            "What are the financing options available?",
            "What is the application process like?",
            "How much working capital do I need?",
            "What marketing support is included?"
        ]
        
        return questions

    async def _get_franchisor_name(self, franchisor_id: str) -> Optional[str]:
        """
        Get franchisor name for context
        
        Args:
            franchisor_id: Franchisor UUID
            
        Returns:
            Franchisor name or None
        """
        try:
            from app.core.database.connection import AsyncSessionLocal
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    text("SELECT name FROM franchisors WHERE id = :fid"),
                    {"fid": franchisor_id}
                )
                name = result.scalar()
                return name
        except Exception as e:
            logger.error(f"Failed to get franchisor name for {franchisor_id}: {e}")
            return None


# Global instance
andy_rag_service = AndyRAGService()
