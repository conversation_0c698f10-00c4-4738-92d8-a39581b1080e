"""
Coochie Meeting Integration Service
Integrates the Coochie Hydrogreen workflow with the existing meeting booking system.
Handles timezone conversions, scheduling options, and booking confirmations.
"""

import os
from datetime import datetime, timedelta, time
from typing import Dict, Any, Optional, List, Tuple
import structlog
import pytz
from app.services.zoho_bookings_service import ZohoBookingsService, BookingSlot
from app.meeting_agent.agent import MeetingAgent
from app.meeting_agent.timezone_utils import TimezoneHandler
from app.core.database.connection import get_db

from app.core.logging import logger


class CoochieMeetingService:
    """
    Service for integrating Coochie workflow with existing meeting booking system
    """
    
    def __init__(self):
        self.zoho_service = ZohoBookingsService()
        self.meeting_agent = MeetingAgent()
        self.timezone_handler = TimezoneHandler()
        self.default_timezone = os.getenv("DEFAULT_TIMEZONE", "Asia/Kolkata")
        self.meeting_agent_enabled = os.getenv("MEETING_AGENT_ENABLED", "false").lower() == "true"
        
        logger.info(
            "Coochie meeting service initialized",
            meeting_agent_enabled=self.meeting_agent_enabled,
            default_timezone=self.default_timezone
        )
    
    async def generate_scheduling_options(
        self, 
        lead_id: str,
        phone_number: Optional[str] = None,
        preferred_timezone: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate multiple scheduling options for the lead
        
        Args:
            lead_id: Lead ID
            phone_number: Lead phone number
            preferred_timezone: Preferred timezone (defaults to Asia/Kolkata)
            
        Returns:
            Dict with scheduling options
        """
        try:
            timezone = preferred_timezone or self.default_timezone
            
            if not self.meeting_agent_enabled:
                # Return mock options when meeting agent is disabled
                return self._generate_mock_scheduling_options(timezone)
            
            # Get available slots for the next few days
            available_slots = await self._get_next_available_slots(
                days_ahead=7,
                max_slots=4,
                timezone=timezone
            )
            
            if not available_slots:
                logger.warning(f"No available slots found for lead {lead_id}")
                return self._generate_fallback_scheduling_options(timezone)
            
            # Format slots for Coochie workflow
            options = self._format_scheduling_options(available_slots, timezone)
            
            logger.info(
                f"Generated {len(options['slots'])} scheduling options for lead {lead_id}",
                timezone=timezone,
                first_option=options['slots'][0] if options['slots'] else None
            )
            
            return options
            
        except Exception as e:
            logger.error(f"Error generating scheduling options for lead {lead_id}: {str(e)}")
            return self._generate_fallback_scheduling_options(timezone or self.default_timezone)
    
    async def _get_next_available_slots(
        self,
        days_ahead: int = 7,
        max_slots: int = 4,
        timezone: str = "Asia/Kolkata"
    ) -> List[BookingSlot]:
        """Get next available slots from Zoho"""
        try:
            # Try to get slots using the Zoho service
            slots = await self.zoho_service.get_next_available_slots(
                max_days_ahead=days_ahead,
                max_slots=max_slots,
                service_type="lead_meeting"
            )
            
            return slots or []
            
        except Exception as e:
            logger.error(f"Error getting available slots: {str(e)}")
            return []
    
    def _format_scheduling_options(
        self, 
        slots: List[BookingSlot], 
        timezone: str
    ) -> Dict[str, Any]:
        """Format slots for Coochie workflow"""
        try:
            # Convert timezone
            tz = pytz.timezone(timezone)
            
            formatted_slots = []
            for i, slot in enumerate(slots[:4]):  # Limit to 4 options
                # Convert to local timezone
                local_start = slot.start_time.astimezone(tz)
                
                formatted_slot = {
                    "option_number": i + 1,
                    "day": local_start.strftime("%A"),
                    "date": local_start.strftime("%B %d"),
                    "time": local_start.strftime("%I:%M %p"),
                    "time_24h": local_start.strftime("%H:%M"),
                    "datetime_local": local_start.isoformat(),
                    "datetime_utc": slot.start_time.isoformat(),
                    "staff_name": slot.staff_name,
                    "staff_id": slot.staff_id,
                    "service_id": slot.service_id,
                    "duration_minutes": slot.duration_minutes,
                    "display_text": f"{local_start.strftime('%I:%M %p')} on {local_start.strftime('%A')}"
                }
                formatted_slots.append(formatted_slot)
            
            # Extract first two options for workflow compatibility
            option1 = formatted_slots[0] if len(formatted_slots) > 0 else None
            option2 = formatted_slots[1] if len(formatted_slots) > 1 else None
            
            return {
                "success": True,
                "timezone": timezone,
                "slots": formatted_slots,
                "time1": option1["time"] if option1 else "2:00 PM",
                "day1": option1["day"] if option1 else "Tomorrow",
                "time2": option2["time"] if option2 else "10:00 AM",
                "day2": option2["day"] if option2 else "Day after",
                "total_options": len(formatted_slots)
            }
            
        except Exception as e:
            logger.error(f"Error formatting scheduling options: {str(e)}")
            return self._generate_fallback_scheduling_options(timezone)
    
    def _generate_mock_scheduling_options(self, timezone: str) -> Dict[str, Any]:
        """Generate mock scheduling options when meeting agent is disabled"""
        try:
            tz = pytz.timezone(timezone)
            now = datetime.now(tz)
            
            # Generate mock slots for next few days
            mock_slots = []
            for i in range(4):
                days_ahead = i + 1
                mock_time = now.replace(hour=14 if i % 2 == 0 else 10, minute=0, second=0, microsecond=0)
                mock_time += timedelta(days=days_ahead)
                
                mock_slot = {
                    "option_number": i + 1,
                    "day": mock_time.strftime("%A"),
                    "date": mock_time.strftime("%B %d"),
                    "time": mock_time.strftime("%I:%M %p"),
                    "time_24h": mock_time.strftime("%H:%M"),
                    "datetime_local": mock_time.isoformat(),
                    "datetime_utc": mock_time.astimezone(pytz.UTC).isoformat(),
                    "staff_name": "Andy (Mock)",
                    "staff_id": "mock_staff_001",
                    "service_id": "mock_service_001",
                    "duration_minutes": 30,
                    "display_text": f"{mock_time.strftime('%I:%M %p')} on {mock_time.strftime('%A')}",
                    "mock": True
                }
                mock_slots.append(mock_slot)
            
            return {
                "success": True,
                "timezone": timezone,
                "slots": mock_slots,
                "time1": mock_slots[0]["time"],
                "day1": mock_slots[0]["day"],
                "time2": mock_slots[1]["time"],
                "day2": mock_slots[1]["day"],
                "total_options": len(mock_slots),
                "mock": True
            }
            
        except Exception as e:
            logger.error(f"Error generating mock scheduling options: {str(e)}")
            return self._generate_fallback_scheduling_options(timezone)
    
    def _generate_fallback_scheduling_options(self, timezone: str) -> Dict[str, Any]:
        """Generate fallback scheduling options"""
        return {
            "success": False,
            "timezone": timezone,
            "slots": [],
            "time1": "2:00 PM",
            "day1": "Tomorrow",
            "time2": "10:00 AM", 
            "day2": "Day after",
            "total_options": 0,
            "error": "Unable to fetch available slots",
            "fallback": True
        }
    
    async def book_meeting(
        self,
        lead_id: str,
        selected_option: Dict[str, Any],
        phone_number: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Book a meeting using the selected option
        
        Args:
            lead_id: Lead ID
            selected_option: Selected scheduling option
            phone_number: Lead phone number
            
        Returns:
            Dict with booking results
        """
        try:
            if not self.meeting_agent_enabled:
                return self._create_mock_booking_result(selected_option)
            
            # Check if this is a mock option
            if selected_option.get("mock", False):
                return self._create_mock_booking_result(selected_option)
            
            # Create BookingSlot object for real booking
            slot_datetime = datetime.fromisoformat(selected_option["datetime_utc"].replace('Z', '+00:00'))
            
            booking_slot = BookingSlot(
                staff_id=selected_option["staff_id"],
                staff_name=selected_option["staff_name"],
                start_time=slot_datetime,
                end_time=slot_datetime + timedelta(minutes=selected_option["duration_minutes"]),
                service_id=selected_option["service_id"],
                service_name="Lead Meeting",
                duration_minutes=selected_option["duration_minutes"],
                booking_url=""
            )
            
            # Book the appointment
            booking_result = await self.zoho_service.book_appointment_for_lead(
                lead_id=lead_id,
                slot=booking_slot
            )
            
            if booking_result.success:
                logger.info(
                    f"Successfully booked meeting for lead {lead_id}",
                    booking_id=booking_result.booking_id,
                    meeting_time=selected_option["datetime_local"]
                )
                
                return {
                    "success": True,
                    "booking_id": booking_result.booking_id,
                    "booking_url": booking_result.booking_url,
                    "meeting_link": booking_result.meeting_link,
                    "meeting_time": selected_option["datetime_local"],
                    "display_time": selected_option["display_text"],
                    "staff_name": selected_option["staff_name"],
                    "confirmation_sent": True
                }
            else:
                logger.error(
                    f"Failed to book meeting for lead {lead_id}",
                    error=booking_result.error_message
                )
                
                return {
                    "success": False,
                    "error": booking_result.error_message,
                    "retry_available": True
                }
                
        except Exception as e:
            logger.error(f"Error booking meeting for lead {lead_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "retry_available": True
            }
    
    def _create_mock_booking_result(self, selected_option: Dict[str, Any]) -> Dict[str, Any]:
        """Create mock booking result for testing"""
        import uuid
        
        mock_booking_id = f"mock_booking_{uuid.uuid4().hex[:8]}"
        
        return {
            "success": True,
            "booking_id": mock_booking_id,
            "booking_url": f"https://mock-booking.com/{mock_booking_id}",
            "meeting_link": f"https://mock-meeting.com/{mock_booking_id}",
            "meeting_time": selected_option["datetime_local"],
            "display_time": selected_option["display_text"],
            "staff_name": selected_option["staff_name"],
            "confirmation_sent": False,
            "mock": True
        }
    
    async def parse_time_selection(self, message: str, available_options: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Parse user's time selection from message
        
        Args:
            message: User message with time selection
            available_options: List of available scheduling options
            
        Returns:
            Selected option or None
        """
        try:
            message_lower = message.lower()
            
            # Check for option numbers (1, 2, 3, 4)
            for option in available_options:
                option_num = option["option_number"]
                if (f"option {option_num}" in message_lower or 
                    f"{option_num}" in message_lower or
                    str(option_num) in message_lower):
                    return option
            
            # Check for day names
            for option in available_options:
                day = option["day"].lower()
                if day in message_lower:
                    return option
            
            # Check for time patterns
            for option in available_options:
                time_str = option["time"].lower()
                if time_str in message_lower:
                    return option
            
            # Default to first option if unclear
            if available_options:
                logger.info(f"Unclear time selection '{message}', defaulting to first option")
                return available_options[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing time selection: {str(e)}")
            return available_options[0] if available_options else None
    
    def format_scheduling_text(self, options: Dict[str, Any]) -> str:
        """
        Format scheduling options for display in conversation
        
        Args:
            options: Scheduling options from generate_scheduling_options
            
        Returns:
            Formatted text for conversation
        """
        try:
            if not options.get("success", False) or not options.get("slots"):
                return f"Are you available at 2:00 PM tomorrow? Or 10:00 AM the day after?"
            
            slots = options["slots"]
            
            if len(slots) >= 2:
                option1 = slots[0]
                option2 = slots[1]
                return f"Are you available at {option1['time']} on {option1['day']}? Or {option2['time']} on {option2['day']}?"
            elif len(slots) == 1:
                option1 = slots[0]
                return f"Are you available at {option1['time']} on {option1['day']}?"
            else:
                return "Are you available for a call this week? I can check specific times for you."
                
        except Exception as e:
            logger.error(f"Error formatting scheduling text: {str(e)}")
            return "Are you available at 2:00 PM tomorrow? Or 10:00 AM the day after?"


# Global service instance
coochie_meeting_service = CoochieMeetingService()


# Export main components
__all__ = [
    "CoochieMeetingService",
    "coochie_meeting_service"
]
