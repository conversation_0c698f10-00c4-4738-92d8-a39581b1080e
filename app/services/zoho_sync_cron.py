"""
Zoho CRM Sync Cron Job Service
Handles scheduled synchronization between CMS and Zoho CRM
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional

from app.services.simple_zoho_sync import SimpleZohoSync
from app.db.session import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

class ZohoSyncCronService:
    """Service for handling scheduled Zoho CRM synchronization"""
    
    def __init__(self):
        self.settings = settings
        self.sync_service = None
        
    async def initialize(self):
        """Initialize the sync service with database session"""
        try:
            # Get database session
            async for session in get_db():
                self.sync_service = SimpleZohoSync(session)
                break
            logger.info("Zoho sync cron service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Zoho sync cron service: {e}")
            raise
    
    async def run_sync(self) -> dict:
        """
        Run the complete Zoho CRM synchronization process
        
        Returns:
            dict: Sync results summary
        """
        if not self.sync_service:
            await self.initialize()
        
        sync_start_time = datetime.now()
        results = {
            "sync_started_at": sync_start_time.isoformat(),
            "leads": {"pulled": 0, "pushed": 0, "errors": 0},
            "franchisors": {"pulled": 0, "pushed": 0, "errors": 0},
            "total_errors": 0,
            "status": "success"
        }
        
        try:
            logger.info("Starting scheduled Zoho CRM sync...")
            
            # 1. Sync Leads (bidirectional)
            try:
                logger.info("Syncing leads...")
                lead_results = await self.sync_service.sync_leads()
                results["leads"]["pulled"] = lead_results.get("pulled_count", 0)
                results["leads"]["pushed"] = lead_results.get("pushed_count", 0)
                results["leads"]["errors"] = lead_results.get("error_count", 0)
                logger.info(f"Lead sync completed: {lead_results}")
            except Exception as e:
                logger.error(f"Error syncing leads: {e}")
                results["leads"]["errors"] += 1
                results["total_errors"] += 1
            
            # 2. Sync Franchisors (pull only for Sale Won - 100%)
            try:
                logger.info("Syncing franchisors...")
                franchisor_results = await self.sync_service.sync_franchisors()
                results["franchisors"]["pulled"] = franchisor_results.get("pulled_count", 0)
                results["franchisors"]["pushed"] = 0  # Pull only
                results["franchisors"]["errors"] = franchisor_results.get("error_count", 0)
                logger.info(f"Franchisor sync completed: {franchisor_results}")
            except Exception as e:
                logger.error(f"Error syncing franchisors: {e}")
                results["franchisors"]["errors"] += 1
                results["total_errors"] += 1
            
            # Update final status
            if results["total_errors"] > 0:
                results["status"] = "completed_with_errors"
            
            sync_end_time = datetime.now()
            sync_duration = (sync_end_time - sync_start_time).total_seconds()
            results["sync_completed_at"] = sync_end_time.isoformat()
            results["duration_seconds"] = sync_duration
            
            logger.info(f"Zoho CRM sync completed in {sync_duration:.2f} seconds")
            logger.info(f"Sync results: {results}")
            
            return results
            
        except Exception as e:
            logger.error(f"Critical error during Zoho sync: {e}", exc_info=True)
            results["status"] = "failed"
            results["error"] = str(e)
            results["total_errors"] += 1
            return results
    
    async def health_check(self) -> dict:
        """
        Check if Zoho sync service is healthy
        
        Returns:
            dict: Health status
        """
        try:
            if not self.sync_service:
                await self.initialize()
            
            # Test Zoho connection
            zoho_status = await self.sync_service.test_zoho_connection()
            
            return {
                "status": "healthy" if zoho_status else "unhealthy",
                "zoho_connection": zoho_status,
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }

# Global instance for cron job
zoho_sync_cron = ZohoSyncCronService()

async def run_zoho_sync_job():
    """
    Main function to be called by cron job
    This function will be executed every 15 minutes
    """
    try:
        logger.info("=== Starting scheduled Zoho CRM sync job ===")
        results = await zoho_sync_cron.run_sync()
        
        # Log summary
        total_pulled = results["leads"]["pulled"] + results["franchisors"]["pulled"]
        total_pushed = results["leads"]["pushed"] + results["franchisors"]["pushed"]
        total_errors = results["total_errors"]
        
        logger.info(f"=== Sync job completed ===")
        logger.info(f"Total records pulled: {total_pulled}")
        logger.info(f"Total records pushed: {total_pushed}")
        logger.info(f"Total errors: {total_errors}")
        logger.info(f"Status: {results['status']}")
        
        return results
        
    except Exception as e:
        logger.error(f"Cron job execution failed: {e}", exc_info=True)
        return {
            "status": "failed",
            "error": str(e),
            "sync_started_at": datetime.now().isoformat()
        }

if __name__ == "__main__":
    # For testing the cron job manually
    asyncio.run(run_zoho_sync_job())
