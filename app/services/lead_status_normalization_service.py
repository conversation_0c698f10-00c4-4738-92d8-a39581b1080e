"""
Lead Status Normalization Service
Handles synonym mapping, fuzzy string matching, and canonical name resolution.
"""

import re
from typing import Dict, <PERSON><PERSON>, Tu<PERSON>
from difflib import SequenceMatcher
import structlog

from app.core.logging import logger


class LeadStatusNormalizationService:
    """Service for normalizing lead status names to canonical forms"""
    
    def __init__(self):
        """Initialize the normalization service with synonym mappings"""
        self.synonym_mapping = {
            # Case-insensitive synonym mappings to canonical names
            # Note: Keep exact matches for underscore/lowercase versions as they are separate statuses
            "new lead": "New Lead",
            "contacted": "Contacted",
            "qualified": "Qualified",
            "unqualified": "Unqualified",
            "not interested": "Not Interested",
            "call back": "Call Back",
            # Note: "wrong number" maps to "Wrong Number", but "wrong_number" stays as is
            # Removed "converted" and "lost" mappings as these statuses were deleted
            "eoi sent": "EOI/NDA Sent",
            "eoi/nda sent": "EOI/NDA Sent",
            "eoi signed": "EOI/NDA Signed",
            "eoi/nda signed": "EOI/NDA Signed"
        }
        
        # Canonical status names for validation
        self.canonical_statuses = {
            "New Lead",
            "New",  # Different from New Lead
            "Contacted",
            "Qualified",
            "Unqualified",
            "Not Qualified",  # Different from Unqualified
            "Not Interested",
            "Follow up Required",
            "Call Back",
            "1st Call - No Answer",
            "2nd Call - No Answer", 
            "3rd Call - No Answer",
            "Wrong Number",
            "Region is not available",
            "Out of Budget",
            "Franchise Database",
            "EOI/NDA Sent",
            "EOI/NDA Signed",
            "Application Form Signed",
            "application_signed",  # Different from Application Form Signed
            "Deposit Paid",
            "Franchise Sold",
            "franchise_sold",  # Different from Franchise Sold
            "Junk Lead",
            "callback",  # Different from Call Back
            "wrong_number"  # Different from Wrong Number
        }
        
        # Minimum similarity threshold for fuzzy matching
        self.similarity_threshold = 0.92
    
    def normalize_status_name(self, input_name: str) -> Tuple[str, float]:
        """
        Normalize a status name to its canonical form
        
        Args:
            input_name: Raw status name to normalize
            
        Returns:
            Tuple of (canonical_name, confidence_score)
            confidence_score is 1.0 for exact matches, lower for fuzzy matches
        """
        if not input_name or not input_name.strip():
            logger.warning("Empty status name provided for normalization")
            return input_name, 0.0
        
        # Clean and normalize input
        cleaned_input = self._clean_input(input_name)
        
        # Step 1: Check for exact canonical match
        if cleaned_input in self.canonical_statuses:
            logger.debug(f"Exact canonical match found: {cleaned_input}")
            return cleaned_input, 1.0
        
        # Step 2: Check synonym mapping (case-insensitive)
        lower_input = cleaned_input.lower()
        if lower_input in self.synonym_mapping:
            canonical = self.synonym_mapping[lower_input]
            logger.debug(f"Synonym match: '{cleaned_input}' -> '{canonical}'")
            return canonical, 1.0
        
        # Step 3: Fuzzy matching with Jaro-Winkler similarity
        best_match, similarity = self._find_best_fuzzy_match(cleaned_input)
        
        if similarity >= self.similarity_threshold:
            logger.info(f"Fuzzy match: '{cleaned_input}' -> '{best_match}' (similarity: {similarity:.3f})")
            return best_match, similarity
        
        # Step 4: No good match found
        logger.warning(f"No suitable match found for status: '{cleaned_input}' (best similarity: {similarity:.3f})")
        return input_name, 0.0
    
    def _clean_input(self, input_name: str) -> str:
        """Clean and normalize input string"""
        # Remove extra whitespace and normalize case
        cleaned = re.sub(r'\s+', ' ', input_name.strip())

        # Don't replace underscores - they might be part of exact status names
        # Only replace hyphens with spaces for fuzzy matching
        # cleaned = re.sub(r'[_-]', ' ', cleaned)  # Replace underscores/hyphens with spaces
        cleaned = re.sub(r'\s+', ' ', cleaned)   # Normalize multiple spaces
        
        return cleaned
    
    def _find_best_fuzzy_match(self, input_name: str) -> Tuple[str, float]:
        """
        Find the best fuzzy match using Jaro-Winkler similarity
        
        Args:
            input_name: Cleaned input name
            
        Returns:
            Tuple of (best_match, similarity_score)
        """
        best_match = input_name
        best_similarity = 0.0
        
        # Check against all canonical statuses
        for canonical in self.canonical_statuses:
            similarity = self._jaro_winkler_similarity(input_name.lower(), canonical.lower())
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = canonical
        
        # Also check against synonym keys for better matching
        for synonym, canonical in self.synonym_mapping.items():
            similarity = self._jaro_winkler_similarity(input_name.lower(), synonym.lower())
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = canonical
        
        return best_match, best_similarity
    
    def _jaro_winkler_similarity(self, s1: str, s2: str) -> float:
        """
        Calculate Jaro-Winkler similarity between two strings
        
        This is a simplified implementation. For production, consider using
        a library like python-Levenshtein or jellyfish for better performance.
        """
        if s1 == s2:
            return 1.0
        
        # Use SequenceMatcher as approximation (not true Jaro-Winkler but similar)
        return SequenceMatcher(None, s1, s2).ratio()
    
    def is_canonical_status(self, status_name: str) -> bool:
        """Check if a status name is already canonical"""
        return status_name in self.canonical_statuses
    
    def get_all_canonical_statuses(self) -> set:
        """Get all canonical status names"""
        return self.canonical_statuses.copy()
    
    def get_synonym_mapping(self) -> Dict[str, str]:
        """Get the complete synonym mapping"""
        return self.synonym_mapping.copy()
    
    def add_synonym(self, synonym: str, canonical: str) -> bool:
        """
        Add a new synonym mapping
        
        Args:
            synonym: The synonym to add
            canonical: The canonical name it maps to
            
        Returns:
            True if added successfully, False if canonical name doesn't exist
        """
        if canonical not in self.canonical_statuses:
            logger.error(f"Cannot add synonym '{synonym}' - canonical status '{canonical}' doesn't exist")
            return False
        
        self.synonym_mapping[synonym.lower()] = canonical
        logger.info(f"Added synonym mapping: '{synonym}' -> '{canonical}'")
        return True
    
    def validate_status_name(self, status_name: str) -> Tuple[bool, str]:
        """
        Validate a status name and provide feedback
        
        Args:
            status_name: Status name to validate
            
        Returns:
            Tuple of (is_valid, feedback_message)
        """
        if not status_name or not status_name.strip():
            return False, "Status name cannot be empty"
        
        normalized, confidence = self.normalize_status_name(status_name)
        
        if confidence >= self.similarity_threshold:
            if confidence == 1.0:
                return True, f"Valid status: '{normalized}'"
            else:
                return True, f"Status normalized to: '{normalized}' (confidence: {confidence:.3f})"
        else:
            return False, f"No suitable canonical status found for: '{status_name}'"


# Global instance for easy access
status_normalizer = LeadStatusNormalizationService()
