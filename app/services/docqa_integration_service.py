"""
DocQA Integration Service for automatic document processing
"""

import logging
import os
from typing import Optional, Dict, Any

from app.core.database.connection import get_db
from app.models.document import Document
from app.models.franchisor import Franchisor

# Import DocQA components
import sys
sys.path.append('.')
from app.services.rag.ingestion_service import franchisor_ingestion_service
from app.services.rag.franchisor_embedding_service import franchisor_embedding_service
from docqa.serve import ask_question
from docqa.types import IngestionResult

logger = logging.getLogger(__name__)


class DocQAIntegrationService:
    """Service for integrating DocQA with GrowthHive API uploads"""

    # Process documents for all franchisors (restriction removed)
    # Previously was restricted to specific franchisor IDs only

    def __init__(self):
        # Use new franchisor-scoped ingestion service
        logger.info("DocQA Integration Service initialized - Processing enabled for all franchisors using franchisor-scoped RAG")
    
    async def process_franchisor_brochure(
        self,
        franchisor_id: str,
        brochure_url: str
    ) -> Optional[IngestionResult]:
        """
        Process franchisor brochure automatically after upload

        Args:
            franchisor_id: ID of the franchisor
            brochure_url: S3 URL of the uploaded brochure

        Returns:
            IngestionResult or None if processing failed
        """
        try:
            logger.info(f"Starting DocQA processing for franchisor {franchisor_id}, brochure: {brochure_url}")

            # Convert S3 filename to full URL if needed
            if not brochure_url.startswith(('http', 's3://')):
                # Construct S3 URL from filename using the correct bucket
                bucket_name = os.getenv('S3_BUCKET_NAME', 'openxcell-development-public')
                s3_url = f"s3://{bucket_name}/{brochure_url}"
            else:
                s3_url = brochure_url

            # Process the document with new franchisor-scoped ingestion
            result = await franchisor_ingestion_service.ingest_brochure_from_url(
                franchisor_id=franchisor_id,
                brochure_url=s3_url,
                clear_existing=True  # Clear existing brochure chunks for this franchisor
            )
            
            if result["success"]:
                logger.info(f"DocQA processing successful for franchisor {franchisor_id}: "
                           f"{result['chunks_created']} chunks created")
                
                # Update franchisor record to indicate processing is complete
                await self._update_franchisor_processing_status(franchisor_id, True)
                
                # Return result in expected format
                return IngestionResult(
                    success=True,
                    chunks_created=result["chunks_created"],
                    document_id=franchisor_id,
                    table_name="franchisor_chunks"
                )
                
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"DocQA processing failed for franchisor {franchisor_id}: {error_msg}")
                await self._update_franchisor_processing_status(franchisor_id, False, error_msg)
                
                return IngestionResult(
                    success=False,
                    error_message=error_msg,
                    document_id=franchisor_id,
                    table_name="franchisor_chunks",
                    chunks_created=0
                )
            
        except Exception as e:
            logger.error(f"Error processing franchisor brochure {franchisor_id}: {str(e)}")
            await self._update_franchisor_processing_status(franchisor_id, False, str(e))
            return IngestionResult(
                success=False,
                error_message=str(e),
                document_id=franchisor_id,
                table_name="franchisor_chunks",
                chunks_created=0
            )
    
    async def process_document(
        self,
        document_id: str,
        file_url: str
    ) -> Optional[IngestionResult]:
        """
        Process document automatically after upload

        Args:
            document_id: ID of the document
            file_url: S3 URL of the uploaded document

        Returns:
            IngestionResult or None if processing failed
        """
        try:
            # Disable general document processing - only process franchisor brochures for Coochie Hydrogreen
            logger.warning(f"Skipping general document processing for document {document_id} - only processing {self.ALLOWED_FRANCHISOR_NAME} brochures")
            return None
            
            # Convert S3 filename to full URL if needed
            if not file_url.startswith(('http', 's3://')):
                # Construct S3 URL from filename using the correct bucket
                bucket_name = os.getenv('S3_BUCKET_NAME', 'openxcell-development-public')
                s3_url = f"s3://{bucket_name}/{file_url}"
            else:
                s3_url = file_url
            
            # Process the document with DocQA
            result = self.ingestion_service.ingest_document(
                source=s3_url,
                force_table="documents",  # Force to documents table
                translate=True,
                extract_charts=True,
                document_id=document_id  # Pass the actual document ID from the database
            )
            
            if result.success:
                logger.info(f"DocQA processing successful for document {document_id}: "
                           f"{result.chunks_created} chunks created")
                
                # Update document record to indicate processing is complete
                await self._update_document_processing_status(document_id, True)
                
            else:
                logger.error(f"DocQA processing failed for document {document_id}: {result.error_message}")
                await self._update_document_processing_status(document_id, False, result.error_message)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {str(e)}")
            await self._update_document_processing_status(document_id, False, str(e))
            return None
    
    async def ask_question_from_document(
        self,
        document_id: str,
        question: str,
        similarity_threshold: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Ask a question specifically about a document by ID
        
        Args:
            document_id: ID of the document to query
            question: Question to ask
            
        Returns:
            Dictionary with answer and metadata
        """
        try:
            logger.info(f"Answering question about document {document_id}: {question}")
            
            # Get document info
            async for session in get_db():
                document = await session.get(Document, document_id)
                if not document:
                    return {
                        "success": False,
                        "error": f"Document {document_id} not found",
                        "answer": "Document not found"
                    }
                
                if not document.is_active or document.is_deleted:
                    return {
                        "success": False,
                        "error": f"Document {document_id} is not active",
                        "answer": "Document is not available"
                    }
            
            # Use DocQA to answer the question with document-specific search
            from docqa.vector_store.embeddings import EmbeddingService
            from docqa.vector_store.pgvector_store import PgVectorStore
            from docqa.ask import QuestionAnsweringService

            # Initialize services
            embedding_service = EmbeddingService()
            vector_store = PgVectorStore()
            qa_service = QuestionAnsweringService()

            # Generate question embedding
            question_embedding = embedding_service.generate_embedding(question)

            # Search only within this specific document
            search_results = vector_store.search_document_specific(
                document_id=document_id,
                query_embedding=question_embedding,
                top_k=10,
                similarity_threshold=similarity_threshold or 0.3  # Use provided threshold or default to 0.3
            )

            if not search_results:
                answer = "I couldn't find relevant information in this document to answer your question. The document may not contain text content or the content may not be related to your question."
            else:
                # Use the QA service to generate an answer from the document-specific results
                answer = qa_service._generate_answer(question, search_results)
                if not answer or answer.strip() == "":
                    answer = f"Based on the document content: {search_results[0].text[:200]}..."
            
            return {
                "success": True,
                "document_id": document_id,
                "document_name": document.name,
                "question": question,
                "answer": answer,
                "processing_note": "Answer based on document content and related information"
            }
            
        except Exception as e:
            logger.error(f"Error answering question for document {document_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "answer": "An error occurred while processing your question"
            }
    
    async def ask_question_from_franchisor(
        self, 
        franchisor_id: str, 
        question: str
    ) -> Dict[str, Any]:
        """
        Ask a question specifically about a franchisor by ID
        
        Args:
            franchisor_id: ID of the franchisor to query
            question: Question to ask
            
        Returns:
            Dictionary with answer and metadata
        """
        try:
            logger.info(f"Answering question about franchisor {franchisor_id}: {question}")
            
            # Get franchisor info
            async for session in get_db():
                franchisor = await session.get(Franchisor, franchisor_id)
                if not franchisor:
                    return {
                        "success": False,
                        "error": f"Franchisor {franchisor_id} not found",
                        "answer": "Franchisor not found"
                    }
                
                if not franchisor.is_active or franchisor.is_deleted:
                    return {
                        "success": False,
                        "error": f"Franchisor {franchisor_id} is not active",
                        "answer": "Franchisor is not available"
                    }
            
            # Use DocQA to answer the question with franchisor context
            contextual_question = f"About {franchisor.name} franchise (ID: {franchisor_id}): {question}"
            
            answer = ask_question(
                question=contextual_question,
                top_k=8,
                similarity_threshold=0.7,  # Higher threshold for franchisor queries
                include_metadata=True
            )
            
            return {
                "success": True,
                "franchisor_id": franchisor_id,
                "franchisor_name": franchisor.name,
                "question": question,
                "answer": answer,
                "processing_note": "Answer prioritizes franchisor-specific information"
            }
            
        except Exception as e:
            logger.error(f"Error answering question for franchisor {franchisor_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "answer": "An error occurred while processing your question"
            }
    
    async def _update_document_processing_status(
        self, 
        document_id: str, 
        success: bool, 
        error_message: Optional[str] = None
    ):
        """Update document processing status"""
        try:
            async for session in get_db():
                document = await session.get(Document, document_id)
                if document:
                    # You can add processing status fields to the Document model if needed
                    # For now, we'll just log the status
                    logger.info(f"Document {document_id} processing status: {'success' if success else 'failed'}")
                    if error_message:
                        logger.error(f"Document {document_id} processing error: {error_message}")
        except Exception as e:
            logger.error(f"Error updating document processing status: {str(e)}")
    
    async def _update_franchisor_processing_status(
        self, 
        franchisor_id: str, 
        success: bool, 
        error_message: Optional[str] = None
    ):
        """Update franchisor processing status"""
        try:
            async for session in get_db():
                franchisor = await session.get(Franchisor, franchisor_id)
                if franchisor:
                    # You can add processing status fields to the Franchisor model if needed
                    # For now, we'll just log the status
                    logger.info(f"Franchisor {franchisor_id} processing status: {'success' if success else 'failed'}")
                    if error_message:
                        logger.error(f"Franchisor {franchisor_id} processing error: {error_message}")
        except Exception as e:
            logger.error(f"Error updating franchisor processing status: {str(e)}")


# Global service instance
_docqa_service: Optional[DocQAIntegrationService] = None


def get_docqa_integration_service() -> DocQAIntegrationService:
    """Get DocQA integration service instance"""
    global _docqa_service
    
    if _docqa_service is None:
        _docqa_service = DocQAIntegrationService()
    
    return _docqa_service
