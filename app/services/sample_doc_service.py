"""
Sample Document service for business logic
"""

import logging
from typing import List

from app.repositories.sample_doc_repository import SampleDocRepository
from app.schemas.sample_doc import SampleDocResponse
from app.core.utils.exception_manager.custom_exceptions import DatabaseError

logger = logging.getLogger(__name__)


class SampleDocService:
    """Service for sample document operations"""
    
    def __init__(self, repository: SampleDocRepository):
        self.repository = repository
    
    async def get_all_docs(self) -> List[SampleDocResponse]:
        """
        Get all sample documents
        
        Returns:
            List[SampleDocResponse]: List of all sample documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # Get all documents from repository using raw SQL to avoid model issues
            from sqlalchemy import text

            logger.info("Executing raw SQL query for sample documents")
            query = text("SELECT id, title, url FROM sample_docs")
            result = await self.repository.db.execute(query)
            rows = result.fetchall()

            logger.info(f"Retrieved {len(rows)} documents from database")

            # Convert to response format
            doc_responses = []
            for i, row in enumerate(rows):
                try:
                    # Convert row to dict for easier access
                    row_dict = dict(row._mapping) if hasattr(row, '_mapping') else dict(row)

                    doc_response = SampleDocResponse(
                        id=str(row_dict.get('id', '')),
                        title=row_dict.get('title'),
                        url=row_dict.get('url')
                    )
                    doc_responses.append(doc_response)

                except Exception as doc_error:
                    logger.error(f"Error processing document {i}: {doc_error}")
                    continue

            logger.info(f"Successfully processed {len(doc_responses)} sample documents")
            return doc_responses
            
        except Exception as e:
            logger.error(f"Failed to retrieve sample documents: {e}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise DatabaseError(
                error_key="SAMPLE_DOCS_RETRIEVAL_FAILED",
                message=f"Failed to retrieve sample documents: {str(e)}"
            )
