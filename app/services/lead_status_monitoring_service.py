"""
Lead Status Monitoring Service
Service layer for integrating Lead Status Agent with SMS conversations
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from app.agents.lead_status_agent import get_lead_status_agent, ConversationAnalysis
from app.tasks.lead_status_tasks import monitor_lead_status
from app.models.lead import Lead
from app.core.database.connection import get_db
from app.core.memory.sms_memory import get_sms_memory_manager

from app.core.logging import logger


class LeadStatusMonitoringService:
    """Service for integrating lead status monitoring with SMS conversations"""
    
    def __init__(self):
        self.agent = get_lead_status_agent()
        self.memory_manager = get_sms_memory_manager()
        self.logger = logger.bind(component="lead_status_monitoring_service")
    
    async def trigger_status_check_after_conversation(
        self, 
        phone_number: str, 
        delay_seconds: int = 30
    ) -> Optional[str]:
        """
        Trigger lead status check after a conversation with delay
        
        Args:
            phone_number: Phone number of the lead
            delay_seconds: Delay before checking status (default 30 seconds)
            
        Returns:
            Task ID if successful, None otherwise
        """
        try:
            # Schedule status monitoring task with delay
            task = monitor_lead_status.apply_async(
                args=[phone_number, False],  # phone_number, force_update=False
                countdown=delay_seconds
            )
            
            self.logger.info(
                "Status check scheduled after conversation",
                phone=phone_number,
                delay_seconds=delay_seconds,
                task_id=task.id
            )
            
            return task.id
            
        except Exception as e:
            self.logger.error(f"Error scheduling status check for {phone_number}: {e}")
            return None
    
    async def check_status_after_lead_response(self, phone_number: str) -> Optional[ConversationAnalysis]:
        """
        Immediately check and potentially update lead status after lead responds
        
        Args:
            phone_number: Phone number of the lead
            
        Returns:
            ConversationAnalysis if successful, None otherwise
        """
        try:
            # Analyze conversation immediately
            analysis = await self.agent.analyze_conversation(phone_number)
            
            if not analysis:
                self.logger.warning(f"No conversation analysis available for {phone_number}")
                return None
            
            # Check if we should update status based on high-confidence indicators
            if analysis.confidence >= 0.8:  # Higher threshold for immediate updates
                success = await self.agent.update_lead_status(phone_number, analysis)
                
                if success:
                    self.logger.info(
                        "Lead status updated immediately after response",
                        phone=phone_number,
                        status=analysis.suggested_status.value,
                        confidence=analysis.confidence
                    )
                else:
                    self.logger.warning(
                        "Failed to update lead status immediately",
                        phone=phone_number,
                        status=analysis.suggested_status.value
                    )
            else:
                # Schedule delayed check for lower confidence
                await self.trigger_status_check_after_conversation(phone_number, delay_seconds=60)
                
                self.logger.info(
                    "Scheduled delayed status check due to lower confidence",
                    phone=phone_number,
                    confidence=analysis.confidence
                )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error checking status after lead response for {phone_number}: {e}")
            return None
    
    async def check_status_after_no_response(
        self, 
        phone_number: str, 
        no_response_count: int = 1
    ) -> Optional[ConversationAnalysis]:
        """
        Check and update lead status when lead doesn't respond
        
        Args:
            phone_number: Phone number of the lead
            no_response_count: Number of consecutive no-responses
            
        Returns:
            ConversationAnalysis if successful, None otherwise
        """
        try:
            # Analyze conversation
            analysis = await self.agent.analyze_conversation(phone_number)
            
            if not analysis:
                return None
            
            # Determine appropriate status based on no-response pattern
            if no_response_count == 1:
                suggested_status = "1st Call - No Answer"
            elif no_response_count == 2:
                suggested_status = "2nd Call - No Answer"
            elif no_response_count >= 3:
                suggested_status = "3rd Call - No Answer"
            else:
                suggested_status = "Follow up Required"
            
            # Override analysis with no-response specific status
            from app.agents.lead_status_agent import LeadStatusType, ConversationAnalysis
            
            no_response_analysis = ConversationAnalysis(
                suggested_status=LeadStatusType(suggested_status),
                confidence=0.9,  # High confidence for no-response patterns
                reasoning=f"No response after {no_response_count} attempt(s)",
                key_indicators=[f"no_response_count_{no_response_count}"],
                conversation_stage=analysis.conversation_stage,
                engagement_level="low",
                response_count=analysis.response_count,
                last_activity=analysis.last_activity
            )
            
            # Update status
            success = await self.agent.update_lead_status(phone_number, no_response_analysis)
            
            if success:
                self.logger.info(
                    "Lead status updated for no response pattern",
                    phone=phone_number,
                    status=suggested_status,
                    no_response_count=no_response_count
                )
            
            return no_response_analysis
            
        except Exception as e:
            self.logger.error(f"Error checking status after no response for {phone_number}: {e}")
            return None
    
    async def get_leads_needing_status_update(self, hours_back: int = 24) -> List[str]:
        """
        Get list of phone numbers for leads that may need status updates
        
        Args:
            hours_back: Hours to look back for conversation activity
            
        Returns:
            List of phone numbers
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            phone_numbers = []
            
            async for db in get_db():
                # Get leads with recent conversation activity but potentially outdated status
                result = await db.execute(
                    select(Lead.phone).where(
                        and_(
                            Lead.phone.isnot(None),
                            Lead.is_deleted == False,
                            Lead.updated_at >= cutoff_time
                        )
                    ).distinct()
                )
                
                phone_numbers = [row[0] for row in result.fetchall() if row[0]]
            
            self.logger.info(f"Found {len(phone_numbers)} leads needing potential status update")
            return phone_numbers
            
        except Exception as e:
            self.logger.error(f"Error getting leads needing status update: {e}")
            return []
    
    async def schedule_periodic_status_monitoring(self) -> Dict[str, Any]:
        """
        Schedule periodic monitoring of all active conversations
        
        Returns:
            Dict with scheduling results
        """
        try:
            # Get leads needing updates
            phone_numbers = await self.get_leads_needing_status_update(hours_back=24)
            
            if not phone_numbers:
                return {
                    "success": True,
                    "message": "No leads found needing status updates",
                    "total_leads": 0
                }
            
            # Schedule monitoring tasks with staggered delays to avoid overwhelming system
            scheduled_tasks = []
            
            for i, phone_number in enumerate(phone_numbers):
                # Stagger tasks by 10 seconds each
                delay = i * 10
                
                task = monitor_lead_status.apply_async(
                    args=[phone_number, False],
                    countdown=delay
                )
                
                scheduled_tasks.append({
                    "phone_number": phone_number,
                    "task_id": task.id,
                    "delay_seconds": delay
                })
            
            self.logger.info(
                "Periodic status monitoring scheduled",
                total_leads=len(phone_numbers),
                total_tasks=len(scheduled_tasks)
            )
            
            return {
                "success": True,
                "message": f"Scheduled monitoring for {len(phone_numbers)} leads",
                "total_leads": len(phone_numbers),
                "scheduled_tasks": scheduled_tasks[:10]  # Show first 10 for reference
            }
            
        except Exception as e:
            self.logger.error(f"Error scheduling periodic status monitoring: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_status_monitoring_stats(self) -> Dict[str, Any]:
        """
        Get statistics about lead status monitoring
        
        Returns:
            Dict with monitoring statistics
        """
        try:
            stats = {
                "total_leads_in_system": 0,
                "leads_with_conversations": 0,
                "recent_status_updates": 0,
                "monitoring_enabled": True
            }
            
            async for db in get_db():
                # Total leads
                result = await db.execute(
                    select(Lead.id).where(Lead.is_deleted == False)
                )
                stats["total_leads_in_system"] = len(result.fetchall())
                
                # Leads with phone numbers (potential for SMS conversations)
                result = await db.execute(
                    select(Lead.id).where(
                        and_(
                            Lead.phone.isnot(None),
                            Lead.is_deleted == False
                        )
                    )
                )
                stats["leads_with_conversations"] = len(result.fetchall())
                
                # Recent status updates (last 24 hours)
                cutoff_time = datetime.now() - timedelta(hours=24)
                result = await db.execute(
                    select(Lead.id).where(
                        and_(
                            Lead.updated_at >= cutoff_time,
                            Lead.is_deleted == False
                        )
                    )
                )
                stats["recent_status_updates"] = len(result.fetchall())
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting status monitoring stats: {e}")
            return {"error": str(e)}


# Global service instance
_monitoring_service: Optional[LeadStatusMonitoringService] = None


def get_lead_status_monitoring_service() -> LeadStatusMonitoringService:
    """Get or create Lead Status Monitoring Service instance"""
    global _monitoring_service
    
    if _monitoring_service is None:
        _monitoring_service = LeadStatusMonitoringService()
    
    return _monitoring_service
