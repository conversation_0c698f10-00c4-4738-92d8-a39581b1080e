"""
Holiday Availability Service
Checks admin availability based on holiday table and returns appropriate messages
"""

from datetime import datetime, date, time
from typing import Optional, Dict, Any, <PERSON><PERSON>
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.models.holiday import Holiday
from app.models.general_message import GeneralMessage
from app.core.logging import logger

logger = structlog.get_logger(__name__)


class HolidayAvailabilityService:
    """Service to check admin availability based on holiday schedules"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def check_admin_availability(self, check_datetime: Optional[datetime] = None) -> Tuple[bool, Optional[str]]:
        """
        Check if admin is available at the given datetime
        
        Args:
            check_datetime: DateTime to check availability for (defaults to current datetime)
            
        Returns:
            Tuple of (is_available: bool, out_of_reach_message: Optional[str])
            - is_available: True if admin is available, False if unavailable
            - out_of_reach_message: Message to show if unavailable, None if available
        """
        try:
            if check_datetime is None:
                check_datetime = datetime.now()
            
            check_date = check_datetime.date()
            check_time = check_datetime.time()
            
            logger.info(f"Checking admin availability for {check_datetime}")
            
            # Check for active holidays on the current date
            stmt = select(Holiday).where(
                and_(
                    Holiday.date == check_date,
                    Holiday.is_active == True,
                    Holiday.is_deleted == False
                )
            )
            
            result = await self.db.execute(stmt)
            holidays = result.scalars().all()
            
            if not holidays:
                logger.info("No holidays found for current date - admin is available")
                return True, None
            
            # Check each holiday to see if admin is unavailable
            for holiday in holidays:
                is_unavailable = await self._is_admin_unavailable_for_holiday(
                    holiday, check_date, check_time
                )
                
                if is_unavailable:
                    logger.info(f"Admin is unavailable due to holiday: {holiday.description}")
                    
                    # Get out_of_reach message from general_message table
                    out_of_reach_message = await self._get_out_of_reach_message()
                    return False, out_of_reach_message
            
            logger.info("Admin is available - no conflicting holidays")
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking admin availability: {str(e)}")
            # In case of error, assume admin is available to avoid blocking conversations
            return True, None
    
    async def _is_admin_unavailable_for_holiday(
        self, 
        holiday: Holiday, 
        check_date: date, 
        check_time: time
    ) -> bool:
        """
        Check if admin is unavailable for a specific holiday
        
        Args:
            holiday: Holiday record to check
            check_date: Date to check
            check_time: Time to check
            
        Returns:
            True if admin is unavailable, False if available
        """
        try:
            # PREDEFINED holidays - admin unavailable all day
            if holiday.holiday_type == "PREDEFINED":
                logger.info(f"PREDEFINED holiday '{holiday.description}' - admin unavailable all day")
                return True
            
            # PERSONAL holidays - check all_day flag
            elif holiday.holiday_type == "PERSONAL":
                if holiday.all_day:
                    logger.info(f"PERSONAL all-day holiday '{holiday.description}' - admin unavailable all day")
                    return True
                else:
                    # Check if current time falls within the holiday time range
                    if holiday.start_time and holiday.end_time:
                        if holiday.start_time <= check_time <= holiday.end_time:
                            logger.info(f"PERSONAL timed holiday '{holiday.description}' - admin unavailable from {holiday.start_time} to {holiday.end_time}")
                            return True
                        else:
                            logger.info(f"PERSONAL timed holiday '{holiday.description}' - admin available (outside time range)")
                            return False
                    else:
                        logger.warning(f"PERSONAL holiday without proper time range - treating as unavailable")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking holiday availability: {str(e)}")
            return False
    
    async def _get_out_of_reach_message(self) -> str:
        """
        Get the out_of_reach message from general_message table
        
        Returns:
            Out of reach message or default message if not found
        """
        try:
            stmt = select(GeneralMessage).where(
                GeneralMessage.message_type == "out_of_reach"
            )
            
            result = await self.db.execute(stmt)
            message_record = result.scalar_one_or_none()
            
            if message_record and message_record.message:
                logger.info("Retrieved out_of_reach message from database")
                return message_record.message
            else:
                logger.warning("No out_of_reach message found in database, using default")
                return self._get_default_out_of_reach_message()
                
        except Exception as e:
            logger.error(f"Error retrieving out_of_reach message: {str(e)}")
            return self._get_default_out_of_reach_message()
    
    def _get_default_out_of_reach_message(self) -> str:
        """Get default out of reach message"""
        return (
            "Thank you for your interest in our franchise opportunities. "
            "Our team is currently unavailable due to holidays. "
            "We will get back to you as soon as possible. "
            "Have a great day!"
        )
    
    async def get_current_holidays(self) -> Dict[str, Any]:
        """
        Get information about current active holidays
        
        Returns:
            Dictionary with current holiday information
        """
        try:
            current_date = date.today()
            
            stmt = select(Holiday).where(
                and_(
                    Holiday.date == current_date,
                    Holiday.is_active == True,
                    Holiday.is_deleted == False
                )
            )
            
            result = await self.db.execute(stmt)
            holidays = result.scalars().all()
            
            holiday_info = []
            for holiday in holidays:
                holiday_data = {
                    "id": str(holiday.id),
                    "type": holiday.holiday_type,
                    "date": holiday.date.isoformat(),
                    "all_day": holiday.all_day,
                    "description": holiday.description
                }
                
                if not holiday.all_day and holiday.start_time and holiday.end_time:
                    holiday_data["start_time"] = holiday.start_time.isoformat()
                    holiday_data["end_time"] = holiday.end_time.isoformat()
                
                holiday_info.append(holiday_data)
            
            return {
                "current_date": current_date.isoformat(),
                "holidays": holiday_info,
                "total_holidays": len(holiday_info)
            }
            
        except Exception as e:
            logger.error(f"Error getting current holidays: {str(e)}")
            return {
                "current_date": date.today().isoformat(),
                "holidays": [],
                "total_holidays": 0,
                "error": str(e)
            }
    
    async def is_admin_available_for_date_range(
        self, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """
        Check admin availability for a date range
        
        Args:
            start_date: Start date to check
            end_date: End date to check
            
        Returns:
            Dictionary with availability information
        """
        try:
            stmt = select(Holiday).where(
                and_(
                    Holiday.date >= start_date,
                    Holiday.date <= end_date,
                    Holiday.is_active == True,
                    Holiday.is_deleted == False
                )
            )
            
            result = await self.db.execute(stmt)
            holidays = result.scalars().all()
            
            unavailable_dates = []
            for holiday in holidays:
                unavailable_dates.append({
                    "date": holiday.date.isoformat(),
                    "type": holiday.holiday_type,
                    "all_day": holiday.all_day,
                    "description": holiday.description,
                    "start_time": holiday.start_time.isoformat() if holiday.start_time else None,
                    "end_time": holiday.end_time.isoformat() if holiday.end_time else None
                })
            
            return {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "unavailable_dates": unavailable_dates,
                "total_unavailable_days": len(unavailable_dates),
                "has_availability_conflicts": len(unavailable_dates) > 0
            }
            
        except Exception as e:
            logger.error(f"Error checking availability for date range: {str(e)}")
            return {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "unavailable_dates": [],
                "total_unavailable_days": 0,
                "has_availability_conflicts": False,
                "error": str(e)
            }
