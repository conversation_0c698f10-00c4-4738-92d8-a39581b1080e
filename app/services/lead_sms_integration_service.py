"""
Lead SMS Integration Service - Handles SMS integration for lead lifecycle events
Coordinates between lead creation, SMS sending, and conversation storage
"""

from datetime import datetime
from typing import Dict, Any, Optional
from uuid import UUID
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.models.lead import Lead
from app.models.messaging_rule import MessagingRule
from app.services.sms_outbound_service import get_sms_outbound_service
from app.tasks.sms_onboarding_tasks import send_intro_sms_to_lead
from app.core.utils.exception_manager.custom_exceptions import BusinessLogicError

from app.core.logging import logger


class LeadSMSIntegrationService:
    """Service for integrating SMS functionality with lead lifecycle"""
    
    def __init__(self):
        """Initialize lead SMS integration service"""
        self.sms_outbound_service = get_sms_outbound_service()
    
    async def handle_new_lead_created(
        self, 
        lead_id: str, 
        db: AsyncSession,
        immediate_send: bool = False
    ) -> Dict[str, Any]:
        """
        Handle new lead creation event - schedule or send introductory SMS
        
        Args:
            lead_id: ID of the newly created lead
            db: Database session
            immediate_send: If True, send immediately; if False, use messaging rule delay
            
        Returns:
            Dict containing processing result
        """
        try:
            logger.info(
                f"Handling new lead creation for SMS onboarding",
                lead_id=lead_id,
                immediate_send=immediate_send
            )
            
            # Validate lead exists and has phone number
            lead = await self._get_lead_by_id(db, lead_id)
            if not lead:
                return {
                    "success": False,
                    "error": f"Lead not found: {lead_id}",
                    "lead_id": lead_id
                }
            
            phone_number = lead.mobile or lead.phone
            if not phone_number:
                logger.warning(f"Lead {lead_id} has no phone number, skipping SMS onboarding")
                return {
                    "success": False,
                    "error": "Lead has no phone number",
                    "lead_id": lead_id,
                    "skipped": True
                }
            
            if immediate_send:
                # Send immediately
                result = await self.sms_outbound_service.send_introductory_sms(
                    lead_id=lead_id,
                    db=db
                )
                
                logger.info(
                    f"Immediate intro SMS sent to new lead",
                    lead_id=lead_id,
                    success=result.get("success"),
                    phone_number=phone_number
                )
                
                return {
                    "success": result.get("success", False),
                    "method": "immediate",
                    "lead_id": lead_id,
                    "phone_number": phone_number,
                    "result": result
                }
            else:
                # Check if follow-up suppression is enabled
                from app.core.followup_suppression import block_followup_if_enabled

                suppression_result = block_followup_if_enabled(
                    "lead_sms_integration_scheduling",
                    lead_id=lead_id[:8] + "..." if lead_id else None,
                    phone_number=phone_number[:6] + "***" if phone_number else None
                )

                if suppression_result:
                    logger.info(
                        f"Lead SMS integration scheduling suppressed for lead {lead_id}",
                        reason=suppression_result.get("reason"),
                        suppressed=True
                    )
                    return {
                        "success": True,
                        "method": "suppressed",
                        "lead_id": lead_id,
                        "phone_number": phone_number,
                        "suppression_reason": suppression_result.get("reason")
                    }

                # Schedule with delay based on messaging rule
                messaging_rule = await self._get_active_messaging_rule(db)
                delay_hours = messaging_rule.lead_init_delay_h if messaging_rule else 1

                # Schedule background task
                task = send_intro_sms_to_lead.apply_async(
                    args=[lead_id],
                    kwargs={"delay_minutes": delay_hours * 60},
                    countdown=delay_hours * 3600  # Convert hours to seconds for Celery
                )
                
                logger.info(
                    f"Scheduled intro SMS for new lead",
                    lead_id=lead_id,
                    delay_hours=delay_hours,
                    task_id=task.id,
                    phone_number=phone_number
                )
                
                return {
                    "success": True,
                    "method": "scheduled",
                    "lead_id": lead_id,
                    "phone_number": phone_number,
                    "delay_hours": delay_hours,
                    "task_id": task.id
                }
                
        except Exception as e:
            logger.error(
                f"Error handling new lead creation: {str(e)}",
                lead_id=lead_id,
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e),
                "lead_id": lead_id
            }
    
    async def handle_lead_updated(
        self, 
        lead_id: str, 
        db: AsyncSession,
        changes: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle lead update event - check if phone number was added
        
        Args:
            lead_id: ID of the updated lead
            db: Database session
            changes: Dictionary of changed fields
            
        Returns:
            Dict containing processing result
        """
        try:
            # Check if phone number was added
            phone_added = (
                "phone" in changes or 
                "mobile" in changes
            )
            
            if not phone_added:
                return {
                    "success": True,
                    "action": "no_phone_change",
                    "lead_id": lead_id
                }
            
            # Check if this lead already has SMS conversation
            has_existing_sms = await self._check_existing_sms_conversation(db, lead_id)
            
            if has_existing_sms:
                logger.info(f"Lead {lead_id} already has SMS conversation, skipping onboarding")
                return {
                    "success": True,
                    "action": "already_has_sms",
                    "lead_id": lead_id
                }
            
            # Trigger SMS onboarding for updated lead
            result = await self.handle_new_lead_created(
                lead_id=lead_id,
                db=db,
                immediate_send=False  # Use standard delay
            )
            
            return {
                "success": result.get("success", False),
                "action": "phone_added_onboarding",
                "lead_id": lead_id,
                "result": result
            }
            
        except Exception as e:
            logger.error(
                f"Error handling lead update: {str(e)}",
                lead_id=lead_id,
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e),
                "lead_id": lead_id
            }
    
    # Follow-up functionality has been removed from Andy AI
    # The handle_no_response_followup method has been removed
    
    async def _get_lead_by_id(self, db: AsyncSession, lead_id: str) -> Optional[Lead]:
        """Get lead by ID from database"""
        try:
            query = select(Lead).where(
                and_(
                    Lead.id == UUID(lead_id),
                    Lead.is_active == True,
                    Lead.is_deleted == False
                )
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting lead by ID: {str(e)}")
            return None
    
    async def _get_active_messaging_rule(self, db: AsyncSession) -> Optional[MessagingRule]:
        """Get the active messaging rule from database"""
        try:
            query = select(MessagingRule).where(
                and_(
                    MessagingRule.is_active == True,
                    MessagingRule.is_deleted == False
                )
            ).order_by(MessagingRule.created_at.desc())
            
            result = await db.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Error getting active messaging rule: {str(e)}")
            return None
    
    async def _check_existing_sms_conversation(self, db: AsyncSession, lead_id: str) -> bool:
        """Check if lead already has SMS conversation"""
        try:
            from app.models.conversation_message import ConversationMessage
            
            query = select(ConversationMessage).where(
                and_(
                    ConversationMessage.lead_id == UUID(lead_id),
                    ConversationMessage.is_active == True,
                    ConversationMessage.is_deleted == False
                )
            ).limit(1)
            
            result = await db.execute(query)
            existing_message = result.scalar_one_or_none()
            
            return existing_message is not None
            
        except Exception as e:
            logger.error(f"Error checking existing SMS conversation: {str(e)}")
            return False
    
    async def _count_system_messages(self, db: AsyncSession, lead_id: str) -> int:
        """Count system messages sent to a lead"""
        try:
            from app.models.conversation_message import ConversationMessage
            from sqlalchemy import func
            
            query = select(func.count(ConversationMessage.id)).where(
                and_(
                    ConversationMessage.lead_id == UUID(lead_id),
                    ConversationMessage.sender == "system",
                    ConversationMessage.is_active == True,
                    ConversationMessage.is_deleted == False
                )
            )
            
            result = await db.execute(query)
            count = result.scalar() or 0
            
            return count
            
        except Exception as e:
            logger.error(f"Error counting system messages: {str(e)}")
            return 0


# Singleton instance
_lead_sms_integration_service = None


def get_lead_sms_integration_service() -> LeadSMSIntegrationService:
    """Get singleton instance of lead SMS integration service"""
    global _lead_sms_integration_service
    if _lead_sms_integration_service is None:
        _lead_sms_integration_service = LeadSMSIntegrationService()
    return _lead_sms_integration_service
