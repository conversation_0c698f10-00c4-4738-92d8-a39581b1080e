"""
Refresh token manager service for handling JWT token refresh
"""
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
import secrets
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.refresh_token import RefreshToken
from app.models.user import User
from app.core.config.settings import settings

class RefreshTokenManager:
    """Refresh token manager service for handling JWT token refresh"""

    async def create_refresh_token(
        self,
        user_id: int,
        db: AsyncSession = None,
        remember_me: bool = False
    ) -> str:
        """Create a new refresh token for a user"""
        # Generate a secure random token
        token = secrets.token_urlsafe(32)

        # Set expiration based on remember_me setting
        expires_at = datetime.now(timezone.utc) + timedelta(
            days=settings.JWT_REMEMBER_ME_TOKEN_EXPIRE_DAYS if remember_me
            else settings.REFRESH_TOKEN_EXPIRE_DAYS
        )

        # Create refresh token record
        refresh_token = RefreshToken(
            user_id=user_id,
            token=token,
            is_active=True,
            expires_at=expires_at
        )
        db.add(refresh_token)
        await db.commit()
        await db.refresh(refresh_token)

        return token

    async def validate_refresh_token(
        self,
        token: str,
        db: AsyncSession = None
    ) -> Optional[Dict[str, Any]]:
        """Validate a refresh token and return user data if valid"""
        stmt = select(RefreshToken).where(
            RefreshToken.token == token,
            RefreshToken.is_active,
            RefreshToken.expires_at > datetime.now(timezone.utc)
        )
        result = await db.execute(stmt)
        refresh_token = result.scalar_one_or_none()

        if not refresh_token:
            return None

        # Get user data
        stmt = select(User).where(User.id == refresh_token.user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if not user or not user.is_active:
            return None

        return {
            "user_id": user.id,
            "email": user.email
        }

    async def invalidate_refresh_token(
        self,
        token: str,
        db: AsyncSession = None
    ) -> bool:
        """Invalidate a specific refresh token"""
        stmt = select(RefreshToken).where(
            RefreshToken.token == token,
            RefreshToken.is_active
        )
        result = await db.execute(stmt)
        refresh_token = result.scalar_one_or_none()

        if refresh_token:
            refresh_token.is_active = False
            await db.commit()
            return True
        return False

    async def invalidate_all_refresh_tokens(
        self,
        user_id: int,
        db: AsyncSession = None
    ) -> int:
        """Invalidate all refresh tokens for a user"""
        stmt = select(RefreshToken).where(
            RefreshToken.user_id == user_id,
            RefreshToken.is_active
        )
        result = await db.execute(stmt)
        refresh_tokens = result.scalars().all()

        for token in refresh_tokens:
            token.is_active = False

        await db.commit()
        return len(refresh_tokens)

    async def cleanup_expired_tokens(
        self,
        db: AsyncSession = None
    ) -> int:
        """Clean up expired refresh tokens"""
        stmt = select(RefreshToken).where(
            RefreshToken.is_active,
            RefreshToken.expires_at < datetime.now(timezone.utc)
        )
        result = await db.execute(stmt)
        expired_tokens = result.scalars().all()

        for token in expired_tokens:
            token.is_active = False

        await db.commit()
        return len(expired_tokens) 