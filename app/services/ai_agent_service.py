"""
AI Agent Service for Lead Qualification and Response
Handles automated lead qualification and response generation
"""

from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime

from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.services.sms_service import SMSService
from app.core.logging import logger

class AIAgentService:
    """AI Agent service for automated lead qualification and response"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.sms_service = SMSService()
        
        # Intent keywords for Australian franchisor context
        self.intent_keywords = {
            "investment": ["investment", "cost", "price", "money", "budget", "how much", "fee", "franchise fee"],
            "meeting": ["meeting", "appointment", "call", "discuss", "talk", "consultation", "schedule"],
            "information": ["information", "details", "brochure", "more info", "tell me more", "learn more"],
            "qualification": ["qualify", "requirements", "criteria", "eligibility", "suitable", "right for me"],
            "location": ["location", "area", "territory", "region", "where", "available"],
            "timeline": ["timeline", "when", "start", "launch", "timeframe", "duration"]
        }
        
        # Qualification questions
        self.qualification_questions = [
            "What's your investment budget range?",
            "What's your preferred location?",
            "Do you have business experience?",
            "When are you looking to start?",
            "What's your motivation for franchising?"
        ]
    
    async def process_message(
        self,
        phone_number: str,
        message_content: str,
        franchisor_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process incoming message and generate appropriate response"""
        try:
            lead = await self._get_or_create_lead(phone_number, franchisor_id)
            
            # Analyze message intent
            intent = self._analyze_intent(message_content.lower())
            
            # Generate response based on intent and lead state
            response = await self._generate_response(
                lead, message_content, intent, franchisor_id
            )
            
            # Update lead with new interaction
            await self._update_lead_interaction(lead["id"], message_content, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "success": False,
                "content": "I'm sorry, but I'm having trouble processing your message right now. A franchisor specialist will contact you shortly.",
                "intent": "error"
            }
    
    def _analyze_intent(self, message: str) -> str:
        """Analyze message intent based on keywords"""
        for intent, keywords in self.intent_keywords.items():
            if any(keyword in message for keyword in keywords):
                return intent
        return "general"
    
    async def _generate_response(
        self,
        lead: Dict[str, Any],
        message_content: str,
        intent: str,
        franchisor_id: Optional[str]
    ) -> Dict[str, Any]:
        """Generate appropriate response based on intent and lead state"""
        
        # Determine franchisor context
        target_franchisor_id = franchisor_id or lead.get("franchisor_id")
        
        if lead.get("qualification_state") == "completed":
            return await self._handle_qualified_lead(
                lead, message_content, intent, target_franchisor_id
            )
        
        if intent == "meeting":
            return await self._handle_meeting_request(lead, target_franchisor_id)
        
        if intent == "qualification":
            return await self._start_qualification_process(lead, target_franchisor_id)
        
        # Default response
        return await self._handle_general_inquiry(
            lead, message_content, target_franchisor_id
        )
    
    async def _handle_general_inquiry(
        self,
        lead: Dict[str, Any],
        message_content: str,
        franchisor_id: Optional[str]
    ) -> Dict[str, Any]:
        """Handle general inquiries"""
        try:
            franchisor_info = None
            if franchisor_id:
                result = await self.db.execute(select(Franchisor).where(Franchisor.id == franchisor_id))
                franchisor_info = result.scalar_one_or_none()
            
            # Send initial response
            await self.sms_service.send_message(
                phone_number=lead["contact_number"],
                message="Thanks for your interest! Let me get you some information."
            )
            
            if franchisor_info:
                await self.sms_service.send_franchisor_introduction(
                    phone_number=lead["contact_number"],
                    franchisor_name=franchisor_info.name,
                    franchisor_category=getattr(franchisor_info, "category", "business")
                )
            else:
                await self.sms_service.send_message(
                    phone_number=lead["contact_number"],
                    message="G'day! Thanks for your interest in our franchisor opportunity. How can I help you today?"
                )
            
            return {
                "success": True,
                "content": "Thanks for your interest! I've sent you some information. How can I help you further?",
                "intent": "general"
            }
            
        except Exception as e:
            logger.error(f"Error handling general inquiry: {e}")
            return {
                "success": False,
                "content": "I'm sorry, but I couldn't process your request. A franchisor specialist will contact you shortly.",
                "intent": "error"
            }
    
    async def _handle_qualified_lead(
        self,
        lead: Dict[str, Any],
        message_content: str,
        intent: str,
        franchisor_id: Optional[str]
    ) -> Dict[str, Any]:
        """Handle qualified leads"""
        try:
            if intent == "investment":
                return {
                    "success": True,
                    "content": "Investment details vary by franchisor and location. I'd love to provide you with specific numbers for your situation. Would you like to schedule a quick call with our franchisor specialist to discuss the investment requirements in AUD?",
                    "intent": "investment"
                }
            else:
                return {
                    "success": True,
                    "content": "That's a great question! Let me connect you with our franchisor specialist who can provide detailed information specific to your needs. Would you prefer a call or can I send you some initial information?",
                    "intent": "general"
                }
                
        except Exception as e:
            logger.error(f"Error handling qualified lead: {e}")
            return {
                "success": False,
                "content": "I'm sorry, but I couldn't process your request. A franchisor specialist will contact you shortly.",
                "intent": "error"
            }
    
    async def _get_or_create_lead(self, phone_number: str, franchisor_id: Optional[str]) -> Dict[str, Any]:
        """Get existing lead or create new one"""
        try:
            # Check if lead exists
            result = await self.db.execute(
                select(Lead).where(Lead.contact_number == phone_number)
            )
            lead = result.scalar_one_or_none()
            
            if not lead:
                # Create new lead
                lead = Lead(contact_number=phone_number, franchisor_id=franchisor_id)
                self.db.add(lead)
                await self.db.commit()
                await self.db.refresh(lead)
            
            return {
                "id": str(lead.id),
                "contact_number": lead.contact_number,
                "franchisor_id": lead.franchisor_id,
                "qualification_state": lead.qualification_state,
                "created_at": lead.created_at
            }
            
        except Exception as e:
            logger.error(f"Error getting or creating lead: {e}")
            raise
    
    async def _update_lead_interaction(self, lead_id: str, message: str, response: Dict[str, Any]) -> None:
        """Update lead with new interaction"""
        try:
            # Update lead with latest interaction
            await self.db.execute(
                update(Lead)
                .where(Lead.id == lead_id)
                .values(
                    last_interaction=datetime.utcnow(),
                    interaction_count=Lead.interaction_count + 1
                )
            )
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"Error updating lead interaction: {e}")
    
    async def _handle_meeting_request(self, lead: Dict[str, Any], franchisor_id: Optional[str]) -> Dict[str, Any]:
        """Handle meeting requests"""
        try:
            # Schedule meeting (placeholder)
            await self.sms_service.send_message(
                phone_number=lead["contact_number"],
                message="Your meeting has been scheduled. A franchisor specialist will contact you shortly to confirm the details."
            )
            
            return {
                "success": True,
                "content": "Your meeting has been scheduled. A franchisor specialist will contact you shortly to confirm the details.",
                "intent": "meeting"
            }
            
        except Exception as e:
            logger.error(f"Error handling meeting request: {e}")
            return {
                "success": False,
                "content": "I'm sorry, but I couldn't process your request. A franchisor specialist will contact you shortly.",
                "intent": "error"
            }
    
    async def _start_qualification_process(self, lead: Dict[str, Any], franchisor_id: Optional[str]) -> Dict[str, Any]:
        """Start qualification process"""
        try:
            # Update lead qualification state
            await self.db.execute(
                update(Lead)
                .where(Lead.id == lead["id"])
                .values(qualification_state="in_progress")
            )
            await self.db.commit()
            
            await self.sms_service.send_message(
                phone_number=lead["contact_number"],
                message="Thank you for starting the qualification process. A franchisor specialist will contact you shortly to discuss your requirements."
            )
            
            return {
                "success": True,
                "content": "Thank you for starting the qualification process. A franchisor specialist will contact you shortly to discuss your requirements.",
                "intent": "qualification"
            }
            
        except Exception as e:
            logger.error(f"Error starting qualification process: {e}")
            return {
                "success": False,
                "content": "I'm sorry, but I couldn't start the qualification process. A franchisor specialist will contact you shortly.",
                "intent": "error"
            }
