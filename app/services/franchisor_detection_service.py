"""
Franchisor Detection Service

This service uses OpenAI's advanced features (function calling, embeddings, structured outputs)
to dynamically detect franchisor names/keywords from questions and match them against
the database of active franchisors using context retrieval similar to RAG.
"""

import json
import re
from typing import Optional, List, Dict, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
import openai
from sklearn.metrics.pairwise import cosine_similarity
from app.core.logging import logger
from app.repositories.franchisor_repository import FranchisorRepository
from app.core.config.settings import settings


class AdvancedFranchisorDetectionService:
    """
    Advanced service for detecting franchisors using OpenAI's advanced features:
    - Embeddings for semantic similarity
    - Function calling for dynamic data retrieval
    - Structured outputs for consistent results
    - Context retrieval similar to RAG systems
    """

    def __init__(self, session: AsyncSession):
        """
        Initialize the advanced franchisor detection service

        Args:
            session: Database session for franchisor queries
        """
        self.session = session
        self.franchisor_repository = FranchisorRepository(session)
        self.settings = settings

        # Initialize OpenAI client
        if not self.settings.OPENAI_API_KEY:
            raise ValueError("OpenAI API key not configured")

        self.openai_client = openai.OpenAI(api_key=self.settings.OPENAI_API_KEY)

        # Advanced configuration from settings
        self.embedding_model = self.settings.FRANCHISOR_DETECTION_EMBEDDING_MODEL
        self.detection_model = self.settings.FRANCHISOR_DETECTION_MODEL
        self.confidence_threshold = self.settings.FRANCHISOR_DETECTION_CONFIDENCE_THRESHOLD
        self.semantic_similarity_threshold = self.settings.FRANCHISOR_DETECTION_SEMANTIC_THRESHOLD

        # Cache for franchisor embeddings (in production, use Redis)
        self._franchisor_embeddings_cache = {}
        self._franchisors_cache = None
        
    async def detect_franchisor_from_question(
        self,
        question: str,
        include_confidence: bool = False,
        use_embeddings: bool = True
    ) -> Union[Optional[str], Tuple[Optional[str], float]]:
        """
        Advanced franchisor detection using multiple OpenAI features

        Args:
            question: The question to analyze
            include_confidence: Whether to return confidence score
            use_embeddings: Whether to use semantic similarity via embeddings

        Returns:
            Franchisor ID if detected, None otherwise
            If include_confidence=True, returns (franchisor_id, confidence_score)
        """
        try:
            logger.info(f"Starting advanced franchisor detection for question: {question[:100]}")

            # Step 1: Get active franchisors with caching
            active_franchisors = await self._get_active_franchisors_with_cache()

            if not active_franchisors:
                logger.warning("No active franchisors found for detection")
                return (None, 0.0) if include_confidence else None

            # Step 2: Multi-approach detection
            detection_results = []

            # Approach 1: Semantic similarity using embeddings
            if use_embeddings:
                embedding_result = await self._detect_using_embeddings(question, active_franchisors)
                if embedding_result:
                    detection_results.append(("embeddings", embedding_result))

            # Approach 2: Function calling with structured output
            function_result = await self._detect_using_function_calling(question, active_franchisors)
            if function_result:
                detection_results.append(("function_calling", function_result))

            # Approach 3: Direct analysis with structured output
            structured_result = await self._detect_using_structured_output(question, active_franchisors)
            if structured_result:
                detection_results.append(("structured", structured_result))

            # Step 3: Combine results and select best match
            final_result = await self._combine_detection_results(detection_results, active_franchisors)

            if final_result and final_result[1] >= self.confidence_threshold:
                logger.info(
                    f"Advanced franchisor detection successful - ID: {final_result[0]}, "
                    f"confidence: {final_result[1]}, methods: {[method for method, _ in detection_results]}"
                )
                return final_result if include_confidence else final_result[0]
            else:
                logger.info(
                    f"Franchisor detection confidence too low or no detection - "
                    f"confidence: {final_result[1] if final_result else 0.0}, threshold: {self.confidence_threshold}"
                )
                return (None, final_result[1] if final_result else 0.0) if include_confidence else None

        except Exception as e:
            logger.error(f"Error in advanced franchisor detection: {e}", exc_info=True)
            return (None, 0.0) if include_confidence else None
    
    async def _get_active_franchisors_with_cache(self) -> List[Dict[str, Any]]:
        """Get all active franchisors with caching for performance"""
        if self._franchisors_cache is not None:
            return self._franchisors_cache

        try:
            franchisors, _ = await self.franchisor_repository.get_multi_with_filters(
                is_active=True,
                limit=100  # Get all active franchisors
            )

            self._franchisors_cache = [
                {
                    "id": str(franchisor.id),
                    "name": franchisor.name,
                    "description": getattr(franchisor, 'description', '') or "",
                    "category": getattr(franchisor, 'category', '') or "",
                    "region": getattr(franchisor, 'region', '') or "",
                    "keywords": self._extract_keywords_from_franchisor(franchisor),
                    "search_text": self._create_search_text(franchisor)
                }
                for franchisor in franchisors
            ]

            return self._franchisors_cache

        except Exception as e:
            logger.error(f"Error getting active franchisors: {e}")
            return []

    def _create_search_text(self, franchisor) -> str:
        """Create comprehensive search text for embeddings"""
        parts = []

        if franchisor.name:
            parts.append(f"Franchise name: {franchisor.name}")

        description = getattr(franchisor, 'description', '')
        if description:
            parts.append(f"Description: {description}")

        category = getattr(franchisor, 'category', '')
        if category:
            parts.append(f"Category: {category}")

        region = getattr(franchisor, 'region', '')
        if region:
            parts.append(f"Region: {region}")

        return " | ".join(parts)
    
    def _extract_keywords_from_franchisor(self, franchisor) -> List[str]:
        """Extract potential keywords from franchisor data"""
        keywords = []
        
        # Add name variations
        if franchisor.name:
            keywords.append(franchisor.name.lower())
            # Add individual words from name
            keywords.extend([word.lower() for word in franchisor.name.split()])
        
        # Add description keywords
        description = getattr(franchisor, 'description', '')
        if description:
            # Extract meaningful words (longer than 3 characters)
            desc_words = re.findall(r'\b\w{4,}\b', description.lower())
            keywords.extend(desc_words[:5])  # Limit to top 5 words

        # Add category keywords
        category = getattr(franchisor, 'category', '')
        if category:
            keywords.append(category.lower())
        
        return list(set(keywords))  # Remove duplicates

    async def _detect_using_embeddings(
        self,
        question: str,
        franchisors: List[Dict[str, Any]]
    ) -> Optional[Tuple[str, float]]:
        """
        Use OpenAI embeddings for semantic similarity detection

        Args:
            question: The question to analyze
            franchisors: List of active franchisors

        Returns:
            Tuple of (franchisor_id, confidence) if detected, None otherwise
        """
        try:
            logger.info("Using embeddings for franchisor detection")

            # Generate embedding for the question
            question_embedding = await self._get_embedding(question)
            if question_embedding is None:
                return None

            # Get or generate embeddings for all franchisors
            franchisor_similarities = []

            for franchisor in franchisors:
                franchisor_embedding = await self._get_franchisor_embedding(franchisor)
                if franchisor_embedding is None:
                    continue

                # Calculate cosine similarity
                similarity = cosine_similarity(
                    [question_embedding],
                    [franchisor_embedding]
                )[0][0]

                franchisor_similarities.append({
                    "id": franchisor["id"],
                    "name": franchisor["name"],
                    "similarity": float(similarity)
                })

            # Sort by similarity and get the best match
            if franchisor_similarities:
                best_match = max(franchisor_similarities, key=lambda x: x["similarity"])

                if best_match["similarity"] >= self.semantic_similarity_threshold:
                    logger.info(
                        f"Embedding-based detection successful - franchisor: {best_match['name']}, "
                        f"similarity: {best_match['similarity']}"
                    )
                    return (best_match["id"], best_match["similarity"])

            return None

        except Exception as e:
            logger.error(f"Error in embedding-based detection: {e}")
            return None

    async def _get_embedding(self, text: str) -> Optional[List[float]]:
        """Get OpenAI embedding for text"""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            return response.data[0].embedding

        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None

    async def _get_franchisor_embedding(self, franchisor: Dict[str, Any]) -> Optional[List[float]]:
        """Get or generate embedding for franchisor"""
        franchisor_id = franchisor["id"]

        # Check cache first
        if franchisor_id in self._franchisor_embeddings_cache:
            return self._franchisor_embeddings_cache[franchisor_id]

        # Generate new embedding
        search_text = franchisor["search_text"]
        embedding = await self._get_embedding(search_text)

        if embedding:
            self._franchisor_embeddings_cache[franchisor_id] = embedding

        return embedding

    async def _detect_using_function_calling(
        self,
        question: str,
        franchisors: List[Dict[str, Any]]
    ) -> Optional[Tuple[str, float]]:
        """
        Use OpenAI function calling to detect franchisors

        Args:
            question: The question to analyze
            franchisors: List of active franchisors

        Returns:
            Tuple of (franchisor_id, confidence) if detected, None otherwise
        """
        try:
            logger.info("Using function calling for franchisor detection")

            # Define function for franchisor lookup
            functions = [
                {
                    "type": "function",
                    "function": {
                        "name": "search_franchisors",
                        "description": "Search for franchisors based on keywords, names, or descriptions",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "search_terms": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Keywords or terms to search for in franchisor names and descriptions"
                                },
                                "confidence": {
                                    "type": "number",
                                    "minimum": 0.0,
                                    "maximum": 1.0,
                                    "description": "Confidence level (0.0-1.0) that these terms relate to a specific franchisor"
                                }
                            },
                            "required": ["search_terms", "confidence"]
                        }
                    }
                }
            ]

            system_prompt = """You are an expert at identifying franchise brands from customer questions.

Analyze the customer question and if you detect any specific franchise brand mentions, keywords, or context clues that point to a particular franchise, use the search_franchisors function to search for relevant franchisors.

Only call the function if you're reasonably confident (>0.6) that the question relates to a specific franchise brand.

Look for:
- Direct franchise name mentions
- Industry-specific terms that might relate to particular franchises
- Location or service descriptions that match known franchises
- Brand-specific terminology or keywords"""

            user_prompt = f"""Analyze this customer question for franchise brand mentions: "{question}"

If you detect any specific franchise-related terms, names, or context clues, search for relevant franchisors."""

            response = self.openai_client.chat.completions.create(
                model=self.detection_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                tools=functions,
                tool_choice="auto",
                max_tokens=500,
                temperature=0.1
            )

            # Check if tool was called
            if response.choices[0].message.tool_calls:
                tool_call = response.choices[0].message.tool_calls[0]
                if tool_call.function.name == "search_franchisors":
                    function_args = json.loads(tool_call.function.arguments)
                    search_terms = function_args.get("search_terms", [])
                    confidence = function_args.get("confidence", 0.0)

                    # Search franchisors using the extracted terms
                    matched_franchisor = self._search_franchisors_by_terms(search_terms, franchisors)

                    if matched_franchisor:
                        final_confidence = min(confidence * 0.9, 1.0)  # Slight penalty for function calling
                        logger.info(
                            f"Function calling detection successful - terms: {search_terms}, "
                            f"franchisor: {matched_franchisor['name']}, confidence: {final_confidence}"
                        )
                        return (matched_franchisor["id"], final_confidence)

            return None

        except Exception as e:
            logger.error(f"Error in function calling detection: {e}")
            return None

    def _search_franchisors_by_terms(
        self,
        search_terms: List[str],
        franchisors: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Search franchisors by terms extracted from function calling"""
        best_match = None
        best_score = 0.0

        for franchisor in franchisors:
            score = 0.0

            # Check name matches
            for term in search_terms:
                term_lower = term.lower()

                # Exact name match
                if term_lower == franchisor["name"].lower():
                    score += 1.0
                # Partial name match
                elif term_lower in franchisor["name"].lower() or franchisor["name"].lower() in term_lower:
                    score += 0.8
                # Description match
                elif term_lower in franchisor["description"].lower():
                    score += 0.6
                # Category match
                elif term_lower in franchisor["category"].lower():
                    score += 0.5
                # Keyword match
                elif any(term_lower in keyword or keyword in term_lower for keyword in franchisor["keywords"]):
                    score += 0.4

            # Normalize score by number of search terms
            if search_terms:
                score = score / len(search_terms)

            if score > best_score:
                best_match = franchisor
                best_score = score

        return best_match if best_score > 0.3 else None

    async def _detect_using_structured_output(
        self,
        question: str,
        franchisors: List[Dict[str, Any]]
    ) -> Optional[Tuple[str, float]]:
        """
        Use OpenAI structured output for consistent detection

        Args:
            question: The question to analyze
            franchisors: List of active franchisors

        Returns:
            Tuple of (franchisor_id, confidence) if detected, None otherwise
        """
        try:
            logger.info("Using structured output for franchisor detection")

            # Create a simplified franchisor list for the prompt
            franchisor_list = [
                {
                    "name": f["name"],
                    "description": f["description"][:100],
                    "category": f["category"]
                }
                for f in franchisors[:20]  # Limit to top 20 to avoid token limits
            ]

            system_prompt = """You are an expert franchise brand detector. Analyze customer questions to identify specific franchise brand mentions.

Return a JSON response with this exact structure:
{
    "detected": boolean,
    "franchisor_name": string or null,
    "confidence": number between 0.0 and 1.0,
    "reasoning": string,
    "keywords_found": array of strings
}

Rules:
1. Only detect if you're confident (>0.6) about a specific franchise brand
2. Look for direct mentions, brand-specific terms, or strong contextual clues
3. Be conservative - false negatives are better than false positives
4. Return the exact franchise name from the provided list"""

            user_prompt = f"""Question: "{question}"

Available franchisors:
{json.dumps(franchisor_list, indent=2)}

Analyze the question and detect any franchise brand mentions."""

            response = self.openai_client.chat.completions.create(
                model=self.detection_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_object"},
                max_tokens=400,
                temperature=0.1
            )

            result = json.loads(response.choices[0].message.content)

            if result.get("detected") and result.get("franchisor_name"):
                detected_name = result["franchisor_name"]
                confidence = result.get("confidence", 0.5)

                # Find matching franchisor in our list
                for franchisor in franchisors:
                    if franchisor["name"].lower() == detected_name.lower():
                        logger.info(
                            f"Structured output detection successful - franchisor: {detected_name}, "
                            f"confidence: {confidence}, keywords: {result.get('keywords_found', [])}"
                        )
                        return (franchisor["id"], confidence)

            return None

        except Exception as e:
            logger.error(f"Error in structured output detection: {e}")
            return None

    async def _combine_detection_results(
        self,
        results: List[Tuple[str, Tuple[str, float]]],
        franchisors: List[Dict[str, Any]]
    ) -> Optional[Tuple[str, float]]:
        """
        Combine results from multiple detection methods

        Args:
            results: List of (method_name, (franchisor_id, confidence)) tuples
            franchisors: List of active franchisors for reference

        Returns:
            Final (franchisor_id, confidence) tuple or None
        """
        if not results:
            return None

        # If only one result, return it
        if len(results) == 1:
            return results[0][1]

        # Multiple results - use weighted voting
        franchisor_votes = {}
        method_weights = {
            "embeddings": 1.0,      # Highest weight for semantic similarity
            "function_calling": 0.9, # High weight for structured extraction
            "structured": 0.8        # Good weight for consistent output
        }

        for method, (franchisor_id, confidence) in results:
            weight = method_weights.get(method, 0.5)
            weighted_confidence = confidence * weight

            if franchisor_id not in franchisor_votes:
                franchisor_votes[franchisor_id] = []

            franchisor_votes[franchisor_id].append({
                "method": method,
                "confidence": confidence,
                "weighted_confidence": weighted_confidence
            })

        # Calculate final scores
        final_scores = {}
        for franchisor_id, votes in franchisor_votes.items():
            # Average weighted confidence
            avg_weighted = sum(v["weighted_confidence"] for v in votes) / len(votes)
            # Bonus for multiple methods agreeing
            method_bonus = 0.1 * (len(votes) - 1)
            final_score = min(avg_weighted + method_bonus, 1.0)

            final_scores[franchisor_id] = {
                "score": final_score,
                "methods": [v["method"] for v in votes],
                "confidences": [v["confidence"] for v in votes]
            }

        # Return the best result
        if final_scores:
            best_franchisor_id = max(final_scores.keys(), key=lambda x: final_scores[x]["score"])
            best_result = final_scores[best_franchisor_id]

            logger.info(
                f"Combined detection results - franchisor: {best_franchisor_id}, "
                f"score: {best_result['score']}, methods: {best_result['methods']}, "
                f"confidences: {best_result['confidences']}"
            )

            return (best_franchisor_id, best_result["score"])

        return None
    

def get_franchisor_detection_service(session: AsyncSession) -> AdvancedFranchisorDetectionService:
    """Factory function to get advanced franchisor detection service"""
    return AdvancedFranchisorDetectionService(session)
