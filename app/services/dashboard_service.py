"""
Dashboard Service
Handles dashboard data aggregation and recent activity tracking
"""

import logging
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, text
from datetime import datetime, timedelta

from app.models.lead import Lead
from app.models.pre_qualification_question import PreQualificationQuestion as Question
from app.models.franchisor import Franchisor
from app.schemas.dashboard import (
    DashboardCountsResponse,
    RecentActivityResponse,
    RecentLeadActivity,
    RecentFranchisorActivity,
    RecentQuestionActivity,
    RecentExceptionActivity
)
from app.core.utils.exception_manager.custom_exceptions import (
    DatabaseError,
    ValidationError
)

logger = logging.getLogger(__name__)


class DashboardService:
    """Service for handling dashboard operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_dashboard_counts(self) -> DashboardCountsResponse:
        """
        Get dashboard counts for leads, franchisors, SMS, and meetings with percentage changes

        Returns:
            DashboardCountsResponse: Dashboard counts data with percentage changes
        """
        try:
            # Calculate date ranges
            now = datetime.now()
            last_month_start = (now.replace(day=1) - timedelta(days=1)).replace(day=1)
            current_month_start = now.replace(day=1)

            # Get total leads count
            leads_query = select(func.count(Lead.id)).where(Lead.is_deleted == False)
            leads_result = await self.db.execute(leads_query)
            total_leads = leads_result.scalar() or 0

            # Get leads count for last month
            leads_last_month_query = select(func.count(Lead.id)).where(
                Lead.is_deleted == False,
                Lead.created_at >= last_month_start,
                Lead.created_at < current_month_start
            )
            leads_last_month_result = await self.db.execute(leads_last_month_query)
            leads_last_month = leads_last_month_result.scalar() or 0

            # Get leads count for current month
            leads_current_month_query = select(func.count(Lead.id)).where(
                Lead.is_deleted == False,
                Lead.created_at >= current_month_start
            )
            leads_current_month_result = await self.db.execute(leads_current_month_query)
            leads_current_month = leads_current_month_result.scalar() or 0

            # Calculate leads percentage change
            leads_change_percent = self._calculate_percentage_change(leads_last_month, leads_current_month)

            # Get total franchisors count
            franchisors_query = select(func.count(Franchisor.id)).where(Franchisor.is_deleted == False)
            franchisors_result = await self.db.execute(franchisors_query)
            total_franchisors = franchisors_result.scalar() or 0

            # Get franchisors count for last month
            franchisors_last_month_query = select(func.count(Franchisor.id)).where(
                Franchisor.is_deleted == False,
                Franchisor.created_at >= last_month_start,
                Franchisor.created_at < current_month_start
            )
            franchisors_last_month_result = await self.db.execute(franchisors_last_month_query)
            franchisors_last_month = franchisors_last_month_result.scalar() or 0

            # Get franchisors count for current month
            franchisors_current_month_query = select(func.count(Franchisor.id)).where(
                Franchisor.is_deleted == False,
                Franchisor.created_at >= current_month_start
            )
            franchisors_current_month_result = await self.db.execute(franchisors_current_month_query)
            franchisors_current_month = franchisors_current_month_result.scalar() or 0

            # Calculate franchisors percentage change
            franchisors_change_percent = self._calculate_percentage_change(franchisors_last_month, franchisors_current_month)

            # Static values for SMS and meetings (as requested)
            total_sms = 0
            total_meetings = 0

            # Default percentage changes for SMS and meetings
            sms_change_percent = 2.5  # Default positive growth
            meetings_change_percent = 1.8  # Default positive growth

            logger.info(f"Dashboard counts retrieved - Leads: {total_leads} ({leads_change_percent:+.1f}%), Franchisors: {total_franchisors} ({franchisors_change_percent:+.1f}%)")

            return DashboardCountsResponse(
                total_leads=total_leads,
                total_franchisors=total_franchisors,
                total_sms=total_sms,
                total_meetings=total_meetings,
                leads_change_percent=leads_change_percent,
                franchisors_change_percent=franchisors_change_percent,
                sms_change_percent=sms_change_percent,
                meetings_change_percent=meetings_change_percent
            )
            
        except Exception as e:
            logger.error(f"Error getting dashboard counts: {e}", exc_info=True)
            raise DatabaseError(
                error_key="DASHBOARD_COUNTS_FAILED",
                message="Failed to retrieve dashboard counts"
            )
    
    async def get_recent_activity(self) -> RecentActivityResponse:
        """
        Get recent activity including latest lead, franchisor, question, and exception
        
        Returns:
            RecentActivityResponse: Recent activity data
        """
        try:
            # Get latest lead
            latest_lead = await self._get_latest_lead()
            
            # Get latest franchisor
            latest_franchisor = await self._get_latest_franchisor()
            
            # Get latest question
            latest_question = await self._get_latest_question()
            
            # Get latest exception (mock data for now)
            latest_exception = await self._get_latest_exception()
            
            logger.info("Recent activity retrieved successfully")
            
            return RecentActivityResponse(
                latest_lead=latest_lead,
                latest_franchisor=latest_franchisor,
                latest_question=latest_question,
                latest_exception=latest_exception
            )
            
        except Exception as e:
            logger.error(f"Error getting recent activity: {e}", exc_info=True)
            raise DatabaseError(
                error_key="RECENT_ACTIVITY_FAILED",
                message="Failed to retrieve recent activity"
            )
    
    async def _get_latest_lead(self) -> Optional[RecentLeadActivity]:
        """Get the latest lead"""
        try:
            query = (
                select(Lead)
                .where(Lead.is_deleted == False)
                .order_by(desc(Lead.created_at))
                .limit(1)
            )
            result = await self.db.execute(query)
            lead = result.scalar_one_or_none()
            
            if lead:
                return RecentLeadActivity(
                    id=str(lead.id),
                    full_name=lead.full_name,
                    email=lead.email,
                    phone=lead.phone,
                    status=lead.status,
                    created_at=lead.created_at
                )
            return None
            
        except Exception as e:
            logger.error(f"Error getting latest lead: {e}")
            return None
    
    async def _get_latest_franchisor(self) -> Optional[RecentFranchisorActivity]:
        """Get the latest franchisor"""
        try:
            query = (
                select(Franchisor)
                .where(Franchisor.is_deleted == False)
                .order_by(desc(Franchisor.created_at))
                .limit(1)
            )
            result = await self.db.execute(query)
            franchisor = result.scalar_one_or_none()
            
            if franchisor:
                return RecentFranchisorActivity(
                    id=str(franchisor.id),
                    name=franchisor.name,
                    contactFirstName=franchisor.contactfirstname,
                    contactLastName=franchisor.contactlastname,
                    email=franchisor.email,
                    region=franchisor.region,
                    is_active=franchisor.is_active,
                    created_at=franchisor.created_at
                )
            return None
            
        except Exception as e:
            logger.error(f"Error getting latest franchisor: {e}")
            return None
    
    async def _get_latest_question(self) -> Optional[RecentQuestionActivity]:
        """Get the latest question"""
        try:
            query = (
                select(Question)
                .where(Question.is_deleted == False)
                .order_by(desc(Question.created_at))
                .limit(1)
            )
            result = await self.db.execute(query)
            question = result.scalar_one_or_none()

            if question:
                return RecentQuestionActivity(
                    id=str(question.id),
                    question_text=question.question_text,
                    category="General",  # Questions don't have category field
                    is_active=question.is_active,
                    created_at=question.created_at
                )
            return None

        except Exception as e:
            logger.error(f"Error getting latest question: {e}")
            return None
    
    async def _get_latest_exception(self) -> Optional[RecentExceptionActivity]:
        """Get the latest exception (mock data for now)"""
        try:
            # For now, return mock exception data
            # In the future, this could be connected to a logging/monitoring system
            return RecentExceptionActivity(
                id="mock-exception-id",
                error_type="ValidationError",
                error_message="Sample exception for dashboard display",
                endpoint="/api/sample/",
                user_id=None,
                severity="info",
                created_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting latest exception: {e}")
            return None

    def _calculate_percentage_change(self, last_month_count: int, current_month_count: int) -> float:
        """
        Calculate percentage change between last month and current month

        Args:
            last_month_count: Count from last month
            current_month_count: Count from current month

        Returns:
            Percentage change (positive for growth, negative for decline)
        """
        if last_month_count == 0:
            # If no data last month, return 100% if there's current data, 0% if no current data
            return 100.0 if current_month_count > 0 else 0.0

        # Calculate percentage change: ((current - last) / last) * 100
        change = ((current_month_count - last_month_count) / last_month_count) * 100
        return round(change, 1)
