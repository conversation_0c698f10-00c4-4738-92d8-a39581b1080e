"""
Analytics Cache Service
Simple in-memory caching for analytics data to improve performance
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with data and expiration"""
    data: Any
    expires_at: datetime
    created_at: datetime


class AnalyticsCache:
    """Simple in-memory cache for analytics data"""
    
    def __init__(self, default_ttl_minutes: int = 5):
        self.cache: Dict[str, CacheEntry] = {}
        self.default_ttl = timedelta(minutes=default_ttl_minutes)
        self._lock = asyncio.Lock()
    
    def _generate_cache_key(self, method_name: str, **kwargs) -> str:
        """Generate cache key from method name and parameters"""
        # Sort kwargs for consistent key generation
        sorted_params = sorted(kwargs.items())
        params_str = "_".join([f"{k}:{v}" for k, v in sorted_params])
        return f"{method_name}_{params_str}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get cached data if not expired"""
        async with self._lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if datetime.now() > entry.expires_at:
                del self.cache[key]
                logger.debug(f"Cache entry expired and removed: {key}")
                return None
            
            logger.debug(f"Cache hit: {key}")
            return entry.data
    
    async def set(self, key: str, data: Any, ttl: Optional[timedelta] = None) -> None:
        """Set cached data with TTL"""
        if ttl is None:
            ttl = self.default_ttl
        
        expires_at = datetime.now() + ttl
        
        async with self._lock:
            self.cache[key] = CacheEntry(
                data=data,
                expires_at=expires_at,
                created_at=datetime.now()
            )
            logger.debug(f"Cache set: {key} (expires: {expires_at})")
    
    async def invalidate(self, pattern: Optional[str] = None) -> int:
        """Invalidate cache entries matching pattern or all if no pattern"""
        async with self._lock:
            if pattern is None:
                # Clear all cache
                count = len(self.cache)
                self.cache.clear()
                logger.info(f"Cleared all cache entries: {count}")
                return count
            
            # Clear entries matching pattern
            keys_to_remove = [key for key in self.cache.keys() if pattern in key]
            for key in keys_to_remove:
                del self.cache[key]
            
            logger.info(f"Cleared cache entries matching '{pattern}': {len(keys_to_remove)}")
            return len(keys_to_remove)
    
    async def cleanup_expired(self) -> int:
        """Remove expired cache entries"""
        now = datetime.now()
        async with self._lock:
            expired_keys = [
                key for key, entry in self.cache.items() 
                if now > entry.expires_at
            ]
            
            for key in expired_keys:
                del self.cache[key]
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
            
            return len(expired_keys)
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        async with self._lock:
            now = datetime.now()
            total_entries = len(self.cache)
            expired_entries = sum(
                1 for entry in self.cache.values() 
                if now > entry.expires_at
            )
            active_entries = total_entries - expired_entries
            
            # Calculate cache ages
            if self.cache:
                oldest_entry = min(entry.created_at for entry in self.cache.values())
                newest_entry = max(entry.created_at for entry in self.cache.values())
                oldest_age = (now - oldest_entry).total_seconds()
                newest_age = (now - newest_entry).total_seconds()
            else:
                oldest_age = newest_age = 0
            
            return {
                "total_entries": total_entries,
                "active_entries": active_entries,
                "expired_entries": expired_entries,
                "oldest_entry_age_seconds": oldest_age,
                "newest_entry_age_seconds": newest_age,
                "default_ttl_minutes": self.default_ttl.total_seconds() / 60,
                "cache_keys": list(self.cache.keys())
            }


# Global cache instance
analytics_cache = AnalyticsCache(default_ttl_minutes=5)


def cache_analytics_result(ttl_minutes: int = 5):
    """Decorator to cache analytics method results"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = analytics_cache._generate_cache_key(
                func.__name__, 
                **{k: str(v) for k, v in kwargs.items()}
            )
            
            # Try to get from cache
            cached_result = await analytics_cache.get(cache_key)
            if cached_result is not None:
                logger.info(f"Analytics cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            logger.info(f"Analytics cache miss for {func.__name__}, executing...")
            start_time = datetime.now()
            
            try:
                result = await func(*args, **kwargs)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                # Cache the result
                await analytics_cache.set(
                    cache_key, 
                    result, 
                    ttl=timedelta(minutes=ttl_minutes)
                )
                
                logger.info(f"Analytics method {func.__name__} executed in {execution_time:.2f}s and cached")
                return result
                
            except Exception as e:
                logger.error(f"Analytics method {func.__name__} failed: {e}")
                raise
        
        return wrapper
    return decorator


async def invalidate_analytics_cache():
    """Invalidate all analytics cache entries"""
    return await analytics_cache.invalidate()


async def cleanup_analytics_cache():
    """Clean up expired analytics cache entries"""
    return await analytics_cache.cleanup_expired()


async def get_analytics_cache_stats():
    """Get analytics cache statistics"""
    return await analytics_cache.get_cache_stats()


# Background task to periodically clean up expired cache entries
async def cache_cleanup_task():
    """Background task to clean up expired cache entries every 10 minutes"""
    while True:
        try:
            await asyncio.sleep(600)  # 10 minutes
            cleaned = await cleanup_analytics_cache()
            if cleaned > 0:
                logger.info(f"Background cache cleanup removed {cleaned} expired entries")
        except Exception as e:
            logger.error(f"Cache cleanup task error: {e}")


# Start the background cleanup task when the module is imported
# Note: This should be started in the main application startup
def start_cache_cleanup_task():
    """Start the background cache cleanup task"""
    asyncio.create_task(cache_cleanup_task())
