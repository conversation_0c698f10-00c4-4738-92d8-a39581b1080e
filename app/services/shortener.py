"""
Short Link Service
Handles creation and management of branded short links (ghv.li)
"""

import random
import string
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from app.models.short_link import ShortLink
from app.core.config.settings import settings
from app.core.logging import logger


class ShortenerService:
    """Service for creating and managing short links"""
    
    # Base62 character set for URL-safe slugs
    BASE62_CHARS = string.ascii_letters + string.digits
    
    def __init__(self):
        self.base_url = settings.SHORTLINK_BASE_URL
        self.domain = settings.SHORTLINK_DOMAIN
    
    def _generate_slug(self, length: int = 8) -> str:
        """
        Generate a random base62 slug
        
        Args:
            length: Length of the slug (default 8, range 8-12)
            
        Returns:
            Random base62 string
        """
        return ''.join(random.choices(self.BASE62_CHARS, k=length))
    
    async def _is_slug_available(self, db: AsyncSession, slug: str) -> bool:
        """
        Check if a slug is available (not already in use)
        
        Args:
            db: Database session
            slug: Slug to check
            
        Returns:
            True if available, False if taken
        """
        try:
            stmt = select(ShortLink).where(ShortLink.slug == slug)
            result = await db.execute(stmt)
            existing_link = result.scalar_one_or_none()
            return existing_link is None
        except Exception as e:
            logger.error(f"Error checking slug availability: {str(e)}")
            return False
    
    async def _generate_unique_slug(self, db: AsyncSession, min_length: int = 8, max_length: int = 12) -> str:
        """
        Generate a unique slug with collision checking
        
        Args:
            db: Database session
            min_length: Minimum slug length
            max_length: Maximum slug length
            
        Returns:
            Unique slug
        """
        max_attempts = 100  # Prevent infinite loops
        
        for length in range(min_length, max_length + 1):
            for attempt in range(max_attempts):
                slug = self._generate_slug(length)
                if await self._is_slug_available(db, slug):
                    logger.info(f"Generated unique slug: {slug} (length: {length})")
                    return slug
            
            logger.warning(f"Could not generate unique slug of length {length} after {max_attempts} attempts")
        
        # Fallback: use timestamp-based slug
        timestamp = str(int(datetime.utcnow().timestamp()))
        fallback_slug = f"t{timestamp[-7:]}"  # Last 7 digits of timestamp
        logger.warning(f"Using fallback timestamp-based slug: {fallback_slug}")
        return fallback_slug
    
    async def create_short_link(
        self,
        db: AsyncSession,
        long_url: str,
        context: Optional[Dict[str, Any]] = None,
        expires_at: Optional[datetime] = None,
        custom_slug: Optional[str] = None
    ) -> str:
        """
        Create a short link
        
        Args:
            db: Database session
            long_url: The original URL to shorten
            context: Optional context metadata
            expires_at: Optional expiration date
            custom_slug: Optional custom slug (must be unique)
            
        Returns:
            Short URL (e.g., "https://ghv.li/r/abc123")
            
        Raises:
            ValueError: If custom slug is already taken
            Exception: For other database errors
        """
        try:
            # Validate URL
            if not long_url or not long_url.strip():
                raise ValueError("Long URL cannot be empty")
            
            # Clean and validate URL
            long_url = long_url.strip()
            if not long_url.startswith(('http://', 'https://')):
                long_url = f"https://{long_url}"
            
            # Generate or validate slug
            if custom_slug:
                # Validate custom slug
                if not custom_slug.isalnum() or len(custom_slug) < 3 or len(custom_slug) > 16:
                    raise ValueError("Custom slug must be 3-16 alphanumeric characters")
                
                if not await self._is_slug_available(db, custom_slug):
                    raise ValueError(f"Custom slug '{custom_slug}' is already taken")
                
                slug = custom_slug
            else:
                slug = await self._generate_unique_slug(db)
            
            # Create short link record
            short_link = ShortLink(
                slug=slug,
                long_url=long_url,
                context_json=context or {},
                expires_at=expires_at
            )
            
            db.add(short_link)
            await db.commit()
            await db.refresh(short_link)
            
            # Generate short URL
            short_url = f"{self.base_url}/r/{slug}"
            
            logger.info(f"Created short link: {short_url} -> {long_url}")
            logger.info(f"Short link context: {context}")
            
            return short_url
            
        except IntegrityError as e:
            await db.rollback()
            if "slug" in str(e):
                raise ValueError(f"Slug '{slug}' is already taken")
            raise Exception(f"Database integrity error: {str(e)}")
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating short link: {str(e)}")
            raise
    
    async def get_short_link(self, db: AsyncSession, slug: str) -> Optional[ShortLink]:
        """
        Get a short link by slug
        
        Args:
            db: Database session
            slug: The slug to look up
            
        Returns:
            ShortLink object or None if not found
        """
        try:
            stmt = select(ShortLink).where(ShortLink.slug == slug)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error retrieving short link: {str(e)}")
            return None
    
    async def resolve_short_link(self, db: AsyncSession, slug: str) -> Optional[str]:
        """
        Resolve a short link to its long URL
        
        Args:
            db: Database session
            slug: The slug to resolve
            
        Returns:
            Long URL or None if not found/expired
        """
        try:
            short_link = await self.get_short_link(db, slug)
            
            if not short_link:
                logger.info(f"Short link not found: {slug}")
                return None
            
            if short_link.is_expired():
                logger.info(f"Short link expired: {slug}")
                return None
            
            # Log click event (optional)
            logger.info(f"Short link clicked: {slug} -> {short_link.long_url}")
            
            return short_link.long_url
            
        except Exception as e:
            logger.error(f"Error resolving short link: {str(e)}")
            return None
    
    async def delete_short_link(self, db: AsyncSession, slug: str) -> bool:
        """
        Delete a short link
        
        Args:
            db: Database session
            slug: The slug to delete
            
        Returns:
            True if deleted, False if not found
        """
        try:
            stmt = select(ShortLink).where(ShortLink.slug == slug)
            result = await db.execute(stmt)
            short_link = result.scalar_one_or_none()
            
            if not short_link:
                return False
            
            await db.delete(short_link)
            await db.commit()
            
            logger.info(f"Deleted short link: {slug}")
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting short link: {str(e)}")
            return False
    
    async def cleanup_expired_links(self, db: AsyncSession) -> int:
        """
        Clean up expired short links
        
        Args:
            db: Database session
            
        Returns:
            Number of links deleted
        """
        try:
            now = datetime.utcnow()
            stmt = select(ShortLink).where(ShortLink.expires_at < now)
            result = await db.execute(stmt)
            expired_links = result.scalars().all()
            
            count = 0
            for link in expired_links:
                await db.delete(link)
                count += 1
            
            await db.commit()
            
            if count > 0:
                logger.info(f"Cleaned up {count} expired short links")
            
            return count
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error cleaning up expired links: {str(e)}")
            return 0


# Global service instance
shortener_service = ShortenerService()


# Convenience functions
async def create_short_link(
    db: AsyncSession,
    long_url: str,
    context: Optional[Dict[str, Any]] = None,
    expires_at: Optional[datetime] = None,
    custom_slug: Optional[str] = None
) -> str:
    """Create a short link - convenience function"""
    return await shortener_service.create_short_link(db, long_url, context, expires_at, custom_slug)


async def resolve_short_link(db: AsyncSession, slug: str) -> Optional[str]:
    """Resolve a short link - convenience function"""
    return await shortener_service.resolve_short_link(db, slug)
