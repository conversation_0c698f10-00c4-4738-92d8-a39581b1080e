"""
Objection Handling Service
Comprehensive objection detection and response system for the Coochie Hydrogreen workflow.
Uses exact phrasing from the workflow document and OpenAI for intelligent objection detection.
"""

import os
import json
from typing import Dict, Any, Optional, List, Tuple
import structlog
from app.services.openai_service import openai_service

from app.core.logging import logger


class ObjectionType:
    """Objection types from the workflow"""
    NO_VALUE = "no_value"
    NOT_HEARD = "not_heard"
    MARKETING = "marketing"
    EXPERIENCE = "experience"
    INCOME = "income"
    INCOME_GUARANTEE = "income_guarantee"
    ROYALTY = "royalty"
    GENERAL = "general"


class ObjectionHandlingService:
    """
    Service for detecting and handling objections using exact workflow responses
    """
    
    def __init__(self):
        # Exact objection responses from the workflow document
        self.objection_responses = {
            ObjectionType.NO_VALUE: "I understand however we are yet to walk you through the business potential. There is a reason we're Australia's largest lawn care company. Let's walk you through all on the call.",
            ObjectionType.NOT_HEARD: "Fair enough. Coochie has been operating for over 30 years and is a very prominent brand in the NSW & QLD and now we're looking to grow in other states due to demand.",
            ObjectionType.MARKETING: "We will provide you lead guarantee during the initial period along with income guarantee in the first year. However the expectation is you are also conducting business development activities in your local area.",
            ObjectionType.EXPERIENCE: "We will provide you comprehensive training for 4 weeks but also you will receive ongoing support. You will also get to spend time with our existing franchise partners. Rest assured, you won't be left alone.",
            ObjectionType.INCOME: "A totally fair question. Some of our franchise partners, who follow our systems and processes very well, earn over $200K net, but as always, your results will depend on your effort and local conditions. We'll go through the 3-year projections together in our meeting so you can see what's possible.",
            ObjectionType.INCOME_GUARANTEE: "In the first year, we guarantee $60K net income - that is after all your expenses.",
            ObjectionType.ROYALTY: "10% royalty and 3% marketing fund so total is 13% of your gross sales.",
            ObjectionType.GENERAL: "Does that make sense? I'm happy to answer any questions you may have."
        }
        
        # Objection detection patterns
        self.objection_patterns = {
            ObjectionType.NO_VALUE: [
                "don't see value", "too expensive", "not worth it", "overpriced", 
                "costs too much", "can't afford", "price is high", "expensive",
                "not seeing the value", "doesn't seem worth", "too costly"
            ],
            ObjectionType.NOT_HEARD: [
                "haven't heard", "don't know you", "never seen", "not familiar",
                "never heard of", "unknown company", "don't recognize", 
                "not aware of", "first time hearing", "unfamiliar"
            ],
            ObjectionType.MARKETING: [
                "marketing", "lead generation", "customers", "advertising",
                "how do I get customers", "where are the leads", "marketing support",
                "customer acquisition", "business development", "sales leads"
            ],
            ObjectionType.EXPERIENCE: [
                "no experience", "don't know how", "never done", "inexperienced",
                "new to this", "don't have background", "never run business",
                "lack experience", "unfamiliar with", "don't understand"
            ],
            ObjectionType.INCOME: [
                "income", "money", "earnings", "profit", "revenue", "financial",
                "how much money", "what's the income", "earning potential",
                "financial returns", "profitability", "make money"
            ],
            ObjectionType.INCOME_GUARANTEE: [
                "income guarantee", "guaranteed income", "guarantee earnings",
                "guaranteed profit", "income protection", "financial guarantee"
            ],
            ObjectionType.ROYALTY: [
                "royalty", "fees", "ongoing costs", "monthly fees", "royalty fee",
                "ongoing payments", "franchise fees", "recurring costs",
                "what do I pay", "ongoing expenses"
            ]
        }
        
        # Follow-up responses for when objections are answered
        self.followup_responses = {
            "general_followup": "Does that make sense? I'm happy to answer any questions you may have.",
            "silent_check": "Just checking if you're still keen to explore this opportunity? Happy to keep the chat going and set up a quick call to walk you through the details. How does that sound?",
            "not_interested_reason": "Any particular reason you wouldn't like to continue?"
        }
    
    async def detect_objection(self, message: str) -> Dict[str, Any]:
        """
        Detect objection type from user message using both pattern matching and OpenAI
        
        Args:
            message: User message to analyze
            
        Returns:
            Dict with objection analysis results
        """
        try:
            # First try pattern matching for quick detection
            pattern_result = self._pattern_match_objection(message)
            
            # Use OpenAI for more sophisticated analysis
            ai_result = await self._ai_analyze_objection(message)
            
            # Combine results with AI taking precedence
            objection_type = ai_result.get("type") or pattern_result.get("type")
            confidence = ai_result.get("confidence", pattern_result.get("confidence", 0.5))
            
            result = {
                "type": objection_type,
                "confidence": confidence,
                "detected_by": "ai" if ai_result.get("type") else "pattern",
                "message_analyzed": message[:100] + "..." if len(message) > 100 else message,
                "pattern_match": pattern_result,
                "ai_analysis": ai_result
            }
            
            # Log objection detection (no PII)
            logger.info(
                "Objection detected",
                objection_type=objection_type,
                confidence=confidence,
                detection_method=result["detected_by"],
                message_length=len(message)
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error detecting objection: {str(e)}")
            return {
                "type": None,
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _pattern_match_objection(self, message: str) -> Dict[str, Any]:
        """
        Pattern match objections using keyword detection
        
        Args:
            message: Message to analyze
            
        Returns:
            Dict with pattern matching results
        """
        message_lower = message.lower()
        
        # Score each objection type
        scores = {}
        for objection_type, patterns in self.objection_patterns.items():
            score = 0
            matched_patterns = []
            
            for pattern in patterns:
                if pattern in message_lower:
                    score += 1
                    matched_patterns.append(pattern)
            
            if score > 0:
                scores[objection_type] = {
                    "score": score,
                    "matched_patterns": matched_patterns,
                    "confidence": min(score * 0.3, 1.0)  # Cap at 1.0
                }
        
        # Return highest scoring objection
        if scores:
            best_objection = max(scores.keys(), key=lambda x: scores[x]["score"])
            return {
                "type": best_objection,
                "confidence": scores[best_objection]["confidence"],
                "matched_patterns": scores[best_objection]["matched_patterns"],
                "all_scores": scores
            }
        
        return {"type": None, "confidence": 0.0}
    
    async def _ai_analyze_objection(self, message: str) -> Dict[str, Any]:
        """
        Use OpenAI to analyze objections with more sophistication
        
        Args:
            message: Message to analyze
            
        Returns:
            Dict with AI analysis results
        """
        try:
            objection_types_list = list(self.objection_patterns.keys())
            
            prompt = f"""
            Analyze this message for objections related to franchise opportunities:
            Message: "{message}"
            
            Possible objection types:
            - no_value: Concerns about value, price, or cost
            - not_heard: Unfamiliarity with the brand or company
            - marketing: Questions about customer acquisition or marketing
            - experience: Concerns about lack of experience or knowledge
            - income: Questions about earnings or financial returns
            - income_guarantee: Specific questions about income guarantees
            - royalty: Questions about fees, royalties, or ongoing costs
            
            Return JSON with:
            - "type": objection type (or null if no clear objection)
            - "confidence": confidence score 0.0-1.0
            - "reasoning": brief explanation
            """
            
            response = await openai_service.get_completion(
                prompt,
                model="gpt-4",
                temperature=0.1,  # Low temperature for consistent analysis
                max_tokens=200
            )
            
            # Parse JSON response
            try:
                result = json.loads(response)
                return {
                    "type": result.get("type"),
                    "confidence": result.get("confidence", 0.5),
                    "reasoning": result.get("reasoning", ""),
                    "raw_response": response
                }
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse AI objection analysis: {response}")
                return {"type": None, "confidence": 0.0, "error": "JSON parse error"}
                
        except Exception as e:
            logger.error(f"AI objection analysis failed: {str(e)}")
            return {"type": None, "confidence": 0.0, "error": str(e)}
    
    def get_objection_response(self, objection_type: str) -> str:
        """
        Get exact workflow response for objection type
        
        Args:
            objection_type: Type of objection
            
        Returns:
            Exact response from workflow
        """
        return self.objection_responses.get(objection_type, self.objection_responses[ObjectionType.GENERAL])
    
    async def handle_objection(
        self, 
        message: str, 
        current_stage: str = "general",
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Complete objection handling: detect and respond
        
        Args:
            message: User message with potential objection
            current_stage: Current conversation stage
            context: Additional context
            
        Returns:
            Dict with objection handling results
        """
        try:
            # Detect objection
            detection_result = await self.detect_objection(message)
            objection_type = detection_result.get("type")
            
            # Get appropriate response
            if objection_type:
                response = self.get_objection_response(objection_type)
                
                # Enhance response with natural conversation if needed
                from app.services.coochie_conversation_service import coochie_conversation_service
                
                enhanced_response = await coochie_conversation_service.enhance_objection_response(
                    user_objection=message,
                    workflow_objection_response=response
                )
                
                result = {
                    "objection_detected": True,
                    "objection_type": objection_type,
                    "confidence": detection_result.get("confidence", 0.5),
                    "response": enhanced_response,
                    "exact_workflow_response": response,
                    "stage": current_stage,
                    "next_stage": current_stage,  # Stay in same stage after objection
                    "awaiting_response": True,
                    "detection_details": detection_result
                }
                
            else:
                # No specific objection detected - use general response
                acknowledgment = await self._generate_acknowledgment(message)
                general_response = f"{acknowledgment}. {self.followup_responses['general_followup']}"
                
                result = {
                    "objection_detected": False,
                    "objection_type": None,
                    "response": general_response,
                    "stage": current_stage,
                    "next_stage": current_stage,
                    "awaiting_response": True,
                    "detection_details": detection_result
                }
            
            # Log objection handling (no PII)
            logger.info(
                "Objection handled",
                objection_detected=result["objection_detected"],
                objection_type=result.get("objection_type"),
                stage=current_stage,
                response_length=len(result["response"])
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error handling objection: {str(e)}")
            return {
                "objection_detected": False,
                "error": str(e),
                "response": "I understand your concern. Let me help clarify that for you.",
                "stage": current_stage,
                "next_stage": current_stage,
                "awaiting_response": True
            }
    
    async def _generate_acknowledgment(self, message: str) -> str:
        """Generate acknowledgment for non-objection messages"""
        try:
            from app.services.coochie_conversation_service import coochie_conversation_service
            return await coochie_conversation_service.generate_acknowledgment(message)
        except:
            return "I understand"
    
    def get_objection_statistics(self) -> Dict[str, Any]:
        """Get statistics about objection types and responses"""
        return {
            "total_objection_types": len(self.objection_responses),
            "objection_types": list(self.objection_responses.keys()),
            "pattern_counts": {
                obj_type: len(patterns) 
                for obj_type, patterns in self.objection_patterns.items()
            },
            "total_patterns": sum(len(patterns) for patterns in self.objection_patterns.values())
        }


# Global service instance
objection_handling_service = ObjectionHandlingService()


# Export main components
__all__ = [
    "ObjectionHandlingService",
    "ObjectionType",
    "objection_handling_service"
]
