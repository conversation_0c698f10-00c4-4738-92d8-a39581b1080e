"""
Zoho Sync Tracker Service
Manages sync status and tracking for Zoho synchronization
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, update
from sqlalchemy.orm import selectinload

from app.models.lead import Lead
from app.models.franchisor import Franchisor

logger = logging.getLogger(__name__)


class ZohoSyncTracker:
    """Service for tracking Zoho sync status and managing sync operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_leads_needing_sync(self, last_sync_time: Optional[datetime] = None, limit: int = 100) -> List[Lead]:
        """Get leads that need to be synced to Zoho based on update timestamp"""
        try:
            # Get leads that:
            # 1. Don't have Zoho ID (new records)
            # 2. OR have Zoho ID and were updated after last sync time
            conditions = [
                Lead.is_deleted == False,
                Lead.is_active == True
            ]

            if last_sync_time:
                # Include new records OR updated records
                conditions.append(
                    or_(
                        Lead.zoho_lead_id.is_(None),  # New records
                        and_(
                            Lead.zoho_lead_id.isnot(None),  # Existing records
                            Lead.updated_at > last_sync_time  # Updated since last sync
                        )
                    )
                )
            else:
                # If no last sync time, get all active records
                pass

            query = select(Lead).where(and_(*conditions)).limit(limit)
            result = await self.db.execute(query)
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"Error getting leads needing sync: {e}")
            raise
    
    async def get_franchisors_needing_sync(self, limit: int = 100) -> List[Franchisor]:
        """Get franchisors that need to be synced to Zoho (new records only, as franchisors are pull-only)"""
        try:
            # Only get new franchisors without Zoho ID
            # Franchisors are pull-only, so we don't push updates
            query = select(Franchisor).where(
                and_(
                    Franchisor.is_deleted == False,
                    Franchisor.is_active == True,
                    Franchisor.zoho_won_id.is_(None)
                )
            ).limit(limit)
            
            result = await self.db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting franchisors needing sync: {e}")
            raise
    
    async def mark_lead_sync_success(self, lead_id: str, zoho_lead_id: str) -> bool:
        """Mark a lead as successfully synced to Zoho"""
        try:
            stmt = update(Lead).where(Lead.id == lead_id).values(
                zoho_lead_id=zoho_lead_id
            )

            await self.db.execute(stmt)
            await self.db.commit()

            logger.info(f"Marked lead {lead_id} as synced with Zoho ID {zoho_lead_id}")
            return True

        except Exception as e:
            logger.error(f"Error marking lead sync success: {e}")
            await self.db.rollback()
            return False
    
    async def mark_lead_sync_failed(self, lead_id: str, error_message: str = None) -> bool:
        """Mark a lead sync as failed (log only, no DB changes)"""
        try:
            logger.warning(f"Lead {lead_id} sync failed: {error_message}")
            return True

        except Exception as e:
            logger.error(f"Error logging lead sync failure: {e}")
            return False
    
    async def mark_franchisor_sync_success(self, franchisor_id: str, zoho_won_id: str) -> bool:
        """Mark a franchisor as successfully synced to Zoho"""
        try:
            stmt = update(Franchisor).where(Franchisor.id == franchisor_id).values(
                zoho_won_id=zoho_won_id
            )

            await self.db.execute(stmt)
            await self.db.commit()

            logger.info(f"Marked franchisor {franchisor_id} as synced with Zoho ID {zoho_won_id}")
            return True

        except Exception as e:
            logger.error(f"Error marking franchisor sync success: {e}")
            await self.db.rollback()
            return False
    
    async def mark_franchisor_sync_failed(self, franchisor_id: str, error_message: str = None) -> bool:
        """Mark a franchisor sync as failed"""
        try:
            stmt = update(Franchisor).where(Franchisor.id == franchisor_id).values(
                zoho_sync_status='failed',
                updated_at=func.now()
            )
            
            await self.db.execute(stmt)
            await self.db.commit()
            
            logger.warning(f"Marked franchisor {franchisor_id} sync as failed: {error_message}")
            return True
            
        except Exception as e:
            logger.error(f"Error marking franchisor sync failed: {e}")
            await self.db.rollback()
            return False
    
    async def reset_failed_syncs(self, max_age_hours: int = 24) -> Tuple[int, int]:
        """Reset failed syncs (simplified without database tracking)"""
        try:
            logger.info(f"Reset failed syncs called (no database tracking implemented)")
            return 0, 0

        except Exception as e:
            logger.error(f"Error resetting failed syncs: {e}")
            return 0, 0
    
    async def get_sync_statistics(self) -> dict:
        """Get sync statistics for monitoring (simplified without detailed tracking)"""
        try:
            # Lead statistics
            lead_total_query = select(func.count(Lead.id)).where(Lead.is_deleted == False)
            lead_synced_query = select(func.count(Lead.id)).where(
                and_(Lead.is_deleted == False, Lead.zoho_lead_id.isnot(None))
            )

            # Franchisor statistics
            franchisor_total_query = select(func.count(Franchisor.id)).where(Franchisor.is_deleted == False)
            franchisor_synced_query = select(func.count(Franchisor.id)).where(
                and_(Franchisor.is_deleted == False, Franchisor.zoho_won_id.isnot(None))
            )

            # Execute queries
            lead_total = (await self.db.execute(lead_total_query)).scalar() or 0
            lead_synced = (await self.db.execute(lead_synced_query)).scalar() or 0

            franchisor_total = (await self.db.execute(franchisor_total_query)).scalar() or 0
            franchisor_synced = (await self.db.execute(franchisor_synced_query)).scalar() or 0

            return {
                "leads": {
                    "total": lead_total,
                    "synced": lead_synced,
                    "pending": 0,  # Not tracked without DB changes
                    "failed": 0,   # Not tracked without DB changes
                    "sync_rate": round((lead_synced / lead_total * 100) if lead_total > 0 else 0, 2)
                },
                "franchisors": {
                    "total": franchisor_total,
                    "synced": franchisor_synced,
                    "pending": 0,  # Not tracked without DB changes
                    "failed": 0,   # Not tracked without DB changes
                    "sync_rate": round((franchisor_synced / franchisor_total * 100) if franchisor_total > 0 else 0, 2)
                }
            }

        except Exception as e:
            logger.error(f"Error getting sync statistics: {e}")
            return {
                "leads": {"total": 0, "synced": 0, "pending": 0, "failed": 0, "sync_rate": 0},
                "franchisors": {"total": 0, "synced": 0, "pending": 0, "failed": 0, "sync_rate": 0}
            }
