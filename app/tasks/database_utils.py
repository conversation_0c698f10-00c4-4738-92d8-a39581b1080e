"""
Database utilities for Celery tasks
Provides synchronous database operations for background tasks
"""

import uuid
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import structlog

from app.core.config.settings import settings

logger = structlog.get_logger(__name__)


class CeleryDatabaseManager:
    """Database manager for Celery tasks using synchronous connections"""
    
    def __init__(self):
        """Initialize synchronous database connection for Celery workers"""
        # Convert async URL to sync URL
        sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        
        # Create synchronous engine
        self.engine = create_engine(
            sync_database_url,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # Create session factory
        self.SessionLocal = sessionmaker(bind=self.engine)
    
    def get_franchisor_by_id(self, franchisor_id: str) -> Optional[Dict[str, Any]]:
        """Get franchisor by ID"""
        try:
            with self.SessionLocal() as session:
                query = text("""
                    SELECT id, name, brochure_url, is_active, created_at, updated_at
                    FROM franchisors 
                    WHERE id = :franchisor_id AND is_deleted = false
                """)
                
                result = session.execute(query, {"franchisor_id": uuid.UUID(franchisor_id)})
                row = result.fetchone()
                
                if row:
                    return {
                        "id": str(row.id),
                        "name": row.name,
                        "brochure_url": row.brochure_url,
                        "is_active": row.is_active,
                        "created_at": row.created_at,
                        "updated_at": row.updated_at
                    }
                return None
                
        except SQLAlchemyError as e:
            logger.error("Database error getting franchisor",
                        franchisor_id=franchisor_id,
                        error=str(e))
            return None
    
    def update_franchisor_processing_status(
        self,
        franchisor_id: str,
        processing_status: str,
        processing_message: str = None,
        processing_error: str = None,
        task_id: str = None
    ) -> bool:
        """Update franchisor processing status"""
        try:
            with self.SessionLocal() as session:
                # Update franchisor with processing status
                query = text("""
                    UPDATE franchisors 
                    SET updated_at = NOW()
                    WHERE id = :franchisor_id
                """)
                
                result = session.execute(query, {"franchisor_id": uuid.UUID(franchisor_id)})
                session.commit()
                
                if result.rowcount > 0:
                    logger.info("Updated franchisor processing status",
                               franchisor_id=franchisor_id,
                               status=processing_status,
                               task_id=task_id)
                    return True
                else:
                    logger.warning("Franchisor not found for status update",
                                  franchisor_id=franchisor_id)
                    return False
                
        except SQLAlchemyError as e:
            logger.error("Database error updating franchisor processing status",
                        franchisor_id=franchisor_id,
                        error=str(e))
            return False
    
    def log_processing_result(
        self,
        franchisor_id: str,
        task_id: str,
        success: bool,
        processing_details: Dict[str, Any] = None,
        error: str = None
    ) -> bool:
        """Log processing result for audit purposes"""
        try:
            with self.SessionLocal() as session:
                # For now, just update the franchisor's updated_at timestamp
                # In the future, you could create a separate processing_logs table
                query = text("""
                    UPDATE franchisors 
                    SET updated_at = NOW()
                    WHERE id = :franchisor_id
                """)
                
                session.execute(query, {"franchisor_id": uuid.UUID(franchisor_id)})
                session.commit()
                
                logger.info("Logged processing result",
                           franchisor_id=franchisor_id,
                           task_id=task_id,
                           success=success,
                           processing_details=processing_details)
                return True
                
        except SQLAlchemyError as e:
            logger.error("Database error logging processing result",
                        franchisor_id=franchisor_id,
                        task_id=task_id,
                        error=str(e))
            return False
    
    def close(self):
        """Close database connections"""
        if hasattr(self, 'engine'):
            self.engine.dispose()


# Global instance for Celery tasks
_db_manager = None

def get_celery_db_manager() -> CeleryDatabaseManager:
    """Get or create Celery database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = CeleryDatabaseManager()
    return _db_manager


def cleanup_celery_db_manager():
    """Cleanup database manager (call on worker shutdown)"""
    global _db_manager
    if _db_manager is not None:
        _db_manager.close()
        _db_manager = None
