"""
SMS-related Celery tasks for scheduled messaging
"""

from datetime import datetime
import structlog
from app.core.celery_app import celery_app

logger = structlog.get_logger(__name__)


@celery_app.task(name='send_scheduled_sms')
def send_scheduled_sms(lead_id: str, message: str) -> str:
    """
    Celery task to send scheduled SMS messages
    
    Args:
        lead_id (str): The ID of the lead to send SMS to
        message (str): The message content to send
        
    Returns:
        str: Success message
    """
    try:
        # Print the scheduled message to terminal
        print(f"Sending SMS to lead {lead_id}: {message}")
        
        # Log the task execution
        logger.info("Scheduled SMS task executed", 
                   lead_id=lead_id, 
                   message=message,
                   executed_at=datetime.now().isoformat())
        
        return f"SMS sent successfully to lead {lead_id}"
        
    except Exception as e:
        logger.error("Error executing scheduled SMS task", 
                    lead_id=lead_id, 
                    error=str(e))
        raise
