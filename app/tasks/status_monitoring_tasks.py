"""
Lead Status Monitoring and Metrics Celery Tasks
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List
import structlog
from sqlalchemy import select, func, and_, desc
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.celery_app import celery_app
from app.core.database.connection import get_db
from app.models.lead_reference import LeadStatusHistory, LeadStatus
from app.models.lead import Lead
from app.core.logging import logger

logger = structlog.get_logger(__name__)


@celery_app.task(name='monitor_status_update_accuracy')
def monitor_status_update_accuracy() -> Dict[str, Any]:
    """
    Monitor the accuracy and performance of status updates
    Runs every hour to collect metrics
    """
    try:
        # Run the async monitoring function
        result = asyncio.run(_monitor_status_accuracy_async())
        
        logger.info(
            "Status update accuracy monitoring completed",
            **result
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error in status monitoring task: {e}")
        raise


async def _monitor_status_accuracy_async() -> Dict[str, Any]:
    """Async function to monitor status accuracy"""
    async for db in get_db():
        try:
            # Get metrics for the last 24 hours
            since_time = datetime.now() - timedelta(hours=24)
            
            # Total status updates in last 24h
            total_updates_stmt = select(func.count(LeadStatusHistory.id)).where(
                and_(
                    LeadStatusHistory.created_at >= since_time,
                    LeadStatusHistory.is_deleted == False
                )
            )
            total_updates_result = await db.execute(total_updates_stmt)
            total_updates = total_updates_result.scalar() or 0
            
            # Updates by source (andy vs manual)
            source_stats_stmt = select(
                LeadStatusHistory.source,
                func.count(LeadStatusHistory.id).label('count'),
                func.avg(LeadStatusHistory.confidence).label('avg_confidence')
            ).where(
                and_(
                    LeadStatusHistory.created_at >= since_time,
                    LeadStatusHistory.is_deleted == False
                )
            ).group_by(LeadStatusHistory.source)
            
            source_stats_result = await db.execute(source_stats_stmt)
            source_stats = {}
            for row in source_stats_result:
                source_stats[row.source] = {
                    'count': row.count,
                    'avg_confidence': float(row.avg_confidence) if row.avg_confidence else 0.0
                }
            
            # Low confidence updates that need review
            low_confidence_stmt = select(func.count(LeadStatusHistory.id)).where(
                and_(
                    LeadStatusHistory.created_at >= since_time,
                    LeadStatusHistory.review_needed == True,
                    LeadStatusHistory.is_deleted == False
                )
            )
            low_confidence_result = await db.execute(low_confidence_stmt)
            low_confidence_count = low_confidence_result.scalar() or 0
            
            # Most common status transitions
            transitions_stmt = select(
                LeadStatus.name.label('from_status'),
                LeadStatus.name.label('to_status'),
                func.count(LeadStatusHistory.id).label('count')
            ).select_from(
                LeadStatusHistory.__table__.join(
                    LeadStatus.__table__, 
                    LeadStatusHistory.from_status_id == LeadStatus.id,
                    isouter=True
                ).join(
                    LeadStatus.__table__.alias('to_status_table'),
                    LeadStatusHistory.to_status_id == LeadStatus.id
                )
            ).where(
                and_(
                    LeadStatusHistory.created_at >= since_time,
                    LeadStatusHistory.is_deleted == False
                )
            ).group_by(
                LeadStatusHistory.from_status_id,
                LeadStatusHistory.to_status_id
            ).order_by(desc('count')).limit(10)
            
            transitions_result = await db.execute(transitions_stmt)
            top_transitions = [
                {
                    'from_status': row.from_status or 'New',
                    'to_status': row.to_status,
                    'count': row.count
                }
                for row in transitions_result
            ]
            
            # Calculate accuracy metrics
            andy_stats = source_stats.get('andy', {'count': 0, 'avg_confidence': 0.0})
            accuracy_rate = (total_updates - low_confidence_count) / total_updates if total_updates > 0 else 0.0
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'period': '24_hours',
                'total_updates': total_updates,
                'andy_updates': andy_stats['count'],
                'manual_updates': source_stats.get('manual', {'count': 0})['count'],
                'avg_andy_confidence': andy_stats['avg_confidence'],
                'low_confidence_count': low_confidence_count,
                'accuracy_rate': accuracy_rate,
                'review_needed_rate': low_confidence_count / total_updates if total_updates > 0 else 0.0,
                'top_transitions': top_transitions,
                'source_breakdown': source_stats
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error in status accuracy monitoring: {e}")
            raise
        finally:
            break





@celery_app.task(name='generate_status_analytics_report')
def generate_status_analytics_report() -> Dict[str, Any]:
    """
    Generate comprehensive analytics report for status updates
    Runs daily to provide insights
    """
    try:
        result = asyncio.run(_generate_analytics_report_async())
        
        logger.info(
            "Status analytics report generated",
            total_leads_analyzed=result.get('total_leads_analyzed', 0),
            report_period=result.get('period', 'unknown')
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error generating status analytics report: {e}")
        raise


async def _generate_analytics_report_async() -> Dict[str, Any]:
    """Generate comprehensive analytics report"""
    async for db in get_db():
        try:
            # Get data for the last 7 days
            since_time = datetime.now() - timedelta(days=7)
            
            # Lead status distribution
            status_distribution_stmt = select(
                LeadStatus.name,
                func.count(Lead.id).label('count')
            ).select_from(
                Lead.__table__.join(LeadStatus, Lead.lead_status_id == LeadStatus.id)
            ).where(
                and_(
                    Lead.is_deleted == False,
                    LeadStatus.is_deleted == False
                )
            ).group_by(LeadStatus.name).order_by(desc('count'))
            
            status_dist_result = await db.execute(status_distribution_stmt)
            status_distribution = [
                {'status': row.name, 'count': row.count}
                for row in status_dist_result
            ]
            
            # Status change velocity (changes per day)
            velocity_stmt = select(
                func.date(LeadStatusHistory.created_at).label('date'),
                func.count(LeadStatusHistory.id).label('changes')
            ).where(
                and_(
                    LeadStatusHistory.created_at >= since_time,
                    LeadStatusHistory.is_deleted == False
                )
            ).group_by(func.date(LeadStatusHistory.created_at)).order_by('date')
            
            velocity_result = await db.execute(velocity_stmt)
            daily_velocity = [
                {'date': str(row.date), 'changes': row.changes}
                for row in velocity_result
            ]
            
            # Classification method effectiveness
            method_effectiveness_stmt = select(
                LeadStatusHistory.source,
                func.avg(LeadStatusHistory.confidence).label('avg_confidence'),
                func.count(LeadStatusHistory.id).label('total_updates'),
                func.sum(func.case((LeadStatusHistory.review_needed == True, 1), else_=0)).label('review_needed')
            ).where(
                and_(
                    LeadStatusHistory.created_at >= since_time,
                    LeadStatusHistory.is_deleted == False
                )
            ).group_by(LeadStatusHistory.source)
            
            method_result = await db.execute(method_effectiveness_stmt)
            method_effectiveness = {}
            for row in method_result:
                method_effectiveness[row.source] = {
                    'avg_confidence': float(row.avg_confidence) if row.avg_confidence else 0.0,
                    'total_updates': row.total_updates,
                    'review_needed': row.review_needed or 0,
                    'accuracy_rate': 1.0 - (row.review_needed or 0) / row.total_updates if row.total_updates > 0 else 0.0
                }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'period': '7_days',
                'since_date': since_time.isoformat(),
                'status_distribution': status_distribution,
                'daily_velocity': daily_velocity,
                'method_effectiveness': method_effectiveness,
                'total_leads_analyzed': sum(item['count'] for item in status_distribution),
                'total_status_changes': sum(item['changes'] for item in daily_velocity)
            }
            
        except Exception as e:
            logger.error(f"Error generating analytics report: {e}")
            raise
        finally:
            break


# Schedule the monitoring tasks
@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """Setup periodic tasks for status monitoring"""
    
    # Monitor accuracy every hour
    sender.add_periodic_task(
        3600.0,  # 1 hour
        monitor_status_update_accuracy.s(),
        name='hourly-status-accuracy-monitoring'
    )
    
    # Generate analytics report daily at 2 AM
    sender.add_periodic_task(
        86400.0,  # 24 hours
        generate_status_analytics_report.s(),
        name='daily-status-analytics-report'
    )
    

