"""
SMS Onboarding Tasks - Celery background tasks for new lead SMS onboarding
Handles automatic detection of new leads and sending introductory SMS messages
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from uuid import UUID
import structlog
from celery import Task
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.celery_app import celery_app
from app.core.database.connection import get_db
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.models.messaging_rule import MessagingRule
from app.services.sms_outbound_service import get_sms_outbound_service
from app.core.utils.exception_manager.custom_exceptions import BusinessLogicError

from app.core.logging import logger


class SMSOnboardingTask(Task):
    """Base task class for SMS onboarding with error handling"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure"""
        logger.error(
            f"SMS onboarding task failed: {task_id}",
            exception=str(exc),
            args=args,
            kwargs=kwargs
        )
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success"""
        logger.info(
            f"SMS onboarding task completed: {task_id}",
            result=retval,
            args=args,
            kwargs=kwargs
        )


@celery_app.task(
    bind=True,
    base=SMSOnboardingTask,
    name="app.tasks.sms_onboarding_tasks.send_intro_sms_to_lead",
    max_retries=3,
    default_retry_delay=300,  # 5 minutes
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 300}
)
def send_intro_sms_to_lead(self, lead_id: str, delay_minutes: int = 0) -> Dict[str, Any]:
    """
    Send introductory SMS to a specific lead
    
    Args:
        lead_id: Lead ID to send SMS to
        delay_minutes: Optional delay before sending (for scheduling)
        
    Returns:
        Dict containing task result
    """
    try:
        logger.info(
            f"Starting intro SMS task for lead",
            lead_id=lead_id,
            delay_minutes=delay_minutes
        )
        
        # Apply delay if specified
        if delay_minutes > 0:
            logger.info(f"Applying delay of {delay_minutes} minutes before sending SMS")
            import time
            time.sleep(delay_minutes * 60)
        
        # Run async function in sync context
        result = asyncio.run(_send_intro_sms_async(lead_id))
        
        return {
            "success": True,
            "lead_id": lead_id,
            "result": result,
            "task_id": self.request.id,
            "delay_applied": delay_minutes
        }
        
    except Exception as e:
        logger.error(
            f"Failed to send intro SMS to lead {lead_id}: {str(e)}",
            error=str(e),
            task_id=self.request.id
        )
        raise self.retry(exc=e)


@celery_app.task(
    bind=True,
    base=SMSOnboardingTask,
    name="app.tasks.sms_onboarding_tasks.detect_and_onboard_new_leads",
    max_retries=2,
    default_retry_delay=600,  # 10 minutes
    autoretry_for=(Exception,),
)
def detect_and_onboard_new_leads(self, hours_lookback: int = 1) -> Dict[str, Any]:
    """
    Detect new leads and schedule introductory SMS messages
    
    Args:
        hours_lookback: How many hours back to look for new leads
        
    Returns:
        Dict containing detection results
    """
    try:
        logger.info(
            f"Starting new lead detection task",
            hours_lookback=hours_lookback,
            task_id=self.request.id
        )
        
        # Run async function in sync context
        result = asyncio.run(_detect_and_onboard_new_leads_async(hours_lookback))
        
        return {
            "success": True,
            "result": result,
            "task_id": self.request.id,
            "hours_lookback": hours_lookback
        }
        
    except Exception as e:
        logger.error(
            f"Failed to detect and onboard new leads: {str(e)}",
            error=str(e),
            task_id=self.request.id
        )
        raise self.retry(exc=e)





async def _send_intro_sms_async(lead_id: str) -> Dict[str, Any]:
    """Async function to send introductory SMS"""
    try:
        sms_service = get_sms_outbound_service()
        
        async for db in get_db():
            result = await sms_service.send_introductory_sms(
                lead_id=lead_id,
                db=db
            )
            
            return result
            
    except Exception as e:
        logger.error(f"Error in _send_intro_sms_async: {str(e)}")
        raise





async def _detect_and_onboard_new_leads_async(hours_lookback: int) -> Dict[str, Any]:
    """Async function to detect and onboard new leads"""
    try:
        async for db in get_db():
            # Get active messaging rule
            messaging_rule = await _get_active_messaging_rule(db)
            if not messaging_rule:
                logger.warning("No active messaging rule found, using default delays")
                lead_init_delay_h = 1  # Default 1 hour delay
            else:
                lead_init_delay_h = messaging_rule.lead_init_delay_h
            
            # Find new leads created in the last N hours
            cutoff_time = datetime.utcnow() - timedelta(hours=hours_lookback)
            
            query = select(Lead).where(
                and_(
                    Lead.created_at >= cutoff_time,
                    Lead.is_active == True,
                    Lead.is_deleted == False,
                    or_(Lead.mobile.isnot(None), Lead.phone.isnot(None))  # Has phone number
                )
            )
            
            result = await db.execute(query)
            new_leads = result.scalars().all()
            
            logger.info(f"Found {len(new_leads)} new leads in the last {hours_lookback} hours")
            
            scheduled_count = 0
            skipped_count = 0
            
            for lead in new_leads:
                try:
                    # Check if we've already sent an intro SMS to this lead
                    existing_message = await _check_existing_intro_message(db, str(lead.id))
                    
                    if existing_message:
                        logger.info(f"Skipping lead {lead.id} - intro SMS already sent")
                        skipped_count += 1
                        continue
                    
                    # Check if follow-up suppression is enabled via environment variable
                    import os
                    followup_suppression_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"

                    if followup_suppression_enabled:
                        logger.info(
                            f"SMS onboarding suppressed for lead {lead.id}",
                            reason="WORKFLOW_ANDY_NO_FOLLOWUPS enabled",
                            suppressed=True
                        )
                        skipped_count += 1
                        continue

                    # Schedule intro SMS with delay
                    delay_minutes = lead_init_delay_h * 60  # Convert hours to minutes

                    # Schedule the task
                    send_intro_sms_to_lead.apply_async(
                        args=[str(lead.id)],
                        kwargs={"delay_minutes": delay_minutes},
                        countdown=delay_minutes * 60  # Celery countdown is in seconds
                    )
                    
                    scheduled_count += 1
                    
                    logger.info(
                        f"Scheduled intro SMS for lead {lead.id}",
                        delay_hours=lead_init_delay_h,
                        phone=lead.mobile or lead.phone
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing lead {lead.id}: {str(e)}")
                    continue
            
            return {
                "new_leads_found": len(new_leads),
                "scheduled_count": scheduled_count,
                "skipped_count": skipped_count,
                "delay_hours": lead_init_delay_h
            }
            
    except Exception as e:
        logger.error(f"Error in _detect_and_onboard_new_leads_async: {str(e)}")
        raise


async def _get_active_messaging_rule(db: AsyncSession) -> Optional[MessagingRule]:
    """Get the active messaging rule from database"""
    try:
        query = select(MessagingRule).where(
            and_(
                MessagingRule.is_active == True,
                MessagingRule.is_deleted == False
            )
        ).order_by(MessagingRule.created_at.desc())
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
        
    except Exception as e:
        logger.error(f"Error getting active messaging rule: {str(e)}")
        return None


async def _check_existing_intro_message(db: AsyncSession, lead_id: str) -> bool:
    """Check if an intro message has already been sent to this lead"""
    try:
        query = select(ConversationMessage).where(
            and_(
                ConversationMessage.lead_id == UUID(lead_id),
                ConversationMessage.sender == "system",
                ConversationMessage.is_active == True,
                ConversationMessage.is_deleted == False
            )
        ).limit(1)
        
        result = await db.execute(query)
        existing_message = result.scalar_one_or_none()
        
        return existing_message is not None
        
    except Exception as e:
        logger.error(f"Error checking existing intro message: {str(e)}")
        return False


# Periodic task to run new lead detection every 30 minutes
@celery_app.task(
    bind=True,
    name="app.tasks.sms_onboarding_tasks.periodic_new_lead_detection"
)
def periodic_new_lead_detection(self):
    """Periodic task to detect and onboard new leads"""
    try:
        # Check if follow-up suppression is enabled via environment variable
        import os
        followup_suppression_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"

        if followup_suppression_enabled:
            logger.info(
                "Periodic new lead detection suppressed",
                reason="WORKFLOW_ANDY_NO_FOLLOWUPS enabled",
                task_id=self.request.id,
                suppressed=True
            )
            return {
                "success": True,
                "suppressed": True,
                "reason": "WORKFLOW_ANDY_NO_FOLLOWUPS enabled",
                "task_id": self.request.id
            }

        logger.info("Running periodic new lead detection")

        # Detect leads from the last 2 hours to ensure we don't miss any
        result = detect_and_onboard_new_leads.delay(hours_lookback=2)
        
        return {
            "success": True,
            "task_id": result.id,
            "scheduled_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in periodic new lead detection: {str(e)}")
        raise
