"""
Lead Status Monitoring Tasks
Celery tasks for automatic lead status updates based on conversation analysis
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

import structlog
from celery import Celery
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from app.core.celery_app import celery_app
from app.agents.lead_status_agent import get_lead_status_agent, ConversationAnalysis
from app.models.lead import Lead
from app.core.database.connection import get_db
from app.core.memory.sms_memory import get_sms_memory_manager

from app.core.logging import logger


@celery_app.task(bind=True, name="monitor_lead_status")
def monitor_lead_status(self, phone_number: str, force_update: bool = False) -> Dict[str, Any]:
    """
    Monitor a single lead's conversation and update status if needed
    
    Args:
        phone_number: Phone number to monitor
        force_update: Force status update regardless of confidence level
        
    Returns:
        Dict with monitoring results
    """
    try:
        logger.info(f"Starting lead status monitoring for {phone_number}")
        
        # Get lead status agent
        agent = get_lead_status_agent()
        
        # Run async monitoring
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            analysis = loop.run_until_complete(
                agent.monitor_conversation(phone_number, force_update)
            )
        finally:
            loop.close()
        
        if analysis:
            result = {
                "success": True,
                "phone_number": phone_number,
                "suggested_status": analysis.suggested_status.value,
                "confidence": analysis.confidence,
                "reasoning": analysis.reasoning,
                "engagement_level": analysis.engagement_level,
                "conversation_stage": analysis.conversation_stage,
                "response_count": analysis.response_count,
                "task_id": self.request.id
            }
            
            logger.info(
                "Lead status monitoring completed",
                phone=phone_number,
                status=analysis.suggested_status.value,
                confidence=analysis.confidence,
                task_id=self.request.id
            )
        else:
            result = {
                "success": False,
                "phone_number": phone_number,
                "error": "No conversation data found or analysis failed",
                "task_id": self.request.id
            }
            
            logger.warning(
                "Lead status monitoring failed - no analysis",
                phone=phone_number,
                task_id=self.request.id
            )
        
        return result
        
    except Exception as e:
        error_msg = f"Error monitoring lead status for {phone_number}: {str(e)}"
        logger.error(error_msg, task_id=self.request.id)
        
        return {
            "success": False,
            "phone_number": phone_number,
            "error": error_msg,
            "task_id": self.request.id
        }


@celery_app.task(bind=True, name="bulk_monitor_lead_statuses")
def bulk_monitor_lead_statuses(self, phone_numbers: List[str]) -> Dict[str, Any]:
    """
    Monitor multiple leads' conversations and update statuses
    
    Args:
        phone_numbers: List of phone numbers to monitor
        
    Returns:
        Dict with bulk monitoring results
    """
    try:
        logger.info(f"Starting bulk lead status monitoring for {len(phone_numbers)} leads")
        
        # Get lead status agent
        agent = get_lead_status_agent()
        
        # Run async bulk monitoring
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            results = loop.run_until_complete(
                agent.bulk_monitor_conversations(phone_numbers)
            )
        finally:
            loop.close()
        
        # Process results
        successful_updates = 0
        failed_updates = 0
        analysis_results = {}
        
        for phone, analysis in results.items():
            if analysis:
                successful_updates += 1
                analysis_results[phone] = {
                    "success": True,
                    "suggested_status": analysis.suggested_status.value,
                    "confidence": analysis.confidence,
                    "reasoning": analysis.reasoning,
                    "engagement_level": analysis.engagement_level
                }
            else:
                failed_updates += 1
                analysis_results[phone] = {
                    "success": False,
                    "error": "Analysis failed or no conversation data"
                }
        
        result = {
            "success": True,
            "total_leads": len(phone_numbers),
            "successful_updates": successful_updates,
            "failed_updates": failed_updates,
            "results": analysis_results,
            "task_id": self.request.id
        }
        
        logger.info(
            "Bulk lead status monitoring completed",
            total=len(phone_numbers),
            successful=successful_updates,
            failed=failed_updates,
            task_id=self.request.id
        )
        
        return result
        
    except Exception as e:
        error_msg = f"Error in bulk lead status monitoring: {str(e)}"
        logger.error(error_msg, task_id=self.request.id)
        
        return {
            "success": False,
            "error": error_msg,
            "task_id": self.request.id
        }


@celery_app.task(bind=True, name="auto_monitor_active_conversations")
def auto_monitor_active_conversations(self, hours_back: int = 24) -> Dict[str, Any]:
    """
    Automatically monitor all active conversations from the last N hours
    
    Args:
        hours_back: Number of hours to look back for active conversations
        
    Returns:
        Dict with monitoring results
    """
    try:
        logger.info(f"Starting automatic monitoring of conversations from last {hours_back} hours")
        
        # Get memory manager
        memory_manager = get_sms_memory_manager()
        
        # Get active phone numbers from recent conversations
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        # Get all phone numbers with recent activity
        active_phones = []
        
        # This would need to be implemented based on your Redis key structure
        # For now, we'll get leads from database that have recent conversation messages
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def get_active_phones():
                async for db in get_db():
                    # Get leads with recent conversation activity
                    result = await db.execute(
                        select(Lead.phone).where(
                            and_(
                                Lead.phone.isnot(None),
                                Lead.is_deleted == False,
                                Lead.updated_at >= cutoff_time
                            )
                        ).distinct()
                    )
                    return [row[0] for row in result.fetchall() if row[0]]
            
            active_phones = loop.run_until_complete(get_active_phones())
            
        finally:
            loop.close()
        
        if not active_phones:
            logger.info("No active conversations found for monitoring")
            return {
                "success": True,
                "message": "No active conversations found",
                "total_leads": 0,
                "task_id": self.request.id
            }
        
        # Trigger bulk monitoring
        bulk_result = bulk_monitor_lead_statuses.delay(active_phones)
        
        result = {
            "success": True,
            "message": f"Triggered monitoring for {len(active_phones)} active conversations",
            "total_leads": len(active_phones),
            "active_phones": active_phones[:10],  # Show first 10 for debugging
            "bulk_task_id": bulk_result.id,
            "task_id": self.request.id
        }
        
        logger.info(
            "Automatic conversation monitoring triggered",
            total_leads=len(active_phones),
            bulk_task_id=bulk_result.id,
            task_id=self.request.id
        )
        
        return result
        
    except Exception as e:
        error_msg = f"Error in automatic conversation monitoring: {str(e)}"
        logger.error(error_msg, task_id=self.request.id)
        
        return {
            "success": False,
            "error": error_msg,
            "task_id": self.request.id
        }


@celery_app.task(bind=True, name="get_lead_status_insights")
def get_lead_status_insights(self, phone_number: str) -> Dict[str, Any]:
    """
    Get detailed insights about a lead's conversation for analysis
    
    Args:
        phone_number: Phone number to analyze
        
    Returns:
        Dict with conversation insights
    """
    try:
        logger.info(f"Getting lead status insights for {phone_number}")
        
        # Get lead status agent
        agent = get_lead_status_agent()
        
        # Run async insights gathering
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            insights = loop.run_until_complete(
                agent.get_conversation_insights(phone_number)
            )
        finally:
            loop.close()
        
        insights["task_id"] = self.request.id
        
        logger.info(
            "Lead status insights generated",
            phone=phone_number,
            task_id=self.request.id
        )
        
        return insights
        
    except Exception as e:
        error_msg = f"Error getting lead status insights for {phone_number}: {str(e)}"
        logger.error(error_msg, task_id=self.request.id)
        
        return {
            "error": error_msg,
            "phone_number": phone_number,
            "task_id": self.request.id
        }
