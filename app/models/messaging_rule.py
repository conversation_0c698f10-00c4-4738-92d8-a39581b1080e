"""
Messaging Rule Model
SQLAlchemy model for messaging rule configuration
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Integer, func, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database.connection import Base


class MessagingRule(Base):
    """Messaging Rule model for GrowthHive General Settings"""

    __tablename__ = "messaging_rule"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Messaging configuration fields (follow-up functionality removed)
    # Note: Follow-up related fields have been removed from Andy AI

    # Status fields
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        comment="Whether this messaging rule is active",
    )
    is_deleted: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, comment="Soft delete flag"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="Creation timestamp",
    )
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="Deletion timestamp"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="Last update timestamp",
    )

    # Table constraints (follow-up constraints removed)
    __table_args__ = (
        # Follow-up related constraints have been removed
    )

    def __repr__(self) -> str:
        return (
            f"<MessagingRule(id={self.id}, is_active={self.is_active})>"
        )

    def __str__(self) -> str:
        return (
            f"MessagingRule: Follow-up functionality removed"
        )
