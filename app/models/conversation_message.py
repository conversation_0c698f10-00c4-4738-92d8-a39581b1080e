"""
Conversation Message Model
SQLAlchemy model for storing conversation messages between leads and the system
"""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, ForeignKey, func, CheckConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSO<PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.core.database.connection import Base


class ConversationMessage(Base):
    """Conversation message model for storing messages between leads and the system"""

    __tablename__ = "conversation_message"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Foreign key relationships
    lead_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("leads.id"),
        nullable=False,
        index=True,
        comment="Reference to the lead who is part of this conversation",
    )

    franchisor_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        ForeignKey("franchisors.id"),
        nullable=True,
        index=True,
        comment="Reference to the franchisor associated with this conversation",
    )

    # Message details
    sender: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        index=True,
        comment="Who sent the message: 'lead' or 'system'",
    )

    message: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="The actual message content",
    )

    # Message metadata for additional message information (follow-up tracking, etc.)
    message_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="JSON metadata for message context (followup_attempt, type, etc.)",
    )

    # Standard fields following codebase patterns
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this message is active",
    )

    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Whether this message has been soft deleted",
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
        comment="When this message was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="When this message was last updated",
    )

    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When this message was soft deleted",
    )

    # Relationships
    lead = relationship("Lead", back_populates="conversation_messages")
    franchisor = relationship("Franchisor", lazy="select")

    # Table constraints
    __table_args__ = (
        CheckConstraint(
            "sender IN ('lead', 'system')",
            name="check_sender_valid"
        ),
        CheckConstraint(
            "message IS NOT NULL AND LENGTH(TRIM(message)) > 0",
            name="check_message_not_empty"
        ),
    )

    def __repr__(self):
        return f"<ConversationMessage(id={self.id}, lead_id={self.lead_id}, sender={self.sender}, message='{self.message[:50]}...')>"
