"""
Sales Script Model
SQLAlchemy model for storing sales script messages used in conversational flow
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, Integer, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database.connection import Base


class SalesScript(Base):
    """Sales script model for storing conversation flow messages"""

    __tablename__ = "sales_script"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Script details
    script_title: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        comment="Title/identifier for the script (e.g., 'Initial greeting', 'Qualification Introduction')",
    )
    
    script_content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="The actual message content to be sent",
    )
    
    script_stage: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        comment="Stage in conversation flow (initial_greeting, prequalification, document_qa, followup, goodbye)",
    )
    
    order_sequence: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="Order sequence for scripts within the same stage",
    )
    
    # Template variables support
    has_variables: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Whether this script contains template variables for dynamic content",
    )
    
    variable_schema: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON schema describing the variables used in this script",
    )

    # Status fields
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this script is active and should be used",
    )
    
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Soft delete flag",
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Creation timestamp",
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Last update timestamp",
    )
    
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="Deletion timestamp",
    )

    def __repr__(self) -> str:
        return (
            f"<SalesScript(id={self.id}, title={self.script_title}, "
            f"stage={self.script_stage}, active={self.is_active})>"
        )
