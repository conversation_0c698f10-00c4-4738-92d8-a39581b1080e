"""
Industry model for database operations (formerly Category)
"""

from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.dialects.postgresql import UUID
from app.core.database.connection import Base
from datetime import datetime
import uuid


class Industry(Base):
    """Industry model representing main industries with UUID primary key."""
    __tablename__ = "industry"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<Industry(id={self.id}, name={self.name})>"


# Keep Category as alias for backward compatibility
Category = Industry
