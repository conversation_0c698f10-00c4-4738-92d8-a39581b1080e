"""
Escalation Question Model
Model for storing escalation questions that need human review
"""

from sqlalchemy import Column, String, DateTime, Boolean, Float, Text, Enum as SQLE<PERSON>, Foreign<PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum

from app.core.database.connection import Base


class EscalationStatus(enum.Enum):
    """Escalation question status enumeration"""
    PENDING = "pending"
    IN_REVIEW = "in_review"
    ANSWERED = "answered"
    RESOLVED = "resolved"
    CANCELLED = "cancelled"


class EscalationQuestion(Base):
    """
    Model for escalation questions that require human review
    """
    __tablename__ = "escalation_questions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Question details
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)
    
    # Associated lead
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=True, index=True)
    
    # Confidence and scoring
    confidence_score = Column(Float, nullable=False, default=0.0)
    similarity_threshold = Column(Float, nullable=False, default=0.8)
    
    # Status tracking
    status = Column(SQLEnum(EscalationStatus), nullable=False, default=EscalationStatus.PENDING)
    
    # Clarification tracking
    clarification_count = Column(Integer, nullable=False, default=0)
    max_clarifications = Column(Integer, nullable=False, default=2)
    
    # Review information
    reviewed_by = Column(String(255), nullable=True)
    reviewed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Embedding for similarity search
    embedding = Column(Text, nullable=True)  # JSON string of embedding vector
    embedding_model = Column(String(100), nullable=True, default="text-embedding-ada-002")
    
    # Source information
    source_context = Column(Text, nullable=True)  # Context where question was asked
    conversation_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Priority and categorization
    priority = Column(String(20), nullable=False, default="medium")  # low, medium, high, urgent
    category = Column(String(100), nullable=True)  # franchise, pricing, training, etc.
    tags = Column(Text, nullable=True)  # JSON array of tags
    
    # Audit fields
    is_active = Column(Boolean, nullable=False, default=True)
    is_deleted = Column(Boolean, nullable=False, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    lead = relationship("Lead", back_populates="escalation_questions", lazy="select")
    
    def __repr__(self):
        return f"<EscalationQuestion(id={self.id}, status={self.status}, confidence={self.confidence_score})>"
    
    @property
    def needs_clarification(self) -> bool:
        """Check if question needs clarification"""
        return (
            self.clarification_count < self.max_clarifications and
            self.confidence_score < self.similarity_threshold and
            self.status == EscalationStatus.PENDING
        )
    
    @property
    def is_resolved(self) -> bool:
        """Check if question is resolved"""
        return self.status in [EscalationStatus.ANSWERED, EscalationStatus.RESOLVED]
    
    def can_request_clarification(self) -> bool:
        """Check if clarification can be requested"""
        return (
            self.clarification_count < self.max_clarifications and
            self.status == EscalationStatus.PENDING
        )
    
    def increment_clarification_count(self):
        """Increment clarification count"""
        if self.can_request_clarification():
            self.clarification_count += 1
            return True
        return False
    
    def mark_answered(self, answer: str, reviewed_by: str = None):
        """Mark question as answered"""
        self.answer = answer
        self.status = EscalationStatus.ANSWERED
        self.reviewed_by = reviewed_by
        self.reviewed_at = func.now()
    
    def mark_resolved(self, reviewed_by: str = None):
        """Mark question as resolved"""
        self.status = EscalationStatus.RESOLVED
        self.reviewed_by = reviewed_by
        self.reviewed_at = func.now()
    
    def set_priority(self, priority: str):
        """Set question priority"""
        valid_priorities = ["low", "medium", "high", "urgent"]
        if priority.lower() in valid_priorities:
            self.priority = priority.lower()
    
    def add_tags(self, tags: list):
        """Add tags to question"""
        import json
        if isinstance(tags, list):
            existing_tags = []
            if self.tags:
                try:
                    existing_tags = json.loads(self.tags)
                except:
                    existing_tags = []
            
            # Merge tags and remove duplicates
            all_tags = list(set(existing_tags + tags))
            self.tags = json.dumps(all_tags)
    
    def get_tags(self) -> list:
        """Get question tags"""
        import json
        if self.tags:
            try:
                return json.loads(self.tags)
            except:
                return []
        return []
    
    def set_embedding(self, embedding: list):
        """Set embedding vector"""
        import json
        if isinstance(embedding, list):
            self.embedding = json.dumps(embedding)
    
    def get_embedding(self) -> list:
        """Get embedding vector"""
        import json
        if self.embedding:
            try:
                return json.loads(self.embedding)
            except:
                return []
        return []
