"""
Booking Models
Database models for storing booking information
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.core.database.base import Base


class Booking(Base):
    """Booking model for storing appointment bookings"""
    __tablename__ = "bookings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Zoho Bookings integration
    zoho_booking_id = Column(String(255), unique=True, nullable=True)
    zoho_service_id = Column(String(255), nullable=True)
    zoho_staff_id = Column(String(255), nullable=True)
    
    # Lead relationship
    lead_id = Column(UUID(as_uuid=True), ForeignKey('leads.id'), nullable=True)
    
    # Customer information
    customer_name = Column(String(255), nullable=False)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(20), nullable=True)
    
    # Booking details
    service_type = Column(String(100), nullable=False)  # consultation, follow_up, demo
    staff_name = Column(String(255), nullable=False)
    staff_email = Column(String(255), nullable=True)
    
    # Scheduling
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    duration_minutes = Column(Integer, nullable=False)
    timezone = Column(String(50), default='Australia/Sydney')
    
    # Status and tracking
    status = Column(String(50), default='scheduled')  # scheduled, confirmed, cancelled, completed, no_show
    booking_source = Column(String(50), default='sms_ai')  # sms_ai, web, manual, api
    
    # URLs and links
    booking_url = Column(Text, nullable=True)
    meeting_link = Column(Text, nullable=True)
    reschedule_url = Column(Text, nullable=True)
    cancel_url = Column(Text, nullable=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # Confirmation and reminders
    confirmation_sent = Column(Boolean, default=False)
    reminder_sent = Column(Boolean, default=False)
    
    # Metadata
    booking_metadata = Column(JSONB, nullable=True)  # Store additional Zoho data
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    lead = relationship("Lead", back_populates="bookings")
    
    def __repr__(self):
        return f"<Booking(id='{self.id}', customer='{self.customer_name}', status='{self.status}')>"


class BookingHistory(Base):
    """Booking history for tracking changes"""
    __tablename__ = "booking_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    booking_id = Column(UUID(as_uuid=True), ForeignKey('bookings.id'), nullable=False)
    
    # Change tracking
    action = Column(String(50), nullable=False)  # created, updated, cancelled, rescheduled, completed
    old_values = Column(JSONB, nullable=True)
    new_values = Column(JSONB, nullable=True)
    
    # Who made the change
    changed_by = Column(String(255), nullable=True)  # user_id, system, zoho_sync
    change_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    
    # Relationships
    booking = relationship("Booking", backref="history")
    
    def __repr__(self):
        return f"<BookingHistory(booking_id='{self.booking_id}', action='{self.action}')>"


class StaffAvailability(Base):
    """Staff availability tracking"""
    __tablename__ = "staff_availability"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Staff information
    staff_name = Column(String(255), nullable=False)
    staff_email = Column(String(255), nullable=False)
    zoho_staff_id = Column(String(255), nullable=False, unique=True)
    
    # Availability settings
    working_hours = Column(JSONB, nullable=True)  # Store working hours per day
    time_zone = Column(String(50), default='Australia/Sydney')
    
    # Status
    is_active = Column(Boolean, default=True)
    is_available = Column(Boolean, default=True)
    
    # Specialties and services
    specialties = Column(JSONB, nullable=True)  # Array of specialties
    available_services = Column(JSONB, nullable=True)  # Array of service types
    
    # Booking preferences
    max_bookings_per_day = Column(Integer, default=8)
    buffer_time_minutes = Column(Integer, default=15)
    advance_booking_days = Column(Integer, default=30)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<StaffAvailability(staff_name='{self.staff_name}', is_available='{self.is_available}')>"


class BookingTemplate(Base):
    """Templates for different types of bookings"""
    __tablename__ = "booking_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template information
    name = Column(String(255), nullable=False)
    service_type = Column(String(100), nullable=False)
    zoho_service_id = Column(String(255), nullable=True)
    
    # Default settings
    duration_minutes = Column(Integer, nullable=False)
    buffer_time_minutes = Column(Integer, default=15)
    
    # Messaging templates
    confirmation_message = Column(Text, nullable=True)
    reminder_message = Column(Text, nullable=True)
    follow_up_message = Column(Text, nullable=True)
    
    # AI prompts for this service type
    ai_booking_prompt = Column(Text, nullable=True)
    ai_confirmation_prompt = Column(Text, nullable=True)
    
    # Settings
    is_active = Column(Boolean, default=True)
    requires_approval = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<BookingTemplate(name='{self.name}', service_type='{self.service_type}')>"
