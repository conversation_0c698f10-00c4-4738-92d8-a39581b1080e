"""
General Message model for database operations
"""
import uuid
from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import UUID
from app.core.database.connection import Base


class GeneralMessage(Base):
    __tablename__ = "general_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    message = Column(String, nullable=True)
    message_type = Column(String, nullable=True)

    def __repr__(self):
        return f"<GeneralMessage(id={self.id}, type={self.message_type}, message={self.message[:50] if self.message else 'None'}...)>"
