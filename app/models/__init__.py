"""Models package"""
from app.db.base_class import Base
from .user import User
from .session import Session
from .category import Category
from .industry import Industry
from .otp import OTP

from .franchisor import Franchisor
from .franchisor_chunk import FranchisorChunk
from .lead import Lead, LeadResponse, Communication
from .lead_reference import LeadSource, LeadStatus, LeadStatusHistory
from .conversation_message import ConversationMessage
from .system_setting import SystemSetting
from .holiday import Holiday
from .messaging_rule import MessagingRule
from .document import Document
from .general_message import GeneralMessage
from .sales_script import SalesScript
from .sample_doc import SampleDoc
from .booking import Booking, BookingHistory, StaffAvailability, BookingTemplate
from .conversation_message import ConversationMessage

from .escalation_question import EscalationQuestion

__all__ = [
    "Base",
    "User",
    "Session",
    "OTP",
    "Franchisor",
    "FranchisorChunk",
    "Lead",
    "LeadResponse",
    "Communication",
    "LeadSource",
    "LeadStatus",
    "LeadStatusHistory",
    "ConversationMessage",
    "SystemSetting",
    "Category",
    "Industry",

    "Document",
    "Holiday",
    "MessagingRule",
    "GeneralMessage",
    "SalesScript",
    "SampleDoc",
    "Booking",
    "BookingHistory",
    "StaffAvailability",
    "BookingTemplate",
    "ConversationMessage",
    "EscalationQuestion"
    # "ScheduledTask",      # REMOVED: Using new follow-up system
    # "FollowUpLog",        # REMOVED: Using new follow-up system
    # "FollowUpMetrics",    # REMOVED: Using new follow-up system
    # "TaskStatus",         # REMOVED: Using new follow-up system
    # "FollowUpStatus"      # REMOVED: Using new follow-up system
]
