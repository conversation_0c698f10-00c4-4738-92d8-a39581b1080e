"""
Lead reference models for database operations
"""

import uuid
from sqlalchemy import Column, String, Boolean, DateTime, func, Text, Float, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from app.core.database.connection import Base


class LeadSource(Base):
    """Lead source reference model"""
    
    __tablename__ = "lead_sources"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    leads = relationship("Lead", back_populates="lead_source_rel")
    
    def __repr__(self):
        return f"<LeadSource(id={self.id}, name={self.name}, is_active={self.is_active})>"


class LeadStatus(Base):
    """Lead status reference model"""

    __tablename__ = "lead_statuses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    colour = Column(String(7), nullable=False)  # Hex color code
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    leads = relationship("Lead", back_populates="lead_status_rel")
    from_status_history = relationship("LeadStatusHistory", foreign_keys="LeadStatusHistory.from_status_id", back_populates="from_status")
    to_status_history = relationship("LeadStatusHistory", foreign_keys="LeadStatusHistory.to_status_id", back_populates="to_status")

    def __repr__(self):
        return f"<LeadStatus(id={self.id}, name={self.name}, colour={self.colour}, is_active={self.is_active})>"


class LeadStatusHistory(Base):
    """Lead status history model for tracking status changes"""

    __tablename__ = "lead_status_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False, index=True)
    from_status_id = Column(UUID(as_uuid=True), ForeignKey("lead_statuses.id"), nullable=True, index=True)  # Nullable for initial status
    to_status_id = Column(UUID(as_uuid=True), ForeignKey("lead_statuses.id"), nullable=False, index=True)
    reason = Column(String(255), nullable=True)  # Reason for status change
    confidence = Column(Float, nullable=True)  # AI confidence score (0.0 to 1.0)
    rationale = Column(Text, nullable=True)  # AI reasoning for the change
    message_excerpt = Column(Text, nullable=True)  # Excerpt from the message that triggered the change
    changed_by = Column(String(100), nullable=False, default="system")  # Who/what made the change
    source = Column(String(50), nullable=False, default="andy")  # Source of the change (andy, manual, etc.)
    review_needed = Column(Boolean, default=False, nullable=False, index=True)  # Flag for low confidence changes

    # Standard audit fields
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    lead = relationship("Lead", back_populates="status_history")
    from_status = relationship("LeadStatus", foreign_keys=[from_status_id], back_populates="from_status_history")
    to_status = relationship("LeadStatus", foreign_keys=[to_status_id], back_populates="to_status_history")

    def __repr__(self):
        return f"<LeadStatusHistory(id={self.id}, lead_id={self.lead_id}, from_status_id={self.from_status_id}, to_status_id={self.to_status_id}, confidence={self.confidence})>"





class QuestionBank(Base):
    """Question bank model for prequalification questions (legacy)"""

    __tablename__ = "question_bank"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String, nullable=False)
    lead_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    franchisor_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<QuestionBank(id={self.id}, name={self.name}, is_active={self.is_active})>"


# EscalationQuestionBank model has been removed from Andy AI
