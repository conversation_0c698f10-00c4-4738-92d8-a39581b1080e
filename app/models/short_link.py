"""
Short Link Model
Handles branded short links for GrowthHive (ghv.li)
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, String, Text, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func

from app.db.base_class import Base


class ShortLink(Base):
    """Short link model for branded URL shortening service"""
    
    __tablename__ = "short_link"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Short slug (8-12 characters, base62 encoded)
    slug = Column(String(16), unique=True, nullable=False, index=True)
    
    # Original long URL to redirect to
    long_url = Column(Text, nullable=False)
    
    # Context metadata (JSONB for flexible storage)
    context_json = Column(JSONB, default={}, nullable=False)
    
    # Optional expiration date
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self) -> str:
        return f"<ShortLink(slug='{self.slug}', long_url='{self.long_url[:50]}...')>"
    
    def is_expired(self) -> bool:
        """Check if the short link has expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at.replace(tzinfo=None)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": str(self.id),
            "slug": self.slug,
            "long_url": self.long_url,
            "context_json": self.context_json,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "is_expired": self.is_expired()
        }
