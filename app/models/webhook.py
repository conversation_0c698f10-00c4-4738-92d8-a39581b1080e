"""
Webhook Model
SQLAlchemy model for storing Kudosity webhook events
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, JSON, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database.connection import Base


class Webhook(Base):
    """Webhook model for storing Kudosity webhook events"""

    __tablename__ = "webhooks"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Webhook event details
    event_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        comment="Type of webhook event (LINK_HIT, OPT_OUT, MMS_INBOUND, etc.)",
    )
    
    event_timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        comment="Timestamp from the webhook payload",
    )
    
    # Raw webhook payload
    payload: Mapped[dict] = mapped_column(
        JSON,
        nullable=False,
        comment="Complete webhook payload as received from Kudosity",
    )
    
    # Extracted key information for easier querying
    message_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        comment="Message ID from the webhook payload",
    )
    
    recipient: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True,
        comment="Recipient phone number",
    )
    
    sender: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True,
        comment="Sender phone number or ID",
    )
    
    message_type: Mapped[Optional[str]] = mapped_column(
        String(10),
        nullable=True,
        index=True,
        comment="Message type (SMS, MMS, RCS)",
    )
    
    status: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True,
        comment="Message status for status events",
    )
    
    # Processing information
    processed: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Whether this webhook has been processed",
    )
    
    processing_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Notes about webhook processing",
    )

    # Standard fields following the existing pattern
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this webhook record is active",
    )
    
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Whether this webhook record is deleted",
    )
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="When this webhook record was created",
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="When this webhook record was last updated",
    )
    
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When this webhook record was deleted",
    )

    def __repr__(self) -> str:
        return (
            f"<Webhook(id={self.id}, event_type={self.event_type}, "
            f"message_id={self.message_id}, recipient={self.recipient}, "
            f"processed={self.processed})>"
        )

    def __str__(self) -> str:
        return (
            f"Webhook: {self.event_type} for message {self.message_id} "
            f"to {self.recipient} (processed: {self.processed})"
        )
