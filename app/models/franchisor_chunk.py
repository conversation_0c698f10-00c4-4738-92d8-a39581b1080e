"""
SQLAlchemy model for franchisor_chunks table
"""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID, uuid4

from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector

from app.db.base_class import Base


class FranchisorChunk(Base):
    """
    Model for storing franchisor-specific document chunks with embeddings
    Used for franchisor-scoped RAG retrieval
    """
    __tablename__ = "franchisor_chunks"

    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    franchisor_id: Optional[UUID] = Column(
        PGUUID(as_uuid=True), 
        ForeignKey("franchisors.id", ondelete="CASCADE"),
        nullable=True,  # NULL for global chunks
        index=True
    )
    text: str = Column(Text, nullable=False, doc="Main text content of the chunk")
    source_url: Optional[str] = Column(Text, nullable=True, doc="Source URL or file path")
    source_title: Optional[str] = Column(Text, nullable=True, doc="Source document title")
    page_no: Optional[int] = Column(Integer, nullable=True, doc="Page number in source document")
    section: Optional[str] = Column(Text, nullable=True, doc="Section or chapter name")
    chunk_metadata: Dict[str, Any] = Column(
        "metadata",  # Keep the database column name as 'metadata'
        JSONB, 
        nullable=False, 
        default=dict,
        doc="Additional metadata as JSON"
    )
    embedding: list = Column(
        Vector(1536), 
        nullable=False,
        doc="1536-dimensional embedding vector"
    )
    created_at: datetime = Column(
        DateTime(timezone=True), 
        nullable=False, 
        default=datetime.utcnow
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), 
        nullable=False, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )

    # Relationships
    # franchisor = relationship("Franchisor", back_populates="chunks")  # Commented out to avoid circular import

    def __repr__(self) -> str:
        return f"<FranchisorChunk(id={self.id}, franchisor_id={self.franchisor_id}, text_length={len(self.text) if self.text else 0})>"

    @property
    def is_global(self) -> bool:
        """Check if this is a global chunk (franchisor_id is NULL)"""
        return self.franchisor_id is None

    @property
    def content_preview(self) -> str:
        """Get a preview of the text content (first 100 characters)"""
        if not self.text:
            return ""
        return self.text[:100] + "..." if len(self.text) > 100 else self.text

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": str(self.id),
            "franchisor_id": str(self.franchisor_id) if self.franchisor_id else None,
            "text": self.text,
            "source_url": self.source_url,
            "source_title": self.source_title,
            "page_no": self.page_no,
            "section": self.section,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_global": self.is_global,
            "content_preview": self.content_preview
        }
