"""
Holiday Model
SQLAlchemy model for holiday management
"""

from datetime import datetime, date, time
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import (
    String,
    Boolean,
    DateTime,
    Date,
    Time,
    Text,
    func,
    CheckConstraint,
)
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database.connection import Base


class Holiday(Base):
    """Holiday model for GrowthHive General Settings"""

    __tablename__ = "holiday"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Holiday type with constraint
    holiday_type: Mapped[str] = mapped_column(
        String(20), nullable=False, comment="Type of holiday: PREDEFINED or PERSONAL"
    )

    # Date and time fields
    date: Mapped[date] = mapped_column(Date, nullable=False, comment="Holiday date")
    all_day: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=True, comment="Whether holiday is all day"
    )
    start_time: Mapped[Optional[time]] = mapped_column(
        Time, nullable=True, comment="Start time if not all day"
    )
    end_time: Mapped[Optional[time]] = mapped_column(
        Time, nullable=True, comment="End time if not all day"
    )

    # Description
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="Holiday description"
    )

    # Status fields
    is_active: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=True, comment="Whether holiday is active"
    )
    is_deleted: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False, comment="Soft delete flag"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="Creation timestamp",
    )
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="Deletion timestamp"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="Last update timestamp",
    )

    # Table constraints
    __table_args__ = (
        CheckConstraint(
            "holiday_type IN ('PREDEFINED', 'PERSONAL')", name="ck_holiday_type"
        ),
        CheckConstraint(
            "all_day = true OR (start_time IS NOT NULL AND end_time IS NOT NULL)",
            name="ck_holiday_time_constraint",
        ),
    )

    def __repr__(self) -> str:
        return f"<Holiday(id={self.id}, date={self.date}, type={self.holiday_type}, all_day={self.all_day})>"

    def __str__(self) -> str:
        return f"Holiday on {self.date} ({self.holiday_type})"
