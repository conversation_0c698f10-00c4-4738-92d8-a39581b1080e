"""
Zoho Sync Models
Database models for tracking Zoho CRM synchronization
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.core.database.base import Base


class ZohoSyncLog(Base):
    """Log table for tracking Zoho sync operations"""
    __tablename__ = "zoho_sync_log"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    entity_type = Column(String(50), nullable=False)  # 'lead', 'franchisor', etc.
    entity_id = Column(UUID(as_uuid=True), nullable=False)  # ID in our system
    zoho_id = Column(String(255), nullable=True)  # ID in Zoho system
    operation = Column(String(20), nullable=False)  # 'create', 'update', 'delete'
    direction = Column(String(10), nullable=False)  # 'push', 'pull'
    status = Column(String(20), nullable=False)  # 'success', 'failed', 'pending'
    error_message = Column(Text, nullable=True)
    sync_data = Column(Text, nullable=True)  # JSON data that was synced
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<ZohoSyncLog(entity_type='{self.entity_type}', operation='{self.operation}', status='{self.status}')>"


class ZohoSyncStatus(Base):
    """Track sync status for entities"""
    __tablename__ = "zoho_sync_status"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    entity_type = Column(String(50), nullable=False)
    entity_id = Column(UUID(as_uuid=True), nullable=False)
    zoho_id = Column(String(255), nullable=True, unique=True)
    last_synced_at = Column(DateTime, nullable=True)
    sync_status = Column(String(50), default='pending')  # 'synced', 'pending', 'failed'
    needs_push = Column(Boolean, default=False)  # True if local changes need to be pushed
    needs_pull = Column(Boolean, default=False)  # True if Zoho changes need to be pulled
    last_modified_local = Column(DateTime, nullable=True)
    last_modified_zoho = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<ZohoSyncStatus(entity_type='{self.entity_type}', sync_status='{self.sync_status}')>"


class ZohoSyncSettings(Base):
    """Global sync settings and last sync timestamps"""
    __tablename__ = "zoho_sync_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    setting_key = Column(String(100), nullable=False, unique=True)
    setting_value = Column(Text, nullable=True)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<ZohoSyncSettings(setting_key='{self.setting_key}')>"
