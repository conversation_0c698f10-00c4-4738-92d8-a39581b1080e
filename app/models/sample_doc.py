"""
Sample Document model for storing document references
"""

from sqlalchemy import Column, String, Text
from sqlalchemy.dialects.postgresql import UUID
from app.core.database.connection import Base
import uuid


class SampleDoc(Base):
    """Sample Document model for storing document references"""

    __tablename__ = "sample_docs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    title = Column(String(255), nullable=True)
    url = Column(Text, nullable=True)

    def __repr__(self):
        return f"<SampleDoc(id={self.id}, title='{self.title}')>"
