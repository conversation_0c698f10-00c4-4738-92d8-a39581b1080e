"""Zoho CRM integration service"""
from typing import Dict, List, Any, Optional
import httpx
from app.core.config.settings import settings
from app.core.logging import logger

class ZohoCRMService:
    """Service for interacting with Zoho CRM API"""
    
    def __init__(self) -> None:
        """Initialize Zoho CRM service"""
        self.base_url = "https://www.zohoapis.com.au/crm/v2"
        self.access_token: Optional[str] = None

    async def get_access_token(self) -> Optional[str]:
        """Get access token using refresh token
        
        Returns:
            Optional[str]: Access token if successful, None otherwise
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://accounts.zoho.com.au/oauth/v2/token",
                    data={
                        "refresh_token": settings.zoho_refresh_token,
                        "client_id": settings.zoho_client_id,
                        "client_secret": settings.zoho_client_secret,
                        "grant_type": "refresh_token"
                    }
                )
                data = response.json()
                self.access_token = data.get("access_token")
                return self.access_token
        except Exception as e:
            logger.error(f"Failed to get Zoho access token: {str(e)}")
            return None

    async def fetch_leads(self) -> List[Dict[str, Any]]:
        """Fetch leads from Zoho CRM
        
        Returns:
            List[Dict[str, Any]]: List of leads from Zoho CRM
        """
        if not self.access_token:
            await self.get_access_token()

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/Leads",
                    headers={"Authorization": f"Zoho-oauthtoken {self.access_token}"}
                )
                data = response.json()
                return data.get("data", [])
        except Exception as e:
            logger.error(f"Failed to fetch leads from Zoho: {str(e)}")
            return []

    async def update_lead(self, zoho_lead_id: str, update_data: Dict[str, Any]) -> bool:
        """Update lead in Zoho CRM
        
        Args:
            zoho_lead_id (str): Zoho CRM lead ID
            update_data (Dict[str, Any]): Data to update
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        if not self.access_token:
            await self.get_access_token()

        try:
            async with httpx.AsyncClient() as client:
                response = await client.put(
                    f"{self.base_url}/Leads/{zoho_lead_id}",
                    headers={"Authorization": f"Zoho-oauthtoken {self.access_token}"},
                    json={"data": [update_data]}
                )
                return response.status_code == 200
        except Exception as e:
            logger.error(f"Failed to update lead in Zoho: {str(e)}")
            return False
