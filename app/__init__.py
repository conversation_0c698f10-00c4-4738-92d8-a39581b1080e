# Use standardized logging from core module
from app.core.logging import logger


class AppLogger:
    """Application logger for Growth Hive Auth API"""

    def __init__(self):
        self.logger = logger

    async def info(self, message: str, function: str = ""):
        """Log info message"""
        log_message = f"[{function}] {message}" if function else message
        self.logger.info(log_message)
    
    async def warning(self, message: str, function: str = ""):
        """Log warning message"""
        log_message = f"[{function}] {message}" if function else message
        self.logger.warning(log_message)
    
    async def error(self, message: str, function: str = ""):
        """Log error message"""
        log_message = f"[{function}] {message}" if function else message
        self.logger.error(log_message)
    
    async def exception(self, raw_exception: Exception, message_key: str, function: str = ""):
        """Log exception"""
        log_message = f"[{function}] {message_key}: {str(raw_exception)}" if function else f"{message_key}: {str(raw_exception)}"
        self.logger.exception(log_message)


# Global app logger instance
app_logger = AppLogger()


class LoggerUtility:
    _instance = None

    def __new__(cls, *args, **kwargs):
        """ Create singleton instance """
        if not cls._instance:
            cls._instance = super(LoggerUtility, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    @staticmethod
    async def exception(raw_exception, message_key: str, function: str, request_id: str = None):
        """ Log exception with standardized format """
        logger.exception(
            f"[EXCEPTION] {message_key} in {function}: {str(raw_exception)}",
            extra={
                "context": {
                    "message_key": message_key,
                    "function": function,
                    "exception_type": type(raw_exception).__name__
                },
                "request_id": request_id or "N/A"
            }
        )

    @staticmethod
    async def error(message_key: str, function: str, extra_message: str = None, request_id: str = None):
        """ Log error with standardized format """
        logger.error(
            f"[ERROR] {message_key} in {function}: {extra_message or 'No additional message'}",
            extra={
                "context": {
                    "message_key": message_key,
                    "function": function,
                    "extra_message": extra_message
                },
                "request_id": request_id or "N/A"
            }
        )

    @staticmethod
    async def warning(message: str, function: str, request_id: str = None):
        """ Log warning with standardized format """
        logger.warning(
            f"[WARNING] {function}: {message}",
            extra={
                "context": {"function": function},
                "request_id": request_id or "N/A"
            }
        )

    @staticmethod
    async def info(message: str, function: str, request_id: str = None):
        """ Log info with standardized format """
        logger.info(
            f"[INFO] {function}: {message}",
            extra={
                "context": {"function": function},
                "request_id": request_id or "N/A"
            }
        )

    @staticmethod
    async def debug(message: str, function: str, request_id: str = None):
        """ Log debug with standardized format """
        logger.debug(
            f"[DEBUG] {function}: {message}",
            extra={
                "context": {"function": function},
                "request_id": request_id or "N/A"
            }
        )

    @staticmethod
    async def critical(message: str, function: str, request_id: str = None):
        """ Log critical with standardized format """
        logger.critical(
            f"[CRITICAL] {function}: {message}",
            extra={
                "context": {"function": function},
                "request_id": request_id or "N/A"
            }
        )


# Singleton instances
app_logger = LoggerUtility() 