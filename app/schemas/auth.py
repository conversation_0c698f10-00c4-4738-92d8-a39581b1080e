"""
Authentication Schemas
Pydantic models for authentication requests and responses
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, Field, constr

class UserBase(BaseModel):
    """Base user schema with common fields"""
    email: Optional[EmailStr] = Field(None, description="User's email address")
    mobile: Optional[constr(min_length=10, max_length=15)] = Field(
        None, 
        description="User's mobile number"
    )
    first_name: constr(min_length=1, max_length=50) = Field(
        ..., 
        description="User's first name"
    )
    last_name: constr(min_length=1, max_length=50) = Field(
        ..., 
        description="User's last name"
    )

class UserCreate(UserBase):
    """Schema for user registration"""
    password: constr(min_length=8) = Field(
        ..., 
        description="User's password (min 8 characters)"
    )
    confirm_password: constr(min_length=8) = Field(
        ..., 
        description="Password confirmation"
    )

class UserLogin(BaseModel):
    """Schema for user login"""
    identifier: str = Field(
        ..., 
        description="Email or mobile number"
    )
    password: str = Field(
        ..., 
        description="User's password"
    )
    remember_me: bool = Field(
        False,
        description="Whether to create a long-lived refresh token"
    )

class Token(BaseModel):
    """Schema for authentication tokens"""
    access_token: str = Field(
        ..., 
        description="JWT access token"
    )
    refresh_token: str = Field(
        ..., 
        description="JWT refresh token"
    )
    token_type: str = Field(
        "bearer",
        description="Token type"
    )

class ForgotPasswordRequest(BaseModel):
    email: EmailStr = Field(..., description="User's email to send reset link")

class ResetPasswordRequest(BaseModel):
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., description="New password")

class AdminForgotPasswordRequest(BaseModel):
    email: EmailStr = Field(..., description="Admin's email to send reset link")

class AdminResetPasswordRequest(BaseModel):
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., description="New password")

class TokenPayload(BaseModel):
    """Schema for JWT token payload"""
    sub: str = Field(
        ..., 
        description="Subject (user ID)"
    )
    exp: int = Field(
        ..., 
        description="Expiration timestamp"
    )
    type: str = Field(
        ..., 
        description="Token type (access/refresh)"
    )

class RefreshToken(BaseModel):
    """Schema for token refresh request"""
    refresh_token: str = Field(
        ..., 
        description="Valid refresh token"
    ) 