"""
General Message Schemas
Pydantic models for general message API requests and responses
"""

from typing import Optional
from pydantic import BaseModel, Field
from enum import Enum
import uuid


class MessageType(str, Enum):
    """Message type enumeration."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"
    NOTIFICATION = "notification"
    ALERT = "alert"


class GeneralMessageUpdateRequest(BaseModel):
    """Request model for updating a general message by message_type."""
    message: str = Field(..., description="Updated message content", example="Updated system message")


class GeneralMessageResponse(BaseModel):
    """Response model for general message details."""
    id: uuid.UUID = Field(..., description="Message ID", example="550e8400-e29b-41d4-a716-************")
    message: Optional[str] = Field(None, description="Message content", example="Welcome to the system!")
    message_type: Optional[str] = Field(None, description="Type of message", example="info")

    class Config:
        from_attributes = True
