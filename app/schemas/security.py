"""
Security schemas for the application
"""
from datetime import datetime
from typing import Optional, List, Dict
from pydantic import BaseModel, EmailStr, constr, Field

class Token(BaseModel):
    """Token schema"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenData(BaseModel):
    """Token data schema"""
    sub: str  # Subject (user ID)
    exp: datetime  # Expiration time
    type: str  # Token type (access or refresh)
    jti: str  # JWT ID

class LoginRequest(BaseModel):
    """Login request schema"""
    email: EmailStr
    password: str
    remember_me: bool = False

class RegisterRequest(BaseModel):
    """Register request schema"""
    email: EmailStr
    password: constr(min_length=8, max_length=128)
    first_name: Optional[str] = None
    last_name: Optional[str] = None

class ChangePasswordRequest(BaseModel):
    """Change password request schema"""
    current_password: str
    new_password: constr(min_length=8, max_length=128)

class ForgotPasswordRequest(BaseModel):
    """Forgot password request schema"""
    email: EmailStr

class ResetPasswordRequest(BaseModel):
    """Reset password request schema"""
    token: str
    new_password: constr(min_length=8, max_length=128)

class VerifyEmailRequest(BaseModel):
    """Verify email request schema"""
    token: str

class ResendVerificationRequest(BaseModel):
    """Resend verification request schema"""
    email: EmailStr

class SessionInfo(BaseModel):
    """Session info schema"""
    id: int
    ip_address: str
    user_agent: str
    is_active: bool
    created_at: datetime
    last_activity: datetime

class SessionList(BaseModel):
    """Session list schema"""
    sessions: list[SessionInfo]
    total: int

class RateLimitInfo(BaseModel):
    """Rate limit info schema"""
    limit: int
    remaining: int
    reset_time: int

class SecuritySettings(BaseModel):
    """
    Security settings for the application
    """
    # JWT settings
    secret_key: str = Field(..., description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(default=30, description="JWT access token expire minutes")
    jwt_refresh_token_expire_days: int = Field(default=7, description="JWT refresh token expire days")
    
    # Password settings
    password_min_length: int = Field(default=8, description="Minimum password length")
    password_max_length: int = Field(default=100, description="Maximum password length")
    password_require_uppercase: bool = Field(default=True, description="Require uppercase in password")
    password_require_lowercase: bool = Field(default=True, description="Require lowercase in password")
    password_require_numbers: bool = Field(default=True, description="Require numbers in password")
    password_require_special_chars: bool = Field(default=True, description="Require special characters in password")
    
    # Security headers
    security_headers: Dict[str, str] = Field(
        default={
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        },
        description="Security headers to add to responses"
    )
    
    # CORS settings
    cors_origins: List[str] = Field(default=["*"], description="CORS allowed origins")
    cors_origin_regex: Optional[str] = Field(default=None, description="CORS allowed origin regex")
    cors_methods: List[str] = Field(default=["*"], description="CORS allowed methods")
    cors_headers: List[str] = Field(default=["*"], description="CORS allowed headers")
    
    class Config:
        """Pydantic config"""
        arbitrary_types_allowed = True 