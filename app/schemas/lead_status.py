"""
Lead Status schemas for request/response validation
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

from app.schemas.base_response import StandardResponse


class StatusFromMessageRequest(BaseModel):
    """Request schema for updating status from message"""
    message: str = Field(..., description="Message content to analyze", min_length=1, max_length=5000)
    channel: str = Field(default="sms", description="Communication channel", example="sms")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context for classification")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Not interested, please stop messaging.",
                "channel": "sms",
                "context": {
                    "territory_available": True,
                    "min_budget": 50000
                }
            }
        }


class ManualStatusUpdateRequest(BaseModel):
    """Request schema for manual status updates"""
    status_name: str = Field(..., description="New status name", min_length=1, max_length=100)
    reason: str = Field(..., description="Reason for the change", min_length=1, max_length=500)
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_name": "Qualified",
                "reason": "Lead showed strong interest during phone call"
            }
        }


class StatusUpdateResult(BaseModel):
    """Response schema for status update results"""
    old_status: Optional[str] = Field(None, description="Previous status name")
    new_status: str = Field(..., description="New status name")
    changed: bool = Field(..., description="Whether the status actually changed")
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0)", ge=0.0, le=1.0)
    rationale: str = Field(..., description="Explanation for the status determination")
    lead_id: str = Field(..., description="Lead ID")
    history_id: Optional[str] = Field(None, description="History record ID if status changed")
    method: Optional[str] = Field(None, description="Classification method used")
    
    class Config:
        json_schema_extra = {
            "example": {
                "old_status": "New Lead",
                "new_status": "Not Interested",
                "changed": True,
                "confidence": 0.99,
                "rationale": "Message contains explicit opt-out phrase",
                "lead_id": "550e8400-e29b-41d4-a716-446655440000",
                "history_id": "660e8400-e29b-41d4-a716-446655440001",
                "method": "rule_based"
            }
        }


class StatusHistoryRecord(BaseModel):
    """Schema for status history records"""
    id: str = Field(..., description="History record ID")
    from_status: Optional[str] = Field(None, description="Previous status name")
    to_status: str = Field(..., description="New status name")
    reason: str = Field(..., description="Reason for the change")
    confidence: Optional[float] = Field(None, description="Confidence score", ge=0.0, le=1.0)
    rationale: Optional[str] = Field(None, description="Detailed rationale")
    message_excerpt: Optional[str] = Field(None, description="Message excerpt that triggered change")
    changed_by: str = Field(..., description="Who/what made the change")
    source: str = Field(..., description="Source of the change")
    review_needed: bool = Field(..., description="Whether this change needs review")
    created_at: str = Field(..., description="When the change occurred")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "770e8400-e29b-41d4-a716-446655440002",
                "from_status": "New Lead",
                "to_status": "Not Interested",
                "reason": "Message analysis: rule_based",
                "confidence": 0.99,
                "rationale": "Message contains explicit opt-out phrase",
                "message_excerpt": "Not interested, please stop messaging.",
                "changed_by": "system",
                "source": "andy",
                "review_needed": False,
                "created_at": "2024-01-15T10:30:00.000000"
            }
        }


class StatusHistoryResponse(BaseModel):
    """Response schema for status history"""
    lead_id: str = Field(..., description="Lead ID")
    history: List[StatusHistoryRecord] = Field(..., description="List of status changes")
    total_count: int = Field(..., description="Total number of history records")
    
    class Config:
        json_schema_extra = {
            "example": {
                "lead_id": "550e8400-e29b-41d4-a716-446655440000",
                "history": [],
                "total_count": 0
            }
        }


class LeadStatusInfo(BaseModel):
    """Schema for lead status information"""
    id: str = Field(..., description="Status ID")
    name: str = Field(..., description="Status name")
    colour: str = Field(..., description="Status color (hex code)")
    is_active: bool = Field(..., description="Whether status is active")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "880e8400-e29b-41d4-a716-446655440003",
                "name": "Qualified",
                "colour": "#007bff",
                "is_active": True
            }
        }


class AvailableStatusesResponse(BaseModel):
    """Response schema for available statuses"""
    statuses: List[LeadStatusInfo] = Field(..., description="List of available statuses")
    total_count: int = Field(..., description="Total number of statuses")
    
    class Config:
        json_schema_extra = {
            "example": {
                "statuses": [],
                "total_count": 0
            }
        }


# Response wrappers using StandardResponse
class StatusUpdateSuccessResponse(StandardResponse[StatusUpdateResult]):
    """Success response for status updates"""
    pass


class StatusHistorySuccessResponse(StandardResponse[StatusHistoryResponse]):
    """Success response for status history"""
    pass


class AvailableStatusesSuccessResponse(StandardResponse[AvailableStatusesResponse]):
    """Success response for available statuses"""
    pass


class ValidationTestRequest(BaseModel):
    """Request schema for testing status validation"""
    status_name: str = Field(..., description="Status name to validate", min_length=1, max_length=100)
    
    class Config:
        json_schema_extra = {
            "example": {
                "status_name": "not interested"
            }
        }


class ValidationTestResult(BaseModel):
    """Result of status validation test"""
    input_status: str = Field(..., description="Original input status")
    normalized_status: str = Field(..., description="Normalized canonical status")
    confidence: float = Field(..., description="Normalization confidence", ge=0.0, le=1.0)
    is_valid: bool = Field(..., description="Whether the status is valid")
    feedback: str = Field(..., description="Validation feedback message")
    
    class Config:
        json_schema_extra = {
            "example": {
                "input_status": "not interested",
                "normalized_status": "Not Interested",
                "confidence": 1.0,
                "is_valid": True,
                "feedback": "Valid status: 'Not Interested'"
            }
        }


class ValidationTestSuccessResponse(StandardResponse[ValidationTestResult]):
    """Success response for validation tests"""
    pass
