"""
Pydantic schemas for session management
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field

class SessionBase(BaseModel):
    """Base session schema"""
    user_agent: str = Field(..., description="User agent string")
    ip_address: Optional[str] = Field(None, description="IP address of the client")

class SessionCreate(SessionBase):
    """Schema for creating a new session"""
    user_id: str = Field(..., description="ID of the user")
    expires_at: datetime = Field(..., description="Session expiration timestamp")

class SessionResponse(SessionBase):
    """Schema for session response"""
    id: str = Field(..., description="Session ID")
    user_id: str = Field(..., description="ID of the user")
    last_activity: datetime = Field(..., description="Last activity timestamp")
    expires_at: datetime = Field(..., description="Session expiration timestamp")
    created_at: datetime = Field(..., description="Session creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

class SessionList(BaseModel):
    """Schema for list of sessions"""
    sessions: list[SessionResponse] = Field(..., description="List of user sessions")
    total: int = Field(..., description="Total number of sessions") 