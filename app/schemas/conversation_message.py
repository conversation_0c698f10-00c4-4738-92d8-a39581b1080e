"""
Conversation Message Schemas
Pydantic models for conversation message API requests and responses
"""

from datetime import datetime
from typing import Optional, List, Literal
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum

from app.schemas.base_response import (
    SuccessResponse,
    PaginationInfo
)


class SenderType(str, Enum):
    """Sender type enumeration"""
    LEAD = "lead"
    SYSTEM = "system"


class ConversationMessageCreateRequest(BaseModel):
    """Request model for creating a conversation message"""
    lead_id: UUID = Field(..., description="ID of the lead who is part of this conversation")
    franchisor_id: Optional[UUID] = Field(None, description="ID of the franchisor associated with this conversation")
    sender: SenderType = Field(..., description="Who sent the message: 'lead' or 'system'")
    message: str = Field(..., description="The actual message content", min_length=1, max_length=10000)

    @field_validator('message')
    @classmethod
    def validate_message_not_empty(cls, v):
        """Validate that message is not empty or just whitespace"""
        if not v or not v.strip():
            raise ValueError("Message cannot be empty or contain only whitespace")
        return v.strip()

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "lead_id": "123e4567-e89b-12d3-a456-************",
                "franchisor_id": "456e7890-e89b-12d3-a456-************",
                "sender": "lead",
                "message": "I'm interested in your franchise opportunity."
            }
        }
    )


class ConversationMessageUpdateRequest(BaseModel):
    """Request model for updating a conversation message"""
    message: Optional[str] = Field(None, description="Updated message content", min_length=1, max_length=10000)
    is_active: Optional[bool] = Field(None, description="Whether the message is active")

    @field_validator('message')
    @classmethod
    def validate_message_not_empty(cls, v):
        """Validate that message is not empty or just whitespace"""
        if v is not None and (not v or not v.strip()):
            raise ValueError("Message cannot be empty or contain only whitespace")
        return v.strip() if v else v

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "Updated message content",
                "is_active": True
            }
        }
    )


class ConversationMessageResponse(BaseModel):
    """Response model for conversation message data"""
    id: UUID = Field(..., description="Unique message identifier")
    lead_id: UUID = Field(..., description="ID of the lead who is part of this conversation")
    lead_first_name: Optional[str] = Field(None, description="First name of the lead")
    lead_last_name: Optional[str] = Field(None, description="Last name of the lead")
    franchisor_id: Optional[UUID] = Field(None, description="ID of the franchisor associated with this conversation")
    franchisor_name: Optional[str] = Field(None, description="Name of the franchisor associated with this conversation")
    sender: SenderType = Field(..., description="Who sent the message: 'lead' or 'system'")
    message: str = Field(..., description="The actual message content")
    is_active: bool = Field(..., description="Whether the message is active")
    is_deleted: bool = Field(..., description="Whether the message has been soft deleted")
    created_at: datetime = Field(..., description="When the message was created")
    updated_at: datetime = Field(..., description="When the message was last updated")
    deleted_at: Optional[datetime] = Field(None, description="When the message was soft deleted")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "lead_id": "456e7890-e89b-12d3-a456-************",
                "lead_first_name": "John",
                "lead_last_name": "Doe",
                "franchisor_id": "789e0123-e89b-12d3-a456-426614174002",
                "franchisor_name": "Coochie Hydrogreen",
                "sender": "lead",
                "message": "I'm interested in your franchise opportunity.",
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T12:00:00.000000Z",
                "updated_at": "2024-01-01T12:00:00.000000Z",
                "deleted_at": None
            }
        }
    )


class ConversationMessageListResponse(BaseModel):
    """Response model for conversation message list"""
    items: List[ConversationMessageResponse] = Field(..., description="List of conversation messages")
    pagination: PaginationInfo = Field(..., description="Pagination information")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "lead_id": "456e7890-e89b-12d3-a456-************",
                        "lead_first_name": "John",
                        "lead_last_name": "Doe",
                        "franchisor_id": "789e0123-e89b-12d3-a456-426614174002",
                        "franchisor_name": "Coochie Hydrogreen",
                        "sender": "lead",
                        "message": "I'm interested in your franchise opportunity.",
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T12:00:00.000000Z",
                        "updated_at": "2024-01-01T12:00:00.000000Z",
                        "deleted_at": None
                    }
                ],
                "pagination": {
                    "current_page": 1,
                    "items_per_page": 20,
                    "total_items": 1,
                    "total_pages": 1
                }
            }
        }
    )


class ConversationMessageBulkCreateRequest(BaseModel):
    """Request model for creating multiple conversation messages in bulk"""
    messages: List[ConversationMessageCreateRequest] = Field(
        ..., 
        description="List of conversation messages to create",
        min_length=1,
        max_length=100
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "messages": [
                    {
                        "lead_id": "123e4567-e89b-12d3-a456-************",
                        "franchisor_id": "456e7890-e89b-12d3-a456-************",
                        "sender": "lead",
                        "message": "I'm interested in your franchise."
                    },
                    {
                        "lead_id": "123e4567-e89b-12d3-a456-************",
                        "franchisor_id": "456e7890-e89b-12d3-a456-************",
                        "sender": "system",
                        "message": "Great! May I know your full name?"
                    }
                ]
            }
        }
    )


class ConversationMessageBulkCreateResponse(BaseModel):
    """Response model for bulk conversation message creation"""
    created_messages: List[ConversationMessageResponse] = Field(..., description="Successfully created messages")
    total_created: int = Field(..., description="Total number of messages created")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "created_messages": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "lead_id": "456e7890-e89b-12d3-a456-************",
                        "franchisor_id": "789e0123-e89b-12d3-a456-426614174002",
                        "sender": "lead",
                        "message": "I'm interested in your franchise.",
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T12:00:00.000000Z",
                        "updated_at": "2024-01-01T12:00:00.000000Z",
                        "deleted_at": None
                    }
                ],
                "total_created": 2
            }
        }
    )


class ConversationStatsResponse(BaseModel):
    """Response model for conversation statistics"""
    total_messages: int = Field(..., description="Total number of messages")
    lead_messages: int = Field(..., description="Number of messages from leads")
    system_messages: int = Field(..., description="Number of messages from system")
    messages_by_sender: dict = Field(..., description="Messages grouped by sender type")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_messages": 150,
                "lead_messages": 75,
                "system_messages": 75,
                "messages_by_sender": {
                    "lead": 75,
                    "system": 75
                }
            }
        }
    )


# AI Agent specific schemas for conversation processing
class ConversationParseRequest(BaseModel):
    """Request model for AI agent to parse conversation data"""
    conversation_data: List[dict] = Field(
        ..., 
        description="Raw conversation data from webhook",
        min_length=1
    )
    lead_id: Optional[UUID] = Field(None, description="Lead ID if known")
    franchisor_id: Optional[UUID] = Field(None, description="Franchisor ID if known")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "conversation_data": [
                    {
                        "message": "I'm interested in your franchise.",
                        "timestamp": "2024-01-01T12:00:00Z",
                        "sender_info": "+1234567890"
                    },
                    {
                        "message": "Great! May I know your full name?",
                        "timestamp": "2024-01-01T12:01:00Z",
                        "sender_info": "system"
                    }
                ],
                "lead_id": "123e4567-e89b-12d3-a456-************",
                "franchisor_id": "456e7890-e89b-12d3-a456-************"
            }
        }
    )


class ConversationParseResponse(BaseModel):
    """Response model for AI agent conversation parsing"""
    parsed_messages: List[ConversationMessageCreateRequest] = Field(..., description="Parsed conversation messages")
    total_parsed: int = Field(..., description="Total number of messages parsed")
    processing_metadata: dict = Field(..., description="Processing metadata from AI agent")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "parsed_messages": [
                    {
                        "lead_id": "123e4567-e89b-12d3-a456-************",
                        "franchisor_id": "456e7890-e89b-12d3-a456-************",
                        "sender": "lead",
                        "message": "I'm interested in your franchise."
                    }
                ],
                "total_parsed": 2,
                "processing_metadata": {
                    "processing_time_ms": 150,
                    "ai_confidence": 0.95,
                    "detected_patterns": ["greeting", "interest_expression"]
                }
            }
        }
    )


# Success response types
ConversationMessageSuccessResponse = SuccessResponse[ConversationMessageResponse]
ConversationMessageListSuccessResponse = SuccessResponse[ConversationMessageListResponse]
ConversationMessageBulkSuccessResponse = SuccessResponse[ConversationMessageBulkCreateResponse]
ConversationStatsSuccessResponse = SuccessResponse[ConversationStatsResponse]
ConversationParseSuccessResponse = SuccessResponse[ConversationParseResponse]
