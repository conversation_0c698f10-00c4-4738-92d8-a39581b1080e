"""
Question Classification Schemas
Request and response models for question classification API
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime

from app.schemas.base_response import ResponseMessage


class QuestionClassificationRequest(BaseModel):
    """Request model for question classification"""
    question_text: str = Field(..., description="The question to classify", example="What's the ROI of this franchise?")
    lead_id: Optional[str] = Field(None, description="Associated lead ID", example="550e8400-e29b-41d4-a716-446655440000")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID", example="569976f2-d845-4615-8a91-96e18086adbe")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context information", example={"brochure_text": "Franchise information..."})

    class Config:
        from_attributes = True


class BatchQuestionClassificationRequest(BaseModel):
    """Request model for batch question classification"""
    questions: List[QuestionClassificationRequest] = Field(..., description="List of questions to classify")

    class Config:
        from_attributes = True


class QuestionClassificationAnalysis(BaseModel):
    """Analysis details for question classification"""
    needs_escalation: bool = Field(..., description="Whether the question needs escalation")
    reason: str = Field(..., description="Reason for classification decision")
    question_length: int = Field(..., description="Number of words in the question")
    analysis: str = Field(..., description="Analysis summary")

    class Config:
        from_attributes = True


class QuestionClassificationResult(BaseModel):
    """Result data for question classification"""
    classification: str = Field(..., description="Classification type: 'answerable' or 'escalation'", example="answerable")
    table: str = Field(..., description="Database table used", example="question_bank")
    question: str = Field(..., description="The original question", example="What's the ROI of this franchise?")
    sql_statement: str = Field(..., description="Generated SQL INSERT statement")
    analysis: QuestionClassificationAnalysis = Field(..., description="Classification analysis details")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    
    # Fields specific to answerable questions
    answer: Optional[str] = Field(None, description="RAG system answer (for answerable questions)")
    
    # Fields specific to escalation questions
    reason: Optional[str] = Field(None, description="Escalation reason (for escalation questions)")
    attempted_answer: Optional[str] = Field(None, description="Attempted RAG answer (for escalation questions)")

    class Config:
        from_attributes = True


class QuestionClassificationResponse(BaseModel):
    """Response model for question classification"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: QuestionClassificationResult = Field(..., description="Classification result data")

    class Config:
        from_attributes = True


class BatchQuestionResult(BaseModel):
    """Individual result in batch classification"""
    index: int = Field(..., description="Index of the question in the batch")
    question: str = Field(..., description="The original question")
    result: QuestionClassificationResponse = Field(..., description="Classification result")

    class Config:
        from_attributes = True


class BatchQuestionClassificationResult(BaseModel):
    """Result data for batch question classification"""
    total_questions: int = Field(..., description="Total number of questions processed")
    successful_classifications: int = Field(..., description="Number of successful classifications")
    failed_classifications: int = Field(..., description="Number of failed classifications")
    results: List[BatchQuestionResult] = Field(..., description="Individual classification results")

    class Config:
        from_attributes = True


class BatchQuestionClassificationResponse(BaseModel):
    """Response model for batch question classification"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: BatchQuestionClassificationResult = Field(..., description="Batch classification result data")

    class Config:
        from_attributes = True


class QuestionExample(BaseModel):
    """Example question for documentation"""
    question: str = Field(..., description="Example question", example="What's the ROI of this franchise?")
    reason: str = Field(..., description="Reason for classification", example="Can be answered from brochure financial information")
    table: str = Field(..., description="Target database table", example="question_bank")

    class Config:
        from_attributes = True


class ClassificationExamples(BaseModel):
    """Examples of question classifications"""
    answerable_questions: List[QuestionExample] = Field(..., description="Examples of answerable questions")
    escalation_questions: List[QuestionExample] = Field(..., description="Examples of escalation questions")
    sql_examples: Dict[str, str] = Field(..., description="SQL statement examples")

    class Config:
        from_attributes = True


class ClassificationExamplesResponse(BaseModel):
    """Response model for classification examples"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: ClassificationExamples = Field(..., description="Classification examples")

    class Config:
        from_attributes = True
