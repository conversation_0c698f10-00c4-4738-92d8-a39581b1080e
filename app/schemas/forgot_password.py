"""
Forgot Password Schemas
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
import re


class ForgotPasswordRequest(BaseModel):
    """Request schema for forgot password"""
    email: EmailStr = Field(..., description="User's email address")
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class ForgotPasswordResponse(BaseModel):
    """Response schema for forgot password"""
    message: str = Field(..., description="Success message")
    user_id: str = Field(..., description="User ID for OTP verification")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "OTP sent successfully to your email",
                "user_id": "123e4567-e89b-12d3-a456-426614174000"
            }
        }


class VerifyOTPRequest(BaseModel):
    """Request schema for OTP verification"""
    user_id: str = Field(..., description="User ID received from forgot password")
    otp: str = Field(..., min_length=6, max_length=6, description="6-digit OTP code")

    @validator('otp')
    def validate_otp(cls, v):
        if not v.isdigit():
            raise ValueError('OTP must contain only digits')
        if len(v) != 6:
            raise ValueError('OTP must be exactly 6 digits')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "otp": "123456"
            }
        }


class VerifyOTPResponse(BaseModel):
    """Response schema for OTP verification"""
    message: str = Field(..., description="Success message")
    reset_code: str = Field(..., description="Reset code for password reset")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "OTP verified successfully",
                "reset_code": "abc123def456ghi789jkl012mno345pq"
            }
        }


class ResetPasswordRequest(BaseModel):
    """Request schema for password reset"""
    reset_code: str = Field(..., description="Reset code received from OTP verification")
    user_id: str = Field(..., description="User ID")
    new_password: str = Field(
        ..., 
        min_length=8, 
        max_length=128, 
        description="New password (minimum 8 characters)"
    )
    confirm_password: str = Field(
        ..., 
        min_length=8, 
        max_length=128, 
        description="Confirm new password"
    )
    
    @validator('new_password')
    def validate_password_strength(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        # Check for at least one uppercase letter
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        
        # Check for at least one lowercase letter
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        
        # Check for at least one digit
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "reset_code": "abc123def456ghi789jkl012mno345pq",
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "new_password": "NewPassword123",
                "confirm_password": "NewPassword123"
            }
        }


class ResetPasswordResponse(BaseModel):
    """Response schema for password reset"""
    message: str = Field(..., description="Success message")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Password reset successfully"
            }
        }
