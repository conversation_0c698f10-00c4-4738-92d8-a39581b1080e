"""
Sample Document schemas for request and response models
"""

from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict

from app.schemas.base_response import SuccessResponse


class SampleDocResponse(BaseModel):
    """Response model for sample document data"""
    id: str = Field(..., description="Unique document identifier")
    title: Optional[str] = Field(None, description="Document title")
    url: Optional[str] = Field(None, description="Document URL")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "title": "Sample Franchisor CSV",
                "url": "https://example.com/sample-franchisor.csv"
            }
        }
    )


class SampleDocListResponse(BaseModel):
    """Response model for list of sample documents"""
    items: List[SampleDocResponse] = Field(..., description="List of sample documents")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "title": "Sample Franchisor CSV",
                        "url": "https://example.com/sample-franchisor.csv"
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-426614174001",
                        "title": "Sample Leads CSV", 
                        "url": "https://example.com/sample-leads.csv"
                    }
                ]
            }
        }
    )


class SampleDocSuccessResponse(SuccessResponse):
    """Success response wrapper for sample documents"""
    data: SampleDocListResponse = Field(..., description="Sample documents data")
