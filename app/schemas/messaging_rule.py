"""
Messaging Rule Schemas
Pydantic models for messaging rule configuration API
"""

from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, model_validator


class MessagingRuleBase(BaseModel):
    """Base messaging rule schema (follow-up functionality removed)"""

    # Follow-up related fields have been removed from Andy AI
    pass

    # Validators removed for Andy AI - no follow-up functionality


class MessagingRuleCreateRequest(MessagingRuleBase):
    """Schema for creating a new messaging rule"""

    class Config:
        json_schema_extra = {
            "example": {
                "lead_init_delay_m": 120,
                "no_response_delay_m": 1440,
                "max_followups": 3,
            }
        }


class MessagingRuleUpdateRequest(BaseModel):
    """Schema for updating an existing messaging rule (follow-up functionality removed)"""

    # Follow-up related fields have been removed from Andy AI
    is_active: Optional[bool] = Field(
        None, description="Whether the messaging rule is active"
    )

    # Follow-up related validators have been removed

    # Follow-up related methods and validators have been removed

    class Config:
        json_schema_extra = {
            "example": {
                "is_active": True,
            }
        }


class MessagingRuleResponse(MessagingRuleBase):
    """Schema for messaging rule response"""

    id: UUID = Field(..., description="Messaging rule unique identifier")
    is_active: bool = Field(..., description="Whether the messaging rule is active")
    is_deleted: bool = Field(
        ..., description="Whether the messaging rule is soft deleted"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "deleted_at": None,
            }
        }


class MessagingRuleListResponse(BaseModel):
    """Schema for messaging rule list response"""

    items: list[MessagingRuleResponse] = Field(
        ..., description="List of messaging rules"
    )
    total_count: int = Field(..., description="Total number of messaging rules")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "lead_init_delay_h": 2,
                        "no_response_delay_h": 24,
                        "max_followups": 3,
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T00:00:00Z",
                        "deleted_at": None,
                    }
                ],
                "total_count": 1,
            }
        }
