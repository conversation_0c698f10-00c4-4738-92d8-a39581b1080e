"""
Pydantic schemas for refresh token management
"""
from datetime import datetime
from pydantic import BaseModel, Field

class RefreshTokenBase(BaseModel):
    """Base refresh token schema"""
    token: str = Field(..., description="Refresh token string")

class RefreshTokenCreate(RefreshTokenBase):
    """Schema for creating a new refresh token"""
    user_id: str = Field(..., description="ID of the user")
    expires_at: datetime = Field(..., description="Token expiration timestamp")

class RefreshTokenResponse(RefreshTokenBase):
    """Schema for refresh token response"""
    id: str = Field(..., description="Token ID")
    user_id: str = Field(..., description="ID of the user")
    expires_at: datetime = Field(..., description="Token expiration timestamp")
    created_at: datetime = Field(..., description="Token creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

class TokenPair(BaseModel):
    """Schema for access and refresh token pair"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds") 