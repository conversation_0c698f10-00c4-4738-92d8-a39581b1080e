"""
Dashboard schemas for request and response models
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime

from app.schemas.base_response import (
    StandardResponse,
    ResponseMessage
)


class DashboardCountsResponse(BaseModel):
    """Response model for dashboard counts"""
    total_leads: int = Field(..., description="Total number of leads")
    total_franchisors: int = Field(..., description="Total number of franchisors")
    total_sms: int = Field(..., description="Total SMS sent")
    total_meetings: int = Field(..., description="Total meetings scheduled")

    # Percentage changes from last month
    leads_change_percent: float = Field(..., description="Percentage change in leads from last month")
    franchisors_change_percent: float = Field(..., description="Percentage change in franchisors from last month")
    sms_change_percent: float = Field(..., description="Percentage change in SMS from last month")
    meetings_change_percent: float = Field(..., description="Percentage change in meetings from last month")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_leads": 1234,
                "total_franchisors": 156,
                "total_sms": 89,
                "total_meetings": 45,
                "leads_change_percent": 2.3,
                "franchisors_change_percent": -1.2,
                "sms_change_percent": 2.5,
                "meetings_change_percent": 1.8
            }
        }
    )


class RecentLeadActivity(BaseModel):
    """Recent lead activity model"""
    id: str = Field(..., description="Lead ID")
    full_name: str = Field(..., description="Lead full name")
    email: Optional[str] = Field(None, description="Lead email")
    phone: Optional[str] = Field(None, description="Lead phone")
    status: Optional[str] = Field(None, description="Lead status")
    created_at: datetime = Field(..., description="Creation timestamp")


class RecentFranchisorActivity(BaseModel):
    """Recent franchisor activity model"""
    id: str = Field(..., description="Franchisor ID")
    name: str = Field(..., description="Franchisor name")
    contactFirstName: Optional[str] = Field(None, description="Contact first name")
    contactLastName: Optional[str] = Field(None, description="Contact last name")
    email: Optional[str] = Field(None, description="Contact email")
    region: Optional[str] = Field(None, description="Franchisor region")
    is_active: bool = Field(..., description="Whether the franchisor is active")
    created_at: datetime = Field(..., description="Creation timestamp")


class RecentQuestionActivity(BaseModel):
    """Recent question activity model"""
    id: str = Field(..., description="Question ID")
    question_text: str = Field(..., description="Question text")
    category: Optional[str] = Field(None, description="Question category")
    is_active: bool = Field(..., description="Whether the question is active")
    created_at: datetime = Field(..., description="Creation timestamp")


class RecentExceptionActivity(BaseModel):
    """Recent exception activity model"""
    id: str = Field(..., description="Exception log ID")
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    endpoint: Optional[str] = Field(None, description="API endpoint where error occurred")
    user_id: Optional[str] = Field(None, description="User ID if available")
    severity: str = Field(default="error", description="Error severity level")
    created_at: datetime = Field(..., description="Exception timestamp")


class RecentActivityResponse(BaseModel):
    """Response model for recent activity"""
    latest_lead: Optional[RecentLeadActivity] = Field(None, description="Latest lead activity")
    latest_franchisor: Optional[RecentFranchisorActivity] = Field(None, description="Latest franchisor activity")
    latest_question: Optional[RecentQuestionActivity] = Field(None, description="Latest question activity")
    latest_exception: Optional[RecentExceptionActivity] = Field(None, description="Latest exception activity")


class DashboardCountsSuccessResponse(StandardResponse):
    """Success response for dashboard counts"""
    data: DashboardCountsResponse = Field(..., description="Dashboard counts data")

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": {
                    "title": "Dashboard Counts Retrieved",
                    "description": "Dashboard counts retrieved successfully"
                },
                "data": {
                    "total_leads": 150,
                    "total_franchisors": 45,
                    "total_sms": 0,
                    "total_meetings": 0
                },
                "error_code": 0
            }
        }
    }


class RecentActivitySuccessResponse(StandardResponse):
    """Success response for recent activity"""
    data: RecentActivityResponse = Field(..., description="Recent activity data")

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "message": {
                    "title": "Recent Activity Retrieved",
                    "description": "Recent activity retrieved successfully"
                },
                "data": {
                    "latest_lead": {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "full_name": "John Doe",
                        "email": "<EMAIL>",
                        "phone": "+1234567890",
                        "status": "new",
                        "created_at": "2025-07-15T10:30:00Z"
                    },
                    "latest_franchisor": {
                        "id": "123e4567-e89b-12d3-a456-426614174001",
                        "name": "Coffee Club Melbourne",
                        "contactFirstName": "Jane",
                        "contactLastName": "Smith",
                        "email": "<EMAIL>",
                        "region": "Melbourne",
                        "is_active": True,
                        "created_at": "2025-07-15T10:25:00Z"
                    },
                    "latest_question": {
                        "id": "123e4567-e89b-12d3-a456-426614174002",
                        "question_text": "What is the initial investment required?",
                        "category": "Investment",
                        "is_active": True,
                        "created_at": "2025-07-15T10:20:00Z"
                    },
                    "latest_exception": {
                        "id": "123e4567-e89b-12d3-a456-426614174003",
                        "error_type": "ValidationError",
                        "error_message": "Invalid email format provided",
                        "endpoint": "/api/leads/",
                        "user_id": "123e4567-e89b-12d3-a456-426614174004",
                        "severity": "warning",
                        "created_at": "2025-07-15T10:15:00Z"
                    }
                },
                "error_code": 0
            }
        }
    }
