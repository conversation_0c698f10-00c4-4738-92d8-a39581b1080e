"""Lead schemas for request/response validation"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
# Removed Decimal import - budget_preference is now string type
from enum import Enum

from app.schemas.base_response import ResponseMessage, PaginationInfo


class LeadStatus(str, Enum):
    """Lead qualification status"""
    NEW = "new"
    CONTACTED = "contacted"
    QUALIFIED = "qualified"
    UNQUALIFIED = "unqualified"
    CONVERTED = "converted"
    LOST = "lost"


class LeadCreateRequest(BaseModel):
    """Schema for creating a lead"""
    # Name fields (required)
    first_name: str = Field(..., description="Lead first name", example="<PERSON>")
    last_name: Optional[str] = Field(None, description="Lead last name", example="Doe")

    # Contact information
    phone: Optional[str] = Field(None, description="Lead phone number", example="+1234567890")
    mobile: Optional[str] = Field(None, description="Lead mobile number", example="+1234567890")
    email: Optional[EmailStr] = Field(None, description="Lead email address", example="<EMAIL>")
    location: Optional[str] = Field(None, description="Lead location", example="New York")
    postal_code: Optional[str] = Field(None, description="Postal code", example="10001")

    # Reference fields (using IDs)
    lead_source_id: Optional[str] = Field(None, description="Lead source ID", example="550e8400-e29b-41d4-a716-************")
    lead_status_id: Optional[str] = Field(None, description="Lead status ID", example="550e8400-e29b-41d4-a716-************")
    brand_preference: Optional[str] = Field(None, description="Brand preference UUID", example="550e8400-e29b-41d4-a716-************")
    budget_preference: Optional[str] = Field(None, description="Budget preference range", example="$75000-$100000")

    # Franchise buyer information fields
    franchise_interested_in: Optional[str] = Field(None, description="Franchise interested in", example="Food & Beverage")
    looking_for_business_opportunity_since: Optional[str] = Field(None, description="Looking for business opportunity since", example="6 months")
    skills: Optional[str] = Field(None, description="Skills", example="Business administration")
    looking_to_be_owner_operator: Optional[str] = Field(None, description="Looking to be owner operator", example="Passive")
    when_looking_to_start: Optional[str] = Field(None, description="When looking to start", example="Within 3 months")
    ethnic_background: Optional[str] = Field(None, description="Ethnic background", example="Asian")
    funds_to_invest: Optional[str] = Field(None, description="Funds to invest", example="$100K - $150K")
    eoi_nda_link: Optional[str] = Field(None, description="EOI/NDA link", example="https://example.com/eoi")
    work_background: Optional[str] = Field(None, description="Work background", example="Business Owner")
    motivation_to_enquire: Optional[str] = Field(None, description="Motivation to enquire", example="Diversification")
    funds_available: Optional[str] = Field(None, description="Funds available", example="$200K")
    motivation: Optional[str] = Field(None, description="Motivation", example="Looking for passive income")
    have_run_business_before: Optional[bool] = Field(None, description="Have run business before", example=True)
    have_mortgage: Optional[bool] = Field(None, description="Have mortgage", example=False)
    high_net_worth: Optional[str] = Field(None, description="High net worth", example="$500K+")

    # System fields
    zoho_lead_id: Optional[str] = Field(None, description="Zoho CRM lead ID")
    is_active: Optional[bool] = Field(True, description="Whether the lead is active")

    class Config:
        from_attributes = True


class LeadUpdateRequest(BaseModel):
    """Schema for updating a lead"""
    # Name fields
    first_name: Optional[str] = Field(None, description="Lead first name", example="John")
    last_name: Optional[str] = Field(None, description="Lead last name", example="Doe")

    # Contact information
    phone: Optional[str] = Field(None, description="Lead phone number", example="+1234567890")
    mobile: Optional[str] = Field(None, description="Lead mobile number", example="+1234567890")
    email: Optional[EmailStr] = Field(None, description="Lead email address", example="<EMAIL>")
    location: Optional[str] = Field(None, description="Lead location", example="New York")
    postal_code: Optional[str] = Field(None, description="Postal code", example="10001")

    # Reference fields (using IDs) - optional for updates
    lead_source_id: Optional[str] = Field(None, description="Lead source ID", example="550e8400-e29b-41d4-a716-************")
    lead_status_id: Optional[str] = Field(None, description="Lead status ID", example="550e8400-e29b-41d4-a716-************")
    brand_preference: Optional[str] = Field(None, description="Brand preference UUID", example="550e8400-e29b-41d4-a716-************")
    budget_preference: Optional[str] = Field(None, description="Budget preference range", example="$75000-$100000")

    # Franchise buyer information fields
    franchise_interested_in: Optional[str] = Field(None, description="Franchise interested in", example="Food & Beverage")
    looking_for_business_opportunity_since: Optional[str] = Field(None, description="Looking for business opportunity since", example="6 months")
    skills: Optional[str] = Field(None, description="Skills", example="Business administration")
    looking_to_be_owner_operator: Optional[str] = Field(None, description="Looking to be owner operator", example="Passive")
    when_looking_to_start: Optional[str] = Field(None, description="When looking to start", example="Within 3 months")
    ethnic_background: Optional[str] = Field(None, description="Ethnic background", example="Asian")
    funds_to_invest: Optional[str] = Field(None, description="Funds to invest", example="$100K - $150K")
    eoi_nda_link: Optional[str] = Field(None, description="EOI/NDA link", example="https://example.com/eoi")
    work_background: Optional[str] = Field(None, description="Work background", example="Business Owner")
    motivation_to_enquire: Optional[str] = Field(None, description="Motivation to enquire", example="Diversification")
    funds_available: Optional[str] = Field(None, description="Funds available", example="$200K")
    motivation: Optional[str] = Field(None, description="Motivation", example="Looking for passive income")
    have_run_business_before: Optional[bool] = Field(None, description="Have run business before", example=True)
    have_mortgage: Optional[bool] = Field(None, description="Have mortgage", example=False)
    high_net_worth: Optional[str] = Field(None, description="High net worth", example="$500K+")

    # System fields
    is_active: Optional[bool] = Field(None, description="Whether the lead is active")

    class Config:
        from_attributes = True


class LeadResponse(BaseModel):
    """Schema for lead response"""
    id: str = Field(..., description="Lead ID", example="550e8400-e29b-41d4-a716-************")
    zoho_lead_id: Optional[str] = Field(None, description="Zoho CRM lead ID")

    # Name fields
    first_name: Optional[str] = Field(None, description="Lead first name", example="John")
    last_name: Optional[str] = Field(None, description="Lead last name", example="Doe")

    # Contact information
    phone: Optional[str] = Field(None, description="Lead phone number", example="+1234567890")
    mobile: Optional[str] = Field(None, description="Lead mobile number", example="+1234567890")
    email: Optional[str] = Field(None, description="Lead email address", example="<EMAIL>")
    location: Optional[str] = Field(None, description="Lead location", example="New York")
    postal_code: Optional[str] = Field(None, description="Postal code", example="10001")

    # Reference fields with details
    lead_source_id: Optional[str] = Field(None, description="Lead source ID")
    lead_source_name: Optional[str] = Field(None, description="Lead source name", example="Website")
    lead_status_id: str = Field(..., description="Lead status ID")
    lead_status_name: str = Field(..., description="Lead status name", example="New Lead")
    lead_status_colour: str = Field(..., description="Lead status color", example="#8B00FF")
    brand_preference: Optional[str] = Field(None, description="Brand preference UUID")
    budget_preference: Optional[str] = Field(None, description="Budget preference range", example="$75000-$100000")

    # Franchise buyer information fields
    franchise_interested_in: Optional[str] = Field(None, description="Franchise interested in")
    looking_for_business_opportunity_since: Optional[str] = Field(None, description="Looking for business opportunity since")
    skills: Optional[str] = Field(None, description="Skills")
    looking_to_be_owner_operator: Optional[str] = Field(None, description="Looking to be owner operator")
    when_looking_to_start: Optional[str] = Field(None, description="When looking to start")
    ethnic_background: Optional[str] = Field(None, description="Ethnic background")
    funds_to_invest: Optional[str] = Field(None, description="Funds to invest")
    eoi_nda_link: Optional[str] = Field(None, description="EOI/NDA link")
    work_background: Optional[str] = Field(None, description="Work background")
    motivation_to_enquire: Optional[str] = Field(None, description="Motivation to enquire")
    funds_available: Optional[str] = Field(None, description="Funds available")
    motivation: Optional[str] = Field(None, description="Motivation")
    have_run_business_before: Optional[bool] = Field(None, description="Have run business before")
    have_mortgage: Optional[bool] = Field(None, description="Have mortgage")
    high_net_worth: Optional[str] = Field(None, description="High net worth")

    # System fields
    is_active: bool = Field(..., description="Whether the lead is active")
    is_deleted: bool = Field(..., description="Whether the lead is deleted")
    created_at: datetime = Field(..., description="Lead creation timestamp")
    updated_at: datetime = Field(..., description="Lead last update timestamp")

    class Config:
        from_attributes = True


class LeadListResponse(BaseModel):
    """Schema for lead list response (Franchisor style)"""
    items: List[LeadResponse] = Field(..., description="List of leads")
    total_count: int = Field(..., description="Total number of leads")
    pagination: PaginationInfo = Field(..., description="Pagination information")


class LeadSuccessResponse(BaseModel):
    """Standard success response for lead operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: LeadResponse = Field(..., description="Lead data")


class LeadListSuccessResponse(BaseModel):
    """Standard success response for lead list operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: LeadListResponse = Field(..., description="Lead list data")


class LeadDeleteSuccessResponse(BaseModel):
    """Standard success response for lead deletion"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: dict = Field(..., description="Deletion confirmation data")





# Legacy schemas for backward compatibility
class LeadBase(BaseModel):
    """Base lead schema"""
    full_name: str = Field(..., description="Lead full name")
    phone: Optional[str] = Field(None, description="Lead phone number")
    mobile: Optional[str] = Field(None, description="Lead mobile number")
    email: Optional[EmailStr] = Field(None, description="Lead email address")
    location: Optional[str] = Field(None, description="Lead location")
    lead_source: Optional[str] = Field(None, description="Lead source")
    brand_preference: Optional[str] = Field(None, description="Brand preference UUID")
    budget_preference: Optional[str] = Field(None, description="Budget preference range")


class LeadCreate(LeadBase):
    """Schema for creating a lead"""
    zoho_lead_id: Optional[str] = Field(None, description="Zoho CRM lead ID")
    qualification_status: Optional[LeadStatus] = Field(LeadStatus.NEW, description="Lead qualification status")


class LeadUpdate(BaseModel):
    """Schema for updating a lead"""
    full_name: Optional[str] = Field(None, description="Lead full name")
    phone: Optional[str] = Field(None, description="Lead phone number")
    mobile: Optional[str] = Field(None, description="Lead mobile number")
    email: Optional[EmailStr] = Field(None, description="Lead email address")
    location: Optional[str] = Field(None, description="Lead location")
    lead_source: Optional[str] = Field(None, description="Lead source")
    brand_preference: Optional[str] = Field(None, description="Brand preference UUID")
    budget_preference: Optional[str] = Field(None, description="Budget preference range")
    qualification_status: Optional[LeadStatus] = Field(None, description="Lead qualification status")


class QuestionBase(BaseModel):
    """Base question schema"""
    question_text: str = Field(..., description="Question text")
    question_type: Optional[str] = Field(None, description="Question type")
    is_required: bool = Field(False, description="Is question required")
    order_sequence: Optional[int] = Field(None, description="Question order sequence")


class QuestionCreate(QuestionBase):
    """Schema for creating a question"""
    is_active: bool = Field(True, description="Is question active")


class QuestionResponse(QuestionBase):
    """Schema for question response"""
    id: str = Field(..., description="Question ID")
    is_active: bool = Field(..., description="Is question active")
    created_at: datetime = Field(..., description="Question creation timestamp")

    class Config:
        from_attributes = True


class LeadResponseBase(BaseModel):
    """Base lead response schema"""
    response_text: Optional[str] = Field(None, description="Response text")


class LeadResponseCreate(LeadResponseBase):
    """Schema for creating lead response"""
    lead_id: str = Field(..., description="Lead ID")
    question_id: str = Field(..., description="Question ID")


class BulkUploadResponse(BaseModel):
    """Schema for bulk upload response"""
    total_processed: int = Field(..., description="Total number of rows processed")
    successful_imports: int = Field(..., description="Number of successful imports")
    duplicates_found: int = Field(..., description="Number of duplicates found")
    errors_found: int = Field(..., description="Number of errors found")
    duplicates: List[str] = Field(..., description="List of duplicate entries")
    errors: List[str] = Field(..., description="List of errors")


class BulkUploadSuccessResponse(BaseModel):
    """Standard success response for bulk upload operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: BulkUploadResponse = Field(..., description="Bulk upload results")


class CommunicationResponse(BaseModel):
    """Schema for communication response"""
    id: str = Field(..., description="Communication ID")
    communication_type: str = Field(..., description="Communication type")
    subject: Optional[str] = Field(None, description="Communication subject")
    content: Optional[str] = Field(None, description="Communication content")
    direction: Optional[str] = Field(None, description="Communication direction")
    user_id: Optional[str] = Field(None, description="User ID who created the communication")
    created_at: datetime = Field(..., description="Communication creation timestamp")
    updated_at: datetime = Field(..., description="Communication last update timestamp")


class CommunicationHistoryResponse(BaseModel):
    """Schema for communication history response"""
    communications: List[CommunicationResponse] = Field(..., description="List of communications")
    total_count: int = Field(..., description="Total number of communications")


class CommunicationHistorySuccessResponse(BaseModel):
    """Standard success response for communication history operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: CommunicationHistoryResponse = Field(..., description="Communication history data")


class LeadResponseResponse(LeadResponseBase):
    """Schema for lead response response"""
    id: str = Field(..., description="Lead response ID")
    lead_id: str = Field(..., description="Lead ID")
    question_id: str = Field(..., description="Question ID")
    answered_at: datetime = Field(..., description="Response timestamp")

    class Config:
        from_attributes = True
