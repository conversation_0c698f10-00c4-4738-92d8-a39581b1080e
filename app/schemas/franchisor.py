"""
Franchisor schemas for request and response models
"""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict, field_validator
from datetime import datetime
import uuid
from enum import Enum

from app.schemas.base_response import (
    SuccessResponse,
    PaginationInfo
)
from app.schemas.industry import IndustryResponse


class FranchisorCategory(str, Enum):
    """Franchisor category enumeration"""
    FOOD_BEVERAGE = "food_beverage"
    RETAIL = "retail"
    SERVICES = "services"
    HEALTH_FITNESS = "health_fitness"
    EDUCATION = "education"
    AUTOMOTIVE = "automotive"
    REAL_ESTATE = "real_estate"
    TECHNOLOGY = "technology"
    HOSPITALITY = "hospitality"
    OTHER = "other"


class FranchisorRegion(str, Enum):
    """Franchisor region enumeration"""
    NORTH_AMERICA = "north_america"
    EUROPE = "europe"
    ASIA_PACIFIC = "asia_pacific"
    LATIN_AMERICA = "latin_america"
    MIDDLE_EAST = "middle_east"
    AFRICA = "africa"
    AUSTRALIA = "australia"
    GLOBAL = "global"


# Request Models
class FranchisorCreateRequest(BaseModel):
    """Request model for creating a franchisor with industry relationship"""
    name: str = Field(..., description="Franchisor name", min_length=1, max_length=255)

    # Contact information
    contactFirstName: Optional[str] = Field(None, description="Contact first name", max_length=100)
    contactLastName: Optional[str] = Field(None, description="Contact last name", max_length=100)
    email: Optional[str] = Field(None, description="Contact email address", max_length=255)
    phone: Optional[str] = Field(None, description="Contact phone number", max_length=20)

    # UUID-based industry relationship
    industry_id: Optional[str] = Field(None, description="Industry UUID from industry table")

    @field_validator('industry_id')
    @classmethod
    def validate_industry_id(cls, v):
        if v is not None and v != "":
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError("Industry ID must be a valid UUID format")
        return v

    region: Optional[str] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount", ge=0)
    is_active: bool = Field(True, description="Whether the franchisor is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Coffee Club Melbourne",
                "contactFirstName": "John",
                "contactLastName": "Doe",
                "email": "<EMAIL>",
                "phone": "+***********",
                "industry_id": "123e4567-e89b-12d3-a456-************",
                "region": "australia",
                "budget": 250000.0,
                "is_active": True
            }
        }
    )




class FranchisorUpdateRequest(BaseModel):
    """Request model for updating a franchisor with industry relationship"""
    name: Optional[str] = Field(None, description="Franchisor name", min_length=1, max_length=255)

    # Contact information
    contactFirstName: Optional[str] = Field(None, description="Contact first name", max_length=100)
    contactLastName: Optional[str] = Field(None, description="Contact last name", max_length=100)
    email: Optional[str] = Field(None, description="Contact email address", max_length=255)
    phone: Optional[str] = Field(None, description="Contact phone number", max_length=20)

    # UUID-based industry relationship
    industry_id: Optional[str] = Field(None, description="Industry UUID from industry table")

    @field_validator('industry_id')
    @classmethod
    def validate_industry_id(cls, v):
        if v is not None and v != "":
            try:
                uuid.UUID(v)
            except ValueError:
                raise ValueError("Industry ID must be a valid UUID format")
        return v

    region: Optional[str] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount", ge=0)
    is_active: Optional[bool] = Field(None, description="Whether the franchisor is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Coffee Club Melbourne Updated",
                "contactFirstName": "Jane",
                "contactLastName": "Smith",
                "email": "<EMAIL>",
                "phone": "+***********",
                "industry_id": "123e4567-e89b-12d3-a456-************",
                "region": "australia",
                "budget": 300000.0,
                "is_active": True
            }
        }
    )


class FranchisorImportRequest(BaseModel):
    """Request model for CSV import"""
    csv_file: str = Field(..., description="Base64 encoded CSV file content")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "csv_file": "bmFtZSxjYXRlZ29yeSxyZWdpb24sYnVkZ2V0LHN1Yl9jYXRlZ29yeSxpc19hY3RpdmUKQ29mZmVlIENsdWIsZm9vZF9iZXZlcmFnZSxhdXN0cmFsaWEsMjUwMDAwLGNhZmUsdHJ1ZQ=="
            }
        }
    )


# Response Models
class FranchisorResponse(BaseModel):
    """Response model for franchisor data with industry relationship"""
    id: str = Field(..., description="Unique franchisor identifier")
    name: str = Field(..., description="Franchisor name")

    # Contact information
    contactFirstName: Optional[str] = Field(None, description="Contact first name")
    contactLastName: Optional[str] = Field(None, description="Contact last name")
    email: Optional[str] = Field(None, description="Contact email address")
    phone: Optional[str] = Field(None, description="Contact phone number")

    # UUID-based industry relationship
    industry_id: Optional[str] = Field(None, description="Industry UUID")
    industry_details: Optional[IndustryResponse] = Field(None, description="Industry details")

    region: Optional[str] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount")
    brochure_url: Optional[str] = Field(None, description="Brochure file URL")
    franchisor_won_id: Optional[str] = Field(None, description="Franchisor Won ID from Zoho")
    is_active: bool = Field(..., description="Whether the franchisor is active")
    is_deleted: bool = Field(..., description="Whether the franchisor is deleted")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "frc_123456789",
                "name": "Coffee Club Melbourne",
                "contactFirstName": "John",
                "contactLastName": "Doe",
                "email": "<EMAIL>",
                "phone": "+***********",
                "industry_id": "123e4567-e89b-12d3-a456-************",
                "industry_details": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "name": "Food & Beverage",
                    "description": "All food-related businesses",
                    "is_active": True,
                    "is_deleted": False,
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z"
                },
                "region": "australia",
                "budget": 250000.0,
                "brochure_url": "https://s3.amazonaws.com/bucket/brochures/coffee_club_brochure.pdf",
                "franchisor_won_id": "GH-1-FB-01",
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z"
            }
        }
    )


class FranchisorListResponse(BaseModel):
    """Response model for franchisor list with pagination"""
    items: List[FranchisorResponse] = Field(..., description="List of franchisors")
    total_count: int = Field(..., description="Total number of franchisors")
    pagination: PaginationInfo = Field(..., description="Pagination information")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [
                    {
                        "id": "frc_123456789",
                        "name": "Coffee Club Melbourne",
                        "contactFirstName": "John",
                        "contactLastName": "Doe",
                        "email": "<EMAIL>",
                        "phone": "+***********",
                        "industry_id": "123e4567-e89b-12d3-a456-************",
                        "industry_details": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "name": "Food & Beverage"
                        },
                        "region": "australia",
                        "budget": 250000.0,
                        "brochure_url": "https://s3.amazonaws.com/bucket/brochures/coffee_club_brochure.pdf",
                        "franchisor_won_id": "GH-1-FB-01",
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T12:00:00Z"
                    }
                ],
                "total_count": 1,
                "pagination": {
                    "current_page": 1,
                    "total_pages": 1,
                    "items_per_page": 20,
                    "total_items": 1
                }
            }
        }
    )


class FranchisorImportResponse(BaseModel):
    """Response model for CSV import results"""
    total_rows: int = Field(..., description="Total number of rows processed")
    successful_imports: int = Field(..., description="Number of successfully imported rows")
    failed_imports: int = Field(..., description="Number of failed imports")
    errors: List[dict] = Field(..., description="List of import errors")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_rows": 10,
                "successful_imports": 8,
                "failed_imports": 2,
                "errors": [
                    {
                        "row": 3,
                        "field": "name",
                        "message": "Name is required"
                    },
                    {
                        "row": 7,
                        "field": "category",
                        "message": "Invalid category value"
                    }
                ]
            }
        }
    )


class FranchisorDeleteResponse(BaseModel):
    """Response model for franchisor deletion"""
    franchisor_id: str = Field(..., description="Deleted franchisor ID")
    deleted_at: datetime = Field(..., description="Deletion timestamp")
    message: str = Field(..., description="Deletion confirmation message")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "franchisor_id": "frc_123456789",
                "deleted_at": "2024-01-01T12:00:00Z",
                "message": "Franchisor has been permanently deleted"
            }
        }
    )


# Type aliases for response models
FranchisorSuccessResponse = SuccessResponse[FranchisorResponse]
FranchisorListSuccessResponse = SuccessResponse[FranchisorListResponse]
FranchisorImportSuccessResponse = SuccessResponse[FranchisorImportResponse]
FranchisorDeleteSuccessResponse = SuccessResponse[FranchisorDeleteResponse] 