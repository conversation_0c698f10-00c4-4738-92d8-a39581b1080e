"""
Dropdown schemas for reference data
"""

from typing import List, Generic, TypeVar, Dict, Any
from pydantic import BaseModel, Field
from app.schemas.base_response import ResponseMessage

T = TypeVar('T')


class LeadSourceResponse(BaseModel):
    """Response model for lead source dropdown"""
    id: str = Field(..., description="Lead source ID", example="550e8400-e29b-41d4-a716-446655440000")
    name: str = Field(..., description="Lead source name", example="Website")
    is_active: bool = Field(..., description="Whether the lead source is active", example=True)

    class Config:
        from_attributes = True


class LeadStatusResponse(BaseModel):
    """Response model for lead status dropdown"""
    id: str = Field(..., description="Lead status ID", example="550e8400-e29b-41d4-a716-446655440000")
    name: str = Field(..., description="Lead status name", example="New Lead")
    colour: str = Field(..., description="Lead status color (hex)", example="#8B00FF")
    is_active: bool = Field(..., description="Whether the lead status is active", example=True)

    class Config:
        from_attributes = True


class DropdownData(BaseModel, Generic[T]):
    """Generic dropdown data container"""
    items: List[T] = Field(..., description="List of dropdown items")
    total_count: int = Field(..., description="Total number of items")


class DropdownListResponse(BaseModel, Generic[T]):
    """Generic dropdown list response"""
    success: bool = Field(..., description="Whether the request was successful", example=True)
    status: str = Field(..., description="Response status", example="success")
    message: ResponseMessage = Field(..., description="Response message")
    data: DropdownData[T] = Field(..., description="Dropdown data")

    class Config:
        from_attributes = True
