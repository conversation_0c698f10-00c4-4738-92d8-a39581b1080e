"""
Analytics API schemas
Pydantic models for analytics dashboard API responses
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from app.schemas.base_response import StandardResponse


class ChartDataPoint(BaseModel):
    """Single data point for charts"""
    date: str = Field(..., description="Date label (e.g., 'May 1', '2024-05-01')", example="May 1")
    question_count: int = Field(..., description="Number of questions asked", example=24)
    escalation_count: int = Field(..., description="Number of escalated questions", example=3)
    escalation_rate: float = Field(..., description="Escalation rate percentage", example=12.5)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "date": "May 1",
                "question_count": 24,
                "escalation_count": 3,
                "escalation_rate": 12.5
            }
        }
    )


class AnalyticsCountsData(BaseModel):
    """Analytics counts data"""
    total_leads: int = Field(..., description="Total number of leads", example=170)
    total_franchisors: int = Field(..., description="Total number of franchisors", example=24)
    total_questions: int = Field(..., description="Total questions asked (question_bank + escalation_question_bank)", example=89)
    total_meetings: int = Field(..., description="Total meetings (static 0)", example=0)
    
    # Percentage changes from last period
    leads_change_percent: float = Field(..., description="Percentage change in leads", example=5.2)
    franchisors_change_percent: float = Field(..., description="Percentage change in franchisors", example=-1.2)
    questions_change_percent: float = Field(..., description="Percentage change in questions", example=15.6)
    meetings_change_percent: float = Field(..., description="Percentage change in meetings", example=0.0)
    
    # Average per day for current period
    avg_questions_per_day: float = Field(..., description="Average questions per day", example=3.4)
    avg_escalations_per_day: float = Field(..., description="Average escalations per day", example=1.2)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_leads": 170,
                "total_franchisors": 24,
                "total_questions": 89,
                "total_meetings": 0,
                "leads_change_percent": 5.2,
                "franchisors_change_percent": -1.2,
                "questions_change_percent": 15.6,
                "meetings_change_percent": 0.0,
                "avg_questions_per_day": 3.4,
                "avg_escalations_per_day": 1.2
            }
        }
    )


class RecentActivityItem(BaseModel):
    """Recent activity item"""
    id: str = Field(..., description="Item ID", example="550e8400-e29b-41d4-a716-446655440000")
    type: str = Field(..., description="Activity type", example="lead")
    title: str = Field(..., description="Activity title", example="New Lead: John Doe")
    description: str = Field(..., description="Activity description", example="Lead created for McDonald's franchise")
    timestamp: datetime = Field(..., description="Activity timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "type": "lead",
                "title": "New Lead: John Doe",
                "description": "Lead created for McDonald's franchise",
                "timestamp": "2024-05-07T10:30:00Z",
                "metadata": {"status": "new", "source": "website"}
            }
        }
    )


class DetailedAnalyticsRow(BaseModel):
    """Row in detailed analytics table"""
    date: str = Field(..., description="Date", example="May 1")
    question_count: int = Field(..., description="Questions asked", example=24)
    escalation_count: int = Field(..., description="Escalations", example=3)
    escalation_rate: float = Field(..., description="Escalation rate percentage", example=12.5)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "date": "May 1",
                "question_count": 24,
                "escalation_count": 3,
                "escalation_rate": 12.5
            }
        }
    )


class AnalyticsFilterOptions(BaseModel):
    """Available filter options"""
    date_ranges: List[str] = Field(..., description="Available date range filters", example=["last_7_days", "last_30_days", "last_3_months", "last_6_months", "last_year", "custom"])
    time_periods: List[str] = Field(..., description="Available time period groupings", example=["day", "week", "month", "year"])

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "date_ranges": ["last_7_days", "last_30_days", "last_3_months", "last_6_months", "last_year", "custom"],
                "time_periods": ["day", "week", "month", "year"]
            }
        }
    )


class DashboardAnalyticsResponse(BaseModel):
    """Complete dashboard analytics response"""
    # Counts section (replaces old counts API)
    counts: AnalyticsCountsData = Field(..., description="Dashboard counts and statistics")
    
    # Recent activity section (replaces old recent activity API)
    recent_activity: List[RecentActivityItem] = Field(..., description="Recent activity items")
    
    # Chart data for visualization
    chart_data: List[ChartDataPoint] = Field(..., description="Chart data points for visualization")
    
    # Detailed analytics table
    detailed_analytics: List[DetailedAnalyticsRow] = Field(..., description="Detailed analytics table data")
    
    # Filter information
    applied_filters: Dict[str, Any] = Field(..., description="Currently applied filters")
    available_filters: AnalyticsFilterOptions = Field(..., description="Available filter options")
    
    # Metadata
    period_label: str = Field(..., description="Human-readable period label", example="Last 7 days")
    generated_at: datetime = Field(..., description="Response generation timestamp")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "counts": {
                    "total_leads": 170,
                    "total_franchisors": 24,
                    "total_questions": 89,
                    "total_meetings": 0,
                    "leads_change_percent": 5.2,
                    "franchisors_change_percent": -1.2,
                    "questions_change_percent": 15.6,
                    "meetings_change_percent": 0.0,
                    "avg_questions_per_day": 3.4,
                    "avg_escalations_per_day": 1.2
                },
                "recent_activity": [
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "type": "lead",
                        "title": "New Lead: John Doe",
                        "description": "Lead created for McDonald's franchise",
                        "timestamp": "2024-05-07T10:30:00Z",
                        "metadata": {"status": "new"}
                    }
                ],
                "chart_data": [
                    {
                        "date": "May 1",
                        "question_count": 24,
                        "escalation_count": 3,
                        "escalation_rate": 12.5
                    }
                ],
                "detailed_analytics": [
                    {
                        "date": "May 1",
                        "question_count": 24,
                        "escalation_count": 3,
                        "escalation_rate": 12.5
                    }
                ],
                "applied_filters": {
                    "date_range": "last_7_days",
                    "time_period": "day"
                },
                "available_filters": {
                    "date_ranges": ["last_7_days", "last_30_days", "custom"],
                    "time_periods": ["day", "week", "month"]
                },
                "period_label": "Last 7 days",
                "generated_at": "2024-05-07T10:30:00Z"
            }
        }
    )


class DashboardAnalyticsSuccessResponse(StandardResponse):
    """Success response for dashboard analytics"""
    data: DashboardAnalyticsResponse = Field(..., description="Dashboard analytics data")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": {
                    "title": "Dashboard Analytics Retrieved",
                    "description": "Dashboard analytics data retrieved successfully"
                },
                "data": {
                    "counts": {
                        "total_leads": 170,
                        "total_franchisors": 24,
                        "total_questions": 89,
                        "total_meetings": 0
                    },
                    "recent_activity": [],
                    "chart_data": [],
                    "detailed_analytics": [],
                    "applied_filters": {"date_range": "last_7_days"},
                    "available_filters": {"date_ranges": ["last_7_days"]},
                    "period_label": "Last 7 days",
                    "generated_at": "2024-05-07T10:30:00Z"
                },
                "error_code": 0
            }
        }
    )
