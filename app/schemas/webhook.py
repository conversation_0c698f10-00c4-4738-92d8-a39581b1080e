"""
Webhook Schemas
Pydantic models for Kudosity webhook events
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, ConfigDict
from uuid import UUID
from enum import Enum


class WebhookEventType(str, Enum):
    """Supported webhook event types"""
    LINK_HIT = "LINK_HIT"
    OPT_OUT = "OPT_OUT"
    MMS_INBOUND = "MMS_INBOUND"
    MMS_STATUS = "MMS_STATUS"
    SMS_INBOUND = "SMS_INBOUND"
    SMS_STATUS = "SMS_STATUS"
    RCS_STATUS = "RCS_STATUS"


class MessageType(str, Enum):
    """Message types"""
    SMS = "SMS"
    MMS = "MMS"
    RCS = "RCS"


class MessageStatus(str, Enum):
    """Message status values"""
    SENT = "SENT"
    DELIVERED = "DELIVERED"
    FAILED = "FAILED"
    ACCEPTED = "ACCEPTED"
    SOFT_BOUNCE = "SOFT_BOUNCE"
    HARD_BOUNCE = "HARD_BOUNCE"
    REJECTED = "REJECTED"
    READ = "READ"  # RCS specific
    OTHER = "OTHER"


class OptOutSource(str, Enum):
    """Opt-out source types"""
    LINK_HIT = "LINK_HIT"
    SMS_INBOUND = "SMS_INBOUND"


# Base message models
class BaseMessage(BaseModel):
    """Base message model"""
    type: MessageType = Field(..., description="Message type")
    id: str = Field(..., description="Message ID")
    message: str = Field(..., description="Message content")
    message_ref: Optional[str] = Field(None, description="Message reference")
    recipient: str = Field(..., description="Recipient phone number")
    sender: str = Field(..., description="Sender phone number or ID")


class SMSMessage(BaseMessage):
    """SMS message model"""
    type: Literal[MessageType.SMS] = MessageType.SMS
    routed_via: Optional[str] = Field(None, description="Routing information")


class MMSMessage(BaseMessage):
    """MMS message model"""
    type: Literal[MessageType.MMS] = MessageType.MMS
    subject: Optional[str] = Field(None, description="MMS subject")
    content_urls: Optional[List[str]] = Field(None, description="Content URLs")


class RCSMessage(BaseMessage):
    """RCS message model"""
    type: Literal[MessageType.RCS] = MessageType.RCS


# Media model for MMS inbound
class MediaContent(BaseModel):
    """Media content model for MMS"""
    name: str = Field(..., description="Media file name")
    content: str = Field(..., description="Base64 encoded media content")


# Status models
class BaseStatus(BaseModel):
    """Base status model"""
    type: MessageType = Field(..., description="Message type")
    id: str = Field(..., description="Message ID")
    message_ref: Optional[str] = Field(None, description="Message reference")
    recipient: str = Field(..., description="Recipient phone number")
    sender: str = Field(..., description="Sender phone number or ID")
    status: MessageStatus = Field(..., description="Message status")


class SMSStatus(BaseStatus):
    """SMS status model"""
    type: Literal[MessageType.SMS] = MessageType.SMS
    routed_via: Optional[str] = Field(None, description="Routing information")


class MMSStatus(BaseStatus):
    """MMS status model"""
    type: Literal[MessageType.MMS] = MessageType.MMS


class RCSStatus(BaseStatus):
    """RCS status model"""
    type: Literal[MessageType.RCS] = MessageType.RCS


# Inbound message models
class SMSInbound(BaseModel):
    """SMS inbound message model"""
    type: Literal[MessageType.SMS] = MessageType.SMS
    id: str = Field(..., description="Message ID")
    message: str = Field(..., description="Inbound message content")
    recipient: str = Field(..., description="Recipient (your number)")
    sender: str = Field(..., description="Sender phone number")
    routed_via: Optional[str] = Field(None, description="Routing information")
    last_message: Optional[SMSMessage] = Field(None, description="Last message sent to this sender")


class MMSInbound(BaseModel):
    """MMS inbound message model"""
    type: Literal[MessageType.MMS] = MessageType.MMS
    id: str = Field(..., description="Message ID")
    sender: str = Field(..., description="Sender phone number")
    recipient: str = Field(..., description="Recipient (your number)")
    message: str = Field(..., description="Inbound message content")
    media: Optional[List[MediaContent]] = Field(None, description="Media attachments")
    last_message: Optional[Union[SMSMessage, MMSMessage]] = Field(None, description="Last message sent to this sender")


# Link hit model
class LinkHit(BaseModel):
    """Link hit model"""
    hits: int = Field(..., description="Total number of hits for this link")
    url: str = Field(..., description="The URL that was clicked")
    source_message: Union[SMSMessage, MMSMessage] = Field(..., description="Original message containing the link")


# Opt out model
class OptOut(BaseModel):
    """Opt out model"""
    source: OptOutSource = Field(..., description="How the user opted out")
    source_message: Union[SMSMessage, MMSMessage] = Field(..., description="Original message that led to opt-out")


# Main webhook payload models
class LinkHitWebhook(BaseModel):
    """Link hit webhook payload"""
    event_type: Literal[WebhookEventType.LINK_HIT] = WebhookEventType.LINK_HIT
    timestamp: datetime = Field(..., description="Event timestamp")
    link_hit: LinkHit = Field(..., description="Link hit details")


class OptOutWebhook(BaseModel):
    """Opt out webhook payload"""
    event_type: Literal[WebhookEventType.OPT_OUT] = WebhookEventType.OPT_OUT
    timestamp: datetime = Field(..., description="Event timestamp")
    opt_out: OptOut = Field(..., description="Opt out details")


class SMSInboundWebhook(BaseModel):
    """SMS inbound webhook payload"""
    event_type: Literal[WebhookEventType.SMS_INBOUND] = WebhookEventType.SMS_INBOUND
    timestamp: datetime = Field(..., description="Event timestamp")
    mo: SMSInbound = Field(..., description="Inbound SMS details")


class MMSInboundWebhook(BaseModel):
    """MMS inbound webhook payload"""
    event_type: Literal[WebhookEventType.MMS_INBOUND] = WebhookEventType.MMS_INBOUND
    timestamp: datetime = Field(..., description="Event timestamp")
    mo: MMSInbound = Field(..., description="Inbound MMS details")


class SMSStatusWebhook(BaseModel):
    """SMS status webhook payload"""
    event_type: Literal[WebhookEventType.SMS_STATUS] = WebhookEventType.SMS_STATUS
    timestamp: datetime = Field(..., description="Event timestamp")
    status: SMSStatus = Field(..., description="SMS status details")


class MMSStatusWebhook(BaseModel):
    """MMS status webhook payload"""
    event_type: Literal[WebhookEventType.MMS_STATUS] = WebhookEventType.MMS_STATUS
    timestamp: datetime = Field(..., description="Event timestamp")
    status: MMSStatus = Field(..., description="MMS status details")


class RCSStatusWebhook(BaseModel):
    """RCS status webhook payload"""
    event_type: Literal[WebhookEventType.RCS_STATUS] = WebhookEventType.RCS_STATUS
    timestamp: datetime = Field(..., description="Event timestamp")
    status: RCSStatus = Field(..., description="RCS status details")


# Union type for all webhook payloads
WebhookPayload = Union[
    LinkHitWebhook,
    OptOutWebhook,
    SMSInboundWebhook,
    MMSInboundWebhook,
    SMSStatusWebhook,
    MMSStatusWebhook,
    RCSStatusWebhook
]


# Generic webhook request model for the endpoint
class WebhookRequest(BaseModel):
    """Generic webhook request model that can handle any webhook type"""
    event_type: WebhookEventType = Field(..., description="Type of webhook event")
    timestamp: datetime = Field(..., description="Event timestamp")
    webhook_id: Optional[str] = Field(None, description="Webhook ID from Kudosity")
    webhook_name: Optional[str] = Field(None, description="Webhook name from Kudosity")

    # Additional fields that may be present depending on event type
    link_hit: Optional[LinkHit] = Field(None, description="Link hit details (for LINK_HIT events)")
    opt_out: Optional[OptOut] = Field(None, description="Opt out details (for OPT_OUT events)")
    mo: Optional[Union[SMSInbound, MMSInbound]] = Field(None, description="Inbound message details (for inbound events)")
    status: Optional[Union[SMSStatus, MMSStatus, RCSStatus]] = Field(None, description="Status details (for status events)")
    
    model_config = ConfigDict(
        extra="allow",  # Allow additional fields not explicitly defined
        json_schema_extra={
            "example": {
                "event_type": "SMS_STATUS",
                "timestamp": "2025-04-23T00:05:18Z",
                "status": {
                    "id": "e48d3e71-264e-4afb-9c06-edec1f0bb9d4",
                    "type": "SMS",
                    "message_ref": "test V2 QA SMS Send",
                    "sender": "************",
                    "routed_via": "6140000000",
                    "recipient": "6140000000",
                    "status": "SENT"
                }
            }
        }
    )


# Response models
class WebhookResponse(BaseModel):
    """Webhook database record response model"""
    id: UUID = Field(..., description="Webhook record ID")
    event_type: str = Field(..., description="Type of webhook event")
    event_timestamp: datetime = Field(..., description="Timestamp from the webhook payload")
    message_id: Optional[str] = Field(None, description="Message ID from the webhook payload")
    recipient: Optional[str] = Field(None, description="Recipient phone number")
    sender: Optional[str] = Field(None, description="Sender phone number or ID")
    message_type: Optional[str] = Field(None, description="Message type (SMS, MMS, RCS)")
    status: Optional[str] = Field(None, description="Message status for status events")
    processed: bool = Field(..., description="Whether this webhook has been processed")
    processing_notes: Optional[str] = Field(None, description="Notes about webhook processing")
    is_active: bool = Field(..., description="Whether this webhook record is active")
    is_deleted: bool = Field(..., description="Whether this webhook record is deleted")
    created_at: datetime = Field(..., description="When this webhook record was created")
    updated_at: datetime = Field(..., description="When this webhook record was last updated")
    deleted_at: Optional[datetime] = Field(None, description="When this webhook record was deleted")

    model_config = ConfigDict(from_attributes=True)


class WebhookCreateRequest(BaseModel):
    """Request model for creating webhook records (for testing purposes)"""
    event_type: WebhookEventType = Field(..., description="Type of webhook event")
    payload: Dict[str, Any] = Field(..., description="Complete webhook payload")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "event_type": "SMS_STATUS",
                "payload": {
                    "event_type": "SMS_STATUS",
                    "timestamp": "2025-04-23T00:05:18Z",
                    "status": {
                        "id": "e48d3e71-264e-4afb-9c06-edec1f0bb9d4",
                        "type": "SMS",
                        "message_ref": "test V2 QA SMS Send",
                        "sender": "************",
                        "recipient": "6140000000",
                        "status": "SENT"
                    }
                }
            }
        }
    )


class WebhookUpdateRequest(BaseModel):
    """Request model for updating webhook records"""
    processed: Optional[bool] = Field(None, description="Whether this webhook has been processed")
    processing_notes: Optional[str] = Field(None, description="Notes about webhook processing")
    is_active: Optional[bool] = Field(None, description="Whether this webhook record is active")
    is_deleted: Optional[bool] = Field(None, description="Whether this webhook record is deleted")


class WebhookListResponse(BaseModel):
    """Response model for webhook list"""
    webhooks: List[WebhookResponse] = Field(..., description="List of webhook records")
    total_count: int = Field(..., description="Total number of webhook records")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "webhooks": [],
                "total_count": 0
            }
        }
    )


class WebhookReceiveResponse(BaseModel):
    """Response model for webhook receive endpoint"""
    message: str = Field(..., description="Success message")
    webhook_id: Optional[UUID] = Field(None, description="ID of the created webhook record")
    event_type: str = Field(..., description="Type of webhook event received")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "Webhook received and processed successfully",
                "webhook_id": "123e4567-e89b-12d3-a456-************",
                "event_type": "SMS_INBOUND"
            }
        }
    )


class WebhookHealthResponse(BaseModel):
    """Response model for webhook health check endpoint"""
    status: str = Field(..., description="Service health status")
    qna_available: bool = Field(..., description="Whether any QnA/RAG system is available")
    timestamp: datetime = Field(..., description="Health check timestamp")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional health check details including RAG system types")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "healthy",
                "qna_available": True,
                "timestamp": "2024-03-20T10:00:00Z"
            }
        }
    )
