"""
Response Schemas
Standardized response models for the API
"""

from typing import Any, Dict, List, TypeVar, Generic
from pydantic import BaseModel, Field

T = TypeVar("T")

class Message(BaseModel):
    """Message model for response messages"""
    title: str = Field(..., description="Short title of the message")
    description: str = Field(..., description="Detailed description of the message")

class Pagination(BaseModel):
    """Pagination model for paginated responses"""
    current_page: int = Field(..., description="Current page number")
    total_pages: int = Field(..., description="Total number of pages")
    items_per_page: int = Field(..., description="Number of items per page")
    total_items: int = Field(..., description="Total number of items")

class SuccessResponse(BaseModel, Generic[T]):
    """Success response model"""
    success: bool = Field(True, description="Indicates if the request was successful")
    message: Message = Field(..., description="Response message")
    data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Response data"
    )

class ErrorResponse(BaseModel):
    """Error response model"""
    success: bool = Field(False, description="Indicates if the request was successful")
    error_code: int = Field(..., description="Error code")
    message: Message = Field(..., description="Error message")
    data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional error data"
    )

class PaginatedResponse(SuccessResponse[T]):
    """Paginated response model"""
    data: Dict[str, Any] = Field(
        ...,
        description="Response data with pagination"
    )
    
    @classmethod
    def create(
        cls,
        items: List[T],
        pagination: Pagination,
        message: Message
    ) -> "PaginatedResponse[T]":
        """Create a paginated response"""
        return cls(
            success=True,
            message=message,
            data={
                "details": items,
                "pagination": pagination
            }
        ) 