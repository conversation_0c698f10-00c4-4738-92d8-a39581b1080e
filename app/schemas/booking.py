"""
Booking Schemas
Pydantic models for booking API requests and responses
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, EmailStr, Field, validator
from uuid import UUID


class BookingCustomerCreate(BaseModel):
    """Customer information for creating a booking"""
    name: str = Field(..., min_length=1, max_length=255)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=20)


class BookingCreate(BaseModel):
    """Schema for creating a new booking"""
    service_type: str = Field(default="lead_meeting", description="Type of service to book")
    customer: BookingCustomerCreate
    preferred_start_time: datetime = Field(..., description="Preferred start time for the appointment")
    timezone: str = Field(default="Australia/Sydney", description="Timezone for the appointment")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes for the booking")
    preferred_staff: Optional[str] = Field(None, description="Preferred staff member (saumil or frank)")
    lead_id: Optional[UUID] = Field(None, description="Associated lead ID")
    
    @validator('service_type')
    def validate_service_type(cls, v):
        allowed_types = ['lead_meeting', 'saumil_consultation', 'frank_consultation']
        if v not in allowed_types:
            raise ValueError(f'Service type must be one of: {allowed_types}')
        return v
    
    @validator('preferred_staff')
    def validate_preferred_staff(cls, v):
        if v is not None:
            allowed_staff = ['saumil', 'frank']
            if v not in allowed_staff:
                raise ValueError(f'Preferred staff must be one of: {allowed_staff}')
        return v


class AvailabilityRequest(BaseModel):
    """Schema for checking availability"""
    service_type: str = Field(default="lead_meeting", description="Type of service")
    date_from: datetime = Field(..., description="Start date for availability check")
    date_to: datetime = Field(..., description="End date for availability check")
    preferred_staff: Optional[str] = Field(None, description="Preferred staff member")
    timezone: str = Field(default="Australia/Sydney", description="Timezone")
    
    @validator('service_type')
    def validate_service_type(cls, v):
        allowed_types = ['lead_meeting', 'saumil_consultation', 'frank_consultation']
        if v not in allowed_types:
            raise ValueError(f'Service type must be one of: {allowed_types}')
        return v


class AvailabilitySlot(BaseModel):
    """Available time slot"""
    staff_id: str
    staff_name: str
    start_time: datetime
    end_time: datetime
    service_id: str
    service_name: str
    duration_minutes: int
    booking_url: Optional[str] = None


class AvailabilityResponse(BaseModel):
    """Response for availability check"""
    success: bool
    slots: List[AvailabilitySlot]
    message: Optional[str] = None
    total_slots: int = Field(default=0)


class BookingResponse(BaseModel):
    """Response for booking creation"""
    success: bool
    booking_id: Optional[str] = None
    zoho_booking_id: Optional[str] = None
    booking_url: Optional[str] = None
    meeting_link: Optional[str] = None
    summary_url: Optional[str] = None
    message: Optional[str] = None
    error_message: Optional[str] = None


class BookingUpdate(BaseModel):
    """Schema for updating a booking"""
    status: Optional[str] = Field(None, description="New status")
    notes: Optional[str] = Field(None, max_length=1000)
    customer_name: Optional[str] = Field(None, max_length=255)
    customer_email: Optional[EmailStr] = None
    customer_phone: Optional[str] = Field(None, max_length=20)
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['scheduled', 'confirmed', 'cancelled', 'completed', 'no_show', 'rescheduled']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v


class BookingDetail(BaseModel):
    """Detailed booking information"""
    id: UUID
    zoho_booking_id: Optional[str]
    zoho_service_id: Optional[str]
    zoho_staff_id: Optional[str]
    
    # Service information
    service_type: str
    staff_name: str
    staff_email: Optional[str]
    
    # Customer information
    customer_name: str
    customer_email: Optional[str]
    customer_phone: Optional[str]
    
    # Timing
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    timezone: str
    
    # Status and URLs
    status: str
    booking_source: str
    booking_url: Optional[str]
    meeting_link: Optional[str]
    
    # Additional info
    notes: Optional[str]
    confirmation_sent: bool
    reminder_sent: bool
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BookingList(BaseModel):
    """List of bookings with pagination"""
    bookings: List[BookingDetail]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class BookingStats(BaseModel):
    """Booking statistics"""
    total_bookings: int
    upcoming_bookings: int
    completed_bookings: int
    cancelled_bookings: int
    bookings_today: int
    bookings_this_week: int
    bookings_this_month: int
    staff_utilization: Dict[str, Any]


class StaffAvailabilityInfo(BaseModel):
    """Staff availability information"""
    staff_id: str
    staff_name: str
    email: str
    is_available: bool
    next_available_slot: Optional[datetime]
    available_slots_count: int
    specialties: List[str]


class StaffAvailabilitySummary(BaseModel):
    """Summary of all staff availability"""
    staff_members: List[StaffAvailabilityInfo]
    total_available_slots: int
    next_available_slot: Optional[datetime]


class BookingRescheduleRequest(BaseModel):
    """Request to reschedule a booking"""
    new_start_time: datetime
    reason: Optional[str] = Field(None, max_length=500)
    notify_customer: bool = Field(default=True)


class BookingCancelRequest(BaseModel):
    """Request to cancel a booking"""
    reason: Optional[str] = Field(None, max_length=500)
    notify_customer: bool = Field(default=True)


class QuickBookingRequest(BaseModel):
    """Quick booking request for AI agents"""
    customer_name: str
    customer_phone: str
    customer_email: Optional[EmailStr] = None
    preferred_time: Optional[str] = Field(None, description="Natural language time preference")
    service_type: str = Field(default="lead_meeting")
    notes: Optional[str] = None
    conversation_id: Optional[UUID] = None


class BookingConfirmation(BaseModel):
    """Booking confirmation details"""
    booking_id: UUID
    confirmation_code: str
    customer_name: str
    service_name: str
    staff_name: str
    start_time: datetime
    duration_minutes: int
    meeting_link: Optional[str]
    booking_url: Optional[str]
    instructions: Optional[str]
