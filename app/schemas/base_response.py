"""
Enhanced base response models for comprehensive Swagger documentation
"""

from typing import Optional, List, Dict, Any, Generic, TypeVar
from pydantic import BaseModel, Field, ConfigDict, model_validator
from datetime import datetime
from enum import Enum

# Generic type for data payload
T = TypeVar('T')


class ResponseStatus(str, Enum):
    """Response status enumeration"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"


class MessageResponse(BaseModel):
    title: str = Field(..., description="Response title")
    description: str = Field(..., description="Response description")


class StandardResponse(BaseModel, Generic[T]):
    success: bool = Field(..., description="Operation success status")
    message: MessageResponse = Field(..., description="Response message")
    data: Optional[T] = Field(None, description="Response data")
    error_code: int = Field(..., description="Error code (0 for success)")


class PaginationInfo(BaseModel):
    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int


class PaginationResponse(BaseModel):
    success: bool = Field(..., description="Operation success status")
    message: MessageResponse = Field(..., description="Response message")
    data: Dict[str, Any] = Field(..., description="Response data with details and pagination")
    error_code: int = Field(..., description="Error code (0 for success)")

    @classmethod
    def create(
        cls,
        details: List[Any],
        pagination: PaginationInfo,
        message: MessageResponse,
        success: bool = True,
        error_code: int = 1
    ) -> "PaginationResponse":
        return cls(
            success=success,
            message=message,
            data={
                "details": details,
                "pagination": pagination.dict()
            },
            error_code=error_code
        )


class ResponseMessage(BaseModel):
    """Response message model"""
    title: str = Field(..., description="Message title", example="Operation Successful")
    description: str = Field(..., description="Detailed message description", example="The operation was completed successfully")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "title": "Operation Successful",
                "description": "The operation was completed successfully"
            }
        }
    )


class ResponseMetadata(BaseModel):
    """Response metadata model"""
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    api_version: str = Field(default="1.0.0", description="API version")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "timestamp": "2024-01-01T12:00:00.000000",
                "request_id": "req_123456789",
                "api_version": "1.0.0",
                "processing_time_ms": 150.5
            }
        }
    )


class BaseResponse(BaseModel, Generic[T]):
    """Base response model for all API responses"""
    success: bool = Field(..., description="Whether the operation was successful")
    status: ResponseStatus = Field(..., description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: Optional[T] = Field(None, description="Response data payload")
    metadata: ResponseMetadata = Field(default_factory=ResponseMetadata, description="Response metadata")
    error_code: Optional[str] = Field(None, description="Error code for failed operations")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "status": "success",
                "message": {
                    "title": "Operation Successful",
                    "description": "The operation was completed successfully"
                },
                "data": {},
                "metadata": {
                    "timestamp": "2024-01-01T12:00:00.000000",
                    "request_id": "req_123456789",
                    "api_version": "1.0.0",
                    "processing_time_ms": 150.5
                },
                "error_code": None
            }
        }
    )


class ErrorDetail(BaseModel):
    """Error detail model"""
    field: Optional[str] = Field(None, description="Field that caused the error")
    message: str = Field(..., description="Error message")
    code: str = Field(..., description="Error code")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "field": "email",
                "message": "Invalid email format",
                "code": "INVALID_EMAIL"
            }
        }
    )


class ErrorResponse(BaseModel):
    """Error response model"""
    success: bool = Field(False, description="Always false for error responses")
    status: ResponseStatus = Field(ResponseStatus.ERROR, description="Always 'error' for error responses")
    message: ResponseMessage = Field(..., description="Error message")
    errors: Optional[List[ErrorDetail]] = Field(None, description="Detailed error information")
    metadata: ResponseMetadata = Field(default_factory=ResponseMetadata, description="Response metadata")
    error_code: str = Field(..., description="Error code")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": False,
                "status": "error",
                "message": {
                    "title": "Validation Error",
                    "description": "The request contains invalid data"
                },
                "errors": [
                    {
                        "field": "email",
                        "message": "Invalid email format",
                        "code": "INVALID_EMAIL"
                    }
                ],
                "metadata": {
                    "timestamp": "2024-01-01T12:00:00.000000",
                    "request_id": "req_123456789",
                    "api_version": "1.0.0",
                    "processing_time_ms": 50.2
                },
                "error_code": "VALIDATION_ERROR"
            }
        }
    )


class SuccessResponse(BaseResponse[T]):
    """Success response model"""
    success: bool = Field(True, description="Always true for success responses")
    status: ResponseStatus = Field(ResponseStatus.SUCCESS, description="Always 'success' for success responses")
    error_code: Optional[str] = Field(None, description="Always null for success responses")


class ListResponse(BaseModel, Generic[T]):
    """List response model with pagination"""
    items: List[T] = Field(..., description="List of items")
    total_count: int = Field(..., description="Total number of items")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [],
                "total_count": 0
            }
        }
    )


class WorkingHourEntry(BaseModel):
    """Model for working hours entry"""
    day_of_week: str = Field(..., description="Day of the week")
    start_time: Optional[str] = Field(
        default=None,
        description="Start time in HH:MM format"
    )
    end_time: Optional[str] = Field(
        default=None,
        description="End time in HH:MM format"
    )
    is_24_hours: bool = Field(
        default=False,
        description="Whether the business is open 24 hours"
    )


class EmergencyHourEntry(BaseModel):
    """Model for emergency hours entry"""
    day_of_week: str = Field(..., description="Day of the week")
    start_time: Optional[str] = Field(
        default=None,
        description="Start time in HH:MM format"
    )
    end_time: Optional[str] = Field(
        default=None,
        description="End time in HH:MM format"
    )
    is_24_hours: bool = Field(
        default=False,
        description="Whether emergency service is available 24 hours"
    )
    cost: Optional[float] = Field(
        default=None,
        description="Emergency service cost"
    )


class ServiceAreaDetailsUpdateRequest(BaseModel):
    """Model for service area details update request"""
    maintenance_capacity: Optional[int] = Field(
        default=None,
        description="Maintenance service capacity"
    )
    turnover_capacity: Optional[int] = Field(
        default=None,
        description="Turnover service capacity"
    )
    initial_rehab_capacity: Optional[int] = Field(
        default=None,
        description="Initial rehabilitation service capacity"
    )
    total_service_capacity: Optional[int] = Field(
        default=None,
        description="Total service capacity"
    )
    service_types: List[int] = Field(
        default_factory=list,
        description="List of service type IDs"
    )
    inherit_global_working_hours: bool = Field(
        ...,
        description="Whether to inherit global working hours"
    )
    same_for_all_days: Optional[bool] = Field(
        default=False,
        description="Whether working hours are same for all days"
    )
    standard_business_hours: Optional[List[WorkingHourEntry]] = Field(
        default=None,
        description="Standard business hours"
    )
    emergency_and_after_hours_coverage: Optional[bool] = Field(
        default=False,
        description="Whether emergency and after hours coverage is available"
    )
    emergency_working_hours: Optional[List[EmergencyHourEntry]] = Field(
        default=None,
        description="Emergency working hours"
    )
    vendor_remarks: Optional[str] = Field(
        default=None,
        description="Additional remarks from vendor"
    )

    @model_validator(mode="after")
    def check_emergency_conflict(self) -> "ServiceAreaDetailsUpdateRequest":
        """Validate that emergency hours don't conflict with standard hours"""
        if not self.inherit_global_working_hours and self.emergency_and_after_hours_coverage:
            std_hours = {h.day_of_week: h for h in self.standard_business_hours or []}
            for e in self.emergency_working_hours or []:
                std = std_hours.get(e.day_of_week)
                if std and not e.is_24_hours and not std.is_24_hours:
                    if (e.start_time and e.end_time and std.start_time and std.end_time and
                            not (e.end_time <= std.start_time or e.start_time >= std.end_time)):
                        raise ValueError(f"Emergency hours overlap with standard hours on {e.day_of_week}")
        return self


# Common HTTP status code responses for OpenAPI documentation
COMMON_RESPONSES = {
    400: {
        "description": "Bad Request - Invalid input data",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "status": "error",
                    "message": {
                        "title": "Bad Request",
                        "description": "The request contains invalid or missing data"
                    },
                    "errors": [
                        {
                            "field": "email",
                            "message": "This field is required",
                            "code": "REQUIRED_FIELD"
                        }
                    ],
                    "metadata": {
                        "timestamp": "2024-01-01T12:00:00.000000",
                        "api_version": "1.0.0"
                    },
                    "error_code": "BAD_REQUEST"
                }
            }
        }
    },
    401: {
        "description": "Unauthorized - Authentication required",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "status": "error",
                    "message": {
                        "title": "Unauthorized",
                        "description": "Authentication is required to access this resource"
                    },
                    "metadata": {
                        "timestamp": "2024-01-01T12:00:00.000000",
                        "api_version": "1.0.0"
                    },
                    "error_code": "UNAUTHORIZED"
                }
            }
        }
    },
    403: {
        "description": "Forbidden - Insufficient permissions",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "status": "error",
                    "message": {
                        "title": "Forbidden",
                        "description": "You don't have permission to access this resource"
                    },
                    "metadata": {
                        "timestamp": "2024-01-01T12:00:00.000000",
                        "api_version": "1.0.0"
                    },
                    "error_code": "FORBIDDEN"
                }
            }
        }
    },
    404: {
        "description": "Not Found - Resource not found",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "status": "error",
                    "message": {
                        "title": "Not Found",
                        "description": "The requested resource was not found"
                    },
                    "metadata": {
                        "timestamp": "2024-01-01T12:00:00.000000",
                        "api_version": "1.0.0"
                    },
                    "error_code": "NOT_FOUND"
                }
            }
        }
    },
    422: {
        "description": "Unprocessable Entity - Validation error",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "status": "error",
                    "message": {
                        "title": "Validation Error",
                        "description": "The request data is invalid"
                    },
                    "errors": [
                        {
                            "field": "email",
                            "message": "Invalid email format",
                            "code": "INVALID_EMAIL"
                        }
                    ],
                    "metadata": {
                        "timestamp": "2024-01-01T12:00:00.000000",
                        "api_version": "1.0.0"
                    },
                    "error_code": "VALIDATION_ERROR"
                }
            }
        }
    },
    500: {
        "description": "Internal Server Error",
        "model": ErrorResponse,
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "status": "error",
                    "message": {
                        "title": "Internal Server Error",
                        "description": "An unexpected error occurred"
                    },
                    "metadata": {
                        "timestamp": "2024-01-01T12:00:00.000000",
                        "api_version": "1.0.0"
                    },
                    "error_code": "INTERNAL_SERVER_ERROR"
                }
            }
        }
    }
}
