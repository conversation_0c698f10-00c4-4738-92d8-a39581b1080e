from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.schemas.base_response import (
    SuccessResponse,
    PaginationInfo
)

class CategoryCreateRequest(BaseModel):
    """Request model for creating a category."""
    name: str = Field(..., example="Food & Beverage")
    description: Optional[str] = Field(None, example="All food-related businesses.")
    is_active: Optional[bool] = Field(default=True, example=True)
    is_deleted: Optional[bool] = Field(default=False, example=False)

class CategoryUpdateRequest(BaseModel):
    """Request model for updating a category."""
    name: Optional[str] = Field(None, example="Retail")
    description: Optional[str] = Field(None, example="Retail businesses.")
    is_active: Optional[bool] = Field(None, example=True)
    is_deleted: Optional[bool] = Field(None, example=False)

class CategoryResponse(BaseModel):
    """Response model for a category with UUID."""
    id: Optional[str] = Field(None, description="Category UUID", example="123e4567-e89b-12d3-a456-************")
    name: str
    description: Optional[str]
    is_active: bool
    is_deleted: bool
    created_at: datetime
    updated_at: datetime


class CategoryListResponse(BaseModel):
    """Response model for category list with pagination"""
    items: List[CategoryResponse] = Field(..., description="List of categories")
    total_count: int = Field(..., description="Total number of categories")
    pagination: PaginationInfo = Field(..., description="Pagination information")


class CategoryListSuccessResponse(SuccessResponse[CategoryListResponse]):
    """Success response for category list"""
    pass