"""
General Message Repository
Database operations for general message management
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from app.models.general_message import GeneralMessage
from app.schemas.general_message import GeneralMessageUpdateRequest
import logging

logger = logging.getLogger(__name__)


class GeneralMessageRepository:
    """Repository for general message operations."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_message_by_type(self, message_type: str) -> Optional[GeneralMessage]:
        """
        Get a general message by message_type.
        
        Args:
            message_type: Message type to fetch
            
        Returns:
            GeneralMessage object or None
        """
        try:
            query = select(GeneralMessage).where(GeneralMessage.message_type == message_type)
            result = await self.db.execute(query)
            message = result.scalar_one_or_none()
            return message
        except Exception as e:
            logger.error(f"Error fetching general message by type {message_type}: {e}")
            raise

    async def update_message_by_type(self, message_type: str, message_data: GeneralMessageUpdateRequest) -> Optional[GeneralMessage]:
        """
        Update a general message by message_type.
        
        Args:
            message_type: Message type to update
            message_data: Updated message data
            
        Returns:
            Updated GeneralMessage object or None
        """
        try:
            # First check if message exists
            existing_message = await self.get_message_by_type(message_type)
            if not existing_message:
                return None
            
            # Update the message
            query = update(GeneralMessage).where(
                GeneralMessage.message_type == message_type
            ).values(
                message=message_data.message
            )
            
            await self.db.execute(query)
            await self.db.commit()
            
            # Return updated message
            return await self.get_message_by_type(message_type)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating general message by type {message_type}: {e}")
            raise
