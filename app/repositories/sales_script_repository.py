"""
Sales Script Repository
Database operations for sales scripts
"""

import logging
from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.exc import IntegrityError

from app.models.sales_script import SalesScript
from app.repositories.base import BaseRepository
from app.schemas.sales_script import SalesScriptCreate, SalesScriptUpdate

logger = logging.getLogger(__name__)


class SalesScriptRepository(BaseRepository[SalesScript, SalesScriptCreate, SalesScriptUpdate]):
    """Repository for sales script operations"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(SalesScript, db)
    
    async def get_by_id(self, script_id: str) -> Optional[SalesScript]:
        """Get sales script by ID"""
        try:
            query = select(SalesScript).where(
                and_(
                    SalesScript.id == script_id,
                    SalesScript.is_deleted == False
                )
            )
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting sales script by ID {script_id}: {e}")
            raise
    
    async def get_all_with_filters(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        script_stage: Optional[str] = None,
        is_active: Optional[bool] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "asc",
        include_deleted: bool = False
    ) -> Tuple[List[SalesScript], int]:
        """Get sales scripts with filtering, search, and pagination"""
        try:
            # Base query
            query = select(SalesScript)
            count_query = select(func.count(SalesScript.id))
            
            # Build filters
            filters = []
            
            if not include_deleted:
                filters.append(SalesScript.is_deleted == False)
            
            if search:
                search_filter = or_(
                    SalesScript.script_title.ilike(f"%{search}%"),
                    SalesScript.script_content.ilike(f"%{search}%")
                )
                filters.append(search_filter)
            
            if script_stage:
                filters.append(SalesScript.script_stage == script_stage)
            
            if is_active is not None:
                filters.append(SalesScript.is_active == is_active)
            
            # Apply filters
            if filters:
                query = query.where(and_(*filters))
                count_query = count_query.where(and_(*filters))
            
            # Get total count
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()
            
            # Apply sorting
            if sort_by:
                sort_column = getattr(SalesScript, sort_by, None)
                if sort_column is not None:
                    if sort_order.lower() == "desc":
                        query = query.order_by(sort_column.desc())
                    else:
                        query = query.order_by(sort_column.asc())
                else:
                    # Default sorting if invalid sort_by
                    query = query.order_by(SalesScript.order_sequence.asc(), SalesScript.created_at.desc())
            else:
                # Default sorting by order_sequence and created_at
                query = query.order_by(SalesScript.order_sequence.asc(), SalesScript.created_at.desc())
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            scripts = result.scalars().all()
            
            return list(scripts), total_count
            
        except Exception as e:
            logger.error(f"Error getting sales scripts with filters: {e}")
            raise
    
    async def create_script(self, script_data: SalesScriptCreate) -> SalesScript:
        """Create a new sales script"""
        try:
            script = SalesScript(**script_data.model_dump())
            self.db.add(script)
            await self.db.commit()
            await self.db.refresh(script)
            
            logger.info(f"Created sales script: {script.id}")
            return script
            
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error creating sales script: {e}")
            raise ValueError("Sales script with this title already exists")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating sales script: {e}")
            raise
    
    async def update_script(self, script_id: str, script_data: SalesScriptUpdate) -> Optional[SalesScript]:
        """Update a sales script"""
        try:
            # Get existing script
            script = await self.get_by_id(script_id)
            if not script:
                return None
            
            # Update fields
            update_data = script_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(script, field, value)
            
            await self.db.commit()
            await self.db.refresh(script)
            
            logger.info(f"Updated sales script: {script.id}")
            return script
            
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error updating sales script: {e}")
            raise ValueError("Sales script with this title already exists")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating sales script {script_id}: {e}")
            raise
    
    async def delete_script(self, script_id: str) -> bool:
        """Soft delete a sales script"""
        try:
            script = await self.get_by_id(script_id)
            if not script:
                return False
            
            script.is_deleted = True
            script.deleted_at = func.now()
            
            await self.db.commit()
            
            logger.info(f"Deleted sales script: {script_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting sales script {script_id}: {e}")
            raise
    
    async def get_by_stage(self, script_stage: str, is_active: bool = True) -> List[SalesScript]:
        """Get sales scripts by stage"""
        try:
            filters = [
                SalesScript.script_stage == script_stage,
                SalesScript.is_deleted == False
            ]
            
            if is_active is not None:
                filters.append(SalesScript.is_active == is_active)
            
            query = select(SalesScript).where(and_(*filters)).order_by(SalesScript.order_sequence.asc())
            result = await self.db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting sales scripts by stage {script_stage}: {e}")
            raise
    
    async def get_by_title(self, script_title: str) -> Optional[SalesScript]:
        """Get sales script by title"""
        try:
            query = select(SalesScript).where(
                and_(
                    SalesScript.script_title == script_title,
                    SalesScript.is_deleted == False
                )
            )
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting sales script by title {script_title}: {e}")
            raise
