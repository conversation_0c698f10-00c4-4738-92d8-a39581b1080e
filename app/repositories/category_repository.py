from app.repositories.base import BaseRepository
from app.models.category import Category
from app.schemas.category import CategoryCreateRequest, CategoryUpdateRequest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional

class CategoryRepository(BaseRepository[Category, CategoryCreateRequest, CategoryUpdateRequest]):
    """Repository for Category data access."""
    def __init__(self, db: AsyncSession):
        super().__init__(Category, db)

    async def get_by_name(self, name: str) -> Optional[Category]:
        result = await self.db.execute(select(self.model).where(self.model.name == name))
        return result.scalar_one_or_none() 