"""
Conversation Message Repository - Data Access Layer
Repository implementation for conversation message operations
"""

import uuid
from typing import Optional, List, Tuple, Union
from datetime import datetime
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base import BaseRepository
from app.models.conversation_message import ConversationMessage
from app.models.lead import Lead
from app.schemas.conversation_message import ConversationMessageCreateRequest, ConversationMessageUpdateRequest
from app.core.logging import logger


class ConversationMessageRepository(BaseRepository[ConversationMessage, ConversationMessageCreateRequest, ConversationMessageUpdateRequest]):
    """Repository for ConversationMessage data access operations"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(ConversationMessage, db)
    
    async def get_by_id(self, message_id: Union[str, uuid.UUID], include_deleted: bool = False) -> Optional[ConversationMessage]:
        """Get conversation message by ID"""
        try:
            query = select(ConversationMessage).where(ConversationMessage.id == message_id)
            
            if not include_deleted:
                query = query.where(ConversationMessage.is_deleted == False)
            
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting conversation message by ID {message_id}: {e}")
            raise
    
    async def get_by_lead_id(
        self,
        lead_id: Union[str, uuid.UUID],
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
        order_by_created_at: str = "asc"  # "asc" or "desc"
    ) -> Tuple[List[ConversationMessage], int]:
        """Get conversation messages by lead ID with pagination"""
        try:
            # Base query
            query = select(ConversationMessage).where(ConversationMessage.lead_id == lead_id)
            
            if not include_deleted:
                query = query.where(ConversationMessage.is_deleted == False)
            
            # Count query for total
            count_query = select(func.count(ConversationMessage.id)).where(ConversationMessage.lead_id == lead_id)
            if not include_deleted:
                count_query = count_query.where(ConversationMessage.is_deleted == False)
            
            total_count_result = await self.db.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply ordering
            if order_by_created_at.lower() == "desc":
                query = query.order_by(desc(ConversationMessage.created_at))
            else:
                query = query.order_by(asc(ConversationMessage.created_at))
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            messages = list(result.scalars().all())
            
            return messages, total_count
            
        except Exception as e:
            logger.error(f"Error getting conversation messages by lead ID {lead_id}: {e}")
            raise
    
    async def get_by_franchisor_id(
        self,
        franchisor_id: Union[str, uuid.UUID],
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
        order_by_created_at: str = "asc"
    ) -> Tuple[List[ConversationMessage], int]:
        """Get conversation messages by franchisor ID with pagination"""
        try:
            # Base query
            query = select(ConversationMessage).where(ConversationMessage.franchisor_id == franchisor_id)
            
            if not include_deleted:
                query = query.where(ConversationMessage.is_deleted == False)
            
            # Count query for total
            count_query = select(func.count(ConversationMessage.id)).where(ConversationMessage.franchisor_id == franchisor_id)
            if not include_deleted:
                count_query = count_query.where(ConversationMessage.is_deleted == False)
            
            total_count_result = await self.db.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply ordering
            if order_by_created_at.lower() == "desc":
                query = query.order_by(desc(ConversationMessage.created_at))
            else:
                query = query.order_by(asc(ConversationMessage.created_at))
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            messages = list(result.scalars().all())
            
            return messages, total_count
            
        except Exception as e:
            logger.error(f"Error getting conversation messages by franchisor ID {franchisor_id}: {e}")
            raise
    
    async def get_conversation_between_lead_and_franchisor(
        self,
        lead_id: Union[str, uuid.UUID],
        franchisor_id: Union[str, uuid.UUID],
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
        order_by_created_at: str = "asc"
    ) -> Tuple[List[ConversationMessage], int]:
        """Get conversation messages between a specific lead and franchisor"""
        try:
            # Base query
            query = select(ConversationMessage).where(
                and_(
                    ConversationMessage.lead_id == lead_id,
                    ConversationMessage.franchisor_id == franchisor_id
                )
            )
            
            if not include_deleted:
                query = query.where(ConversationMessage.is_deleted == False)
            
            # Count query for total
            count_query = select(func.count(ConversationMessage.id)).where(
                and_(
                    ConversationMessage.lead_id == lead_id,
                    ConversationMessage.franchisor_id == franchisor_id
                )
            )
            if not include_deleted:
                count_query = count_query.where(ConversationMessage.is_deleted == False)
            
            total_count_result = await self.db.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply ordering
            if order_by_created_at.lower() == "desc":
                query = query.order_by(desc(ConversationMessage.created_at))
            else:
                query = query.order_by(asc(ConversationMessage.created_at))
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            messages = list(result.scalars().all())
            
            return messages, total_count
            
        except Exception as e:
            logger.error(f"Error getting conversation between lead {lead_id} and franchisor {franchisor_id}: {e}")
            raise
    
    async def get_by_sender(
        self,
        sender: str,
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
        order_by_created_at: str = "asc"
    ) -> Tuple[List[ConversationMessage], int]:
        """Get conversation messages by sender (lead or system)"""
        try:
            # Base query
            query = select(ConversationMessage).where(ConversationMessage.sender == sender)
            
            if not include_deleted:
                query = query.where(ConversationMessage.is_deleted == False)
            
            # Count query for total
            count_query = select(func.count(ConversationMessage.id)).where(ConversationMessage.sender == sender)
            if not include_deleted:
                count_query = count_query.where(ConversationMessage.is_deleted == False)
            
            total_count_result = await self.db.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply ordering
            if order_by_created_at.lower() == "desc":
                query = query.order_by(desc(ConversationMessage.created_at))
            else:
                query = query.order_by(asc(ConversationMessage.created_at))
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            messages = list(result.scalars().all())
            
            return messages, total_count
            
        except Exception as e:
            logger.error(f"Error getting conversation messages by sender {sender}: {e}")
            raise
    
    async def create_bulk(self, messages_data: List[ConversationMessageCreateRequest]) -> List[ConversationMessage]:
        """Create multiple conversation messages in bulk"""
        try:
            created_messages = []
            
            for message_data in messages_data:
                message_dict = message_data.model_dump()
                message_dict['id'] = uuid.uuid4()
                
                db_message = ConversationMessage(**message_dict)
                self.db.add(db_message)
                created_messages.append(db_message)
            
            if created_messages:
                await self.db.commit()
                for message in created_messages:
                    await self.db.refresh(message)
            
            return created_messages
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error in bulk conversation message creation: {e}")
            raise
    
    async def soft_delete(self, message_id: Union[str, uuid.UUID]) -> bool:
        """Soft delete a conversation message"""
        try:
            message = await self.get_by_id(message_id)
            if message:
                message.is_deleted = True
                message.is_active = False
                message.deleted_at = datetime.utcnow()
                await self.db.commit()
                await self.db.refresh(message)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting conversation message {message_id}: {e}")
            raise
    
    async def get_conversation_stats(self, lead_id: Optional[Union[str, uuid.UUID]] = None) -> dict:
        """Get conversation statistics"""
        try:
            base_filter = ConversationMessage.is_deleted == False
            
            if lead_id:
                base_filter = and_(base_filter, ConversationMessage.lead_id == lead_id)
            
            # Total messages
            total_query = select(func.count(ConversationMessage.id)).where(base_filter)
            total_result = await self.db.execute(total_query)
            total_messages = total_result.scalar()
            
            # Messages by sender
            sender_query = (
                select(ConversationMessage.sender, func.count(ConversationMessage.id))
                .where(base_filter)
                .group_by(ConversationMessage.sender)
            )
            sender_result = await self.db.execute(sender_query)
            sender_stats = dict(sender_result.fetchall())
            
            return {
                "total_messages": total_messages,
                "messages_by_sender": sender_stats,
                "lead_messages": sender_stats.get("lead", 0),
                "system_messages": sender_stats.get("system", 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation stats: {e}")
            raise
