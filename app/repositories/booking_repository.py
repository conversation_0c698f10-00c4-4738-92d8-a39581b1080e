"""
Booking Repository
Database operations for booking management
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func
from sqlalchemy.orm import selectinload
from app.models.booking import Booking, BookingHistory
from app.schemas.booking import BookingCreate, BookingUpdate
import logging
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)


class BookingRepository:
    """Repository for booking operations."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_booking(self, booking_data: Dict[str, Any]) -> Booking:
        """
        Create a new booking record.
        
        Args:
            booking_data: Dictionary containing booking information
            
        Returns:
            Created booking object
        """
        try:
            logger.info(f"Creating booking for customer: {booking_data.get('customer_name')}")
            
            # Convert UUID objects to strings for JSON serialization
            if booking_data.get('lead_id') and isinstance(booking_data['lead_id'], uuid.UUID):
                booking_data['lead_id'] = str(booking_data['lead_id'])
            
            # Convert booking_metadata UUIDs to strings
            if booking_data.get('booking_metadata'):
                booking_data['booking_metadata'] = self._convert_uuids_to_strings(booking_data['booking_metadata'])
            
            # Create booking object
            booking = Booking(
                zoho_booking_id=booking_data.get('zoho_booking_id'),
                zoho_service_id=booking_data.get('zoho_service_id'),
                zoho_staff_id=booking_data.get('zoho_staff_id'),
                lead_id=uuid.UUID(booking_data.get('lead_id')) if booking_data.get('lead_id') else None,
                customer_name=booking_data.get('customer_name'),
                customer_email=booking_data.get('customer_email'),
                customer_phone=booking_data.get('customer_phone'),
                service_type=booking_data.get('service_type'),
                staff_name=booking_data.get('staff_name'),
                staff_email=booking_data.get('staff_email'),
                start_time=booking_data.get('start_time'),
                end_time=booking_data.get('end_time'),
                duration_minutes=booking_data.get('duration_minutes'),
                timezone=booking_data.get('timezone', 'Australia/Canberra'),
                status=booking_data.get('status', 'scheduled'),
                booking_source=booking_data.get('booking_source', 'api'),
                booking_url=booking_data.get('booking_url'),
                meeting_link=booking_data.get('meeting_link'),
                notes=booking_data.get('notes'),
                booking_metadata=booking_data.get('booking_metadata')
            )
            
            self.db.add(booking)
            await self.db.commit()
            await self.db.refresh(booking)
            
            # Create booking history entry
            await self._create_booking_history(booking.id, 'created', None, booking_data)
            
            logger.info(f"✅ Successfully created booking: {booking.id}")
            return booking
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ Error creating booking: {e}")
            raise

    async def get_booking_by_id(self, booking_id: uuid.UUID) -> Optional[Booking]:
        """
        Get a booking by ID.
        
        Args:
            booking_id: Booking ID
            
        Returns:
            Booking object or None
        """
        try:
            query = select(Booking).options(
                selectinload(Booking.lead)
            ).where(Booking.id == booking_id)
            
            result = await self.db.execute(query)
            booking = result.scalar_one_or_none()
            
            return booking
        except Exception as e:
            logger.error(f"Error fetching booking {booking_id}: {e}")
            raise

    async def get_booking_by_zoho_id(self, zoho_booking_id: str) -> Optional[Booking]:
        """
        Get a booking by Zoho booking ID.
        
        Args:
            zoho_booking_id: Zoho booking ID
            
        Returns:
            Booking object or None
        """
        try:
            query = select(Booking).where(Booking.zoho_booking_id == zoho_booking_id)
            result = await self.db.execute(query)
            booking = result.scalar_one_or_none()
            
            return booking
        except Exception as e:
            logger.error(f"Error fetching booking by Zoho ID {zoho_booking_id}: {e}")
            raise

    async def get_bookings_by_lead_id(self, lead_id: uuid.UUID) -> List[Booking]:
        """
        Get all bookings for a specific lead.
        
        Args:
            lead_id: Lead ID
            
        Returns:
            List of booking objects
        """
        try:
            query = select(Booking).where(
                and_(
                    Booking.lead_id == lead_id,
                    Booking.status != 'cancelled'
                )
            ).order_by(Booking.start_time.desc())
            
            result = await self.db.execute(query)
            bookings = result.scalars().all()
            
            return list(bookings)
        except Exception as e:
            logger.error(f"Error fetching bookings for lead {lead_id}: {e}")
            raise

    async def update_booking_status(self, booking_id: uuid.UUID, new_status: str, 
                                   old_values: Optional[Dict] = None, 
                                   change_reason: Optional[str] = None) -> Optional[Booking]:
        """
        Update booking status.
        
        Args:
            booking_id: Booking ID
            new_status: New status
            old_values: Previous values for history
            change_reason: Reason for the change
            
        Returns:
            Updated booking object or None
        """
        try:
            booking = await self.get_booking_by_id(booking_id)
            if not booking:
                return None
            
            # Store old values for history
            old_values = old_values or {
                'status': booking.status,
                'updated_at': booking.updated_at
            }
            
            # Update booking
            booking.status = new_status
            booking.updated_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(booking)
            
            # Create history entry
            new_values = {'status': new_status, 'updated_at': booking.updated_at}
            await self._create_booking_history(booking_id, 'status_updated', old_values, new_values, change_reason)
            
            logger.info(f"✅ Updated booking {booking_id} status to: {new_status}")
            return booking
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ Error updating booking status: {e}")
            raise

    async def update_booking(self, booking_id: uuid.UUID, update_data: Dict[str, Any]) -> Optional[Booking]:
        """
        Update booking information.
        
        Args:
            booking_id: Booking ID
            update_data: Data to update
            
        Returns:
            Updated booking object or None
        """
        try:
            booking = await self.get_booking_by_id(booking_id)
            if not booking:
                return None
            
            # Store old values for history
            old_values = {
                'customer_name': booking.customer_name,
                'customer_email': booking.customer_email,
                'customer_phone': booking.customer_phone,
                'notes': booking.notes,
                'updated_at': booking.updated_at
            }
            
            # Update fields
            for field, value in update_data.items():
                if hasattr(booking, field) and value is not None:
                    setattr(booking, field, value)
            
            booking.updated_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(booking)
            
            # Create history entry
            new_values = {field: value for field, value in update_data.items() if value is not None}
            new_values['updated_at'] = booking.updated_at
            await self._create_booking_history(booking_id, 'updated', old_values, new_values)
            
            logger.info(f"✅ Updated booking: {booking_id}")
            return booking
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ Error updating booking: {e}")
            raise

    async def get_upcoming_bookings(self, limit: int = 10) -> List[Booking]:
        """
        Get upcoming bookings.
        
        Args:
            limit: Maximum number of bookings to return
            
        Returns:
            List of upcoming booking objects
        """
        try:
            now = datetime.utcnow()
            query = select(Booking).where(
                and_(
                    Booking.start_time > now,
                    Booking.status.in_(['scheduled', 'confirmed'])
                )
            ).order_by(Booking.start_time).limit(limit)
            
            result = await self.db.execute(query)
            bookings = result.scalars().all()
            
            return list(bookings)
        except Exception as e:
            logger.error(f"Error fetching upcoming bookings: {e}")
            raise

    async def get_bookings_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Booking]:
        """
        Get bookings within a date range.
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            List of booking objects
        """
        try:
            query = select(Booking).where(
                and_(
                    Booking.start_time >= start_date,
                    Booking.start_time <= end_date
                )
            ).order_by(Booking.start_time)
            
            result = await self.db.execute(query)
            bookings = result.scalars().all()
            
            return list(bookings)
        except Exception as e:
            logger.error(f"Error fetching bookings by date range: {e}")
            raise

    async def get_booking_statistics(self) -> Dict[str, Any]:
        """
        Get booking statistics.
        
        Returns:
            Dictionary with booking statistics
        """
        try:
            now = datetime.utcnow()
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Total bookings
            total_query = select(func.count(Booking.id))
            total_result = await self.db.execute(total_query)
            total_bookings = total_result.scalar()
            
            # Upcoming bookings
            upcoming_query = select(func.count(Booking.id)).where(
                and_(
                    Booking.start_time > now,
                    Booking.status.in_(['scheduled', 'confirmed'])
                )
            )
            upcoming_result = await self.db.execute(upcoming_query)
            upcoming_bookings = upcoming_result.scalar()
            
            # Today's bookings
            today_query = select(func.count(Booking.id)).where(
                and_(
                    Booking.start_time >= today_start,
                    Booking.start_time < today_start.replace(day=today_start.day + 1)
                )
            )
            today_result = await self.db.execute(today_query)
            today_bookings = today_result.scalar()
            
            # Completed bookings
            completed_query = select(func.count(Booking.id)).where(Booking.status == 'completed')
            completed_result = await self.db.execute(completed_query)
            completed_bookings = completed_result.scalar()
            
            # Cancelled bookings
            cancelled_query = select(func.count(Booking.id)).where(Booking.status == 'cancelled')
            cancelled_result = await self.db.execute(cancelled_query)
            cancelled_bookings = cancelled_result.scalar()
            
            return {
                'total_bookings': total_bookings,
                'upcoming_bookings': upcoming_bookings,
                'today_bookings': today_bookings,
                'completed_bookings': completed_bookings,
                'cancelled_bookings': cancelled_bookings
            }
            
        except Exception as e:
            logger.error(f"Error fetching booking statistics: {e}")
            raise

    async def _create_booking_history(self, booking_id: uuid.UUID, action: str, 
                                     old_values: Optional[Dict] = None, 
                                     new_values: Optional[Dict] = None,
                                     change_reason: Optional[str] = None) -> None:
        """
        Create a booking history entry.
        
        Args:
            booking_id: Booking ID
            action: Action performed
            old_values: Previous values
            new_values: New values
            change_reason: Reason for change
        """
        try:
            # Convert UUIDs to strings for JSON serialization
            old_values_serialized = self._convert_uuids_to_strings(old_values) if old_values else None
            new_values_serialized = self._convert_uuids_to_strings(new_values) if new_values else None
            
            history_entry = BookingHistory(
                booking_id=booking_id,
                action=action,
                old_values=old_values_serialized,
                new_values=new_values_serialized,
                changed_by='system',
                change_reason=change_reason
            )
            
            self.db.add(history_entry)
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"Error creating booking history: {e}")
            # Don't raise here as it's not critical for the main operation

    def _convert_uuids_to_strings(self, data: Any) -> Any:
        """
        Recursively convert UUID and datetime objects to strings for JSON serialization.
        
        Args:
            data: Data that may contain UUID or datetime objects
            
        Returns:
            Data with UUIDs and datetimes converted to strings
        """
        if isinstance(data, dict):
            return {key: self._convert_uuids_to_strings(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._convert_uuids_to_strings(item) for item in data]
        elif isinstance(data, uuid.UUID):
            return str(data)
        elif isinstance(data, datetime):
            return data.isoformat()
        else:
            return data 