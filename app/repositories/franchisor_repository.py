"""
Franchisor Repository - Data Access Layer
Production-grade repository implementation following established patterns
"""

import uuid
from typing import Optional, List, Tuple, Union
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from app.repositories.base import BaseRepository
from app.models.franchisor import Franchisor
from app.models.industry import Industry

from app.schemas.franchisor import FranchisorCreateRequest, FranchisorUpdateRequest
from app.core.logging import logger
from app.core.utils.exception_manager.custom_exceptions import (
    ValidationError,
    DatabaseError
)


class FranchisorRepository(BaseRepository[Franchisor, FranchisorCreateRequest, FranchisorUpdateRequest]):
    """Repository for Franchisor data access operations"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Franchi<PERSON>, db)

    async def create(self, obj_in: FranchisorCreateRequest) -> Franchisor:
        """
        Create a new franchisor with field name mapping for database compatibility

        Args:
            obj_in: Franchisor creation data with camelCase field names

        Returns:
            Created franchisor model
        """
        import time
        from app.core.logging import log_database_operation

        start_time = time.time()
        try:
            # Get the data and map camelCase to lowercase for database
            obj_data = obj_in.model_dump()

            # Map camelCase schema fields to lowercase database fields
            if 'contactFirstName' in obj_data:
                obj_data['contactfirstname'] = obj_data.pop('contactFirstName')
            if 'contactLastName' in obj_data:
                obj_data['contactlastname'] = obj_data.pop('contactLastName')

            # Convert industry_id string to UUID object for database
            if 'industry_id' in obj_data and obj_data['industry_id'] is not None:
                try:
                    import uuid as uuid_module
                    obj_data['industry_id'] = uuid_module.UUID(obj_data['industry_id'])
                    logger.info(f"Converted industry_id string to UUID for create: {obj_data['industry_id']}")
                except ValueError as e:
                    logger.error(f"Failed to convert industry_id to UUID in create: {e}")
                    # Remove invalid industry_id to prevent database error
                    obj_data.pop('industry_id', None)

            db_obj = self.model(**obj_data)
            self.db.add(db_obj)
            await self.db.commit()
            await self.db.refresh(db_obj)

            # Log successful operation
            duration = time.time() - start_time
            log_database_operation(
                operation="CREATE",
                table=self.model.__tablename__,
                success=True,
                duration=duration
            )

            return db_obj
        except Exception as e:
            await self.db.rollback()
            duration = time.time() - start_time
            log_database_operation(
                operation="CREATE",
                table=self.model.__tablename__,
                success=False,
                duration=duration
            )
            logger.error(f"Error creating {self.model.__name__}: {str(e)}", exc_info=True)
            raise

    async def update(self, db_obj: Franchisor, obj_in: FranchisorUpdateRequest) -> Franchisor:
        """
        Update an existing franchisor with field name mapping for database compatibility

        Args:
            db_obj: Existing franchisor model instance
            obj_in: Franchisor update data with camelCase field names

        Returns:
            Updated franchisor model
        """
        import time
        from app.core.logging import log_database_operation

        start_time = time.time()
        try:
            # Get the data and map camelCase to lowercase for database
            obj_data = obj_in.model_dump(exclude_unset=True)
            logger.info(f"Franchisor update data received: {obj_data}")

            # Map camelCase schema fields to lowercase database fields
            if 'contactFirstName' in obj_data:
                obj_data['contactfirstname'] = obj_data.pop('contactFirstName')
            if 'contactLastName' in obj_data:
                obj_data['contactlastname'] = obj_data.pop('contactLastName')

            logger.info(f"Franchisor update data after field mapping: {obj_data}")

            for field, value in obj_data.items():
                if hasattr(db_obj, field):
                    old_value = getattr(db_obj, field, None)

                    # Convert industry_id string to UUID object for database
                    if field == 'industry_id' and value is not None:
                        try:
                            import uuid as uuid_module
                            value = uuid_module.UUID(value)
                            logger.info(f"Converted industry_id string to UUID: {value}")
                        except ValueError as e:
                            logger.error(f"Failed to convert industry_id to UUID: {e}")
                            continue

                    setattr(db_obj, field, value)
                    logger.info(f"Updated field '{field}': {old_value} -> {value}")
                else:
                    logger.warning(f"Field '{field}' not found on franchisor model")

            await self.db.commit()
            await self.db.refresh(db_obj)

            # Log successful operation
            duration = time.time() - start_time
            log_database_operation(
                operation="UPDATE",
                table=self.model.__tablename__,
                success=True,
                duration=duration
            )

            return db_obj
        except Exception as e:
            await self.db.rollback()
            duration = time.time() - start_time
            log_database_operation(
                operation="UPDATE",
                table=self.model.__tablename__,
                success=False,
                duration=duration
            )
            logger.error(f"Error updating {self.model.__name__}: {str(e)}", exc_info=True)
            raise
    
    async def get_by_id(self, franchisor_id: Union[str, uuid.UUID]) -> Optional[Franchisor]:
        """Get franchisor by ID with relationships"""
        try:
            query = (
                select(Franchisor)
                .options(
                    selectinload(Franchisor.industry_rel)
                )
                .where(
                    and_(
                        Franchisor.id == franchisor_id,
                        Franchisor.is_deleted == False
                    )
                )
            )
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except SQLAlchemyError as db_error:
            logger.error(f"Database error getting franchisor by ID {franchisor_id}: {db_error}")
            raise DatabaseError(
                error_key="DATABASE_QUERY_FAILED",
                message=f"Failed to retrieve franchisor {franchisor_id} due to database error"
            )
        except Exception as e:
            logger.error(f"Unexpected error getting franchisor by ID {franchisor_id}: {e}", exc_info=True)
            raise DatabaseError(
                error_key="FRANCHISOR_QUERY_FAILED",
                message=f"An unexpected error occurred while retrieving franchisor: {str(e)}"
            )

    async def get_by_id_with_relations(self, franchisor_id: Union[str, uuid.UUID], include_deleted: bool = False) -> Optional[Franchisor]:
        """Get franchisor by ID with relationships - alias for get_by_id with include_deleted option"""
        try:
            query = (
                select(Franchisor)
                .options(
                    selectinload(Franchisor.industry_rel)
                )
                .where(Franchisor.id == franchisor_id)
            )

            if not include_deleted:
                query = query.where(Franchisor.is_deleted == False)

            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting franchisor by ID with relations {franchisor_id}: {e}")
            raise
    
    async def get_by_name(self, name: str) -> Optional[Franchisor]:
        """Get franchisor by name"""
        try:
            query = select(Franchisor).where(
                and_(
                    Franchisor.name == name,
                    Franchisor.is_deleted == False
                )
            )
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting franchisor by name {name}: {e}")
            raise
    
    async def get_multi_with_filters(
        self,
        skip: int = 0,
        limit: int = 100,
        industry: Optional[str] = None,
        region: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "asc"
    ) -> Tuple[List[Franchisor], int]:
        """Get franchisors with filtering and pagination"""
        try:
            # Base query with relationships
            query = (
                select(Franchisor)
                .options(
                    selectinload(Franchisor.industry_rel)
                )
                .where(Franchisor.is_deleted == False)
            )
            
            # Track if we've already joined Industry table
            industry_joined = False

            # Apply filters
            if industry:
                # Check if industry is a UUID (ID) or a name
                try:
                    # Try to parse as UUID - if successful, filter by ID
                    import uuid
                    industry_uuid = uuid.UUID(industry)
                    query = query.where(Franchisor.industry_id == industry_uuid)
                except (ValueError, TypeError):
                    # If not a valid UUID, filter by name
                    query = query.join(Industry).where(Industry.name.ilike(f"%{industry}%"))
                    industry_joined = True
            
            if region:
                query = query.where(Franchisor.region.ilike(f"%{region}%"))
            
            if is_active is not None:
                query = query.where(Franchisor.is_active == is_active)
            
            if search:
                search_filter = or_(
                    Franchisor.name.ilike(f"%{search}%"),
                    Franchisor.region.ilike(f"%{search}%")
                )
                query = query.where(search_filter)

            # Apply sorting
            if sort_by:
                sort_column = None
                if sort_by == "name":
                    sort_column = Franchisor.name
                elif sort_by == "contactfirstname":
                    sort_column = Franchisor.contactfirstname
                elif sort_by == "contactlastname":
                    sort_column = Franchisor.contactlastname
                elif sort_by == "franchisor_won_id":
                    sort_column = Franchisor.franchisor_won_id
                elif sort_by == "industry_name":
                    # Only join if we haven't already joined for filtering
                    if not industry_joined:
                        query = query.join(Industry, isouter=True)

                    # Handle NULL values properly for DESC sorting
                    if sort_order.lower() == "desc":
                        # NULL values last in DESC order
                        sort_column = Industry.name.desc().nullslast()
                    else:
                        # NULL values last in ASC order too
                        sort_column = Industry.name.asc().nullslast()
                elif sort_by == "is_active":
                    sort_column = Franchisor.is_active
                elif sort_by == "region":
                    sort_column = Franchisor.region
                elif sort_by == "budget":
                    sort_column = Franchisor.budget
                elif sort_by == "created_at":
                    sort_column = Franchisor.created_at

                if sort_column is not None:
                    # For industry_name, we already handled NULL values above
                    if sort_by == "industry_name":
                        query = query.order_by(sort_column)
                    else:
                        if sort_order.lower() == "desc":
                            query = query.order_by(sort_column.desc())
                        else:
                            query = query.order_by(sort_column.asc())
            else:
                # Default sorting by name
                query = query.order_by(Franchisor.name.asc())
            
            # Get total count
            count_query = select(func.count(Franchisor.id)).where(Franchisor.is_deleted == False)
            if industry:
                # Apply same industry filter logic for count
                try:
                    import uuid
                    industry_uuid = uuid.UUID(industry)
                    count_query = count_query.where(Franchisor.industry_id == industry_uuid)
                except (ValueError, TypeError):
                    count_query = count_query.join(Industry).where(Industry.name.ilike(f"%{industry}%"))
            if region:
                count_query = count_query.where(Franchisor.region.ilike(f"%{region}%"))
            if is_active is not None:
                count_query = count_query.where(Franchisor.is_active == is_active)
            if search:
                count_query = count_query.where(search_filter)
            
            total_count_result = await self.db.execute(count_query)
            total_count = total_count_result.scalar()
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            franchisors = list(result.scalars().all())
            
            return franchisors, total_count
            
        except SQLAlchemyError as db_error:
            logger.error(f"Database error getting franchisors with filters: {db_error}")
            raise DatabaseError(
                error_key="DATABASE_QUERY_FAILED",
                message="Failed to retrieve franchisors due to database error"
            )
        except Exception as e:
            logger.error(f"Unexpected error getting franchisors with filters: {e}", exc_info=True)
            raise DatabaseError(
                error_key="FRANCHISORS_QUERY_FAILED",
                message=f"An unexpected error occurred while querying franchisors: {str(e)}"
            )
    

    
    async def get_categories(self) -> List[Industry]:
        """Get all active industries for franchisor endpoints"""
        try:
            query = select(Industry).where(
                and_(
                    Industry.is_active == True,
                    Industry.is_deleted == False
                )
            ).order_by(Industry.name)
            
            result = await self.db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            raise
    

    
    async def soft_delete(self, franchisor_id: Union[str, uuid.UUID]) -> bool:
        """Soft delete a franchisor"""
        try:
            franchisor = await self.get_by_id(franchisor_id)
            if franchisor:
                franchisor.is_deleted = True
                franchisor.is_active = False
                await self.db.commit()
                await self.db.refresh(franchisor)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error soft deleting franchisor {franchisor_id}: {e}")
            raise
    
    async def activate(self, franchisor_id: Union[str, uuid.UUID]) -> bool:
        """Activate a franchisor"""
        try:
            franchisor = await self.get_by_id(franchisor_id)
            if franchisor and not franchisor.is_deleted:
                franchisor.is_active = True
                await self.db.commit()
                await self.db.refresh(franchisor)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error activating franchisor {franchisor_id}: {e}")
            raise
    
    async def deactivate(self, franchisor_id: Union[str, uuid.UUID]) -> bool:
        """Deactivate a franchisor"""
        try:
            franchisor = await self.get_by_id(franchisor_id)
            if franchisor and not franchisor.is_deleted:
                franchisor.is_active = False
                await self.db.commit()
                await self.db.refresh(franchisor)
                return True
            return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deactivating franchisor {franchisor_id}: {e}")
            raise

    async def update_status(self, franchisor_id: Union[str, uuid.UUID], is_active: bool) -> Optional[Franchisor]:
        """Update franchisor status (active/inactive)"""
        try:
            franchisor = await self.get_by_id(franchisor_id)
            if franchisor and not franchisor.is_deleted:
                franchisor.is_active = is_active
                await self.db.commit()
                await self.db.refresh(franchisor)
                return franchisor
            return None
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating franchisor status {franchisor_id}: {e}")
            raise

    async def update_brochure_url(self, franchisor_id: Union[str, uuid.UUID], brochure_url: str) -> Optional[Franchisor]:
        """Update franchisor brochure URL"""
        try:
            franchisor = await self.get_by_id(franchisor_id)
            if franchisor and not franchisor.is_deleted:
                franchisor.brochure_url = brochure_url
                await self.db.commit()
                await self.db.refresh(franchisor)
                return franchisor
            return None
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating franchisor brochure URL {franchisor_id}: {e}")
            raise
