from app.repositories.base import BaseRepository
from app.models.industry import Industry
from app.schemas.industry import IndustryCreateRequest, IndustryUpdateRequest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional

class IndustryRepository(BaseRepository[Industry, IndustryCreateRequest, IndustryUpdateRequest]):
    """Repository for Industry data access."""
    def __init__(self, db: AsyncSession):
        super().__init__(Industry, db)

    async def get_by_name(self, name: str) -> Optional[Industry]:
        result = await self.db.execute(select(self.model).where(self.model.name == name))
        return result.scalar_one_or_none()
