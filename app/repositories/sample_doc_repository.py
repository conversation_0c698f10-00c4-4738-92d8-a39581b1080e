"""
Sample Document repository for database operations
"""

from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.sample_doc import SampleDoc


class SampleDocRepository:
    """Repository for sample document operations"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_all_docs(self) -> List[SampleDoc]:
        """
        Get all sample documents without any filtering, sorting, or pagination

        Returns:
            List[SampleDoc]: List of all sample documents
        """
        try:
            query = select(SampleDoc)
            result = await self.db.execute(query)
            docs = result.scalars().all()
            return list(docs)
        except Exception as e:
            print(f"Repository error: {e}")
            raise
