"""
Meeting Booking Natural Language Understanding
OpenAI-powered intent detection and slot extraction for meeting booking
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import structlog
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from app.core.config.settings import settings
from .fsm import MeetingIntent, MeetingContext

from app.core.logging import logger


class MeetingNLU:
    """
    Natural Language Understanding for meeting booking
    Uses OpenAI for robust intent detection and slot extraction
    """
    
    def __init__(self):
        try:
            self.llm = ChatOpenAI(
                model="gpt-4",
                temperature=0.1,
                max_tokens=500
            )
            self.openai_available = True
        except Exception as e:
            logger.warning(f"OpenAI not available: {e}")
            self.llm = None
            self.openai_available = False
        
        # Date/time patterns for fallback parsing
        self.date_patterns = [
            r'\b(\d{1,2})\s*(st|nd|rd|th)?\s*(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec|january|february|march|april|may|june|july|august|september|october|november|december)\b',
            r'\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec|january|february|march|april|may|june|july|august|september|october|november|december)\s*(\d{1,2})\s*(st|nd|rd|th)?\b',
            r'\b(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})\b',
            r'\b(today|tomorrow|next\s+week|this\s+week)\b',
            r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b'
        ]
        
        self.time_patterns = [
            r'\b(\d{1,2}):(\d{2})\s*(am|pm|AM|PM)\b',
            r'\b(\d{1,2})\s*(am|pm|AM|PM)\b',
            r'\b(\d{1,2}):(\d{2})\b',
            r'\b(morning|afternoon|evening|night)\b',
            r'\b(noon|midnight)\b'
        ]
    
    async def detect_intent(self, user_message: str, context: MeetingContext) -> MeetingIntent:
        """Detect user intent using OpenAI or fallback patterns"""
        if not self.openai_available:
            return self._detect_intent_fallback(user_message, context)

        try:
            system_prompt = """You are an expert at detecting meeting booking intents. 
            
            Analyze the user message and return ONLY one of these intents:
            - SCHEDULE: User wants to schedule a new meeting
            - RESCHEDULE: User wants to change an existing meeting
            - CANCEL: User wants to cancel a meeting
            - AVAILABILITY: User is asking about available times
            - CONFIRM: User is confirming a proposed time/date
            - DEFER: User wants to postpone the decision
            - CLARIFY: User is asking for clarification
            - UNKNOWN: Intent is unclear
            
            Consider the conversation context and current state.
            
            Examples:
            "I want to schedule a meeting" -> SCHEDULE
            "Can we do Tuesday?" -> SCHEDULE (if no existing booking) or RESCHEDULE (if existing booking)
            "What times do you have?" -> AVAILABILITY
            "Yes, that works" -> CONFIRM
            "Let me check and get back" -> DEFER
            "Cancel my meeting" -> CANCEL
            
            Return ONLY the intent name."""
            
            user_prompt = f"""
            Current conversation state: {context.current_state.value}
            Has existing booking: {bool(context.booking_id)}
            User message: "{user_message}"
            
            Intent:"""
            
            response = await self.llm.ainvoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ])
            
            intent_str = response.content.strip().upper()
            
            try:
                return MeetingIntent(intent_str.lower())
            except ValueError:
                logger.warning(f"Unknown intent detected: {intent_str}")
                return MeetingIntent.UNKNOWN
                
        except Exception as e:
            logger.error(f"Error detecting intent: {e}")
            return MeetingIntent.UNKNOWN
    
    async def extract_datetime_slots(self, user_message: str, context: MeetingContext) -> Dict[str, Any]:
        """Extract date/time information using OpenAI with fallback patterns"""
        if not self.openai_available:
            return self._extract_with_patterns(user_message)

        try:
            system_prompt = """You are an expert at extracting date and time information from natural language.

            Extract the following information from the user message:
            - dates: List of specific dates mentioned (format: YYYY-MM-DD)
            - times: List of specific times mentioned (format: HH:MM in 24h)
            - date_ranges: Date ranges like "3rd or 4th"
            - time_ranges: Time ranges like "morning", "afternoon"
            - relative_dates: Relative references like "tomorrow", "next week"
            - weekdays: Specific weekdays mentioned
            - anytime_indicators: Words indicating flexibility like "anytime", "any time"
            
            Today's date for reference: {datetime.now().strftime('%Y-%m-%d')}
            Current timezone: {context.timezone}
            
            Return a JSON object with the extracted information.
            If nothing is found for a category, return an empty list.
            
            Examples:
            "Can we do Tuesday at 2pm?" -> {{"dates": [], "times": ["14:00"], "weekdays": ["tuesday"], "date_ranges": [], "time_ranges": [], "relative_dates": [], "anytime_indicators": []}}
            "I'm free tomorrow morning" -> {{"dates": [], "times": [], "weekdays": [], "date_ranges": [], "time_ranges": ["morning"], "relative_dates": ["tomorrow"], "anytime_indicators": []}}
            "Anytime next week" -> {{"dates": [], "times": [], "weekdays": [], "date_ranges": [], "time_ranges": [], "relative_dates": ["next week"], "anytime_indicators": ["anytime"]}}
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Extract datetime info from: '{user_message}'")
            ])
            
            try:
                extracted = json.loads(response.content.strip())
                
                # Validate and clean the extracted data
                cleaned = {
                    "dates": extracted.get("dates", []),
                    "times": extracted.get("times", []),
                    "date_ranges": extracted.get("date_ranges", []),
                    "time_ranges": extracted.get("time_ranges", []),
                    "relative_dates": extracted.get("relative_dates", []),
                    "weekdays": extracted.get("weekdays", []),
                    "anytime_indicators": extracted.get("anytime_indicators", [])
                }
                
                # Add fallback pattern matching
                fallback_data = self._extract_with_patterns(user_message)
                
                # Merge fallback data
                for key, values in fallback_data.items():
                    if values and not cleaned[key]:
                        cleaned[key] = values
                
                return cleaned
                
            except json.JSONDecodeError:
                logger.warning("Failed to parse OpenAI datetime extraction response")
                return self._extract_with_patterns(user_message)
                
        except Exception as e:
            logger.error(f"Error extracting datetime slots: {e}")
            return self._extract_with_patterns(user_message)
    
    def _extract_with_patterns(self, user_message: str) -> Dict[str, Any]:
        """Fallback pattern-based extraction"""
        message_lower = user_message.lower()
        
        extracted = {
            "dates": [],
            "times": [],
            "date_ranges": [],
            "time_ranges": [],
            "relative_dates": [],
            "weekdays": [],
            "anytime_indicators": []
        }
        
        # Extract dates with patterns
        for pattern in self.date_patterns:
            matches = re.findall(pattern, message_lower, re.IGNORECASE)
            if matches:
                extracted["dates"].extend([str(match) for match in matches])
        
        # Extract times with patterns
        for pattern in self.time_patterns:
            matches = re.findall(pattern, message_lower, re.IGNORECASE)
            if matches:
                extracted["times"].extend([str(match) for match in matches])
        
        # Extract weekdays
        weekdays = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        for weekday in weekdays:
            if weekday in message_lower:
                extracted["weekdays"].append(weekday)
        
        # Extract relative dates
        relative_terms = ["today", "tomorrow", "next week", "this week"]
        for term in relative_terms:
            if term in message_lower:
                extracted["relative_dates"].append(term)
        
        # Extract time ranges
        time_ranges = ["morning", "afternoon", "evening", "night", "noon", "midnight"]
        for time_range in time_ranges:
            if time_range in message_lower:
                extracted["time_ranges"].append(time_range)
        
        # Extract anytime indicators
        anytime_terms = ["anytime", "any time", "flexible", "whenever"]
        for term in anytime_terms:
            if term in message_lower:
                extracted["anytime_indicators"].append(term)
        
        return extracted

    def _detect_intent_fallback(self, user_message: str, context: MeetingContext) -> MeetingIntent:
        """Fallback intent detection using patterns"""
        message_lower = user_message.lower().strip()

        # Schedule intent patterns
        if any(word in message_lower for word in ["schedule", "book", "meeting", "appointment", "call"]):
            return MeetingIntent.SCHEDULE

        # Cancel intent patterns
        if any(word in message_lower for word in ["cancel", "delete", "remove"]):
            return MeetingIntent.CANCEL

        # Reschedule intent patterns
        if any(word in message_lower for word in ["reschedule", "change", "move", "different"]):
            return MeetingIntent.RESCHEDULE

        # Availability intent patterns
        if any(word in message_lower for word in ["available", "times", "slots", "when"]):
            return MeetingIntent.AVAILABILITY

        # Confirm intent patterns
        if any(word in message_lower for word in ["yes", "ok", "sure", "works", "good", "fine"]):
            return MeetingIntent.CONFIRM

        # Defer intent patterns
        if any(word in message_lower for word in ["later", "check", "get back", "think"]):
            return MeetingIntent.DEFER

        return MeetingIntent.UNKNOWN

    async def generate_clarification_prompt(self, context: MeetingContext,
                                          missing_info: str) -> str:
        """Generate natural clarification prompt using OpenAI or fallback"""
        if not self.openai_available:
            return self._generate_clarification_fallback(missing_info)

        try:
            system_prompt = """You are Andy, a friendly Australian meeting booking assistant.
            
            Generate a natural, concise clarification question to get the missing information.
            
            Style guidelines:
            - Conversational and friendly
            - Australian casual tone
            - No emojis
            - Keep it brief and clear
            - Use natural contractions
            
            The user is trying to book a meeting and we need to clarify some information."""
            
            user_prompt = f"""
            Current context:
            - State: {context.current_state.value}
            - Has date: {bool(context.target_date_local)}
            - Has time: {bool(context.target_time_local)}
            - Missing info: {missing_info}
            - Clarification attempts: {context.clarification_count}
            
            Generate a clarification question for the missing information.
            """
            
            response = await self.llm.ainvoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ])
            
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error generating clarification prompt: {e}")
            
            return self._generate_clarification_fallback(missing_info)

    def _generate_clarification_fallback(self, missing_info: str) -> str:
        """Fallback clarification templates"""
        if missing_info == "date":
            return "Which day would work best for you?"
        elif missing_info == "time":
            return "What time would you prefer?"
        else:
            return "Could you provide a bit more detail about when you'd like to meet?"
