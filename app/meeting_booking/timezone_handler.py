"""
Timezone Handler for Meeting Booking
Robust timezone conversion and date/time resolution
"""

import pytz
from datetime import datetime, timedelta, time
from typing import Dict, Any, Optional, List, Tuple
import structlog
from dateutil import parser
from dateutil.relativedelta import relativedelta

from app.core.config.settings import settings

from app.core.logging import logger


class TimezoneHandler:
    """
    Handles timezone conversions and date/time resolution for meeting booking
    Stores UTC in DB, displays local time to users
    """
    
    def __init__(self, default_timezone: str = None):
        self.default_timezone = default_timezone or settings.MEETING_AGENT_DEFAULT_TIMEZONE
        self.default_tz = pytz.timezone(self.default_timezone)
        
        # Business hours configuration
        self.business_start = time(9, 0)  # 9:00 AM
        self.business_end = time(17, 0)   # 5:00 PM
        self.business_days = [0, 1, 2, 3, 4]  # Monday to Friday
    
    def get_user_timezone(self, timezone_str: str = None) -> pytz.BaseTzInfo:
        """Get user timezone, fallback to default"""
        try:
            if timezone_str:
                return pytz.timezone(timezone_str)
            return self.default_tz
        except pytz.UnknownTimeZoneError:
            logger.warning(f"Unknown timezone: {timezone_str}, using default")
            return self.default_tz
    
    def now_in_timezone(self, timezone_str: str = None) -> datetime:
        """Get current time in specified timezone"""
        tz = self.get_user_timezone(timezone_str)
        return datetime.now(tz)
    
    def to_utc(self, dt: datetime, timezone_str: str = None) -> datetime:
        """Convert local datetime to UTC"""
        try:
            if dt.tzinfo is None:
                # Assume local timezone if no timezone info
                tz = self.get_user_timezone(timezone_str)
                dt = tz.localize(dt)
            
            return dt.astimezone(pytz.UTC)
        except Exception as e:
            logger.error(f"Error converting to UTC: {e}")
            return dt
    
    def from_utc(self, dt: datetime, timezone_str: str = None) -> datetime:
        """Convert UTC datetime to local timezone"""
        try:
            if dt.tzinfo is None:
                dt = pytz.UTC.localize(dt)
            
            tz = self.get_user_timezone(timezone_str)
            return dt.astimezone(tz)
        except Exception as e:
            logger.error(f"Error converting from UTC: {e}")
            return dt
    
    def resolve_relative_date(self, relative_term: str, 
                            reference_date: datetime = None) -> Optional[datetime]:
        """Resolve relative date terms like 'tomorrow', 'next week'"""
        if not reference_date:
            reference_date = self.now_in_timezone()
        
        relative_term = relative_term.lower().strip()
        
        try:
            if relative_term == "today":
                return reference_date.replace(hour=0, minute=0, second=0, microsecond=0)
            
            elif relative_term == "tomorrow":
                return reference_date.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            
            elif relative_term in ["next week", "next_week"]:
                # Next Monday
                days_ahead = 7 - reference_date.weekday()
                if days_ahead == 7:  # If today is Monday, next week is 7 days ahead
                    days_ahead = 7
                return reference_date.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_ahead)
            
            elif relative_term in ["this week", "this_week"]:
                # This coming Monday (or today if it's Monday)
                days_ahead = -reference_date.weekday()
                if days_ahead <= 0:
                    days_ahead = 0
                return reference_date.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_ahead)
            
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error resolving relative date '{relative_term}': {e}")
            return None
    
    def resolve_weekday(self, weekday: str, reference_date: datetime = None) -> Optional[datetime]:
        """Resolve weekday to next occurrence"""
        if not reference_date:
            reference_date = self.now_in_timezone()
        
        weekday_map = {
            "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
            "friday": 4, "saturday": 5, "sunday": 6
        }
        
        weekday = weekday.lower().strip()
        if weekday not in weekday_map:
            return None
        
        target_weekday = weekday_map[weekday]
        current_weekday = reference_date.weekday()
        
        # Calculate days ahead
        days_ahead = target_weekday - current_weekday
        if days_ahead <= 0:  # If it's today or past, get next week
            days_ahead += 7
        
        target_date = reference_date.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_ahead)
        return target_date
    
    def resolve_time_range(self, time_range: str) -> Tuple[time, time]:
        """Resolve time range to specific times"""
        time_range = time_range.lower().strip()
        
        time_ranges = {
            "morning": (time(9, 0), time(12, 0)),
            "afternoon": (time(13, 0), time(17, 0)),
            "evening": (time(17, 0), time(20, 0)),
            "night": (time(20, 0), time(23, 0)),
            "noon": (time(12, 0), time(12, 30)),
            "midnight": (time(0, 0), time(0, 30))
        }
        
        return time_ranges.get(time_range, (self.business_start, self.business_end))
    
    def parse_time_string(self, time_str: str) -> Optional[time]:
        """Parse time string to time object"""
        try:
            time_str = time_str.lower().strip()
            
            # Handle common formats
            if "am" in time_str or "pm" in time_str:
                dt = parser.parse(time_str)
                return dt.time()
            
            # Handle 24-hour format
            if ":" in time_str:
                parts = time_str.split(":")
                if len(parts) == 2:
                    hour = int(parts[0])
                    minute = int(parts[1])
                    return time(hour, minute)
            
            # Handle hour only
            if time_str.isdigit():
                hour = int(time_str)
                if 1 <= hour <= 12:
                    # Assume business hours context
                    if hour < 8:
                        hour += 12  # PM
                    return time(hour, 0)
                elif 0 <= hour <= 23:
                    return time(hour, 0)
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing time string '{time_str}': {e}")
            return None
    
    def is_business_day(self, dt: datetime) -> bool:
        """Check if date is a business day"""
        return dt.weekday() in self.business_days
    
    def is_business_hours(self, dt: datetime) -> bool:
        """Check if datetime is within business hours"""
        return self.business_start <= dt.time() <= self.business_end
    
    def get_next_business_day(self, reference_date: datetime = None) -> datetime:
        """Get next business day"""
        if not reference_date:
            reference_date = self.now_in_timezone()
        
        next_day = reference_date.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        
        while not self.is_business_day(next_day):
            next_day += timedelta(days=1)
        
        return next_day
    
    def format_datetime_local(self, dt: datetime, timezone_str: str = None, 
                            include_date: bool = True, include_time: bool = True) -> str:
        """Format datetime for user display in local timezone"""
        try:
            local_dt = self.from_utc(dt, timezone_str) if dt.tzinfo else dt
            
            parts = []
            
            if include_date:
                # Format: "1 September" or "Monday, 1 September"
                date_part = local_dt.strftime("%d %B").lstrip("0")
                weekday = local_dt.strftime("%A")
                parts.append(f"{weekday}, {date_part}")
            
            if include_time:
                # Format: "10:00 AM"
                time_part = local_dt.strftime("%I:%M %p").lstrip("0")
                parts.append(time_part)
            
            return " at ".join(parts) if len(parts) == 2 else parts[0] if parts else ""
            
        except Exception as e:
            logger.error(f"Error formatting datetime: {e}")
            return str(dt)
    
    def format_date_long(self, dt: datetime, timezone_str: str = None) -> str:
        """Format date in long format: 'Monday, 1 September'"""
        try:
            local_dt = self.from_utc(dt, timezone_str) if dt.tzinfo else dt
            return local_dt.strftime("%A, %d %B").replace(" 0", " ")
        except Exception as e:
            logger.error(f"Error formatting date: {e}")
            return str(dt.date())
    
    def format_time_local(self, dt: datetime, timezone_str: str = None) -> str:
        """Format time in local format: '10:00 AM'"""
        try:
            local_dt = self.from_utc(dt, timezone_str) if dt.tzinfo else dt
            return local_dt.strftime("%I:%M %p").lstrip("0")
        except Exception as e:
            logger.error(f"Error formatting time: {e}")
            return str(dt.time())
    
    def validate_datetime_range(self, start_dt: datetime, end_dt: datetime) -> bool:
        """Validate that datetime range is reasonable"""
        try:
            # Check that end is after start
            if end_dt <= start_dt:
                return False
            
            # Check that duration is reasonable (15 minutes to 4 hours)
            duration = end_dt - start_dt
            if duration < timedelta(minutes=15) or duration > timedelta(hours=4):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating datetime range: {e}")
            return False
