"""
Meeting Booking Case Handlers
Handles all 11 specific meeting booking cases with exact response templates
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import structlog
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from .fsm import MeetingBookingFSM, MeetingState, MeetingContext
from .providers import ZohoBookingsProvider, BookingSlot, BookingResult
from .timezone_handler import TimezoneHandler
from .nlu import MeetingNLU

from app.core.logging import logger


class MeetingCaseHandlers:
    """
    Handles all 11 meeting booking cases with natural language generation
    Uses OpenAI for human-like responses while following exact workflow patterns
    """
    
    def __init__(self, provider: ZohoBookingsProvider, 
                 timezone_handler: TimezoneHandler, nlu: MeetingNLU):
        self.provider = provider
        self.timezone_handler = timezone_handler
        self.nlu = nlu
        self.fsm = MeetingBookingFSM()
        
        # OpenAI for natural response generation
        try:
            self.llm = ChatOpenAI(
                model="gpt-4",
                temperature=0.3,
                max_tokens=200
            )
            self.openai_available = True
        except Exception as e:
            logger.warning(f"OpenAI not available for case handlers: {e}")
            self.llm = None
            self.openai_available = False
    
    async def handle_clear_date_time(self, context: MeetingContext, 
                                   date_str: str, time_str: str) -> Dict[str, Any]:
        """Case 1: Clear Date & Time"""
        try:
            # Parse and resolve date/time
            target_date = await self._resolve_date(date_str, context.timezone)
            target_time = self.timezone_handler.parse_time_string(time_str)
            
            if not target_date or not target_time:
                return {"message": "I couldn't understand that date or time. Could you try again?"}
            
            # Combine date and time
            target_datetime = datetime.combine(target_date.date(), target_time)
            target_datetime = self.timezone_handler.get_user_timezone(context.timezone).localize(target_datetime)
            
            # Check availability
            slots = await self.provider.get_availability(target_datetime, context.timezone)
            
            if not slots:
                # Case 11: No slots available
                return await self.handle_no_slots_available(context, target_datetime)
            
            # Find matching slot
            matching_slot = self._find_matching_slot(slots, target_datetime)
            
            if matching_slot:
                # Direct booking
                context.selected_slot = matching_slot.to_dict()
                context.target_datetime_utc = self.timezone_handler.to_utc(target_datetime, context.timezone).isoformat()
                
                self.fsm.transition_to(context, MeetingState.BOOKING, "direct booking")
                
                # Book the appointment
                booking_result = await self._book_appointment(context, matching_slot)
                
                if booking_result.success:
                    self.fsm.transition_to(context, MeetingState.BOOKED, "booking successful")
                    context.booking_id = booking_result.booking_id
                    context.meeting_link = booking_result.meeting_link
                    
                    date_str = self.timezone_handler.format_date_long(target_datetime, context.timezone)
                    time_str = self.timezone_handler.format_time_local(target_datetime, context.timezone)
                    
                    return await self._generate_booking_confirmation(
                        context, date_str, time_str, matching_slot.staff_name, booking_result.meeting_link
                    )
                else:
                    self.fsm.transition_to(context, MeetingState.FAILED, "booking failed")
                    return {"message": "I'm sorry, there was an issue booking that time. Let me show you other available options."}
            else:
                # Show available slots for that day
                return await self._show_available_slots(context, target_datetime, slots)
                
        except Exception as e:
            logger.error(f"Error handling clear date/time: {e}")
            return {"message": "I'd be happy to help you schedule a meeting. Could you let me know when you'd prefer to meet?"}
    
    async def handle_date_range(self, context: MeetingContext, 
                              dates: List[str]) -> Dict[str, Any]:
        """Case 2: Range of Dates"""
        try:
            # Parse dates
            parsed_dates = []
            for date_str in dates[:2]:  # Limit to 2 dates
                parsed_date = await self._resolve_date(date_str, context.timezone)
                if parsed_date:
                    parsed_dates.append(parsed_date)
            
            if len(parsed_dates) < 2:
                return {"message": "I couldn't understand those dates. Could you specify which days you're available?"}
            
            # Generate clarification prompt
            date1_str = self.timezone_handler.format_date_long(parsed_dates[0], context.timezone)
            date2_str = self.timezone_handler.format_date_long(parsed_dates[1], context.timezone)
            
            self.fsm.transition_to(context, MeetingState.COLLECT_DAY, "date range clarification")
            
            return await self._generate_natural_response(
                context,
                f"Which day would you prefer — {date1_str} or {date2_str}?",
                "date_range_clarification"
            )
            
        except Exception as e:
            logger.error(f"Error handling date range: {e}")
            return {"message": "Which day would work better for you?"}
    
    async def handle_time_only(self, context: MeetingContext, time_str: str) -> Dict[str, Any]:
        """Case 3: Time Only"""
        try:
            target_time = self.timezone_handler.parse_time_string(time_str)
            if not target_time:
                return {"message": "I couldn't understand that time. Could you try again?"}
            
            context.target_time_local = target_time.strftime("%H:%M")
            self.fsm.transition_to(context, MeetingState.COLLECT_DAY, "time provided, need date")
            
            time_formatted = self.timezone_handler.format_time_local(
                datetime.combine(datetime.today(), target_time), context.timezone
            )
            
            return await self._generate_natural_response(
                context,
                f"Thanks. Could you confirm which day you'd like the meeting at {time_formatted}?",
                "time_only_clarification"
            )
            
        except Exception as e:
            logger.error(f"Error handling time only: {e}")
            return {"message": "What day would work for that time?"}
    
    async def handle_anytime(self, context: MeetingContext, 
                           relative_date: str = None) -> Dict[str, Any]:
        """Case 4: Anytime Tomorrow/Today"""
        try:
            if relative_date:
                target_date = self.timezone_handler.resolve_relative_date(relative_date, 
                    self.timezone_handler.now_in_timezone(context.timezone))
            else:
                # Default to tomorrow
                target_date = self.timezone_handler.resolve_relative_date("tomorrow",
                    self.timezone_handler.now_in_timezone(context.timezone))
            
            if not target_date:
                return {"message": "Which day would work for you?"}
            
            # Get availability for the day
            slots = await self.provider.get_availability(target_date, context.timezone)
            
            if not slots:
                return await self.handle_no_slots_available(context, target_date)
            
            # Show available slots
            return await self._show_available_slots(context, target_date, slots)
            
        except Exception as e:
            logger.error(f"Error handling anytime: {e}")
            return {"message": "Let me check what's available. Which day works for you?"}
    
    async def handle_weekday_availability(self, context: MeetingContext, 
                                        weekday: str) -> Dict[str, Any]:
        """Case 5: Weekday Availability Question"""
        try:
            target_date = self.timezone_handler.resolve_weekday(weekday,
                self.timezone_handler.now_in_timezone(context.timezone))
            
            if not target_date:
                return {"message": "Which day would work for you?"}
            
            # Get availability
            slots = await self.provider.get_availability(target_date, context.timezone)
            
            if not slots:
                return await self.handle_no_slots_available(context, target_date)
            
            # Format response
            date_str = self.timezone_handler.format_date_long(target_date, context.timezone)
            
            return await self._show_available_slots(context, target_date, slots, 
                                                  prefix=f"On {date_str}")
            
        except Exception as e:
            logger.error(f"Error handling weekday availability: {e}")
            return {"message": "Let me check availability for that day."}
    
    async def handle_date_only(self, context: MeetingContext, date_str: str) -> Dict[str, Any]:
        """Case 6: Date Only"""
        try:
            target_date = await self._resolve_date(date_str, context.timezone)
            if not target_date:
                return {"message": "I couldn't understand that date. Could you try again?"}
            
            # Get availability
            slots = await self.provider.get_availability(target_date, context.timezone)
            
            if not slots:
                return await self.handle_no_slots_available(context, target_date)
            
            return await self._show_available_slots(context, target_date, slots)
            
        except Exception as e:
            logger.error(f"Error handling date only: {e}")
            return {"message": "Let me check availability for that day."}
    
    async def handle_next_week(self, context: MeetingContext) -> Dict[str, Any]:
        """Case 9: Next Week"""
        try:
            next_week_start = self.timezone_handler.resolve_relative_date("next week",
                self.timezone_handler.now_in_timezone(context.timezone))
            
            if not next_week_start:
                return {"message": "Which day next week would work for you?"}
            
            # Get weekday options for next week
            weekday_options = []
            for i in range(5):  # Monday to Friday
                day = next_week_start + timedelta(days=i)
                day_name = day.strftime("%A")
                day_date = day.strftime("%d").lstrip("0")
                weekday_options.append(f"{day_name} ({day_date})")
            
            options_text = ", ".join(weekday_options[:-1]) + f", or {weekday_options[-1]}"
            
            self.fsm.transition_to(context, MeetingState.COLLECT_DAY, "next week clarification")
            
            return await self._generate_natural_response(
                context,
                f"Which day next week works best for you — {options_text}?",
                "next_week_clarification"
            )
            
        except Exception as e:
            logger.error(f"Error handling next week: {e}")
            return {"message": "Which day next week would work for you?"}
    
    async def handle_user_defer(self, context: MeetingContext) -> Dict[str, Any]:
        """Case 10: User Defers"""
        try:
            # Park the context with longer expiry
            context.expires_at = (datetime.utcnow() + timedelta(hours=24)).isoformat()
            
            return await self._generate_natural_response(
                context,
                "No problem. I'll keep these options handy. When you're ready, just tell me which slot to confirm.",
                "user_defer"
            )
            
        except Exception as e:
            logger.error(f"Error handling user defer: {e}")
            return {"message": "No problem. Let me know when you're ready to confirm a time."}
    
    async def handle_no_slots_available(self, context: MeetingContext, 
                                      requested_date: datetime) -> Dict[str, Any]:
        """Case 11: Holiday / No Slots"""
        try:
            # Check if it's a weekend or holiday
            is_weekend = not self.timezone_handler.is_business_day(requested_date)
            
            date_str = self.timezone_handler.format_date_long(requested_date, context.timezone)
            
            # Get next available business day
            next_business_day = self.timezone_handler.get_next_business_day(requested_date)
            fallback_slots = await self.provider.get_availability(next_business_day, context.timezone)
            
            if fallback_slots:
                fallback_date_str = self.timezone_handler.format_date_long(next_business_day, context.timezone)
                
                if is_weekend:
                    prefix = f"{date_str} falls on a {requested_date.strftime('%A')}, and I don't have any slots that day."
                else:
                    prefix = f"I don't have any availability on {date_str}."
                
                return await self._show_available_slots(
                    context, next_business_day, fallback_slots,
                    prefix=f"{prefix} I do have availability on {fallback_date_str}"
                )
            else:
                return {"message": f"I don't have availability on {date_str}. Let me connect you with our team to find a suitable time."}
                
        except Exception as e:
            logger.error(f"Error handling no slots: {e}")
            return {"message": "Let me check other available times for you."}
    
    async def handle_slot_confirmation(self, context: MeetingContext, 
                                     message: str) -> Dict[str, Any]:
        """Handle slot confirmation from user"""
        try:
            if not context.candidate_slots_local:
                return {"message": "Let me show you the available times first."}
            
            # Parse which slot the user selected
            selected_slot_data = await self._parse_slot_selection(message, context.candidate_slots_local)
            
            if not selected_slot_data:
                return {"message": "I couldn't understand which time you'd prefer. Could you specify?"}
            
            # Convert to BookingSlot object
            selected_slot = BookingSlot(
                staff_id=selected_slot_data["staff_id"],
                staff_name=selected_slot_data["staff_name"],
                start_time=datetime.fromisoformat(selected_slot_data["start_time"]),
                end_time=datetime.fromisoformat(selected_slot_data["end_time"]),
                service_id=selected_slot_data["service_id"],
                service_name=selected_slot_data["service_name"],
                duration_minutes=selected_slot_data["duration_minutes"]
            )
            
            # Book the appointment
            self.fsm.transition_to(context, MeetingState.BOOKING, "slot confirmed")
            
            booking_result = await self._book_appointment(context, selected_slot)
            
            if booking_result.success:
                self.fsm.transition_to(context, MeetingState.BOOKED, "booking successful")
                context.booking_id = booking_result.booking_id
                context.meeting_link = booking_result.meeting_link
                
                date_str = self.timezone_handler.format_date_long(selected_slot.start_time, context.timezone)
                time_str = self.timezone_handler.format_time_local(selected_slot.start_time, context.timezone)
                
                return await self._generate_booking_confirmation(
                    context, date_str, time_str, selected_slot.staff_name, booking_result.meeting_link
                )
            else:
                self.fsm.transition_to(context, MeetingState.FAILED, "booking failed")
                return {"message": "I'm sorry, there was an issue booking that time. Let me show you other available options."}
                
        except Exception as e:
            logger.error(f"Error handling slot confirmation: {e}")
            return {"message": "Let me help you confirm a time. Which slot would you prefer?"}
    
    async def handle_cancel_confirmation(self, context: MeetingContext) -> Dict[str, Any]:
        """Handle meeting cancellation confirmation"""
        try:
            # Here you would integrate with the provider's cancel API
            # For now, we'll simulate successful cancellation
            
            context.booking_id = None
            context.meeting_link = None
            self.fsm.transition_to(context, MeetingState.IDLE, "meeting cancelled")
            
            return await self._generate_natural_response(
                context,
                "Your meeting has been canceled. Is there anything else I can help you with?",
                "cancellation_confirmation"
            )
            
        except Exception as e:
            logger.error(f"Error handling cancel confirmation: {e}")
            return {"message": "I've canceled your meeting. Is there anything else I can help you with?"}
    
    async def handle_general_availability(self, context: MeetingContext) -> Dict[str, Any]:
        """Handle general availability inquiry"""
        try:
            # Get next few days of availability
            current_date = self.timezone_handler.now_in_timezone(context.timezone)
            
            for day_offset in range(7):
                check_date = current_date + timedelta(days=day_offset)
                if self.timezone_handler.is_business_day(check_date):
                    slots = await self.provider.get_availability(check_date, context.timezone)
                    if slots:
                        return await self._show_available_slots(context, check_date, slots)
            
            return {"message": "Let me connect you with our team to find a suitable time."}
            
        except Exception as e:
            logger.error(f"Error handling general availability: {e}")
            return {"message": "Let me check what times are available for you."}
    
    # Helper methods

    async def _resolve_date(self, date_str: str, timezone_str: str) -> Optional[datetime]:
        """Resolve date string to datetime object"""
        try:
            # Try relative dates first
            if date_str.lower() in ["today", "tomorrow", "next week", "this week"]:
                return self.timezone_handler.resolve_relative_date(date_str,
                    self.timezone_handler.now_in_timezone(timezone_str))

            # Try weekdays
            weekdays = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            if date_str.lower() in weekdays:
                return self.timezone_handler.resolve_weekday(date_str,
                    self.timezone_handler.now_in_timezone(timezone_str))

            # Try parsing as date
            from dateutil import parser
            parsed_date = parser.parse(date_str, fuzzy=True)

            # If no year specified, assume current year
            if parsed_date.year == 1900:
                current_year = datetime.now().year
                parsed_date = parsed_date.replace(year=current_year)

            # Ensure it's in the future
            now = self.timezone_handler.now_in_timezone(timezone_str)
            if parsed_date.date() < now.date():
                # Assume next year if date has passed
                parsed_date = parsed_date.replace(year=parsed_date.year + 1)

            return parsed_date

        except Exception as e:
            logger.error(f"Error resolving date '{date_str}': {e}")
            return None

    def _find_matching_slot(self, slots: List[BookingSlot],
                           target_datetime: datetime) -> Optional[BookingSlot]:
        """Find slot that matches target datetime"""
        for slot in slots:
            # Allow 30-minute window for matching
            time_diff = abs((slot.start_time - target_datetime).total_seconds())
            if time_diff <= 1800:  # 30 minutes
                return slot
        return None

    async def _show_available_slots(self, context: MeetingContext,
                                  target_date: datetime, slots: List[BookingSlot],
                                  prefix: str = None) -> Dict[str, Any]:
        """Show available slots to user"""
        try:
            if not slots:
                return await self.handle_no_slots_available(context, target_date)

            # Store slots in context
            context.candidate_slots_local = [slot.to_dict() for slot in slots[:3]]  # Limit to 3
            context.target_date_local = target_date.strftime("%Y-%m-%d")

            self.fsm.transition_to(context, MeetingState.SHOW_SLOTS, "showing available slots")

            # Format slots for display
            slot_descriptions = []
            for slot in slots[:3]:
                time_str = self.timezone_handler.format_time_local(slot.start_time, context.timezone)
                slot_descriptions.append(time_str)

            slots_text = ", ".join(slot_descriptions[:-1]) + f", and {slot_descriptions[-1]}" if len(slot_descriptions) > 1 else slot_descriptions[0]

            date_str = self.timezone_handler.format_date_long(target_date, context.timezone)

            if prefix:
                message = f"{prefix}, I have these slots: {slots_text}. Which one works for you?"
            else:
                message = f"On {date_str}, I have these slots: {slots_text}. Which one works for you?"

            self.fsm.transition_to(context, MeetingState.AWAIT_CONFIRM, "awaiting slot confirmation")

            return await self._generate_natural_response(context, message, "show_slots")

        except Exception as e:
            logger.error(f"Error showing available slots: {e}")
            return {"message": "Let me check what times are available for you."}

    async def _parse_slot_selection(self, message: str,
                                  available_slots: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Parse user's slot selection"""
        try:
            message_lower = message.lower().strip()

            # Simple parsing logic
            if any(word in message_lower for word in ["first", "1", "one", "earlier"]):
                return available_slots[0] if available_slots else None
            elif any(word in message_lower for word in ["second", "2", "two"]):
                return available_slots[1] if len(available_slots) > 1 else None
            elif any(word in message_lower for word in ["third", "3", "three", "last"]):
                return available_slots[2] if len(available_slots) > 2 else None

            # Try to match time mentions
            for slot in available_slots:
                slot_time = datetime.fromisoformat(slot["start_time"])
                time_str = self.timezone_handler.format_time_local(slot_time)

                # Check if time is mentioned in message
                if any(part in message_lower for part in time_str.lower().split()):
                    return slot

            # Default to first slot if unclear
            return available_slots[0] if available_slots else None

        except Exception as e:
            logger.error(f"Error parsing slot selection: {e}")
            return available_slots[0] if available_slots else None

    async def _book_appointment(self, context: MeetingContext,
                              slot: BookingSlot) -> BookingResult:
        """Book appointment with provider"""
        try:
            # Get real lead details from database
            lead_email = await self._get_lead_email_by_id(context.lead_id)
            lead_name = await self._get_lead_name_by_id(context.lead_id)
            
            # Generate customer details using real data when available
            customer_name = lead_name if lead_name else "Lead Contact"
            customer_email = lead_email if lead_email else f"lead.{context.phone_number.replace('+', '').replace(' ', '')}@growthhive.com.au"
            customer_phone = context.phone_number
            notes = f"Meeting booked via GrowthHive Meeting Agent. Lead ID: {context.lead_id}"
            
            print(f"   📧 Using email: {customer_email}")
            print(f"   👤 Using name: {customer_name}")
            if lead_email:
                print(f"   ✅ Real email from database")
            else:
                print(f"   ⚠️ Fallback email generated")

            return await self.provider.book_appointment(
                slot=slot,
                customer_name=customer_name,
                customer_email=customer_email,
                customer_phone=customer_phone,
                notes=notes,
                timezone_str=context.timezone
            )

        except Exception as e:
            logger.error(f"Error booking appointment: {e}")
            return BookingResult(success=False, error_message=str(e))

    async def _generate_natural_response(self, context: MeetingContext,
                                       base_message: str, response_type: str) -> Dict[str, Any]:
        """Generate natural response using OpenAI or fallback"""
        if not self.openai_available:
            return {"message": base_message}

        try:
            system_prompt = """You are Andy, a friendly Australian meeting booking assistant.

            Rewrite the given message to sound more natural and conversational while keeping the same meaning.

            Style guidelines:
            - Conversational and friendly Australian tone
            - Use natural contractions (I'll, you'd, we'll)
            - Keep it concise and clear
            - No emojis
            - Maintain professional but casual tone

            The message should sound like a human assistant, not a bot."""

            user_prompt = f"""
            Response type: {response_type}
            Base message: "{base_message}"

            Rewrite this to sound more natural and conversational:
            """

            response = await self.llm.ainvoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ])

            return {"message": response.content.strip()}

        except Exception as e:
            logger.error(f"Error generating natural response: {e}")
            return {"message": base_message}

    async def _generate_booking_confirmation(self, context: MeetingContext,
                                           date_str: str, time_str: str,
                                           staff_name: str, meeting_link: str) -> Dict[str, Any]:
        """Generate booking confirmation message"""
        try:
            base_message = f"Perfect. Your meeting is booked for {date_str} at {time_str}"

            if staff_name and staff_name != "Team Member":
                base_message += f" with {staff_name}"

            if meeting_link:
                base_message += f". Here is your meeting link: {meeting_link}"
            else:
                base_message += ". You'll receive the meeting details shortly."

            return await self._generate_natural_response(context, base_message, "booking_confirmation")

        except Exception as e:
            logger.error(f"Error generating booking confirmation: {e}")
            return {"message": f"Your meeting is booked for {date_str} at {time_str}. Looking forward to speaking with you!"}

    async def _get_lead_email_by_id(self, lead_id: str) -> Optional[str]:
        """Get lead's email address by lead ID"""
        try:
            from app.models.lead import Lead
            from app.core.database.connection import get_db
            from sqlalchemy import select

            async for db in get_db():
                query = select(Lead.email).where(
                    Lead.id == lead_id,
                    Lead.is_active == True,
                    Lead.is_deleted == False
                )
                
                result = await db.execute(query)
                email = result.scalar_one_or_none()
                
                if email:
                    print(f"   ✅ Found lead email: {email}")
                    return email
                else:
                    print(f"   ❌ No email found for lead ID: {lead_id}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Error getting lead email: {e}")
            return None

    async def _get_lead_name_by_id(self, lead_id: str) -> Optional[str]:
        """Get lead's full name by lead ID"""
        try:
            from app.models.lead import Lead
            from app.core.database.connection import get_db
            from sqlalchemy import select

            async for db in get_db():
                query = select(Lead.first_name, Lead.last_name).where(
                    Lead.id == lead_id,
                    Lead.is_active == True,
                    Lead.is_deleted == False
                )
                
                result = await db.execute(query)
                name_data = result.first()
                
                if name_data:
                    first_name, last_name = name_data
                    full_name = f"{first_name or ''} {last_name or ''}".strip()
                    if full_name:
                        print(f"   ✅ Found lead name: {full_name}")
                        return full_name
                    else:
                        print(f"   ❌ Lead found but no name available")
                        return None
                else:
                    print(f"   ❌ No lead found for ID: {lead_id}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Error getting lead name: {e}")
            return None
