"""
Meeting Booking Providers
Zoho Bookings integration with robust error handling and retry logic
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import structlog
import httpx
from dataclasses import dataclass

from app.core.config.settings import settings
from .timezone_handler import TimezoneHandler

from app.core.logging import logger


@dataclass
class BookingSlot:
    """Represents an available booking slot"""
    staff_id: str
    staff_name: str
    start_time: datetime
    end_time: datetime
    service_id: str
    service_name: str
    duration_minutes: int
    booking_url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "staff_id": self.staff_id,
            "staff_name": self.staff_name,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "service_id": self.service_id,
            "service_name": self.service_name,
            "duration_minutes": self.duration_minutes,
            "booking_url": self.booking_url
        }


@dataclass
class BookingResult:
    """Result of a booking operation"""
    success: bool
    booking_id: Optional[str] = None
    zoho_booking_id: Optional[str] = None
    meeting_link: Optional[str] = None
    booking_url: Optional[str] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "booking_id": self.booking_id,
            "zoho_booking_id": self.zoho_booking_id,
            "meeting_link": self.meeting_link,
            "booking_url": self.booking_url,
            "error_message": self.error_message
        }


class ZohoBookingsProvider:
    """
    Zoho Bookings provider with robust error handling and retry logic
    Handles authentication, availability fetching, and booking operations
    """
    
    def __init__(self):
        self.base_url = settings.ZOHO_BOOKINGS_BASE_URL
        self.auth_url = settings.ZOHO_BOOKINGS_AUTH_URL
        self.client_id = settings.ZOHO_BOOKINGS_CLIENT_ID
        self.client_secret = settings.ZOHO_BOOKINGS_CLIENT_SECRET
        self.refresh_token = settings.ZOHO_BOOKINGS_REFRESH_TOKEN
        self.scope = settings.ZOHO_BOOKINGS_SCOPE
        
        # Service configuration
        self.workspace_id = getattr(settings, 'ZOHO_BOOKINGS_WORKSPACE_ID', None)
        self.lead_meeting_service_id = getattr(settings, 'ZOHO_BOOKINGS_LEAD_MEETING_SERVICE_ID', None)
        
        # Cache for access token
        self._access_token = None
        self._token_expires_at = None
        
        self.timezone_handler = TimezoneHandler()
        
        # Retry configuration
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds
        
        logger.info("ZohoBookingsProvider initialized")
    
    async def _get_access_token(self) -> Optional[str]:
        """Get valid access token, refresh if needed"""
        try:
            # Check if current token is still valid
            if (self._access_token and self._token_expires_at and 
                datetime.now() < self._token_expires_at - timedelta(minutes=5)):
                return self._access_token
            
            # Refresh token
            logger.info("Refreshing Zoho access token")
            
            token_data = {
                'refresh_token': self.refresh_token,
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'refresh_token'
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(self.auth_url, data=token_data)
                
                if response.status_code == 200:
                    token_response = response.json()
                    self._access_token = token_response.get('access_token')
                    expires_in = token_response.get('expires_in', 3600)
                    self._token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    
                    logger.info("Access token refreshed successfully")
                    return self._access_token
                else:
                    logger.error(f"Token refresh failed: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error refreshing access token: {e}")
            return None
    
    async def _make_api_request(self, method: str, endpoint: str, 
                              data: Dict[str, Any] = None, 
                              params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Make API request with retry logic"""
        access_token = await self._get_access_token()
        if not access_token:
            logger.error("No valid access token available")
            return None
        
        headers = {
            'Authorization': f'Zoho-oauthtoken {access_token}',
            'Content-Type': 'application/json'
        }
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    if method.upper() == 'GET':
                        response = await client.get(url, headers=headers, params=params)
                    elif method.upper() == 'POST':
                        response = await client.post(url, headers=headers, json=data)
                    elif method.upper() == 'PUT':
                        response = await client.put(url, headers=headers, json=data)
                    elif method.upper() == 'DELETE':
                        response = await client.delete(url, headers=headers)
                    else:
                        logger.error(f"Unsupported HTTP method: {method}")
                        return None
                    
                    if response.status_code == 200:
                        return response.json()
                    elif response.status_code == 401:
                        # Token expired, refresh and retry
                        logger.warning("Access token expired, refreshing...")
                        self._access_token = None
                        access_token = await self._get_access_token()
                        if access_token:
                            headers['Authorization'] = f'Zoho-oauthtoken {access_token}'
                            continue
                        else:
                            logger.error("Failed to refresh token")
                            return None
                    elif response.status_code in [429, 500, 502, 503, 504]:
                        # Rate limit or server error, retry with backoff
                        if attempt < self.max_retries - 1:
                            wait_time = self.retry_delay * (2 ** attempt)
                            logger.warning(f"API error {response.status_code}, retrying in {wait_time}s...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            logger.error(f"API request failed after {self.max_retries} attempts: {response.status_code}")
                            return None
                    else:
                        logger.error(f"API request failed: {response.status_code} - {response.text}")
                        return None
                        
            except httpx.TimeoutException:
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Request timeout, retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error("Request timed out after all retries")
                    return None
            except Exception as e:
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (2 ** attempt)
                    logger.warning(f"Request error: {e}, retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Request failed after all retries: {e}")
                    return None
        
        return None
    
    async def get_availability(self, date_local: datetime, 
                             timezone_str: str = None) -> List[BookingSlot]:
        """Get available slots for a specific date"""
        try:
            if not self.workspace_id or not self.lead_meeting_service_id:
                logger.error("Missing Zoho workspace or service configuration")
                return []
            
            # Convert to local timezone for API
            tz_handler = TimezoneHandler(timezone_str)
            local_date = tz_handler.from_utc(date_local, timezone_str) if date_local.tzinfo else date_local
            
            # Format date for API (YYYY-MM-DD)
            date_str = local_date.strftime('%Y-%m-%d')
            
            params = {
                'workspace_id': self.workspace_id,
                'service_id': self.lead_meeting_service_id,
                'selected_date': date_str,
                'timezone': timezone_str or settings.MEETING_AGENT_DEFAULT_TIMEZONE
            }
            
            logger.info(f"Fetching availability for {date_str} in {params['timezone']}")
            
            response = await self._make_api_request('GET', 'availableslots', params=params)
            
            if not response:
                logger.warning("No response from availability API")
                return []
            
            slots = []
            
            # Parse response based on Zoho API structure
            if 'response' in response and 'availableslots' in response['response']:
                for slot_data in response['response']['availableslots']:
                    try:
                        # Parse slot data
                        start_time_str = slot_data.get('from_time')
                        duration = slot_data.get('duration', 30)
                        staff_info = slot_data.get('staff', {})
                        
                        if start_time_str:
                            # Parse start time
                            start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                            end_time = start_time + timedelta(minutes=duration)
                            
                            slot = BookingSlot(
                                staff_id=staff_info.get('staff_id', ''),
                                staff_name=staff_info.get('name', 'Team Member'),
                                start_time=start_time,
                                end_time=end_time,
                                service_id=self.lead_meeting_service_id,
                                service_name='Lead Meeting',
                                duration_minutes=duration,
                                booking_url=slot_data.get('booking_url')
                            )
                            
                            slots.append(slot)
                            
                    except Exception as e:
                        logger.warning(f"Error parsing slot data: {e}")
                        continue
            
            logger.info(f"Found {len(slots)} available slots for {date_str}")
            return slots
            
        except Exception as e:
            logger.error(f"Error fetching availability: {e}")
            return []
    
    async def book_appointment(self, slot: BookingSlot, customer_name: str,
                             customer_email: str, customer_phone: str,
                             notes: str = None, timezone_str: str = None) -> BookingResult:
        """Book an appointment for the specified slot"""
        try:
            if not self.workspace_id:
                return BookingResult(
                    success=False,
                    error_message="Missing Zoho workspace configuration"
                )
            
            # Prepare booking data
            booking_data = {
                'workspace_id': self.workspace_id,
                'service_id': slot.service_id,
                'staff_id': slot.staff_id,
                'from_time': slot.start_time.strftime('%d-%b-%Y %H:%M:%S'),
                'timezone': timezone_str or settings.MEETING_AGENT_DEFAULT_TIMEZONE,
                'customer_details': json.dumps({
                    'name': customer_name.strip(),
                    'email': customer_email.strip() if customer_email else '',
                    'phone_number': customer_phone.strip() if customer_phone else ''
                }),
                'notes': notes or 'Booking via GrowthHive Meeting Agent'
            }
            
            logger.info(f"Booking appointment: {customer_name} at {slot.start_time}")
            
            response = await self._make_api_request('POST', 'bookings', data=booking_data)
            
            if response and response.get('response', {}).get('returnvalue', {}).get('status') == 'success':
                booking_info = response['response']['returnvalue']
                
                return BookingResult(
                    success=True,
                    booking_id=booking_info.get('booking_id'),
                    zoho_booking_id=booking_info.get('booking_id'),
                    meeting_link=booking_info.get('meeting_link'),
                    booking_url=booking_info.get('booking_url')
                )
            else:
                error_msg = "Booking failed"
                if response and 'response' in response:
                    error_msg = response['response'].get('message', error_msg)
                
                return BookingResult(
                    success=False,
                    error_message=error_msg
                )
                
        except Exception as e:
            logger.error(f"Error booking appointment: {e}")
            return BookingResult(
                success=False,
                error_message=f"Booking error: {str(e)}"
            )
