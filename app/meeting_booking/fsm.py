"""
Meeting Booking Finite State Machine
Robust state management for meeting booking conversations with loop prevention
"""

import json
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict, field
import structlog

from app.core.config.settings import settings
from app.core.redis_client import get_redis_client

from app.core.logging import logger


class MeetingState(Enum):
    """Meeting booking conversation states"""
    IDLE = "idle"
    COLLECT_DAY = "collect_day"
    COLLECT_TIME = "collect_time"
    SHOW_SLOTS = "show_slots"
    AWAIT_CONFIRM = "await_confirm"
    BOOKING = "booking"
    BOOKED = "booked"
    RESCHEDULING = "rescheduling"
    CANCELING = "canceling"
    FAILED = "failed"


class MeetingIntent(Enum):
    """Meeting booking intents"""
    SCHEDULE = "schedule"
    RESCHEDULE = "reschedule"
    CANCEL = "cancel"
    AVAILABILITY = "availability"
    CONFIRM = "confirm"
    DEFER = "defer"
    CLARIFY = "clarify"
    UNKNOWN = "unknown"


@dataclass
class MeetingContext:
    """Context for meeting booking conversation with comprehensive tracking"""
    # Core identifiers
    lead_id: str
    phone_number: str
    session_id: str
    
    # Current state
    current_state: MeetingState = MeetingState.IDLE
    
    # Date/time preferences (local timezone)
    target_date_local: Optional[str] = None
    target_time_local: Optional[str] = None
    target_datetime_utc: Optional[str] = None
    
    # Available slots and selection
    candidate_slots_local: List[Dict[str, Any]] = field(default_factory=list)
    selected_slot: Optional[Dict[str, Any]] = None
    
    # Booking details
    provider_payload: Optional[Dict[str, Any]] = None
    meeting_link: Optional[str] = None
    booking_id: Optional[str] = None
    zoho_booking_id: Optional[str] = None
    
    # Conversation tracking
    recent_turns_window: List[Dict[str, Any]] = field(default_factory=list)
    clarification_count: int = 0
    max_clarifications: int = 2
    
    # Timezone and locale
    timezone: str = "Asia/Kolkata"
    locale: str = "en_IN"
    
    # Metadata
    created_at: str = field(default_factory=lambda: datetime.utcnow().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.utcnow().isoformat())
    expires_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Redis storage"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MeetingContext':
        """Create from dictionary from Redis"""
        # Handle enum conversion
        if 'current_state' in data and isinstance(data['current_state'], str):
            data['current_state'] = MeetingState(data['current_state'])
        
        # Ensure lists are properly initialized
        if 'candidate_slots_local' not in data:
            data['candidate_slots_local'] = []
        if 'recent_turns_window' not in data:
            data['recent_turns_window'] = []
            
        return cls(**data)


class MeetingBookingFSM:
    """
    Finite State Machine for meeting booking conversations
    Handles state transitions, context persistence, and loop prevention
    """
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client or get_redis_client()
        self.context_ttl = settings.MEETING_AGENT_CONVERSATION_TIMEOUT
        
        # Define valid state transitions
        self.transitions = {
            MeetingState.IDLE: [
                MeetingState.COLLECT_DAY,
                MeetingState.COLLECT_TIME,
                MeetingState.SHOW_SLOTS,
                MeetingState.FAILED
            ],
            MeetingState.COLLECT_DAY: [
                MeetingState.COLLECT_TIME,
                MeetingState.SHOW_SLOTS,
                MeetingState.COLLECT_DAY,  # Re-clarify
                MeetingState.FAILED
            ],
            MeetingState.COLLECT_TIME: [
                MeetingState.SHOW_SLOTS,
                MeetingState.COLLECT_TIME,  # Re-clarify
                MeetingState.COLLECT_DAY,   # Back to day
                MeetingState.FAILED
            ],
            MeetingState.SHOW_SLOTS: [
                MeetingState.AWAIT_CONFIRM,
                MeetingState.COLLECT_DAY,   # Different day
                MeetingState.COLLECT_TIME,  # Different time
                MeetingState.FAILED
            ],
            MeetingState.AWAIT_CONFIRM: [
                MeetingState.BOOKING,
                MeetingState.SHOW_SLOTS,    # Show different slots
                MeetingState.COLLECT_DAY,   # Different day
                MeetingState.FAILED
            ],
            MeetingState.BOOKING: [
                MeetingState.BOOKED,
                MeetingState.FAILED
            ],
            MeetingState.BOOKED: [
                MeetingState.RESCHEDULING,
                MeetingState.CANCELING
            ],
            MeetingState.RESCHEDULING: [
                MeetingState.COLLECT_DAY,
                MeetingState.SHOW_SLOTS,
                MeetingState.BOOKED,
                MeetingState.FAILED
            ],
            MeetingState.CANCELING: [
                MeetingState.IDLE,
                MeetingState.FAILED
            ],
            MeetingState.FAILED: [
                MeetingState.IDLE
            ]
        }
    
    def get_context_key(self, phone_number: str) -> str:
        """Generate Redis key for meeting context"""
        return f"meeting_context:{phone_number}"
    
    async def get_context(self, phone_number: str) -> Optional[MeetingContext]:
        """Retrieve meeting context from Redis"""
        try:
            key = self.get_context_key(phone_number)
            data = self.redis_client.get(key)
            
            if not data:
                return None
            
            context_data = json.loads(data)
            return MeetingContext.from_dict(context_data)
            
        except Exception as e:
            logger.error(f"Error retrieving meeting context: {e}")
            return None
    
    async def save_context(self, context: MeetingContext) -> bool:
        """Save meeting context to Redis with TTL"""
        try:
            key = self.get_context_key(context.phone_number)
            context.updated_at = datetime.utcnow().isoformat()
            
            # Set expiry if not set
            if not context.expires_at:
                expires_at = datetime.utcnow() + timedelta(seconds=self.context_ttl)
                context.expires_at = expires_at.isoformat()
            
            data = json.dumps(context.to_dict(), default=str)
            self.redis_client.setex(key, self.context_ttl, data)
            
            logger.info(f"Meeting context saved for {context.phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving meeting context: {e}")
            return False
    
    def can_transition(self, from_state: MeetingState, to_state: MeetingState) -> bool:
        """Check if state transition is valid"""
        return to_state in self.transitions.get(from_state, [])
    
    def transition_to(self, context: MeetingContext, new_state: MeetingState, 
                     reason: str = None) -> bool:
        """Transition to new state if valid"""
        if not self.can_transition(context.current_state, new_state):
            logger.warning(f"Invalid transition from {context.current_state.value} to {new_state.value}")
            return False
        
        old_state = context.current_state
        context.current_state = new_state
        context.updated_at = datetime.utcnow().isoformat()
        
        logger.info(f"State transition: {old_state.value} -> {new_state.value}" + 
                   (f" ({reason})" if reason else ""))
        
        return True
    
    def add_turn(self, context: MeetingContext, user_message: str, 
                agent_response: str = None, intent: str = None):
        """Add conversation turn to context window"""
        turn = {
            "timestamp": datetime.utcnow().isoformat(),
            "user_message": user_message,
            "agent_response": agent_response,
            "intent": intent,
            "state": context.current_state.value
        }
        
        context.recent_turns_window.append(turn)
        
        # Keep only recent turns (last 10)
        if len(context.recent_turns_window) > 10:
            context.recent_turns_window = context.recent_turns_window[-10:]
    
    def should_prevent_loop(self, context: MeetingContext, user_message: str) -> bool:
        """Check if we should prevent conversation loops"""
        if len(context.recent_turns_window) < 2:
            return False
        
        # Check for repeated user messages
        recent_messages = [turn["user_message"].lower().strip() 
                          for turn in context.recent_turns_window[-3:]]
        
        if recent_messages.count(user_message.lower().strip()) >= 2:
            logger.warning(f"Loop detected: repeated message '{user_message}'")
            return True
        
        # Check clarification count
        if context.clarification_count >= context.max_clarifications:
            logger.warning(f"Max clarifications reached: {context.clarification_count}")
            return True
        
        return False
    
    def reset_clarifications(self, context: MeetingContext):
        """Reset clarification counter"""
        context.clarification_count = 0
    
    def increment_clarifications(self, context: MeetingContext):
        """Increment clarification counter"""
        context.clarification_count += 1
