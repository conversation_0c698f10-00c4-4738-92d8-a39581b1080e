"""
Meeting Booking Orchestrator
Main orchestrator that handles the complete meeting booking flow
"""

import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import structlog

from app.core.config.settings import settings
from app.core.database.connection import get_db
from app.models.lead import Lead
from app.models.booking import Booking

from .fsm import MeetingBookingFSM, MeetingState, MeetingContext, MeetingIntent
from .nlu import MeetingNLU
from .providers import ZohoBookingsProvider, BookingSlot, BookingResult
from .timezone_handler import TimezoneHandler
from .case_handlers import MeetingCaseHandlers

from app.core.logging import logger


class MeetingOrchestrator:
    """
    Main orchestrator for meeting booking conversations
    Integrates FSM, NLU, providers, and database operations
    """
    
    def __init__(self):
        self.enabled = settings.MEETING_AGENT_ENABLED
        self.provider_name = settings.MEETING_AGENT_PROVIDER
        
        if not self.enabled:
            logger.info("Meeting booking orchestrator disabled")
            return
        
        # Initialize components
        self.fsm = MeetingBookingFSM()
        self.nlu = MeetingNLU()
        self.timezone_handler = TimezoneHandler()
        
        # Initialize provider
        if self.provider_name == "zoho":
            self.provider = ZohoBookingsProvider()
        else:
            logger.error(f"Unsupported provider: {self.provider_name}")
            self.provider = None
        
        # Initialize case handlers
        self.case_handlers = MeetingCaseHandlers(
            self.provider, 
            self.timezone_handler,
            self.nlu
        )
        
        logger.info(f"Meeting orchestrator initialized (provider: {self.provider_name})")
    
    async def handle_message(self, phone_number: str, message: str, 
                           lead_id: str = None) -> Dict[str, Any]:
        """
        Handle incoming message for meeting booking
        Returns response and updated context
        """
        if not self.enabled:
            return {
                "handled": False,
                "reason": "Meeting agent disabled"
            }
        
        try:
            # Get or create context
            context = await self.fsm.get_context(phone_number)
            if not context:
                context = MeetingContext(
                    lead_id=lead_id or str(uuid.uuid4()),
                    phone_number=phone_number,
                    session_id=str(uuid.uuid4()),
                    timezone=settings.MEETING_AGENT_DEFAULT_TIMEZONE
                )
            
            # Update lead_id if provided
            if lead_id:
                context.lead_id = lead_id
            
            # Check for loop prevention
            if self.fsm.should_prevent_loop(context, message):
                return await self._handle_loop_prevention(context, message)
            
            # Detect intent
            intent = await self.nlu.detect_intent(message, context)
            
            # Add turn to context
            self.fsm.add_turn(context, message, intent=intent.value)
            
            # Route to appropriate handler
            if intent == MeetingIntent.SCHEDULE:
                response = await self._handle_schedule_intent(context, message)
            elif intent == MeetingIntent.RESCHEDULE:
                response = await self._handle_reschedule_intent(context, message)
            elif intent == MeetingIntent.CANCEL:
                response = await self._handle_cancel_intent(context, message)
            elif intent == MeetingIntent.AVAILABILITY:
                response = await self._handle_availability_intent(context, message)
            elif intent == MeetingIntent.CONFIRM:
                response = await self._handle_confirm_intent(context, message)
            elif intent == MeetingIntent.DEFER:
                response = await self._handle_defer_intent(context, message)
            else:
                response = await self._handle_unknown_intent(context, message)
            
            # Update turn with response
            if context.recent_turns_window:
                context.recent_turns_window[-1]["agent_response"] = response["message"]
            
            # Save context
            await self.fsm.save_context(context)
            
            return {
                "handled": True,
                "message": response["message"],
                "context": context.to_dict(),
                "state": context.current_state.value,
                "booking_id": context.booking_id,
                "meeting_link": context.meeting_link
            }
            
        except Exception as e:
            logger.error(f"Error handling meeting message: {e}")
            return {
                "handled": False,
                "reason": f"Error: {str(e)}"
            }
    
    async def _handle_schedule_intent(self, context: MeetingContext, 
                                    message: str) -> Dict[str, Any]:
        """Handle scheduling intent"""
        try:
            # Extract datetime information
            datetime_info = await self.nlu.extract_datetime_slots(message, context)
            
            # Determine which case handler to use
            if datetime_info.get("dates") and datetime_info.get("times"):
                # Case 1: Clear date & time
                return await self.case_handlers.handle_clear_date_time(
                    context, datetime_info["dates"][0], datetime_info["times"][0]
                )
            elif len(datetime_info.get("dates", [])) > 1:
                # Case 2: Range of dates
                return await self.case_handlers.handle_date_range(
                    context, datetime_info["dates"]
                )
            elif datetime_info.get("times") and not datetime_info.get("dates"):
                # Case 3: Time only
                return await self.case_handlers.handle_time_only(
                    context, datetime_info["times"][0]
                )
            elif datetime_info.get("anytime_indicators"):
                # Case 4: Anytime
                relative_date = datetime_info.get("relative_dates", [None])[0]
                return await self.case_handlers.handle_anytime(context, relative_date)
            elif datetime_info.get("weekdays") and not datetime_info.get("dates"):
                # Case 5: Weekday availability question
                return await self.case_handlers.handle_weekday_availability(
                    context, datetime_info["weekdays"][0]
                )
            elif datetime_info.get("dates") and not datetime_info.get("times"):
                # Case 6: Date only
                return await self.case_handlers.handle_date_only(
                    context, datetime_info["dates"][0]
                )
            elif "next week" in datetime_info.get("relative_dates", []):
                # Case 9: Next week
                return await self.case_handlers.handle_next_week(context)
            else:
                # Need more information
                return await self._handle_incomplete_info(context, datetime_info)
                
        except Exception as e:
            logger.error(f"Error handling schedule intent: {e}")
            return {"message": "I'd be happy to help you schedule a meeting. Could you let me know when you'd prefer to meet?"}
    
    async def _handle_reschedule_intent(self, context: MeetingContext, 
                                      message: str) -> Dict[str, Any]:
        """Handle reschedule intent"""
        if not context.booking_id:
            return {"message": "I don't see an existing booking to reschedule. Would you like to schedule a new meeting?"}
        
        # Transition to rescheduling state
        self.fsm.transition_to(context, MeetingState.RESCHEDULING, "reschedule request")
        
        return {"message": "I can help reschedule your meeting. Which day would you prefer? I'll share available slots for that day."}
    
    async def _handle_cancel_intent(self, context: MeetingContext, 
                                  message: str) -> Dict[str, Any]:
        """Handle cancel intent"""
        if not context.booking_id:
            return {"message": "I don't see an existing booking to cancel. Is there anything else I can help you with?"}
        
        # Transition to canceling state
        self.fsm.transition_to(context, MeetingState.CANCELING, "cancel request")
        
        return {"message": "I can cancel your meeting. Would you like me to proceed now?"}
    
    async def _handle_availability_intent(self, context: MeetingContext, 
                                        message: str) -> Dict[str, Any]:
        """Handle availability inquiry"""
        # Extract date information
        datetime_info = await self.nlu.extract_datetime_slots(message, context)
        
        if datetime_info.get("weekdays"):
            # Case 5: Weekday availability question
            return await self.case_handlers.handle_weekday_availability(
                context, datetime_info["weekdays"][0]
            )
        elif datetime_info.get("dates"):
            # Show availability for specific date
            return await self.case_handlers.handle_date_only(
                context, datetime_info["dates"][0]
            )
        else:
            # General availability inquiry
            return await self.case_handlers.handle_general_availability(context)
    
    async def _handle_confirm_intent(self, context: MeetingContext, 
                                   message: str) -> Dict[str, Any]:
        """Handle confirmation intent"""
        if context.current_state == MeetingState.AWAIT_CONFIRM:
            # User is confirming a slot
            return await self.case_handlers.handle_slot_confirmation(context, message)
        elif context.current_state == MeetingState.CANCELING:
            # User is confirming cancellation
            return await self.case_handlers.handle_cancel_confirmation(context)
        else:
            return {"message": "What would you like me to confirm?"}
    
    async def _handle_defer_intent(self, context: MeetingContext, 
                                 message: str) -> Dict[str, Any]:
        """Handle defer intent (Case 10)"""
        return await self.case_handlers.handle_user_defer(context)
    
    async def _handle_unknown_intent(self, context: MeetingContext, 
                                   message: str) -> Dict[str, Any]:
        """Handle unknown intent with clarification"""
        context.clarification_count += 1
        
        if context.clarification_count >= context.max_clarifications:
            return await self._handle_loop_prevention(context, message)
        
        # Generate clarification based on current state
        if context.current_state == MeetingState.IDLE:
            missing_info = "meeting_request"
        elif not context.target_date_local:
            missing_info = "date"
        elif not context.target_time_local:
            missing_info = "time"
        else:
            missing_info = "confirmation"
        
        clarification = await self.nlu.generate_clarification_prompt(context, missing_info)
        return {"message": clarification}
    
    async def _handle_incomplete_info(self, context: MeetingContext, 
                                    datetime_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incomplete datetime information"""
        if not datetime_info.get("dates") and not datetime_info.get("relative_dates") and not datetime_info.get("weekdays"):
            # Missing date
            self.fsm.transition_to(context, MeetingState.COLLECT_DAY, "missing date")
            return {"message": "Which day would work best for you?"}
        elif not datetime_info.get("times") and not datetime_info.get("time_ranges"):
            # Missing time
            self.fsm.transition_to(context, MeetingState.COLLECT_TIME, "missing time")
            return {"message": "What time would you prefer?"}
        else:
            # General clarification
            return {"message": "Could you provide a bit more detail about when you'd like to meet?"}
    
    async def _handle_loop_prevention(self, context: MeetingContext, 
                                    message: str) -> Dict[str, Any]:
        """Handle loop prevention with escalation"""
        logger.warning(f"Loop prevention triggered for {context.phone_number}")
        
        # Reset clarifications and provide specific options
        context.clarification_count = 0
        
        # Get next few business days with availability
        try:
            next_slots = await self._get_next_available_slots(context.timezone, max_days=5, max_slots=3)
            
            if next_slots:
                slot_descriptions = []
                for slot in next_slots[:2]:  # Show top 2 options
                    date_str = self.timezone_handler.format_date_long(slot.start_time, context.timezone)
                    time_str = self.timezone_handler.format_time_local(slot.start_time, context.timezone)
                    slot_descriptions.append(f"{date_str} at {time_str}")
                
                options_text = " or ".join(slot_descriptions)
                return {"message": f"I can suggest specific options for the next few days. Would {options_text} work?"}
            else:
                return {"message": "Let me connect you with our team to find a suitable time. They'll be in touch shortly."}
                
        except Exception as e:
            logger.error(f"Error in loop prevention: {e}")
            return {"message": "Let me connect you with our team to help schedule your meeting."}
    
    async def _get_next_available_slots(self, timezone_str: str, 
                                      max_days: int = 7, 
                                      max_slots: int = 10) -> List[BookingSlot]:
        """Get next available slots across multiple days"""
        try:
            all_slots = []
            current_date = self.timezone_handler.now_in_timezone(timezone_str)
            
            for day_offset in range(max_days):
                check_date = current_date + timedelta(days=day_offset)
                
                # Skip weekends for business meetings
                if not self.timezone_handler.is_business_day(check_date):
                    continue
                
                day_slots = await self.provider.get_availability(check_date, timezone_str)
                all_slots.extend(day_slots)
                
                if len(all_slots) >= max_slots:
                    break
            
            return all_slots[:max_slots]
            
        except Exception as e:
            logger.error(f"Error getting next available slots: {e}")
            return []
    
    async def create_booking_record(self, context: MeetingContext, 
                                  booking_result: BookingResult) -> Optional[str]:
        """Create booking record in database"""
        try:
            async for db in get_db():
                booking = Booking(
                    lead_id=uuid.UUID(context.lead_id) if context.lead_id else None,
                    zoho_booking_id=booking_result.zoho_booking_id,
                    customer_name="Lead Contact",
                    customer_phone=context.phone_number,
                    service_type="lead_meeting",
                    staff_name="Team Member",
                    start_time=datetime.fromisoformat(context.target_datetime_utc) if context.target_datetime_utc else datetime.utcnow(),
                    end_time=datetime.fromisoformat(context.target_datetime_utc) + timedelta(minutes=30) if context.target_datetime_utc else datetime.utcnow() + timedelta(minutes=30),
                    duration_minutes=30,
                    timezone=context.timezone,
                    status='scheduled',
                    booking_source='meeting_agent',
                    meeting_link=booking_result.meeting_link,
                    booking_url=booking_result.booking_url
                )
                
                db.add(booking)
                await db.commit()
                await db.refresh(booking)
                
                logger.info(f"Booking record created: {booking.id}")
                return str(booking.id)
                
        except Exception as e:
            logger.error(f"Error creating booking record: {e}")
            return None
