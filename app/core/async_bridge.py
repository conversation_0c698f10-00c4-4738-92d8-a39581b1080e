"""
Async-to-Sync Bridge for Celery Tasks
Enables async agent processing within synchronous Celery workers
"""

import asyncio
import threading
from typing import Any, Callable, Coroutine, Optional
import structlog
from concurrent.futures import ThreadPoolExecutor
import functools

from app.core.logging import logger


class AsyncBridge:
    """Bridge to run async code in sync Celery tasks"""
    
    def __init__(self):
        self._executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="async-bridge")
        self._loop_thread = None
        self._loop = None
        self._shutdown = False
    
    def run_async(self, coro: Coroutine) -> Any:
        """
        Run async coroutine in sync context
        
        Args:
            coro: Async coroutine to run
            
        Returns:
            Result of the coroutine
        """
        try:
            # Try to get existing event loop
            try:
                loop = asyncio.get_running_loop()
                # If we're already in an async context, we need to run in a thread
                return self._run_in_thread(coro)
            except RuntimeError:
                # No running loop, we can create one
                return asyncio.run(coro)
                
        except Exception as e:
            logger.error("Failed to run async coroutine", error=str(e))
            raise
    
    def _run_in_thread(self, coro: Coroutine) -> Any:
        """Run coroutine in a separate thread with its own event loop"""
        def run_in_new_loop():
            # Create new event loop for this thread
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            
            try:
                return new_loop.run_until_complete(coro)
            finally:
                new_loop.close()
        
        # Submit to thread pool and wait for result
        future = self._executor.submit(run_in_new_loop)
        return future.result(timeout=300)  # 5 minute timeout
    
    def shutdown(self):
        """Shutdown the async bridge"""
        self._shutdown = True
        if self._executor:
            self._executor.shutdown(wait=True)


# Global instance
async_bridge = AsyncBridge()


def run_async_in_celery(coro: Coroutine) -> Any:
    """
    Convenience function to run async code in Celery tasks
    
    Args:
        coro: Async coroutine to run
        
    Returns:
        Result of the coroutine
    """
    return async_bridge.run_async(coro)


def async_to_sync(async_func: Callable) -> Callable:
    """
    Decorator to convert async function to sync for Celery tasks
    
    Args:
        async_func: Async function to convert
        
    Returns:
        Sync wrapper function
    """
    @functools.wraps(async_func)
    def sync_wrapper(*args, **kwargs):
        coro = async_func(*args, **kwargs)
        return run_async_in_celery(coro)
    
    return sync_wrapper


class CeleryAsyncContext:
    """Context manager for async operations in Celery tasks"""
    
    def __init__(self):
        self.loop = None
        self.original_loop = None
    
    def __enter__(self):
        try:
            # Check if there's already a running loop
            self.original_loop = asyncio.get_running_loop()
            # If there is, we'll need to create a new one in a thread
            return self
        except RuntimeError:
            # No running loop, we can create one
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.loop:
            try:
                # Cancel any remaining tasks
                pending = asyncio.all_tasks(self.loop)
                for task in pending:
                    task.cancel()
                
                # Wait for cancellation to complete
                if pending:
                    self.loop.run_until_complete(
                        asyncio.gather(*pending, return_exceptions=True)
                    )
            except Exception as e:
                logger.warning("Error during async context cleanup", error=str(e))
            finally:
                self.loop.close()
                if self.original_loop:
                    asyncio.set_event_loop(self.original_loop)
    
    def run(self, coro: Coroutine) -> Any:
        """Run coroutine in this context"""
        if self.loop:
            return self.loop.run_until_complete(coro)
        else:
            # We're in a thread, use the bridge
            return async_bridge.run_async(coro)


# Cleanup function for graceful shutdown
def cleanup_async_bridge():
    """Cleanup async bridge on shutdown"""
    global async_bridge
    if async_bridge:
        async_bridge.shutdown()
