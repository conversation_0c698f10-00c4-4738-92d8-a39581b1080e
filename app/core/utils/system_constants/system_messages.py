# System messages for different languages

# Default response template
default_response = {
    "success": False,
    "message": {
        "title": "Error",
        "description": "An unexpected error occurred. Please try again or contact support if the issue persists."
    },
    "data": {"details": None},
    "error_code": 5000
}

# Client messages for different languages
client_messages = {
    "en": {
        # Success messages
        "user_registered_successfully": {
            "title": "Registration Successful",
            "description": "Your account has been created successfully. You can now login with your credentials."
        },
        "user_authenticated_successfully": {
            "title": "Login Successful", 
            "description": "You have been logged in successfully. Welcome back!"
        },
        "user_logged_out_successfully": {
            "title": "Logout Successful",
            "description": "You have been logged out successfully. Thank you for using our service."
        },
        "password_reset_email_sent": {
            "title": "Password Reset Email Sent",
            "description": "Password reset instructions have been sent to your email address. Please check your inbox."
        },
        "password_reset_successfully": {
            "title": "Password Reset Successful",
            "description": "Your password has been reset successfully. You can now login with your new password."
        },
        "user_profile_retrieved": {
            "title": "Profile Retrieved",
            "description": "Your profile information has been retrieved successfully."
        },
        
        # Vendor relation messages
        "vendor_relation_created_successfully": {
            "title": "Vendor Relation Created",
            "description": "The vendor relation has been created successfully."
        },
        "vendor_relation_updated_successfully": {
            "title": "Vendor Relation Updated",
            "description": "The vendor relation has been updated successfully."
        },
        "vendor_relation_deleted_successfully": {
            "title": "Vendor Relation Deleted",
            "description": "The vendor relation has been deleted successfully."
        },
        "vendor_relations_retrieved_successfully": {
            "title": "Vendor Relations Retrieved",
            "description": "Vendor relations have been retrieved successfully."
        },
        
        # Error messages
        "default_error": {
            "title": "Error",
            "description": "An unexpected error occurred. Please try again or contact support if the issue persists."
        },
        "validation_error": {
            "title": "Validation Error",
            "description": "Please check your input data and ensure all required fields are filled correctly."
        },
        "authentication_required": {
            "title": "Authentication Required",
            "description": "Please login to access this resource. If you don't have an account, please register first."
        },
        "insufficient_permissions": {
            "title": "Insufficient Permissions",
            "description": "You don't have permission to access this resource. Please contact your administrator if you believe this is an error."
        }
    }
} 