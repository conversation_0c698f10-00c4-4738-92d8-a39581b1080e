from fastapi import status
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any, Union

from app.schemas.base_response import StandardResponse, MessageResponse
from app.core.utils.system_constants.system_error_code import pre_define_error
from app.core.utils.exception_manager.custom_exceptions import (
    GrowthHiveException,
    AuthenticationError,
    ValidationError,
    DatabaseError,
    ResourceNotFoundError,
    BusinessLogicError
)


class ExceptionHandler:
    _instance = None

    def __new__(cls, *args, **kwargs):
        """Create singleton instance"""
        if not cls._instance:
            cls._instance = super(Exception<PERSON>and<PERSON>, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    @staticmethod
    async def manage_exception(
        error_key: str,
        message: Optional[Union[str, Dict[str, str], MessageResponse]] = None,
        language: str = "en",
        use_error_specific_msg: bool = True,
        return_obj: bool = True,
        details: Optional[Dict[str, Any]] = None
    ) -> Union[JSONResponse, GrowthHiveException]:
        """
        Manage exception and return appropriate response
        
        Args:
            error_key: Key for predefined error
            message: Custom error message (string, dict, or MessageResponse)
            language: Response language
            use_error_specific_msg: Whether to use error specific message
            return_obj: Whether to return JSONResponse or raise exception
            details: Additional error details
            
        Returns:
            JSONResponse or raises GrowthHiveException
        """
        try:
            # Get error definition
            err = pre_define_error.get(language, pre_define_error.get('en', {})).get(error_key) or {}
            
            # Create default message
            default_message = MessageResponse(
                title="Error",
                description="An unexpected error occurred. Please try again or contact support if the issue persists."
            )
            
            # Handle different message types
            if isinstance(message, str):
                error_message = MessageResponse(
                    title=err.get("message", {}).get("title", "Error"),
                    description=message
                )
            elif isinstance(message, dict):
                error_message = MessageResponse(**message)
            elif isinstance(message, MessageResponse):
                error_message = message
            elif err and use_error_specific_msg:
                error_message = MessageResponse(**err["message"])
            else:
                error_message = default_message

            response = StandardResponse(
                success=False,
                message=error_message,
                data=details or {},
                error_code=err.get("code", 5000)
            )

            if return_obj:
                return JSONResponse(
                    content=response.model_dump(),
                    status_code=err.get("http_status", status.HTTP_500_INTERNAL_SERVER_ERROR)
                )
            
            # Map error codes to appropriate exception types
            error_code = err.get("code", 5000)
            if 4000 <= error_code < 4100:
                raise AuthenticationError(error_key, response.message.model_dump(), err.get("http_status"), error_code, details)
            elif 4100 <= error_code < 4200:
                raise ResourceNotFoundError(error_key, response.message.model_dump(), err.get("http_status"), error_code, details)
            elif 4200 <= error_code < 5000:
                raise ValidationError(error_key, response.message.model_dump(), err.get("http_status"), error_code, details)
            elif 5000 <= error_code < 6000:
                raise DatabaseError(error_key, response.message.model_dump(), err.get("http_status"), error_code, details)
            elif 6000 <= error_code < 7000:
                raise BusinessLogicError(error_key, response.message.model_dump(), err.get("http_status"), error_code, details)
            else:
                raise GrowthHiveException(error_key, response.message.model_dump(), err.get("http_status"), error_code, details)
                
        except Exception as e:
            # Fallback for any errors in exception handling
            fallback_response = StandardResponse(
                success=False,
                message=MessageResponse(
                    title="Internal Server Error",
                    description=f"An error occurred while handling the exception: {str(e)}"
                ),
                data={},
                error_code=5000
            )
            return JSONResponse(
                content=fallback_response.model_dump(),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Singleton instance
exception_handler_obj = ExceptionHandler() 