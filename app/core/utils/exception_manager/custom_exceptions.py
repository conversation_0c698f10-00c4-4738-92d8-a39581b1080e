from typing import Optional, Dict, Any, Union
from fastapi import status

from app.schemas.base_response import BaseResponse, ResponseMessage, ResponseMetadata


class GrowthHiveException(Exception):
    """Base exception class for Growth Hive application"""
    def __init__(
        self,
        error_key: str,
        message: Union[str, Dict[str, str], ResponseMessage],
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code: int = 5000,
        details: Optional[Dict[str, Any]] = None
    ):
        self.error_key = error_key
        self.status_code = status_code
        self.error_code = error_code
        
        # Handle different message types
        if isinstance(message, str):
            self.message = ResponseMessage(
                title="Error",
                description=message
            )
        elif isinstance(message, dict):
            self.message = ResponseMessage(**message)
        elif isinstance(message, ResponseMessage):
            self.message = message
        else:
            self.message = ResponseMessage(
                title="Error",
                description="An unexpected error occurred"
            )
            
        self.details = details
        self.detail = BaseResponse(
            success=False,
            status="error",
            message=self.message,
            data=details,
            metadata=ResponseMetadata(),
            error_code=str(self.error_code)
        ).model_dump()
        
        super().__init__(self.message.description)


class AuthenticationError(GrowthHiveException):
    """Exception raised for authentication errors"""
    def __init__(
        self,
        error_key: str,
        message: Union[str, Dict[str, str], ResponseMessage],
        status_code: int = status.HTTP_401_UNAUTHORIZED,
        error_code: int = 4001,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(error_key, message, status_code, error_code, details)


class ValidationError(GrowthHiveException):
    """Exception raised for validation errors"""
    def __init__(
        self,
        error_key: str,
        message: Union[str, Dict[str, str], ResponseMessage],
        status_code: int = status.HTTP_400_BAD_REQUEST,
        error_code: int = 4202,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(error_key, message, status_code, error_code, details)


class DatabaseError(GrowthHiveException):
    """Exception raised for database errors"""
    def __init__(
        self,
        error_key: str,
        message: Union[str, Dict[str, str], ResponseMessage],
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code: int = 5001,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(error_key, message, status_code, error_code, details)


class ResourceNotFoundError(GrowthHiveException):
    """Exception raised when a requested resource is not found"""
    def __init__(
        self,
        error_key: str,
        message: Union[str, Dict[str, str], ResponseMessage],
        status_code: int = status.HTTP_404_NOT_FOUND,
        error_code: int = 4104,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(error_key, message, status_code, error_code, details)


class BusinessLogicError(GrowthHiveException):
    """Exception raised for business logic errors"""
    def __init__(
        self,
        error_key: str,
        message: Union[str, Dict[str, str], ResponseMessage],
        status_code: int = status.HTTP_400_BAD_REQUEST,
        error_code: int = 6000,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(error_key, message, status_code, error_code, details) 