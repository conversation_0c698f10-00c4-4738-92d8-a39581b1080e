"""
SMS Control System
Centralized control for SMS sending based on environment flags.
Handles SMS_TEST_MODE and KUDOSITY_SMS_ENABLED flags to prevent actual SMS sending during testing.
"""

import os
import datetime as dt
from typing import Dict, Any, Optional
import structlog

from app.core.logging import logger


class SMSController:
    """
    Central controller for SMS sending behavior based on environment flags.
    Ensures no actual SMS messages are sent when in test mode or when SMS is disabled.
    """
    
    def __init__(self):
        self.sms_test_mode = os.getenv("SMS_TEST_MODE", "false").lower() == "true"
        self.kudosity_sms_enabled = os.getenv("KUDOSITY_SMS_ENABLED", "false").lower() == "true"
        self.workflow_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"
        
        # Log configuration on initialization
        logger.info(
            "SMS Controller initialized",
            sms_test_mode=self.sms_test_mode,
            kudosity_sms_enabled=self.kudosity_sms_enabled,
            workflow_enabled=self.workflow_enabled,
            will_send_sms=self.should_send_sms()
        )
    
    def should_send_sms(self) -> bool:
        """
        Determine if SMS should actually be sent based on environment flags.
        
        Returns:
            bool: True if SMS should be sent, False if it should be mocked/logged only
        """
        return self.kudosity_sms_enabled and not self.sms_test_mode
    
    def get_sms_status(self) -> Dict[str, Any]:
        """
        Get current SMS configuration status.
        
        Returns:
            Dict with SMS configuration details
        """
        return {
            "sms_test_mode": self.sms_test_mode,
            "kudosity_sms_enabled": self.kudosity_sms_enabled,
            "workflow_enabled": self.workflow_enabled,
            "will_send_sms": self.should_send_sms(),
            "reason": self._get_status_reason()
        }
    
    def _get_status_reason(self) -> str:
        """Get reason for current SMS sending status"""
        if not self.kudosity_sms_enabled:
            return "Kudosity SMS disabled (KUDOSITY_SMS_ENABLED=false)"
        elif self.sms_test_mode:
            return "SMS test mode enabled (SMS_TEST_MODE=true)"
        else:
            return "SMS sending enabled"
    
    def log_sms_attempt(
        self, 
        phone_number: str, 
        message: str, 
        context: str = "general"
    ) -> Dict[str, Any]:
        """
        Log an SMS attempt with appropriate handling based on flags.
        
        Args:
            phone_number: Phone number (will be masked in logs)
            message: SMS message content
            context: Context of the SMS (e.g., "coochie_workflow", "general_response")
            
        Returns:
            Dict with logging information
        """
        masked_phone = self._mask_phone_number(phone_number)
        message_length = len(message)
        
        if self.should_send_sms():
            # SMS will be sent - log normally
            logger.info(
                "SMS will be sent",
                context=context,
                phone_masked=masked_phone,
                message_length=message_length,
                sms_test_mode=self.sms_test_mode,
                kudosity_enabled=self.kudosity_sms_enabled
            )
            
            return {
                "action": "send_sms",
                "will_send": True,
                "reason": "SMS sending enabled"
            }
        else:
            # SMS will NOT be sent - log as mock
            logger.info(
                "SMS mocked (not sent)",
                context=context,
                phone_masked=masked_phone,
                message_length=message_length,
                sms_test_mode=self.sms_test_mode,
                kudosity_enabled=self.kudosity_sms_enabled,
                reason=self._get_status_reason()
            )
            
            return {
                "action": "mock_sms",
                "will_send": False,
                "reason": self._get_status_reason()
            }
    
    def print_sms_output(
        self, 
        phone_number: str, 
        message: str, 
        context: str = "SMS Response"
    ):
        """
        Print SMS output to terminal when SMS is disabled or in test mode.
        
        Args:
            phone_number: Phone number to send to
            message: Message content
            context: Context description
        """
        if not self.should_send_sms():
            print(f"\n" + "="*80)
            print(f"📱 {context.upper()} (NOT SENT - SMS DISABLED)")
            print(f"="*80)
            print(f"SMS_TEST_MODE: {self.sms_test_mode}")
            print(f"KUDOSITY_SMS_ENABLED: {self.kudosity_sms_enabled}")
            print(f"Reason: {self._get_status_reason()}")
            print(f"To: {phone_number}")
            print(f"Message Length: {len(message)} characters")
            print(f"Message Content:")
            print(f"   {message}")
            print(f"="*80)
            print(f"💡 To enable SMS: Set SMS_TEST_MODE=false AND KUDOSITY_SMS_ENABLED=true")
            print(f"="*80 + "\n")
    
    def create_mock_sms_response(self, context: str = "mock") -> Dict[str, Any]:
        """
        Create a mock SMS response for when SMS is disabled.
        
        Args:
            context: Context for the mock response
            
        Returns:
            Mock SMS response dict
        """
        timestamp = dt.datetime.now().strftime('%H%M%S')
        mock_id = f"mock_{context}_{timestamp}"
        
        return {
            "success": True,
            "message_id": mock_id,
            "error": None,
            "mock": True,
            "reason": self._get_status_reason()
        }
    
    def _mask_phone_number(self, phone_number: str) -> str:
        """Mask phone number for logging (privacy)"""
        if len(phone_number) > 6:
            return phone_number[:6] + "***"
        else:
            return "***"
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate SMS configuration and provide recommendations.
        
        Returns:
            Dict with validation results and recommendations
        """
        validation = {
            "valid": True,
            "warnings": [],
            "recommendations": [],
            "current_config": self.get_sms_status()
        }
        
        # Check for common configuration issues
        if self.sms_test_mode and self.kudosity_sms_enabled:
            validation["warnings"].append(
                "SMS_TEST_MODE=true overrides KUDOSITY_SMS_ENABLED=true - no SMS will be sent"
            )
            validation["recommendations"].append(
                "Set SMS_TEST_MODE=false to enable SMS sending"
            )
        
        if not self.kudosity_sms_enabled and not self.sms_test_mode:
            validation["warnings"].append(
                "Both SMS_TEST_MODE and KUDOSITY_SMS_ENABLED are false - no SMS will be sent"
            )
            validation["recommendations"].append(
                "Set KUDOSITY_SMS_ENABLED=true to enable SMS sending"
            )
        
        if self.workflow_enabled and not self.should_send_sms():
            validation["warnings"].append(
                "Coochie workflow is enabled but SMS sending is disabled - responses will only appear in logs"
            )
        
        return validation


# Global SMS controller instance
sms_controller = SMSController()


def should_send_sms() -> bool:
    """Quick check if SMS should be sent"""
    return sms_controller.should_send_sms()


def get_sms_status() -> Dict[str, Any]:
    """Get current SMS status"""
    return sms_controller.get_sms_status()


def log_sms_attempt(phone_number: str, message: str, context: str = "general") -> Dict[str, Any]:
    """Log an SMS attempt"""
    return sms_controller.log_sms_attempt(phone_number, message, context)


def print_sms_output(phone_number: str, message: str, context: str = "SMS Response"):
    """Print SMS output to terminal"""
    sms_controller.print_sms_output(phone_number, message, context)


def create_mock_sms_response(context: str = "mock") -> Dict[str, Any]:
    """Create mock SMS response"""
    return sms_controller.create_mock_sms_response(context)


def validate_sms_configuration() -> Dict[str, Any]:
    """Validate SMS configuration"""
    return sms_controller.validate_configuration()


# Export main components
__all__ = [
    "SMSController",
    "sms_controller",
    "should_send_sms",
    "get_sms_status", 
    "log_sms_attempt",
    "print_sms_output",
    "create_mock_sms_response",
    "validate_sms_configuration"
]
