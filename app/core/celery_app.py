"""
Celery Application Configuration
Centralized Celery app for background task processing
"""

import os
from celery import Celery
from app.core.config.settings import settings


def _get_beat_schedule():
    """Get beat schedule with conditional follow-up task suppression"""

    # Check if follow-up suppression is enabled
    followup_suppression_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"

    base_schedule = {}

    if not followup_suppression_enabled:
        # Include follow-up related periodic tasks only when suppression is disabled
        base_schedule.update({
            'detect-new-leads-every-30-minutes': {
                'task': 'app.tasks.sms_onboarding_tasks.periodic_new_lead_detection',
                'schedule': 30.0 * 60,  # Every 30 minutes
            },
            # 'cleanup-completed-followups-every-hour': {
            #     'task': 'app.tasks.followup_tasks.cleanup_completed_followups',
            #     'schedule': 60.0 * 60,  # Every hour
            # },  # Removed for Andy AI
        })
    else:
        # Log that follow-up periodic tasks are suppressed
        import structlog
        from app.core.logging import logger
        logger.info(
            "Follow-up periodic tasks suppressed",
            suppression_enabled=True,
            reason="WORKFLOW_ANDY_NO_FOLLOWUPS enabled"
        )

    return base_schedule

def create_celery_app() -> Celery:
    """Create and configure Celery application"""
    
    celery_app = Celery(
        "growthhive",
        broker=settings.CELERY_BROKER_URL,
        backend=settings.CELERY_RESULT_BACKEND,
        include=[
            "app.tasks.document_processing",
            "app.tasks.docqa_processing",
            "app.tasks.sms_tasks",
            "app.tasks.sms_onboarding_tasks",
            # "app.tasks.followup_tasks",  # Removed for Andy AI
        ]
    )
    
    # Configure Celery
    celery_app.conf.update(
        task_serializer=settings.CELERY_TASK_SERIALIZER,
        result_serializer=settings.CELERY_RESULT_SERIALIZER,
        accept_content=settings.CELERY_ACCEPT_CONTENT,
        timezone=settings.CELERY_TIMEZONE,
        enable_utc=settings.CELERY_ENABLE_UTC,
        
        # Task routing
        task_routes={
            "app.tasks.document_processing.*": {"queue": settings.DOCUMENT_PROCESSING_QUEUE},
            "app.tasks.docqa_processing.*": {"queue": settings.DOCUMENT_PROCESSING_QUEUE},
            "app.tasks.sms_tasks.*": {"queue": "sms_queue"},
            "app.tasks.sms_onboarding_tasks.*": {"queue": "sms_onboarding_queue"},
            # "app.tasks.followup_tasks.*": {"queue": "andy_followup_queue"},  # Removed for Andy AI
            "app.tasks.status_monitoring_tasks.*": {"queue": "monitoring_queue"},
        },
        
        # Task execution settings
        task_acks_late=True,
        worker_prefetch_multiplier=1,
        
        # Retry settings
        task_default_retry_delay=settings.DOCUMENT_PROCESSING_RETRY_DELAY,
        task_max_retries=settings.DOCUMENT_PROCESSING_MAX_RETRIES,
        
        # Result settings
        result_expires=3600,  # 1 hour
        result_persistent=True,
        
        # Worker settings
        worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
        worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
        
        # Monitoring
        worker_send_task_events=True,
        task_send_sent_event=True,

        # Periodic tasks (Celery Beat) - Follow-up tasks conditionally disabled
        beat_schedule=_get_beat_schedule(),
    )
    
    return celery_app

# Create the Celery app instance
celery_app = create_celery_app()

# Auto-discover tasks
celery_app.autodiscover_tasks()

if __name__ == "__main__":
    celery_app.start()
