"""Response utility functions for standardized API responses"""

import json
import logging
from typing import Any, Dict, List, Optional, Union
from fastapi import HTT<PERSON>Ex<PERSON>
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder

from .models import (
    StandardResponse, 
    MessageModel, 
    PaginationModel,
    EncryptedResponse,
    ErrorCodes,
    ERROR_MESSAGES
)

logger = logging.getLogger(__name__)


def create_success_response(
    data: Union[Dict[str, Any], List[Any], str, int, float, bool, None] = None,
    message_title: str = "",
    message_description: str = "Operation completed successfully",
    pagination: Optional[PaginationModel] = None,
    status_code: int = 200,
    wrap_in_details: bool = False
) -> JSONResponse:
    """
    Create a standardized success response

    Args:
        data: The response data
        message_title: Message title
        message_description: Message description
        pagination: Pagination information
        status_code: HTTP status code
        wrap_in_details: Whether to wrap data in a 'details' field

    Returns:
        JSONResponse with standardized format
    """
    message = MessageModel(title=message_title, description=message_description)

    # Handle data structure based on requirements
    if wrap_in_details or pagination:
        response_data = {"details": data}
        if pagination:
            response_data["pagination"] = pagination.model_dump()
    else:
        response_data = data

    # Build response kwargs
    response_kwargs = dict(
        success=True,
        message=message,
        error_code=0
    )
    if response_data is not None:
        response_kwargs["data"] = response_data

    response = StandardResponse(**response_kwargs)

    return JSONResponse(
        content=jsonable_encoder(response.model_dump(exclude_none=True)),
        status_code=status_code
    )


def create_error_response(
    error_code: int,
    message_title: Optional[str] = None,
    message_description: Optional[str] = None,
    status_code: int = 400,
    data: Optional[Dict[str, Any]] = None
) -> JSONResponse:
    """
    Create a standardized error response
    
    Args:
        error_code: 4-digit error code
        message_title: Custom message title (optional)
        message_description: Custom message description (optional)
        status_code: HTTP status code
        data: Additional error data (optional)
    
    Returns:
        JSONResponse with standardized error format
    """
    # Get default error message if not provided
    default_error = ERROR_MESSAGES.get(error_code, {
        "title": "Error",
        "description": "An error occurred"
    })
    
    title = message_title or default_error["title"]
    description = message_description or default_error["description"]
    
    message = MessageModel(title=title, description=description)

    response = StandardResponse(
        success=False,
        message=message,
        data=data if data else None,  # Only include data if it's not empty
        error_code=error_code
    )

    return JSONResponse(
        content=jsonable_encoder(response.model_dump(exclude_none=True)),
        status_code=status_code
    )


def create_paginated_response(
    items: List[Any],
    current_page: int,
    items_per_page: int,
    total_items: int,
    message_title: str = "",
    message_description: str = "Data retrieved successfully",
    status_code: int = 200
) -> JSONResponse:
    """
    Create a standardized paginated response

    Args:
        items: List of items for current page
        current_page: Current page number
        items_per_page: Items per page
        total_items: Total number of items
        message_title: Message title
        message_description: Message description
        status_code: HTTP status code

    Returns:
        JSONResponse with paginated format
    """
    total_pages = (total_items + items_per_page - 1) // items_per_page

    pagination = PaginationModel(
        current_page=current_page,
        total_pages=total_pages,
        items_per_page=items_per_page,
        total_items=total_items
    )

    return create_success_response(
        data=items,
        message_title=message_title,
        message_description=message_description,
        pagination=pagination,
        status_code=status_code,
        wrap_in_details=True  # Always wrap paginated responses in details
    )


def handle_http_exception(exc: HTTPException) -> JSONResponse:
    """
    Convert HTTPException to standardized error response
    
    Args:
        exc: HTTPException instance
    
    Returns:
        JSONResponse with standardized error format
    """
    # Map HTTP status codes to error codes
    status_to_error_code = {
        400: ErrorCodes.INVALID_INPUT,
        401: ErrorCodes.INVALID_ACCESS_TOKEN,
        403: ErrorCodes.INSUFFICIENT_PERMISSIONS,
        404: ErrorCodes.RESOURCE_NOT_FOUND,
        409: ErrorCodes.DUPLICATE_ENTRY,
        422: ErrorCodes.MISMATCH_INPUT_SCHEMA,
        500: ErrorCodes.UNKNOWN_ERROR,
        503: ErrorCodes.SERVICE_UNAVAILABLE
    }
    
    error_code = status_to_error_code.get(exc.status_code, ErrorCodes.UNKNOWN_ERROR)
    
    return create_error_response(
        error_code=error_code,
        message_description=str(exc.detail),
        status_code=exc.status_code
    )


def log_error(error_code: int, exception_type: str, function_name: str, microservice: str = "GH"):
    """
    Log error in the specified format
    
    Args:
        error_code: 4-digit error code
        exception_type: Type of exception
        function_name: Function where error occurred
        microservice: Microservice initials (default: GH for GrowthHive)
    """
    log_message = f"{microservice}-{error_code}-{exception_type}-{function_name}"
    logger.error(log_message)


# Encryption/Decryption placeholder functions
def encrypt_response(response_data: Dict[str, Any]) -> EncryptedResponse:
    """
    Encrypt response data (placeholder implementation)
    
    Args:
        response_data: Data to encrypt
    
    Returns:
        EncryptedResponse with encrypted string
    """
    # TODO: Implement actual encryption logic
    # For now, just return JSON string (in production, this should be encrypted)
    encrypted_string = json.dumps(response_data)
    
    return EncryptedResponse(details=encrypted_string)


def decrypt_request(encrypted_data: str) -> Dict[str, Any]:
    """
    Decrypt request data (placeholder implementation)
    
    Args:
        encrypted_data: Encrypted data string
    
    Returns:
        Decrypted data dictionary
    """
    # TODO: Implement actual decryption logic
    # For now, just parse JSON string (in production, this should be decrypted)
    try:
        return json.loads(encrypted_data)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400,
            detail="Invalid encrypted data format"
        )


# Convenience functions for common responses
def success_response(data: Any = None, message: str = "Operation completed successfully"):
    """Quick success response"""
    return create_success_response(data=data, message_description=message)


def create_list_response(
    items: List[Any],
    message_title: str = "",
    message_description: str = "Data retrieved successfully",
    status_code: int = 200
) -> JSONResponse:
    """
    Create a standardized list response (non-paginated) with details structure

    Args:
        items: List of items to return
        message_title: Message title
        message_description: Message description
        status_code: HTTP status code

    Returns:
        JSONResponse with list format using details structure
    """
    return create_success_response(
        data=items,
        message_title=message_title,
        message_description=message_description,
        status_code=status_code,
        wrap_in_details=True  # Always wrap list responses in details for consistency
    )


def error_response(error_code: int, message: str = None, status_code: int = 400):
    """Quick error response"""
    return create_error_response(
        error_code=error_code,
        message_description=message,
        status_code=status_code
    )


def not_found_response(message: str = "Resource not found"):
    """Quick not found response"""
    return create_error_response(
        error_code=ErrorCodes.RESOURCE_NOT_FOUND,
        message_description=message,
        status_code=404
    )


def validation_error_response(message: str = "Invalid input data"):
    """Quick validation error response"""
    return create_error_response(
        error_code=ErrorCodes.INVALID_INPUT,
        message_description=message,
        status_code=400
    )


def unauthorized_response(message: str = "Authentication required"):
    """Quick unauthorized response"""
    return create_error_response(
        error_code=ErrorCodes.MISSING_ACCESS_TOKEN,
        message_description=message,
        status_code=401
    )
