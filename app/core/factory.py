from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.base import get_db
from app.repositories.category_repository import CategoryRepository
from app.repositories.industry_repository import IndustryRepository

from app.repositories.document_repository import DocumentRepository
from app.repositories.franchisor_repository import FranchisorRepository
from app.repositories.lead_repository import LeadRepository
from app.repositories.holiday_repository import HolidayRepository
from app.repositories.messaging_rule_repository import MessagingRuleRepository
from app.repositories.conversation_message_repository import ConversationMessageRepository
from app.services.category_service import CategoryService
from app.services.industry_service import IndustryService

from app.services.document_service import DocumentService
from app.services.franchisor_service import FranchisorService
from app.services.lead_service import LeadService
from app.services.s3_service import S3Service
from app.services.holiday_service import HolidayService
from app.services.messaging_rule_service import MessagingRuleService
from app.services.conversation_message_service import ConversationMessageService


def get_category_repository(db: AsyncSession = Depends(get_db)) -> CategoryRepository:
    """Factory for CategoryRepository."""
    return CategoryRepository(db)


def get_franchisor_repository(db: AsyncSession = Depends(get_db)) -> FranchisorRepository:
    """Factory for FranchisorRepository."""
    return FranchisorRepository(db)


def get_franchisor_service(
    repository: FranchisorRepository = Depends(get_franchisor_repository)
) -> FranchisorService:
    """Factory for FranchisorService with dependency injection."""
    return FranchisorService(repository)


def get_lead_repository(db: AsyncSession = Depends(get_db)) -> LeadRepository:
    """Factory for LeadRepository."""
    return LeadRepository(db)


def get_lead_service(db: AsyncSession = Depends(get_db)) -> LeadService:
    """Factory for LeadService with dependency injection."""
    return LeadService(db)


def get_category_service(
    repo: CategoryRepository = Depends(get_category_repository),
) -> CategoryService:
    """Factory for CategoryService."""
    return CategoryService(repo)

def get_industry_repository(db: AsyncSession = Depends(get_db)) -> IndustryRepository:
    """Factory for IndustryRepository."""
    return IndustryRepository(db)

def get_industry_service(
    repo: IndustryRepository = Depends(get_industry_repository),
) -> IndustryService:
    """Factory for IndustryService."""
    return IndustryService(repo)



def get_document_repository(db: AsyncSession = Depends(get_db)) -> DocumentRepository:
    """Factory for DocumentRepository."""
    return DocumentRepository(db)

def get_s3_service() -> S3Service:
    """Factory for S3Service."""
    return S3Service()

def get_document_service(
    repo: DocumentRepository = Depends(get_document_repository),
    s3_service: S3Service = Depends(get_s3_service),
) -> DocumentService:
    """Factory for DocumentService."""
    return DocumentService(repo, s3_service)


# Holiday Management Factories
def get_holiday_repository(db: AsyncSession = Depends(get_db)) -> HolidayRepository:
    """Factory for HolidayRepository."""
    return HolidayRepository(db)


def get_holiday_service(
    repo: HolidayRepository = Depends(get_holiday_repository),
) -> HolidayService:
    """Factory for HolidayService."""
    return HolidayService(repo)


# Messaging Rule Factories
def get_messaging_rule_repository(db: AsyncSession = Depends(get_db)) -> MessagingRuleRepository:
    """Factory for MessagingRuleRepository."""
    return MessagingRuleRepository(db)


def get_messaging_rule_service(
    repo: MessagingRuleRepository = Depends(get_messaging_rule_repository),
) -> MessagingRuleService:
    """Factory for MessagingRuleService."""
    return MessagingRuleService(repo)


# Conversation Message Factories
def get_conversation_message_repository(db: AsyncSession = Depends(get_db)) -> ConversationMessageRepository:
    """Factory for ConversationMessageRepository."""
    return ConversationMessageRepository(db)


def get_conversation_message_service(
    repo: ConversationMessageRepository = Depends(get_conversation_message_repository),
    lead_service: LeadService = Depends(get_lead_service),
    franchisor_service: FranchisorService = Depends(get_franchisor_service),
) -> ConversationMessageService:
    """Factory for ConversationMessageService."""
    return ConversationMessageService(repo, lead_service, franchisor_service)