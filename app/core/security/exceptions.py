"""
Security exceptions for handling security-related exceptions
"""
from fastapi import HTTPException, status

class SecurityException(HTTPException):
    """Base security exception"""
    pass

class AuthenticationError(SecurityException):
    """Authentication error"""
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )

class AuthorizationError(SecurityException):
    """Authorization error"""
    def __init__(self, detail: str = "Not enough permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )

class TokenError(SecurityException):
    """Token error"""
    def __init__(self, detail: str = "Invalid token"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )

class TokenExpiredError(TokenError):
    """Token expired error"""
    def __init__(self):
        super().__init__(detail="Token has expired")

class TokenBlacklistedError(TokenError):
    """Token blacklisted error"""
    def __init__(self):
        super().__init__(detail="Token has been invalidated")

class RateLimitError(SecurityException):
    """Rate limit error"""
    def __init__(self, retry_after: int):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded",
            headers={"Retry-After": str(retry_after)}
        )

class PasswordError(SecurityException):
    """Password error"""
    def __init__(self, detail: str = "Invalid password"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )

class EmailVerificationError(SecurityException):
    """Email verification error"""
    def __init__(self, detail: str = "Email verification failed"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )

class PasswordResetError(SecurityException):
    """Password reset error"""
    def __init__(self, detail: str = "Password reset failed"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )

class SessionError(SecurityException):
    """Session error"""
    def __init__(self, detail: str = "Session error"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )

class RefreshTokenError(SecurityException):
    """Refresh token error"""
    def __init__(self, detail: str = "Refresh token error"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        ) 