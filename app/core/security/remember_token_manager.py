"""
Remember Token Manager for handling remember_me_token authentication
"""

import secrets
import logging
import re
from datetime import datetime, timedelta, timezone
from typing import Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from app.models.user import User
from app.core.config.settings import settings

logger = logging.getLogger(__name__)


class RememberTokenManager:
    """Manager for remember_me_token operations"""
    
    def __init__(self):
        """Initialize remember token manager with validation"""
        # Validate configuration
        if not hasattr(settings, 'REMEMBER_ME_TOKEN_LENGTH') or settings.REMEMBER_ME_TOKEN_LENGTH < 16:
            logger.warning("REMEMBER_ME_TOKEN_LENGTH not set or too small, using default 32")
            self.token_length = 32
        else:
            self.token_length = settings.REMEMBER_ME_TOKEN_LENGTH

        # Token validation pattern (URL-safe base64)
        self.token_pattern = re.compile(r'^[A-Za-z0-9_-]+$')

    def generate_remember_token(self) -> str:
        """
        Generate a cryptographically secure remember me token

        Returns:
            str: Cryptographically secure URL-safe token

        Raises:
            RuntimeError: If token generation fails
        """
        try:
            # Generate multiple tokens and pick the most random one
            tokens = [secrets.token_urlsafe(self.token_length) for _ in range(3)]
            # Select token with best entropy (most varied characters)
            selected_token = max(tokens, key=lambda t: len(set(t)))

            # Validate generated token
            if not self._validate_token_strength(selected_token):
                raise RuntimeError("Generated token failed strength validation")

            return selected_token
        except Exception as e:
            logger.error(f"Failed to generate remember token: {e}")
            raise RuntimeError("Token generation failed") from e

    def _validate_token_strength(self, token: str) -> bool:
        """
        Validate token strength and format

        Args:
            token: Token to validate

        Returns:
            bool: True if token meets strength requirements
        """
        if not token or len(token) < 20:
            return False

        # Check character diversity
        unique_chars = len(set(token))
        if unique_chars < len(token) * 0.6:  # At least 60% unique characters
            return False

        # Check for URL-safe base64 format
        if not self.token_pattern.match(token):
            return False

        return True
    
    async def store_remember_token(
        self,
        user_id: str,
        token: str,
        db: AsyncSession
    ) -> bool:
        """
        Store remember_me_token for user with robust error handling

        Args:
            user_id: User ID (validated)
            token: Remember me token (validated)
            db: Database session

        Returns:
            bool: True if successful, False otherwise
        """
        # Input validation
        if not user_id or not isinstance(user_id, str):
            logger.error("Invalid user_id provided to store_remember_token")
            return False

        if not token or not isinstance(token, str):
            logger.error("Invalid token provided to store_remember_token")
            return False

        # Validate token format
        if not self.validate_remember_token_format(token):
            logger.error("Invalid token format provided to store_remember_token")
            return False

        try:
            # Convert user_id to UUID if it's a string
            try:
                user_uuid = UUID(user_id) if isinstance(user_id, str) else user_id
            except ValueError:
                logger.error(f"Invalid user_id format: {user_id}")
                return False

            # Check if user exists first
            user_check = await db.execute(
                select(User.id).where(User.id == user_uuid, User.is_active)
            )
            if not user_check.scalar_one_or_none():
                logger.warning(f"Attempted to store remember token for non-existent/inactive user: {user_id}")
                return False

            logger.info(f"Storing remember token for user: {user_id}, token: {token[:10]}...")

            # Store token with timestamp
            result = await db.execute(
                update(User)
                .where(User.id == user_uuid)
                .values(
                    remember_me_token=token,
                    remember_me_token_expires_at=datetime.now(timezone.utc) + timedelta(days=30),  # 30 days expiry
                    last_login_at=datetime.now(timezone.utc)
                )
            )

            # Check if update was successful
            if result.rowcount == 0:
                logger.warning(f"No rows updated when storing remember token for user: {user_id}")
                await db.rollback()
                return False

            await db.commit()
            logger.info(f"Successfully stored remember token for user: {user_id}")
            return True

        except IntegrityError as e:
            logger.error(f"Database integrity error storing remember token: {e}")
            await db.rollback()
            return False
        except SQLAlchemyError as e:
            logger.error(f"Database error storing remember token: {e}")
            await db.rollback()
            return False
        except Exception as e:
            logger.error(f"Unexpected error storing remember token: {e}")
            await db.rollback()
            return False
    
    async def authenticate_with_remember_token(
        self,
        token: str,
        db: AsyncSession
    ) -> Optional[User]:
        """
        Authenticate user using remember_me_token with comprehensive security checks

        Args:
            token: Remember me token (will be validated)
            db: Database session

        Returns:
            Optional[User]: User if token is valid and all security checks pass, None otherwise
        """
        # Input validation
        if not token or not isinstance(token, str):
            logger.warning("Invalid token provided to authenticate_with_remember_token")
            return None

        # Sanitize token (remove whitespace, limit length)
        token = token.strip()
        if len(token) > 200:  # Reasonable upper limit
            logger.warning("Token too long in authenticate_with_remember_token")
            return None

        # Validate token format
        if not self.validate_remember_token_format(token):
            logger.warning(f"Invalid token format in authentication: {token[:10]}...")
            # Temporarily disable format validation for debugging
            return None

        try:
            # Find user by remember_me_token with additional security checks
            logger.info(f"Attempting to authenticate with remember token: {token[:10]}...")
            
            # Simplified query to find user by remember_me_token
            result = await db.execute(
                select(User).where(User.remember_me_token == token)
            )
            user = result.scalar_one_or_none()

            if not user:
                logger.warning(f"No user found with remember token: {token[:10]}...")
                return None

            logger.info(f"Found user for remember token: {user.email}")

            # Check if user is active
            if not user.is_active:
                logger.warning(f"Inactive user attempted remember token authentication: {user.email}")
                return None

            # Check token expiration if expiry field exists
            if hasattr(user, 'remember_me_token_expires_at') and user.remember_me_token_expires_at:
                current_time = datetime.now(timezone.utc)
                logger.info(f"Token expires at: {user.remember_me_token_expires_at}")
                logger.info(f"Current time: {current_time}")
                if current_time > user.remember_me_token_expires_at:
                    logger.warning(f"Expired remember token used: {token[:10]}...")
                    return None
                else:
                    logger.info("Token is not expired")

            # Additional security checks
            if not user.email or not user.id:
                logger.error(f"User data incomplete for remember token authentication: {user.id}")
                return None

            # Update last login timestamp with error handling
            try:
                user.last_login_at = datetime.now(timezone.utc)
                await db.commit()
            except SQLAlchemyError as e:
                logger.error(f"Failed to update last_login_at for user {user.email}: {e}")
                # Don't fail authentication for this, but log it
                await db.rollback()
                # Re-establish session state
                await db.refresh(user)

            logger.info(f"Successful remember token authentication for user: {user.email}")
            return user

        except SQLAlchemyError as e:
            logger.error(f"Database error during remember token authentication: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during remember token authentication: {e}", exc_info=True)
            return None
    
    async def clear_remember_token(
        self, 
        user_id: str, 
        db: AsyncSession
    ) -> bool:
        """
        Clear remember_me_token for user (logout)
        
        Args:
            user_id: User ID
            db: Database session
            
        Returns:
            bool: True if successful
        """
        try:
            # Convert user_id to UUID if it's a string
            try:
                user_uuid = UUID(user_id) if isinstance(user_id, str) else user_id
            except ValueError:
                logger.error(f"Invalid user_id format: {user_id}")
                return False

            await db.execute(
                update(User)
                .where(User.id == user_uuid)
                .values(
                    remember_me_token=None,
                    remember_me_token_expires_at=None
                )
            )
            await db.commit()
            
            logger.info(f"Cleared remember token for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear remember token: {e}")
            await db.rollback()
            return False
    
    async def rotate_remember_token(
        self, 
        user_id: str, 
        db: AsyncSession
    ) -> Optional[str]:
        """
        Rotate remember_me_token for security
        
        Args:
            user_id: User ID
            db: Database session
            
        Returns:
            Optional[str]: New token if successful, None otherwise
        """
        try:
            # Convert user_id to UUID if it's a string
            try:
                user_uuid = UUID(user_id) if isinstance(user_id, str) else user_id
            except ValueError:
                logger.error(f"Invalid user_id format: {user_id}")
                return None

            new_token = self.generate_remember_token()
            
            await db.execute(
                update(User)
                .where(User.id == user_uuid)
                .values(remember_me_token=new_token)
            )
            await db.commit()
            
            logger.info(f"Rotated remember token for user: {user_id}")
            return new_token
            
        except Exception as e:
            logger.error(f"Failed to rotate remember token: {e}")
            await db.rollback()
            return None
    
    async def revoke_all_remember_tokens(
        self, 
        user_id: str, 
        db: AsyncSession
    ) -> bool:
        """
        Revoke all remember me sessions for user
        
        Args:
            user_id: User ID
            db: Database session
            
        Returns:
            bool: True if successful
        """
        return await self.clear_remember_token(user_id, db)
    
    def extract_remember_token_from_request(self, request) -> Optional[str]:
        """
        Safely extract remember_me_token from request with validation

        Args:
            request: FastAPI request object

        Returns:
            Optional[str]: Sanitized remember token if found and valid, None otherwise
        """
        if not request:
            return None

        try:
            # Primary: Check custom header
            remember_token = request.headers.get("X-Remember-Token")

            # Fallback: Check alternative header names (for flexibility)
            if not remember_token:
                remember_token = request.headers.get("X-Remember-Me-Token")

            # Fallback: Check cookies (if enabled)
            if not remember_token and hasattr(request, 'cookies'):
                remember_token = request.cookies.get("remember_token")

            if remember_token:
                # Sanitize and validate
                sanitized_token = self._sanitize_token(remember_token)
                if sanitized_token and len(sanitized_token) >= 20:
                    return sanitized_token

            return None

        except Exception as e:
            logger.error(f"Error extracting remember token from request: {e}")
            return None

    def _sanitize_token(self, token: str) -> Optional[str]:
        """
        Sanitize token input

        Args:
            token: Raw token from request

        Returns:
            Optional[str]: Sanitized token or None if invalid
        """
        if not token or not isinstance(token, str):
            return None

        # Remove whitespace and control characters
        sanitized = ''.join(c for c in token.strip() if c.isprintable())

        # Length check
        if len(sanitized) < 20 or len(sanitized) > 200:
            return None

        # Basic format check
        if not self.token_pattern.match(sanitized):
            return None

        return sanitized
    
    def validate_remember_token_format(self, token: str) -> bool:
        """
        Comprehensive remember token format validation

        Args:
            token: Token to validate

        Returns:
            bool: True if format is valid and secure
        """
        if not token or not isinstance(token, str):
            return False

        # Length validation (reasonable bounds)
        if len(token) < 20 or len(token) > 200:
            return False

        # Character validation (URL-safe base64)
        if not self.token_pattern.match(token):
            return False

        # Temporarily disable strict security checks for debugging
        return True
        
        # Security checks
        try:
            # Check for suspicious patterns
            if self._has_suspicious_patterns(token):
                return False

            # Check entropy (randomness)
            if not self._has_sufficient_entropy(token):
                return False

            return True
        except Exception as e:
            logger.error(f"Error validating token format: {e}")
            return False

    def _has_suspicious_patterns(self, token: str) -> bool:
        """Check for suspicious patterns in token"""
        # Check for repeated patterns
        if len(set(token)) < len(token) * 0.4:  # Less than 40% unique characters
            return True

        # Check for common patterns
        suspicious_patterns = ['000', '111', 'aaa', 'AAA', '___', '---']
        for pattern in suspicious_patterns:
            if pattern in token:
                return True

        return False

    def _has_sufficient_entropy(self, token: str) -> bool:
        """Check if token has sufficient entropy"""
        # Simple entropy check - count unique characters
        unique_chars = len(set(token))
        min_unique = max(10, len(token) * 0.5)  # At least 50% unique or 10 chars
        return unique_chars >= min_unique


# Global instance
remember_token_manager = RememberTokenManager()
