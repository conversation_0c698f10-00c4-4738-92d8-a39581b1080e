"""
Password hasher service for handling password hashing and verification
"""
import bcrypt
from passlib.context import CryptContext

# Configure password hashing
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=12  # Increased rounds for better security
)

class PasswordHasher:
    """Password hasher service for handling password hashing and verification"""

    @staticmethod
    def hash_password(password: str) -> str:
        """
        Hash a password using bcrypt
        
        Args:
            password: The password to hash
            
        Returns:
            The hashed password
        """
        return pwd_context.hash(password)

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash
        
        Args:
            plain_password: The plain text password to verify
            hashed_password: The hashed password to verify against
            
        Returns:
            True if the password matches, False otherwise
        """
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def needs_rehash(hashed_password: str) -> bool:
        """
        Check if a password hash needs to be rehashed
        
        Args:
            hashed_password: The hashed password to check
            
        Returns:
            True if the hash needs to be rehashed, False otherwise
        """
        return pwd_context.needs_update(hashed_password)

    @staticmethod
    def generate_salt() -> bytes:
        """
        Generate a random salt for password hashing
        
        Returns:
            A random salt
        """
        return bcrypt.gensalt()

    @staticmethod
    def hash_with_salt(password: str, salt: bytes) -> bytes:
        """
        Hash a password with a specific salt
        
        Args:
            password: The password to hash
            salt: The salt to use
            
        Returns:
            The hashed password with salt
        """
        return bcrypt.hashpw(password.encode(), salt)

    @staticmethod
    def verify_with_salt(password: str, hashed_password: bytes) -> bool:
        """
        Verify a password against its hash with salt
        
        Args:
            password: The plain text password to verify
            hashed_password: The hashed password with salt to verify against
            
        Returns:
            True if the password matches, False otherwise
        """
        return bcrypt.checkpw(password.encode(), hashed_password)

# Create a singleton instance
password_hasher = PasswordHasher() 