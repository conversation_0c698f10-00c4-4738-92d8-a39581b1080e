"""
Enhanced authentication error handler with precise error messages
"""

from typing import Dict, Any, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from fastapi.responses import JSONResponse
from enum import Enum


class AuthErrorType(Enum):
    """Authentication error types"""
    MISSING_TOKEN = "missing_token"
    INVALID_TOKEN_FORMAT = "invalid_token_format"
    INVALID_TOKEN = "invalid_token"
    EXPIRED_TOKEN = "expired_token"
    INSUFFICIENT_PERMISSIONS = "insufficient_permissions"
    INVALID_CREDENTIALS = "invalid_credentials"
    ACCOUNT_DISABLED = "account_disabled"
    ACCOUNT_LOCKED = "account_locked"
    TOKEN_REVOKED = "token_revoked"
    MALFORMED_HEADER = "malformed_header"
    UNSUPPORTED_TOKEN_TYPE = "unsupported_token_type"
    AUTHENTICATION_FAILED = "authentication_failed"


class AuthErrorHandler:
    """Enhanced authentication error handler"""
    
    # Error messages mapping
    ERROR_MESSAGES = {
        AuthErrorType.MISSING_TOKEN: {
            "title": "Authentication Required",
            "description": "Access token is required. Please provide a valid Bearer token in the Authorization header.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2003  # MISSING_ACCESS_TOKEN
        },
        AuthErrorType.INVALID_TOKEN_FORMAT: {
            "title": "Invalid Token Format",
            "description": "The provided token format is invalid. Expected format: 'Bearer <token>'.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2014  # INVALID_TOKEN_FORMAT
        },
        AuthErrorType.INVALID_TOKEN: {
            "title": "Invalid Token",
            "description": "The provided access token is invalid or malformed. Please login again to get a new token.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2001  # INVALID_ACCESS_TOKEN
        },
        AuthErrorType.EXPIRED_TOKEN: {
            "title": "Token Expired",
            "description": "Your access token has expired. Please login again to get a new token.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2002  # EXPIRED_ACCESS_TOKEN
        },
        AuthErrorType.INSUFFICIENT_PERMISSIONS: {
            "title": "Insufficient Permissions",
            "description": "You do not have sufficient permissions to access this resource.",
            "status_code": status.HTTP_403_FORBIDDEN,
            "error_code": 3104  # INSUFFICIENT_PERMISSIONS
        },
        AuthErrorType.INVALID_CREDENTIALS: {
            "title": "Invalid Credentials",
            "description": "The provided email/mobile and password combination is incorrect.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 5004  # INVALID_USER_CREDENTIALS
        },
        AuthErrorType.ACCOUNT_DISABLED: {
            "title": "Account Disabled",
            "description": "Your account has been disabled. Please contact support for assistance.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 5007  # ACCOUNT_DISABLED
        },
        AuthErrorType.ACCOUNT_LOCKED: {
            "title": "Account Locked",
            "description": "Your account has been temporarily locked due to multiple failed login attempts. Please try again later.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 5003  # ACCOUNT_LOCKED
        },
        AuthErrorType.TOKEN_REVOKED: {
            "title": "Token Revoked",
            "description": "Your access token has been revoked. Please login again to get a new token.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2016  # TOKEN_REVOKED
        },
        AuthErrorType.MALFORMED_HEADER: {
            "title": "Malformed Authorization Header",
            "description": "The Authorization header is malformed. Expected format: 'Authorization: Bearer <token>'.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2017  # MALFORMED_AUTH_HEADER
        },
        AuthErrorType.UNSUPPORTED_TOKEN_TYPE: {
            "title": "Unsupported Token Type",
            "description": "Only Bearer tokens are supported. Please provide a valid Bearer token.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2018  # UNSUPPORTED_TOKEN_TYPE
        },
        AuthErrorType.AUTHENTICATION_FAILED: {
            "title": "Authentication Failed",
            "description": "Authentication failed. Please provide valid credentials.",
            "status_code": status.HTTP_401_UNAUTHORIZED,
            "error_code": 2013  # AUTHENTICATION_FAILED
        }
    }
    
    @classmethod
    def create_auth_error(
        self,
        error_type: AuthErrorType,
        details: Optional[Dict[str, Any]] = None,
        custom_message: Optional[str] = None
    ) -> JSONResponse:
        """
        Create a standardized authentication error response
        
        Args:
            error_type: Type of authentication error
            details: Additional error details
            custom_message: Custom error message (overrides default)
            
        Returns:
            JSONResponse: Standardized error response
        """
        error_config = self.ERROR_MESSAGES.get(error_type)
        
        if not error_config:
            # Fallback for unknown error types
            error_config = {
                "title": "Authentication Error",
                "description": "An authentication error occurred.",
                "status_code": status.HTTP_401_UNAUTHORIZED,
                "error_code": 2013  # AUTHENTICATION_FAILED
            }
        
        # Use custom message if provided
        description = custom_message if custom_message else error_config["description"]
        
        return JSONResponse(
            status_code=error_config["status_code"],
            content={
                "success": False,
                "message": {
                    "title": error_config["title"],
                    "description": description
                },
                "data": details if details else None,
                "error_code": error_config["error_code"]
            }
        )
    
    @classmethod
    def create_http_exception(
        self,
        error_type: AuthErrorType,
        details: Optional[Dict[str, Any]] = None,
        custom_message: Optional[str] = None
    ) -> HTTPException:
        """
        Create an HTTPException for authentication errors
        
        Args:
            error_type: Type of authentication error
            details: Additional error details
            custom_message: Custom error message
            
        Returns:
            HTTPException: FastAPI HTTP exception
        """
        error_config = self.ERROR_MESSAGES.get(error_type, {
            "title": "Authentication Error",
            "description": "An authentication error occurred.",
            "status_code": status.HTTP_401_UNAUTHORIZED
        })
        
        description = custom_message if custom_message else error_config["description"]
        
        return HTTPException(
            status_code=error_config["status_code"],
            detail=description,
            headers={"WWW-Authenticate": "Bearer"} if error_config["status_code"] == 401 else None
        )
    
    @classmethod
    def handle_token_validation_error(self, error_message: str) -> JSONResponse:
        """
        Handle token validation errors with specific error types
        
        Args:
            error_message: Error message from token validation
            
        Returns:
            JSONResponse: Appropriate error response
        """
        error_message_lower = error_message.lower()
        
        if "expired" in error_message_lower:
            return self.create_auth_error(AuthErrorType.EXPIRED_TOKEN)
        elif "invalid" in error_message_lower or "malformed" in error_message_lower:
            return self.create_auth_error(AuthErrorType.INVALID_TOKEN)
        elif "signature" in error_message_lower:
            return self.create_auth_error(AuthErrorType.INVALID_TOKEN)
        else:
            return self.create_auth_error(AuthErrorType.INVALID_TOKEN)
    
    @classmethod
    def handle_authorization_header_error(self, authorization_header: Optional[str]) -> Optional[JSONResponse]:
        """
        Handle authorization header validation errors
        
        Args:
            authorization_header: The authorization header value
            
        Returns:
            Optional[JSONResponse]: Error response if header is invalid, None if valid
        """
        if not authorization_header:
            return self.create_auth_error(AuthErrorType.MISSING_TOKEN)
        
        if not authorization_header.startswith("Bearer "):
            return self.create_auth_error(AuthErrorType.INVALID_TOKEN_FORMAT)
        
        # Check if token part is empty
        token_part = authorization_header[7:]  # Remove "Bearer "
        if not token_part.strip():
            return self.create_auth_error(AuthErrorType.MISSING_TOKEN)
        
        # Header is valid, return None
        return None
    
    @classmethod
    def handle_login_error(
        self,
        error_type: str,
        identifier: Optional[str] = None,
        additional_details: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """
        Handle login-specific errors
        
        Args:
            error_type: Type of login error
            identifier: User identifier (email/mobile)
            additional_details: Additional error details
            
        Returns:
            JSONResponse: Appropriate error response
        """
        error_mapping = {
            "invalid_credentials": AuthErrorType.INVALID_CREDENTIALS,
            "account_disabled": AuthErrorType.ACCOUNT_DISABLED,
            "account_locked": AuthErrorType.ACCOUNT_LOCKED,
            "account_not_found": AuthErrorType.INVALID_CREDENTIALS
        }
        
        auth_error_type = error_mapping.get(error_type, AuthErrorType.INVALID_CREDENTIALS)
        
        details = additional_details or {}
        if identifier:
            details["identifier"] = identifier
        
        return self.create_auth_error(auth_error_type, details=details)
    
    @classmethod
    def get_error_details(self, error_type: AuthErrorType) -> Dict[str, Any]:
        """
        Get error details for a specific error type
        
        Args:
            error_type: Type of authentication error
            
        Returns:
            Dict: Error details
        """
        return self.ERROR_MESSAGES.get(error_type, {})


# Global instance for easy access
auth_error_handler = AuthErrorHandler()
