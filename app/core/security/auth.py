"""
Authentication utilities for GrowthHive API
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer
from sqlalchemy.ext.asyncio import AsyncSession
from jose import JWTError, jwt
from app.core.database.connection import get_db
from app.services.user_service import UserService
from app.schemas.user import UserResponse
from app.core.config.settings import settings
from app.core.logging import logger

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    """Get current authenticated user from JWT token.
    
    Args:
        token: JWT token from OAuth2 scheme
        db: Database session
        
    Returns:
        UserResponse: Current authenticated user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Decode token
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        # Get user ID from token
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
        # Get user from database
        user_service = UserService(db)
        user = await user_service.get_user(user_id)
        
        if user is None:
            raise credentials_exception
            
        return user
        
    except JWTError as e:
        logger.error(f"JWT validation failed: {e}")
        raise credentials_exception

async def get_current_admin_user(
    current_user: UserResponse = Depends(get_current_user)
) -> UserResponse:
    """Get current authenticated admin user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserResponse: Current admin user
        
    Raises:
        HTTPException: If user is not an admin
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

async def get_current_admin_or_manager_user(
    current_user: UserResponse = Depends(get_current_user)
) -> UserResponse:
    """Get current authenticated admin or manager user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserResponse: Current admin or manager user
        
    Raises:
        HTTPException: If user is not an admin or manager
    """
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

async def get_current_user_with_profile(
    current_user: UserResponse = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    """Get current authenticated user with profile information.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserResponse: Current user with profile information
        
    Raises:
        HTTPException: If user profile is not found
    """
    user_service = UserService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user.id)
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    # Return the original current_user object with additional profile info
    return current_user
