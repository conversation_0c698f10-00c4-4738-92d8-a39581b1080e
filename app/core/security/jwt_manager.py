"""
JWT token manager service for handling JWT token generation and validation
"""
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
import jwt
from fastapi import HTTPEx<PERSON>, status
from pydantic import BaseModel
from app.core.config.settings import settings

class TokenData(BaseModel):
    """Token data model"""
    sub: str  # Subject (user ID)
    exp: datetime  # Expiration time
    type: str  # Token type (access or refresh)
    jti: str  # JWT ID

class JWTManager:
    """JWT token manager service for handling JWT token generation and validation"""

    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 30,
        refresh_token_expire_days: int = 7
    ):
        """
        Initialize JWT manager
        
        Args:
            secret_key: The secret key for signing tokens
            algorithm: The algorithm to use for signing
            access_token_expire_minutes: Access token expiration in minutes
            refresh_token_expire_days: Refresh token expiration in days
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days

    def create_access_token(
        self,
        user_id: str,
        jti: str,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create an access token
        
        Args:
            user_id: The user ID to include in the token
            jti: The JWT ID to include in the token
            expires_delta: Optional expiration time delta
            
        Returns:
            The encoded access token
        """
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire_minutes)

        to_encode = {
            "sub": str(user_id),
            "exp": expire,
            "type": "access",
            "jti": jti
        }

        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

    def create_refresh_token(
        self,
        user_id: str,
        jti: str,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a refresh token
        
        Args:
            user_id: The user ID to include in the token
            jti: The JWT ID to include in the token
            expires_delta: Optional expiration time delta
            
        Returns:
            The encoded refresh token
        """
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire_days)

        to_encode = {
            "sub": str(user_id),
            "exp": expire,
            "type": "refresh",
            "jti": jti
        }

        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> TokenData:
        """
        Verify a token and return its data
        
        Args:
            token: The token to verify
            
        Returns:
            The token data
            
        Raises:
            HTTPException: If the token is invalid or expired
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return TokenData(**payload)
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )

    def get_token_data(self, token: str) -> Dict[str, Any]:
        """
        Get token data without verification
        
        Args:
            token: The token to decode
            
        Returns:
            The token data
            
        Raises:
            HTTPException: If the token is invalid
        """
        try:
            return jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )

    def is_token_expired(self, token: str) -> bool:
        """
        Check if a token is expired
        
        Args:
            token: The token to check
            
        Returns:
            True if the token is expired, False otherwise
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            exp = datetime.fromtimestamp(payload["exp"], timezone.utc)
            return exp < datetime.now(timezone.utc)
        except jwt.JWTError:
            return True

# Create a singleton instance
jwt_manager = JWTManager(
    secret_key=settings.JWT_SECRET_KEY,
    algorithm=settings.JWT_ALGORITHM,
    access_token_expire_minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    refresh_token_expire_days=settings.REFRESH_TOKEN_EXPIRE_DAYS
) 