"""Password handler for secure password management"""
from passlib.context import CryptContext

from app.core.logging import logger


class PasswordHandler:
    """Class for hashing and verifying passwords using bcrypt."""
    
    _instance = None

    def __new__(cls):
        """Ensure only one instance exists (singleton pattern)."""
        if cls._instance is None:
            cls._instance = super(PasswordHandler, cls).__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        """Initialize the password context with bcrypt."""
        if not hasattr(self, 'pwd_context'):
            self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    async def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt.
        
        Args:
            password: Plain text password to hash
            
        Returns:
            str: Hashed password
            
        Raises:
            Exception: If hashing fails
        """
        try:
            hashed = self.pwd_context.hash(password)
            logger.info("Password hashed successfully")
            return hashed
        except Exception as e:
            logger.error(f"Failed to hash password: {e}")
            raise

    async def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash.
        
        Args:
            plain_password: Plain text password to verify
            hashed_password: Hashed password to verify against
            
        Returns:
            bool: True if password matches, False otherwise
        """
        try:
            is_valid = self.pwd_context.verify(plain_password, hashed_password)
            logger.info(f"Password verification completed: {is_valid}")
            return is_valid
        except Exception as e:
            logger.error(f"Failed to verify password: {e}")
            return False

    def validate_password_strength(self, password: str) -> bool:
        """Validate password strength.
        
        Requirements:
        - At least 8 characters long
        - Contains at least one uppercase letter
        - Contains at least one lowercase letter
        - Contains at least one digit
        - Contains at least one special character
        
        Args:
            password: Password to validate
            
        Returns:
            bool: True if password meets requirements, False otherwise
        """
        if len(password) < 8:
            return False
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;':\",./<>?" for c in password)
        
        return all([has_upper, has_lower, has_digit, has_special])


# Global singleton instance
password_handler = PasswordHandler() 