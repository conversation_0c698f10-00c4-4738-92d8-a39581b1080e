"""
Token Blacklist
Handles JWT token blacklisting for logout functionality
"""

from typing import Set, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class TokenBlacklist:
    """Simple in-memory token blacklist"""
    
    def __init__(self):
        self._blacklisted_tokens: Set[str] = set()
        self._token_expiry: dict[str, datetime] = {}
    
    def add_to_blacklist(self, token: str, jti: str, expires_at: Optional[datetime] = None) -> None:
        """Add a token to the blacklist"""
        self._blacklisted_tokens.add(token)
        if expires_at:
            self._token_expiry[token] = expires_at
        logger.info(f"Token {jti} added to blacklist")
    
    def is_blacklisted(self, token: str) -> bool:
        """Check if a token is blacklisted"""
        if token in self._blacklisted_tokens:
            # Check if token has expired
            if token in self._token_expiry:
                if datetime.utcnow() > self._token_expiry[token]:
                    # Remove expired token from blacklist
                    self._blacklisted_tokens.discard(token)
                    del self._token_expiry[token]
                    return False
            return True
        return False
    
    def cleanup_expired_tokens(self) -> None:
        """Remove expired tokens from blacklist"""
        current_time = datetime.utcnow()
        expired_tokens = [
            token for token, expiry in self._token_expiry.items()
            if current_time > expiry
        ]
        
        for token in expired_tokens:
            self._blacklisted_tokens.discard(token)
            del self._token_expiry[token]
        
        if expired_tokens:
            logger.info(f"Cleaned up {len(expired_tokens)} expired tokens from blacklist")

# Global instance
token_blacklist = TokenBlacklist() 