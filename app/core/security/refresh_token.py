"""
Refresh Token Manager for handling JWT refresh tokens
"""
from datetime import datetime, timezone, timedelta
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, delete
from jose import jwt

from app.models.refresh_token import RefreshToken
from app.core.config.settings import settings
from app.core.logging import logger

class RefreshTokenManager:
    """Manages refresh tokens with secure storage and validation"""

    async def create_refresh_token(
        self,
        user_id: str,
        db: AsyncSession,
        remember_me: bool = False
    ) -> str:
        """
        Create a new refresh token

        Args:
            user_id: User ID
            db: Database session
            remember_me: Whether to create a long-lived token

        Returns:
            str: JWT refresh token
        """
        try:
            # Set expiration based on remember_me
            if remember_me:
                expires_delta = timedelta(days=settings.JWT_REMEMBER_ME_REFRESH_TOKEN_EXPIRE_DAYS)
            else:
                expires_delta = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

            to_encode = {
                "sub": user_id,
                "type": "refresh",
                "remember_me": remember_me,
                "exp": datetime.now(timezone.utc) + expires_delta
            }
            token = jwt.encode(
                to_encode,
                settings.JWT_SECRET_KEY,
                algorithm=settings.JWT_ALGORITHM
            )

            # Store token in database
            refresh_token = RefreshToken(
                user_id=user_id,
                token=token,
                expires_at=datetime.now(timezone.utc) + expires_delta,
                is_remember_me=remember_me
            )
            db.add(refresh_token)
            await db.commit()

            return token
        except Exception as e:
            logger.error(f"Error creating refresh token: {e}")
            raise

    async def verify_refresh_token(
        self,
        db: AsyncSession,
        token: str
    ) -> Optional[str]:
        """
        Verify a refresh token

        Args:
            db: Database session
            token: Refresh token

        Returns:
            Optional[str]: User ID if token is valid, None otherwise
        """
        try:
            # Decode and verify token
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )

            if payload.get("type") != "refresh":
                logger.warning("Invalid token type for refresh")
                return None

            user_id = payload.get("sub")
            if not user_id:
                logger.warning("No user ID in refresh token")
                return None

            # Check if token exists in database and is not expired
            stmt = select(RefreshToken).where(
                and_(
                    RefreshToken.token == token,
                    RefreshToken.user_id == user_id,
                    RefreshToken.expires_at > datetime.now(timezone.utc)
                )
            )
            result = await db.execute(stmt)
            stored_token = result.scalar_one_or_none()

            if not stored_token:
                logger.warning("Refresh token not found or expired")
                return None

            # Verify remember_me status matches
            if payload.get("remember_me") != stored_token.is_remember_me:
                logger.warning("Token remember_me status mismatch")
                return None

            return user_id

        except jwt.ExpiredSignatureError:
            logger.warning("Expired refresh token")
            return None
        except jwt.JWTError as e:
            logger.error(f"JWT error during refresh token verification: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying refresh token: {e}")
            raise

    async def invalidate_refresh_token(
        self,
        db: AsyncSession,
        user_id: str,
        token: str
    ) -> bool:
        """
        Invalidate a refresh token

        Args:
            db: Database session
            user_id: User ID
            token: Refresh token

        Returns:
            bool: True if token was invalidated successfully
        """
        try:
            stmt = delete(RefreshToken).where(
                and_(
                    RefreshToken.token == token,
                    RefreshToken.user_id == user_id
                )
            )
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error invalidating refresh token: {e}")
            raise

    async def invalidate_all_refresh_tokens(
        self,
        db: AsyncSession,
        user_id: str
    ) -> int:
        """
        Invalidate all refresh tokens for a user

        Args:
            db: Database session
            user_id: User ID

        Returns:
            int: Number of tokens invalidated
        """
        try:
            stmt = delete(RefreshToken).where(RefreshToken.user_id == user_id)
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"Error invalidating all refresh tokens: {e}")
            raise

    async def cleanup_expired_tokens(
        self,
        db: AsyncSession
    ) -> int:
        """
        Clean up expired refresh tokens

        Args:
            db: Database session

        Returns:
            int: Number of tokens cleaned up
        """
        try:
            stmt = delete(RefreshToken).where(
                RefreshToken.expires_at <= datetime.now(timezone.utc)
            )
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"Error cleaning up expired refresh tokens: {e}")
            raise 