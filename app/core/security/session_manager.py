"""
Session Manager for handling user sessions
"""
from datetime import datetime, timezone, timedelta
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update

from app.models.session import Session
from app.core.logging import logger

class SessionManager:
    """Manages user sessions with comprehensive tracking and security features"""

    async def create_session(
        self,
        db: AsyncSession,
        user_id: str,
        user_agent: str,
        ip_address: Optional[str] = None,
        remember_me: bool = False
    ) -> Session:
        """
        Create a new user session

        Args:
            db: Database session
            user_id: User ID
            user_agent: User agent string
            ip_address: IP address (optional)
            remember_me: Whether to create a long-lived session

        Returns:
            Session: Created session object
        """
        try:
            session = Session(
                user_id=user_id,
                user_agent=user_agent,
                ip_address=ip_address,
                last_activity=datetime.now(timezone.utc),
                expires_at=datetime.now(timezone.utc) + (
                    timedelta(days=30) if remember_me else timedelta(hours=24)
                )
            )
            db.add(session)
            await db.commit()
            await db.refresh(session)
            return session
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise

    async def get_user_sessions(
        self,
        db: AsyncSession,
        user_id: str
    ) -> List[Session]:
        """
        Get all active sessions for a user

        Args:
            db: Database session
            user_id: User ID

        Returns:
            List[Session]: List of active sessions
        """
        try:
            stmt = select(Session).where(
                and_(
                    Session.user_id == user_id,
                    Session.expires_at > datetime.now(timezone.utc)
                )
            ).order_by(Session.last_activity.desc())
            result = await db.execute(stmt)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting user sessions: {e}")
            raise

    async def end_session(
        self,
        db: AsyncSession,
        user_id: str,
        user_agent: str,
        ip_address: Optional[str] = None
    ) -> bool:
        """
        End a specific session

        Args:
            db: Database session
            user_id: User ID
            user_agent: User agent string
            ip_address: IP address (optional)

        Returns:
            bool: True if session was ended successfully
        """
        try:
            stmt = update(Session).where(
                and_(
                    Session.user_id == user_id,
                    Session.user_agent == user_agent,
                    Session.ip_address == ip_address,
                    Session.expires_at > datetime.now(timezone.utc)
                )
            ).values(expires_at=datetime.now(timezone.utc))
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error ending session: {e}")
            raise

    async def end_session_by_id(
        self,
        db: AsyncSession,
        session_id: str,
        user_id: str
    ) -> bool:
        """
        End a session by its ID

        Args:
            db: Database session
            session_id: Session ID
            user_id: User ID

        Returns:
            bool: True if session was ended successfully
        """
        try:
            stmt = update(Session).where(
                and_(
                    Session.id == session_id,
                    Session.user_id == user_id
                )
            ).values(expires_at=datetime.now(timezone.utc))
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error ending session by ID: {e}")
            raise

    async def end_all_sessions_except_current(
        self,
        db: AsyncSession,
        user_id: str,
        user_agent: str,
        ip_address: Optional[str] = None
    ) -> int:
        """
        End all sessions except the current one

        Args:
            db: Database session
            user_id: User ID
            user_agent: Current user agent
            ip_address: Current IP address (optional)

        Returns:
            int: Number of sessions ended
        """
        try:
            stmt = update(Session).where(
                and_(
                    Session.user_id == user_id,
                    Session.user_agent != user_agent,
                    Session.ip_address != ip_address,
                    Session.expires_at > datetime.now(timezone.utc)
                )
            ).values(expires_at=datetime.now(timezone.utc))
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"Error ending all sessions except current: {e}")
            raise

    async def update_session_activity(
        self,
        db: AsyncSession,
        session_id: str,
        user_id: str
    ) -> bool:
        """
        Update session's last activity timestamp

        Args:
            db: Database session
            session_id: Session ID
            user_id: User ID

        Returns:
            bool: True if session was updated successfully
        """
        try:
            stmt = update(Session).where(
                and_(
                    Session.id == session_id,
                    Session.user_id == user_id,
                    Session.expires_at > datetime.now(timezone.utc)
                )
            ).values(last_activity=datetime.now(timezone.utc))
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating session activity: {e}")
            raise

    async def cleanup_expired_sessions(
        self,
        db: AsyncSession
    ) -> int:
        """
        Clean up expired sessions

        Args:
            db: Database session

        Returns:
            int: Number of sessions cleaned up
        """
        try:
            stmt = update(Session).where(
                Session.expires_at <= datetime.now(timezone.utc)
            ).values(expires_at=datetime.now(timezone.utc))
            result = await db.execute(stmt)
            await db.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
            raise 