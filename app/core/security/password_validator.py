"""
Password validator service for handling password validation
"""
import re
from typing import <PERSON>ple, List

class PasswordValidator:
    """Password validator service for handling password validation"""

    def __init__(self) -> None:
        """Initialize password validator with default rules"""
        self.min_length = 8
        self.max_length = 128
        self.require_uppercase = True
        self.require_lowercase = True
        self.require_digit = True
        self.require_special = True
        self.special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    def validate(self, password: str) -> Tuple[bool, List[str]]:
        """
        Validate a password against the configured rules
        
        Args:
            password: The password to validate
            
        Returns:
            Tuple of (is_valid, list of error messages)
        """
        errors = []

        # Check length
        if len(password) < self.min_length:
            errors.append(f"Password must be at least {self.min_length} characters long")
        if len(password) > self.max_length:
            errors.append(f"Password must be at most {self.max_length} characters long")

        # Check character requirements
        if self.require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        if self.require_lowercase and not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        if self.require_digit and not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")
        if self.require_special and not any(c in self.special_chars for c in password):
            errors.append(f"Password must contain at least one special character ({self.special_chars})")

        return len(errors) == 0, errors

    def get_requirements(self) -> List[str]:
        """Get list of password requirements"""
        requirements = [
            f"At least {self.min_length} characters long",
            f"At most {self.max_length} characters long"
        ]

        if self.require_uppercase:
            requirements.append("At least one uppercase letter")
        if self.require_lowercase:
            requirements.append("At least one lowercase letter")
        if self.require_digit:
            requirements.append("At least one digit")
        if self.require_special:
            requirements.append(f"At least one special character ({self.special_chars})")

        return requirements

# Create a singleton instance
password_validator = PasswordValidator()
