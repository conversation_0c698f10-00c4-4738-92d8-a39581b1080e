"""
Security constants for handling security-related constants
"""

# Token types
TOKEN_TYPE_ACCESS = "access"
TOKEN_TYPE_REFRESH = "refresh"
TOKEN_TYPE_EMAIL_VERIFICATION = "email_verification"
TOKEN_TYPE_PASSWORD_RESET = "password_reset"

# Token expiration times
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7
EMAIL_VERIFICATION_TOKEN_EXPIRE_MINUTES = 60
PASSWORD_RESET_TOKEN_EXPIRE_MINUTES = 60

# Password requirements
PASSWORD_MIN_LENGTH = 8
PASSWORD_MAX_LENGTH = 128
PASSWORD_REQUIRE_UPPERCASE = True
PASSWORD_REQUIRE_LOWERCASE = True
PASSWORD_REQUIRE_DIGIT = True
PASSWORD_REQUIRE_SPECIAL = True
PASSWORD_SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?"

# Session settings
SESSION_EXPIRE_DAYS = 30
SESSION_CLEANUP_DAYS = 7

# Token blacklist settings
BLACKLIST_PREFIX = "bl_"
BLACKLIST_TOKEN_EXPIRE_DAYS = 7

# Security headers
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
}

# CORS settings
CORS_ORIGINS = ["*"]
CORS_ORIGIN_REGEX = None
CORS_METHODS = ["*"]
CORS_HEADERS = ["*"]

# Security middleware settings
SECURITY_MIDDLEWARE_SETTINGS = {
    "security_headers": SECURITY_HEADERS,
    "cors_origins": CORS_ORIGINS,
    "cors_origin_regex": CORS_ORIGIN_REGEX,
    "cors_methods": CORS_METHODS,
    "cors_headers": CORS_HEADERS
}

# Security dependencies settings
SECURITY_DEPENDENCIES_SETTINGS = {
    "token_type": TOKEN_TYPE_ACCESS,
    "token_expire_minutes": ACCESS_TOKEN_EXPIRE_MINUTES,
    "refresh_token_expire_days": REFRESH_TOKEN_EXPIRE_DAYS,
    "password_min_length": PASSWORD_MIN_LENGTH,
    "password_max_length": PASSWORD_MAX_LENGTH,
    "password_require_uppercase": PASSWORD_REQUIRE_UPPERCASE,
    "password_require_lowercase": PASSWORD_REQUIRE_LOWERCASE,
    "password_require_digit": PASSWORD_REQUIRE_DIGIT,
    "password_require_special": PASSWORD_REQUIRE_SPECIAL,
    "password_special_chars": PASSWORD_SPECIAL_CHARS,
    "session_expire_days": SESSION_EXPIRE_DAYS,
    "session_cleanup_days": SESSION_CLEANUP_DAYS
}

# Security exceptions settings
SECURITY_EXCEPTIONS_SETTINGS = {
    "authentication_error": {
        "status_code": 401,
        "detail": "Could not validate credentials"
    },
    "authorization_error": {
        "status_code": 403,
        "detail": "Not enough permissions"
    },
    "token_error": {
        "status_code": 401,
        "detail": "Invalid token"
    },
    "session_error": {
        "status_code": 401,
        "detail": "Invalid session"
    },
    "refresh_token_error": {
        "status_code": 401,
        "detail": "Invalid refresh token"
    }
} 