"""
Email verification manager for user registration and email changes
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from jose import jwt, JWTError

from app.models.user import User
from app.core.config.settings import settings
from app.core.logging import logger

class EmailVerificationManager:
    """Manager for email verification tokens and logic"""

    @staticmethod
    def create_verification_token(email: str) -> str:
        """Create a JWT verification token for the given email address"""
        try:
            expire = datetime.utcnow() + timedelta(hours=24)
            payload = {
                "sub": email,
                "exp": expire,
                "type": "email_verification"
            }
            token = jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
            return token
        except Exception as e:
            logger.error(f"Failed to create verification token: {e}")
            raise

    @staticmethod
    async def send_verification_email(email: str, db: AsyncSession) -> None:
        """Stub for sending verification email (removed logic)"""
        logger.info(f"[Stub] Would send verification email to: {email}")
        # Email sending logic removed as per request
        pass

    @staticmethod
    async def verify_email(token: str, db: AsyncSession) -> Optional[User]:
        """Verify a user's email using the provided token"""
        try:
            payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            if payload.get("type") != "email_verification":
                logger.error("Invalid token type for email verification")
                return None
            email = payload.get("sub")
            if not email:
                logger.error("Token missing email subject")
                return None
            result = await db.execute(select(User).where(User.email == email))
            user = result.scalar_one_or_none()
            if not user:
                logger.error(f"User not found for email: {email}")
                return None
            user.is_email_verified = True
            user.clear_email_verification_token()
            await db.commit()
            logger.info(f"Email verified for user: {email}")
            return user
        except JWTError as e:
            logger.error(f"Invalid or expired email verification token: {e}")
            return None
        except Exception as e:
            logger.error(f"Error verifying email: {e}")
            return None 