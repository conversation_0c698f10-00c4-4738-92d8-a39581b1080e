"""
Security utils for handling security-related utilities
"""
import secrets
import string
from typing import Optional
from datetime import datetime, timezone, timedelta
import jwt

from app.core.config.settings import settings
from app.core.security.exceptions import TokenError, TokenExpiredError

def generate_random_string(length: int = 32) -> str:
    """
    Generate a random string
    
    Args:
        length: Length of the string
        
    Returns:
        Random string
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_token(
    data: dict,
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Generate a JWT token
    
    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time delta
        
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    return jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )

def verify_token(token: str) -> dict:
    """
    Verify a JWT token
    
    Args:
        token: Token to verify
        
    Returns:
        Decoded token data
        
    Raises:
        TokenError: If the token is invalid
        TokenExpiredError: If the token has expired
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise TokenExpiredError()
    except jwt.JWTError:
        raise TokenError()

def get_token_expiry(token: str) -> datetime:
    """
    Get token expiry time
    
    Args:
        token: Token to check
        
    Returns:
        Token expiry time
        
    Raises:
        TokenError: If the token is invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM],
            options={"verify_exp": False}
        )
        return datetime.fromtimestamp(payload["exp"], timezone.utc)
    except jwt.JWTError:
        raise TokenError()

def is_token_expired(token: str) -> bool:
    """
    Check if a token is expired
    
    Args:
        token: Token to check
        
    Returns:
        True if the token is expired, False otherwise
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        exp = datetime.fromtimestamp(payload["exp"], timezone.utc)
        return exp < datetime.now(timezone.utc)
    except jwt.JWTError:
        return True

def get_token_type(token: str) -> str:
    """
    Get token type
    
    Args:
        token: Token to check
        
    Returns:
        Token type
        
    Raises:
        TokenError: If the token is invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM],
            options={"verify_exp": False}
        )
        return payload.get("type", "unknown")
    except jwt.JWTError:
        raise TokenError()

def get_token_jti(token: str) -> str:
    """
    Get token JWT ID
    
    Args:
        token: Token to check
        
    Returns:
        Token JWT ID
        
    Raises:
        TokenError: If the token is invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM],
            options={"verify_exp": False}
        )
        return payload.get("jti", "")
    except jwt.JWTError:
        raise TokenError()

def get_token_subject(token: str) -> str:
    """
    Get token subject
    
    Args:
        token: Token to check
        
    Returns:
        Token subject
        
    Raises:
        TokenError: If the token is invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM],
            options={"verify_exp": False}
        )
        return payload.get("sub", "")
    except jwt.JWTError:
        raise TokenError() 