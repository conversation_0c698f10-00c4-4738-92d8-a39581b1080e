from enum import Enum
from typing import Any, List, Optional, Dict, Union
from pydantic import BaseModel, Field


# ========================
# ENUMS (UPPERCASE FORMAT)
# ========================
class UserType(str, Enum):
    ADMIN = "ADMIN"
    NORMAL = "NORMAL"
    GUEST = "GUEST"
    ULTIMATE = "ULTIMATE"
    DIVINE = "DIVINE"


# ========================
# MESSAGE STRUCTURE
# ========================
class Message(BaseModel):
    title: Optional[str] = Field(default="", example="Data Retrieved Successfully")
    description: str = Field(..., example="Fetched data successfully.")


# ========================
# PAGINATION DETAILS
# ========================
class Pagination(BaseModel):
    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int


# ========================
# BASE RESPONSE STRUCTURE
# ========================
class ApiResponse(BaseModel):
    success: bool = Field(..., example=True)
    message: Message
    data: Union[Dict[str, Any], List[Any], str, int, float, bool] = Field(default_factory=dict)
    error_code: Optional[int] = Field(default=None, example=1000)


# ========================
# PAGINATED RESPONSE WRAPPER (IF NEEDED)
# ========================
class PaginatedData(BaseModel):
    details: List[Dict[str, Any]]
    pagination: Pagination


# ========================
# FACTORY FUNCTIONS
# ========================
def success_response(
    details: Union[dict, list, str, int, float, bool],
    title: str = "Success",
    description: str = "Request completed successfully"
) -> ApiResponse:
    return ApiResponse(
        success=True,
        message=Message(title=title, description=description),
        data={"details": details}
    )


def paginated_response(
    details: list,
    current_page: int,
    total_pages: int,
    items_per_page: int,
    total_items: int,
    title: str = "Success",
    description: str = "Paginated data fetched"
) -> ApiResponse:
    return ApiResponse(
        success=True,
        message=Message(title=title, description=description),
        data={
            "details": details,
            "pagination": Pagination(
                current_page=current_page,
                total_pages=total_pages,
                items_per_page=items_per_page,
                total_items=total_items
            )
        }
    )


def error_response(
    error_code: int,
    title: str,
    description: str,
    http_success: bool = False
) -> ApiResponse:
    return ApiResponse(
        success=http_success,
        message=Message(title=title, description=description),
        data={},
        error_code=error_code
    )
