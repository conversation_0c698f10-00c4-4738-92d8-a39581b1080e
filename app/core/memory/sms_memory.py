"""
Enhanced SMS Memory Management System
Redis-based conversation memory with 6+ month persistence and intelligent caching
"""

import json
import redis
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from sqlalchemy.orm import selectinload

from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.core.database.connection import get_db
from app.core.config.settings import settings

from app.core.logging import logger


class SMSMemoryManager:
    """Enhanced Redis-based memory management for SMS conversations"""
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or settings.REDIS_URL
        self.redis_client = None
        self.connect()
        
        # Key patterns
        self.LEAD_CONTEXT_KEY = "sms_lead_context:{phone}"
        self.CONVERSATION_KEY = "sms_conversation:{phone}"
        self.LEAD_SUMMARY_KEY = "sms_lead_summary:{lead_id}"
        self.ENGAGEMENT_KEY = "sms_engagement:{phone}"
        self.QUALIFICATION_KEY = "sms_qualification:{phone}"


        # TTL values (6+ months persistence)
        self.LEAD_CONTEXT_TTL = 15552000  # 6 months
        self.CONVERSATION_TTL = 15552000  # 6 months
        self.SUMMARY_TTL = 31104000      # 12 months
        self.ENGAGEMENT_TTL = 2592000    # 1 month
        self.QUALIFICATION_TTL = 15552000 # 6 months

    
    def connect(self):
        """Connect to Redis with enhanced configuration"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
                max_connections=20
            )
            self.redis_client.ping()
            logger.info("Enhanced SMS Memory Manager connected to Redis")
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            self.redis_client = None
    
    def get_lead_context(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive lead context from Redis"""
        if not self.redis_client:
            return None
        
        try:
            key = self.LEAD_CONTEXT_KEY.format(phone=phone_number)
            data = self.redis_client.get(key)
            if data:
                context = json.loads(data)
                # Refresh TTL on access
                self.redis_client.expire(key, self.LEAD_CONTEXT_TTL)
                return context
        except Exception as e:
            logger.error(f"Error getting lead context: {e}")
        
        return None
    
    def store_lead_context(self, phone_number: str, context: Dict[str, Any]) -> bool:
        """Store lead context with enhanced metadata"""
        if not self.redis_client:
            return False
        
        try:
            key = self.LEAD_CONTEXT_KEY.format(phone=phone_number)
            
            # Add metadata
            context.update({
                "last_updated": datetime.now().isoformat(),
                "access_count": context.get("access_count", 0) + 1,
                "phone_number": phone_number
            })
            
            # Store with TTL
            self.redis_client.setex(key, self.LEAD_CONTEXT_TTL, json.dumps(context))
            
            # Also store in qualification tracking
            self._update_qualification_tracking(phone_number, context)
            
            logger.info(f"Stored lead context for {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing lead context: {e}")
            return False
    
    def get_conversation_history(self, phone_number: str, limit: int = 25) -> List[Dict[str, Any]]:
        """Get conversation history with intelligent filtering"""
        if not self.redis_client:
            return []
        
        try:
            key = self.CONVERSATION_KEY.format(phone=phone_number)
            messages = self.redis_client.lrange(key, 0, limit - 1)
            
            # Parse and enrich messages
            conversation = []
            for msg in messages:
                try:
                    parsed_msg = json.loads(msg)
                    # Add message metadata
                    parsed_msg["age_hours"] = self._calculate_message_age(parsed_msg.get("timestamp"))
                    conversation.append(parsed_msg)
                except json.JSONDecodeError:
                    continue
            
            # Refresh TTL on access
            if conversation:
                self.redis_client.expire(key, self.CONVERSATION_TTL)
            
            return conversation
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def add_to_conversation_history(self, phone_number: str, message: Dict[str, Any]) -> bool:
        """Add message to conversation history with intelligent management"""
        if not self.redis_client:
            return False
        
        try:
            key = self.CONVERSATION_KEY.format(phone=phone_number)
            
            # Enrich message with metadata
            enriched_message = {
                **message,
                "message_id": self._generate_message_id(phone_number, message),
                "timestamp": message.get("timestamp", datetime.now().isoformat()),
                "phone_number": phone_number
            }
            
            # Add to list (newest first)
            self.redis_client.lpush(key, json.dumps(enriched_message))
            
            # Maintain conversation size (keep last 50 messages)
            self.redis_client.ltrim(key, 0, 49)
            
            # Set TTL
            self.redis_client.expire(key, self.CONVERSATION_TTL)
            
            # Update engagement tracking
            self._update_engagement_tracking(phone_number, message)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding to conversation history: {e}")
            return False
    
    def get_lead_summary(self, lead_id: str) -> Optional[Dict[str, Any]]:
        """Get AI-generated lead summary"""
        if not self.redis_client:
            return None
        
        try:
            key = self.LEAD_SUMMARY_KEY.format(lead_id=lead_id)
            data = self.redis_client.get(key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting lead summary: {e}")
        
        return None
    
    def store_lead_summary(self, lead_id: str, summary: Dict[str, Any]) -> bool:
        """Store AI-generated lead summary"""
        if not self.redis_client:
            return False
        
        try:
            key = self.LEAD_SUMMARY_KEY.format(lead_id=lead_id)
            summary.update({
                "generated_at": datetime.now().isoformat(),
                "lead_id": lead_id
            })
            
            self.redis_client.setex(key, self.SUMMARY_TTL, json.dumps(summary))
            return True
            
        except Exception as e:
            logger.error(f"Error storing lead summary: {e}")
            return False
    
    def get_engagement_metrics(self, phone_number: str) -> Dict[str, Any]:
        """Get engagement metrics for lead"""
        if not self.redis_client:
            return {}
        
        try:
            key = self.ENGAGEMENT_KEY.format(phone=phone_number)
            data = self.redis_client.get(key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting engagement metrics: {e}")
        
        return {}
    
    def get_qualification_status(self, phone_number: str) -> Dict[str, Any]:
        """Get qualification status and score"""
        if not self.redis_client:
            return {}
        
        try:
            key = self.QUALIFICATION_KEY.format(phone=phone_number)
            data = self.redis_client.get(key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting qualification status: {e}")
        
        return {}
    
    async def sync_with_database(self, phone_number: str) -> bool:
        """Sync Redis data with database for persistence"""
        try:
            # Get data from Redis
            lead_context = self.get_lead_context(phone_number)
            conversation_history = self.get_conversation_history(phone_number, 100)
            
            if not lead_context:
                return False
            
            async for session in get_db():
                # Find or create lead
                lead = await self._find_or_create_lead(session, lead_context)
                
                # Sync conversation messages
                await self._sync_conversation_messages(session, lead.id, conversation_history)
                
                await session.commit()
                
                # Update lead context with database ID
                if not lead_context.get("lead_id"):
                    lead_context["lead_id"] = str(lead.id)
                    self.store_lead_context(phone_number, lead_context)
                
                logger.info(f"Synced data for {phone_number} with database")
                return True
                
        except Exception as e:
            logger.error(f"Error syncing with database: {e}")
            return False


    
    def _generate_message_id(self, phone_number: str, message: Dict[str, Any]) -> str:
        """Generate unique message ID"""
        content = f"{phone_number}_{message.get('message', '')}_{message.get('timestamp', '')}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def _calculate_message_age(self, timestamp_str: Optional[str]) -> float:
        """Calculate message age in hours"""
        if not timestamp_str:
            return 0.0
        
        try:
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            age = datetime.now() - timestamp.replace(tzinfo=None)
            return age.total_seconds() / 3600
        except:
            return 0.0
    
    def _update_engagement_tracking(self, phone_number: str, message: Dict[str, Any]):
        """Update engagement metrics"""
        if not self.redis_client:
            return
        
        try:
            key = self.ENGAGEMENT_KEY.format(phone=phone_number)
            
            # Get existing metrics
            existing_data = self.redis_client.get(key)
            metrics = json.loads(existing_data) if existing_data else {
                "total_messages": 0,
                "last_activity": None,
                "engagement_score": 0.0,
                "response_times": [],
                "message_lengths": []
            }
            
            # Update metrics
            metrics["total_messages"] += 1
            metrics["last_activity"] = datetime.now().isoformat()
            metrics["message_lengths"].append(len(message.get("message", "")))
            
            # Keep only recent data
            metrics["message_lengths"] = metrics["message_lengths"][-20:]
            metrics["response_times"] = metrics["response_times"][-20:]
            
            # Calculate engagement score
            metrics["engagement_score"] = self._calculate_engagement_score(metrics)
            
            # Store updated metrics
            self.redis_client.setex(key, self.ENGAGEMENT_TTL, json.dumps(metrics))
            
        except Exception as e:
            logger.error(f"Error updating engagement tracking: {e}")
    
    def _update_qualification_tracking(self, phone_number: str, context: Dict[str, Any]):
        """Update qualification tracking"""
        if not self.redis_client:
            return
        
        try:
            key = self.QUALIFICATION_KEY.format(phone=phone_number)
            
            qualification_data = {
                "score": context.get("qualification_score", 0.0),
                "status": self._determine_qualification_status(context),
                "last_updated": datetime.now().isoformat(),
                "criteria_met": self._check_qualification_criteria(context)
            }
            
            self.redis_client.setex(key, self.QUALIFICATION_TTL, json.dumps(qualification_data))
            
        except Exception as e:
            logger.error(f"Error updating qualification tracking: {e}")
    
    def _calculate_engagement_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate engagement score based on metrics"""
        score = 0.0
        
        # Message frequency
        if metrics["total_messages"] > 10:
            score += 0.3
        elif metrics["total_messages"] > 5:
            score += 0.2
        elif metrics["total_messages"] > 2:
            score += 0.1
        
        # Message length (indicates thoughtfulness)
        avg_length = sum(metrics["message_lengths"]) / len(metrics["message_lengths"]) if metrics["message_lengths"] else 0
        if avg_length > 50:
            score += 0.3
        elif avg_length > 20:
            score += 0.2
        
        # Recent activity
        if metrics["last_activity"]:
            try:
                last_activity = datetime.fromisoformat(metrics["last_activity"])
                hours_since = (datetime.now() - last_activity).total_seconds() / 3600
                if hours_since < 24:
                    score += 0.4
                elif hours_since < 72:
                    score += 0.2
            except:
                pass
        
        return min(score, 1.0)
    
    def _determine_qualification_status(self, context: Dict[str, Any]) -> str:
        """Determine qualification status"""
        score = context.get("qualification_score", 0.0)
        
        if score >= 0.8:
            return "highly_qualified"
        elif score >= 0.6:
            return "qualified"
        elif score >= 0.4:
            return "partially_qualified"
        else:
            return "unqualified"
    
    def _check_qualification_criteria(self, context: Dict[str, Any]) -> Dict[str, bool]:
        """Check which qualification criteria are met"""
        return {
            "has_name": bool(context.get("name")),
            "has_budget": bool(context.get("budget")),
            "has_location": bool(context.get("city")),
            "has_timeline": bool(context.get("timeline")),
            "has_experience": bool(context.get("experience")),
            "shows_interest": context.get("engagement_level") in ["medium", "high"]
        }
    
    async def _find_or_create_lead(self, session: AsyncSession, context: Dict[str, Any]) -> Lead:
        """Find existing lead or create new one"""
        # Try to find existing lead
        result = await session.execute(
            select(Lead).where(
                and_(
                    Lead.phone == context["phone_number"],
                    Lead.is_active == True,
                    Lead.is_deleted == False
                )
            )
        )
        existing_lead = result.scalar_one_or_none()
        
        if existing_lead:
            return existing_lead
        
        # Create new lead
        name_parts = context.get("name", "").split(" ", 1) if context.get("name") else ["Unknown"]
        
        new_lead = Lead(
            first_name=name_parts[0],
            last_name=name_parts[1] if len(name_parts) > 1 else None,
            phone=context["phone_number"],
            location=context.get("city"),
            budget_preference=context.get("budget"),
            lead_source_id=None,  # Will be set by business logic
            lead_status_id=None,  # Will be set by business logic
        )
        
        session.add(new_lead)
        await session.flush()
        return new_lead
    
    async def _sync_conversation_messages(self, session: AsyncSession, lead_id: str, conversation: List[Dict[str, Any]]):
        """Sync conversation messages with database"""
        # Get existing message IDs to avoid duplicates
        result = await session.execute(
            select(ConversationMessage.message).where(
                ConversationMessage.lead_id == lead_id
            ).order_by(desc(ConversationMessage.created_at)).limit(50)
        )
        existing_messages = {row[0] for row in result.fetchall()}
        
        # Add new messages
        for msg in reversed(conversation):  # Oldest first for proper ordering
            if msg.get("message") not in existing_messages:
                conversation_message = ConversationMessage(
                    lead_id=lead_id,
                    sender=msg.get("sender", "lead"),
                    message=msg.get("message", "")
                )
                session.add(conversation_message)


# Global memory manager instance
_memory_manager: Optional[SMSMemoryManager] = None


def get_sms_memory_manager() -> SMSMemoryManager:
    """Get or create SMS Memory Manager instance"""
    global _memory_manager
    
    if _memory_manager is None:
        _memory_manager = SMSMemoryManager()
    
    return _memory_manager
