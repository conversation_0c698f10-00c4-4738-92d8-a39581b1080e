"""
Security configuration for the application
"""
from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from app.core.config.settings import settings

class SecurityConfig(BaseModel):
    """
    Security configuration for the application
    """
    # JWT settings
    secret_key: str = Field(..., description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(default=30, description="JWT access token expire minutes")
    jwt_refresh_token_expire_days: int = Field(default=7, description="JWT refresh token expire days")
    
    # Password settings
    password_min_length: int = Field(default=8, description="Minimum password length")
    password_max_length: int = Field(default=100, description="Maximum password length")
    password_require_uppercase: bool = Field(default=True, description="Require uppercase in password")
    password_require_lowercase: bool = Field(default=True, description="Require lowercase in password")
    password_require_numbers: bool = Field(default=True, description="Require numbers in password")
    password_require_special_chars: bool = Field(default=True, description="Require special characters in password")
    
    # Security headers
    security_headers: Dict[str, str] = Field(
        default={
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        },
        description="Security headers to add to responses"
    )
    
    # CORS settings
    cors_origins: List[str] = Field(default=["*"], description="CORS allowed origins")
    cors_origin_regex: Optional[str] = Field(default=None, description="CORS allowed origin regex")
    cors_methods: List[str] = Field(default=["*"], description="CORS allowed methods")
    cors_headers: List[str] = Field(default=["*"], description="CORS allowed headers")
    
    class Config:
        """Pydantic config"""
        arbitrary_types_allowed = True

# Create security settings instance
security_settings = SecurityConfig(
    secret_key=settings.JWT_SECRET_KEY,
    jwt_algorithm=settings.JWT_ALGORITHM,
    jwt_access_token_expire_minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    jwt_refresh_token_expire_days=settings.REFRESH_TOKEN_EXPIRE_DAYS,
    password_min_length=8,
    password_max_length=100,
    password_require_uppercase=True,
    password_require_lowercase=True,
    password_require_numbers=True,
    password_require_special_chars=True,
    cors_origins=settings.CORS_ORIGINS,
    cors_methods=["*"],
    cors_headers=["*"]
) 