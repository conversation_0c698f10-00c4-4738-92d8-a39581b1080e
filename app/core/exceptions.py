"""
Core exceptions module for handling application-wide exceptions
"""
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Request
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import ValidationError
from typing import Any
from app.core.app_rules import error_response

class SecurityException(HTTPException):
    """Security related exceptions"""
    def __init__(self, detail: str, status_code: int = 403):
        super().__init__(status_code=status_code, detail=detail)

class AuthenticationError(HTTPException):
    """Authentication related exceptions"""
    def __init__(self, detail: str, status_code: int = 401):
        super().__init__(status_code=status_code, detail=detail)

class AuthorizationError(HTTPException):
    """Authorization related exceptions"""
    def __init__(self, detail: str, status_code: int = 403):
        super().__init__(status_code=status_code, detail=detail)

class TokenError(HTTPException):
    """Token related exceptions"""
    def __init__(self, detail: str, status_code: int = 401):
        super().__init__(status_code=status_code, detail=detail)

class SessionError(HTTPException):
    """Session related exceptions"""
    def __init__(self, detail: str, status_code: int = 401):
        super().__init__(status_code=status_code, detail=detail)

class RefreshTokenError(HTTPException):
    """Refresh token related exceptions"""
    def __init__(self, detail: str, status_code: int = 401):
        super().__init__(status_code=status_code, detail=detail)

class DuplicateEntryError(HTTPException):
    """Duplicate entry exceptions"""
    def __init__(self, title: str, description: str, status_code: int = 409):
        super().__init__(
            status_code=status_code,
            detail={"title": title, "description": description}
        )

class CustomValidationError(HTTPException):
    """Validation related exceptions"""
    def __init__(self, title: str, description: str, status_code: int = 400):
        super().__init__(
            status_code=status_code,
            detail={"title": title, "description": description}
        )

class DatabaseError(HTTPException):
    """Database error"""
    def __init__(self, detail: str = "Database error occurred"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class NotFoundError(HTTPException):
    """Not found error"""
    def __init__(self, detail: str = "Resource not found"):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ConflictError(HTTPException):
    """Conflict error"""
    def __init__(self, detail: str = "Resource already exists"):
        super().__init__(status_code=status.HTTP_409_CONFLICT, detail=detail)

class BadRequestError(HTTPException):
    """Bad request error"""
    def __init__(self, detail: str = "Bad request"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class ExceptionHandler:
    """Exception handler for managing application exceptions"""
    
    @staticmethod
    async def manage_exception(error_type: str, language: str = "en", details: dict[str, Any] = None) -> JSONResponse:
        """
        Manage exceptions and return appropriate responses.
        Args:
            error_type (str): The type of error.
            language (str, optional): Language for the error message. Defaults to "en".
            details (dict[str, Any], optional): Additional error details. Defaults to None.
        Returns:
            JSONResponse: A JSON response with error details in StandardResponse format.
        """
        error_messages: dict[str, dict[str, str]] = {
            "email_already_exists": {
                "title": "Email already registered",
                "description": "This email address is already in use"
            },
            "mobile_already_exists": {
                "title": "Mobile number already registered", 
                "description": "This mobile number is already in use"
            },
            "user_creation_failed": {
                "title": "User creation failed",
                "description": "Failed to create user account"
            }
        }
        error_info = error_messages.get(error_type, {
            "title": "Error",
            "description": "An error occurred"
        })
        return JSONResponse(
            status_code=409 if "already_exists" in error_type else 500,
            content={
                "success": False,
                "message": error_info,
                "data": {},
                "error_code": 409 if "already_exists" in error_type else 500
            }
        )

async def pydantic_validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """
    Global handler for Pydantic validation errors.
    Returns standardized error response format (StandardResponse style).
    Args:
        request (Request): The incoming request object.
        exc (ValidationError): The Pydantic validation error exception.
    Returns:
        JSONResponse: A JSON response with error details in StandardResponse format.
    """
    errors: list[str] = []
    for error in exc.errors():
        field = " -> ".join(str(x) for x in error["loc"])
        message = error["msg"]
        errors.append(f"{field}: {message}")

    error_message: str = "; ".join(errors)
    
    return JSONResponse(
        status_code=422,
        content=error_response(
            error_code=422,
            title="Validation Error",
            description=error_message
        ).model_dump()
    )

# Dictionary of error handlers
exception_handlers = {
    ValidationError: pydantic_validation_exception_handler
} 