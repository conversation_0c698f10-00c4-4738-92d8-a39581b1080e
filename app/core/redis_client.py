"""
Redis Client Configuration
Provides Redis client for caching and session management
"""

import os
import redis
from typing import Optional
import structlog

from app.core.logging import logger

# Redis configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")

# Global Redis client instance
_redis_client: Optional[redis.Redis] = None


def get_redis_client() -> redis.Redis:
    """
    Get Redis client instance (singleton pattern)
    
    Returns:
        redis.Redis: Redis client instance
    """
    global _redis_client
    
    if _redis_client is None:
        try:
            if REDIS_URL:
                # Use Redis URL if provided
                _redis_client = redis.from_url(
                    REDIS_URL,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True
                )
            else:
                # Use individual connection parameters
                _redis_client = redis.Redis(
                    host=REDIS_HOST,
                    port=REDIS_PORT,
                    db=REDIS_DB,
                    password=REDIS_PASSWORD,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True
                )
            
            # Test connection
            _redis_client.ping()
            logger.info("Redis client initialized successfully", 
                       host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB)
            
        except redis.ConnectionError as e:
            logger.warning(f"Redis connection failed: {e}. Using mock client for testing.")
            _redis_client = MockRedisClient()
        except Exception as e:
            logger.error(f"Redis initialization error: {e}. Using mock client.")
            _redis_client = MockRedisClient()
    
    return _redis_client


class MockRedisClient:
    """
    Mock Redis client for testing and development when Redis is not available
    """
    
    def __init__(self):
        self._data = {}
        self._ttl = {}
        logger.info("Mock Redis client initialized for testing")
    
    def ping(self):
        """Mock ping method"""
        return True
    
    def get(self, key: str) -> Optional[str]:
        """Mock get method"""
        return self._data.get(key)
    
    def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """Mock set method"""
        self._data[key] = value
        if ex:
            self._ttl[key] = ex
        return True
    
    def delete(self, *keys) -> int:
        """Mock delete method"""
        deleted = 0
        for key in keys:
            if key in self._data:
                del self._data[key]
                if key in self._ttl:
                    del self._ttl[key]
                deleted += 1
        return deleted
    
    def exists(self, key: str) -> bool:
        """Mock exists method"""
        return key in self._data
    
    def expire(self, key: str, time: int) -> bool:
        """Mock expire method"""
        if key in self._data:
            self._ttl[key] = time
            return True
        return False
    
    def ttl(self, key: str) -> int:
        """Mock ttl method"""
        return self._ttl.get(key, -1)
    
    def flushdb(self) -> bool:
        """Mock flushdb method"""
        self._data.clear()
        self._ttl.clear()
        return True
    
    def keys(self, pattern: str = "*") -> list:
        """Mock keys method"""
        if pattern == "*":
            return list(self._data.keys())
        # Simple pattern matching for testing
        import fnmatch
        return [key for key in self._data.keys() if fnmatch.fnmatch(key, pattern)]
    
    def hget(self, name: str, key: str) -> Optional[str]:
        """Mock hget method"""
        hash_data = self._data.get(name, {})
        if isinstance(hash_data, dict):
            return hash_data.get(key)
        return None
    
    def hset(self, name: str, key: str, value: str) -> int:
        """Mock hset method"""
        if name not in self._data:
            self._data[name] = {}
        if not isinstance(self._data[name], dict):
            self._data[name] = {}
        
        is_new = key not in self._data[name]
        self._data[name][key] = value
        return 1 if is_new else 0
    
    def hgetall(self, name: str) -> dict:
        """Mock hgetall method"""
        hash_data = self._data.get(name, {})
        return hash_data if isinstance(hash_data, dict) else {}
    
    def hdel(self, name: str, *keys) -> int:
        """Mock hdel method"""
        if name not in self._data or not isinstance(self._data[name], dict):
            return 0
        
        deleted = 0
        for key in keys:
            if key in self._data[name]:
                del self._data[name][key]
                deleted += 1
        
        # Remove hash if empty
        if not self._data[name]:
            del self._data[name]
        
        return deleted


def reset_redis_client():
    """Reset Redis client (useful for testing)"""
    global _redis_client
    _redis_client = None


# Export main functions
__all__ = [
    "get_redis_client",
    "reset_redis_client",
    "MockRedisClient"
]
