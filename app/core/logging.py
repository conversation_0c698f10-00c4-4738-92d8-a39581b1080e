"""
Standardized Logging configuration for the GrowthHive API.
This is the single source of truth for all logging in the application.
"""
import logging
from loguru import logger as loguru_logger
import os
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Any, Dict, Optional
import json
from datetime import datetime, timezone
import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config.settings import settings

# Create logs directory if it doesn't exist
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "environment": getattr(settings, 'ENVIRONMENT', 'development'),
            "path_name": f"{record.module}:{record.funcName}",
            "request_id": getattr(record, 'request_id', 'N/A')
        }

        # Add extra fields if they exist
        if hasattr(record, "context"):
            log_data["context"] = record.context

        # Add any additional extra fields
        if hasattr(record, "extra"):
            log_data.update(record.extra)

        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }

        return json.dumps(log_data)

class RequestContextFilter(logging.Filter):
    """Add request ID to log records"""

    def filter(self, record: logging.LogRecord) -> bool:
        record.request_id = getattr(record, 'request_id', 'N/A')
        return True

def setup_logger(name: str = "growthhive"):
    log_dir = 'src/logs'
    # logger = logging.getLogger(name)
    # logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    os.makedirs(log_dir, exist_ok=True)
    loguru_logger.remove() # Remove default handler

    # Define a more detailed format
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )

    # Configure console logging
    loguru_logger.add(
        sys.stdout,
        level="INFO",
        format=log_format,
        colorize=True # Enable colorization for console
    )

    # Configure file logging
    loguru_logger.add(
        "src/logs/{time:YYYY-MM-DD}.log",
        rotation="50 MB", # Reduced rotation size for more frequent rotation if needed
        retention="10 days", # Keep logs for 10 days
        level="DEBUG",
        format=log_format,
        enqueue=True, # Make logging asynchronous for performance
        backtrace=True, # Include traceback in logs
        diagnose=True # Add exception diagnosis information
    )

    return loguru_logger
def setup_logging(name: str = "growthhive") -> logging.Logger:
    """
    Setup comprehensive logging configuration with multiple handlers.
    This is the standardized logging setup for the entire application.
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))

    # Prevent duplicate handlers
    if logger.handlers:
        return logger

    # Create formatters
    json_formatter = JSONFormatter()
    console_formatter = logging.Formatter(settings.LOG_FORMAT)

    # Console Handler - for development and debugging
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(RequestContextFilter())

    # Main Application File Handler (with rotation)
    file_handler = RotatingFileHandler(
        filename=log_dir / "app.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(json_formatter)
    file_handler.addFilter(RequestContextFilter())

    # Error File Handler (with rotation) - only errors and above
    error_file_handler = RotatingFileHandler(
        filename=log_dir / "error.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_file_handler.setLevel(logging.ERROR)
    error_file_handler.setFormatter(json_formatter)
    error_file_handler.addFilter(RequestContextFilter())

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.addHandler(error_file_handler)

    return logger

# Create the standard logger instance
logger = setup_logger()



class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for monitoring request performance"""
    async def dispatch(self, request: Request, call_next: Response) -> Response:
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # Add process time to response headers
        response.headers["X-Process-Time"] = str(process_time)

        # Log if performance threshold is exceeded (default 1 second if not set)
        threshold_seconds = getattr(settings, 'PERFORMANCE_THRESHOLD_MS', 1000) / 1000
        if process_time > threshold_seconds:
            logger.warning(
                f"Slow request detected: {request.method} {request.url.path}",
                extra={
                    "context": {
                        "method": request.method,
                        "path": request.url.path,
                        "duration": process_time,
                        "threshold": threshold_seconds,
                        "client_host": request.client.host if request.client else None
                    }
                }
            )

        return response

def log_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """Log an error with optional context.
    
    Args:
        error: The exception to log
        context: Optional context dictionary
    """
    error_msg = f"Error: {str(error)}"
    if context:
        error_msg += f" | Context: {context}"
    logger.error(error_msg, exc_info=True, extra={"context": context})

def log_api_request(
    method: str,
    path: str,
    status_code: int,
    duration: float,
    client_host: Optional[str] = None,
    user_agent: Optional[str] = None,
    request_id: Optional[str] = None
) -> None:
    """Log an API request with enhanced context.

    Args:
        method: HTTP method
        path: Request path
        status_code: Response status code
        duration: Request duration in seconds
        client_host: Client IP address
        user_agent: User agent string
        request_id: Request ID for tracing
    """
    logger.info(
        f"API Request: {method} {path} | Status: {status_code} | Duration: {duration:.2f}s",
        extra={
            "context": {
                "method": method,
                "path": path,
                "status_code": status_code,
                "duration": duration,
                "client_host": client_host,
                "user_agent": user_agent,
                "environment": getattr(settings, 'ENVIRONMENT', 'development')
            },
            "request_id": request_id or "N/A"
        }
    )

def log_performance_metric(
    metric_name: str,
    value: float,
    threshold: Optional[float] = None,
    context: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> None:
    """Log a performance metric.

    Args:
        metric_name: Name of the metric
        value: Metric value
        threshold: Optional threshold value
        context: Optional context dictionary
        request_id: Request ID for tracing
    """
    log_context = {
        "metric": metric_name,
        "value": value,
        "threshold": threshold
    }
    if context:
        log_context.update(context)

    log_data = {
        "context": log_context,
        "request_id": request_id or "N/A"
    }

    if threshold and value > threshold:
        logger.warning(
            f"Performance threshold exceeded: {metric_name} ({value} > {threshold})",
            extra=log_data
        )
    else:
        logger.info(
            f"Performance metric: {metric_name} = {value}",
            extra=log_data
        )

# Convenience functions for common logging patterns
def log_auth_attempt(email: str, success: bool, request_id: Optional[str] = None) -> None:
    """Log authentication attempt"""
    logger.info(
        f"Authentication attempt: {email} - {'SUCCESS' if success else 'FAILED'}",
        extra={
            "context": {
                "email": email,
                "success": success,
                "event_type": "auth_attempt"
            },
            "request_id": request_id or "N/A"
        }
    )

def log_database_operation(
    operation: str,
    table: str,
    success: bool,
    duration: Optional[float] = None,
    request_id: Optional[str] = None
) -> None:
    """Log database operation"""
    logger.info(
        f"Database {operation} on {table} - {'SUCCESS' if success else 'FAILED'}",
        extra={
            "context": {
                "operation": operation,
                "table": table,
                "success": success,
                "duration": duration,
                "event_type": "db_operation"
            },
            "request_id": request_id or "N/A"
        }
    )