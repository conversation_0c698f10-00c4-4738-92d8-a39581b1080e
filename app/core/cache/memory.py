"""
In-memory cache implementation
"""
from typing import Dict, Any, Optional
import threading

# Cache store
_cache_store: Dict[str, Any] = {}
_cache_lock = threading.Lock()

def get_cache(key: str) -> Optional[Any]:
    """
    Get a value from the cache
    
    Args:
        key: Cache key
        
    Returns:
        Cached value or None if not found
    """
    with _cache_lock:
        return _cache_store.get(key)

def set_cache(key: str, value: Any, ttl: Optional[int] = None) -> None:
    """
    Set a value in the cache
    
    Args:
        key: Cache key
        value: Value to cache
        ttl: Time to live in seconds
    """
    with _cache_lock:
        _cache_store[key] = value

def delete_cache(key: str) -> None:
    """
    Delete a value from the cache
    
    Args:
        key: Cache key
    """
    with _cache_lock:
        _cache_store.pop(key, None)

def clear_cache() -> None:
    """
    Clear all cached values
    """
    with _cache_lock:
        _cache_store.clear() 