"""
Coochie Hydrogreen Workflow Agent
Implements the specific conversation workflow for Coochie Hydrogreen leads
with exact sentences from the workflow document and NO follow-up messages.
"""

import os
import json
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from datetime import datetime, timezone
import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config.settings import settings
from app.core.database.connection import get_db
from app.services.openai_service import OpenAIService
from app.services.coochie_conversation_service import coochie_conversation_service
from app.services.objection_handling_service import objection_handling_service
from app.services.coochie_meeting_service import coochie_meeting_service
from app.models.lead import Lead
from app.models.conversation_session import ConversationSession
from app.core.redis_client import get_redis_client

from app.core.logging import logger


class CoochieWorkflowStage(Enum):
    """Workflow stages for Coochie Hydrogreen conversation"""
    INTRODUCTION = "introduction"
    WORK_BACKGROUND = "work_background"
    MOTIVATION = "motivation"
    BUDGET = "budget"
    SCHEDULING = "scheduling"
    CONFIRMATION = "confirmation"
    OBJECTION_HANDLING = "objection_handling"
    COMPLETED = "completed"


class WorkBackground(Enum):
    """Work background categories"""
    CORPORATE = "corporate"
    TRADES = "trades"
    UNCLEAR = "unclear"


class Motivation(Enum):
    """Motivation categories from workflow"""
    BE_YOUR_OWN_BOSS = "be_your_own_boss"
    SICK_OF_CORPORATE = "sick_of_corporate"
    FLEXIBLE_LIFESTYLE = "flexible_lifestyle"
    EASE_INTO_RETIREMENT = "ease_into_retirement"
    LEGACY_FOR_CHILDREN = "legacy_for_children"
    REDUNDANCY = "redundancy"
    DIVERSIFICATION = "diversification"
    RELOCATION = "relocation"


class CoochieWorkflowAgent:
    """
    Agent that handles the Coochie Hydrogreen conversation workflow
    with exact sentences from the workflow document.
    """
    
    def __init__(self):
        self.openai_service = OpenAIService()
        self.workflow_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"
        self.default_timezone = os.getenv("DEFAULT_TIMEZONE", "Asia/Kolkata")
        self.redis_client = get_redis_client()
        
        # Exact sentences from workflow
        self.workflow_responses = {
            "introduction": "Hi {name}, My name is Andy, I'm reaching out in regards to the Coochie Hydrogreen franchise you enquired about. Do you have a minute for a quick SMS chat? Thanks!",
            "no_response_followup": "Apologies, I don't want to be a nuisance but do you have a minute to chat today or tomorrow in regards to the Coochie Hydrogreen franchise?",
            "no_time_response": "No problem, what's the best time to connect today or possibly tomorrow?",
            "no_time_with_proposed": "No problem, I'll lock this in my calendar to touch base at {proposed_time}. Talk soon!",
            "work_background_question": "Great, thanks for your enquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?",
            "motivation_corporate": "what made you enquire about this franchise opportunity given you come from a different background?",
            "motivation_trades": "what made you enquire about this franchise opportunity?",
            "trades_clarification": "Do you work for yourself (sole trader) or working as an employee?",
            "budget_question": "You mentioned that you have access to a {budget} budget to invest in the business. Is that true?",
            "budget_no_response": "How much funds do you have access to invest?",
            "budget_still_no_amount": "We'll walk you through the financials and real examples from our 70 franchise partners, so rest assured you'll have a clear picture of the ROI. I just need to confirm you have funds to get started—that's why I asked.",
            "budget_confirmed": "Thanks for confirming. Total Franchise Fee for Coochie Hydrogreen is $120K(ex. GST) however we have an option to pay 50% in advance and 50% after 12 months. Also, there is a finance option for you. How does that sound?",
            "scheduling_good_response": "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at {time} on {day}?",
            "confirmation": "Thanks for the confirmation. Looking forward to speaking with you.",
            "no_value_objection": "I understand however we are yet to walk you through the business potential. There is a reason we're Australia's largest lawn care company. Let's walk you through all on the call.",
            "not_heard_objection": "Fair enough. Coochie has been operating for over 30 years and is a very prominent brand in the NSW & QLD and now we're looking to grow in other states due to demand.",
            "marketing_objection": "We will provide you lead guarantee during the initial period along with income guarantee in the first year. However the expectation is you are also conducting business development activities in your local area.",
            "experience_objection": "We will provide you comprehensive training for 4 weeks but also you will receive ongoing support. You will also get to spend time with our existing franchise partners. Rest assured, you won't be left alone.",
            "income_objection": "A totally fair question. Some of our franchise partners, who follow our systems and processes very well, earn over $200K net, but as always, your results will depend on your effort and local conditions. We'll go through the 3-year projections together in our meeting so you can see what's possible.",
            "income_guarantee_question": "In the first year, we guarantee $60K net income - that is after all your expenses.",
            "royalty_question": "10% royalty and 3% marketing fund so total is 13% of your gross sales.",
            "objection_followup": "Does that make sense? I'm happy to answer any questions you may have.",
            "silent_check": "Just checking if you're still keen to explore this opportunity? Happy to keep the chat going and set up a quick call to walk you through the details. How does that sound?",
            "not_interested_reason": "Any particular reason you wouldn't like to continue?"
        }
        
    async def process_message(
        self, 
        lead_id: str, 
        phone_number: str, 
        message: str, 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        Process incoming message through the Coochie workflow
        """
        if not self.workflow_enabled:
            return {"response": "Workflow not enabled", "stage": "disabled"}
            
        try:
            # Get or create conversation session
            session = await self._get_or_create_session(db, lead_id, phone_number)
            
            # Get lead information
            lead = await self._get_lead(db, lead_id)
            
            # Determine current stage and process message
            current_stage = CoochieWorkflowStage(session.get("workflow_stage", "introduction"))
            
            # Process message based on current stage
            response_data = await self._process_stage(
                db, session, lead, message, current_stage
            )
            
            # Update session with new stage and context
            await self._update_session(db, session, response_data)
            
            # Log workflow progress (no PII)
            logger.info(
                "Coochie workflow processed",
                stage=current_stage.value,
                next_stage=response_data.get("next_stage"),
                lead_id=lead_id[:8] + "..." if lead_id else None
            )
            
            return response_data
            
        except Exception as e:
            logger.error(f"Error in Coochie workflow: {str(e)}")
            return {
                "response": "I apologize, but I encountered an error. Please try again later.",
                "stage": "error",
                "error": str(e)
            }
    
    async def _get_or_create_session(
        self,
        db: AsyncSession,
        lead_id: str,
        phone_number: str
    ) -> Dict[str, Any]:
        """Get or create conversation session using Redis"""
        session_key = f"coochie_session:{phone_number}"

        try:
            # Try to get existing session from Redis
            session_data = self.redis_client.get(session_key)
            if session_data:
                session = json.loads(session_data)
                logger.info(f"Retrieved existing session for {phone_number}, stage: {session.get('workflow_stage')}")
                return session
        except Exception as e:
            logger.warning(f"Error retrieving session from Redis: {e}")

        # Create new session
        session = {
            "lead_id": lead_id,
            "phone_number": phone_number,
            "workflow_stage": "introduction",
            "context": {},
            "messages_count": 0,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Store in Redis with 24 hour expiry
        try:
            self.redis_client.setex(session_key, 86400, json.dumps(session))
            logger.info(f"Created new session for {phone_number}")
        except Exception as e:
            logger.warning(f"Error storing session in Redis: {e}")

        return session
    
    async def _get_lead(self, db: AsyncSession, lead_id: str) -> Dict[str, Any]:
        """Get lead information"""
        if not lead_id:
            return {
                "id": None,
                "first_name": "there",
                "phone_number": "",
                "budget": None
            }

        try:
            from sqlalchemy import select
            from app.models.lead import Lead

            stmt = select(Lead).where(Lead.id == lead_id)
            result = await db.execute(stmt)
            lead = result.scalar_one_or_none()

            if lead:
                return {
                    "id": str(lead.id),
                    "first_name": lead.first_name or "there",
                    "phone_number": lead.phone or lead.mobile or "",
                    "budget": lead.funds_available or "your available funds"
                }
        except Exception as e:
            logger.error(f"Error getting lead {lead_id}: {e}")

        # Fallback
        return {
            "id": lead_id,
            "first_name": "there",
            "phone_number": "",
            "budget": None
        }
    
    async def _process_stage(
        self,
        db: AsyncSession,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str,
        current_stage: CoochieWorkflowStage
    ) -> Dict[str, Any]:
        """Process message based on current workflow stage"""
        
        if current_stage == CoochieWorkflowStage.INTRODUCTION:
            return await self._handle_introduction(session, lead, message)
        elif current_stage == CoochieWorkflowStage.WORK_BACKGROUND:
            return await self._handle_work_background(session, lead, message)
        elif current_stage == CoochieWorkflowStage.MOTIVATION:
            return await self._handle_motivation(session, lead, message)
        elif current_stage == CoochieWorkflowStage.BUDGET:
            return await self._handle_budget(session, lead, message)
        elif current_stage == CoochieWorkflowStage.SCHEDULING:
            return await self._handle_scheduling(session, lead, message)
        elif current_stage == CoochieWorkflowStage.CONFIRMATION:
            return await self._handle_confirmation(session, lead, message)
        else:
            return await self._handle_objection_or_general(session, lead, message)
    
    async def _handle_introduction(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle introduction stage"""

        # Check if this is the initial contact
        if not session.get("messages_count", 0):
            # Send introduction message using exact workflow text
            response = self.workflow_responses["introduction"].format(
                name=lead.get("first_name", "there")
            )
            return {
                "response": response,
                "stage": "introduction",
                "next_stage": "work_background",
                "awaiting_response": True
            }

        # Process response to introduction
        intent = await self._analyze_intent(message, "introduction_response")
        
        if intent.get("has_time", False):
            # Lead has time to chat - move to work background
            response = self.workflow_responses["work_background_question"]
            return {
                "response": response,
                "stage": "work_background",
                "next_stage": "work_background",
                "awaiting_response": True
            }
        elif intent.get("no_time", False):
            # Lead doesn't have time
            if intent.get("proposed_time"):
                response = self.workflow_responses["no_time_with_proposed"].format(
                    proposed_time=intent["proposed_time"]
                )
                return {
                    "response": response,
                    "stage": "scheduled_callback",
                    "next_stage": "completed",
                    "awaiting_response": False
                }
            else:
                response = self.workflow_responses["no_time_response"]
                return {
                    "response": response,
                    "stage": "introduction",
                    "next_stage": "introduction",
                    "awaiting_response": True
                }
        else:
            # Unclear response - acknowledge and proceed
            acknowledgment = await self._generate_acknowledgment(message)
            response = f"{acknowledgment} {self.workflow_responses['work_background_question']}"
            return {
                "response": response,
                "stage": "work_background",
                "next_stage": "work_background",
                "awaiting_response": True
            }

    async def _handle_work_background(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle work background qualification"""

        # Analyze work background
        background_analysis = await self._analyze_work_background(message)
        background_type = background_analysis.get("type", WorkBackground.UNCLEAR)

        # Store work background in session context (ensure JSON serializable)
        session_context = session.get("context", {})
        session_context["work_background"] = {
            "type": background_type.value,
            "description": message,
            "analysis": {
                "type": background_type.value,
                "confidence": background_analysis.get("confidence", 0.0),
                "details": str(background_analysis.get("details", ""))
            }
        }

        # Use exact acknowledgment and move to motivation
        if background_type == WorkBackground.CORPORATE:
            response = f"That sounds interesting, {self.workflow_responses['motivation_corporate']}"
        else:
            response = f"That sounds interesting, {self.workflow_responses['motivation_trades']}"

        return {
            "response": response,
            "stage": "motivation",
            "next_stage": "motivation",
            "awaiting_response": True,
            "context_update": session_context
        }

    async def _handle_motivation(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle motivation qualification"""

        # Get work background from context
        work_background = session.get("context", {}).get("work_background", {})
        background_type = work_background.get("type", "unclear")

        # Analyze motivation
        motivation_analysis = await self._analyze_motivation(message)
        motivation_category = motivation_analysis.get("category")

        # Check if we need trades clarification
        if (background_type == WorkBackground.TRADES.value and
            not motivation_analysis.get("employment_clear", True)):

            acknowledgment = await self._generate_acknowledgment(message)
            response = f"{acknowledgment}. {self.workflow_responses['trades_clarification']}"

            return {
                "response": response,
                "stage": "motivation",
                "next_stage": "motivation",
                "awaiting_response": True
            }

        # Store motivation and move to budget (ensure JSON serializable)
        session_context = session.get("context", {})
        session_context["motivation"] = {
            "category": motivation_category.value if hasattr(motivation_category, 'value') else str(motivation_category),
            "description": message,
            "analysis": {
                "category": motivation_category.value if hasattr(motivation_category, 'value') else str(motivation_category),
                "confidence": motivation_analysis.get("confidence", 0.0),
                "details": str(motivation_analysis.get("details", ""))
            }
        }

        # Get budget from Zoho CRM field (placeholder for now)
        budget_amount = lead.get("budget") or "your available"

        # Use exact acknowledgment and budget question
        budget_question = self.workflow_responses["budget_question"].format(
            budget=budget_amount
        )
        response = f"That sounds interesting!. {budget_question}"

        return {
            "response": response,
            "stage": "budget",
            "next_stage": "budget",
            "awaiting_response": True,
            "context_update": session_context
        }

    async def _handle_budget(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle budget qualification"""

        # Analyze budget response
        budget_analysis = await self._analyze_budget_response(message)

        if budget_analysis.get("confirmed", False):
            # Budget confirmed - provide franchise fee info
            response = self.workflow_responses["budget_confirmed"]
            return {
                "response": response,
                "stage": "scheduling",
                "next_stage": "scheduling",
                "awaiting_response": True
            }
        elif budget_analysis.get("declined", False):
            # Budget not confirmed - ask for amount
            response = self.workflow_responses["budget_no_response"]
            return {
                "response": response,
                "stage": "budget",
                "next_stage": "budget",
                "awaiting_response": True,
                "budget_attempt": session.get("context", {}).get("budget_attempt", 0) + 1
            }
        elif session.get("context", {}).get("budget_attempt", 0) >= 1:
            # Second attempt - provide reassurance
            response = self.workflow_responses["budget_still_no_amount"]
            return {
                "response": response,
                "stage": "scheduling",
                "next_stage": "scheduling",
                "awaiting_response": True
            }
        else:
            # First unclear response - acknowledge and ask again
            acknowledgment = await self._generate_acknowledgment(message)
            response = f"{acknowledgment}. {self.workflow_responses['budget_no_response']}"
            return {
                "response": response,
                "stage": "budget",
                "next_stage": "budget",
                "awaiting_response": True,
                "budget_attempt": 1
            }

    async def _handle_scheduling(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle scheduling logic"""

        # Analyze scheduling response
        scheduling_analysis = await self._analyze_scheduling_response(message)

        if scheduling_analysis.get("positive_response", False):
            # Generate scheduling options using existing meeting booking system
            scheduling_options = await self._generate_scheduling_options(lead)

            # Use the meeting service to format the scheduling text
            if scheduling_options.get("full_options"):
                formatted_text = coochie_meeting_service.format_scheduling_text(
                    scheduling_options["full_options"]
                )
                response = f"Great, I would love to organize a phone call with Andy who will go through the model in detail. {formatted_text}"
            else:
                # Fallback to workflow response
                response = self.workflow_responses["scheduling_good_response"].format(
                    time=scheduling_options["time1"],
                    day=scheduling_options["day1"]
                )
                response += f" Or {scheduling_options['time2']} on {scheduling_options['day2']}?"

            return {
                "response": response,
                "stage": "confirmation",
                "next_stage": "confirmation",
                "awaiting_response": True,
                "scheduling_options": scheduling_options
            }
        else:
            # Handle objection or unclear response
            return await self._handle_objection_or_general(session, lead, message)

    async def _handle_confirmation(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle meeting confirmation"""

        # Analyze confirmation response
        confirmation_analysis = await self._analyze_confirmation_response(message)

        if confirmation_analysis.get("confirmed", False):
            # Meeting confirmed - book it and send confirmation
            selected_time = confirmation_analysis.get("selected_time", message)

            # Get scheduling options from session context
            scheduling_options = session.get("context", {}).get("scheduling_options")

            booking_result = await self._book_meeting(
                lead,
                selected_time,
                scheduling_options
            )

            # Enhance confirmation response with booking details
            base_response = self.workflow_responses["confirmation"]
            if booking_result.get("success") and booking_result.get("display_time"):
                response = f"{base_response} Your meeting is scheduled for {booking_result['display_time']}."
            else:
                response = base_response

            return {
                "response": response,
                "stage": "completed",
                "next_stage": "completed",
                "awaiting_response": False,
                "booking_result": booking_result
            }
        else:
            # Handle objection or request for different time
            return await self._handle_objection_or_general(session, lead, message)

    async def _handle_objection_or_general(
        self,
        session: Dict[str, Any],
        lead: Dict[str, Any],
        message: str
    ) -> Dict[str, Any]:
        """Handle objections using comprehensive objection handling service"""

        try:
            # Use the comprehensive objection handling service
            objection_result = await objection_handling_service.handle_objection(
                message=message,
                current_stage=session.get("workflow_stage", "introduction"),
                context={
                    "lead_id": str(lead.get("id")) if lead and lead.get("id") else None,
                    "session_id": session.get("phone_number", "unknown"),
                    "conversation_stage": session.get("workflow_stage", "introduction")
                }
            )

            # Extract results from objection service
            response = objection_result.get("response")
            objection_type = objection_result.get("objection_type")
            objection_detected = objection_result.get("objection_detected", False)

            # Log objection handling (no PII)
            logger.info(
                f"Objection handling completed for lead {lead.get('id', 'unknown') if lead else 'unknown'}",
                objection_detected=objection_detected,
                objection_type=objection_type,
                stage=session.get("workflow_stage", "introduction"),
                confidence=objection_result.get("confidence", 0.0)
            )

            return {
                "response": response,
                "stage": session.get("workflow_stage", "introduction"),
                "next_stage": session.get("workflow_stage", "introduction"),
                "awaiting_response": True,
                "objection_handled": objection_type,
                "objection_detected": objection_detected,
                "objection_confidence": objection_result.get("confidence", 0.0),
                "detection_method": objection_result.get("detection_details", {}).get("detected_by")
            }

        except Exception as e:
            logger.error(f"Error in objection handling: {str(e)}")
            # Fallback to simple acknowledgment
            acknowledgment = await self._generate_acknowledgment(message)
            response = f"{acknowledgment}. {self.workflow_responses['objection_followup']}"

            return {
                "response": response,
                "stage": session.get("workflow_stage", "introduction"),
                "next_stage": session.get("workflow_stage", "introduction"),
                "awaiting_response": True,
                "objection_handled": None,
                "error": str(e)
            }

    # Helper methods for OpenAI analysis
    async def _analyze_intent(self, message: str, context: str) -> Dict[str, Any]:
        """Analyze user intent using OpenAI"""
        expected_fields = ["has_time", "no_time", "proposed_time"]

        result = await self.openai_service.analyze_intent(
            message=message,
            context=context,
            expected_fields=expected_fields
        )

        # Ensure we have default values
        return {
            "has_time": result.get("has_time", True),
            "no_time": result.get("no_time", False),
            "proposed_time": result.get("proposed_time")
        }

    async def _analyze_work_background(self, message: str) -> Dict[str, Any]:
        """Analyze work background using OpenAI"""
        expected_fields = ["type", "reasoning"]

        result = await self.openai_service.analyze_intent(
            message=message,
            context="work background categorization (corporate/trades/unclear)",
            expected_fields=expected_fields
        )

        # Map to enum
        type_mapping = {
            "corporate": WorkBackground.CORPORATE,
            "trades": WorkBackground.TRADES,
            "unclear": WorkBackground.UNCLEAR
        }

        work_type = result.get("type", "unclear")
        if isinstance(work_type, str):
            work_type = work_type.lower()

        return {
            "type": type_mapping.get(work_type, WorkBackground.UNCLEAR),
            "reasoning": result.get("reasoning", "Analysis completed")
        }

    async def _analyze_motivation(self, message: str) -> Dict[str, Any]:
        """Analyze motivation using OpenAI"""
        prompt = f"""
        Analyze this motivation for franchise interest:
        "{message}"

        Categorize into one of these motivations:
        - be_your_own_boss: Want to work for themselves
        - sick_of_corporate: Tired of office/desk job
        - flexible_lifestyle: Want better work-life balance
        - ease_into_retirement: 50+ looking for easier transition
        - legacy_for_children: Want to pass business to kids
        - redundancy: Job was made redundant
        - diversification: Want to diversify current business
        - relocation: Moving to new location

        Also determine:
        - employment_clear: Is their employment status clear? (for trades)

        Return JSON format.
        """

        response = await self.openai_service.get_completion(
            prompt,
            model="gpt-4",
            temperature=0.1
        )

        try:
            return json.loads(response)
        except:
            return {"category": "unclear", "employment_clear": True}

    async def _analyze_budget_response(self, message: str) -> Dict[str, Any]:
        """Analyze budget response using OpenAI"""
        prompt = f"""
        Analyze this budget response:
        "{message}"

        Determine:
        - confirmed: Did they confirm they have the budget? (true/false)
        - declined: Did they say no to having the budget? (true/false)
        - amount_mentioned: Did they mention a specific amount?
        - amount: If amount mentioned, extract it

        Return JSON format.
        """

        response = await self.openai_service.get_completion(
            prompt,
            model="gpt-4",
            temperature=0.1
        )

        try:
            return json.loads(response)
        except:
            return {"confirmed": False, "declined": False}

    async def _analyze_scheduling_response(self, message: str) -> Dict[str, Any]:
        """Analyze scheduling response using OpenAI"""
        prompt = f"""
        Analyze this response to scheduling:
        "{message}"

        Determine:
        - positive_response: Is this a positive response to scheduling? (true/false)
        - objection: Is this an objection or concern?
        - objection_type: If objection, what type?

        Return JSON format.
        """

        response = await self.openai_service.get_completion(
            prompt,
            model="gpt-4",
            temperature=0.1
        )

        try:
            return json.loads(response)
        except:
            return {"positive_response": True}

    async def _analyze_confirmation_response(self, message: str) -> Dict[str, Any]:
        """Analyze confirmation response using OpenAI"""
        prompt = f"""
        Analyze this meeting confirmation response:
        "{message}"

        Determine:
        - confirmed: Did they confirm a meeting time? (true/false)
        - selected_time: Which time option did they select?
        - request_different: Do they want different times?

        Return JSON format.
        """

        response = await self.openai_service.get_completion(
            prompt,
            model="gpt-4",
            temperature=0.1
        )

        try:
            return json.loads(response)
        except:
            return {"confirmed": False}

    async def _analyze_objection(self, message: str) -> Dict[str, Any]:
        """Analyze objections using OpenAI"""
        objection_types = {
            "no_value": ["don't see value", "too expensive", "not worth it"],
            "not_heard": ["haven't heard", "don't know you", "never seen"],
            "marketing": ["marketing", "lead generation", "customers"],
            "experience": ["no experience", "don't know how", "never done"],
            "income": ["income", "money", "earnings", "profit"],
            "income_guarantee": ["income guarantee", "guaranteed income"],
            "royalty": ["royalty", "fees", "ongoing costs"]
        }

        prompt = f"""
        Analyze this message for objections:
        "{message}"

        Objection types: {list(objection_types.keys())}

        Return JSON with "type" field (or null if no objection).
        """

        response = await self.openai_service.get_completion(
            prompt,
            model="gpt-4",
            temperature=0.1
        )

        try:
            return json.loads(response)
        except:
            return {"type": None}

    async def _generate_acknowledgment(self, message: str) -> str:
        """Generate natural acknowledgment using conversation service"""
        return await coochie_conversation_service.generate_acknowledgment(message)

    async def _generate_scheduling_options(self, lead: Lead) -> Dict[str, Any]:
        """Generate scheduling options using existing meeting booking system"""
        try:
            # Use the Coochie meeting service to get real scheduling options
            options = await coochie_meeting_service.generate_scheduling_options(
                lead_id=str(lead.get("id", "unknown")),
                phone_number=lead.get("phone_number", ""),
                preferred_timezone=self.default_timezone
            )

            # Return in format expected by workflow
            return {
                "time1": options.get("time1", "2:00 PM"),
                "day1": options.get("day1", "Tomorrow"),
                "time2": options.get("time2", "10:00 AM"),
                "day2": options.get("day2", "Day after"),
                "full_options": options,  # Store full options for booking
                "success": options.get("success", False)
            }

        except Exception as e:
            logger.error(f"Error generating scheduling options: {str(e)}")
            # Fallback to basic options
            from datetime import datetime, timedelta

            tomorrow = datetime.now() + timedelta(days=1)
            day_after = datetime.now() + timedelta(days=2)

            return {
                "time1": "2:00 PM",
                "day1": tomorrow.strftime("%A"),
                "time2": "10:00 AM",
                "day2": day_after.strftime("%A"),
                "success": False,
                "error": str(e)
            }

    async def _book_meeting(
        self,
        lead: Lead,
        selected_time: str,
        scheduling_options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Book meeting using existing meeting booking system"""
        try:
            # Parse the selected time to find the matching option
            if scheduling_options and scheduling_options.get("full_options"):
                full_options = scheduling_options["full_options"]
                available_slots = full_options.get("slots", [])

                # Find the selected option
                selected_option = await coochie_meeting_service.parse_time_selection(
                    message=selected_time,
                    available_options=available_slots
                )

                if selected_option:
                    # Book the meeting using the selected option
                    booking_result = await coochie_meeting_service.book_meeting(
                        lead_id=str(lead.get("id", "unknown")),
                        selected_option=selected_option,
                        phone_number=lead.get("phone_number", "")
                    )

                    logger.info(
                        f"Meeting booking result for lead {lead.get('id', 'unknown')}",
                        success=booking_result.get("success", False),
                        booking_id=booking_result.get("booking_id"),
                        meeting_time=booking_result.get("meeting_time")
                    )

                    return booking_result
                else:
                    logger.warning(f"Could not parse selected time: {selected_time}")

            # Fallback: create a mock booking result
            return {
                "success": True,
                "booking_id": f"fallback_booking_{lead.get('id', 'unknown')}_{datetime.now().strftime('%H%M%S')}",
                "meeting_time": selected_time,
                "display_time": selected_time,
                "staff_name": "Andy",
                "confirmation_sent": False,
                "fallback": True
            }

        except Exception as e:
            logger.error(f"Error booking meeting for lead {lead.get('id', 'unknown')}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "retry_available": True
            }

    async def _update_session(
        self,
        db: AsyncSession,
        session: Dict[str, Any],
        response_data: Dict[str, Any]
    ):
        """Update conversation session with new stage and context"""
        if "next_stage" in response_data:
            session["workflow_stage"] = response_data["next_stage"]

        if "context_update" in response_data:
            session["context"] = response_data["context_update"]

        # Store scheduling options in context for booking
        if "scheduling_options" in response_data:
            if not session.get("context"):
                session["context"] = {}
            session["context"]["scheduling_options"] = response_data["scheduling_options"]

        # Increment message count
        session["messages_count"] = session.get("messages_count", 0) + 1

        # Update timestamp
        session["updated_at"] = datetime.now(timezone.utc).isoformat()

        # Save to Redis
        session_key = f"coochie_session:{session['phone_number']}"
        try:
            self.redis_client.setex(session_key, 86400, json.dumps(session))
            logger.info(f"Updated session for {session['phone_number']}, stage: {session['workflow_stage']}")
        except Exception as e:
            logger.warning(f"Error updating session in Redis: {e}")
