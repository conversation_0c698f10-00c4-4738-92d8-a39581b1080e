"""
Lead Qualification Agent
Handles lead qualification through structured questioning and data collection
"""

from typing import Dict, Any, List, Optional
import structlog
from langchain_core.messages import AIMessage

from .agent_state_base import BaseAgent, AgentState
from .tools.registry import tool_registry

from app.core.logging import logger


class LeadQualificationAgent(BaseAgent):
    """
    Agent responsible for qualifying leads through structured questioning
    and updating lead status based on responses
    """
    
    def _initialize_tools(self):
        """Initialize lead qualification tools"""
        tool_names = [
            "create_lead", "update_lead_status", "get_lead", 
            "create_communication", "validate_lead_data"
        ]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process lead qualification requests"""
        try:
            user_input = state.get("user_input", "")
            lead_id = state.get("lead_id")
            context = state.get("context", {})
            
            # Extract lead information from user input
            lead_data = await self._extract_lead_information(user_input, context)
            
            # Create or update lead
            if not lead_id:
                lead_id = await self._create_lead(lead_data)
                state["lead_id"] = lead_id
            else:
                await self._update_lead(lead_id, lead_data)
            
            # Determine next qualification question
            next_question = await self._get_next_qualification_question(lead_id, lead_data)
            
            # Assess qualification status
            qualification_status = await self._assess_qualification(lead_data)
            
            # Update lead status
            await self._update_lead_status(lead_id, qualification_status)
            
            # Generate response
            if next_question:
                response = await self._generate_qualification_response(next_question, lead_data)
                state["next_action"] = "continue"  # Continue qualification
            else:
                response = await self._generate_completion_response(qualification_status, lead_data)
                state["next_action"] = "continue" if qualification_status == "qualified" else "end"
            
            # Update state
            state["response"] = response
            state["lead_data"] = lead_data
            state["lead_status"] = qualification_status
            state["messages"] = state.get("messages", []) + [AIMessage(content=response)]
            
            # Log communication
            await self._log_qualification_interaction(lead_id, user_input, response)
            
            logger.info(f"Lead qualification processed for lead {lead_id}")
            return state
            
        except Exception as e:
            logger.error(f"Error in lead qualification agent: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _extract_lead_information(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract lead information from user input using LLM"""
        extraction_prompt = f"""
        Extract lead qualification information from the user's message.

        User message: "{user_input}"
        Previous context: {context}

        Extract the following information if available:
        - full_name: Full name of the person
        - email: Email address
        - phone: Phone number
        - location: City, state, or region
        - budget_preference: Investment budget or range
        - experience: Business or franchise experience
        - timeline: When they want to start
        - brand_interest: Specific franchise brands mentioned
        - motivation: Why they're interested in franchising

        Return as JSON format with only the fields that have values.
        If no information is found, return empty JSON {{}}.
        """
        
        response = await self.llm.ainvoke(extraction_prompt)
        
        try:
            # Parse the JSON response (simplified for demo)
            import json
            lead_data = json.loads(response.content.strip())
            return lead_data
        except:
            # Fallback to empty dict if parsing fails
            return {}
    
    async def _create_lead(self, lead_data: Dict[str, Any]) -> str:
        """Create a new lead record"""
        try:
            create_tool = tool_registry.get_tool("create_lead")
            if create_tool:
                # Create lead (implementation depends on tool)
                # lead_id = await create_tool.arun(lead_data)
                # return lead_id
                
                # Mock lead creation for now
                import uuid
                return str(uuid.uuid4())
            
        except Exception as e:
            logger.error(f"Error creating lead: {str(e)}")
            raise
    
    async def _update_lead(self, lead_id: str, lead_data: Dict[str, Any]):
        """Update existing lead with new information"""
        try:
            # Implementation would update lead in database
            logger.info(f"Updated lead {lead_id} with new data")
            
        except Exception as e:
            logger.error(f"Error updating lead: {str(e)}")
    
    async def _get_next_qualification_question(self, lead_id: str, lead_data: Dict[str, Any]) -> Optional[str]:
        """Determine the next qualification question to ask"""
        # Define qualification questions in order of priority
        qualification_questions = [
            {
                "field": "full_name",
                "question": "To better assist you, may I have your name?"
            },
            {
                "field": "location", 
                "question": "What city or region are you looking to open a franchise in?"
            },
            {
                "field": "budget_preference",
                "question": "What's your investment budget range for a franchise opportunity?"
            },
            {
                "field": "experience",
                "question": "Do you have any previous business or franchise experience?"
            },
            {
                "field": "timeline",
                "question": "What's your ideal timeline for getting started with a franchise?"
            }
        ]
        
        # Find the first missing field
        for q in qualification_questions:
            if q["field"] not in lead_data or not lead_data[q["field"]]:
                return q["question"]
        
        return None  # All questions answered
    
    async def _assess_qualification(self, lead_data: Dict[str, Any]) -> str:
        """Assess lead qualification status based on collected data"""
        assessment_prompt = f"""
        Assess the qualification status of this lead based on their information:
        
        Lead data: {lead_data}
        
        Qualification criteria:
        - Has contact information (name, phone/email)
        - Has specified location
        - Has realistic budget expectations
        - Shows genuine interest and motivation
        - Has reasonable timeline
        
        Qualification levels:
        - "new": Just started, minimal information
        - "in_progress": Some information collected, needs more
        - "qualified": Good information, ready for consultation
        - "highly_qualified": Excellent prospect, priority follow-up
        - "unqualified": Not a good fit
        
        Return only the qualification status.
        """
        
        response = await self.llm.ainvoke(assessment_prompt)
        return response.content.strip().lower()
    
    async def _update_lead_status(self, lead_id: str, status: str):
        """Update lead status in database"""
        try:
            update_tool = tool_registry.get_tool("update_lead_status")
            if update_tool:
                # Update status (implementation depends on tool)
                # await update_tool.arun({"lead_id": lead_id, "status": status})
                pass
                
        except Exception as e:
            logger.error(f"Error updating lead status: {str(e)}")
    
    async def _generate_qualification_response(self, question: str, lead_data: Dict[str, Any]) -> str:
        """Generate a natural response that includes the qualification question"""
        response_prompt = f"""
        Generate a natural, conversational response that includes the qualification question.
        
        Question to ask: "{question}"
        Lead data so far: {lead_data}
        
        Make the response:
        1. Friendly and professional
        2. Natural, not robotic
        3. Show appreciation for information already provided
        4. Smoothly transition to the question
        5. Explain why the information is helpful
        
        Keep it concise but warm.
        """
        
        response = await self.llm.ainvoke(response_prompt)
        return response.content.strip()
    
    async def _generate_completion_response(self, qualification_status: str, lead_data: Dict[str, Any]) -> str:
        """Generate response when qualification is complete"""
        if qualification_status in ["qualified", "highly_qualified"]:
            return """Thank you for providing that information! Based on what you've shared, it sounds like you could be a great fit for franchise ownership. 

I'd love to connect you with one of our franchise consultants who can provide more detailed information about opportunities in your area and budget range. They can also answer any specific questions you might have.

Would you like me to schedule a consultation for you?"""
        
        elif qualification_status == "in_progress":
            return "Thank you for the information! I have a few more questions to better understand your franchise interests and help match you with the right opportunities."
        
        else:
            return "Thank you for your interest in franchising. While the opportunities we currently have may not be the perfect fit, I encourage you to stay in touch as new options become available."
    
    async def _log_qualification_interaction(self, lead_id: str, user_input: str, response: str):
        """Log the qualification interaction"""
        try:
            comm_tool = tool_registry.get_tool("create_communication")
            if comm_tool:
                communication_data = {
                    "lead_id": lead_id,
                    "communication_type": "qualification",
                    "subject": "Lead Qualification",
                    "content": f"User: {user_input}\nAgent: {response}",
                    "direction": "inbound"
                }
                
                # Log communication (implementation depends on tool)
                # await comm_tool.arun(communication_data)
                
        except Exception as e:
            logger.warning(f"Failed to log qualification interaction: {str(e)}")
    
    def get_qualification_criteria(self) -> List[str]:
        """Get list of qualification criteria"""
        return [
            "Contact information (name, phone, email)",
            "Target location for franchise",
            "Investment budget range", 
            "Business/franchise experience",
            "Timeline for getting started",
            "Motivation and commitment level"
        ]
