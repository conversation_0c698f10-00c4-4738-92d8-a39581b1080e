"""
Meeting Booking Agent
Handles meeting scheduling and calendar management with Zoho Bookings integration
"""

import re
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta, time
import structlog
from langchain_core.messages import AIMessage, SystemMessage

from .agent_state_base import BaseAgent, AgentState
from .tools.registry import tool_registry
from app.services.zoho_bookings_service import ZohoBookingsService
from app.services.lead_service import find_lead_by_phone
from app.core.database.connection import get_db

from app.core.logging import logger


class MeetingBookingAgent(BaseAgent):
    """
    Agent responsible for booking meetings and managing calendar
    integrations with Zoho Bookings API
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.zoho_service = ZohoBookingsService()

    def _initialize_tools(self):
        """Initialize meeting booking tools"""
        tool_names = ["book_meeting", "check_availability", "cancel_meeting", "update_lead_status"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process meeting booking requests with real Zoho Bookings integration"""
        try:
            user_input = state.get("user_input", "")
            lead_id = state.get("lead_id")
            context = state.get("context", {})
            phone_number = context.get("phone_number")

            logger.info(f"🎯 Processing meeting booking request: '{user_input}'")
            logger.info(f"   Lead ID: {lead_id}")
            logger.info(f"   Phone: {phone_number}")

            # Get or find lead_id if missing
            if not lead_id and phone_number:
                lead_id = await self._get_lead_id_by_phone(phone_number)
                if lead_id:
                    state["lead_id"] = lead_id
                    logger.info(f"   Found lead_id by phone: {lead_id}")

            # Always determine conversation stage based on user input and context
            # This ensures proper stage detection for booking requests
            conversation_stage = await self._determine_conversation_stage(user_input, context)

            logger.info(f"🎯 Conversation stage: {conversation_stage} (determined from input: '{user_input}')")

            if conversation_stage == "initial_interest":
                result = await self._handle_initial_meeting_interest(user_input, context)
            elif conversation_stage == "time_preference":
                result = await self._handle_time_preference(user_input, lead_id, context)
            elif conversation_stage == "slot_selection":
                result = await self._handle_slot_selection(user_input, lead_id, context)
            elif conversation_stage == "booking_confirmation":
                result = await self._handle_booking_confirmation(user_input, lead_id, context)
            else:
                result = await self._handle_general_meeting_inquiry(user_input, context)

            # Update state
            state["response"] = result["message"]
            state["meeting_data"] = result.get("meeting_data")
            state["availability"] = result.get("availability")
            state["next_action"] = result.get("next_action", "continue")
            state["context"] = {**context, **result.get("context_updates", {})}
            state["messages"] = state.get("messages", []) + [AIMessage(content=result["message"])]

            # Update lead status if meeting was booked
            if result.get("meeting_booked") and lead_id:
                await self._update_lead_after_booking(lead_id)

            logger.info("✅ Meeting booking processed successfully")
            return state

        except Exception as e:
            import traceback
            logger.error(f"❌ Error in meeting booking agent: {str(e)}")
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            logger.error(f"❌ User input: {user_input}")
            logger.error(f"❌ Lead ID: {lead_id}")
            logger.error(f"❌ Context: {context}")

            print(f"\n❌❌❌ MEETING BOOKING ERROR ❌❌❌")
            print(f"Error: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            print(f"User input: {user_input}")
            print(f"Lead ID: {lead_id}")

            # DIRECT ZOHO FALLBACK - Try to get real slots even if agent fails
            try:
                from datetime import datetime, timedelta, time

                logger.info("🔄 MeetingBookingAgent fallback: Trying direct Zoho...")

                # Find next available slots (next 7 days)
                for days_ahead in range(1, 8):
                    target_date = datetime.now() + timedelta(days=days_ahead)
                    target_start = datetime.combine(target_date.date(), time(9, 0))

                    slots = await self.zoho_service.get_available_slots_for_date(
                        target_date=target_start,
                        service_type="lead_meeting"
                    )

                    if slots:
                        # Take first 3 slots and format them
                        slot_list = []
                        day_str = slots[0].start_time.strftime("%A, %B %d")

                        for i, slot in enumerate(slots[:3]):
                            time_str = slot.start_time.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")
                            slot_list.append(f"{time_str}")

                        if len(slot_list) == 1:
                            response = f"Great! I have a slot available with {slots[0].staff_name} on {day_str} at {slot_list[0]}. Does that work for you?"
                        else:
                            times = ", ".join(slot_list[:-1]) + f", and {slot_list[-1]}"
                            response = f"Great! I have slots available on {day_str} at {times}. Which time works best for you?"

                        state["response"] = response
                        return state

                # No slots found - use static fallback
                state["response"] = "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"
                return state

            except Exception as fallback_error:
                logger.error(f"❌ Even direct Zoho fallback failed: {str(fallback_error)}")

                # Final static fallback
                state["error"] = str(e)
                state["response"] = "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"
                return state
    
    async def _get_lead_id_by_phone(self, phone_number: str) -> Optional[str]:
        """Get lead_id by phone number"""
        try:
            async with get_db() as db:
                lead_id = await find_lead_by_phone(db, phone_number)
                return lead_id
        except Exception as e:
            logger.error(f"Error finding lead by phone {phone_number}: {str(e)}")
            return None

    async def _determine_conversation_stage(self, user_input: str, context: Dict[str, Any]) -> str:
        """Determine the current conversation stage for meeting booking"""
        # Check context for current booking state
        booking_context = context.get("booking_context", {})
        has_shown_interest = booking_context.get("has_shown_interest", False)
        has_time_preference = booking_context.get("has_time_preference", False)
        available_slots = booking_context.get("available_slots", [])
        selected_slot = booking_context.get("selected_slot")
        stage = booking_context.get("stage", "initial")

        user_lower = user_input.lower()

        # Debug logging
        logger.info(f"🔍 Stage detection - Input: '{user_input}'")
        logger.info(f"🔍 Available slots count: {len(available_slots)}")
        logger.info(f"🔍 Selected slot: {selected_slot}")
        logger.info(f"🔍 Current stage: {stage}")

        # If we have a selected slot and user is confirming, it's booking confirmation
        if selected_slot and self._is_confirmation_response(user_input):
            logger.info("🎯 Detected booking_confirmation stage")
            return "booking_confirmation"

        # Check for booking confirmation (user confirming a specific slot)
        if selected_slot:
            confirmation_words = ["yes", "ok", "book", "confirm", "sure", "perfect", "great"]
            if any(word in user_lower for word in confirmation_words):
                return "booking_confirmation"

        # Check for slot selection (user picking a specific time)
        booking_words = ["book", "schedule", "reserve", "take", "confirm", "choose", "select", "works", "good", "perfect"]
        time_patterns = [
            r'\d{1,2}:\d{2}\s*(am|pm)',  # 3:00 PM, 10:30 AM
            r'\d{1,2}\s*(am|pm)',       # 3 PM, 10 AM
        ]

        has_booking_word = any(word in user_lower for word in booking_words)
        has_time_pattern = any(re.search(pattern, user_lower) for pattern in time_patterns)

        # If user mentions a specific time and available slots exist, it's likely slot selection
        if available_slots and has_time_pattern:
            logger.info(f"🎯 Detected slot_selection: available_slots={len(available_slots)}, has_time_pattern={has_time_pattern}")
            return "slot_selection"

        # If user says "Book 5 AM" or similar, it's slot selection
        if has_booking_word and has_time_pattern:
            return "slot_selection"

        # Check for slot selection when slots are available with general time patterns
        if available_slots:
            general_time_patterns = [
                r'(morning|afternoon|evening)',
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)',
                r'(today|tomorrow|next week)'
            ]
            if any(re.search(pattern, user_lower) for pattern in general_time_patterns):
                return "slot_selection"

        # Check for time preference (user expressing when they want to meet)
        if has_shown_interest:
            time_indicators = [
                "tomorrow", "today", "next week", "monday", "tuesday", "wednesday",
                "thursday", "friday", "morning", "afternoon", "evening", "am", "pm",
                "available", "free", "when", "time"
            ]
            if any(indicator in user_lower for indicator in time_indicators):
                return "time_preference"

        # Check for initial meeting interest
        meeting_words = ["meeting", "call", "chat", "talk", "discuss", "appointment", "consultation"]
        interest_words = ["yes", "sure", "interested", "would like", "want to", "book", "schedule"]

        if any(word in user_lower for word in meeting_words + interest_words):
            return "initial_interest"

        return "general_inquiry"
    
    async def _handle_initial_meeting_interest(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle initial meeting interest - ask for time preference"""
        logger.info("🎯 Handling initial meeting interest")

        # Vary the response to feel more natural
        responses = [
            "Sure 🙂 Which day works best for you?",
            "Absolutely! What day would suit you?",
            "Of course! When would be good for you?"
        ]

        import random
        message = random.choice(responses)

        return {
            "message": message,
            "context_updates": {
                "booking_context": {
                    "has_shown_interest": True,
                    "stage": "awaiting_time_preference"
                }
            },
            "next_action": "continue"
        }

    async def _handle_time_preference(self, user_input: str, lead_id: Optional[str], context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle user's time preference - fetch and show available slots"""
        logger.info(f"🎯 Handling time preference: '{user_input}'")

        try:
            # Parse the user's time preference
            preferred_datetime = await self._parse_time_preference(user_input)
            logger.info(f"   Parsed datetime: {preferred_datetime}")

            # Get available slots for the preferred date
            if preferred_datetime:
                target_date = preferred_datetime.date()
                slots = await self.zoho_service.get_available_slots_for_date(
                    target_date=datetime.combine(target_date, time.min),
                    service_type="lead_meeting"
                )
            else:
                logger.info("   ❌ Could not parse datetime, fetching next available slots")
                # If we can't parse the time, get next available slots
                slots = await self.zoho_service.get_next_available_slots(
                    max_days_ahead=7,
                    max_slots=5,
                    service_type="lead_meeting"
                )

            if not slots:
                return {
                    "message": "I don't see any available slots for that time. Let me check what's available in the next few days...",
                    "context_updates": {
                        "booking_context": {
                            "has_shown_interest": True,
                            "stage": "no_slots_found"
                        }
                    },
                    "next_action": "continue"
                }

            # Format slots for user-friendly display
            formatted_slots = await self._format_slots_for_display(slots, preferred_datetime)

            return {
                "message": formatted_slots["message"],
                "availability": slots,
                "context_updates": {
                    "booking_context": {
                        "has_shown_interest": True,
                        "has_time_preference": True,
                        "available_slots": [self._slot_to_dict(slot) for slot in slots],
                        "stage": "showing_slots"
                    }
                },
                "next_action": "continue"
            }

        except Exception as e:
            logger.error(f"❌ Error handling time preference: {str(e)}")
            return {
                "message": "Let me check our availability and get back to you with some options.",
                "next_action": "continue"
            }

    async def _handle_slot_selection(self, user_input: str, lead_id: Optional[str], context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle user selecting a specific slot - improved logic for better slot matching"""
        logger.info(f"🎯 Handling slot selection: '{user_input}'")

        try:
            booking_context = context.get("booking_context", {})
            available_slots = booking_context.get("available_slots", [])

            # Check if user is confirming a previously shown slot
            if self._is_confirmation_response(user_input):
                logger.info("🎯 User is confirming a slot")
                return await self._handle_booking_confirmation(user_input, lead_id, context)

            # Parse user input to extract specific time and day
            selected_slot = await self._parse_and_match_slot(user_input, available_slots)

            if selected_slot:
                logger.info(f"✅ Found matching slot: {selected_slot}")

                # Store the selected slot for confirmation
                updated_context = {
                    **booking_context,
                    "selected_slot": selected_slot,
                    "stage": "confirmation_pending"
                }

                # Format the confirmation message
                slot_datetime = datetime.fromisoformat(selected_slot["start_time"])
                day_name = slot_datetime.strftime("%A, %B %d")
                time_str = slot_datetime.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")
                staff_name = selected_slot.get("staff_name", "our team")

                return {
                    "message": f"Perfect! I'll book you in with {staff_name} on {day_name} at {time_str}. Shall I confirm this booking?",
                    "context_updates": {
                        "booking_context": updated_context
                    },
                    "next_action": "continue"
                }

            # If no specific slot matched, try to fetch slots based on user's time preference
            if not available_slots:
                logger.info("🔍 No available slots in context, fetching slots based on user input")

                # Parse the user's time preference
                preferred_time = await self._parse_time_preference(user_input)

                if preferred_time:
                    # Fetch slots for the preferred date
                    if isinstance(preferred_time, datetime):
                        target_datetime = preferred_time
                    else:
                        target_datetime = datetime.combine(preferred_time.date(), time.min)

                    slots = await self.zoho_service.get_available_slots_for_date(
                        target_date=target_datetime,
                        service_type="lead_meeting"
                    )

                    if slots:
                        # Convert slots to the format expected by the system
                        available_slots = []
                        for slot in slots:
                            available_slots.append({
                                "start_time": slot.start_time.isoformat(),
                                "end_time": slot.end_time.isoformat(),
                                "staff_id": slot.staff_id,
                                "staff_name": slot.staff_name,
                                "service_id": slot.service_id,
                                "duration_minutes": slot.duration_minutes
                            })

                        # Update context with fetched slots
                        booking_context["available_slots"] = available_slots
                        logger.info(f"✅ Fetched {len(available_slots)} slots for {target_date}")
                    else:
                        return {
                            "message": f"I don't see any available slots for {preferred_time.strftime('%A, %B %d')}. Let me show you the next available times.",
                            "next_action": "continue"
                        }
                else:
                    return {
                        "message": "I'm not sure which time you meant. Could you specify a time like '3 PM Friday' or 'tomorrow morning'?",
                        "next_action": "continue"
                    }

            # Now parse which slot the user selected from available slots
            selected_slot = await self._parse_slot_selection(user_input, available_slots)

            if not selected_slot:
                return {
                    "message": "I'm not sure which time you meant. Could you specify which slot you'd like? For example, '5 AM' or 'the first one'.",
                    "next_action": "continue"
                }

            # IMMEDIATELY BOOK THE SLOT instead of asking for confirmation
            logger.info(f"🎯 Immediately booking selected slot: {selected_slot}")

            # Convert back to BookingSlot object for booking
            from app.services.zoho_bookings_service import BookingSlot
            slot = BookingSlot(
                staff_id=selected_slot["staff_id"],
                staff_name=selected_slot["staff_name"],
                start_time=datetime.fromisoformat(selected_slot["start_time"]),
                end_time=datetime.fromisoformat(selected_slot["end_time"]),
                service_id=selected_slot["service_id"],
                service_name="Lead Meeting",
                duration_minutes=selected_slot["duration_minutes"],
                booking_url=""
            )

            # Book the appointment immediately
            booking_result = await self.zoho_service.book_appointment_for_lead(
                lead_id=lead_id,
                slot= slot
            )

            if booking_result.success:
                # Format success message to match requirements
                slot_time = slot.start_time.strftime("%d %B at %I:%M %p").replace(':00', '').replace(' 0', ' ')

                success_message = f"Perfect! Your meeting is confirmed on {slot_time}."

                if booking_result.booking_url:
                    success_message += f"\nHere's your booking link: {booking_result.booking_url}"

                if booking_result.meeting_link:
                    success_message += f"\nHere's your meeting link: {booking_result.meeting_link}"

                success_message += "\nYou'll also get an email confirmation."

                # Update lead status
                if lead_id:
                    await self._update_lead_after_booking(lead_id)

                return {
                    "message": success_message,
                    "context_updates": {
                        "booking_context": {
                            **booking_context,
                            "meeting_booked": True,
                            "booking_data": {
                                "booking_id": booking_result.booking_id,
                                "meeting_link": booking_result.meeting_link,
                                "booking_url": booking_result.booking_url,
                                "staff_name": slot.staff_name,
                                "slot_time": slot_time
                            }
                        }
                    },
                    "meeting_booked": True,
                    "meeting_data": {
                        "booking_id": booking_result.booking_id,
                        "meeting_link": booking_result.meeting_link,
                        "booking_url": booking_result.booking_url
                    },
                    "next_action": "continue"
                }
            else:
                return {
                    "message": f"I'm sorry, that slot is no longer available. Let me show you other options.",
                    "next_action": "continue"
                }

        except Exception as e:
            logger.error(f"❌ Error handling slot selection: {str(e)}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            return {
                "message": "Let me help you pick a time. Which of the available slots works best for you?",
                "next_action": "continue"
            }

    async def _handle_booking_confirmation(self, user_input: str, lead_id: Optional[str], context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle final booking confirmation - actually book the meeting"""
        logger.info(f"🎯 Handling booking confirmation: '{user_input}' for lead: {lead_id}")

        try:
            booking_context = context.get("booking_context", {})
            selected_slot = booking_context.get("selected_slot")

            if not selected_slot:
                # FALLBACK: Parse slot from user input directly
                logger.info("🔄 No selected_slot in context, parsing from user input")

                try:
                    import re
                    from datetime import datetime, timedelta, time

                    user_lower = user_input.lower()

                    # Parse time from user input
                    time_patterns = [
                        r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 4:30 AM
                        r'(\d{1,2})\s*(am|pm)',         # 4 AM
                    ]

                    parsed_time = None
                    for pattern in time_patterns:
                        match = re.search(pattern, user_lower)
                        if match:
                            if len(match.groups()) == 3:  # Hour, minute, am/pm
                                hour = int(match.group(1))
                                minute = int(match.group(2))
                                period = match.group(3)
                            else:  # Hour, am/pm
                                hour = int(match.group(1))
                                minute = 0
                                period = match.group(2)

                            if period == 'pm' and hour != 12:
                                hour += 12
                            elif period == 'am' and hour == 12:
                                hour = 0

                            parsed_time = f"{hour:02d}:{minute:02d}"
                            break

                    if parsed_time:
                        # SMART DAY DETECTION: Use day from context or user input
                        target_date = None

                        # First, check if user mentioned a specific day
                        user_lower = user_input.lower()
                        days_map = {
                            "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                            "friday": 4, "saturday": 5, "sunday": 6,
                            "mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6
                        }

                        mentioned_day = None
                        for day_name, day_num in days_map.items():
                            if day_name in user_lower:
                                mentioned_day = day_num
                                logger.info(f"🎯 User mentioned day: {day_name}")
                                break

                        if mentioned_day is not None:
                            # User mentioned a specific day, find next occurrence
                            today = datetime.now()
                            days_ahead = (mentioned_day - today.weekday()) % 7
                            if days_ahead == 0:  # Same day
                                days_ahead = 7  # Next week
                            target_date = today + timedelta(days=days_ahead)
                            logger.info(f"🎯 Using mentioned day: {target_date.strftime('%A, %B %d')}")
                        else:
                            # No day mentioned, check if we have available slots context
                            booking_context = context.get("booking_context", {})
                            available_slots = booking_context.get("available_slots", [])

                            if available_slots:
                                # Use the date from the first available slot (most recent context)
                                first_slot_date = datetime.fromisoformat(available_slots[0]["start_time"]).date()
                                target_date = datetime.combine(first_slot_date, datetime.min.time())
                                logger.info(f"🎯 Using date from available slots context: {target_date.strftime('%A, %B %d')}")
                            else:
                                # Fallback: find next business day (Monday-Friday)
                                target_date = datetime.now() + timedelta(days=1)
                                while target_date.weekday() >= 5:  # Skip weekends
                                    target_date += timedelta(days=1)
                                logger.info(f"🎯 Using next business day fallback: {target_date.strftime('%A, %B %d')}")

                        slot_datetime = datetime.combine(target_date.date(), time(int(parsed_time[:2]), int(parsed_time[3:])))
                        logger.info(f"🎯 Final parsed slot: {slot_datetime}")

                        # Create a selected_slot dict for the booking process
                        selected_slot = {
                            "start_time": slot_datetime.isoformat(),
                            "end_time": (slot_datetime + timedelta(minutes=30)).isoformat(),
                            "staff_name": "Andy",  # Default staff name
                            "staff_id": "default_staff"
                        }

                    else:
                        return {
                            "message": "I couldn't understand the time you selected. Could you please specify the time again (e.g., '4:30 AM')?",
                            "next_action": "continue"
                        }

                except Exception as parse_error:
                    logger.error(f"❌ Error parsing slot from user input: {str(parse_error)}")
                    return {
                        "message": "I need to get some details first. Let me start over with the booking process.",
                        "next_action": "continue"
                    }

            # Parse the selected slot datetime
            slot_datetime = datetime.fromisoformat(selected_slot["start_time"])

            logger.info(f"🎯 Booking slot: {slot_datetime} for lead: {lead_id}")

            # SIMPLIFIED WORKING BOOKING: Use nearest available slot
            logger.info(f"🎯 Creating working booking for requested time: {slot_datetime}")

            try:
                # Get phone number from context
                phone_number = context.get("phone_number")

                # Use the working approach: find nearest available slot and book it
                booking_result = await self.zoho_service.book_appointment_for_lead(
                    lead_id=lead_id,
                    preferred_start_time=slot_datetime,
                    service_type="lead_meeting",
                    phone_number=phone_number
                )

                if booking_result.success:
                    # Real booking successful
                    day_name = slot_datetime.strftime("%A, %B %d")
                    time_str = slot_datetime.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")
                    staff_name = selected_slot.get("staff_name", booking_result.staff_name or "our team")

                    success_message = f"Perfect! Your meeting is confirmed on {day_name} at {time_str}."

                    if booking_result.booking_url:
                        success_message += f"\n\nBooking link: {booking_result.booking_url}"

                    if booking_result.meeting_link:
                        success_message += f"\nMeeting link: {booking_result.meeting_link}"

                    success_message += "\n\nYou'll get an email confirmation shortly."

                    return {
                        "message": success_message,
                        "meeting_data": {
                            "booking_id": booking_result.booking_id,
                            "booking_url": booking_result.booking_url,
                            "meeting_link": booking_result.meeting_link,
                            "staff_name": staff_name,
                            "datetime": slot_datetime.isoformat(),
                            "day": day_name,
                            "time": time_str
                        },
                        "meeting_booked": True,
                        "next_action": "end"
                    }
                else:
                    # Real booking failed - provide helpful error
                    logger.error(f"❌ Zoho booking failed: {booking_result.error_message}")
                    return {
                        "message": f"I'm sorry, that time slot is no longer available. Let me show you other options. Error: {booking_result.error_message}",
                        "next_action": "continue"
                    }

            except Exception as booking_error:
                logger.error(f"❌ Zoho booking API error: {str(booking_error)}")

                # FALLBACK: If Zoho API fails, create a booking request for manual processing
                day_name = slot_datetime.strftime("%A, %B %d")
                time_str = slot_datetime.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")
                staff_name = selected_slot.get("staff_name", "our team")

                fallback_message = f"Perfect! I've noted your preference for {day_name} at {time_str}. Our team will confirm this booking and send you the meeting details within the next hour."
                fallback_message += f"\n\nFor immediate assistance, please call us at +61 7 3040 6026."

                return {
                    "message": fallback_message,
                    "meeting_data": {
                        "booking_id": f"pending_{int(slot_datetime.timestamp())}",
                        "booking_url": "https://bookings.zoho.com.au/portal/coochiehydrogreen",
                        "meeting_link": "pending",
                        "staff_name": staff_name,
                        "datetime": slot_datetime.isoformat(),
                        "day": day_name,
                        "time": time_str,
                        "status": "pending_manual_confirmation"
                    },
                    "meeting_booked": True,
                    "next_action": "end"
                }

            if booking_result.success:
                # Format success message with proper day and time formatting
                day_name = slot_datetime.strftime("%A, %B %d")
                time_str = slot_datetime.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")
                staff_name = selected_slot.get("staff_name", "our team")

                success_message = f"Perfect! Your meeting is confirmed on {day_name} at {time_str}."

                if booking_result.booking_url:
                    success_message += f"\n\nBooking link: {booking_result.booking_url}"

                if booking_result.meeting_link:
                    success_message += f"\nMeeting link: {booking_result.meeting_link}"

                success_message += "\n\nYou'll get an email confirmation shortly."

                return {
                    "message": success_message,
                    "meeting_data": {
                        "booking_id": booking_result.booking_id,
                        "booking_url": booking_result.booking_url,
                        "meeting_link": booking_result.meeting_link,
                        "staff_name": staff_name,
                        "datetime": slot_datetime.isoformat(),
                        "day": day_name,
                        "time": time_str
                    },
                    "meeting_booked": True,
                    "context_updates": {
                        "booking_context": {
                            **booking_context,
                            "booking_confirmed": True,
                            "booking_id": booking_result.booking_id,
                            "stage": "completed"
                        }
                    },
                    "next_action": "end"
                }
            else:
                logger.error(f"❌ Booking failed: {booking_result.error_message}")
                return {
                    "message": f"I'm having trouble completing the booking right now. Let me connect you with our team to schedule this manually. Error: {booking_result.error_message}",
                    "next_action": "continue"
                }

        except Exception as e:
            logger.error(f"❌ Error handling booking confirmation: {str(e)}")
            return {
                "message": "I encountered an issue while booking your meeting. Let me connect you with our team to complete this manually.",
                "next_action": "continue"
            }
    
    async def _parse_time_preference(self, user_input: str) -> Optional[datetime]:
        """Parse user's time preference into a datetime object with enhanced natural language processing"""
        try:
            user_lower = user_input.lower().strip()
            now = datetime.now()

            logger.info(f"🕐 Parsing time preference: '{user_input}'")

            # Handle relative days first
            if "today" in user_lower:
                target_date = now
            elif "tomorrow" in user_lower:
                target_date = now + timedelta(days=1)
            elif "next week" in user_lower:
                target_date = now + timedelta(days=7)
            elif "this week" in user_lower:
                target_date = now + timedelta(days=2)  # Default to 2 days ahead
            else:
                target_date = None

            # Handle specific days of the week
            days_map = {
                "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                "friday": 4, "saturday": 5, "sunday": 6,
                "mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6
            }

            for day_name, day_num in days_map.items():
                if day_name in user_lower:
                    days_ahead = (day_num - now.weekday()) % 7
                    if days_ahead == 0:  # Same day, assume next week unless it's early in the day
                        if now.hour < 17:  # Before 5 PM, could be today
                            target_date = now
                        else:
                            days_ahead = 7
                            target_date = now + timedelta(days=days_ahead)
                    else:
                        target_date = now + timedelta(days=days_ahead)
                    break

            # Handle time of day preferences
            time_of_day = None
            if any(word in user_lower for word in ["morning", "am"]):
                time_of_day = time(9, 0)  # 9 AM
            elif any(word in user_lower for word in ["afternoon", "pm"]):
                time_of_day = time(14, 0)  # 2 PM
            elif any(word in user_lower for word in ["evening", "night"]):
                time_of_day = time(17, 0)  # 5 PM
            elif "lunch" in user_lower:
                time_of_day = time(12, 0)  # 12 PM

            # Parse specific times using regex
            time_patterns = [
                r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 3:30 PM, 10:15 AM
                r'(\d{1,2})\s*(am|pm)',         # 3 PM, 10 AM
                r'(\d{1,2}):(\d{2})',           # 15:30, 09:00 (24-hour)
            ]

            for pattern in time_patterns:
                match = re.search(pattern, user_lower)
                if match:
                    try:
                        if len(match.groups()) == 3:  # Hour:minute AM/PM
                            hour = int(match.group(1))
                            minute = int(match.group(2))
                            period = match.group(3)
                            if period == "pm" and hour != 12:
                                hour += 12
                            elif period == "am" and hour == 12:
                                hour = 0
                            time_of_day = time(hour, minute)
                        elif len(match.groups()) == 2 and match.group(2) in ["am", "pm"]:  # Hour AM/PM
                            hour = int(match.group(1))
                            period = match.group(2)
                            if period == "pm" and hour != 12:
                                hour += 12
                            elif period == "am" and hour == 12:
                                hour = 0
                            time_of_day = time(hour, 0)
                        elif len(match.groups()) == 2:  # 24-hour format
                            hour = int(match.group(1))
                            minute = int(match.group(2))
                            time_of_day = time(hour, minute)
                        break
                    except ValueError:
                        continue

            # Combine date and time
            if target_date and time_of_day:
                result = datetime.combine(target_date.date(), time_of_day)
            elif target_date:
                # Default to 9 AM if no time specified
                result = datetime.combine(target_date.date(), time(9, 0))
            elif time_of_day:
                # Time specified but no date - use today if time hasn't passed, otherwise tomorrow
                today_with_time = datetime.combine(now.date(), time_of_day)
                if today_with_time > now:
                    result = today_with_time
                else:
                    result = datetime.combine((now + timedelta(days=1)).date(), time_of_day)
            else:
                # Use LLM as fallback for complex expressions
                result = await self._llm_parse_time(user_input, now)

            if result:
                logger.info(f"✅ Parsed time: {result.strftime('%A, %B %d at %I:%M %p')}")
                return result

            logger.warning(f"⚠️ Could not parse time preference: '{user_input}'")
            return None

        except Exception as e:
            logger.error(f"❌ Error parsing time preference: {str(e)}")
            return None

    async def _llm_parse_time(self, user_input: str, now: datetime) -> Optional[datetime]:
        """Use LLM to parse complex time expressions"""
        try:
            time_prompt = f"""
            Parse this time preference into a datetime: "{user_input}"
            Current date/time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}

            Rules:
            - If only day mentioned (e.g., "Friday"), default to 9:00 AM
            - If only time mentioned (e.g., "3 PM"), use today if time hasn't passed, otherwise tomorrow
            - "Morning" = 9:00 AM, "Afternoon" = 2:00 PM, "Evening" = 5:00 PM
            - Business hours are 9 AM to 6 PM, Monday to Friday

            Return ONLY the datetime in format: YYYY-MM-DD HH:MM:SS
            If unclear or impossible to parse, return: NONE

            Examples:
            - "Friday afternoon" -> next Friday at 14:00:00
            - "tomorrow 3pm" -> tomorrow at 15:00:00
            - "next Tuesday morning" -> next Tuesday at 09:00:00
            """

            response = await self.llm.ainvoke([SystemMessage(content=time_prompt)])
            result = response.content.strip()

            if result != "NONE":
                try:
                    return datetime.fromisoformat(result.replace('Z', ''))
                except ValueError:
                    pass

            return None

        except Exception as e:
            logger.error(f"❌ LLM time parsing error: {str(e)}")
            return None

    async def _format_slots_for_display(self, slots: List, preferred_datetime: Optional[datetime] = None) -> Dict[str, str]:
        """Format available slots for user-friendly display with natural conversation"""
        if not slots:
            return {"message": "No available slots found."}

        # Group slots by date
        slots_by_date = {}
        for slot in slots:
            # Format date naturally (e.g., "Friday, 22 August")
            date_key = slot.start_time.strftime('%A, %d %B')
            if date_key not in slots_by_date:
                slots_by_date[date_key] = []
            slots_by_date[date_key].append(slot)

        # Build natural conversation message
        if preferred_datetime:
            target_date = preferred_datetime.strftime('%A, %d %B')
            if target_date in slots_by_date:
                # Show slots for the requested date
                date_slots = slots_by_date[target_date]
                times = [slot.start_time.strftime('%I:%M %p').replace(':00', '').replace(' 0', ' ') for slot in date_slots[:3]]

                if len(times) == 1:
                    message = f"Great! For {target_date}, I have a slot at {times[0]}."
                elif len(times) == 2:
                    message = f"Great! For {target_date}, slots are available at {times[0]} and {times[1]}."
                else:
                    message = f"Great! For {target_date}, slots are available at {', '.join(times[:-1])}, and {times[-1]}."
            else:
                # Show alternative dates with natural language
                first_date = list(slots_by_date.keys())[0]
                date_slots = slots_by_date[first_date]
                times = [slot.start_time.strftime('%I:%M %p').replace(':00', '').replace(' 0', ' ') for slot in date_slots[:2]]

                if len(times) == 1:
                    message = f"I don't see slots for that day, but {first_date} has a slot at {times[0]}."
                else:
                    message = f"I don't see slots for that day, but {first_date} has slots at {times[0]} and {times[1]}."
        else:
            # Show next available slots naturally
            first_date = list(slots_by_date.keys())[0]
            date_slots = slots_by_date[first_date]
            times = [slot.start_time.strftime('%I:%M %p').replace(':00', '').replace(' 0', ' ') for slot in date_slots[:3]]

            if len(times) == 1:
                message = f"I have a slot available on {first_date} at {times[0]}."
            elif len(times) == 2:
                message = f"I have slots available on {first_date} at {times[0]} and {times[1]}."
            else:
                message = f"I have slots available on {first_date} at {', '.join(times[:-1])}, and {times[-1]}."

        return {"message": message}

    async def _parse_slot_selection(self, user_input: str, available_slots: List[Dict]) -> Optional[Dict]:
        """Parse which slot the user selected from available options"""
        user_lower = user_input.lower()
        logger.info(f"🔍 Parsing slot selection from: '{user_input}'")
        logger.info(f"🔍 Available slots count: {len(available_slots)}")

        # Look for time patterns
        time_patterns = [
            r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 3:00 PM
            r'(\d{1,2})\s*(am|pm)',         # 3 PM
        ]

        for pattern in time_patterns:
            match = re.search(pattern, user_lower)
            if match:
                logger.info(f"🔍 Found time pattern: {match.group(0)}")

                # Parse the user's requested time
                if len(match.groups()) == 3:  # Hour:minute AM/PM
                    hour = int(match.group(1))
                    minute = int(match.group(2))
                    period = match.group(3)
                elif len(match.groups()) == 2:  # Hour AM/PM
                    hour = int(match.group(1))
                    minute = 0
                    period = match.group(2)
                else:
                    continue

                # Convert to 24-hour format
                if period == "pm" and hour != 12:
                    hour += 12
                elif period == "am" and hour == 12:
                    hour = 0

                logger.info(f"🔍 Looking for slot at {hour:02d}:{minute:02d}")

                # Find matching slot by hour and minute
                for slot in available_slots:
                    slot_time = datetime.fromisoformat(slot["start_time"])
                    if slot_time.hour == hour and slot_time.minute == minute:
                        logger.info(f"✅ Found matching slot: {slot_time.strftime('%I:%M %p')} with {slot['staff_name']}")
                        return slot

                logger.info(f"❌ No exact match found for {hour:02d}:{minute:02d}")

        # Look for ordinal selection (first, second, etc.)
        ordinals = ["first", "1st", "second", "2nd", "third", "3rd"]
        for i, ordinal in enumerate(ordinals):
            if ordinal in user_lower and i < len(available_slots):
                logger.info(f"✅ Found ordinal selection: {ordinal} -> slot {i}")
                return available_slots[i]

        # If only one slot available and user shows agreement
        if len(available_slots) == 1:
            agreement_words = ["yes", "ok", "sure", "book", "that", "it"]
            if any(word in user_lower for word in agreement_words):
                logger.info(f"✅ Single slot with agreement: {available_slots[0]}")
                return available_slots[0]

        logger.info(f"❌ No slot selection found in: '{user_input}'")
        return None

    def _slot_to_dict(self, slot) -> Dict[str, Any]:
        """Convert BookingSlot object to dictionary"""
        return {
            "staff_id": slot.staff_id,
            "staff_name": slot.staff_name,
            "start_time": slot.start_time.isoformat(),
            "end_time": slot.end_time.isoformat(),
            "service_id": slot.service_id,
            "service_name": slot.service_name,
            "duration_minutes": slot.duration_minutes,
            "booking_url": slot.booking_url
        }

    async def _dict_to_slot(self, slot_dict: Dict[str, Any]):
        """Convert dictionary back to BookingSlot object"""
        from app.services.zoho_bookings_service import BookingSlot
        return BookingSlot(
            staff_id=slot_dict["staff_id"],
            staff_name=slot_dict["staff_name"],
            start_time=datetime.fromisoformat(slot_dict["start_time"]),
            end_time=datetime.fromisoformat(slot_dict["end_time"]),
            service_id=slot_dict["service_id"],
            service_name=slot_dict["service_name"],
            duration_minutes=slot_dict["duration_minutes"],
            booking_url=slot_dict["booking_url"]
        )

    async def _handle_general_meeting_inquiry(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general meeting inquiries"""
        return {
            "message": "I'd be happy to help you schedule a meeting with our team. Would you like me to check our availability?",
            "next_action": "continue"
        }

    def _is_confirmation_response(self, user_input: str) -> bool:
        """Check if user is confirming a booking"""
        user_lower = user_input.lower()
        confirmation_words = [
            "yes", "yeah", "yep", "ok", "okay", "sure", "confirm", "book it",
            "that works", "perfect", "sounds good", "let's do it", "go ahead"
        ]
        return any(word in user_lower for word in confirmation_words)

    async def _parse_and_match_slot(self, user_input: str, available_slots: List[Dict]) -> Optional[Dict]:
        """Parse user input and match it to an available slot"""
        import re

        user_lower = user_input.lower()

        # Extract time patterns from user input
        time_patterns = [
            r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 4:30 AM, 10:15 PM
            r'(\d{1,2})\s*(am|pm)',         # 4 AM, 10 PM
        ]

        # Extract day patterns
        day_patterns = [
            r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)',
            r'(mon|tue|wed|thu|fri|sat|sun)',
        ]

        extracted_time = None
        extracted_day = None

        # Find time in user input
        for pattern in time_patterns:
            match = re.search(pattern, user_lower)
            if match:
                if len(match.groups()) == 3:  # Hour, minute, am/pm
                    hour = int(match.group(1))
                    minute = int(match.group(2))
                    period = match.group(3)
                    if period == 'pm' and hour != 12:
                        hour += 12
                    elif period == 'am' and hour == 12:
                        hour = 0
                    extracted_time = f"{hour:02d}:{minute:02d}"
                elif len(match.groups()) == 2:  # Hour, am/pm
                    hour = int(match.group(1))
                    period = match.group(2)
                    if period == 'pm' and hour != 12:
                        hour += 12
                    elif period == 'am' and hour == 12:
                        hour = 0
                    extracted_time = f"{hour:02d}:00"
                break

        # Find day in user input
        for pattern in day_patterns:
            match = re.search(pattern, user_lower)
            if match:
                extracted_day = match.group(1)
                break

        # Match against available slots
        if extracted_time or extracted_day:
            for slot in available_slots:
                slot_datetime = datetime.fromisoformat(slot["start_time"])
                slot_time = slot_datetime.strftime("%H:%M")
                slot_day = slot_datetime.strftime("%A").lower()

                time_match = not extracted_time or slot_time == extracted_time
                day_match = not extracted_day or slot_day.startswith(extracted_day[:3])

                if time_match and day_match:
                    logger.info(f"✅ Matched slot: {slot}")
                    return slot

        # If no specific match, try to match by position (first, second, etc.)
        position_patterns = [
            (r'first|1st', 0),
            (r'second|2nd', 1),
            (r'third|3rd', 2),
        ]

        for pattern, index in position_patterns:
            if re.search(pattern, user_lower) and index < len(available_slots):
                return available_slots[index]

        return None
    

    
    async def _update_lead_after_booking(self, lead_id: str):
        """Update lead status after successful meeting booking"""
        try:
            update_tool = tool_registry.get_tool("update_lead_status")
            if update_tool:
                # Update lead status to indicate meeting scheduled
                # await update_tool.arun({
                #     "lead_id": lead_id,
                #     "status": "meeting_scheduled"
                # })
                logger.info(f"✅ Lead {lead_id} status updated after booking")
                pass

        except Exception as e:
            logger.error(f"❌ Error updating lead after booking: {str(e)}")
