"""
Booking Agent
AI agent for handling booking requests via SMS and other channels
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from app.core.logging import logger
from app.services.zoho_bookings_service import ZohoBookingsService, BookingSlot, BookingResult
from app.models.booking import Booking
from app.models.lead import Lead
from app.core.database.connection import get_db


@dataclass
class BookingRequest:
    """Represents a booking request from a lead"""
    lead_id: Optional[str] = None
    customer_name: str = ""
    customer_email: str = ""
    customer_phone: str = ""
    preferred_date: Optional[datetime] = None
    preferred_time: Optional[str] = None
    service_type: str = "consultation"
    preferred_staff: Optional[str] = None
    notes: Optional[str] = None
    urgency: str = "normal"  # urgent, normal, flexible


class BookingAgent:
    """AI agent for handling booking requests and scheduling"""
    
    def __init__(self):
        self.zoho_service = ZohoBookingsService()
        
        # AI prompts for different scenarios
        self.prompts = {
            "booking_confirmation": """
            Great! I've found an available appointment slot for you:

            Date: {date}
            Time: {time}
            With: {staff_name}
            Service: {service_name}
            Duration: {duration} minutes

            Would you like me to book this appointment for you?
            Reply 'YES' to confirm or 'NO' to see other options.
            """,
            
            "booking_success": """
            Your appointment has been booked successfully!

            Booking Details:
            Date: {date}
            Time: {time}
            With: {staff_name}
            Confirmation sent to: {email}

            Meeting Link: {meeting_link}

            You'll receive a confirmation email and reminder before your appointment.
            """,
            
            "no_availability": """
            I couldn't find any available slots for your preferred time. 
            
            Here are the next available options:
            {available_slots}
            
            Would any of these work for you?
            """,
            
            "booking_error": """
            I'm sorry, there was an issue booking your appointment. 
            
            Please try again or contact our team directly at:
            📞 Phone: 1800-GROWTHHIVE
            📧 Email: <EMAIL>
            """
        }

    async def process_booking_request(self, request: BookingRequest) -> Dict[str, Any]:
        """Process a booking request and return response"""
        try:
            logger.info(f"Processing booking request for {request.customer_name}")
            
            # Step 1: Find available slots
            available_slots = await self._find_available_slots(request)
            
            if not available_slots:
                return await self._handle_no_availability(request)
            
            # Step 2: Select best slot based on preferences
            best_slot = await self._select_best_slot(available_slots, request)
            
            # Step 3: Present slot to customer for confirmation
            return await self._present_slot_for_confirmation(best_slot, request)
            
        except Exception as e:
            logger.error(f"Error processing booking request: {e}")
            return {
                "success": False,
                "message": self.prompts["booking_error"],
                "error": str(e)
            }

    async def confirm_booking(self, slot: BookingSlot, request: BookingRequest) -> Dict[str, Any]:
        """Confirm and create the booking"""
        try:
            logger.info("Agent is booking the appointment")
            # Book the appointment via Zoho Bookings
            booking_result = await self.zoho_service.book_appointment(
                slot=slot,
                customer_name=request.customer_name,
                customer_email=request.customer_email,
                customer_phone=request.customer_phone,
                notes=request.notes,
                lead_id=request.lead_id
            )
            
            if booking_result.success:
                # Save booking to database
                await self._save_booking_to_db(slot, request, booking_result)
                
                # Send success message
                success_message = self.prompts["booking_success"].format(
                    date=slot.start_time.strftime("%A, %B %d, %Y"),
                    time=slot.start_time.strftime("%I:%M %p"),
                    staff_name=slot.staff_name,
                    email=request.customer_email,
                    meeting_link=booking_result.meeting_link or "Will be provided in confirmation email"
                )
                
                return {
                    "success": True,
                    "message": success_message,
                    "booking_id": booking_result.booking_id,
                    "meeting_link": booking_result.meeting_link
                }
            else:
                return {
                    "success": False,
                    "message": f"Booking failed: {booking_result.error_message}",
                    "error": booking_result.error_message
                }
                
        except Exception as e:
            logger.error(f"Error confirming booking: {e}")
            return {
                "success": False,
                "message": self.prompts["booking_error"],
                "error": str(e)
            }

    async def _find_available_slots(self, request: BookingRequest) -> List[BookingSlot]:
        """Find available slots based on request preferences"""
        try:
            # Determine date range
            if request.preferred_date:
                date_from = request.preferred_date
                date_to = request.preferred_date + timedelta(days=1)
            else:
                # Default to next 7 days
                date_from = datetime.now()
                date_to = date_from + timedelta(days=7)
            
            # Get available slots
            slots = await self.zoho_service.get_available_slots(
                date_from=date_from,
                date_to=date_to,
                service_type=request.service_type,
                preferred_staff=request.preferred_staff
            )
            
            # Filter by preferred time if specified
            if request.preferred_time:
                slots = self._filter_by_preferred_time(slots, request.preferred_time)
            
            return slots
            
        except Exception as e:
            logger.error(f"Error finding available slots: {e}")
            return []

    async def _select_best_slot(self, slots: List[BookingSlot], request: BookingRequest) -> BookingSlot:
        """Select the best slot based on preferences and urgency"""
        if not slots:
            return None
        
        # Sort by preference factors
        def slot_score(slot: BookingSlot) -> float:
            score = 0.0
            
            # Prefer earlier slots for urgent requests
            if request.urgency == "urgent":
                days_from_now = (slot.start_time - datetime.now()).days
                score += max(0, 7 - days_from_now) * 10
            
            # Prefer specific staff if requested
            if request.preferred_staff:
                staff_info = self.zoho_service.sales_team.get(request.preferred_staff, {})
                if slot.staff_id == staff_info.get("zoho_staff_id"):
                    score += 20
            
            # Prefer business hours (9 AM - 5 PM)
            hour = slot.start_time.hour
            if 9 <= hour <= 17:
                score += 5
            
            return score
        
        # Return slot with highest score
        return max(slots, key=slot_score)

    async def _present_slot_for_confirmation(self, slot: BookingSlot, request: BookingRequest) -> Dict[str, Any]:
        """Present the selected slot to customer for confirmation"""
        if not slot:
            return await self._handle_no_availability(request)
        
        confirmation_message = self.prompts["booking_confirmation"].format(
            date=slot.start_time.strftime("%A, %B %d, %Y"),
            time=slot.start_time.strftime("%I:%M %p"),
            staff_name=slot.staff_name,
            service_name=slot.service_name,
            duration=slot.duration_minutes
        )
        
        return {
            "success": True,
            "message": confirmation_message,
            "slot": slot,
            "requires_confirmation": True
        }

    async def _handle_no_availability(self, request: BookingRequest) -> Dict[str, Any]:
        """Handle case when no slots are available"""
        # Try to find slots in extended date range
        extended_date_from = datetime.now()
        extended_date_to = extended_date_from + timedelta(days=14)
        
        extended_slots = await self.zoho_service.get_available_slots(
            date_from=extended_date_from,
            date_to=extended_date_to,
            service_type=request.service_type
        )
        
        if extended_slots:
            # Show next 3 available slots
            next_slots = sorted(extended_slots, key=lambda x: x.start_time)[:3]
            slots_text = "\n".join([
                f"• {slot.start_time.strftime('%A, %B %d at %I:%M %p')} with {slot.staff_name}"
                for slot in next_slots
            ])
            
            message = self.prompts["no_availability"].format(
                available_slots=slots_text
            )
        else:
            message = "I'm sorry, we don't have any available appointments in the next 2 weeks. Please contact our team directly for assistance."
        
        return {
            "success": False,
            "message": message,
            "alternative_slots": extended_slots[:3] if extended_slots else []
        }

    async def _save_booking_to_db(self, slot: BookingSlot, request: BookingRequest, booking_result: BookingResult):
        """Save booking information to database"""
        try:
            async with get_db_session() as db:
                booking = Booking(
                    zoho_booking_id=booking_result.booking_id,
                    zoho_service_id=slot.service_id,
                    zoho_staff_id=slot.staff_id,
                    lead_id=request.lead_id,
                    customer_name=request.customer_name,
                    customer_email=request.customer_email,
                    customer_phone=request.customer_phone,
                    service_type=request.service_type,
                    staff_name=slot.staff_name,
                    start_time=slot.start_time,
                    end_time=slot.end_time,
                    duration_minutes=slot.duration_minutes,
                    status='scheduled',
                    booking_source='sms_ai',
                    booking_url=booking_result.booking_url,
                    meeting_link=booking_result.meeting_link,
                    notes=request.notes
                )
                
                db.add(booking)
                await db.commit()
                logger.info(f"Booking saved to database: {booking.id}")
                
        except Exception as e:
            logger.error(f"Error saving booking to database: {e}")

    def _filter_by_preferred_time(self, slots: List[BookingSlot], preferred_time: str) -> List[BookingSlot]:
        """Filter slots by preferred time (morning, afternoon, evening)"""
        time_ranges = {
            "morning": (6, 12),
            "afternoon": (12, 17),
            "evening": (17, 21)
        }
        
        if preferred_time.lower() in time_ranges:
            start_hour, end_hour = time_ranges[preferred_time.lower()]
            return [
                slot for slot in slots 
                if start_hour <= slot.start_time.hour < end_hour
            ]
        
        return slots

    async def get_booking_status(self, booking_id: str) -> Dict[str, Any]:
        """Get status of a specific booking"""
        try:
            async with get_db_session() as db:
                booking = await db.get(Booking, booking_id)
                if booking:
                    return {
                        "success": True,
                        "booking": {
                            "id": str(booking.id),
                            "status": booking.status,
                            "start_time": booking.start_time.isoformat(),
                            "staff_name": booking.staff_name,
                            "service_type": booking.service_type,
                            "meeting_link": booking.meeting_link
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": "Booking not found"
                    }
        except Exception as e:
            logger.error(f"Error getting booking status: {e}")
            return {
                "success": False,
                "message": "Error retrieving booking status"
            }


# Dependency injection
def get_booking_agent() -> BookingAgent:
    """Get booking agent instance"""
    return BookingAgent()
