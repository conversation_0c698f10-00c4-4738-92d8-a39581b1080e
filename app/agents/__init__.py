"""
Multi-Agent System for GrowthHive
Production-grade modular agent ecosystem
"""

from .agent_state_base import BaseAgent
from .types import Agent<PERSON><PERSON>, AgentConfig
from .orchestrator import AgentOrchestrator
from .document_ingestion import DocumentIngestionAgent
from .question_answering import QuestionAnsweringAgent
# from .question_classification import QuestionClassificationAgent  # Removed for Andy AI
from .lead_qualification import LeadQualificationAgent
from .conversation import ConversationAgent
from .tools import ToolRegistry

# Note: MeetingBookingAgent is imported lazily to avoid circular imports

__all__ = [
    "BaseAgent",
    "AgentRole",
    "AgentConfig",
    "AgentOrchestrator",
    "DocumentIngestionAgent",
    "QuestionAnsweringAgent",
    # "QuestionClassificationAgent",  # Removed for Andy AI
    "LeadQualificationAgent",
    "ConversationAgent",
    "ToolRegistry"
]
