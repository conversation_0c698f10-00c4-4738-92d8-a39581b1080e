"""
Agent Factory
Factory class for creating agents
"""

import structlog
from .agent_state_base import BaseAgent
from .types import Agent<PERSON><PERSON>, AgentConfig

from app.core.logging import logger


class AgentFactory:
    """Factory class for creating agents"""
    
    @staticmethod
    def create_agent(config: AgentConfig) -> BaseAgent:
        """Create an agent based on configuration"""
        from .document_ingestion import DocumentIngestionAgent
        from .question_answering import QuestionAnsweringAgent
        from .lead_qualification import LeadQualificationAgent
        from .conversation import ConversationAgent
        from .conversation_agent import ConversationAgent as ConversationalChatbotAgent
        
        agent_classes = {
            AgentRole.DOCUMENT_INGESTION: DocumentIngestionAgent,
            AgentR<PERSON>.QUESTION_ANSWERING: QuestionAnsweringAgent,
            AgentRole.LEAD_QUALIFICATION: LeadQualificationAgent,
            AgentRole.CONVERSATION: ConversationAgent,
            AgentR<PERSON>.CONVERSATIONAL_CHATBOT: ConversationalChatbotAgent,
        }
        
        # Handle MeetingBookingAgent separately to avoid circular imports
        if config.role == AgentRole.MEETING_BOOKING:
            from .meeting_booking import MeetingBookingAgent
            return MeetingBookingAgent(config)
        
        agent_class = agent_classes.get(config.role)
        if not agent_class:
            raise ValueError(f"Unknown agent role: {config.role}")
        
        return agent_class(config)
