"""
Lead Status Agent - Intelligent conversation monitoring and automatic lead status updates
Monitors SMS conversations and updates lead status based on conversation analysis
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, desc
from sqlalchemy.orm import selectinload

from app.models.lead import Lead
from app.models.lead_reference import LeadStatus
from app.models.conversation_message import ConversationMessage
from app.core.memory.sms_memory import get_sms_memory_manager, SMSMemoryManager
from app.core.database.connection import get_db
from app.services.lead_service import LeadService

from app.core.logging import logger


class LeadStatusType(str, Enum):
    """Lead status types for automatic classification"""
    JUNK_LEAD = "Junk Lead"
    NEW_LEAD = "New Lead"
    NOT_QUALIFIED = "Not Qualified"
    FIRST_CALL_NO_ANSWER = "1st Call - No Answer"
    SECOND_CALL_NO_ANSWER = "2nd Call - No Answer"
    THIRD_CALL_NO_ANSWER = "3rd Call - No Answer"
    CALL_BACK = "Call Back"
    FOLLOW_UP_REQUIRED = "Follow up Required"
    FRANCHISE_DATABASE = "Franchise Database"
    NOT_INTERESTED = "Not Interested"
    QUALIFIED = "Qualified"
    REGION_NOT_AVAILABLE = "Region is not available"
    WRONG_NUMBER = "Wrong Number"
    EOI_NDA_SENT = "EOI/NDA Sent"
    EOI_NDA_SIGNED = "EOI/NDA Signed"
    APPLICATION_FORM_SIGNED = "Application Form Signed"
    DEPOSIT_PAID = "Deposit Paid"
    FRANCHISE_SOLD = "Franchise Sold"
    OUT_OF_BUDGET = "Out of Budget"
    NEW = "New"
    CONTACTED = "Contacted"
    UNQUALIFIED = "Unqualified"
    CONVERTED = "Converted"
    LOST = "Lost"


@dataclass
class ConversationAnalysis:
    """Analysis result of conversation for status determination"""
    suggested_status: LeadStatusType
    confidence: float  # 0.0 to 1.0
    reasoning: str
    key_indicators: List[str]
    conversation_stage: str
    engagement_level: str  # high, medium, low
    response_count: int
    last_activity: datetime


class LeadStatusAgent:
    """Intelligent agent for monitoring conversations and updating lead status"""
    
    def __init__(self):
        self.memory_manager = get_sms_memory_manager()
        self.logger = logger.bind(component="lead_status_agent")
        
        # Status mapping cache
        self._status_cache: Dict[str, uuid.UUID] = {}
        
        # Conversation analysis patterns
        self.status_patterns = {
            LeadStatusType.NOT_INTERESTED: [
                "not interested", "no thanks", "not for me", "don't want",
                "not looking", "already have", "too busy", "not now"
            ],
            LeadStatusType.QUALIFIED: [
                "very interested", "tell me more", "when can we meet",
                "what's the next step", "ready to proceed", "sounds good"
            ],
            LeadStatusType.OUT_OF_BUDGET: [
                "too expensive", "can't afford", "budget is lower",
                "too much money", "beyond my budget", "cheaper option"
            ],
            LeadStatusType.WRONG_NUMBER: [
                "wrong number", "don't know", "never inquired",
                "not me", "mistake", "wrong person"
            ],
            LeadStatusType.REGION_NOT_AVAILABLE: [
                "not in my area", "different location", "wrong city",
                "not available here", "different region"
            ],
            LeadStatusType.CALL_BACK: [
                "call me back", "call later", "busy now",
                "in a meeting", "driving", "can't talk now"
            ]
        }
        
        # Engagement level indicators
        self.engagement_indicators = {
            "high": [
                "very interested", "tell me more", "when can we meet",
                "what documents", "next steps", "ready to proceed"
            ],
            "medium": [
                "maybe", "thinking about it", "need to discuss",
                "let me check", "sounds interesting", "tell me about"
            ],
            "low": [
                "ok", "sure", "yes", "no", "maybe later",
                "not sure", "don't know"
            ]
        }
    
    async def analyze_conversation(self, phone_number: str) -> Optional[ConversationAnalysis]:
        """Analyze conversation and determine appropriate lead status"""
        try:
            # Get conversation history
            conversation_history = self.memory_manager.get_conversation_history(phone_number, limit=50)
            
            if not conversation_history:
                return None
            
            # Get lead context
            lead_context = self.memory_manager.get_lead_context(phone_number)
            
            # Analyze conversation patterns
            analysis = await self._analyze_conversation_patterns(
                conversation_history, lead_context, phone_number
            )
            
            self.logger.info(
                "Conversation analyzed",
                phone=phone_number,
                suggested_status=analysis.suggested_status.value,
                confidence=analysis.confidence,
                reasoning=analysis.reasoning
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing conversation for {phone_number}: {e}")
            return None
    
    async def _analyze_conversation_patterns(
        self, 
        conversation_history: List[Dict[str, Any]], 
        lead_context: Optional[Dict[str, Any]],
        phone_number: str
    ) -> ConversationAnalysis:
        """Analyze conversation patterns to determine status"""
        
        # Initialize analysis variables
        lead_messages = []
        system_messages = []
        last_activity = datetime.now()
        
        # Separate messages by sender
        for msg in conversation_history:
            if msg.get('sender') in ['user', 'lead']:  # Support both 'user' and 'lead' sender values
                lead_messages.append(msg)
            elif msg.get('sender') in ['system', 'assistant']:  # Support both 'system' and 'assistant' sender values
                system_messages.append(msg)
            
            # Update last activity
            try:
                msg_time = datetime.fromisoformat(msg.get('timestamp', ''))
                if msg_time > last_activity:
                    last_activity = msg_time
            except:
                pass
        
        # Analyze response patterns
        response_count = len(lead_messages)
        engagement_level = self._determine_engagement_level(lead_messages)
        conversation_stage = self._determine_conversation_stage(conversation_history, lead_context)
        
        # Determine status based on patterns
        suggested_status, confidence, reasoning, key_indicators = self._determine_status_from_patterns(
            lead_messages, system_messages, engagement_level, conversation_stage, response_count
        )
        
        return ConversationAnalysis(
            suggested_status=suggested_status,
            confidence=confidence,
            reasoning=reasoning,
            key_indicators=key_indicators,
            conversation_stage=conversation_stage,
            engagement_level=engagement_level,
            response_count=response_count,
            last_activity=last_activity
        )
    
    def _determine_engagement_level(self, lead_messages: List[Dict[str, Any]]) -> str:
        """Determine engagement level based on lead messages"""
        if not lead_messages:
            return "low"
        
        # Analyze message content for engagement indicators
        all_text = " ".join([msg.get('message', '').lower() for msg in lead_messages])
        
        high_score = sum(1 for indicator in self.engagement_indicators["high"] if indicator in all_text)
        medium_score = sum(1 for indicator in self.engagement_indicators["medium"] if indicator in all_text)
        low_score = sum(1 for indicator in self.engagement_indicators["low"] if indicator in all_text)
        
        # Determine engagement level
        if high_score > 0:
            return "high"
        elif medium_score > 0:
            return "medium"
        else:
            return "low"
    
    def _determine_conversation_stage(
        self, 
        conversation_history: List[Dict[str, Any]], 
        lead_context: Optional[Dict[str, Any]]
    ) -> str:
        """Determine current conversation stage"""
        if not conversation_history:
            return "initial"
        
        # Check for qualification indicators
        all_text = " ".join([msg.get('message', '').lower() for msg in conversation_history])
        
        if any(word in all_text for word in ["budget", "investment", "money", "cost"]):
            return "qualification"
        elif any(word in all_text for word in ["document", "brochure", "information", "details"]):
            return "information_sharing"
        elif any(word in all_text for word in ["meeting", "call", "appointment", "visit"]):
            return "meeting_scheduling"
        else:
            return "initial_contact"
    
    def _determine_status_from_patterns(
        self,
        lead_messages: List[Dict[str, Any]],
        system_messages: List[Dict[str, Any]],
        engagement_level: str,
        conversation_stage: str,
        response_count: int
    ) -> Tuple[LeadStatusType, float, str, List[str]]:
        """Determine status based on conversation patterns"""
        
        # Default to contacted if there's any conversation
        if response_count == 0:
            if len(system_messages) > 0:
                return (
                    LeadStatusType.FIRST_CALL_NO_ANSWER,
                    0.8,
                    "System messages sent but no lead response",
                    ["no_response"]
                )
            else:
                return (
                    LeadStatusType.NEW_LEAD,
                    0.9,
                    "New lead with no conversation yet",
                    ["new_lead"]
                )
        
        # Analyze lead message content
        all_lead_text = " ".join([msg.get('message', '').lower() for msg in lead_messages])
        
        # Check for specific status patterns
        for status_type, patterns in self.status_patterns.items():
            matches = [pattern for pattern in patterns if pattern in all_lead_text]
            if matches:
                confidence = min(0.9, 0.6 + (len(matches) * 0.1))
                return (
                    status_type,
                    confidence,
                    f"Detected {status_type.value} indicators: {', '.join(matches)}",
                    matches
                )
        
        # Determine status based on engagement and stage
        if engagement_level == "high":
            if conversation_stage in ["qualification", "information_sharing"]:
                return (
                    LeadStatusType.QUALIFIED,
                    0.7,
                    f"High engagement in {conversation_stage} stage",
                    ["high_engagement", conversation_stage]
                )
        elif engagement_level == "low" and response_count < 3:
            return (
                LeadStatusType.NOT_QUALIFIED,
                0.6,
                "Low engagement with minimal responses",
                ["low_engagement", "minimal_responses"]
            )
        
        # Default status based on conversation activity
        if response_count >= 3:
            return (
                LeadStatusType.CONTACTED,
                0.8,
                f"Active conversation with {response_count} responses",
                ["active_conversation"]
            )
        else:
            return (
                LeadStatusType.FOLLOW_UP_REQUIRED,
                0.7,
                f"Limited conversation, follow-up needed",
                ["limited_conversation"]
            )

    async def get_lead_status_id(self, status_name: str) -> Optional[uuid.UUID]:
        """Get lead status ID by name from database"""
        if status_name in self._status_cache:
            return self._status_cache[status_name]

        try:
            async for db in get_db():
                result = await db.execute(
                    select(LeadStatus.id).where(
                        and_(
                            LeadStatus.name == status_name,
                            LeadStatus.is_active == True,
                            LeadStatus.is_deleted == False
                        )
                    )
                )
                status_id = result.scalar_one_or_none()

                if status_id:
                    self._status_cache[status_name] = status_id
                    return status_id

                self.logger.warning(f"Lead status not found: {status_name}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting lead status ID for {status_name}: {e}")
            return None

    async def update_lead_status(self, phone_number: str, analysis: ConversationAnalysis) -> bool:
        """Update lead status based on conversation analysis"""
        try:
            # Get lead by phone number
            async for db in get_db():
                # Find lead by phone number
                result = await db.execute(
                    select(Lead).where(
                        and_(
                            Lead.phone == phone_number,
                            Lead.is_deleted == False
                        )
                    )
                )
                lead = result.scalar_one_or_none()

                if not lead:
                    self.logger.warning(f"Lead not found for phone number: {phone_number}")
                    return False

                # Get status ID
                status_id = await self.get_lead_status_id(analysis.suggested_status.value)
                if not status_id:
                    self.logger.error(f"Status ID not found for: {analysis.suggested_status.value}")
                    return False

                # Update lead status
                await db.execute(
                    update(Lead)
                    .where(Lead.id == lead.id)
                    .values(
                        lead_status_id=status_id,
                        updated_at=datetime.now()
                    )
                )
                await db.commit()

                self.logger.info(
                    "Lead status updated",
                    phone=phone_number,
                    lead_id=str(lead.id),
                    old_status=str(lead.lead_status_id),
                    new_status=analysis.suggested_status.value,
                    confidence=analysis.confidence,
                    reasoning=analysis.reasoning
                )

                # Update lead context with status change
                self._update_lead_context_with_status(phone_number, analysis)

                return True

        except Exception as e:
            self.logger.error(f"Error updating lead status for {phone_number}: {e}")
            return False

    def _update_lead_context_with_status(self, phone_number: str, analysis: ConversationAnalysis):
        """Update lead context with status change information"""
        try:
            context = self.memory_manager.get_lead_context(phone_number) or {}

            # Add status change information
            context.update({
                "last_status_update": datetime.now().isoformat(),
                "current_status": analysis.suggested_status.value,
                "status_confidence": analysis.confidence,
                "status_reasoning": analysis.reasoning,
                "engagement_level": analysis.engagement_level,
                "conversation_stage": analysis.conversation_stage,
                "response_count": analysis.response_count
            })

            # Store updated context
            self.memory_manager.store_lead_context(phone_number, context)

        except Exception as e:
            self.logger.error(f"Error updating lead context for {phone_number}: {e}")

    async def monitor_conversation(self, phone_number: str, force_update: bool = False) -> Optional[ConversationAnalysis]:
        """Monitor a specific conversation and update status if needed"""
        try:
            # Analyze conversation
            analysis = await self.analyze_conversation(phone_number)

            if not analysis:
                return None

            # Check if status update is needed
            if analysis.confidence >= 0.7 or force_update:
                success = await self.update_lead_status(phone_number, analysis)

                if success:
                    self.logger.info(
                        "Lead status monitoring completed",
                        phone=phone_number,
                        status=analysis.suggested_status.value,
                        confidence=analysis.confidence
                    )
                else:
                    self.logger.warning(
                        "Failed to update lead status",
                        phone=phone_number,
                        status=analysis.suggested_status.value
                    )
            else:
                self.logger.info(
                    "Status update skipped due to low confidence",
                    phone=phone_number,
                    confidence=analysis.confidence,
                    threshold=0.7
                )

            return analysis

        except Exception as e:
            self.logger.error(f"Error monitoring conversation for {phone_number}: {e}")
            return None

    async def bulk_monitor_conversations(self, phone_numbers: List[str]) -> Dict[str, Optional[ConversationAnalysis]]:
        """Monitor multiple conversations and update statuses"""
        results = {}

        for phone_number in phone_numbers:
            try:
                analysis = await self.monitor_conversation(phone_number)
                results[phone_number] = analysis

                # Small delay to avoid overwhelming the system
                await asyncio.sleep(0.1)

            except Exception as e:
                self.logger.error(f"Error in bulk monitoring for {phone_number}: {e}")
                results[phone_number] = None

        return results

    async def get_conversation_insights(self, phone_number: str) -> Dict[str, Any]:
        """Get detailed insights about a conversation for debugging/analysis"""
        try:
            analysis = await self.analyze_conversation(phone_number)

            if not analysis:
                return {"error": "No conversation data found"}

            conversation_history = self.memory_manager.get_conversation_history(phone_number, limit=50)
            lead_context = self.memory_manager.get_lead_context(phone_number)

            return {
                "phone_number": phone_number,
                "analysis": {
                    "suggested_status": analysis.suggested_status.value,
                    "confidence": analysis.confidence,
                    "reasoning": analysis.reasoning,
                    "key_indicators": analysis.key_indicators,
                    "conversation_stage": analysis.conversation_stage,
                    "engagement_level": analysis.engagement_level,
                    "response_count": analysis.response_count,
                    "last_activity": analysis.last_activity.isoformat()
                },
                "conversation_summary": {
                    "total_messages": len(conversation_history) if conversation_history else 0,
                    "lead_messages": len([m for m in conversation_history if m.get('sender') == 'user']) if conversation_history else 0,
                    "system_messages": len([m for m in conversation_history if m.get('sender') == 'system']) if conversation_history else 0
                },
                "lead_context": lead_context
            }

        except Exception as e:
            self.logger.error(f"Error getting conversation insights for {phone_number}: {e}")
            return {"error": str(e)}


# Global agent instance
_lead_status_agent: Optional[LeadStatusAgent] = None


def get_lead_status_agent() -> LeadStatusAgent:
    """Get or create Lead Status Agent instance"""
    global _lead_status_agent

    if _lead_status_agent is None:
        _lead_status_agent = LeadStatusAgent()

    return _lead_status_agent
