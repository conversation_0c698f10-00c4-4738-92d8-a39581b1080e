"""
Agent Types and Enums
Type definitions for the multi-agent system
"""

from enum import Enum
from typing import Any, Dict, List, Optional, TypedDict
from datetime import datetime


class AgentRole(Enum):
    """Agent role enumeration"""
    DOCUMENT_INGESTION = "document_ingestion"
    QUESTION_ANSWERING = "question_answering"
    LEAD_QUALIFICATION = "lead_qualification"
    CONVERSATION = "conversation"
    CONVERSATIONAL_CHATBOT = "conversational_chatbot"
    MEETING_BOOKING = "meeting_booking"
    SMS_ASSISTANT = "sms_assistant"
    ORCHESTRATOR = "orchestrator"


class AgentStatus(Enum):
    """Agent status enumeration"""
    IDLE = "idle"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"
    TIMEOUT = "timeout"


class AgentConfig(TypedDict):
    """Agent configuration type"""
    role: AgentRole
    name: str
    description: Optional[str]
    model: Optional[str]
    temperature: Optional[float]
    max_tokens: Optional[int]
    timeout: Optional[int]
    tools: Optional[List[str]]
    memory_enabled: Optional[bool]
    context_window: Optional[int]


class AgentResponse(TypedDict):
    """Agent response type"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]]
    error: Optional[str]
    agent_id: str
    timestamp: datetime
    processing_time: Optional[float]
    tokens_used: Optional[int]


class AgentMetrics(TypedDict):
    """Agent metrics type"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    total_tokens_used: int
    uptime: float


class ConversationContext(TypedDict):
    """Conversation context type"""
    session_id: str
    user_id: Optional[str]
    lead_id: Optional[str]
    conversation_stage: Optional[str]
    context_data: Dict[str, Any]
    message_history: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime


class ToolResult(TypedDict):
    """Tool execution result type"""
    success: bool
    result: Any
    error: Optional[str]
    tool_name: str
    execution_time: Optional[float]
