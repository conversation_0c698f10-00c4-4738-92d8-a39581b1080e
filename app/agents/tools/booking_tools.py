"""
Booking Tools for AI Agents
Tools that AI agents can use to check availability and create bookings during conversations
"""

import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

from app.core.logging import logger
from app.services.zoho_bookings_service import ZohoBookingsService


class CheckAvailabilityTool(BaseModel):
    """Tool for checking booking availability"""
    
    name: str = "check_availability"
    description: str = """
    Check availability for booking appointments. Use this when a customer asks about available times.
    
    Parameters:
    - service_type: Type of service (lead_meeting, saumil_consultation, frank_consultation)
    - days_ahead: Number of days to look ahead (default: 7)
    - preferred_staff: Preferred staff member (saumil or frank, optional)
    """
    
    def __call__(
        self,
        service_type: str = "lead_meeting",
        days_ahead: int = 7,
        preferred_staff: Optional[str] = None
    ) -> str:
        """Check availability and return formatted response"""
        logger.info(f"🔍 CheckAvailabilityTool called")
        logger.info(f"   Service: {service_type}")
        logger.info(f"   Days ahead: {days_ahead}")
        logger.info(f"   Preferred staff: {preferred_staff or 'Any'}")

        try:
            import asyncio

            # Create service instance
            bookings_service = ZohoBookingsService()

            # Calculate date range
            date_from = datetime.now()
            date_to = date_from + timedelta(days=days_ahead)

            logger.debug(f"   Date range: {date_from.strftime('%Y-%m-%d')} to {date_to.strftime('%Y-%m-%d')}")

            # Get available slots - handle event loop properly
            try:
                # Try to get current loop
                loop = asyncio.get_running_loop()
                # If we're in an async context, we can't use run_until_complete
                logger.warning("⚠️ Already in async context, cannot run synchronous booking tool")
                return "Availability check requires async context. Please use the async version of this tool."
            except RuntimeError:
                # No running loop, create new one
                logger.debug("🔄 Creating new event loop for availability check")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    slots = loop.run_until_complete(
                        bookings_service.get_available_slots(
                            date_from=date_from,
                            date_to=date_to,
                            service_type=service_type,
                            preferred_staff=preferred_staff
                        )
                    )
                    logger.info(f"✅ Retrieved {len(slots)} available slots")
                finally:
                    loop.close()
                    logger.debug("🔄 Event loop closed")
            
            if not slots:
                logger.info(f"❌ No available slots found for {service_type} in the next {days_ahead} days")
                return f"No available slots found for {service_type} in the next {days_ahead} days."

            logger.info(f"📋 Formatting {len(slots)} slots for user response")

            # Format response
            response = f"Available slots for {service_type}:\n\n"

            # Group by date
            slots_by_date = {}
            for slot in slots[:10]:  # Limit to first 10 slots
                date_str = slot.start_time.strftime("%A, %B %d")
                time_str = slot.start_time.strftime("%I:%M %p")

                if date_str not in slots_by_date:
                    slots_by_date[date_str] = []

                slots_by_date[date_str].append({
                    "time": time_str,
                    "staff": slot.staff_name,
                    "duration": f"{slot.duration_minutes} minutes"
                })

            # Format output
            for date, times in slots_by_date.items():
                response += f"📅 {date}:\n"
                for time_info in times:
                    response += f"  • {time_info['time']} with {time_info['staff']} ({time_info['duration']})\n"
                response += "\n"

            response += "Would you like to book one of these slots?"

            logger.info(f"✅ CheckAvailabilityTool completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error checking availability: {str(e)}")
            return f"Sorry, I couldn't check availability right now. Please try again later. Error: {str(e)}"


class BookAppointmentTool(BaseModel):
    """Tool for booking appointments"""
    
    name: str = "book_appointment"
    description: str = """
    Book an appointment for a customer. Use this when a customer wants to book a specific time.
    
    Parameters:
    - customer_name: Customer's full name
    - customer_phone: Customer's phone number
    - customer_email: Customer's email (optional)
    - preferred_time: Preferred time in natural language (e.g., "tomorrow at 2pm", "next Monday morning")
    - service_type: Type of service (lead_meeting, saumil_consultation, frank_consultation)
    - notes: Additional notes for the booking
    """
    
    def __call__(
        self,
        customer_name: str,
        customer_phone: str,
        customer_email: Optional[str] = None,
        preferred_time: Optional[str] = None,
        service_type: str = "lead_meeting",
        notes: Optional[str] = None
    ) -> str:
        """Book an appointment and return confirmation"""
        try:
            import asyncio

            # Create service instance
            bookings_service = ZohoBookingsService()

            # Handle event loop properly
            try:
                # Try to get current loop
                loop = asyncio.get_running_loop()
                return "Booking requires async context. Please use the async version of this tool."
            except RuntimeError:
                # No running loop, create new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    slot = loop.run_until_complete(
                        bookings_service.get_next_available_slot(service_type=service_type)
                    )

                    if not slot:
                        return "Sorry, no available slots found in the next 7 days. Please contact us directly to schedule."

                    # Create the booking
                    booking_result = loop.run_until_complete(
                        bookings_service.book_appointment(
                            slot=slot,
                            customer_name=customer_name,
                            customer_email=customer_email or f"{customer_phone}@temp.com",
                            customer_phone=customer_phone,
                            notes=notes
                        )
                    )

                finally:
                    loop.close()
            
            if booking_result.success:
                # Format confirmation message
                start_time = slot.start_time.strftime("%A, %B %d at %I:%M %p")
                
                confirmation = f"""
✅ Booking Confirmed!

📅 Date & Time: {start_time}
👤 Staff Member: {slot.staff_name}
⏱️ Duration: {slot.duration_minutes} minutes
📋 Service: {slot.service_name}

📧 Confirmation details will be sent to your email.
"""
                
                if booking_result.meeting_link:
                    confirmation += f"\n🔗 Meeting Link: {booking_result.meeting_link}"
                
                if booking_result.booking_url:
                    confirmation += f"\n📱 Manage Booking: {booking_result.booking_url}"
                
                confirmation += "\n\nIf you need to reschedule or cancel, please contact us."
                
                return confirmation
            else:
                return f"Sorry, I couldn't complete the booking. Error: {booking_result.error_message}"
                
        except Exception as e:
            logger.error(f"Error booking appointment: {str(e)}")
            return f"Sorry, I couldn't complete the booking right now. Please try again later. Error: {str(e)}"


class GetNextAvailableSlotTool(BaseModel):
    """Tool for getting the next available appointment slot"""
    
    name: str = "get_next_available"
    description: str = """
    Get the next available appointment slot. Use this for quick booking suggestions.
    
    Parameters:
    - service_type: Type of service (lead_meeting, saumil_consultation, frank_consultation)
    - preferred_staff: Preferred staff member (saumil or frank, optional)
    """
    
    def __call__(
        self,
        service_type: str = "lead_meeting",
        preferred_staff: Optional[str] = None
    ) -> str:
        """Get next available slot"""
        try:
            import asyncio

            bookings_service = ZohoBookingsService()

            # Handle event loop properly
            try:
                # Try to get current loop
                loop = asyncio.get_running_loop()
                return "Next available check requires async context. Please use the async version of this tool."
            except RuntimeError:
                # No running loop, create new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    slot = loop.run_until_complete(
                        bookings_service.get_next_available_slot(
                            service_type=service_type,
                            preferred_staff=preferred_staff
                        )
                    )
                finally:
                    loop.close()
            
            if not slot:
                return "No available slots found in the next 7 days."
            
            start_time = slot.start_time.strftime("%A, %B %d at %I:%M %p")
            
            return f"""
🕐 Next Available Slot:

📅 {start_time}
👤 With {slot.staff_name}
⏱️ Duration: {slot.duration_minutes} minutes
📋 Service: {slot.service_name}

Would you like me to book this slot for you?
"""
            
        except Exception as e:
            logger.error(f"Error getting next available slot: {str(e)}")
            return f"Sorry, I couldn't check availability right now. Error: {str(e)}"


# Tool registry for AI agents
BOOKING_TOOLS = {
    "check_availability": CheckAvailabilityTool(),
    "book_appointment": BookAppointmentTool(),
    "get_next_available": GetNextAvailableSlotTool()
}


def get_booking_tools() -> Dict[str, Any]:
    """Get all booking tools for AI agents"""
    return BOOKING_TOOLS


def execute_booking_tool(tool_name: str, **kwargs) -> str:
    """Execute a booking tool by name"""
    if tool_name not in BOOKING_TOOLS:
        return f"Unknown booking tool: {tool_name}"
    
    try:
        tool = BOOKING_TOOLS[tool_name]
        return tool(**kwargs)
    except Exception as e:
        logger.error(f"Error executing booking tool {tool_name}: {str(e)}")
        return f"Error executing {tool_name}: {str(e)}"


# Quick booking function for SMS integration
async def quick_book_from_sms(
    customer_name: str,
    customer_phone: str,
    customer_email: Optional[str] = None,
    service_type: str = "lead_meeting",
    notes: Optional[str] = None
) -> Dict[str, Any]:
    """Quick booking function for SMS conversations"""
    logger.info(f"📱 Quick booking from SMS initiated")
    logger.info(f"   Customer: {customer_name}")
    logger.info(f"   Phone: {customer_phone}")
    logger.info(f"   Email: {customer_email or 'Not provided'}")
    logger.info(f"   Service: {service_type}")
    logger.info(f"   Notes: {notes or 'None'}")

    try:
        bookings_service = ZohoBookingsService()

        # Get next available slot
        logger.info(f"🔍 Finding next available slot for {service_type}")
        slot = await bookings_service.get_next_available_slot(service_type=service_type)

        if not slot:
            logger.warning(f"❌ No available slots found for {service_type} in the next 7 days")
            return {
                "success": False,
                "message": "No available slots found in the next 7 days. Please contact us directly."
            }

        logger.info(f"✅ Found available slot: {slot.start_time.strftime('%A, %B %d at %I:%M %p')} with {slot.staff_name}")

        # Create booking
        logger.info(f"📝 Creating booking appointment")
        booking_result = await bookings_service.book_appointment(
            slot=slot,
            customer_name=customer_name,
            customer_email=customer_email or f"{customer_phone}@temp.com",
            customer_phone=customer_phone,
            notes=notes or "Booked via SMS conversation"
        )

        if booking_result.success:
            start_time = slot.start_time.strftime("%A, %B %d at %I:%M %p")

            logger.info(f"🎉 Booking created successfully!")
            logger.info(f"   Booking ID: {booking_result.booking_id}")
            logger.info(f"   Time: {start_time}")
            logger.info(f"   Staff: {slot.staff_name}")
            logger.info(f"   Meeting Link: {booking_result.meeting_link or 'Not provided'}")

            return {
                "success": True,
                "message": f"✅ Booking confirmed for {start_time} with {slot.staff_name}",
                "booking_details": {
                    "start_time": start_time,
                    "staff_name": slot.staff_name,
                    "duration": slot.duration_minutes,
                    "service_name": slot.service_name,
                    "booking_id": booking_result.booking_id,
                    "meeting_link": booking_result.meeting_link,
                    "booking_url": booking_result.booking_url
                }
            }
        else:
            logger.error(f"❌ Booking creation failed: {booking_result.error_message}")
            return {
                "success": False,
                "message": f"Booking failed: {booking_result.error_message}"
            }

    except Exception as e:
        logger.error(f"💥 Error in quick booking from SMS: {str(e)}")
        import traceback
        logger.debug(f"   Traceback: {traceback.format_exc()}")
        return {
            "success": False,
            "message": f"Booking error: {str(e)}"
        }
