"""
Database Tools for Agent System
Tools for interacting with the database (leads, communications, etc.)
"""

from typing import Optional
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import structlog
import uuid
from sqlalchemy import select

from app.core.database.connection import get_db
from app.models.lead import Lead, Communication

from app.core.logging import logger


class CreateLeadInput(BaseModel):
    """Input schema for creating a lead"""
    full_name: str = Field(description="Full name of the lead")
    email: Optional[str] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    mobile: Optional[str] = Field(None, description="Mobile number")
    location: Optional[str] = Field(None, description="Location/city")
    lead_source: Optional[str] = Field("ai_agent", description="Source of the lead")
    budget_preference: Optional[float] = Field(None, description="Budget preference")
    qualification_status: str = Field("new", description="Initial qualification status")


class CreateLeadTool(BaseTool):
    """Tool for creating new leads in the database"""

    name: str = "create_lead"
    description: str = "Create a new lead record in the database"
    args_schema: type = CreateLeadInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Create a new lead"""
        try:
            async for db in get_db():
                # Split full_name into first_name and last_name
                full_name = kwargs["full_name"]
                parts = full_name.strip().split(' ', 1) if full_name else [""]
                first_name = parts[0]
                last_name = parts[1] if len(parts) > 1 else ""

                # Default lead status ID for "New Lead"
                default_lead_status_id = uuid.UUID("f53c50cf-9374-4d18-98c1-c905215051eb")

                # Create new lead
                lead = Lead(
                    id=uuid.uuid4(),
                    first_name=first_name,
                    last_name=last_name,
                    email=kwargs.get("email"),
                    phone=kwargs.get("phone"),
                    mobile=kwargs.get("mobile"),
                    location=kwargs.get("location"),
                    lead_status_id=default_lead_status_id,  # REQUIRED FIELD
                    budget_preference=kwargs.get("budget_preference"),
                    status="new",
                    is_active=True,
                    is_deleted=False
                )
                
                db.add(lead)
                await db.commit()
                await db.refresh(lead)
                
                logger.info(f"Created new lead: {lead.id}")
                return str(lead.id)
                
        except Exception as e:
            logger.error(f"Error creating lead: {str(e)}")
            return f"Error creating lead: {str(e)}"


class UpdateLeadStatusInput(BaseModel):
    """Input schema for updating lead status"""
    lead_id: str = Field(description="ID of the lead to update")
    status: str = Field(description="New status for the lead")
    qualification_status: Optional[str] = Field(None, description="New qualification status")


class UpdateLeadStatusTool(BaseTool):
    """Tool for updating lead status"""

    name: str = "update_lead_status"
    description: str = "Update the status of an existing lead"
    args_schema: type = UpdateLeadStatusInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Update lead status"""
        try:
            async for db in get_db():
                logger.info(f"Updating lead {kwargs['lead_id']} status to {kwargs['status']}") 
                lead = await db.get(Lead, kwargs["lead_id"])
                if not lead:
                    return f"Lead {kwargs['lead_id']} not found"
                
                lead.status = kwargs["status"]
                if kwargs.get("qualification_status"):
                    lead.qualification_status = kwargs["qualification_status"]
                
                await db.commit()
                
                logger.info(f"Updated lead {lead.id} status to {kwargs['status']}")
                return "Lead status updated successfully"
                
        except Exception as e:
            logger.error(f"Error updating lead status: {str(e)}")
            return f"Error updating lead status: {str(e)}"


class GetLeadInput(BaseModel):
    """Input schema for getting lead information"""
    lead_id: str = Field(description="ID of the lead to retrieve")


class GetLeadTool(BaseTool):
    """Tool for retrieving lead information"""

    name: str = "get_lead"
    description: str = "Get information about a specific lead"
    args_schema: type = GetLeadInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Get lead information"""
        try:
            async for db in get_db():
                lead = await db.get(Lead, kwargs["lead_id"])
                if not lead:
                    return f"Lead {kwargs['lead_id']} not found"
                
                lead_info = {
                    "id": str(lead.id),
                    "full_name": lead.full_name,
                    "email": lead.email,
                    "phone": lead.phone,
                    "mobile": lead.mobile,
                    "location": lead.location,
                    "status": lead.status,
                    "qualification_status": lead.qualification_status,
                    "budget_preference": float(lead.budget_preference) if lead.budget_preference else None,
                    "created_at": lead.created_at.isoformat()
                }
                
                return str(lead_info)
                
        except Exception as e:
            logger.error(f"Error getting lead: {str(e)}")
            return f"Error getting lead: {str(e)}"


class CreateCommunicationInput(BaseModel):
    """Input schema for creating communication records"""
    lead_id: str = Field(description="ID of the lead")
    communication_type: str = Field(description="Type of communication (email, phone, ai_interaction, etc.)")
    subject: Optional[str] = Field(None, description="Subject of the communication")
    content: str = Field(description="Content of the communication")
    direction: str = Field("inbound", description="Direction of communication (inbound, outbound, internal)")


class CreateCommunicationTool(BaseTool):
    """Tool for creating communication records"""

    name: str = "create_communication"
    description: str = "Create a communication record for a lead"
    args_schema: type = CreateCommunicationInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Create communication record"""
        try:
            async for db in get_db():
                # Verify lead exists
                lead = await db.get(Lead, kwargs["lead_id"])
                if not lead:
                    return f"Lead {kwargs['lead_id']} not found"
                
                # Create communication
                communication = Communication(
                    id=uuid.uuid4(),
                    lead_id=kwargs["lead_id"],
                    communication_type=kwargs["communication_type"],
                    subject=kwargs.get("subject"),
                    content=kwargs["content"],
                    direction=kwargs.get("direction", "inbound")
                )
                
                db.add(communication)
                await db.commit()
                
                logger.info(f"Created communication for lead {kwargs['lead_id']}")
                return "Communication record created successfully"
                
        except Exception as e:
            logger.error(f"Error creating communication: {str(e)}")
            return f"Error creating communication: {str(e)}"


class SearchLeadsInput(BaseModel):
    """Input schema for searching leads"""
    query: Optional[str] = Field(None, description="Search query")
    status: Optional[str] = Field(None, description="Filter by status")
    qualification_status: Optional[str] = Field(None, description="Filter by qualification status")
    limit: int = Field(10, description="Maximum number of results")


class SearchLeadsTool(BaseTool):
    """Tool for searching leads"""

    name: str = "search_leads"
    description: str = "Search for leads based on criteria"
    args_schema: type = SearchLeadsInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Search leads"""
        try:
            async for db in get_db():
                query = select(Lead).filter(not Lead.is_deleted)
                
                if kwargs.get("status"):
                    query = query.filter(Lead.status == kwargs["status"])
                
                if kwargs.get("qualification_status"):
                    query = query.filter(Lead.qualification_status == kwargs["qualification_status"])
                
                if kwargs.get("query"):
                    search_term = f"%{kwargs['query']}%"
                    query = query.filter(
                        (Lead.full_name.ilike(search_term)) |
                        (Lead.email.ilike(search_term)) |
                        (Lead.phone.ilike(search_term))
                    )
                
                result = await db.execute(query.limit(kwargs.get("limit", 10)))
                leads = result.scalars().all()
                
                results = []
                for lead in leads:
                    results.append({
                        "id": str(lead.id),
                        "full_name": lead.full_name,
                        "email": lead.email,
                        "status": lead.status,
                        "qualification_status": lead.qualification_status,
                        "created_at": lead.created_at.isoformat()
                    })
                
                return str(results)
                
        except Exception as e:
            logger.error(f"Error searching leads: {str(e)}")
            return f"Error searching leads: {str(e)}"
