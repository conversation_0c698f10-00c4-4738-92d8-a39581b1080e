"""
Validation Tools for Agent System
Tools for validating user input and data
"""

from typing import Dict, Any
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import structlog
import re
import phonenumbers
from phonenumbers import NumberParseException

from app.core.logging import logger


class ValidateEmailInput(BaseModel):
    """Input schema for email validation"""
    email: str = Field(description="Email address to validate")


class ValidateEmailTool(BaseTool):
    """Tool for validating email addresses"""
    
    name: str = "validate_email"
    description: str = "Validate an email address format"
    args_schema: type = ValidateEmailInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Validate email"""
        try:
            email = kwargs["email"]
            
            # Basic email regex pattern
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            
            if re.match(email_pattern, email):
                return f"Valid email address: {email}"
            else:
                return f"Invalid email address: {email}"
                
        except Exception as e:
            logger.error(f"Error validating email: {str(e)}")
            return f"Error validating email: {str(e)}"


class ValidatePhoneInput(BaseModel):
    """Input schema for phone validation"""
    phone: str = Field(description="Phone number to validate")
    country_code: str = Field("AU", description="Country code (default: AU for Australia)")


class ValidatePhoneTool(BaseTool):
    """Tool for validating phone numbers"""

    name: str = "validate_phone"
    description: str = "Validate a phone number format"
    args_schema: type = ValidatePhoneInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Validate phone number"""
        try:
            phone = kwargs["phone"]
            country_code = kwargs.get("country_code", "AU")
            
            try:
                # Parse phone number
                parsed_number = phonenumbers.parse(phone, country_code)
                
                # Check if valid
                if phonenumbers.is_valid_number(parsed_number):
                    formatted = phonenumbers.format_number(
                        parsed_number, 
                        phonenumbers.PhoneNumberFormat.INTERNATIONAL
                    )
                    return f"Valid phone number: {formatted}"
                else:
                    return f"Invalid phone number: {phone}"
                    
            except NumberParseException as e:
                return f"Invalid phone number format: {phone} ({str(e)})"
                
        except ImportError:
            # Fallback validation without phonenumbers library
            phone_pattern = r'^[\+]?[1-9][\d]{0,15}$'
            if re.match(phone_pattern, re.sub(r'[\s\-\(\)]', '', phone)):
                return f"Phone number appears valid: {phone}"
            else:
                return f"Invalid phone number format: {phone}"
        except Exception as e:
            logger.error(f"Error validating phone: {str(e)}")
            return f"Error validating phone: {str(e)}"


class ValidateLeadDataInput(BaseModel):
    """Input schema for lead data validation"""
    lead_data: Dict[str, Any] = Field(description="Lead data to validate")


class ValidateLeadDataTool(BaseTool):
    """Tool for validating lead data completeness and format"""
    
    name: str = "validate_lead_data"
    description: str = "Validate lead data for completeness and correct format"
    args_schema: type = ValidateLeadDataInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Validate lead data"""
        try:
            lead_data = kwargs["lead_data"]
            validation_results = []
            
            # Required fields
            required_fields = ["full_name"]
            for field in required_fields:
                if field not in lead_data or not lead_data[field]:
                    validation_results.append(f"Missing required field: {field}")
            
            # Email validation
            if "email" in lead_data and lead_data["email"]:
                email_tool = ValidateEmailTool()
                email_result = await email_tool._arun(email=lead_data["email"])
                if "Invalid" in email_result:
                    validation_results.append(f"Invalid email format: {lead_data['email']}")
            
            # Phone validation
            if "phone" in lead_data and lead_data["phone"]:
                phone_tool = ValidatePhoneTool()
                phone_result = await phone_tool._arun(phone=lead_data["phone"])
                if "Invalid" in phone_result:
                    validation_results.append(f"Invalid phone format: {lead_data['phone']}")
            
            # Budget validation
            if "budget_preference" in lead_data and lead_data["budget_preference"]:
                try:
                    budget = float(lead_data["budget_preference"])
                    if budget < 0:
                        validation_results.append("Budget cannot be negative")
                except (ValueError, TypeError):
                    validation_results.append("Budget must be a valid number")
            
            # Name validation
            if "full_name" in lead_data and lead_data["full_name"]:
                name = lead_data["full_name"].strip()
                if len(name) < 2:
                    validation_results.append("Name must be at least 2 characters")
                elif not re.match(r'^[a-zA-Z\s\-\.\']+$', name):
                    validation_results.append("Name contains invalid characters")
            
            # Location validation
            if "location" in lead_data and lead_data["location"]:
                location = lead_data["location"].strip()
                if len(location) < 2:
                    validation_results.append("Location must be at least 2 characters")
            
            if validation_results:
                return f"Validation errors: {'; '.join(validation_results)}"
            else:
                return "Lead data validation passed"
                
        except Exception as e:
            logger.error(f"Error validating lead data: {str(e)}")
            return f"Error validating lead data: {str(e)}"


class ValidateBudgetInput(BaseModel):
    """Input schema for budget validation"""
    budget: str = Field(description="Budget amount to validate")
    currency: str = Field("AUD", description="Currency code (default: AUD)")


class ValidateBudgetTool(BaseTool):
    """Tool for validating budget amounts"""
    
    name: str = "validate_budget"
    description: str = "Validate and parse budget amounts"
    args_schema: type = ValidateBudgetInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Validate budget"""
        try:
            budget_str = kwargs["budget"]
            currency = kwargs.get("currency", "AUD")
            
            # Remove common currency symbols and formatting
            cleaned_budget = re.sub(r'[^\d\.\,]', '', budget_str)
            cleaned_budget = cleaned_budget.replace(',', '')
            
            try:
                budget_amount = float(cleaned_budget)
                
                if budget_amount < 0:
                    return "Budget cannot be negative"
                elif budget_amount == 0:
                    return "Budget cannot be zero"
                elif budget_amount < 10000:
                    return f"Budget seems low for franchise investment: {currency} {budget_amount:,.2f}"
                elif budget_amount > 10000000:
                    return f"Budget seems very high: {currency} {budget_amount:,.2f}"
                else:
                    return f"Valid budget amount: {currency} {budget_amount:,.2f}"
                    
            except ValueError:
                return f"Invalid budget format: {budget_str}"
                
        except Exception as e:
            logger.error(f"Error validating budget: {str(e)}")
            return f"Error validating budget: {str(e)}"


class ValidateTimelineInput(BaseModel):
    """Input schema for timeline validation"""
    timeline: str = Field(description="Timeline to validate")


class ValidateTimelineTool(BaseTool):
    """Tool for validating timeline information"""
    
    name: str = "validate_timeline"
    description: str = "Validate and interpret timeline information"
    args_schema: type = ValidateTimelineInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Validate timeline"""
        try:
            timeline = kwargs["timeline"].lower()
            
            # Common timeline patterns
            immediate_patterns = ["asap", "immediately", "right away", "now", "urgent"]
            short_term_patterns = ["1-3 months", "within 3 months", "soon", "quickly"]
            medium_term_patterns = ["3-6 months", "6 months", "this year", "2024"]
            long_term_patterns = ["next year", "2025", "12 months", "1-2 years"]
            
            if any(pattern in timeline for pattern in immediate_patterns):
                return "Timeline: Immediate (ASAP) - High priority lead"
            elif any(pattern in timeline for pattern in short_term_patterns):
                return "Timeline: Short-term (1-3 months) - Good prospect"
            elif any(pattern in timeline for pattern in medium_term_patterns):
                return "Timeline: Medium-term (3-6 months) - Qualified lead"
            elif any(pattern in timeline for pattern in long_term_patterns):
                return "Timeline: Long-term (6+ months) - Future opportunity"
            else:
                return f"Timeline provided: {timeline} - Needs clarification"
                
        except Exception as e:
            logger.error(f"Error validating timeline: {str(e)}")
            return f"Error validating timeline: {str(e)}"
