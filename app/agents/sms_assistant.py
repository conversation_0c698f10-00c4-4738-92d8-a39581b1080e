"""
Andy - AI SMS Lead Qualification Specialist
Warm and knowledgeable Lead Qualification Specialist with 5+ years in the Australian franchise industry.
Communicates over SMS, qualifying leads and answering franchise-related questions using RAG.
"""

import json
import uuid
import re
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, TypedDict
import redis
from sqlalchemy import select, and_

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import SystemMessage
from langchain_openai import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, Field

from app.models.lead import Lead, LeadResponse
from app.models.conversation_message import ConversationMessage
from app.core.database.connection import get_db
from app.core.config.settings import settings
from app.services.question_extraction_service import get_question_extraction_service
from app.services.lead_service import find_lead_by_phone, get_lead_data_by_phone

from app.core.logging import logger


# Andy's Workflow Constants - Following Exact Specification
class AndyWorkflowStages:
    INTRODUCTION = "introduction"
    QUALIFICATION_1_WORK = "qualification_1_work"  # Work background
    QUALIFICATION_2_MOTIVATION = "qualification_2_motivation"  # Motivation
    QUALIFICATION_3_BUDGET = "qualification_3_budget"  # Budget
    FRANCHISE_FEE_BREAKDOWN = "franchise_fee_breakdown"  # After budget confirmation
    OBJECTION_HANDLING = "objection_handling"
    SCHEDULING = "scheduling"
    CONFIRMATION = "confirmation"



class AndyMotivationCategories:
    BE_YOUR_OWN_BOSS = "be_your_own_boss"
    SICK_OF_CORPORATE = "sick_of_corporate"
    FLEXIBLE_LIFESTYLE = "flexible_lifestyle"
    EASE_INTO_RETIREMENT = "ease_into_retirement"
    LEGACY_FOR_CHILDREN = "legacy_for_children"
    REDUNDANCY = "redundancy"
    DIVERSIFICATION = "diversification"
    RELOCATION = "relocation"


class AndyTemplates:
    """Andy's conversation templates following the exact workflow"""

    INTRODUCTION = "G'day {name}! Thanks for your interest in {franchisor_name}. Do you have a minute to chat about this opportunity?"

    INTRODUCTION_NO_NAME = "G'day! Thanks for your interest in {franchisor_name}. Do you have a minute to chat about this opportunity?"

    HELLO_RESPONSE = "G'day! Do you have a minute to chat about {franchisor_name}?"

    FOLLOWUP_3_HOURS = "G'day! Just following up on your enquiry. Got a minute to chat about the opportunity?"

    UNAVAILABILITY_RESPONSE = "No worries at all! I totally understand that now might not be the best time for a chat. Just text me when you're available and we can pick up where we left off. Cheers!"

    NO_TIME_WITH_PROPOSED = "No problem, I'll lock this in my calendar to touch base at {proposed_time}. Talk soon!"

    QUALIFICATION_1_WORK = "Great, thanks for your enquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?"

    QUALIFICATION_2_MOTIVATION_CORPORATE = "What made you enquire about this franchise opportunity given you come from a different background?"

    QUALIFICATION_2_MOTIVATION_TRADES = "What made you enquire about this franchise opportunity?"

    QUALIFICATION_2_WORK_STATUS = "Do you work for yourself (sole trader) or working as an employee?"

    QUALIFICATION_3_BUDGET = "You mentioned that you have access to a {budget} budget to invest in the business. Is that true?"

    BUDGET_INQUIRY = "How much funds do you have access to invest?"

    BUDGET_REASSURANCE = "We'll walk you through the financials and real examples from our 70 franchise partners, so rest assured you'll have a clear picture of the ROI. I just need to confirm you have funds to get started—that's why I asked."

    FRANCHISE_FEE_BREAKDOWN = "Thanks for confirming. {fee_info} How does that sound?"
    
    # Fallback template if RAG fails
    FRANCHISE_FEE_FALLBACK = "Thanks for confirming. I'd love to walk you through the investment details. How does that sound?"

    SCHEDULING_OFFER = "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"

    CONFIRMATION = "Thanks for the confirmation. Looking forward to speaking with you."

    SILENCE_CHECK = "Just checking if you're still keen to explore this opportunity? Happy to keep the chat going and set up a quick call to walk you through the details. How does that sound?"

    OBJECTION_CLARIFICATION = "Does that make sense? I'm happy to answer any questions you may have."

    # Objection handling responses

    # Motivation categories for classification
    MOTIVATION_CATEGORIES = {
        "be_your_own_boss": ["own boss", "work for myself", "be independent", "self employed"],
        "sick_of_corporate": ["tired of office", "sick of corporate", "desk job", "office work", "corporate life"],
        "flexible_lifestyle": ["work life balance", "flexible hours", "spend time with family", "flexibility", "better hours"],
        "ease_into_retirement": ["retirement", "retire", "semi retire", "ease into", "winding down"],
        "legacy_for_children": ["kids", "children", "family business", "legacy", "pass on", "inheritance"],
        "redundancy": ["redundant", "lost job", "made redundant", "job loss", "unemployment"],
        "diversification": ["diversify", "different income", "multiple streams", "expand business"],
        "relocation": ["relocating", "moving", "new location", "different area", "interstate"]
    }


class AndyLeadContext(TypedDict):
    """Andy's structured lead context for Redis storage"""
    lead_id: Optional[str]
    name: Optional[str]
    phone: str
    email: Optional[str]  # Always include email in context
    franchisor_id: Optional[str]
    franchisor_name: Optional[str]

    # Work background classification
    work_background: Optional[str]  # Specific job/role description
    work_background_type: Optional[str]  # "corporate" or "trades"
    work_details: Optional[str]  # Additional work details
    employment_type: Optional[str]  # "employee" or "sole_trader" (for trades)
    work_status: Optional[str]  # "employee" or "sole_trader" (clarification for trades)
    work_status_clarified: Optional[bool]  # Whether work status has been clarified for trades

    # Motivation categorization
    motivation: Optional[str]  # be_your_own_boss, sick_of_corporate, flexible_lifestyle, etc.
    motivation_details: Optional[str]
    motivation_category: Optional[str] = None

    # Budget information
    budget: Optional[str]  # e.g., "$150k"
    budget_confirmed: Optional[bool]
    budget_details: Optional[str]  # financing options, payment structure

    # Coochie Hydrogreen specific
    franchise_fee_discussed: Optional[bool]
    financing_discussed: Optional[bool]

    # Workflow state
    current_stage: str  # introduction, qualification_1, qualification_2, qualification_3, objection_handling, scheduling, confirmation
    qualification_complete: Optional[bool]
    questions_answered: int

    # Meeting and scheduling
    meeting_interest: Optional[bool]
    meeting_scheduled: Optional[bool]
    meeting_booked_at: Optional[str]  # When meeting was booked
    meeting_link: Optional[str]  # Meeting link for SMS sharing (can be short link)
    meeting_time: Optional[str]  # Formatted meeting time
    staff_name: Optional[str]  # Staff member name for the meeting
    meeting_booking_id: Optional[str]  # Booking ID from Zoho
    proposed_times: Optional[List[str]]

    # Objection handling
    objections_raised: Optional[List[str]]
    objections_handled: Optional[List[str]]

    # Engagement tracking
    last_updated: str
    conversation_summary: Optional[str]
    engagement_level: str  # high, medium, low
    silence_count: int  # Track how many times lead went silent

    # Anti-repetition tracking
    topics_discussed: Optional[List[str]]  # Track what topics have been covered
    questions_asked: Optional[List[str]]   # Track what questions have been asked
    information_shared: Optional[List[str]]  # Track what info has been shared
    last_acknowledgment: Optional[str]     # Track last thing we acknowledged


class AndyAssistantState(TypedDict):
    """State for Andy's SMS Assistant LangGraph workflow"""
    # Input
    message: str
    phone_number: str
    session_id: str

    # Context
    lead_context: Optional[AndyLeadContext]
    conversation_history: List[Dict[str, Any]]
    extracted_info: Dict[str, Any]

    # Andy's workflow processing
    current_stage: str
    intent: Optional[str]
    response: Optional[str]
    next_action: Optional[str]

    # Qualification tracking
    qualification_question: Optional[str]
    qualification_answer: Optional[str]
    qualification_category: Optional[str]

    # RAG integration
    rag_query: Optional[str]
    rag_response: Optional[str]

    # Database operations
    lead_id: Optional[str]
    conversation_stored: bool
    context_updated: bool
    lead_response_stored: bool

    # Tool execution results
    tool_execution_results: Optional[Dict[str, Any]]
    
    # Metadata
    processing_time: float
    execution_path: List[str]
    error: Optional[str]


class AndyMemoryManager:
    """Redis-based memory management for Andy's SMS conversations"""

    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or settings.REDIS_URL
        self.redis_client = None
        self.connect()

    def connect(self):
        """Connect to Redis"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            self.redis_client.ping()
            logger.info("Andy Memory Manager connected to Redis")
        except Exception as e:
            logger.error("Redis connection failed", error=str(e))
            self.redis_client = None

    def get_lead_context(self, phone_number: str) -> Optional[AndyLeadContext]:
        """Get enhanced Andy's lead context from Redis with conversation metadata"""
        if not self.redis_client:
            return None

        try:
            # Use consistent key pattern with SMS Memory Manager
            key = f"sms_lead_context:{phone_number}"
            data = self.redis_client.get(key)
            if data:
                context = json.loads(data)

                # Enhance with conversation metadata
                context["total_messages"] = self.get_message_count(phone_number)
                context["session_duration"] = self._calculate_session_duration(context.get("first_contact"))
                context["last_seen"] = self._calculate_last_seen(context.get("last_updated"))
                context["engagement_level"] = self._calculate_engagement_level(phone_number)

                return context
        except Exception as e:
            logger.error("Error getting enhanced lead context", error=str(e))

        return None

    def get_message_count(self, phone_number: str) -> int:
        """Get total message count for this lead"""
        if not self.redis_client:
            return 0

        try:
            key = f"andy_conversation:{phone_number}"
            return self.redis_client.llen(key) or 0
        except Exception as e:
            logger.error("Error getting message count", error=str(e))
            return 0

    def _calculate_session_duration(self, first_contact: str) -> str:
        """Calculate how long the conversation has been going"""
        if not first_contact:
            return "0 minutes"

        try:
            start_time = datetime.fromisoformat(first_contact.replace('Z', '+00:00'))
            duration = datetime.now(timezone.utc) - start_time.replace(tzinfo=timezone.utc)

            if duration.days > 0:
                return f"{duration.days} days"
            elif duration.seconds > 3600:
                return f"{duration.seconds // 3600} hours"
            else:
                return f"{duration.seconds // 60} minutes"
        except:
            return "unknown"

    def _calculate_last_seen(self, last_updated: str) -> str:
        """Calculate when lead was last seen"""
        if not last_updated:
            return "unknown"

        try:
            last_time = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            duration = datetime.now(timezone.utc) - last_time.replace(tzinfo=timezone.utc)

            if duration.seconds < 60:
                return "just now"
            elif duration.seconds < 3600:
                return f"{duration.seconds // 60} minutes ago"
            elif duration.days == 0:
                return f"{duration.seconds // 3600} hours ago"
            else:
                return f"{duration.days} days ago"
        except:
            return "unknown"

    def _calculate_engagement_level(self, phone_number: str) -> str:
        """Calculate engagement level based on conversation patterns"""
        if not self.redis_client:
            return "unknown"

        try:
            messages = self.get_conversation_history(phone_number, 10)
            if not messages:
                return "new"

            # Analyze recent messages for engagement
            user_messages = [msg for msg in messages if msg.get("sender") == "user"]
            avg_length = sum(len(msg.get("message", "")) for msg in user_messages) / max(len(user_messages), 1)

            if avg_length > 50:
                return "high"
            elif avg_length > 20:
                return "medium"
            else:
                return "low"
        except:
            return "unknown"

    async def _get_lead_id_by_phone(self, phone_number: str) -> Optional[str]:
        """Get lead_id by phone number from database"""
        try:
            async for db_session in get_db():
                lead_id = await find_lead_by_phone(db_session, phone_number)
                return lead_id
        except Exception as e:
            logger.error(f"Error finding lead by phone {phone_number}: {str(e)}")
            return None

    async def _get_lead_data_by_phone(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Get full lead data including email by phone number from database"""
        try:
            async for db_session in get_db():
                lead_data = await get_lead_data_by_phone(db_session, phone_number)
                return lead_data
        except Exception as e:
            logger.error(f"Error finding lead data by phone {phone_number}: {str(e)}")
            return None

    async def _enrich_lead_context_with_email(self, phone_number: str, lead_context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Enrich lead context with email and other data from database"""
        if not lead_context:
            lead_context = {}
        
        # If email is already in context, return as is
        if lead_context.get("email"):
            return lead_context
        
        # Get lead data from database
        lead_data = await self._get_lead_data_by_phone(phone_number)
        if lead_data:
            # Update context with email and other missing data
            lead_context.update({
                "lead_id": lead_context.get("lead_id") or lead_data.get("lead_id"),
                "email": lead_data.get("email"),
                "name": lead_context.get("name") or f"{lead_data.get('first_name', '')} {lead_data.get('last_name', '')}".strip(),
                "franchisor_id": lead_context.get("franchisor_id") or lead_data.get("franchisor_id"),
                "phone": phone_number
            })
            logger.info(f"📧 ENRICHED CONTEXT: Added email {lead_data.get('email')} for lead {phone_number}")
        
        return lead_context

    def store_lead_context(self, phone_number: str, context: AndyLeadContext, ttl: int = 15552000):  # 6 months
        """Store Andy's lead context in Redis using consistent key pattern"""
        if not self.redis_client:
            return False

        try:
            # Use consistent key pattern with SMS Memory Manager
            key = f"sms_lead_context:{phone_number}"
            context["last_updated"] = datetime.now().isoformat()
            self.redis_client.setex(key, ttl, json.dumps(context))
            return True
        except Exception as e:
            logger.error("Error storing lead context", error=str(e))
            return False

    def clear_lead_context(self, phone_number: str) -> bool:
        """Clear lead context from Redis to restart conversation"""
        if not self.redis_client:
            return False

        try:
            key = f"sms_lead_context:{phone_number}"
            self.redis_client.delete(key)
            logger.info("Lead context cleared for conversation restart", phone=phone_number)
            return True
        except Exception as e:
            logger.error("Error clearing lead context", error=str(e))
            return False
    
    def get_conversation_history(self, phone_number: str, limit: int = 25) -> List[Dict[str, Any]]:
        """Get recent conversation history from Redis"""
        if not self.redis_client:
            return []

        try:
            key = f"andy_conversation:{phone_number}"
            messages = self.redis_client.lrange(key, 0, limit - 1)
            return [json.loads(msg) for msg in messages]
        except Exception as e:
            logger.error("Error getting conversation history", error=str(e))
            return []

    def add_to_conversation_history(self, phone_number: str, message: Dict[str, Any]):
        """Add message to conversation history in Redis with enhanced metadata"""
        if not self.redis_client:
            return False

        try:
            # Enhance message with metadata
            enhanced_message = {
                **message,
                "timestamp": datetime.now().isoformat(),
                "message_id": str(uuid.uuid4()),
                "phone_number": phone_number
            }

            key = f"andy_conversation:{phone_number}"
            self.redis_client.lpush(key, json.dumps(enhanced_message))
            self.redis_client.ltrim(key, 0, 49)  # Keep last 50 messages
            self.redis_client.expire(key, 15552000)  # 6 months

            # Also store in daily conversation log for analytics
            daily_key = f"andy_daily_conversations:{datetime.now().strftime('%Y-%m-%d')}"
            self.redis_client.lpush(daily_key, json.dumps(enhanced_message))
            self.redis_client.expire(daily_key, 2592000)  # 30 days

            return True
        except Exception as e:
            logger.error("Error adding to conversation history", error=str(e))
            return False

    def get_conversation_summary(self, phone_number: str) -> Dict[str, Any]:
        """Get conversation summary and analytics"""
        if not self.redis_client:
            return {}

        try:
            messages = self.get_conversation_history(phone_number, 50)
            if not messages:
                return {"total_messages": 0, "status": "new_lead"}

            user_messages = [msg for msg in messages if msg.get("sender") == "user"]
            andy_messages = [msg for msg in messages if msg.get("sender") == "assistant"]

            # Calculate conversation metrics
            total_user_chars = sum(len(msg.get("message", "")) for msg in user_messages)
            avg_response_length = total_user_chars / max(len(user_messages), 1)

            # Determine conversation status
            last_message = messages[0] if messages else {}
            last_sender = last_message.get("sender", "unknown")

            if last_sender == "user":
                status = "awaiting_andy_response"
            elif last_sender == "assistant":
                status = "awaiting_user_response"
            else:
                status = "unknown"

            return {
                "total_messages": len(messages),
                "user_messages": len(user_messages),
                "andy_messages": len(andy_messages),
                "avg_response_length": round(avg_response_length, 1),
                "last_sender": last_sender,
                "status": status,
                "first_contact": messages[-1].get("timestamp") if messages else None,
                "last_contact": messages[0].get("timestamp") if messages else None
            }
        except Exception as e:
            logger.error("Error getting conversation summary", error=str(e))
            return {}


class AndySMSAssistant:
    """Andy - AI SMS Lead Qualification Specialist for Coochie Hydrogreen"""

    def __init__(self):
        self.memory_manager = AndyMemoryManager()
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.AGENT_TEMPERATURE,
            max_tokens=settings.AGENT_MAX_TOKENS,
            timeout=30,  # 30 second timeout for OpenAI calls
            max_retries=2  # Maximum 2 retries
        )
        self.checkpointer = MemorySaver()
        self.graph = None

        # Check if strict workflow mode is enabled
        import os
        self.strict_workflow_mode = False

        self._build_workflow()

        # Initialize question extraction service
        self._question_extraction_service = None
    
    @property
    def question_extraction_service(self):
        """Lazy load question extraction service"""
        if self._question_extraction_service is None:
            self._question_extraction_service = get_question_extraction_service()
        return self._question_extraction_service

    def _build_workflow(self):
        """Build Andy's LangGraph workflow"""
        workflow = StateGraph(AndyAssistantState)

        # Add Andy's workflow nodes
        workflow.add_node("context_extraction", self._context_extraction_node)
        workflow.add_node("question_extraction", self._question_extraction_node)
        workflow.add_node("determine_next_step", self._determine_next_step_node)
        workflow.add_node("meeting_booking_tools", self._meeting_booking_tools_node)
        workflow.add_node("rag_tools", self._rag_tools_node)
        workflow.add_node("context_management_tools", self._context_management_tools_node)
        workflow.add_node("communication_tools", self._communication_tools_node)
        workflow.add_node("response_generation", self._response_generation_node)
        workflow.add_node("conversation_storage", self._conversation_storage_node)
        workflow.add_node("lead_data_persistence", self._lead_data_persistence_node)

        # Define Andy's autonomous workflow with determine_next_step
        workflow.set_entry_point("context_extraction")
        workflow.add_edge("context_extraction", "question_extraction")
        workflow.add_edge("question_extraction", "determine_next_step")

        # Conditional routing from determine_next_step based on autonomous decisions
        workflow.add_conditional_edges(
            "determine_next_step",
            self._route_based_on_next_step,
            {
                "meeting_booking_tools": "meeting_booking_tools",
                "rag_tools": "rag_tools",
                "context_management_tools": "context_management_tools",
                "communication_tools": "communication_tools",
                "response_generation": "response_generation"
            }
        )

        # Tool nodes route to response generation
        workflow.add_edge("meeting_booking_tools", "response_generation")
        workflow.add_edge("rag_tools", "response_generation")
        workflow.add_edge("context_management_tools", "response_generation")
        workflow.add_edge("communication_tools", "response_generation")

        # Final flow
        workflow.add_edge("response_generation", "conversation_storage")
        workflow.add_edge("conversation_storage", "lead_data_persistence")
        workflow.add_edge("lead_data_persistence", END)

        self.graph = workflow.compile(checkpointer=self.checkpointer)

    # ===== AUTONOMOUS determine_next_step IMPLEMENTATION =====

    async def _determine_next_step_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Central autonomous decision-making node that determines next step"""
        state["execution_path"].append("determine_next_step")
        logger.info(f"this is the state at this stage {state}")

        try:
            # CRITICAL: Check if conversation is complete (meeting booked) FIRST
            lead_context = self.memory_manager.get_lead_context(state["phone_number"])
            if (state.get("conversation_complete") or 
                (lead_context and (
                    (isinstance(lead_context, dict) and lead_context.get("meeting_scheduled")) or
                    (hasattr(lead_context, 'meeting_scheduled') and lead_context.meeting_scheduled)
                ))):
                logger.info("🔚 CONVERSATION COMPLETE: Meeting already booked, routing to post-booking response")
                state["current_stage"] = AndyWorkflowStages.CONFIRMATION
                state["next_action"] = "post_booking_response"
                state["conversation_complete"] = True
                return state
            
            # CRITICAL: Check for hello messages SECOND before any complex AI analysis
            # Pass context to make hello detection more intelligent
            conversation_history = state.get("conversation_history", [])
            current_stage = state.get("current_stage")
            
            if self._is_hello_message(state["message"], conversation_history, current_stage):
                logger.info("🎯 HELLO DETECTED: Direct routing to hello response")
                state["current_stage"] = AndyWorkflowStages.INTRODUCTION
                state["next_action"] = "send_hello_response"
                state["intent_analysis"] = {"primary_intent": "greeting", "confidence": 1.0}
                return state

            logger.info("🎯 DETERMINE_NEXT_STEP: Starting autonomous decision making")

            # Step 1: Comprehensive context analysis
            context_analysis = await self._analyze_full_context(state)
            readiness_info = context_analysis.get('readiness_score', {})
            readiness_score = readiness_info.get('score', 0) if isinstance(readiness_info, dict) else 0
            logger.info(f"🎯 Context analysis complete - readiness: {readiness_score:.2f}")

            # Step 2: Intent and action prediction
            intent_analysis = await self._predict_intent_and_actions(
                message=state["message"],
                context=context_analysis,
                history=state.get("conversation_history", [])
            )
            primary_intent = intent_analysis.get('primary_intent', 'unknown')
            confidence_value = intent_analysis.get('confidence', 0)
            confidence_score = confidence_value if isinstance(confidence_value, (int, float)) else 0
            recommended_tools = intent_analysis.get('recommended_tools', [])
            logger.info(f"🎯 Intent predicted: {primary_intent} (confidence: {confidence_score:.2f})")
            logger.info(f"🔧 DEBUG: AI recommended tools: {recommended_tools}")

            # Step 3: Tool selection and orchestration
            tool_plan = await self._create_autonomous_tool_plan(
                intent=intent_analysis,
                context=context_analysis,
                available_tools=self._get_available_autonomous_tools()
            )
            primary_tools = tool_plan.get('primary_tools', [])
            tools_str = ', '.join(primary_tools) if isinstance(primary_tools, list) else str(primary_tools)
            logger.info(f"🎯 Tool plan created: [{tools_str}]")

            # Step 4: Execute tools in optimal sequence
            execution_results = await self._execute_tool_plan(tool_plan, state)
            logger.info(f"🎯 Tool execution complete - success: {execution_results.get('success', False)}")
            logger.info(f"🔧 DEBUG: Tool execution results: {execution_results}")

            # Step 5: Determine next workflow state
            next_state = await self._determine_next_workflow_state(
                execution_results=execution_results,
                current_context=context_analysis
            )
            logger.info(f"🎯 Next state determined: {next_state.get('action')} -> {next_state.get('stage')}")

            # Step 6: Update state with results
            state.update({
                "tool_execution_results": execution_results,
                "next_action": next_state["action"],
                "current_stage": next_state["stage"],
                "response": next_state.get("response"),
                "confidence_score": next_state.get("confidence", 0.0),
                "autonomous_decisions": next_state.get("decisions", [])
            })

            logger.info("✅ DETERMINE_NEXT_STEP: Autonomous decision making complete")
            return state

        except Exception as e:
            logger.error(f"❌ DETERMINE_NEXT_STEP ERROR: {str(e)}")
            # Fallback to default workflow
            state.update({
                "next_action": "general_response",
                "current_stage": AndyWorkflowStages.INTRODUCTION,  # Fallback to introduction stage
                "error": str(e),
                "autonomous_decisions": ["Error occurred, falling back to introduction stage"]
            })
            return state

    async def _analyze_full_context(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Comprehensive context analysis for autonomous decision making"""

        # Get lead context from Redis and enrich with email data
        lead_context = self.memory_manager.get_lead_context(state["phone_number"])
        lead_context = await self.memory_manager._enrich_lead_context_with_email(state["phone_number"], lead_context)

        # Scan conversation history for missed qualification information
        history_extracted_info = await self._scan_conversation_history_for_qualification_info(
            history=state.get("conversation_history", []),
            current_context=lead_context or {}
        )

        # Analyze conversation patterns
        conversation_patterns = await self._analyze_conversation_patterns(
            history=state.get("conversation_history", []),
            phone_number=state["phone_number"]
        )

        # Extract information from current message
        extracted_info = state.get("extracted_info", {})
        
        # Merge history-extracted info with current message info
        merged_extracted_info = {**history_extracted_info, **extracted_info}
        
        # Update state with merged extracted info so all subsequent functions use it
        state["extracted_info"] = merged_extracted_info

        # Calculate engagement metrics
        engagement_metrics = await self._calculate_engagement_metrics(
            message=state["message"],
            history=state.get("conversation_history", []),
            context=lead_context
        )

        # Predict lead readiness
        readiness_score = await self._predict_lead_readiness(
            context=lead_context,
            patterns=conversation_patterns,
            engagement=engagement_metrics
        )

        return {
            "lead_context": lead_context,
            "conversation_patterns": conversation_patterns,
            "extracted_info": merged_extracted_info,
            "history_extracted_info": history_extracted_info,
            "engagement_metrics": engagement_metrics,
            "readiness_score": readiness_score,
            "current_message": state["message"],
            "phone_number": state["phone_number"]
        }

    async def _predict_intent_and_actions(
        self,
        message: str,
        context: Dict[str, Any],
        history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Predict user intent and optimal actions using AI with Andy's personality and workflow"""

        # Get Andy's role and workflow from system configuration
        andy_role_prompt = self._get_andy_role_prompt()
        
        # Create comprehensive intent analysis prompt
        # Safely format dictionaries to avoid format string errors
        lead_context_str = json.dumps(context.get('lead_context', {}), indent=2)
        engagement_metrics_str = json.dumps(context.get('engagement_metrics', {}), indent=2)
        history_str = json.dumps(history[-5:] if history else [], indent=2)
        
        intent_prompt = f"""{andy_role_prompt}

CURRENT CONVERSATION CONTEXT:
- User Message: "{message}"
- Lead Context: {lead_context_str}
- Engagement Level: {engagement_metrics_str}
- Conversation History (last 5 messages): {history_str}

ANALYSIS REQUIRED:
As Andy, analyze this message and determine:
1. What is the user's primary intent?
2. What actions should Andy take?
3. What tools are needed to respond appropriately?
4. How should Andy respond according to his personality and workflow?

Consider Andy's flexible workflow:
- Can answer questions at any point (price, process, etc.)
- Can handle meeting booking requests immediately
- Can address objections when they arise
- Should always acknowledge what the user shared before moving forward
- Should maintain conversational, friendly tone with "yeah", "so", "okay"

CONTEXT-AWARE INTENT DETECTION:
- If user provides budget amount (like "190K", "$150k", "150 thousand") in qualification_3_budget stage → "qualification_response"
- If user provides motivation (like "extra income", "work-life balance") in qualification_2_motivation stage → "qualification_response"
- If user provides work background (like "I am a PM", "I'm an electrician") in qualification_1_work stage → "qualification_response"
- If user provides specific numbers/dollar amounts in budget context → "qualification_response" NOT "budget_discussion"
- Budget discussions about costs/pricing = "information_request" or "price_inquiry"
- Budget responses providing personal budget amounts = "qualification_response"
- If user responds with affirmative messages like "Yeah", "Yes", "Sure", "Okay", "Sounds good", "Perfect", "Great" → get hint from context and proceed to next step accordingly

TOOL SELECTION GUIDE:
- Use "get_available_slots" when user asks: "available slots", "available times", "when can we meet", "show me times", "what times do you have"
- Use "check_availability" when user asks about a specific time: "is 2pm tomorrow free", "can we meet at 10am"
- Use "book_meeting" when user wants to book a specific time: "book me for 2pm tomorrow", "I want the 10am slot"
- Use "parse_natural_datetime" when user mentions complex time/date: "next Tuesday", "tomorrow afternoon", "day after tomorrow at 3"
- Use "parse_natural_datetime" when user gives time-only messages like "12PM", "3:30 PM" - the tool will use conversation context to determine the date
- Use "get_date_from_day" when user mentions days: "next Monday", "this Friday", "coming Wednesday"
- Use "get_day_from_date" when user provides dates: "September 10th", "2025-09-15"
- Use "rag_query" for franchise questions: "what's the price", "tell me about royalties", "income guarantee"
- Use "get_lead_context" for general chat or qualification progression
- For affirmative responses like "Yeah", "Yes", "Sure", "Okay", "Sounds good", "Perfect", "Great": get hint from context and proceed to next step - use appropriate tool based on current stage (get_lead_context for qualification, book_meeting for scheduling, etc.)

Return ONLY valid JSON:
{{
    "primary_intent": "meeting_booking|information_request|objection_handling|budget_discussion|qualification_question|general_chat|price_inquiry|affirmative_response",
    "specific_intent": "detailed description of what user wants",
    "urgency": "high|medium|low",
    "confidence": 0.0,
    "recommended_tools": ["book_meeting", "check_availability", "get_available_slots", "get_lead_context", "rag_query", "get_franchise_info", "get_day_from_date", "get_date_from_day", "parse_natural_datetime"],
    "immediate_action_required": true/false,
    "andy_response_type": "acknowledge_and_answer|ask_qualification|handle_objection|book_meeting|provide_info",
    "conversation_stage": "introduction|qualification_1_work|qualification_2_motivation|qualification_3_budget|franchise_fee_breakdown|objection_handling|scheduling|confirmation",
    "next_best_action": "specific next action Andy should take"
}}"""

        try:
            # Get AI analysis
            response = await self.llm.ainvoke([SystemMessage(content=intent_prompt)])
            intent_analysis = json.loads(response.content.strip())

            # Pre-process intent for qualification context
            current_stage = context.get('lead_context', {}).get('current_stage', '')
            message_lower = message.lower()

            # Override intent classification for qualification stages
            if current_stage == 'qualification_3_budget':
                # If in budget stage and user provides ANY budget response, treat as qualification response
                budget_indicators = [
                    # Amounts
                    'k', 'thousand', '$', 'dollars',
                    # Flexible confirmations  
                    'about', 'around', 'roughly', 'approximately', 'ballpark', 'thereabouts', 'or so',
                    'close to', 'something like', 'in that range',
                    # Direct confirmations
                    'yes', 'yeah', 'yep', 'i have', 'access to', 'can do', 'that\'s right', 'correct'
                ]
                
                if (any(char.isdigit() for char in message) or 
                    any(indicator in message_lower for indicator in budget_indicators)):
                    intent_analysis['primary_intent'] = 'qualification_response'
                    intent_analysis['specific_intent'] = f'User provided budget response: {message}'
                    intent_analysis['confidence'] = 0.95
                    logger.info(f"🔧 INTENT OVERRIDE: qualification_3_budget + budget response → qualification_response")

            elif current_stage == 'qualification_2_motivation':
                # If in motivation stage and user provides motivation, treat as qualification response
                motivation_keywords = ['income', 'extra', 'work-life', 'balance', 'own boss', 'freedom', 'retirement']
                if any(keyword in message_lower for keyword in motivation_keywords):
                    intent_analysis['primary_intent'] = 'qualification_response'
                    intent_analysis['specific_intent'] = f'User provided motivation: {message}'
                    intent_analysis['confidence'] = 0.95
                    logger.info(f"🔧 INTENT OVERRIDE: qualification_2_motivation + motivation → qualification_response")

            elif current_stage == 'qualification_1_work':
                # If in work stage and user provides work background, treat as qualification response
                work_keywords = ['work', 'job', 'pm', 'manager', 'electrician', 'plumber', 'business']
                if any(keyword in message_lower for keyword in work_keywords) and ('i am' in message_lower or 'i\'m' in message_lower or 'work as' in message_lower):
                    intent_analysis['primary_intent'] = 'qualification_response'
                    intent_analysis['specific_intent'] = f'User provided work background: {message}'
                    intent_analysis['confidence'] = 0.95
                    logger.info(f"🔧 INTENT OVERRIDE: qualification_1_work + work info → qualification_response")

            # Handle affirmative responses - get hint and proceed to next step
            affirmative_keywords = ['yeah', 'yes', 'sure', 'okay', 'sounds good', 'perfect', 'great', 'alright', 'fine', 'good']
            if any(keyword in message_lower for keyword in affirmative_keywords):
                intent_analysis['primary_intent'] = 'affirmative_response'
                intent_analysis['specific_intent'] = f'Affirmative response: {message} - get hint from context and proceed to next step'
                intent_analysis['confidence'] = 0.95
                intent_analysis['recommended_tools'] = ['get_lead_context']  # Default tool, will be overridden based on context
                logger.info(f"🔧 INTENT OVERRIDE: affirmative response → get hint and proceed to next step")

            return intent_analysis
        except Exception as e:
            logger.warning(f"Intent prediction failed, using fallback: {str(e)}")
            # Fallback intent analysis
            return await self._fallback_intent_analysis(message, context)

    async def _create_autonomous_tool_plan(
        self,
        intent: Dict[str, Any],
        context: Dict[str, Any],
        available_tools: List[str]
    ) -> Dict[str, Any]:
        """Create flexible tool execution plan based on Andy's needs"""

        primary_intent = intent.get("primary_intent", "general_chat")
        specific_intent = intent.get("specific_intent", "")
        recommended_tools = intent.get("recommended_tools", [])
        immediate_action = intent.get("immediate_action_required", False)
        andy_response_type = intent.get("andy_response_type", "provide_info")

        # Flexible tool mapping based on Andy's workflow needs
        tool_mapping = {
            "meeting_booking": ["get_available_slots", "check_availability", "book_meeting"],
            "information_request": ["rag_query", "get_franchise_info"],
            "objection_handling": ["rag_query", "get_objection_response"],
            "budget_discussion": ["rag_query", "get_financial_info"],  # For questions about pricing/costs
            "price_inquiry": ["rag_query", "get_franchise_info"],      # For general price questions
            "qualification_response": ["get_lead_context"],           # For providing personal info (work, motivation, budget amounts)
            "qualification_question": ["get_lead_context", "update_context"],
            "general_chat": ["get_lead_context"],
            "affirmative_response": ["get_lead_context"]              # For affirmative responses - get hint and proceed
        }

        # Enhanced tool selection based on specific intent
        selected_tools = []

        # If AI recommended specific tools, use them
        if recommended_tools:
            selected_tools = recommended_tools
        else:
            # Use intent-based mapping
            base_tools = tool_mapping.get(primary_intent, ["get_lead_context"])
            
            # Let the AI agent decide which tools to use autonomously
            # No hardcoded keyword-based tool injection
            
            selected_tools = base_tools

        # Create flexible execution plan
        execution_plan = {
            "primary_tools": selected_tools[:2] if len(selected_tools) > 1 else selected_tools,
            "secondary_tools": selected_tools[2:] if len(selected_tools) > 2 else [],
            "execution_order": selected_tools,
            "parallel_execution": len(selected_tools) > 1 and not immediate_action,
            "timeout_seconds": 30,
            "immediate_action_required": immediate_action,
            "andy_response_type": andy_response_type,
            "success_criteria": self._define_flexible_success_criteria(primary_intent, specific_intent),
            "affirmative_response_handling": primary_intent == "affirmative_response",
            "fallback_strategy": ["get_lead_context", "rag_query"]
        }

        return execution_plan

    async def _execute_tool_plan(
        self,
        tool_plan: Dict[str, Any],
        state: AndyAssistantState
    ) -> Dict[str, Any]:
        """Execute tools according to the plan"""

        execution_results = {
            "success": True,
            "tool_results": {},
            "execution_time": 0,
            "errors": [],
            "partial_success": False
        }

        start_time = datetime.now()

        try:
            # Execute primary tools
            for tool_name in tool_plan["primary_tools"]:
                tool_result = await self._execute_autonomous_tool(
                    tool_name=tool_name,
                    state=state,
                    context=tool_plan
                )
                execution_results["tool_results"][tool_name] = tool_result

                # Check if we should continue with secondary tools
                if not tool_result.get("success", False):
                    execution_results["partial_success"] = True
                    break

            # Execute secondary tools if primary succeeded
            if execution_results["success"]:
                for tool_name in tool_plan["secondary_tools"]:
                    tool_result = await self._execute_autonomous_tool(
                        tool_name=tool_name,
                        state=state,
                        context=tool_plan
                    )
                    execution_results["tool_results"][tool_name] = tool_result

        except Exception as e:
            execution_results["success"] = False
            execution_results["errors"].append(str(e))
            logger.error(f"Tool execution error: {str(e)}")

        execution_results["execution_time"] = (datetime.now() - start_time).total_seconds()

        return execution_results

    async def _determine_next_workflow_state(
        self,
        execution_results: Dict[str, Any],
        current_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Determine the next workflow state based on tool results"""

        tool_results = execution_results.get("tool_results", {})

        # Analyze tool execution outcomes
        successful_tools = [tool for tool, result in tool_results.items() if result.get("success")]
        failed_tools = [tool for tool, result in tool_results.items() if not result.get("success")]

        # CRITICAL: Check for unavailability FIRST before any other processing
        extracted_info = current_context.get("extracted_info", {})
        if extracted_info.get("availability") == "no":
            logger.info("🚫 UNAVAILABILITY DETECTED: User indicated they're not available")
            return {
                "action": "handle_unavailability",
                "stage": AndyWorkflowStages.INTRODUCTION,  # Stay in introduction when unavailable
                "response": None,
                "confidence": 0.95,
                "decisions": ["User indicated unavailability - acknowledge and tell them to text when available"]
            }

        # Determine next action based on outcomes - using only document-defined stages
        next_state = {
            "action": "general_response",
            "stage": AndyWorkflowStages.INTRODUCTION,  # Default to introduction
            "response": None,
            "confidence": 0.6,
            "decisions": []
        }

        # Meeting booking attempted (successful or failed) - route to meeting tools for handling
        if "book_meeting" in tool_results:
            if "book_meeting" in successful_tools:
                # Successful booking
                next_state.update({
                    "action": "meeting_booking_tools",
                    "stage": AndyWorkflowStages.CONFIRMATION,
                    "confidence": 0.95
                })
            else:
                # Failed booking - still route to meeting tools to handle failure
                next_state.update({
                    "action": "meeting_booking_tools",
                    "stage": AndyWorkflowStages.SCHEDULING,
                    "confidence": 0.90
                })

        # Available slots or availability check - move to scheduling stage
        elif any(tool in successful_tools for tool in ["get_available_slots", "check_availability"]):
            next_state.update({
                "action": "meeting_booking_tools",
                "stage": AndyWorkflowStages.SCHEDULING,
                "confidence": 0.90
            })

        # Datetime parsing successful - proceed to booking
        elif "parse_natural_datetime" in successful_tools:
            datetime_result = tool_results["parse_natural_datetime"]
            # Check if we have a valid datetime to book
            if datetime_result.get("datetime") and datetime_result.get("confidence", 0) >= 0.7:
                next_state.update({
                    "action": "meeting_booking_tools", 
                    "stage": AndyWorkflowStages.SCHEDULING,
                    "confidence": 0.95
                })
            else:
                # Low confidence or no datetime - ask for clarification
                next_state.update({
                    "action": "general_response",
                    "stage": AndyWorkflowStages.SCHEDULING,
                    "confidence": 0.6
                })

        # Information retrieved successfully - continue with current qualification stage
        elif any(tool in successful_tools for tool in ["rag_query", "get_franchise_info", "search_documents"]):
            # Determine appropriate stage based on current context
            current_stage = current_context.get("lead_context", {}).get("current_stage", AndyWorkflowStages.INTRODUCTION)
            next_state.update({
                "action": "rag_tools",
                "stage": current_stage,  # Stay in current stage after providing information
                "confidence": 0.90
            })

        # Context updated - determine next qualification stage
        elif "get_lead_context" in successful_tools:
            # Determine next qualification stage based on progress
            lead_context = current_context.get("lead_context", {})
            current_stage = lead_context.get("current_stage", AndyWorkflowStages.INTRODUCTION)

            # CRITICAL: Don't progress through qualification stages if user is unavailable
            if extracted_info.get("availability") == "no":
                logger.info("🚫 BLOCKING QUALIFICATION PROGRESSION: User indicated unavailability")
                next_state.update({
                    "action": "handle_unavailability",
                    "stage": current_stage,  # Stay in current stage when unavailable
                    "response": None,
                    "confidence": 0.90
                })
            else:
                # Progress through qualification stages only if user is available
                if current_stage == AndyWorkflowStages.INTRODUCTION:
                    next_stage = AndyWorkflowStages.QUALIFICATION_1_WORK
                elif current_stage == AndyWorkflowStages.QUALIFICATION_1_WORK:
                    next_stage = AndyWorkflowStages.QUALIFICATION_2_MOTIVATION
                elif current_stage == AndyWorkflowStages.QUALIFICATION_2_MOTIVATION:
                    next_stage = AndyWorkflowStages.QUALIFICATION_3_BUDGET
                elif current_stage == AndyWorkflowStages.QUALIFICATION_3_BUDGET:
                    next_stage = AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN
                elif current_stage == AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN:
                    next_stage = AndyWorkflowStages.SCHEDULING
                else:
                    next_stage = current_stage  # Stay in current stage

                next_state.update({
                    "action": "context_management_tools",
                    "stage": next_stage,
                    "response": None,  # Let AI generate contextual response
                    "confidence": 0.80
                })

        # Add decision reasoning
        next_state["decisions"] = [
            f"Successful tools: {successful_tools}",
            f"Failed tools: {failed_tools}",
            f"Determined action: {next_state['action']}",
            f"Confidence: {next_state['confidence']}"
        ]

        return next_state

    def _route_based_on_next_step(self, state: AndyAssistantState) -> str:
        """Route to appropriate node based on determine_next_step results"""

        next_action = state.get("next_action", "general_response")

        # Define routing logic
        routing_map = {
            "meeting_booking_tools": "meeting_booking_tools",
            "rag_tools": "rag_tools",
            "context_management_tools": "context_management_tools",
            "communication_tools": "communication_tools",
            "send_hello_response": "response_generation",  # Hello responses go to response generation
            "general_response": "response_generation",
            "end_conversation": "response_generation",  # End conversation goes to response generation with completion flag
            "handle_unavailability": "response_generation"  # Unavailability handling goes to response generation
        }

        # Get target node
        target_node = routing_map.get(next_action, "response_generation")

        logger.info(f"🎯 Routing from determine_next_step: {next_action} -> {target_node}")
        return target_node

    # ===== TOOL NODE IMPLEMENTATIONS =====

    async def _meeting_booking_tools_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Handle all meeting-related autonomous actions with Andy's personality"""
        state["execution_path"].append("meeting_booking_tools")

        try:
            tool_results = state.get("tool_execution_results", {})
            logger.info(f"🔧 DEBUG: Full tool_results structure: {tool_results}")
            booking_result = tool_results.get("tool_results", {}).get("book_meeting", {})
            availability_result = tool_results.get("tool_results", {}).get("check_availability", {})
            slots_result = tool_results.get("tool_results", {}).get("get_available_slots", {})
            logger.info(f"🔧 DEBUG: booking_result: {booking_result}")
            logger.info(f"🔧 DEBUG: availability_result: {availability_result}")
            logger.info(f"🔧 DEBUG: slots_result: {slots_result}")

            # Handle successful booking
            # Check if booking_result is a dictionary or BookingResult object
            booking_success = False
            booking_confirmed = False

            if isinstance(booking_result, dict):
                booking_success = booking_result.get("success", False)
                booking_confirmed = booking_result.get("booking_confirmed", False)
            elif hasattr(booking_result, 'success'):
                # It's a BookingResult object
                booking_success = booking_result.success
                booking_confirmed = booking_result.success  # For BookingResult, success implies confirmed

            if booking_success and booking_confirmed:
                state["meeting_data"] = booking_result

                # Mark conversation as complete and set to confirmation stage
                state["current_stage"] = AndyWorkflowStages.CONFIRMATION
                state["conversation_complete"] = True

                # Update lead context to mark meeting as scheduled
                lead_context = self.memory_manager.get_lead_context(state["phone_number"])
                if lead_context:
                    if isinstance(lead_context, dict):
                        lead_context["meeting_scheduled"] = True
                        lead_context["current_stage"] = AndyWorkflowStages.CONFIRMATION
                    else:
                        lead_context.meeting_scheduled = True
                        lead_context.current_stage = AndyWorkflowStages.CONFIRMATION
                    self.memory_manager.store_lead_context(state["phone_number"], lead_context)

            # Handle booking failure due to unavailable slot
            elif isinstance(booking_result, dict) and booking_result.get("suggested_action") == "offer_alternative_times":
                state["current_stage"] = AndyWorkflowStages.SCHEDULING
                # Store booking failure details for response generation
                booking_failure_data = {
                    "requested_time": booking_result.get("requested_time", "that time"),
                    "alternative_times": booking_result.get("alternative_times", []),
                    "error": booking_result.get("error", "Time slot not available")
                }
                state["booking_failure"] = booking_failure_data
                logger.info(f"🔧 DEBUG: Setting booking_failure in state: {booking_failure_data}")

            # Handle need for specific time clarification
            elif isinstance(booking_result, dict) and booking_result.get("suggested_action") == "ask_for_specific_time":
                pass

            # Handle qualification requirement before booking
            elif isinstance(booking_result, dict) and booking_result.get("suggested_action") == "continue_qualification":
                deferred_time = booking_result.get("deferred_booking_time", "")
                missing_info = booking_result.get("missing_info", [])

                # Store the deferred booking time for later
                if deferred_time:
                    state["deferred_booking"] = deferred_time

                # Determine which qualification stage to go to based on missing info
                if "work_background" in missing_info:
                    state["current_stage"] = AndyWorkflowStages.QUALIFICATION_1_WORK
                elif "motivation" in missing_info:
                    state["current_stage"] = AndyWorkflowStages.QUALIFICATION_2_MOTIVATION
                elif "budget" in missing_info:
                    state["current_stage"] = AndyWorkflowStages.QUALIFICATION_3_BUDGET
                else:
                    # Default to work if no specific missing info
                    state["current_stage"] = AndyWorkflowStages.QUALIFICATION_1_WORK

            # Handle availability check results
            elif availability_result.get("success"):
                logger.info(f"🔧 Processing availability result: {availability_result}")

            # Handle available slots request
            elif slots_result.get("success"):
                logger.info(f"🔧 Processing slots result: {slots_result}")
                available_slots = slots_result.get("available_slots", [])
                logger.info(f"🔧 Available slots: {available_slots}")

            # Default meeting response
            else:
                logger.info(f"🔧 DEBUG: Falling back to default response - no conditions matched")
                if isinstance(booking_result, dict):
                    logger.info(f"🔧 DEBUG: booking_result.get('success'): {booking_result.get('success')}")
                else:
                    logger.info(f"🔧 DEBUG: booking_result type: {type(booking_result)}")
                logger.info(f"🔧 DEBUG: availability_result.get('success'): {availability_result.get('success')}")
                logger.info(f"🔧 DEBUG: slots_result.get('success'): {slots_result.get('success')}")

        except Exception as e:
            logger.error(f"Meeting booking tools error: {str(e)}")

        return state

    async def _rag_tools_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Handle all RAG and information retrieval actions with Andy's personality"""
        state["execution_path"].append("rag_tools")

        try:
            tool_results = state.get("tool_execution_results", {})
            rag_results = tool_results.get("tool_results", {})
            user_message = state.get("message", "").lower()

            # Get information from successful RAG tools
            information_parts = []
            for tool_name, result in rag_results.items():
                if result.get("success") and result.get("information"):
                    information_parts.append(result["information"])

            if information_parts:
                combined_info = " ".join(information_parts)
                
                # Let AI generate autonomous response based on information retrieved
                pass
                
                state["rag_response"] = combined_info
            else:
                # No information found, provide helpful response
                pass

        except Exception as e:
            logger.error(f"RAG tools error: {str(e)}")

        return state

    async def _context_management_tools_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Handle all context and lead management actions with qualification progression"""
        state["execution_path"].append("context_management_tools")

        try:
            tool_results = state.get("tool_execution_results", {})
            context_results = tool_results.get("tool_results", {})

            # Get context information
            lead_context = context_results.get("get_lead_context", {})
            current_stage = state.get("current_stage", AndyWorkflowStages.INTRODUCTION)

            # Always try to process extracted information regardless of tool results
            extracted_info = state.get("extracted_info", {})

            # Check if motivation was extracted and update context
            if extracted_info.get("motivation"):
                logger.info(f"🔧 Detected motivation: {extracted_info.get('motivation')} -> {extracted_info.get('motivation_category')}")

                # Get current lead context and update it
                current_context = self.memory_manager.get_lead_context(state["phone_number"])
                logger.info(f"🔧 Context type: {type(current_context)}")
                if current_context:
                    # Handle both object and dictionary cases
                    if isinstance(current_context, dict):
                        # Context is a dictionary - update directly
                        current_context["motivation"] = extracted_info["motivation"]
                        current_context["motivation_details"] = state["message"]
                        current_context["motivation_category"] = extracted_info.get("motivation_category")
                        current_context["current_stage"] = AndyWorkflowStages.QUALIFICATION_3_BUDGET  # Progress to budget stage
                    else:
                        # Context is an object - update attributes
                        current_context.motivation = extracted_info["motivation"]
                        current_context.motivation_details = state["message"]
                        current_context.motivation_category = extracted_info.get("motivation_category")
                        current_context.current_stage = AndyWorkflowStages.QUALIFICATION_3_BUDGET  # Progress to budget stage

                    # Store updated context
                    self.memory_manager.store_lead_context(state["phone_number"], current_context)

                    # Let AI generate autonomous response based on updated context
                    logger.info(f"🔧 Motivation context updated, letting AI generate autonomous response")

            # Check if work background was extracted and update context
            elif extracted_info.get("work_background"):
                logger.info(f"🔧 Detected work: {extracted_info.get('work_background')} -> {extracted_info.get('work_category')}")

                # Get current lead context and update it
                current_context = self.memory_manager.get_lead_context(state["phone_number"])
                logger.info(f"🔧 Work context type: {type(current_context)}")
                if current_context:
                    # Handle both object and dictionary cases
                    if isinstance(current_context, dict):
                        # Context is a dictionary - update directly
                        current_context["work_background"] = extracted_info["work_background"]
                        current_context["work_details"] = state["message"]
                        current_context["work_background_type"] = extracted_info.get("work_category")
                        current_context["current_stage"] = AndyWorkflowStages.QUALIFICATION_2_MOTIVATION  # Progress to motivation stage
                    else:
                        # Context is an object - update attributes
                        current_context.work_background = extracted_info["work_background"]
                        current_context.work_details = state["message"]
                        current_context.work_background_type = extracted_info.get("work_category")
                        current_context.current_stage = AndyWorkflowStages.QUALIFICATION_2_MOTIVATION  # Progress to motivation stage

                    # Store updated context
                    self.memory_manager.store_lead_context(state["phone_number"], current_context)

                    # Let AI generate autonomous response based on updated context
                    logger.info(f"🔧 Work background context updated, letting AI generate autonomous response")

            # Check if budget was extracted and update context
            elif extracted_info.get("budget"):
                logger.info(f"🔧 Detected budget: {extracted_info.get('budget')} -> {extracted_info.get('budget_response')}")

                # Get current lead context and update it
                current_context = self.memory_manager.get_lead_context(state["phone_number"])
                if current_context:
                    # Handle both object and dictionary cases
                    if isinstance(current_context, dict):
                        # Context is a dictionary - update directly
                        current_context["budget"] = extracted_info["budget"]
                        current_context["budget_details"] = state["message"]
                        # Consider both confirming and providing_amount as confirmed budget
                        budget_response = extracted_info.get("budget_response")
                        current_context["budget_confirmed"] = budget_response in ["confirming", "providing_amount"]
                        current_context["current_stage"] = AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN  # Progress to franchise fee stage
                    else:
                        # Context is an object - update attributes
                        current_context.budget = extracted_info["budget"]
                        current_context.budget_details = state["message"]
                        # Consider both confirming and providing_amount as confirmed budget
                        budget_response = extracted_info.get("budget_response")
                        current_context.budget_confirmed = budget_response in ["confirming", "providing_amount"]
                        current_context.current_stage = AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN  # Progress to franchise fee stage

                    # Store updated context
                    self.memory_manager.store_lead_context(state["phone_number"], current_context)

                    # Fetch franchise fee information from RAG system
                    logger.info(f"🔧 Budget context updated, fetching franchise fee information from RAG")
                    
                    # Execute RAG query for franchise fee information
                    rag_state = {
                        "message": "franchise fee information",
                        "current_stage": AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN,
                        "phone_number": state["phone_number"],
                        "lead_id": state.get("lead_id")  # Ensure lead_id is passed for franchisor-scoped RAG
                    }
                    rag_result = await self._execute_rag_query_tool(rag_state)
                    
                    # Store RAG response in state for use in response generation
                    if rag_result.get("success"):
                        state["rag_response"] = rag_result.get("information", "")
                        state["rag_query"] = rag_result.get("query_used", "")
                        logger.info(f"🔧 RAG franchise fee info retrieved: {state['rag_response'][:100]}...")
                    else:
                        logger.warning(f"🔧 RAG query failed: {rag_result.get('error', 'Unknown error')}")
                        state["rag_response"] = None

                    # Let AI generate autonomous response based on updated context
                    logger.info(f"🔧 Budget context updated, letting AI generate autonomous response")

        except Exception as e:
            logger.error(f"Context management tools error: {str(e)}")
            state["response"] = "I'd love to learn more about your interest in franchising."

        return state

    async def _communication_tools_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Handle all communication orchestration actions"""
        state["execution_path"].append("communication_tools")

        try:
            # For now, this is a placeholder for future communication tools
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            state["response"] = f"I'll follow up with you soon with more information about this opportunity."

        except Exception as e:
            logger.error(f"Communication tools error: {str(e)}")
            state["response"] = "I'll be in touch soon with more information."

        return state

    # ===== ANDY'S ROLE AND PERSONALITY =====

    def _get_andy_role_prompt(self) -> str:
        """Get Andy's complete role, personality, and workflow prompt - exactly as per document"""
        return """
AI SMS Role:

You are Andy, Lead Qualification Specialist, with over 5 years of experience in qualifying leads who enquire expressing interest in purchasing the franchise. 

Your role is to qualify the leads based on questions we share with you and at the same time answer their questions with the aim for them to book a call with us. 

You are considered as an expert in the field of Australian Franchise industry and all the information we feed about a franchise model needs to be communicated in the simplest way possible without creating a confusion. 

When generating responses, aim for a conversational tone. Think of how you might talk to a friend in a relaxed setting but respectful. Use common colloquial expressions such as "so", "yeah", and "okay". Instead, focus on making the dialogue flow naturally, using contractions like "you're" instead of "you are", and inserting filler words occasionally to mimic natural speech patterns. Strive to make responses sound warm and approachable, using everyday vocabulary and sentence structures.

When the lead responds, always start your next sentence as you're acknowledging what they shared and then get on to the next topic or qualification question. For example if lead mentions "I work as a Marketing Manager", your response to that could be "That sounds interesting, what attracted you to this opportunity?". Also you can use terms such as "Great", "That make sense", "Sounds interesting" as per the context of the chat.

IMPORTANT: When the lead responds with affirmative messages like "Yeah", "Yes", "Sure", "Okay", "Sounds good", "Perfect", "Great", or similar positive confirmations, immediately get a hint about what they're agreeing to and dive right into the next step accordingly. Don't ask for clarification - use the context to determine the next logical action and proceed confidently.

Work-flow:
• Introduction
• Qualification questions
• Objections
• Scheduling logic
• Confirmation

Introduction: G'day {name}! Thanks for your interest in Coochie Hydrogreen. Do you have a minute to chat about this franchise opportunity?

>If no response for 3 hours, send follow-up sms:
G'day! Just following up on your interest in Coochie Hydrogreen. Got a minute to chat about this franchise opportunity? 

>If reply is NO: No problem, what's the best time to connect today or possibly tomorrow? 
>If reply is NO with the proposed time:
No problem, I'll lock this in my calendar to touch base at {proposed time}. Talk soon! 

>if reply is YES
Qualification 1: Work background Great, thanks for your inquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?

IMPORTANT WORKFLOW RULE: When lead responds with "Yeah", "Yes", "Sure", "Okay", "Sounds good", or similar affirmative responses, immediately get a hint about the current context and dive right into the next logical step without asking for clarification. Use the conversation context to determine what they're agreeing to and proceed accordingly.
Based on the response, you will have to figure if they are coming from a corporate background (like office work such as project manager, accountant, HR manager, Marketing Manager or any role that doesn't require working on the tools) or Trades background (such as plumber, electrician (sparky), handyman or any other trades where they are on tools)

Qualification 2: Motivation
>If they come from corporate background: Ask "what made you enquire about this franchise opportunity given you come from a different background?"   
>If they come from trades background (such as plumber, electrician (sparky), handyman, landscaping or any other trades where they are working on tools): Ask "what made you enquire about this franchise opportunity?"   
>If it's not clear from their response (only if they come from trades background): Ask "Do you work for yourself (sole trader) or working as an employee?"  

Understand their motivation and categorise it from below options: 
• Be your own boss: they just want to work for themselves
• Sick of corporate (tired of office job or working at the desk)
• Flexible lifestyle (their hours are too long and it is not allowing to spend time with family)
• Ease into retirement (they may be of the age 50+ and need something easy with aim to retire in next few years) 
• Legacy for children (would like to pass the business to their kids)
• Redundancy (Their job has been made redundant and they don't want to do a job)
• Diversification (They need a franchise to diversify from what they do currently)
• Relocation (if they are relocating to a new location and looking to buy business there)

Qualification 3: (Budget) 
Question: You mentioned that you have access to a {Zoho CRM field "funds to invest?"} budget to invest in the business. Is that true?  
>If response is No, How much funds do you have access to invest?  
>If they still don't provide amount to invest, We'll walk you through the financials and real examples from our 70 franchise partners, so rest assured you'll have a clear picture of the ROI. I just need to confirm you have funds to get started—that's why I asked.
>If response is yes (or amount provided post above message), Thanks for confirming. Total Franchise Fee for Coochie Hydrogreen is $120K(ex. GST) however we have an option to pay 50% in advance and 50% after 12 months. Also, there is a finance option for you. How does that sound? 

Scheduling Logic
>If response is "Good" or anything similar Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at {time} on {day}? (Provide couple of options)

>When lead responds with affirmative messages like "Yeah", "Yes", "Sure", "Okay", "Sounds good", "Perfect", "Great" to scheduling questions, immediately get a hint about which time slot they prefer and proceed with booking that slot. Don't ask for additional confirmation unless the preference is unclear.

Confirmation:
>If lead picks up a day and time, "Thanks for the confirmation. Looking forward to speaking with you."

>When lead responds with affirmative messages like "Yeah", "Yes", "Sure", "Okay", "Sounds good", "Perfect", "Great" to confirmation questions, immediately get a hint about the confirmed booking and complete the process. Mark the meeting as scheduled and provide confirmation details.

Objection handling: 
>If objection is "I don't see a value (or it is too expensive)" I understand however we are yet to walk you through the business potential. There is a reason we're Australia's largest lawn care company. Let's walk you through all on the call. 

>I haven't seen you around (or haven't heard about you) Fair enough. Coochie has been operating for over 30 years and is a very prominent brand in the NSW & QLD and now we're looking to grow in other states due to demand. 

>Who does marketing and lead generation? We will provide you lead guarantee during the initial period along with income guarantee in the first year. However the expectation is you are also conducting business development activities in your local area. 

>I don't have any experience in this field. We will provide you comprehensive training for 4 weeks but also you will receive ongoing support. You will also get to spend time with our existing franchise partners. Rest assured, you won't be left alone. 

>I don't see this business generating good income (or will replace my income) A totally fair question. Some of our franchise partners, who follow our systems and processes very well, earn over $200K net, but as always, your results will depend on your effort and local conditions. We'll go through the 3-year projections together in our meeting so you can see what's possible.

> What's the income guarantee? In the first year, we guarantee $60K net income - that is after all your expenses. 

> What's the royalty fee? (Sometimes leads confuse this with the initial franchise fee. Royalty is monthly fee while franchise fee is an initial investment) 10% royalty and 3% marketing fund so total is 13% of your gross sales.

>When you answer the objections and if you don't get response from lead, "Does that make sense? I'm happy to answer any questions you may have. "

>If during the qualification process, lead doesn't respond "Just checking if you're still keen to explore this opportunity? Happy to keep the chat going and set up a quick call to walk you through the details. How does that sound?"  
>If response on above is "Yes", Go back to the qualification question the lead went silent on.   
>If response on above is "No" "Any particular reason you wouldn't like to continue?"

IMPORTANT: You can handle ANY question at ANY point in the conversation. If someone asks about price at the beginning, answer it. If they want to book a meeting immediately, help them book it. Always acknowledge what they shared first, then provide the information or take the action they requested.

AFFIRMATIVE RESPONSE PROTOCOL: When the lead responds with "Yeah", "Yes", "Sure", "Okay", "Sounds good", "Perfect", "Great", or any similar positive confirmation, immediately:
1. Get a hint from the conversation context about what they're agreeing to
2. Dive right into the next logical step without asking for clarification
3. Use the context to determine the appropriate action and proceed confidently
4. Maintain the conversational flow by acknowledging their agreement and moving forward
"""

    # ===== HELPER METHODS =====

    def _get_available_autonomous_tools(self) -> List[str]:
        """Get list of available autonomous tools"""
        return [
            "book_meeting",
            "check_availability", 
            "get_available_slots",
            "get_lead_context",
            "rag_query",
            "get_franchise_info",
            "search_documents",
            "update_context",
            "get_financial_info"
        ]

    async def _execute_autonomous_tool(
        self,
        tool_name: str,
        state: AndyAssistantState,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute an autonomous tool with proper error handling"""
        
        logger.info(f"🔧 DEBUG: Executing tool: {tool_name}")
        try:
            if tool_name == "book_meeting":
                result = await self._execute_book_meeting_tool(state)
            elif tool_name == "check_availability":
                result = await self._execute_check_availability_tool(state)
            elif tool_name == "get_available_slots":
                result = await self._execute_get_available_slots_tool(state)
            elif tool_name == "get_lead_context":
                result = await self._execute_get_lead_context_tool(state)
            elif tool_name == "rag_query":
                result = await self._execute_rag_query_tool(state)
            elif tool_name == "get_franchise_info":
                result = await self._execute_get_franchise_info_tool(state)
            elif tool_name == "update_context":
                result = await self._execute_update_context_tool(state)
            elif tool_name == "get_day_from_date":
                result = await self._execute_get_day_from_date_tool(state)
            elif tool_name == "get_date_from_day":
                result = await self._execute_get_date_from_day_tool(state)
            elif tool_name == "parse_natural_datetime":
                result = await self._execute_parse_natural_datetime_tool(state)
            else:
                result = {"success": False, "error": f"Unknown tool: {tool_name}"}

            return result

        except Exception as e:
            logger.error(f"Tool execution failed: {tool_name} - {str(e)}")
            return {"success": False, "error": str(e)}

    async def _execute_book_meeting_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Execute meeting booking tool with intelligent time extraction and qualification checking"""
        try:
            # Initialize Zoho service first to avoid variable scope issues
            from app.services.zoho_bookings_service import ZohoBookingsService, BookingResult
            zoho_service = ZohoBookingsService()
            
            # Get lead context for booking details
            lead_context = self.memory_manager.get_lead_context(state["phone_number"])
            
            # CHECK QUALIFICATION BEFORE BOOKING
            current_stage = state.get("current_stage", "introduction")
            
            # Use existing qualification check method to determine if user is qualified
            is_qualified = self._is_qualification_complete(state, lead_context or {})
            
            if not is_qualified:
                # Extract meeting details for deferred booking
                meeting_details = await self._extract_meeting_details_with_ai(state)
                deferred_time = ""
                
                if meeting_details and meeting_details.get("datetime"):
                    deferred_time = meeting_details["datetime"].strftime("%A, %B %d at %I:%M %p")
                
                # Determine what's actually missing
                missing_info = []
                if lead_context:
                    if not (lead_context.get("work_background") or lead_context.get("work_details") or lead_context.get("employment_type")):
                        missing_info.append("work_background")
                    if not (lead_context.get("motivation") or lead_context.get("motivation_details")):
                        missing_info.append("motivation")
                    if not (lead_context.get("budget_confirmed") or lead_context.get("budget")):
                        missing_info.append("budget")
                else:
                    missing_info = ["work_background", "motivation", "budget"]
                
                return {
                    "success": False,
                    "error": "Qualification incomplete",
                    "suggested_action": "continue_qualification",
                    "deferred_booking_time": deferred_time,
                    "missing_info": missing_info
                }
            
            # Use the parse_natural_datetime tool to extract meeting details with context
            logger.info(f"🔧 Using parse_natural_datetime tool for booking")
            datetime_result = await self._execute_parse_natural_datetime_tool(state)
            
            if datetime_result.get("success") and datetime_result.get("datetime"):
                # Convert ISO string back to datetime object
                from datetime import datetime
                import dateutil.parser
                meeting_datetime = dateutil.parser.isoparse(datetime_result["datetime"])
                
                meeting_details = {
                    "datetime": meeting_datetime,
                    "confidence": datetime_result.get("confidence", 0.8),
                    "source": datetime_result.get("source", "tool_parsing")
                }
                
                logger.info(f"🎯 Tool parsed datetime: {meeting_datetime} (source: {meeting_details['source']})")
                # Check availability using the same extracted time
                from app.services.zoho_bookings_service import ZohoBookingsService
                zoho_service_check = ZohoBookingsService()
                
                is_available = await zoho_service_check.is_time_slot_available(
                    target_datetime=meeting_details["datetime"],
                    service_type="lead_meeting"
                )
                
                if is_available:

                    # Use existing lead context for booking details
                    lead_id = lead_context.get("lead_id") if lead_context else state.get("lead_id")
                    
                    # Double-check availability right before booking to avoid race conditions
                    final_availability = await zoho_service.is_time_slot_available(meeting_details["datetime"])
                    if not final_availability:
                        logger.warning(f"🔄 Slot became unavailable during booking process: {meeting_details['datetime']}")
                        # Get fresh alternatives
                        alternative_result = await zoho_service.get_next_available_slots(max_days_ahead=7, max_slots=3)
                        alternative_slots = [slot.start_time.strftime("%A, %B %d at %I:%M %p") for slot in alternative_result]

                        return {
                            "success": False,
                            "error": "Time slot became unavailable",
                            "suggested_action": "offer_alternative_times",
                            "requested_time": meeting_details["datetime"].strftime("%A, %B %d at %I:%M %p"),
                            "alternative_times": alternative_slots
                        }

                    booking_result = await zoho_service.book_appointment_for_lead(
                        lead_id=lead_id,
                        preferred_start_time=meeting_details["datetime"],
                        timezone="Australia/Sydney",
                        service_type="lead_meeting",
                        phone_number=state["phone_number"]
                    )

                    if booking_result.success:
                        return {
                            "success": True,
                            "meeting_time": meeting_details["datetime"].strftime("%A, %B %d at %I:%M %p"),
                            "meeting_id": booking_result.booking_id,
                            "join_url": booking_result.meeting_link,
                            "booking_confirmed": True
                        }
                    else:
                        # Get alternative times for the failed booking
                        logger.warning(f"🔄 Booking failed even though slot was available - getting fresh alternatives")
                        alternative_result = await zoho_service.get_next_available_slots(max_days_ahead=7, max_slots=3)
                        alternative_slots = [slot.start_time.strftime("%A, %B %d at %I:%M %p") for slot in alternative_result]

                        return {
                            "success": False,
                            "error": "Booking failed",
                            "suggested_action": "offer_alternative_times",
                            "requested_time": meeting_details["datetime"].strftime("%A, %B %d at %I:%M %p"),
                            "alternative_times": alternative_slots
                        }
                else:
                    # Slot not available, suggest alternatives
                    logger.info(f"🔄 Slot not available, getting fresh alternatives")
                    alternative_result = await zoho_service.get_next_available_slots(max_days_ahead=7, max_slots=3)
                    alternative_slots = [slot.start_time.strftime("%A, %B %d at %I:%M %p") for slot in alternative_result]
                    
                    return {
                        "success": False,
                        "error": "Time slot not available",
                        "suggested_action": "offer_alternative_times",
                        "requested_time": meeting_details["datetime"].strftime("%A, %B %d at %I:%M %p"),
                        "alternative_times": alternative_slots
                    }
            else:
                # Could not extract specific time, ask for clarification
                error_message = datetime_result.get("error", "Could not extract specific meeting time")
                logger.warning(f"🔄 DateTime parsing failed: {error_message}")
                
                return {
                    "success": False,
                    "error": error_message,
                    "suggested_action": "ask_for_specific_time",
                    "extracted_info": datetime_result,
                    "context_day": datetime_result.get("found_context_day"),
                    "suggestion": datetime_result.get("suggestion")
                }

        except Exception as e:
            logger.error(f"Meeting booking error: {str(e)}")
            # If we can't get alternatives, provide a helpful fallback response
            return {
                "success": False, 
                "error": str(e), 
                "suggested_action": "offer_alternative_times",
                "requested_time": "that time",
                "alternative_times": []  # Empty list triggers fallback message
            }

    async def _execute_get_lead_context_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Execute lead context retrieval tool"""
        try:
            context = self.memory_manager.get_lead_context(state["phone_number"])
            return {
                "success": True,
                "context_data": context or {},
                "has_existing_context": context is not None
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _execute_rag_query_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Execute RAG query tool using franchisor-scoped RAG"""
        try:
            # Use new franchisor-scoped RAG system
            from app.services.rag.andy_rag_service import andy_rag_service

            # Determine query based on context
            query = state["message"]
            current_stage = state.get("current_stage", "")
            
            # If we're in franchise fee breakdown stage, query for fee information
            if current_stage == "franchise_fee_breakdown":
                query = "What is the franchise fee? What are the payment options and financing available? What is the total investment required?"
            
            # Get lead_id from state
            lead_id = state.get("lead_id")
            if not lead_id:
                return {"success": False, "error": "No lead_id available for RAG query"}
            
            result = await andy_rag_service.answer_lead_question(
                lead_id=lead_id,
                user_query=query,
                top_k=6,
                k_a=4,
                min_score=0.3,
                temperature=0.1  # Low temperature for factual information
            )

            return {
                "success": result.get("success", False),
                "information": result.get("answer", ""),
                "sources": result.get("contexts", []),
                "query_used": query,
                "franchisor_id": result.get("franchisor_id"),
                "franchisor_name": result.get("franchisor_name"),
                "used_fallback": result.get("used_fallback", False)
            }
        except Exception as e:
            logger.error(f"RAG query failed: {e}")
            return {"success": False, "error": str(e)}

    def _define_flexible_success_criteria(self, primary_intent: str, specific_intent: str) -> Dict[str, Any]:
        """Define flexible success criteria based on intent and context"""
        base_criteria = {
            "meeting_booking": {"required_tools": ["check_availability"], "min_confidence": 0.8},
            "information_request": {"required_tools": ["rag_query"], "min_confidence": 0.7},
            "objection_handling": {"required_tools": ["rag_query"], "min_confidence": 0.6},
            "price_inquiry": {"required_tools": ["rag_query"], "min_confidence": 0.7},
            "qualification_question": {"required_tools": ["get_lead_context"], "min_confidence": 0.6},
            "general_chat": {"required_tools": ["get_lead_context"], "min_confidence": 0.5}
        }
        
        criteria = base_criteria.get(primary_intent, {"min_confidence": 0.5})
        
        # Enhance criteria based on specific intent
        if "book" in specific_intent.lower() or "schedule" in specific_intent.lower():
            criteria["required_tools"] = ["check_availability", "book_meeting"]
            criteria["min_confidence"] = 0.9
        elif "price" in specific_intent.lower() or "cost" in specific_intent.lower():
            criteria["required_tools"] = ["rag_query"]
            criteria["min_confidence"] = 0.8
            
        return criteria

    def _define_success_criteria(self, primary_intent: str) -> Dict[str, Any]:
        """Define success criteria for different intents (legacy method)"""
        return self._define_flexible_success_criteria(primary_intent, "")

    async def _analyze_conversation_patterns(
        self,
        history: List[Dict[str, Any]],
        phone_number: str
    ) -> Dict[str, Any]:
        """Analyze conversation patterns for autonomous decision making"""
        if not history:
            return {"engagement_level": "new", "message_count": 0, "avg_length": 0}

        user_messages = [msg for msg in history if msg.get("sender") == "user"]
        avg_length = sum(len(msg.get("message", "")) for msg in user_messages) / max(len(user_messages), 1)

        engagement_level = "low"
        if avg_length > 50:
            engagement_level = "high"
        elif avg_length > 20:
            engagement_level = "medium"

        return {
            "engagement_level": engagement_level,
            "message_count": len(user_messages),
            "avg_length": avg_length,
            "last_message_time": history[-1].get("timestamp") if history else None
        }

    async def _calculate_engagement_metrics(
        self,
        message: str,
        history: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate engagement metrics"""
        message_length = len(message)
        has_questions = "?" in message
        urgency_indicators = ["urgent", "asap", "soon", "quickly", "immediately"]

        urgency_score = 1.0 if any(word in message.lower() for word in urgency_indicators) else 0.5

        return {
            "message_length": message_length,
            "has_questions": has_questions,
            "urgency_score": urgency_score,
            "conversation_depth": len(history) if history else 0
        }

    async def _predict_lead_readiness(
        self,
        context: Optional[Dict[str, Any]],
        patterns: Dict[str, Any],
        engagement: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Predict lead readiness score"""
        if not context:
            return {"score": 0.3, "level": "low", "factors": ["No existing context"]}

        score = 0.5  # Base score

        # Increase score based on engagement
        if patterns.get("engagement_level") == "high":
            score += 0.2
        elif patterns.get("engagement_level") == "medium":
            score += 0.1

        # Increase score based on qualification progress
        if context.get("current_stage") in ["qualification_3_budget", "franchise_fee_breakdown"]:
            score += 0.2

        # Increase score based on meeting interest
        if context.get("meeting_interest"):
            score += 0.1

        level = "low"
        if score >= 0.8:
            level = "high"
        elif score >= 0.6:
            level = "medium"

        return {
            "score": min(score, 1.0),
            "level": level,
            "factors": ["Engagement", "Qualification progress", "Meeting interest"]
        }

    async def _fallback_intent_analysis(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback intent analysis when AI fails"""
        message_lower = message.lower()

        # Simple keyword-based intent detection
        if any(word in message_lower for word in ["meeting", "call", "schedule", "book", "appointment"]):
            primary_intent = "meeting_booking"
            tools = ["book_meeting", "check_availability"]
        elif any(word in message_lower for word in ["what", "how", "tell me", "explain", "about"]):
            primary_intent = "information_request"
            tools = ["rag_query", "get_franchise_info"]
        elif any(word in message_lower for word in ["no", "can't", "don't", "expensive", "worried"]):
            primary_intent = "objection_handling"
            tools = ["rag_query", "get_objection_response"]
        else:
            primary_intent = "general_chat"
            tools = ["get_lead_context"]

        return {
            "primary_intent": primary_intent,
            "secondary_intents": [],
            "urgency": "medium",
            "confidence": 0.6,
            "recommended_tools": tools,
            "action_sequence": tools,
            "expected_outcomes": ["Gather information", "Provide response"]
        }

    async def _extract_meeting_details_with_ai(self, state: AndyAssistantState) -> Optional[Dict[str, Any]]:
        """Extract meeting details using the parse_natural_datetime tool"""
        try:
            # Use the parse_natural_datetime tool for context-aware parsing
            datetime_result = await self._execute_parse_natural_datetime_tool(state)
            
            if datetime_result.get("success") and datetime_result.get("datetime"):
                # Convert ISO string back to datetime object
                from datetime import datetime
                import dateutil.parser
                meeting_datetime = dateutil.parser.isoparse(datetime_result["datetime"])
                
                from app.utils.datetime_parser import DateTimeParser
                parser = DateTimeParser()
                
                return {
                    "has_meeting_request": True,
                    "datetime": meeting_datetime,
                    "date_mentioned": parser.get_day_from_date(meeting_datetime),
                    "time_mentioned": meeting_datetime.strftime("%I:%M %p").lower(),
                    "duration": 30,
                    "urgency": "medium",
                    "specific_time_given": True,
                    "confidence": datetime_result.get('confidence', 0.8),
                    "source": datetime_result.get('source', 'tool_parsing'),
                    "alternatives": datetime_result.get('alternatives', [])
                }
            
            # If tool failed, return basic meeting details without specific time
            logger.info(f"🔧 parse_natural_datetime tool failed: {datetime_result.get('error', 'Unknown error')}")
            
            # Check if this looks like a meeting request
            message_lower = state["message"].lower()
            has_meeting_request = any(word in message_lower for word in [
                "meeting", "call", "schedule", "book", "appointment", "chat", "talk", 
                "yes please book", "set up", "arrange"
            ])
            
            if has_meeting_request:
                return {
                    "has_meeting_request": True,
                    "datetime": None,
                    "date_mentioned": "",
                    "time_mentioned": "",
                    "duration": 30,
                    "urgency": "medium",
                    "specific_time_given": False,
                    "confidence": 0.6,
                    "source": "fallback_detection",
                    "context_day": datetime_result.get("found_context_day"),
                    "needs_time_clarification": True
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Meeting details extraction failed: {str(e)}")
            # Fallback extraction
            message_lower = state["message"].lower()
            has_meeting_request = any(word in message_lower for word in [
                "meeting", "call", "schedule", "book", "appointment", "chat", "talk"
            ])
            
            return {
                "has_meeting_request": has_meeting_request,
                "datetime": None,
                "specific_time_given": False,
                "urgency": "medium",
                "source": "fallback"
            }

    async def _execute_check_availability_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Check availability for meeting booking"""
        try:
            # Extract requested time
            meeting_details = await self._extract_meeting_details_with_ai(state)
            
            if not meeting_details or not meeting_details.get("datetime"):
                return {
                    "success": True,
                    "slot_available": False,
                    "message": "No specific time requested",
                    "alternative_slots": self._get_default_available_slots()
                }
            
            requested_time = meeting_details["datetime"]
            
            # Use existing Zoho service to check availability
            from app.services.zoho_bookings_service import ZohoBookingsService
            zoho_service = ZohoBookingsService()
            
            # Check if the requested slot is available
            is_available = await zoho_service.is_time_slot_available(
                target_datetime=requested_time,
                service_type="lead_meeting"
            )
            
            if is_available:
                return {
                    "success": True,
                    "slot_available": True,
                    "requested_time": requested_time.strftime("%A, %B %d at %I:%M %p"),
                    "message": "Slot is available"
                }
            else:
                # Get alternative slots
                alternative_slots = await zoho_service.get_next_available_slots(
                    max_days_ahead=7,
                    max_slots=3
                )
                
                # Format alternative slots
                formatted_alternatives = []
                if alternative_slots:
                    for slot in alternative_slots[:3]:
                        if hasattr(slot, 'start_time'):
                            formatted_alternatives.append(slot.start_time.strftime("%A, %B %d at %I:%M %p"))
                        elif isinstance(slot, dict) and 'start_time' in slot:
                            start_time = slot['start_time']
                            if hasattr(start_time, 'strftime'):
                                formatted_alternatives.append(start_time.strftime("%A, %B %d at %I:%M %p"))
                
                return {
                    "success": True,
                    "slot_available": False,
                    "requested_time": requested_time.strftime("%A, %B %d at %I:%M %p"),
                    "alternative_slots": formatted_alternatives,
                    "message": "Requested slot not available"
                }
                
        except Exception as e:
            logger.error(f"Availability check failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "alternative_slots": self._get_default_available_slots()
            }

    async def _execute_get_available_slots_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Get available meeting slots for the user"""
        logger.info("🔧 Executing get_available_slots tool")
        try:
            # Use existing Zoho service to get available slots
            from app.services.zoho_bookings_service import ZohoBookingsService
            zoho_service = ZohoBookingsService()
            
            # Get next available slots for the week
            logger.info("🔧 Calling Zoho service for available slots")
            available_slots = await zoho_service.get_next_available_slots(
                max_days_ahead=7,  # Look 7 days ahead
                max_slots=5        # Return up to 5 slots
            )
            logger.info(f"🔧 Zoho returned {len(available_slots) if available_slots else 0} slots")
            
            if available_slots:
                # Format the slots nicely
                formatted_slots = []
                for slot in available_slots[:5]:  # Show max 5 slots
                    if isinstance(slot, dict) and 'start_time' in slot:
                        start_time = slot['start_time']
                        if hasattr(start_time, 'strftime'):
                            formatted_slots.append(start_time.strftime("%A, %B %d at %I:%M %p"))
                        else:
                            formatted_slots.append(str(start_time))
                    elif hasattr(slot, 'start_time'):
                        # Handle BookingSlot objects
                        start_time = slot.start_time
                        if hasattr(start_time, 'strftime'):
                            formatted_slots.append(start_time.strftime("%A, %B %d at %I:%M %p"))
                        else:
                            formatted_slots.append(str(start_time))
                    else:
                        formatted_slots.append(str(slot))
                
                logger.info(f"🔧 Formatted {len(formatted_slots)} slots: {formatted_slots}")
                return {
                    "success": True,
                    "available_slots": formatted_slots,
                    "message": f"Found {len(formatted_slots)} available slots"
                }
            else:
                # Fallback to default slots
                logger.info("🔧 No slots from Zoho, using default slots")
                default_slots = self._get_default_available_slots()
                return {
                    "success": True,
                    "available_slots": default_slots,
                    "message": "Showing default available times"
                }
                
        except Exception as e:
            logger.error(f"Get available slots failed: {str(e)}")
            # Fallback to default slots
            default_slots = self._get_default_available_slots()
            logger.info(f"🔧 Using fallback default slots: {default_slots}")
            return {
                "success": True,
                "available_slots": default_slots,
                "message": "Showing default available times",
                "error": str(e)
            }

    def _get_default_available_slots(self) -> List[str]:
        """Get default available time slots"""
        from datetime import timedelta
        
        tomorrow = datetime.now() + timedelta(days=1)
        day_after = datetime.now() + timedelta(days=2)
        
        return [
            tomorrow.replace(hour=10, minute=0).strftime("%A, %B %d at %I:%M %p"),
            tomorrow.replace(hour=14, minute=0).strftime("%A, %B %d at %I:%M %p"),
            day_after.replace(hour=10, minute=0).strftime("%A, %B %d at %I:%M %p")
        ]

    async def _execute_get_franchise_info_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        # This tool is a placeholder - actual franchise info should be retrieved from knowledge base
        return {"success": True, "information": ""}

    async def _execute_update_context_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        return {"success": True, "context_updated": True}

    async def _extract_meeting_datetime_from_message(self, state: AndyAssistantState) -> Optional[Dict[str, Any]]:
        """Simple datetime extraction that delegates to agent tools for complex parsing"""
        try:
            from app.utils.datetime_parser import DateTimeParser
            
            message = state.get("message", "").lower().strip()
            parser = DateTimeParser()
            
            # Only try basic explicit datetime parsing
            result = parser.parse_datetime_from_message(state.get("message", ""))
            
            if result and result.get("datetime"):
                logger.info(f"🎯 Direct datetime parsing successful: {result['datetime']}")
                return {
                    "datetime": result["datetime"],
                    "confidence": result.get("confidence", 0.8),
                    "date_mentioned": result.get("date_text", ""),
                    "time_mentioned": result.get("time_text", ""),
                    "specific_time_given": True,
                    "source": "direct_parsing"
                }
            
            # For anything else, let the agent decide which tools to use
            logger.info(f"🔧 Direct parsing failed for '{message}' - letting agent choose appropriate tools")
            return None
                
        except Exception as e:
            logger.error(f"Error extracting datetime: {str(e)}")
            return None
    
    def _extract_time_suggestions_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract time suggestions from assistant's previous messages"""
        try:
            from app.utils.datetime_parser import DateTimeParser
            import re
            from datetime import datetime, timedelta
            
            suggestions = []
            parser = DateTimeParser()
            
            # Look for patterns like "Thursday at 10 AM", "Monday, September 08 at 12:00 PM", or "around 3 PM"
            time_patterns = [
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)(?:\s*,\s*\w+\s+\d{1,2})?\s+at\s+(\d{1,2}(?::\d{2})?\s*(?:am|pm))',  # Day with optional date at time
                r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\s*,?\s*\w*\s*\d{0,2}\s*at\s*(\d{1,2}(?::\d{2})?\s*(?:am|pm))',  # More flexible day at time
                r'(\d{1,2}(?::\d{2})?\s*(?:am|pm))',  # Just time
                r'(morning|afternoon|evening)',  # Time periods
                r'this\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\s*at\s*(\d{1,2}(?::\d{2})?\s*(?:am|pm))',  # "this Wednesday at 12 PM"
            ]
            
            for pattern in time_patterns:
                matches = re.finditer(pattern, text.lower())
                for match in matches:
                    # Try to parse this into an actual datetime
                    match_text = match.group(0)
                    logger.info(f"🔍 Regex match found: '{match_text}' for pattern: {pattern}")

                    result = parser.parse_datetime_from_message(match_text)

                    if result and result.get("datetime"):
                        logger.info(f"🔍 Datetime parser result: {result}")
                        suggestions.append({
                            "datetime": result["datetime"],
                            "date_text": result.get("date_text", ""),
                            "time_text": result.get("time_text", ""),
                            "original_text": match_text
                        })
                    else:
                        logger.info(f"🔍 Datetime parser failed for: '{match_text}'")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error extracting time suggestions: {str(e)}")
            return []

    async def _execute_get_day_from_date_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Get day name from date - useful for converting dates to weekdays"""
        try:
            from app.utils.datetime_parser import get_day_from_date
            
            # Try to extract date from message
            message = state.get("message", "")
            
            # Look for date patterns in message
            import re
            from datetime import datetime
            
            # Try to find dates in various formats
            date_patterns = [
                r'(\d{4}-\d{2}-\d{2})',  # 2025-09-10
                r'(\d{2}/\d{2}/\d{4})',  # 09/10/2025
                r'(\d{1,2}/\d{1,2}/\d{4})',  # 9/10/2025
            ]
            
            extracted_date = None
            for pattern in date_patterns:
                match = re.search(pattern, message)
                if match:
                    try:
                        date_str = match.group(1)
                        if '-' in date_str:
                            extracted_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        else:
                            extracted_date = datetime.strptime(date_str, '%m/%d/%Y').date()
                        break
                    except:
                        continue
            
            if extracted_date:
                day_name = get_day_from_date(extracted_date)
                return {
                    "success": True,
                    "date": str(extracted_date),
                    "day_name": day_name,
                    "formatted": f"{day_name.title()}, {extracted_date.strftime('%B %d, %Y')}"
                }
            else:
                return {
                    "success": False,
                    "error": "No date found in message",
                    "message": "Please provide a date to convert to day name"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _execute_get_date_from_day_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Get date from day name - useful for converting 'next Tuesday' to actual date"""
        try:
            from app.utils.datetime_parser import get_date_from_day
            
            message = state.get("message", "").lower()
            
            # Extract day and relative indicator
            days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            relative_indicators = ["next", "this", "coming"]
            
            found_day = None
            found_relative = "next"  # default
            
            for day in days:
                if day in message:
                    found_day = day
                    break
            
            for relative in relative_indicators:
                if relative in message:
                    found_relative = relative
                    break
            
            if found_day:
                target_date = get_date_from_day(found_day, found_relative)
                if target_date:
                    return {
                        "success": True,
                        "day_name": found_day,
                        "relative": found_relative,
                        "date": str(target_date),
                        "formatted": f"{found_relative.title()} {found_day.title()}: {target_date.strftime('%A, %B %d, %Y')}"
                    }
            
            return {
                "success": False,
                "error": "No day name found in message",
                "message": "Please specify a day like 'Monday', 'next Tuesday', etc."
            }
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _execute_parse_natural_datetime_tool(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Parse natural language datetime from message with conversation context"""
        try:
            from app.utils.datetime_parser import DateTimeParser
            import re
            
            message = state.get("message", "")
            conversation_history = state.get("conversation_history", [])
            
            parser = DateTimeParser()
            
            # Try direct parsing first
            result = parser.parse_datetime_from_message(message)
            
            if result and result.get("datetime"):
                from app.utils.datetime_parser import format_datetime_naturally
                
                return {
                    "success": True,
                    "datetime": result['datetime'].isoformat(),
                    "confidence": result.get('confidence', 0),
                    "source": result.get('source', 'direct_parsing'),
                    "natural_format": format_datetime_naturally(result['datetime']),
                    "alternatives": [alt.isoformat() if hasattr(alt, 'isoformat') else str(alt) 
                                   for alt in result.get('alternatives', [])]
                }
            
            # If direct parsing fails, try context-aware parsing
            logger.info(f"🔧 Direct parsing failed, trying context-aware parsing for: '{message}'")
            
            # Look for day references in conversation history
            target_day = None
            logger.info(f"🔍 Scanning {len(conversation_history)} messages for day references...")
            for i, msg in enumerate(reversed(conversation_history[-15:])):
                message_text = msg.get("message", "").lower()
                sender = msg.get("sender", "unknown")
                logger.info(f"🔍 Message {i+1} ({sender}): '{message_text[:100]}...'")
                for day in ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]:
                    if day in message_text:
                        target_day = day
                        logger.info(f"🔍 Found day reference in history: {day} from {sender}")
                        break
                if target_day:
                    break
            
            if not target_day:
                logger.info(f"🔍 No day reference found in conversation history")
            
            # Check for time-only messages that can be combined with context
            time_match = re.search(r'(\d{1,2})\s*(am|pm)', message.lower(), re.IGNORECASE)
            if time_match and target_day:
                logger.info(f"🎯 Combining time '{message.strip()}' with day '{target_day}' from context")
                
                hour = int(time_match.group(1))
                period = time_match.group(2).lower()
                
                if period == 'pm' and hour != 12:
                    hour += 12
                elif period == 'am' and hour == 12:
                    hour = 0
                
                target_date = parser.get_date_from_day(target_day, "next")
                
                if target_date:
                    from datetime import datetime, time
                    target_datetime = datetime.combine(target_date, time(hour, 0))
                    target_datetime = parser.timezone.localize(target_datetime)
                    
                    from app.utils.datetime_parser import format_datetime_naturally
                    
                    return {
                        "success": True,
                        "datetime": target_datetime.isoformat(),
                        "confidence": 0.95,
                        "source": "context_combination",
                        "natural_format": format_datetime_naturally(target_datetime),
                        "context_day": target_day,
                        "context_time": message.strip(),
                        "alternatives": []
                    }
            
            # Check for confirmatory responses that reference previous suggestions
            confirmation_phrases = [
                "that works", "that's good", "sounds good", "perfect", "great", 
                "yes", "yep", "yeah", "ok", "okay", "fine", "works for me",
                "that time works", "that's fine", "that suits me"
            ]
            
            if any(phrase in message.lower() for phrase in confirmation_phrases):
                # Look for time suggestions in recent assistant messages
                for msg in reversed(conversation_history[-5:]):
                    if msg.get("sender") == "assistant":
                        assistant_message = msg.get("message", "")
                        time_suggestions = self._extract_time_suggestions_from_text(assistant_message)
                        
                        if time_suggestions:
                            selected_time = time_suggestions[0]
                            from app.utils.datetime_parser import format_datetime_naturally
                            
                            return {
                                "success": True,
                                "datetime": selected_time["datetime"].isoformat(),
                                "confidence": 0.9,
                                "source": "confirmation_context",
                                "natural_format": format_datetime_naturally(selected_time["datetime"]),
                                "original_suggestion": selected_time.get("original_text", ""),
                                "alternatives": []
                            }
                        break
            
            return {
                "success": False,
                "error": "Could not parse datetime from message",
                "message": "Please provide a clearer date/time like 'tomorrow 3pm', 'next Tuesday at 2:30'",
                "found_context_day": target_day,
                "suggestion": "Try specifying both day and time together"
            }
                
        except Exception as e:
            logger.error(f"Error in parse_natural_datetime_tool: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _context_extraction_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Extract structured details from incoming message for Andy's workflow"""
        state["execution_path"].append("context_extraction")

        try:
            # Get existing context
            existing_context = self.memory_manager.get_lead_context(state["phone_number"])

            # CRITICAL: Ensure lead_id is available early - lookup by phone if missing
            if not state.get("lead_id") and not (existing_context and existing_context.get("lead_id")):
                logger.info(f"DEBUG: No lead_id found, looking up by phone: {state['phone_number']}")
                lead_id = await self._get_lead_id_by_phone(state["phone_number"])
                if lead_id:
                    state["lead_id"] = lead_id
                    if existing_context:
                        existing_context["lead_id"] = lead_id
                    else:
                        existing_context = {"lead_id": lead_id, "phone": state["phone_number"]}
                    logger.info(f"DEBUG: Found lead_id by phone lookup: {lead_id}")
                else:
                    logger.info(f"DEBUG: No lead found for phone: {state['phone_number']}")

            # Use advanced OpenAI analysis for intelligent extraction
            current_stage = state.get("current_stage", AndyWorkflowStages.INTRODUCTION)

            # Build prompt safely to avoid format specifier issues
            context_str = json.dumps(existing_context) if existing_context else "New lead"
            message_safe = str(state['message']).replace('%', '%%')  # Escape % characters

            # Include recent conversation history for better context
            conversation_context = ""
            if state.get("conversation_history"):
                recent_messages = state["conversation_history"][:5]  # Last 5 messages
                conversation_context = "\n- Recent Conversation:\n"
                for msg in reversed(recent_messages):  # Show chronologically
                    sender = "Andy" if msg.get("sender") == "assistant" else "Lead"
                    conversation_context += f"  {sender}: {msg.get('message', '')}\n"

            extraction_prompt = f"""You are an expert lead qualification analyst with deep understanding of franchise sales. Analyze this SMS message with maximum intelligence and precision.

CONTEXT:
- Current Workflow Stage: {current_stage}
- User's Message: "{message_safe}"
- Existing Context: {context_str}{conversation_context}

INTELLIGENT ANALYSIS REQUIRED:
Perform deep analysis and extract information as JSON:

{{
    "name": "first name only if mentioned",
    "work_background": "specific job title/profession",
    "work_category": "corporate or trades",
    "employment_type": "employee, self_employed, business_owner, or unemployed",
    "motivation": "their stated reason for franchise interest",
    "motivation_category": "be_your_own_boss, sick_of_corporate, flexible_lifestyle, ease_into_retirement, legacy_for_children, redundancy, diversification, relocation, financial_freedom, or other",
    "budget": "specific dollar amount mentioned",
    "budget_response": "confirming, denying, or providing_amount",
    "availability": "yes, no, or specific_time",
    "objections": ["too_expensive", "no_experience", "unknown_brand", "income_concerns", "time_constraints", "location_issues"],
    "questions": ["specific franchise questions asked"],
    "sentiment": "very_positive, positive, neutral, concerned, negative",
    "engagement_level": "high, medium, low",
    "urgency": "high, medium, low",
    "intent": "initial_inquiry, answering_question, expressing_objection, confirming_budget, scheduling, general_chat"
}}

CRITICAL CATEGORIZATION RULES:
1. work_category:
   - "corporate" = office jobs, managers, professionals, IT, marketing, sales, admin, finance, HR, consultants
   - "trades" = electrician, plumber, builder, mechanic, carpenter, painter, landscaper, technician, hands-on work

2. motivation_category: Analyze their WHY and categorize precisely:
   - "be_your_own_boss" = want independence, control, own business
   - "sick_of_corporate" = tired of office politics, bureaucracy, desk job
   - "flexible_lifestyle" = work-life balance, family time, flexible hours
   - "ease_into_retirement" = semi-retirement, easier work, less stress
   - "financial_freedom" = more money, better income, wealth building, extra income, source of income, additional income, diversification

3. budget_response:
   - "confirming" = yes, yeah, I have it, access to that, can do that, around that, about that, roughly that, close to that, approximately, something like that, in that range, ballpark, thereabouts, or so
   - "denying" = no, don't have that much, can't access, less than, lower
   - "providing_amount" = giving specific different amount with numbers (120K, $150k, 170 thousand, etc.)

4. objections: Identify specific concerns that need addressing

5. engagement_level:
   - "high" = asking detailed questions, very interested, ready to proceed
   - "medium" = answering questions, showing interest, some concerns
   - "low" = short responses, not engaged, distracted

CRITICAL: Return ONLY a valid JSON object. No explanations, no markdown, just pure JSON.

Example format (replace with actual values):
- work_background: specific job title
- work_category: corporate or trades
- motivation: their stated reason
- sentiment: positive/neutral/negative
- engagement_level: high/medium/low"""

            # Use a more structured approach for better JSON parsing
            messages = [
                {"role": "system", "content": "You are a JSON extraction expert. Always return valid JSON only."},
                {"role": "user", "content": extraction_prompt}
            ]

            response = await self.llm.ainvoke([SystemMessage(content=extraction_prompt)])

            try:
                # Clean the response to ensure valid JSON
                response_text = response.content.strip()

                # Remove any markdown formatting
                if response_text.startswith("```json"):
                    response_text = response_text.replace("```json", "").replace("```", "").strip()
                elif response_text.startswith("```"):
                    response_text = response_text.replace("```", "").strip()

                # Try to extract JSON from the response
                if not response_text.startswith("{"):
                    # Look for JSON object in the response
                    import re
                    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                    if json_match:
                        response_text = json_match.group()

                extracted_info = json.loads(response_text)
                # Validate and enhance the extracted data
                extracted_info = self._validate_and_enhance_extraction(extracted_info, state["message"])
                logger.info("Successfully parsed OpenAI JSON extraction")

            except (json.JSONDecodeError, AttributeError) as e:
                # Safely handle error to avoid format specifier issues
                error_message = str(e)
                safe_error = error_message.replace('%', 'percent').replace('{', '[').replace('}', ']')
                logger.warning("OpenAI JSON parsing failed, using intelligent fallback", error=safe_error)
                extracted_info = await self._intelligent_fallback_extraction(state["message"], current_stage)

            state["extracted_info"] = extracted_info

        except Exception as e:
            # Safely handle error to avoid format specifier issues
            error_message = str(e)
            # Remove any potential format specifiers from error message
            safe_error = error_message.replace('%', 'percent').replace('{', '[').replace('}', ']')
            logger.error("Andy context extraction error", error=safe_error)
            state["extracted_info"] = self._andy_fallback_extraction(state["message"])
            state["error"] = safe_error

        return state

    def _validate_and_enhance_extraction(self, extracted: dict, message: str) -> dict:
        """Validate and enhance extracted data with additional intelligence"""
        message_lower = message.lower()

        # Enhance work categorization
        if extracted.get("work_background") and not extracted.get("work_category"):
            work = extracted["work_background"].lower()
            if any(term in work for term in ["engineer", "manager", "analyst", "coordinator", "specialist", "consultant", "administrator", "executive", "director", "officer", "accountant", "marketing", "sales", "hr", "finance"]):
                extracted["work_category"] = "corporate"
            elif any(term in work for term in ["electrician", "plumber", "builder", "mechanic", "technician", "tradesman", "carpenter", "painter", "landscaper", "handyman"]):
                extracted["work_category"] = "trades"

        # Enhance budget response detection - MORE FLEXIBLE
        if not extracted.get("budget_response"):
            # Flexible confirmation patterns
            confirming_patterns = [
                "yes", "yeah", "yep", "i have", "access to", "can do", "that's right", "correct",
                "around that", "about that", "roughly that", "close to that", "approximately",
                "something like that", "in that range", "ballpark", "thereabouts", "or so"
            ]
            
            # Amount providing patterns (user gives specific amount)
            amount_indicators = ["k", "000", "thousand", "$", "dollars"]
            
            # Denying patterns
            denying_patterns = ["no", "don't have", "can't", "not that much", "less than", "lower"]
            
            if any(pattern in message_lower for pattern in confirming_patterns):
                extracted["budget_response"] = "confirming"
            elif any(char.isdigit() for char in message) and any(indicator in message_lower for indicator in amount_indicators):
                extracted["budget_response"] = "providing_amount"
            elif any(pattern in message_lower for pattern in denying_patterns):
                extracted["budget_response"] = "denying"

        # Enhance engagement level
        if not extracted.get("engagement_level"):
            if len(message) > 50 or "?" in message or any(word in message_lower for word in ["tell me", "how", "what", "when", "where", "interested"]):
                extracted["engagement_level"] = "high"
            elif len(message) > 20:
                extracted["engagement_level"] = "medium"
            else:
                extracted["engagement_level"] = "low"

        return extracted

    async def _intelligent_fallback_extraction(self, message: str, current_stage: str) -> dict:
        """Enhanced intelligent fallback extraction with pattern matching"""
        try:
            # Try a simpler OpenAI extraction first
            simple_prompt = f"""Extract information from: {repr(message)}

IMPORTANT EXAMPLES:
- "Extra source of income" → motivation: "extra income", motivation_category: "financial_freedom"
- "I am a PM" → work_background: "PM", work_category: "corporate"
- "Looking for additional income" → motivation: "additional income", motivation_category: "financial_freedom"

Return only this JSON format:
{{
    "work_background": "job title if mentioned or null",
    "work_category": "corporate or trades or null",
    "motivation": "reason for interest or null",
    "motivation_category": "financial_freedom if income-related or null",
    "budget": "dollar amount or null",
    "budget_response": "confirming or denying or providing_amount or null",
    "sentiment": "positive or neutral or negative"
}}

Return only valid JSON, no other text."""

            response = await self.llm.ainvoke([SystemMessage(content=simple_prompt)])
            response_text = response.content.strip()

            # Clean response
            if response_text.startswith("```"):
                response_text = response_text.replace("```json", "").replace("```", "").strip()

            extracted = json.loads(response_text)
            logger.info("Fallback OpenAI extraction successful")
            return extracted

        except Exception as e:
            logger.warning("Fallback OpenAI extraction failed, using pattern matching", error=str(e))
            return self._pattern_based_extraction(message, current_stage)

    def _pattern_based_extraction(self, message: str, current_stage: str) -> dict:
        """Pattern-based extraction as final fallback"""
        extracted = {}
        message_lower = message.lower()

        # Work background patterns
        work_patterns = {
            "software engineer": "corporate", "developer": "corporate", "programmer": "corporate",
            "manager": "corporate", "director": "corporate", "analyst": "corporate",
            "electrician": "trades", "plumber": "trades", "builder": "trades",
            "mechanic": "trades", "carpenter": "trades", "painter": "trades"
        }

        for work, category in work_patterns.items():
            if work in message_lower:
                extracted["work_background"] = work
                extracted["work_category"] = category
                break

        # Enhanced budget detection for qualification_3_budget stage
        if current_stage == "qualification_3_budget":
            # Check for confirmation responses
            if any(word in message_lower for word in ["yes", "i have", "access to", "can do", "sure", "okay", "yep", "yeah"]):
                extracted["budget_response"] = "confirming"
            # Check for denial responses
            elif any(word in message_lower for word in ["no", "don't have", "can't", "not", "unable"]):
                extracted["budget_response"] = "denying"
            # Check for specific amounts (digits, K, thousand, etc.)
            elif any(char.isdigit() for char in message) or "k" in message_lower or "thousand" in message_lower:
                extracted["budget_response"] = "providing_amount"

                # Extract the actual budget amount
                import re
                # Look for patterns like "190K", "190k", "$190k", "190 thousand", "190,000"
                budget_patterns = [
                    r'(\d+(?:\.\d+)?)\s*k\b',  # 190K, 190.5K
                    r'\$(\d+(?:\.\d+)?)\s*k\b', # $190K
                    r'(\d{1,3}(?:,\d{3})*)',    # 190,000 or 190000
                    r'(\d+)\s*thousand',        # 190 thousand
                    r'(\d+(?:\.\d+)?)\s*m\b'   # 0.19M
                ]

                for pattern in budget_patterns:
                    match = re.search(pattern, message, re.IGNORECASE)
                    if match:
                        budget_str = match.group(1).replace(',', '')
                        try:
                            if 'k' in message_lower:
                                budget_amount = float(budget_str) * 1000
                            elif 'm' in message_lower:
                                budget_amount = float(budget_str) * 1000000
                            elif 'thousand' in message_lower:
                                budget_amount = float(budget_str) * 1000
                            else:
                                budget_amount = float(budget_str)

                            extracted["budget"] = int(budget_amount)
                            break
                        except ValueError:
                            continue

        # Motivation patterns
        motivation_patterns = {
            "work life balance": "flexible_lifestyle",
            "flexible": "flexible_lifestyle",
            "own boss": "be_your_own_boss",
            "tired of corporate": "sick_of_corporate",
            "retirement": "ease_into_retirement"
        }

        for phrase, category in motivation_patterns.items():
            if phrase in message_lower:
                extracted["motivation"] = phrase
                extracted["motivation_category"] = category
                break

        # Sentiment
        if any(word in message_lower for word in ["great", "interested", "yes", "sounds good"]):
            extracted["sentiment"] = "positive"
        elif any(word in message_lower for word in ["no", "not interested", "expensive"]):
            extracted["sentiment"] = "negative"
        else:
            extracted["sentiment"] = "neutral"

        return extracted

    def _andy_fallback_extraction(self, message: str) -> Dict[str, Any]:
        """Andy's fallback extraction using pattern matching"""
        extracted = {}
        message_lower = message.lower()

        # Availability patterns - enhanced to catch more rejection phrases
        if any(word in message_lower for word in ["yes", "sure", "ok", "okay", "yeah", "yep", "ready", "available now"]):
            extracted["availability"] = "yes"
        elif any(phrase in message_lower for phrase in [
            "no", "not now", "busy", "later", "don't want to chat", "don't want to talk",
            "can't talk right now", "not right now", "no not right now", "not available",
            "don't have time", "no time", "not ready", "maybe later", "talk later"
        ]):
            extracted["availability"] = "no"

        # Budget patterns (Australian context)
        budget_patterns = [
            (r'\$?(\d+)k', lambda m: f"${m.group(1)}k"),
            (r'\$?(\d+),?(\d+)', lambda m: f"${m.group(1)},{m.group(2)}" if m.group(2) else f"${m.group(1)}"),
            (r'(\d+)\s*thousand', lambda m: f"${m.group(1)}k"),
        ]

        for pattern, formatter in budget_patterns:
            match = re.search(pattern, message_lower)
            if match:
                extracted["budget"] = formatter(match)
                break

        # Enhanced work background patterns
        corporate_roles = ["manager", "director", "executive", "analyst", "coordinator", "officer", "consultant",
                          "administrator", "engineer", "developer", "programmer", "accountant", "marketing",
                          "sales", "finance", "hr", "business", "corporate"]
        trades_roles = ["plumber", "electrician", "sparky", "handyman", "builder", "carpenter", "mechanic",
                       "technician", "tradie", "trades", "construction", "landscaper", "painter"]

        # Extract specific job title and categorize
        for role in corporate_roles:
            if role in message_lower:
                extracted["work_background"] = role
                extracted["work_category"] = "corporate"
                break

        if not extracted.get("work_background"):
            for role in trades_roles:
                if role in message_lower:
                    extracted["work_background"] = role
                    extracted["work_category"] = "trades"
                    break

        # Employment type patterns
        if any(phrase in message_lower for phrase in ["sole trader", "own business", "self employed", "freelance"]):
            extracted["employment_type"] = "sole_trader"
            extracted["work_status"] = "sole_trader"
        elif any(phrase in message_lower for phrase in ["employee", "work for", "employed by"]):
            extracted["employment_type"] = "employee"
            extracted["work_status"] = "employee"

        # Meeting interest patterns
        if any(phrase in message_lower for phrase in ["call", "meeting", "chat", "discuss", "talk", "speak"]):
            extracted["meeting_interest"] = True

        # Enhanced motivation detection
        motivation_patterns = {
            "work-life balance": ["work life balance", "balance", "flexible", "family time", "less stress", "better hours"],
            "financial freedom": ["money", "income", "financial", "earn more", "profit", "wealth", "extra income", "source of income", "additional income", "second income", "more money"],
            "own boss": ["own boss", "be my own", "independence", "control", "freedom"],
            "retirement": ["retirement", "retire", "pension", "future", "security"],
            "career change": ["change", "different", "new career", "transition", "switch"]
        }

        for motivation, keywords in motivation_patterns.items():
            if any(keyword in message_lower for keyword in keywords):
                extracted["motivation"] = motivation
                extracted["motivation_category"] = motivation.replace("-", "_").replace(" ", "_")
                break

        # Enhanced budget response detection
        if any(phrase in message_lower for phrase in ["yes", "i have", "have access", "can access", "got the", "150"]):
            extracted["budget_response"] = "confirming"
        elif any(phrase in message_lower for phrase in ["maybe", "think so", "probably", "should be"]):
            extracted["budget_response"] = "uncertain"
        elif any(phrase in message_lower for phrase in ["no", "don't have", "can't", "not that much"]):
            extracted["budget_response"] = "declining"

        # Objection patterns
        objections = []
        if any(phrase in message_lower for phrase in ["expensive", "too much", "can't afford", "no value"]):
            objections.append("expensive")
        if any(phrase in message_lower for phrase in ["never heard", "don't know", "not familiar"]):
            objections.append("unknown_brand")
        if any(phrase in message_lower for phrase in ["no experience", "don't know how", "never done"]):
            objections.append("no_experience")

        if objections:
            extracted["objections"] = objections

        # Set default values for better workflow progression
        if not extracted.get("sentiment"):
            extracted["sentiment"] = "positive"
        if not extracted.get("engagement_level"):
            extracted["engagement_level"] = "high"

        return extracted

    async def _question_extraction_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Extract and store questions from lead's message in question_bank table"""
        state["execution_path"].append("question_extraction")

        try:
            # Get conversation context for better question extraction
            conversation_history = self.memory_manager.get_conversation_history(
                state["phone_number"], limit=10
            )

            # Extract questions using the question extraction service
            extraction_result = await self.question_extraction_service.analyze_and_extract_questions(
                message=state["message"],
                lead_id=state.get("lead_id"),
                franchisor_id=state.get("franchisor_id") or self._get_franchisor_id_for_lead(state.get("lead_id")),
                phone_number=state["phone_number"],
                conversation_context=conversation_history
            )

            # Store extraction results in state for potential use in response generation
            state["question_extraction_result"] = extraction_result

            # Log extraction results
            if extraction_result.get("success"):
                questions_found = extraction_result.get("questions_found", 0)
                questions_stored = extraction_result.get("questions_stored", 0)

                logger.info(
                    "Question extraction completed",
                    phone_number=state["phone_number"],
                    lead_id=state.get("lead_id"),
                    questions_found=questions_found,
                    questions_stored=questions_stored,
                    message_preview=state["message"][:50] + "..."
                )

                # Add extracted questions to state for potential response customization
                if extraction_result.get("extracted_questions"):
                    state["extracted_questions"] = extraction_result["extracted_questions"]
            else:
                logger.warning(
                    "Question extraction failed",
                    phone_number=state["phone_number"],
                    error=extraction_result.get("error", "Unknown error")
                )

        except Exception as e:
            logger.warning(
                "Question extraction node error - continuing workflow",
                error=str(e),
                phone_number=state["phone_number"],
                lead_id=state.get("lead_id")
            )
            # Don't fail the entire workflow if question extraction fails
            state["question_extraction_result"] = {
                "success": False,
                "error": str(e),
                "questions_found": 0,
                "questions_stored": 0,
                "message": "Question extraction failed but conversation continues"
            }

        return state

    async def _workflow_determination_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """AI-driven workflow determination using prompts instead of hardcoded conditions"""
        state["execution_path"].append("workflow_determination")

        try:

            # SPECIAL HANDLING: Check for hello messages first
            conversation_history = state.get("conversation_history", [])
            current_stage = state.get("current_stage")
            
            if self._is_hello_message(state["message"], conversation_history, current_stage):
                logger.info("Detected hello message, using direct hello response")
                return {
                    "current_stage": AndyWorkflowStages.INTRODUCTION,
                    "next_action": "send_hello_response",
                    "reasoning": "User sent a greeting, responding with hello message asking for permission to chat"
                }

            # Use AI to determine workflow progression for other messages
            workflow_decision = await self._ai_workflow_determination(state)
            
            # Apply the AI decision
            state["current_stage"] = workflow_decision.get("current_stage", AndyWorkflowStages.INTRODUCTION)
            state["next_action"] = workflow_decision.get("next_action", "send_introduction")
            state["workflow_reasoning"] = workflow_decision.get("reasoning", "AI workflow decision")
            
            # Preserve any qualification data from AI analysis
            if workflow_decision.get("qualification_answer"):
                state["qualification_answer"] = workflow_decision["qualification_answer"]
            if workflow_decision.get("qualification_category"):
                state["qualification_category"] = workflow_decision["qualification_category"]
                
            # Get existing context for state
            existing_context = self.memory_manager.get_lead_context(state["phone_number"])
            state["lead_context"] = existing_context

            logger.info(
                "AI workflow decision",
                current_stage=state["current_stage"],
                next_action=state["next_action"],
                reasoning=workflow_decision.get("reasoning", "")[:100]  # Log first 100 chars
            )

        except Exception as e:
            logger.error("AI workflow determination error", error=str(e))
            # Fallback to safe defaults
            state["current_stage"] = AndyWorkflowStages.INTRODUCTION
            state["next_action"] = "send_introduction"
            state["error"] = str(e)
            state["workflow_reasoning"] = f"Error fallback: {str(e)}"

        return state

    async def _ai_workflow_determination(self, state: AndyAssistantState) -> Dict[str, Any]:
        """Use AI to determine the next workflow stage and action based on conversation context"""
        try:
            # Get conversation context
            existing_context = self.memory_manager.get_lead_context(state["phone_number"])
            conversation_history = self.memory_manager.get_conversation_history(state["phone_number"], limit=10)
            extracted_info = state.get("extracted_info", {})
            
            # Build context for AI decision
            context_summary = self._build_workflow_context_summary(existing_context, conversation_history, extracted_info)

            # Add qualification completion check to prevent jumping to budget
            qualification_complete = self._check_qualification_completion(existing_context, conversation_history)
            context_summary += f"\n\nQUALIFICATION COMPLETION CHECK:\n{qualification_complete}"
            
            # Create AI prompt for workflow decision
            workflow_prompt = f"""
You are Andy, a friendly Australian franchise advisor. You have a warm, conversational tone that's professional but approachable.

PERSONALITY & TONE:
- Friendly and approachable with a slight Australian accent
- Use natural conversational language ("mate", "g'day", "brilliant", "awesome")
- Professional but not formal - like chatting with a knowledgeable friend
- Always acknowledge what the user shared before progressing
- Use conversational connectors like "yeah", "so", "okay" naturally
- Build on previous responses and maintain context

CONVERSATION CONTEXT:
{context_summary}

CURRENT MESSAGE: "{state['message']}"

EXTRACTED INFO FROM CURRENT MESSAGE:
{json.dumps(extracted_info, indent=2)}

WORKFLOW STAGES (in order):
1. introduction - First contact, asking for permission to chat
2. qualification_1_work - Ask about their current work/job
3. qualification_2_motivation - Ask why they're interested in franchising
4. qualification_3_budget - Discuss investment budget
5. franchise_fee_breakdown - Present franchise fee information
6. scheduling - Book discovery call meeting
7. confirmation - Confirm meeting details
8. objection_handling - Handle concerns or objections

CRITICAL: No Stage / Info should be skipped
- Always progress through stages in order: introduction → qualification_1_work → qualification_2_motivation → qualification_3_budget → franchise_fee_breakdown → scheduling → confirmation
- Do not skip stages or jump ahead to budget questions unless all previous qualification steps are complete.
- And if the user has already provided info, then and only then move to the next stage.
- If the user wants to book a meeting but hasn't provided all info, let them know that this information will help you in the process (DO NOT DIRECTLY REVEAL STAGES TO THEM).
- CRITICAL: Never ask budget questions unless work background AND motivation have been discussed first.
- CRITICAL: Never offer to book a meeting unless work background, motivation, AND budget have all been discussed.
- If user pushes for meeting before qualification complete, politely explain that gathering this info first will help tailor the conversation to their specific situation.


NEXT ACTIONS (choose most appropriate):
- send_introduction: Send welcome message asking for permission to chat
- send_hello_response: Respond to a greeting
- handle_unavailability: User indicated they're not available - acknowledge and tell them to text you when they are available do not ask any other question (like when are you available and all)
- ask_work_background: Ask about their current job (MANDATORY - cannot skip)
- ask_motivation_corporate: Ask motivation (for corporate workers) (MANDATORY - cannot skip)
- ask_motivation_trades: Ask motivation (for trades workers) (MANDATORY - cannot skip)
- ask_budget_initial: Ask about investment budget (ONLY after work + motivation completed)
- ask_budget_followup: Follow up on budget discussion
- show_franchise_fee: Present franchise fee breakdown
- offer_meeting: Suggest booking a discovery call (ONLY after all qualification completed)
- handle_objections: Address concerns or objections
- general_response: Let AI generate contextual response
- end_conversation: Conversation is complete

POST-BOOKING BEHAVIOR (CRITICAL - When next_action is "post_booking_response"):
- The meeting has already been successfully booked
- DO NOT ask any questions about budget, motivation, work, or any qualification details
- DO NOT ask for confirmations or additional information
- Provide only casual, friendly acknowledgments that express enthusiasm
- Keep responses short, warm, and conversational
- Examples: "Excellent! Looking forward to our chat.", "Perfect! Can't wait to speak with you.", "Great! See you then."
- NEVER ask follow-up questions or seek additional confirmations
- The conversation should feel naturally concluded after booking
- And if they ask for any information do provide that information and do not ask any question from yourside.

DECISION RULES (apply with Australian warmth):
1. CRITICAL: If user says "hello", "hi", "hey", "g'day" etc. → send_hello_response action
2. CRITICAL: If user indicates they're not available (extracted_info.availability == "no") → handle_unavailability action
3. If user shows interest/says yes to chat → move to work background (NEVER skip to budget)
4. If work background provided → acknowledge their experience, then move to motivation (NEVER skip to budget)
5. If motivation provided → relate to their goals, then move to budget discussion
6. CRITICAL: Never ask budget questions unless both work background AND motivation have been completed
7. CRITICAL: Never offer meeting unless work background, motivation, AND budget have all been completed
8. If budget discussed → show franchise fee breakdown with confidence
9. If positive response to fees → enthusiastically offer meeting
10. If user wants to skip qualification → politely redirect to complete the process first
11. If objections detected → handle with understanding and Australian directness
12. If meeting booked → confirmation stage with excitement
13. Always acknowledge their previous responses before progressing
14. Keep responses conversational and build rapport
15. Always read the converstaion before making any decision or replying to any message
16. CRITICAL: Do not keep the messages too long, keep it short and concise, and refrain from using too many "great" and "brilliant" and other such words.
17. For Example: "That is great to hear that you are interested to explore" this is not right way intead use "Great!"
18. CRITICAL: KEEP IT SIMPLE AND TO THE POINT. (Spare user all the formalities and be professional)

Return a JSON response with:
{{
    "current_stage": "stage_name",
    "next_action": "action_name",
    "reasoning": "Brief explanation of why this decision was made (using Andy's voice)",
    "qualification_answer": "extracted answer if any",
    "qualification_category": "category if applicable"
}}
"""

            # Get AI decision
            ai_response = await self._get_openai_completion(workflow_prompt)
            
            try:
                workflow_decision = json.loads(ai_response)
                
                # Validate the response has required fields
                if not all(key in workflow_decision for key in ["current_stage", "next_action", "reasoning"]):
                    raise ValueError("Missing required fields in AI response")
                    
                # Validate stage and action values
                valid_stages = [stage.value for stage in AndyWorkflowStages]
                if workflow_decision["current_stage"] not in valid_stages:
                    logger.warning(f"Invalid stage from AI: {workflow_decision['current_stage']}, defaulting to introduction")
                    workflow_decision["current_stage"] = AndyWorkflowStages.INTRODUCTION
                
                return workflow_decision
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Error parsing AI workflow decision: {e}")
                # Return safe fallback
                return {
                    "current_stage": AndyWorkflowStages.INTRODUCTION,
                    "next_action": "send_introduction",
                    "reasoning": f"AI parsing error, using fallback: {str(e)}"
                }
                
        except Exception as e:
            logger.error(f"Error in AI workflow determination: {e}")
            return {
                "current_stage": AndyWorkflowStages.INTRODUCTION,
                "next_action": "send_introduction",
                "reasoning": f"Error occurred, using safe fallback: {str(e)}"
            }

    def _check_qualification_completion(self, existing_context: Dict, conversation_history: List) -> str:
        """Check if required qualification steps have been completed"""
        if not existing_context:
            return "No existing context - start from beginning"

        work_background = existing_context.get("work_background") or existing_context.get("qualification_1_work")
        motivation = existing_context.get("motivation") or existing_context.get("qualification_2_motivation")
        budget_confirmed = existing_context.get("budget_confirmed") or existing_context.get("budget")

        # Check conversation history for qualification discussions
        work_discussed = any("work" in msg.get("message", "").lower() for msg in conversation_history if msg.get("sender") == "assistant")
        motivation_discussed = any("motivation" in msg.get("message", "").lower() or "why" in msg.get("message", "").lower() for msg in conversation_history if msg.get("sender") == "assistant")
        
        # CRITICAL: Check for budget responses in conversation history to prevent re-asking
        budget_discussed = budget_confirmed
        if not budget_discussed and conversation_history:
            # Look for budget questions asked by assistant and responses from user
            budget_question_asked = any(
                any(keyword in msg.get("message", "").lower() for keyword in ["budget", "funds", "invest"]) 
                for msg in conversation_history if msg.get("sender") == "assistant"
            )
            
            if budget_question_asked:
                # Check for user responses to budget questions
                recent_user_messages = [msg for msg in conversation_history[-10:] if msg.get("sender") == "user"]
                for msg in recent_user_messages:
                    message_text = msg.get("message", "").lower()
                    budget_response_indicators = [
                        'around that', 'about that', 'roughly', 'approximately', 
                        'ballpark', 'close to', 'something like', 'in that range',
                        'yes', 'yeah', 'yep', 'i have', 'access to', 'can do', 'that\'s right', 'correct'
                    ]
                    if (any(char.isdigit() for char in message_text) and 
                        any(indicator in message_text for indicator in ['k', 'thousand', '$'])) or \
                       any(indicator in message_text for indicator in budget_response_indicators):
                        budget_discussed = True
                        break

        completion_status = []
        if work_background or work_discussed:
            completion_status.append("✅ Work background completed")
        else:
            completion_status.append("❌ Work background NOT completed")

        if motivation or motivation_discussed:
            completion_status.append("✅ Motivation completed")
        else:
            completion_status.append("❌ Motivation NOT completed")

        if budget_discussed:
            completion_status.append("✅ Budget discussed/confirmed - NEVER ask budget questions again")
        else:
            completion_status.append("❌ Budget NOT discussed")

        can_ask_budget = (work_background or work_discussed) and (motivation or motivation_discussed) and not budget_discussed
        if budget_discussed:
            completion_status.append("🚫 BUDGET ALREADY DISCUSSED - Move to franchise fee breakdown or scheduling")
        elif can_ask_budget:
            completion_status.append("✅ CAN proceed to budget questions")
        else:
            completion_status.append("❌ CANNOT proceed to budget questions - missing prerequisites")

        return "\n".join(completion_status)

    def _build_workflow_context_summary(self, existing_context: Dict, conversation_history: List, extracted_info: Dict) -> str:
        """Build a summary of the current conversation context for AI decision making"""
        summary_parts = []
        
        # Current stage and context
        if existing_context:
            current_stage = existing_context.get("current_stage", "None")
            summary_parts.append(f"Current Stage: {current_stage}")
            
            # Qualification progress
            qualification_info = []
            if existing_context.get("work_background"):
                qualification_info.append(f"Work: {existing_context['work_background']}")
            if existing_context.get("motivation"):
                qualification_info.append(f"Motivation: {existing_context['motivation']}")
            if existing_context.get("budget"):
                qualification_info.append(f"Budget: {existing_context['budget']}")
            if existing_context.get("meeting_scheduled"):
                qualification_info.append("Meeting: Scheduled")
                
            if qualification_info:
                summary_parts.append(f"Qualification Progress: {', '.join(qualification_info)}")
        else:
            summary_parts.append("Current Stage: New conversation (no prior context)")
        
        # Recent conversation
        if conversation_history:
            recent_messages = []
            for msg in conversation_history[-5:]:  # Last 5 messages
                sender = msg.get("sender", "unknown")
                message = msg.get("message", "")[:100]  # Truncate long messages
                recent_messages.append(f"{sender}: {message}")
            summary_parts.append(f"Recent Messages:\n" + "\n".join(recent_messages))
        
        # Extracted information
        if extracted_info:
            extraction_summary = []
            for key, value in extracted_info.items():
                if value:
                    extraction_summary.append(f"{key}: {value}")
            if extraction_summary:
                summary_parts.append(f"Extracted Info: {', '.join(extraction_summary)}")
        
        return "\n\n".join(summary_parts)

    async def _get_openai_completion(self, prompt: str) -> str:
        """Get completion from OpenAI with error handling"""
        try:
            if not self.openai_service:
                raise ValueError("OpenAI service not available")
                
            response = await self.openai_service.get_completion(
                prompt,
                model="gpt-5",
                temperature=0.1,
                max_tokens=500
            )
            return response
            
        except Exception as e:
            logger.error(f"OpenAI completion error: {e}")
            # Return a safe JSON fallback
            return json.dumps({
                "current_stage": "introduction",
                "next_action": "send_introduction",
                "reasoning": f"OpenAI error, using fallback: {str(e)}"
            })

    async def _response_generation_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Generate Andy's intelligent response using OpenAI while following strict requirements"""
        state["execution_path"].append("response_generation")

        try:
            # Check if lead is asking for meeting link
            user_message = state.get("message", "").lower()
            if any(phrase in user_message for phrase in ["meeting link", "link for meeting", "zoom link", "teams link", "call link"]):
                lead_context = state.get("lead_context", {})
                if lead_context and lead_context.get("meeting_link"):
                    state["response"] = f"Here's your meeting link: {lead_context['meeting_link']}"
                    logger.info("🔗 PROVIDED MEETING LINK: User requested meeting link")
                    return state
                elif lead_context and lead_context.get("meeting_scheduled"):
                    state["response"] = "I'll send you the meeting link once it's generated. You should receive it via email shortly."
                    return state
                else:
                    state["response"] = "I don't see any scheduled meeting yet. Would you like to book one?"
                    return state
            
            # Check if conversation should end (meeting booked successfully)
            if state.get("next_action") == "end_conversation" or state.get("conversation_complete"):
                # Use existing response from booking confirmation, no further processing needed
                if state.get("response"):
                    logger.info(f"🔚 CONVERSATION COMPLETE: Using booking confirmation response")
                    return state
                else:
                    # Fallback end message
                    state["response"] = "Thank you for your time! Looking forward to speaking with you soon."
                    return state
            
            # PRIORITY: Always check for autonomous responses first, regardless of strict mode
            tool_results = state.get("tool_execution_results", {})
            autonomous_response = (tool_results.get("response_content") if tool_results else state.get("response") if state else "")
            has_autonomous_response = bool(autonomous_response)
            # Also check for responses set by tools handlers (meeting booking, etc.)
            tools_response = state.get("response")
            tools_response_flag = state.get("tools_response", False)
            if (tools_response and not has_autonomous_response) or tools_response_flag:
                autonomous_response = tools_response
                has_autonomous_response = True
                logger.info(f"🎯 TOOLS RESPONSE: Using response set by tools handler: {tools_response[:100]}...")
            
            if has_autonomous_response:
                # ALWAYS use autonomous response when available - this ensures flexibility
                response = autonomous_response
                print(f"🎯 FLEXIBLE MODE: Using autonomous response (overriding strict mode)")
                logger.info(f"FLEXIBLE MODE: Using autonomous response: {response[:100]}...")
            elif not self.strict_workflow_mode:
                # Use OpenAI to generate intelligent, requirement-compliant responses
                response = await self._generate_openai_response(state)
                print(f"🎯 OPENAI MODE: Using intelligent response generation")
                logger.info("OPENAI MODE: Using intelligent response generation")
            else:
                # Only use strict templates as absolute last resort
                print(f"🎯 STRICT MODE: Using template response generation (no autonomous response available)")
                logger.info("STRICT MODE: Using template response generation as fallback")
                response = await self._generate_strict_workflow_response(state)
                
            print(f"🎯 FINAL RESPONSE: {response[:100]}...")

            state["response"] = response

        except Exception as e:
            # Safely handle error to avoid format specifier issues
            error_message = str(e)
            safe_error = error_message.replace('%', 'percent').replace('{', '[').replace('}', ']')
            logger.error(f"Response generation error {e}", error=safe_error)
            # Fallback to template-based response
            response = await self._generate_fallback_response(state)
            state["response"] = response
            state["error"] = safe_error

        return state

    async def _generate_strict_workflow_response(self, state: AndyAssistantState) -> str:
        """STRICT WORKFLOW: Generate exact template responses"""
        current_stage = state["current_stage"]
        next_action = state["next_action"]
        lead_context = state["lead_context"]
        user_message = state["message"]

        print(f"🎯 STRICT RESPONSE GENERATION")
        print(f"   Stage: {current_stage}")
        print(f"   Action: {next_action}")

        # EXACT TEMPLATE RESPONSES
        if next_action == "send_introduction":
            # STAGE 1: Introduction
            name = lead_context.get("name", "there") if lead_context else "there"
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            
            if name and name != "there":
                response = AndyTemplates.INTRODUCTION.format(name=name, franchisor_name=franchisor_name)
            else:
                response = AndyTemplates.INTRODUCTION_NO_NAME.format(franchisor_name=franchisor_name)
            print(f"   → Using INTRODUCTION template with franchisor: {franchisor_name}")
            return response

        elif next_action == "send_hello_response":
            # HELLO RESPONSE: When lead says hello, ask for permission to chat
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            response = AndyTemplates.HELLO_RESPONSE.format(franchisor_name=franchisor_name)
            print(f"   → Using HELLO_RESPONSE template with franchisor: {franchisor_name}")
            return response

        elif next_action == "handle_unavailability":
            # HANDLE UNAVAILABILITY: When user indicates they're not available
            response = AndyTemplates.UNAVAILABILITY_RESPONSE
            print(f"   → Using UNAVAILABILITY_RESPONSE template for unavailability")
            return response

        elif next_action == "ask_work_background":
            # STAGE 2: Work Background
            response = AndyTemplates.QUALIFICATION_1_WORK
            print(f"   → Using WORK_BACKGROUND template")
            return response

        elif next_action == "ask_motivation":
            # STAGE 3: Motivation (with acknowledgment)
            work_type = self._analyze_work_background_type(user_message)
            self._update_strict_workflow_context(state, {"work_background": user_message, "work_type": work_type})

            if work_type == "corporate":
                response = f"That sounds interesting, {AndyTemplates.QUALIFICATION_2_MOTIVATION_CORPORATE}"
            else:
                response = f"That sounds interesting, {AndyTemplates.QUALIFICATION_2_MOTIVATION_TRADES}"

            print(f"   → Using MOTIVATION template (work_type: {work_type})")
            return response

        elif next_action == "ask_budget":
            # STAGE 4: Budget (with acknowledgment)
            budget = lead_context.get("budget", "your available") if lead_context else "your available"
            logger.info(f"DEBUG: Budget: {budget}")
            response = f"That sounds interesting!. {AndyTemplates.QUALIFICATION_3_BUDGET.format(budget=budget)}"
            print(f"   → Using BUDGET template")
            return response

        elif next_action == "ask_budget_followup":
            # STAGE 4b: Budget follow-up question when user says no
            response = AndyTemplates.BUDGET_INQUIRY
            print(f"   → Using BUDGET_FOLLOWUP template")
            return response

        elif next_action == "provide_franchise_fee":
            # STAGE 5: Franchise Fee Breakdown - Use dynamic RAG information
            lead_id = state.get("lead_id")
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            
            # Try to get dynamic franchise fee info from RAG
            if lead_id:
                fee_result = await self._get_franchise_fee_info(lead_id)
                if fee_result.get("success"):
                    fee_info = fee_result["fee_info"]
                    response = AndyTemplates.FRANCHISE_FEE_BREAKDOWN.format(fee_info=fee_info)
                    print(f"   → Using DYNAMIC FRANCHISE_FEE template with RAG info for: {franchisor_name}")
                    return response
            
            # Fallback to static template
            response = AndyTemplates.FRANCHISE_FEE_FALLBACK
            print(f"   → Using FALLBACK FRANCHISE_FEE template")
            return response

        elif next_action == "offer_meeting":
            # STAGE 6: Complete Meeting Booking Integration
            print(f"   → Using COMPLETE MEETING BOOKING SYSTEM")
            return await self._handle_complete_meeting_booking(state, "offer_slots")

        elif next_action == "confirm_meeting":
            # STAGE 7: Complete Meeting Confirmation Integration
            print(f"   → Using COMPLETE MEETING CONFIRMATION SYSTEM")
            return await self._handle_complete_meeting_booking(state, "confirm_booking")

        elif next_action == "offer_alternative_slots":
            # STAGE 8: Offer Alternative Slots when user is not ready or slot unavailable
            print(f"   → Offering alternative slots")
            return await self._handle_user_not_ready_response(state)

        else:
            # Fallback with exact template
            response = AndyTemplates.CONFIRMATION
            print(f"   → Using FALLBACK template")
            return response

    def _update_strict_workflow_context(self, state: AndyAssistantState, updates: dict):
        """Update lead context for strict workflow mode"""
        existing_context = self.memory_manager.get_lead_context(state["phone_number"]) or {}
        existing_context.update(updates)
        existing_context["current_stage"] = state["current_stage"]
        self.memory_manager.store_lead_context(state["phone_number"], existing_context)
        state["lead_context"] = existing_context

    def _analyze_work_background_type(self, message: str) -> str:
        """Analyze work background to determine corporate vs trades"""
        message_lower = message.lower()

        # Corporate background indicators
        corporate_keywords = [
            "manager", "director", "executive", "analyst", "coordinator", "specialist",
            "administrator", "consultant", "accountant", "marketing", "sales", "hr",
            "human resources", "finance", "accounting", "project manager", "office",
            "desk", "corporate", "business", "admin", "supervisor", "team lead"
        ]

        # Trades background indicators
        trades_keywords = [
            "plumber", "electrician", "sparky", "handyman", "carpenter", "builder",
            "mechanic", "technician", "installer", "fitter", "welder", "painter",
            "landscaper", "gardener", "roofer", "tiler", "bricklayer", "tradie",
            "contractor", "tools", "on-site", "field work"
        ]

        # Check for corporate keywords
        if any(keyword in message_lower for keyword in corporate_keywords):
            return "corporate"

        # Check for trades keywords
        if any(keyword in message_lower for keyword in trades_keywords):
            return "trades"

        # Default to corporate if unclear
        return "corporate"

    async def _generate_openai_response(self, state: AndyAssistantState) -> str:
        """Generate intelligent response using OpenAI while strictly following requirements"""
        current_stage = state["current_stage"]
        next_action = state["next_action"]
        lead_context = state["lead_context"]
        extracted_info = state["extracted_info"]
        user_message = state["message"]
        rag_response = state.get("rag_response")
        conversation_history = state.get("conversation_history", [])

        # Format conversation history for the prompt
        formatted_history = ""
        if conversation_history:
            # Get the most recent 10 messages to avoid token limits
            recent_messages = conversation_history[-10:]
            history_lines = []
            for msg in recent_messages:
                sender = msg.get("sender", "unknown")
                message_text = msg.get("message", "")
                timestamp = msg.get("timestamp", "")
                if timestamp:
                    # Format timestamp nicely
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = dt.strftime("%H:%M")
                    except:
                        time_str = "recent"
                else:
                    time_str = "recent"

                history_lines.append(f"{sender} ({time_str}): {message_text}")

            formatted_history = "\n".join(history_lines)
            logger.info(f"Conversation history is {formatted_history}")
        else:
            formatted_history = "No previous conversation history available."

        # 🎯 CRITICAL: Handle booking failures BEFORE any other processing
        if next_action == "meeting_booking_tools":
            booking_failure = state.get("booking_failure")
            tool_results = state.get("tool_execution_results", {})
            booking_result = tool_results.get("tool_results", {}).get("book_meeting", {})
            
            logger.info(f"🔧 DEBUG: Processing meeting_booking_tools - booking_failure: {booking_failure}")
            logger.info(f"🔧 DEBUG: Processing meeting_booking_tools - booking_result: {booking_result}")
            
            # Check booking_failure first
            if booking_failure:
                requested_time = booking_failure.get("requested_time", "that time")
                alternative_times = booking_failure.get("alternative_times", [])
                
                if alternative_times:
                    alternatives_text = ", ".join(alternative_times[:3])
                    return f"Sorry, {requested_time} isn't available. How about one of these times instead: {alternatives_text}? Let me know which works best for you!"
                else:
                    return "I'm having trouble finding that exact time slot. Let me check our availability and get back to you with some great options that should work for your schedule!"
            
            # Check tool results directly if booking_failure not set
            elif booking_result.get("suggested_action") == "offer_alternative_times":
                requested_time = booking_result.get("requested_time", "that time")
                alternative_times = booking_result.get("alternative_times", [])
                
                if alternative_times:
                    alternatives_text = ", ".join(alternative_times[:3])
                    return f"Sorry, {requested_time} isn't available. How about one of these times instead: {alternatives_text}? Let me know which works best for you!"
                else:
                    return "I'm having trouble finding that exact time slot. Let me check our availability and get back to you with some great options that should work for your schedule!"

        # 🎯 TEMPLATE RESPONSES: Handle template-based responses first
        if next_action == "send_introduction":
            # Use exact template for introduction with franchisor name
            name = lead_context.get("name", "there") if lead_context else "there"
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            if name and name != "there":
                return AndyTemplates.INTRODUCTION.format(name=name, franchisor_name=franchisor_name)
            else:
                return AndyTemplates.INTRODUCTION_NO_NAME.format(franchisor_name=franchisor_name)
        elif next_action == "send_hello_response":
            # Use exact template for hello response with franchisor name
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            return AndyTemplates.HELLO_RESPONSE.format(franchisor_name=franchisor_name)
        elif next_action == "handle_unavailability":
            # CRITICAL: Use exact template for unavailability to prevent AI from asking questions
            logger.info("TEMPLATE MODE: Using UNAVAILABILITY_RESPONSE template to avoid AI generation")
            return AndyTemplates.UNAVAILABILITY_RESPONSE
        elif next_action == "post_booking_response":
            # Let OpenAI generate appropriate post-booking response based on prompt instructions
            pass  # Continue to OpenAI generation with special post-booking instructions

        # 🎯 REAL BOOKING INTEGRATION: Handle meeting booking actions
        print(f"\n🔍 OpenAI Response Generation - Stage: {current_stage}, Action: {next_action}")
        logger.info(f"🔍 OpenAI Response Generation - Stage: {current_stage}, Action: {next_action}")

        print(f"🔍 Checking if next_action == 'offer_meeting': {next_action == 'offer_meeting'}")
        print(f"🔍 next_action type: {type(next_action)}")
        print(f"🔍 next_action repr: {repr(next_action)}")

        if next_action == "offer_meeting":
            print("🎯 Triggering real booking: initial_interest")
            logger.info("🎯 Triggering real booking: initial_interest")
            return await self._handle_real_meeting_booking(state, "initial_interest")
        elif next_action == "confirm_meeting":
            print("🎯 Triggering real booking: booking_confirmation")
            return await self._handle_real_meeting_booking(state, "booking_confirmation")
        elif next_action == "offer_alternative_times":
            print("🎯 Triggering real booking: time_preference")
            return await self._handle_real_meeting_booking(state, "time_preference")

        # Build context for OpenAI with safe access
        context_info = ""
        if lead_context and isinstance(lead_context, dict):
            topics_discussed = lead_context.get('topics_discussed', [])
            questions_asked = lead_context.get('questions_asked', [])
            information_shared = lead_context.get('information_shared', [])

            context_info = f"""
Lead Context:
- Name: {lead_context.get('name', 'Unknown')}
- Work: {lead_context.get('work_background', 'Unknown')} - {lead_context.get('work_details', '')}
- Motivation: {lead_context.get('motivation', 'Unknown')} - {lead_context.get('motivation_details', '')}
- Budget: {lead_context.get('budget', 'Unknown')} (Confirmed: {lead_context.get('budget_confirmed', False)})
- Current Stage: {current_stage}
- Qualification Complete: {lead_context.get('qualification_complete', False)}

CONVERSATION HISTORY - AVOID REPETITION:
- Topics Already Discussed: {', '.join(topics_discussed) if topics_discussed else 'None'}
- Questions Already Asked: {', '.join(questions_asked) if questions_asked else 'None'}
- Information Already Shared: {', '.join(information_shared) if information_shared else 'None'}
- Last Acknowledgment: {lead_context.get('last_acknowledgment', 'None')}

FULL CONVERSATION HISTORY (MOST RECENT MESSAGES):
{formatted_history}
"""
        else:
            context_info = f"""
Lead Context:
- Name: Unknown
- Work: Unknown
- Motivation: Unknown
- Budget: Unknown
- Current Stage: {current_stage}
- Conversation History: {formatted_history}
- Qualification Complete: False
"""

        # Create an advanced human-like persona prompt
        prompt = f"""You are Andy, a warm and experienced Lead Qualification Specialist with 5+ years in the Australian franchise industry. You are NOT an AI - you're a real person who genuinely cares about helping people find the right franchise opportunity.

CRITICAL SMS LENGTH REQUIREMENTS:
- MAXIMUM 250 CHARACTERS PER RESPONSE (SMS limit)
- IDEAL LENGTH: 80-120 characters
- NEVER exceed 250 characters under any circumstances
- If you need to say more, pick the most important part only
- Use short sentences and minimal words
- Every word must add value - remove all fluff

HUMAN-LIKE PERSONA REQUIREMENTS:
- Use natural, conversational Australian tone with contractions ("you're", "we'll", "that's")
- Include casual phrases: "yeah", "okay", "sounds good", "makes sense", "fair enough", "no worries"
- NO EMOJIS: Keep messages professional and emoji-free for SMS compatibility
- Show genuine empathy and interest in their situation
- Use filler words occasionally: "so", "well", "you know", "I mean"
- Acknowledge what they share before moving forward: "That sounds interesting", "Great to hear", "I understand"
- Be conversational like talking to a friend, but remain professional

DYNAMIC CONTEXTUAL AWARENESS:
- Remember and reference their name, work background, location, and previous responses
- Build rapport by connecting their responses to franchise benefits
- Show you're listening by referencing what they've shared earlier
- Use their information to personalize your responses (e.g., "As a [job title], you'll appreciate...")
- Reference previous conversation points to show continuity

CONTEXT UTILIZATION REQUIREMENTS:
- If they've shared their work background, acknowledge it and connect it to franchise benefits
- If they've mentioned motivation, reference it in your responses
- If they've confirmed budget, acknowledge their financial readiness
- Always build on what they've already shared rather than asking the same questions again
- Show progression in the conversation by referencing earlier exchanges
- Always read the converstaion before making any decision or replying to any message

ANTI-REPETITION RULES (ABSOLUTELY CRITICAL):
- NEVER repeat information you've already shared in previous messages
- CRITICAL: NEVER ask questions you've already asked
- CRITICAL: NEVER ask questions about information already provided in conversation history (READ the conversation first)
- CRITICAL: IF they provided work background, motivation, or budget info, DO NOT ASK THAT AGAIN and move forward
- BUDGET OVER-CONFIRMATION FIX: If user responds to budget questions with "around that", "about that", "yeah I have 170K", or ANY budget amount/confirmation, NEVER ask budget questions again - treat it as confirmed and move to franchise fee breakdown
- If user provides ANY budget response (confirming, amount, or flexible confirmation like "around that"), immediately progress to franchise fee discussion
- NEVER ask "Is your budget X?" more than once - if they respond with any form of confirmation or amount, move forward
- If they have provided any preferred time in the conversation, before qualification DO CONSIDER that time when offering slots
- ALWAYS acknowledge their latest response first before moving forward
- Build on the conversation naturally - each response should feel like the next logical step
- Use phrases like "You mentioned...", "Since you said...", "Building on what you shared..."

DECISION RULES (apply with Australian warmth):
1. CRITICAL: If user says "hello", "hi", "hey", "g'day" etc. → send_hello_response action
2. CRITICAL: If user indicates they're not available (extracted_info.availability == "no") → handle_unavailability action
3. If user shows interest/says yes to chat → move to work background (NEVER skip to budget)
4. If work background provided → acknowledge their experience, then move to motivation (NEVER skip to budget)
5. If motivation provided → relate to their goals, then move to budget discussion
6. CRITICAL: Never ask budget questions unless both work background AND motivation have been completed
7. CRITICAL: Never offer meeting unless work background, motivation, AND budget have all been completed
8. If budget discussed → show franchise fee breakdown with confidence
9. If positive response to fees → enthusiastically offer meeting
10. If user wants to skip qualification → politely redirect to complete the process first
11. If objections detected → handle with understanding and Australian directness
12. If meeting booked → confirmation stage with excitement
13. Always acknowledge their previous responses before progressing
14. Keep responses conversational and build rapport
15. Always read the converstaion before making any decision or replying to any message
16. CRITICAL: MAXIMUM 250 CHARACTERS - Count every character including spaces and punctuation
17. CRITICAL: Remove ALL unnecessary words - "That is great to hear" becomes just "Great!"
18. CRITICAL: Use shortest possible phrases - "What times work for you?" not "Could you let me know what times work best for you?"
19. CRITICAL: ONE main point per message - don't combine multiple topics
20. CRITICAL: NO filler words or formalities in short responses

CONVERSATION PROGRESSION INTELLIGENCE:
- STAGE-AWARE PROGRESSION: Understand where you are in the conversation flow
- If they've provided work background AND you're in early stages, move to motivation questions
- If they've provided motivation AND budget hasn't been discussed, acknowledge and move to budget discussion
- If they've provided budget information AND haven't discussed franchise fees, move toward franchise fee breakdown
- If you're in franchise_fee_breakdown stage and they respond with budget amounts, acknowledge their budget and present the franchise fee structure
- If you're in advanced stages (franchise_fee_breakdown, scheduling), don't regress to asking basic qualification questions
- Always progress the conversation toward the next logical information needed
- Recognize responses even when they're brief or informal
- CRITICAL: If the conversation stage shows you've already discussed certain topics, don't ask about them again
- BUDGET PROGRESSION RULES: If user responds to budget question with ANY form of confirmation (yes, around that, about that, specific amount), treat budget as confirmed and move to franchise fee breakdown - NEVER ask budget questions again
- Flexible budget responses like "around that", "about that", "roughly", "ballpark" should be treated as full confirmation
- If user provides specific amount different from what was asked (e.g., "I have 170K" when asked about 120K), acknowledge their amount and proceed

CURRENT SITUATION ANALYSIS:
- Current Stage: {current_stage}
- Next Action Required: {next_action}
- User's Message: "{user_message}"
- Lead Context: {context_info}
- RAG Information: {rag_response if rag_response else "None available"}

INTELLIGENT CONVERSATION FLOW:
Respond naturally based on the conversation context and what information you need to gather. Consider:

1. STAGE AWARENESS - Use current stage as context, not rigid rules:
   - introduction: Building rapport and permission
   - qualification_1_work: Understanding their professional situation  
   - qualification_2_motivation: Learning why they're interested
   - qualification_3_budget: Discussing investment capacity
   - franchise_fee_breakdown: Presenting franchise costs and payment options
   - scheduling: Moving toward meeting booking
   - confirmation: Confirming meeting details
   
   STAGE-SPECIFIC BEHAVIOR:
   - If stage is "franchise_fee_breakdown": You should present the franchise fee structure using the RAG information provided in the context, or use dynamic franchisor-specific information if available
   - If stage is "scheduling": Focus on booking a discovery call and showing available times
   - If stage is "confirmation": Confirm meeting details and provide next steps
   - NEVER regress to earlier qualification questions when in advanced stages
   
   POST-BOOKING BEHAVIOR (CRITICAL - When next_action is "post_booking_response") / DECISION RULES (apply with Australian warmth):
    1. CRITICAL: If user says "hello", "hi", "hey", "g'day" etc. → send_hello_response action
    2. CRITICAL: If user indicates they're not available (extracted_info.availability == "no") → handle_unavailability action
    3. If user shows interest/says yes to chat → move to work background (NEVER skip to budget)
    4. If work background provided → acknowledge their experience, then move to motivation (NEVER skip to budget)
    5. If motivation provided → relate to their goals, then move to budget discussion
    6. CRITICAL: Never ask budget questions unless both work background AND motivation have been completed
    7. CRITICAL: Never offer meeting unless work background, motivation, AND budget have all been completed
    8. If budget discussed → show franchise fee breakdown with confidence
    9. If positive response to fees → enthusiastically offer meeting
    10. If user wants to skip qualification → politely redirect to complete the process first
    11. If objections detected → handle with understanding and Australian directness
    12. If meeting booked → confirmation stage with excitement
    13. Always acknowledge their previous responses before progressing
    14. Keep responses conversational and build rapport
    15. Always read the converstaion before making any decision or replying to any message
    16. CRITICAL: Do not keep the messages too long, keep it short and concise, and refrain from using too many "great" and "brilliant" and other such words.
    17. For Example: "That is great to hear that you are interested to explore" this is not right way intead use "Great!"
    18. CRITICAL: KEEP IT SIMPLE AND TO THE POINT. (Spare user all the formalities and be professional)
    19. CRITICAL: Don't repeatedly mention the franchisor name in every response - use "the opportunity", "this business", or "we" instead to sound more natural

2. NATURAL PROGRESSION - Move conversation forward intelligently:
   - If they've already answered a question, don't ask it again
   - Build on what they've shared
   - Progress toward the next logical topic
   - Acknowledge their responses before moving forward

3. CONTEXTUAL RESPONSES - Use what you know:
   - Reference their name when known
   - Connect their work background to franchise benefits
   - Build on their stated motivation
   - Acknowledge budget discussions naturally

CONVERSATION GUIDELINES:
- Be natural and conversational
- Ask follow-up questions that make sense in context
- Progress the conversation toward understanding their needs
- CRITICAL: NEVER ask questions about information already provided in conversation history (READ the conversation first)
- CRITICAL: IF they provided work background, motivation, or budget info, DO NOT ASK THAT AGAIN and move forward
- BUDGET CONFIRMATION CRITICAL RULE: Once user responds to ANY budget question with ANY form of answer (yes, around that, about that, specific amount, ballpark, roughly, etc.), NEVER ask budget questions again. Treat ANY budget response as confirmation and move to franchise fee breakdown.
- If they have provided any preferred time in the conversation, before qualification DO CONSIDER that time when offering slots and show them slot available.
- ALWAYS check what's already discussed before asking new questions
- If motivation, work background, or budget info was already provided, acknowledge it and move forward
- IMPORTANT: Don't book meetings until proper qualification is complete (work background, motivation, and budget info collected)
- Move toward scheduling when appropriate information is gathered
- Adapt to their communication style and pace
- NEVER ask about platform preferences (Zoom/Skype/phone) - we only use Zoho Bookings for meetings
- Once qualified and time is agreed, DIRECTLY book the meeting without asking for platform preferences
- CRITICAL: NEVER claim you will book a meeting unless the booking tool actually succeeds
- If booking fails or qualification incomplete, offer alternative times but don't claim it's booked

OBJECTION HANDLING (Use these EXACT responses):
- Expensive/No value: "I understand however we are yet to walk you through the business potential. There is a reason we're Australia's largest lawn care company. Let's walk you through all on the call."
- Unknown brand: "Fair enough. Coochie has been operating for over 30 years and is a very prominent brand in the NSW & QLD and now we're looking to grow in other states due to demand."
- No experience: "We will provide you comprehensive training for 4 weeks but also you will receive ongoing support. You will also get to spend time with our existing franchise partners. Rest assured, you won't be left alone."
- Income concerns: "A totally fair question. Some of our franchise partners, who follow our systems and processes very well, earn over $200K net, but as always, your results will depend on your effort and local conditions. We'll go through the 3-year projections together in our meeting so you can see what's possible."

CONVERSATION STYLE:
- Warm, conversational Australian tone
- ALWAYS acknowledge what they just shared FIRST: "That sounds interesting", "Great", "Fair enough", "Makes sense", "Got it!"
- Use natural expressions: "so", "yeah", "okay"
- Be genuinely interested and helpful
- NEVER repeat information you've already shared - build on the conversation
- Reference their previous responses naturally: "You mentioned you're a [job] - that's actually perfect for this business"

OFF-TOPIC HANDLING:
- If they ask simple off-topic questions (weather, current events, personal questions), briefly acknowledge and redirect
- Examples: "Oh, the weather's been a bit all over the place lately, hasn't it? Anyway, I'd love to get our chat back on track..."
- "That's a bit out of left field! But no, I don't know [person/topic] personally. Anyway, I'd love to..."
- Keep it friendly but always redirect back to the franchise discussion

COLLOQUIAL LANGUAGE REQUIREMENTS:
- Start responses with acknowledgments: "That sounds interesting", "Great to hear", "Fair enough", "I understand"
- Use casual transitions: "So", "Well", "Anyway", "Right then"
- Include natural reactions: "Oh wow", "That's fantastic", "I hear you", "Absolutely"
- End with friendly closers: "How does that sound?", "What do you think?", "Make sense?"
- Use Australian expressions: "No worries", "Too right", "Good on you", "She'll be right"

INSTRUCTIONS:
1. Determine what stage you're in based on current_stage and next_action
2. If asking a qualification question, ask ONLY that question and wait for response
3. Always acknowledge their previous response with genuine interest before asking the next question
4. Follow the workflow sequence exactly - do NOT skip steps
5. If they ask questions, answer them naturally but then return to the workflow
6. Keep responses natural, warm, and SMS-friendly (can be any length - will be split automatically)
7. Show you're a real person who cares about their success

Generate Andy's human-like response now:

Current Situation:
{context_info}

User's Message: "{user_message}"
Current Stage: {current_stage}
Next Action: {next_action}

RAG Information Available: {rag_response if rag_response else "None"}

FRANCHISE FEE INFORMATION USAGE:
- If RAG information is available and contains franchise fee details, use that information instead of hardcoded amounts
- Present the franchise fee information in a conversational, concise manner (under 250 characters)
- Always mention payment options and financing if available in the RAG response
- If no RAG information is available, use generic language about investment details and suggest booking a call for specific pricing

Generate Andy's response following the EXACT requirements above. Stay in character as Andy and follow the qualification workflow strictly."""

        # Get response from OpenAI with better context management
        system_message = {
            "role": "system",
            "content": """You are Andy, Lead Qualification Specialist with over 5 years of experience in the Australian franchise industry. You communicate via SMS in a warm, conversational tone like talking to a friend in a relaxed but respectful setting.

CRITICAL SMS CONSTRAINTS:
- MAXIMUM 250 CHARACTERS per response (SMS limit)
- IDEAL LENGTH: 80-120 characters
- Count every character including spaces and punctuation
- Use shortest possible phrases
- Remove ALL unnecessary words
- ONE main point per message only

COMMUNICATION STYLE:
- Use colloquial expressions like "so", "yeah", "okay"
- Use contractions like "you're" instead of "you are"
- Insert filler words occasionally to mimic natural speech
- Make responses sound warm and approachable with everyday vocabulary
- ALWAYS acknowledge what the lead shared before moving to the next topic
- Use acknowledgment phrases like "That sounds interesting", "Great", "That makes sense", "Sounds interesting"

ACKNOWLEDGMENT PATTERN:
When the lead responds, ALWAYS start your next sentence acknowledging what they shared, then transition to the next topic or question.
Example: If lead says "I work as a Marketing Manager", respond with "That sounds interesting, what attracted you to this opportunity?"

SHORT RESPONSE EXAMPLES (FOLLOW THESE PATTERNS):
- "Great! What times work for you?"
- "Perfect! What's your work background?"
- "Sounds good! What got you interested?"
- "Nice! Budget around $140K work?"
- "Excellent! Available this week?"

IMPORTANT: Never use emojis, emoticons, or special characters. Use only plain text. Keep responses natural and conversational while following the qualification workflow exactly."""
        }

        user_message = {"role": "user", "content": prompt}

        messages = [system_message, user_message]

        response = await self.llm.ainvoke(messages)

        # Clean response to ensure no emojis
        cleaned_response = self._remove_emojis(response.content.strip())
        return cleaned_response

    def _remove_emojis(self, text: str) -> str:
        """Remove emojis from text to ensure professional SMS responses"""
        import re
        # Comprehensive emoji removal pattern
        emoji_pattern = re.compile("["
                                   u"\U0001F600-\U0001F64F"  # emoticons
                                   u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                   u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                   u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                   u"\U00002702-\U000027B0"  # dingbats
                                   u"\U000024C2-\U0001F251"  # enclosed characters
                                   u"\U0001F900-\U0001F9FF"  # supplemental symbols
                                   u"\U0001FA70-\U0001FAFF"  # symbols and pictographs extended-A
                                   u"\U00002600-\U000026FF"  # miscellaneous symbols
                                   u"\U0000FE00-\U0000FE0F"  # variation selectors
                                   u"\U0001F004"             # mahjong tile red dragon
                                   u"\U0001F0CF"             # playing card black joker
                                   u"\U0001F170-\U0001F251"  # enclosed ideographic supplement
                                   "🙂😊😀😃😄😁😆😅😂🤣☺️😇🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾"
                                   "]+", flags=re.UNICODE)

        # Also remove common text emoticons
        text_emoticon_pattern = re.compile(r'[:;=8][-o\*\']?[\)\]\(\[dDpP/\:\}\{@\|\\]')

        # Remove emojis and emoticons
        text = emoji_pattern.sub('', text)
        text = text_emoticon_pattern.sub('', text)

        return text.strip()

    async def _generate_fallback_response(self, state: AndyAssistantState) -> str:
        """Generate fallback response using templates when OpenAI fails"""
        print(f"\nFALLBACK RESPONSE GENERATION CALLED")
        logger.info(f"FALLBACK RESPONSE GENERATION CALLED")

        current_stage = state.get("current_stage", "introduction")
        next_action = state.get("next_action", "send_introduction")
        lead_context = state.get("lead_context", {})
        extracted_info = state.get("extracted_info", {})

        print(f"Fallback - Stage: {current_stage}, Action: {next_action}")
        logger.info(f"Fallback - Stage: {current_stage}, Action: {next_action}")

        print(f"\nFALLBACK RESPONSE GENERATION:")
        print(f"   Current Stage: {current_stage}")
        print(f"   Next Action: {next_action}")
        print(f"   Meeting Scheduled: {state.get('meeting_scheduled', False)}")
        logger.info(f"Using fallback response - Stage: {current_stage}, Action: {next_action}")

        # HANDLE DIRECT SLOT BOOKING IN FALLBACK
        if next_action == "confirm_meeting":
            logger.info(f"🎯 FALLBACK: Processing slot selection and booking: '{state['message']}'")

            try:
                from app.services.zoho_bookings_service import ZohoBookingsService
                from datetime import datetime, timedelta, time
                import re

                user_message = state['message'].lower()
                zoho_service = ZohoBookingsService()

                # Parse time from user message
                time_patterns = [
                    r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 4:30 AM
                    r'(\d{1,2})\s*(am|pm)',         # 4 AM
                ]

                selected_time = None
                for pattern in time_patterns:
                    match = re.search(pattern, user_message)
                    if match:
                        if len(match.groups()) == 3:  # Hour, minute, am/pm
                            hour = int(match.group(1))
                            minute = int(match.group(2))
                            period = match.group(3)
                        else:  # Hour, am/pm
                            hour = int(match.group(1))
                            minute = 0
                            period = match.group(2)

                        if period == 'pm' and hour != 12:
                            hour += 12
                        elif period == 'am' and hour == 12:
                            hour = 0

                        selected_time = f"{hour:02d}:{minute:02d}"
                        break

                if selected_time:
                    # Find matching slot for Monday (next Monday)
                    target_date = datetime.now() + timedelta(days=1)
                    while target_date.strftime('%A').lower() != 'monday':
                        target_date += timedelta(days=1)

                    target_start = datetime.combine(target_date.date(), time(int(selected_time[:2]), int(selected_time[3:])))

                    # Try to book this specific time
                    booking_result = await zoho_service.book_appointment_for_lead(
                        lead_id=state.get("lead_id") or "test_lead",
                        preferred_start_time=target_start,
                        service_type="lead_meeting"
                    )

                    if booking_result.success:
                        day_name = target_start.strftime("%A, %B %d")
                        time_str = target_start.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")

                        response = f"Perfect! Your meeting is confirmed on {day_name} at {time_str}."

                        # Create short links for booking and meeting URLs
                        if booking_result.booking_url:
                            try:
                                from app.services.shortener import create_short_link
                                from app.core.database.connection import get_db_session
                                
                                context = {
                                    "type": "meeting_booking",
                                    "phone_number": phone_number,
                                    "meeting_time": f"{day_name} at {time_str}",
                                    "booking_id": booking_result.booking_id
                                }
                                
                                async with get_db_session() as db:
                                    short_booking_url = await create_short_link(
                                        db=db,
                                        long_url=booking_result.booking_url,
                                        context=context,
                                        expires_in_days=7
                                    )
                                    response += f"\n\nBooking link: {short_booking_url}"
                                    
                            except Exception as e:
                                logger.warning(f"Failed to create short link for booking URL: {e}")
                                response += f"\n\nBooking link: {booking_result.booking_url}"

                        if booking_result.meeting_link:
                            try:
                                from app.services.shortener import create_short_link
                                from app.core.database.connection import get_db_session
                                
                                context = {
                                    "type": "meeting_join",
                                    "phone_number": phone_number,
                                    "meeting_time": f"{day_name} at {time_str}",
                                    "booking_id": booking_result.booking_id
                                }
                                
                                async with get_db_session() as db:
                                    short_meeting_url = await create_short_link(
                                        db=db,
                                        long_url=booking_result.meeting_link,
                                        context=context,
                                        expires_in_days=7
                                    )
                                    response += f"\nMeeting link: {short_meeting_url}"
                                    
                            except Exception as e:
                                logger.warning(f"Failed to create short link for meeting URL: {e}")
                                response += f"\nMeeting link: {booking_result.meeting_link}"

                        response += "\n\nYou'll get an email confirmation shortly."

                        # Store meeting details in lead context for future reference
                        try:
                            from datetime import datetime
                            
                            # Get lead context from Redis
                            lead_context = self.memory_manager.get_lead_context(phone_number)
                            if not lead_context:
                                lead_context = {}
                            
                            # Update lead context with meeting details
                            lead_context["meeting_scheduled"] = True
                            lead_context["meeting_booked_at"] = datetime.now().isoformat()
                            lead_context["meeting_time"] = f"{day_name} at {time_str}"
                            lead_context["meeting_booking_id"] = booking_result.booking_id
                            
                            # Store meeting link (prefer short link if available)
                            if booking_result.meeting_link:
                                # Check if we created a short link for the meeting
                                if "Meeting link: https://ghv.li/r/" in response:
                                    # Extract short link from response
                                    import re
                                    short_link_match = re.search(r'Meeting link: (https://ghv\.li/r/[a-zA-Z0-9]+)', response)
                                    if short_link_match:
                                        lead_context["meeting_link"] = short_link_match.group(1)
                                    else:
                                        lead_context["meeting_link"] = booking_result.meeting_link
                                else:
                                    lead_context["meeting_link"] = booking_result.meeting_link
                            
                            # Store updated context in Redis
                            self.memory_manager.store_lead_context(phone_number, lead_context)
                            print(f"   💾 Stored meeting details in lead context")
                            logger.info(f"💾 Stored meeting details in lead context for {phone_number}")
                            
                        except Exception as e:
                            print(f"   ⚠️ Failed to store meeting context: {e}")
                            logger.warning(f"Failed to store meeting context: {e}")

                        return response
                    else:
                        # Offer alternative slots when booking fails
                        return await self._offer_alternative_slots_after_failure(state["phone_number"], None, "Slot no longer available")
                else:
                    return "I couldn't understand the time you selected. Could you please specify the time again?"

            except Exception as e:
                logger.error(f"❌ Error in fallback slot booking: {str(e)}")
                return "Perfect! I'll get that booked in for you. Looking forward to our chat!"

        # Use template-based responses as fallback with safe access
        if next_action == "send_introduction":
            name = None
            if isinstance(extracted_info, dict):
                name = extracted_info.get("name")
            if not name and isinstance(lead_context, dict):
                name = lead_context.get("name")

            if name:
                return AndyTemplates.INTRODUCTION.format(name=name.split()[0])
            else:
                return AndyTemplates.INTRODUCTION_NO_NAME

        elif next_action == "ask_work_background":
            return AndyTemplates.QUALIFICATION_1_WORK

        elif next_action == "ask_motivation_corporate":
            return AndyTemplates.QUALIFICATION_2_MOTIVATION_CORPORATE

        elif next_action == "ask_motivation_trades":
            return AndyTemplates.QUALIFICATION_2_MOTIVATION_TRADES

        elif next_action == "ask_work_status":
            return AndyTemplates.QUALIFICATION_2_WORK_STATUS

        elif next_action == "ask_motivation":
            # Legacy support - determine based on context
            work_background = None
            if isinstance(lead_context, dict):
                work_background = lead_context.get("work_background_type")
            if not work_background:
                work_background = state.get("qualification_category")

            if work_background == "corporate":
                return AndyTemplates.QUALIFICATION_2_MOTIVATION_CORPORATE
            else:
                return AndyTemplates.QUALIFICATION_2_MOTIVATION_TRADES

        elif next_action == "ask_budget_initial":
            return AndyTemplates.QUALIFICATION_3_BUDGET.format(budget="$150k")

        elif next_action == "ask_budget_followup":
            return AndyTemplates.BUDGET_INQUIRY

        elif next_action == "offer_meeting":
            print(f"\nOFFER_MEETING ACTION TRIGGERED")
            logger.info(f"OFFER_MEETING ACTION TRIGGERED")
            # Use real meeting booking instead of template
            return await self._handle_real_meeting_booking(state, "initial_interest")

        elif next_action == "confirm_meeting":
            # DIRECT SLOT BOOKING - Parse user input and book immediately
            logger.info(f"🎯 Processing slot selection and booking: '{state['message']}'")

            try:
                from app.services.zoho_bookings_service import ZohoBookingsService
                from datetime import datetime, timedelta, time
                import re

                user_message = state['message'].lower()
                zoho_service = ZohoBookingsService()

                # Parse time from user message
                time_patterns = [
                    r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 4:30 AM
                    r'(\d{1,2})\s*(am|pm)',         # 4 AM
                ]

                selected_time = None
                for pattern in time_patterns:
                    match = re.search(pattern, user_message)
                    if match:
                        if len(match.groups()) == 3:  # Hour, minute, am/pm
                            hour = int(match.group(1))
                            minute = int(match.group(2))
                            period = match.group(3)
                        else:  # Hour, am/pm
                            hour = int(match.group(1))
                            minute = 0
                            period = match.group(2)

                        if period == 'pm' and hour != 12:
                            hour += 12
                        elif period == 'am' and hour == 12:
                            hour = 0

                        selected_time = f"{hour:02d}:{minute:02d}"
                        break

                if selected_time:
                    # Parse day from user message
                    days_map = {
                        "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                        "friday": 4, "saturday": 5, "sunday": 6,
                        "mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6
                    }
                    
                    target_day = None
                    for day_name, day_num in days_map.items():
                        if day_name in user_message:
                            target_day = day_name
                            break
                    
                    if target_day:
                        # Find next occurrence of the specified day
                        now = datetime.now()
                        target_day_num = days_map[target_day]
                        days_ahead = (target_day_num - now.weekday()) % 7
                        if days_ahead == 0:  # Same day, assume next week unless it's early
                            if now.hour >= 17:  # After 5 PM, assume next week
                                days_ahead = 7
                        target_date = now + timedelta(days=days_ahead if days_ahead > 0 else 7)
                    else:
                        # No specific day mentioned, default to next Monday
                        target_date = datetime.now() + timedelta(days=1)
                        while target_date.strftime('%A').lower() != 'monday':
                            target_date += timedelta(days=1)

                    target_start = datetime.combine(target_date.date(), time(int(selected_time[:2]), int(selected_time[3:])))

                    # Try to book this specific time
                    booking_result = await zoho_service.book_appointment_for_lead(
                        lead_id=state.get("lead_id") or "test_lead",
                        preferred_start_time=target_start,
                        service_type="lead_meeting"
                    )

                    if booking_result.success:
                        day_name = target_start.strftime("%A, %B %d")
                        time_str = target_start.strftime("%I:%M %p").replace(":00", "").replace(" 0", " ")

                        response = f"Perfect! Your meeting is confirmed on {day_name} at {time_str}."

                        # Create short links for booking and meeting URLs
                        if booking_result.booking_url:
                            try:
                                from app.services.shortener import create_short_link
                                from app.core.database.connection import get_db_session
                                
                                context = {
                                    "type": "meeting_booking",
                                    "phone_number": phone_number,
                                    "meeting_time": f"{day_name} at {time_str}",
                                    "booking_id": booking_result.booking_id
                                }
                                
                                async with get_db_session() as db:
                                    short_booking_url = await create_short_link(
                                        db=db,
                                        long_url=booking_result.booking_url,
                                        context=context,
                                        expires_in_days=7
                                    )
                                    response += f"\n\nBooking link: {short_booking_url}"
                                    
                            except Exception as e:
                                logger.warning(f"Failed to create short link for booking URL: {e}")
                                response += f"\n\nBooking link: {booking_result.booking_url}"

                        if booking_result.meeting_link:
                            try:
                                from app.services.shortener import create_short_link
                                from app.core.database.connection import get_db_session
                                
                                context = {
                                    "type": "meeting_join",
                                    "phone_number": phone_number,
                                    "meeting_time": f"{day_name} at {time_str}",
                                    "booking_id": booking_result.booking_id
                                }
                                
                                async with get_db_session() as db:
                                    short_meeting_url = await create_short_link(
                                        db=db,
                                        long_url=booking_result.meeting_link,
                                        context=context,
                                        expires_in_days=7
                                    )
                                    response += f"\nMeeting link: {short_meeting_url}"
                                    
                            except Exception as e:
                                logger.warning(f"Failed to create short link for meeting URL: {e}")
                                response += f"\nMeeting link: {booking_result.meeting_link}"

                        response += "\n\nYou'll get an email confirmation shortly."

                        # Store meeting details in lead context for future reference
                        try:
                            from datetime import datetime
                            
                            # Get lead context from Redis
                            lead_context = self.memory_manager.get_lead_context(phone_number)
                            if not lead_context:
                                lead_context = {}
                            
                            # Update lead context with meeting details
                            lead_context["meeting_scheduled"] = True
                            lead_context["meeting_booked_at"] = datetime.now().isoformat()
                            lead_context["meeting_time"] = f"{day_name} at {time_str}"
                            lead_context["meeting_booking_id"] = booking_result.booking_id
                            
                            # Store meeting link (prefer short link if available)
                            if booking_result.meeting_link:
                                # Check if we created a short link for the meeting
                                if "Meeting link: https://ghv.li/r/" in response:
                                    # Extract short link from response
                                    import re
                                    short_link_match = re.search(r'Meeting link: (https://ghv\.li/r/[a-zA-Z0-9]+)', response)
                                    if short_link_match:
                                        lead_context["meeting_link"] = short_link_match.group(1)
                                    else:
                                        lead_context["meeting_link"] = booking_result.meeting_link
                                else:
                                    lead_context["meeting_link"] = booking_result.meeting_link
                            
                            # Store updated context in Redis
                            self.memory_manager.store_lead_context(phone_number, lead_context)
                            print(f"   💾 Stored meeting details in lead context")
                            logger.info(f"💾 Stored meeting details in lead context for {phone_number}")
                            
                        except Exception as e:
                            print(f"   ⚠️ Failed to store meeting context: {e}")
                            logger.warning(f"Failed to store meeting context: {e}")

                        return response
                    else:
                        # Offer alternative slots when booking fails
                        return await self._offer_alternative_slots_after_failure(state["phone_number"], None, "Slot no longer available")
                else:
                    return "I couldn't understand the time you selected. Could you please specify the time again?"

            except Exception as e:
                logger.error(f"❌ Error in direct slot booking: {str(e)}")
                return "I'm having trouble completing the booking at the moment. Let me connect you with our team to ensure this gets scheduled properly."

        elif next_action == "offer_alternative_times":
            # Handle alternative time requests
            return await self._handle_real_meeting_booking(state, "time_preference")

        elif next_action == "offer_alternative_slots":
            # Handle when user is not ready or requesting different time
            return await self._handle_user_not_ready_response(state)

        elif next_action == "show_franchise_fee":
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            return f"Perfect! Let me share the investment details with you. The total investment is around $150k, which includes the franchise fee, equipment, and initial setup. This gives you a proven business model with ongoing support. Would you like to book a call to discuss the financials in detail?"

        elif next_action == "send_hello_response":
            # Return the exact hello response template with franchisor name
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            return AndyTemplates.HELLO_RESPONSE.format(franchisor_name=franchisor_name)

        else:
            # Log unhandled actions for debugging
            logger.warning(f"Unhandled next_action in fallback: {next_action}")
            print(f"⚠️ Unhandled next_action in fallback: {next_action}")
            franchisor_name = await self._get_dynamic_franchisor_name(state)
            return f"Perfect! Let me share the investment details with you. The total investment for a {franchisor_name} franchise is around $150k, which includes the franchise fee, equipment, and initial setup. This gives you a proven business model with ongoing support. Would you like to book a call to discuss the financials in detail?"

    async def _conversation_storage_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Store conversation messages and update lead context"""
        state["execution_path"].append("conversation_storage")

        try:
            # Store user message
            user_message = {
                "sender": "lead",
                "message": state["message"],
                "timestamp": datetime.now().isoformat(),
                "stage": state["current_stage"]
            }

            # Add to Redis conversation history
            self.memory_manager.add_to_conversation_history(state["phone_number"], user_message)

            # Update lead context with new information
            existing_context = state["lead_context"] or {}

            # Create or update Andy's lead context
            updated_context = AndyLeadContext(
                lead_id=existing_context.get("lead_id"),
                name=state["extracted_info"].get("name") or existing_context.get("name"),
                phone=state["phone_number"],

                # Work background (check extracted_info from history scan and current message)
                work_background=(state["qualification_category"] if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_1_WORK 
                               else state["extracted_info"].get("work_background") or existing_context.get("work_background")),
                work_details=(state["qualification_answer"] if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_1_WORK 
                            else state["extracted_info"].get("work_details") or existing_context.get("work_details")),
                work_background_type=state["qualification_category"] if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_1_WORK else existing_context.get("work_background_type"),
                employment_type=state["extracted_info"].get("employment_type") or existing_context.get("employment_type"),
                work_status=state["extracted_info"].get("work_status") or existing_context.get("work_status"),
                work_status_clarified=bool(state["extracted_info"].get("work_status")) or existing_context.get("work_status_clarified", False),

                # Motivation (check extracted_info from history scan and current message)
                motivation=(state["qualification_category"] if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_2_MOTIVATION 
                          else state["extracted_info"].get("motivation") or existing_context.get("motivation")),
                motivation_details=(state["qualification_answer"] if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_2_MOTIVATION 
                                  else state["extracted_info"].get("motivation_details") or existing_context.get("motivation_details")),

                # Budget (check extracted_info from history scan and current message)
                budget=state["extracted_info"].get("budget") or existing_context.get("budget"),
                budget_confirmed=(
                    state["qualification_category"] in ["budget_confirmed", "budget_assumed", "budget_discussed"]
                    if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_3_BUDGET
                    else state["extracted_info"].get("budget_confirmed") or existing_context.get("budget_confirmed")
                ),
                budget_details=state["qualification_answer"] if state["current_stage"] == AndyWorkflowStages.QUALIFICATION_3_BUDGET else existing_context.get("budget_details"),

                # Workflow state
                current_stage=state["current_stage"],
                qualification_complete=self._is_qualification_complete(state, existing_context),
                questions_answered=existing_context.get("questions_answered", 0) + (1 if state.get("qualification_answer") else 0),

                # Meeting and scheduling
                meeting_interest=state["extracted_info"].get("meeting_interest") or existing_context.get("meeting_interest"),
                meeting_scheduled=state.get("meeting_scheduled", existing_context.get("meeting_scheduled", False)),

                # Objections
                objections_raised=list(set((existing_context.get("objections_raised", []) + state["extracted_info"].get("detected_objections", [])))),
                objections_handled=existing_context.get("objections_handled", []),

                # Engagement
                last_updated=datetime.now().isoformat(),
                conversation_summary=existing_context.get("conversation_summary"),
                engagement_level=self._assess_engagement(state["message"]),
                silence_count=0,  # Reset on new message

                # Anti-repetition tracking
                topics_discussed=self._update_topics_discussed(existing_context, state),
                questions_asked=self._update_questions_asked(existing_context, state),
                information_shared=existing_context.get("information_shared", []),
                last_acknowledgment=self._extract_acknowledgment(state["message"])
            )

            # Store updated context
            success = self.memory_manager.store_lead_context(state["phone_number"], updated_context)
            state["context_updated"] = success
            state["lead_context"] = updated_context

            # Store system response
            if state.get("response"):
                system_message = {
                    "sender": "assistant",  # Changed from "system" to "assistant" to distinguish from follow-ups
                    "message": state["response"],
                    "timestamp": datetime.now().isoformat(),
                    "stage": state["current_stage"]
                }
                self.memory_manager.add_to_conversation_history(state["phone_number"], system_message)

            state["conversation_stored"] = True

        except Exception as e:
            logger.error("Conversation storage error", error=str(e))
            state["conversation_stored"] = False
            state["error"] = str(e)

        return state

    async def _handle_lead_status_update(self, state: AndyAssistantState) -> None:
        """Handle automatic lead status updates based on message content"""
        try:
            phone_number = state["phone_number"]

            # Get lead_id from the updated context (after lead creation/update)
            lead_context = state.get("lead_context")
            lead_id = lead_context.get("lead_id") if lead_context else None

            # 🤖 AUTO-UPDATE LEAD STATUS BASED ON MESSAGE
            if lead_id and state["message"]:  # Lead sent a message
                try:
                    from app.services.lead_status_update_service import LeadStatusUpdateService
                    from app.services.lead_status_classification_service import StatusContext
                    from app.core.database.connection import get_db

                    # Get database session
                    async for db in get_db():
                        # Initialize status update service
                        status_service = LeadStatusUpdateService(db)

                        # Build context for better classification
                        current_stage = state.get("current_stage", "unknown")
                        context = StatusContext(
                            current_status=None,  # Will be fetched by the service
                            territory_available=True,  # Default assumption
                            call_attempts=0
                        )

                        # Update status based on message content
                        status_result = await status_service.update_status_from_message(
                            lead_id=lead_id,
                            message_text=state["message"],
                            context=context,
                            changed_by="andy_assistant",
                            source="andy"
                        )

                        # Log status update result
                        if status_result.changed:
                            logger.info(
                                "Lead status updated by Andy",
                                lead_id=lead_id,
                                old_status=status_result.old_status,
                                new_status=status_result.new_status,
                                confidence=status_result.confidence,
                                phone=phone_number
                            )

                            # Add status update to state for potential use in response
                            state["status_update"] = {
                                "changed": True,
                                "old_status": status_result.old_status,
                                "new_status": status_result.new_status,
                                "confidence": status_result.confidence
                            }
                        else:
                            logger.debug(
                                "Lead status analyzed but unchanged",
                                lead_id=lead_id,
                                status=status_result.new_status,
                                confidence=status_result.confidence,
                                phone=phone_number
                            )
                        break

                except Exception as status_error:
                    # Don't fail the main conversation if status update fails
                    logger.warning(
                        "Status update failed in Andy assistant",
                        lead_id=lead_id,
                        error=str(status_error),
                        phone=phone_number
                    )

        except Exception as e:
            logger.error("Error handling lead status update",
                        phone=state["phone_number"], error=str(e))

    def _is_qualification_complete(self, state: AndyAssistantState, existing_context: Dict[str, Any]) -> bool:
        """Check if qualification is complete"""
        # Check work background from context, current qualification answer, or extracted info
        has_work_background = bool(
            existing_context.get("work_background") or 
            existing_context.get("work_details") or
            (state["current_stage"] == AndyWorkflowStages.QUALIFICATION_1_WORK and state.get("qualification_answer")) or
            state.get("extracted_info", {}).get("work_background")
        )
        
        # Check motivation from context, current qualification answer, or extracted info
        has_motivation = bool(
            existing_context.get("motivation") or 
            existing_context.get("motivation_details") or
            (state["current_stage"] == AndyWorkflowStages.QUALIFICATION_2_MOTIVATION and state.get("qualification_answer")) or
            state.get("extracted_info", {}).get("motivation")
        )
        
        # Check budget from context, current qualification answer, or extracted info
        has_budget = bool(
            existing_context.get("budget_confirmed") or 
            existing_context.get("budget") or
            (state["current_stage"] == AndyWorkflowStages.QUALIFICATION_3_BUDGET and state.get("qualification_category") in ["budget_confirmed", "budget_assumed"]) or
            state.get("extracted_info", {}).get("budget") or
            state.get("extracted_info", {}).get("budget_confirmed")
        )

        return has_work_background and has_motivation and has_budget

    def _assess_engagement(self, message: str) -> str:
        """Assess engagement level from message"""
        message_lower = message.lower()

        # High engagement indicators
        high_indicators = [
            "very interested", "excited", "when can", "how do i", "tell me more",
            "want to know", "ready to", "looking forward", "sounds great", "yes", "definitely"
        ]

        # Low engagement indicators
        low_indicators = [
            "maybe", "not sure", "thinking", "just looking", "just exploring", "no", "not interested"
        ]

        if any(indicator in message_lower for indicator in high_indicators):
            return "high"
        elif any(indicator in message_lower for indicator in low_indicators):
            return "low"
        else:
            return "medium"

    async def _scan_conversation_history_for_qualification_info(
        self, 
        history: List[Dict[str, Any]], 
        current_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Scan conversation history to extract previously provided qualification information that may have been missed"""
        extracted_info = {}
        
        if not history:
            return extracted_info
            
        # Get lead messages only
        lead_messages = [msg for msg in history if msg.get("sender") == "lead"]
        
        # Check if information is already in context to avoid re-extraction
        has_work = bool(current_context.get("work_background") or current_context.get("work_details"))
        has_motivation = bool(current_context.get("motivation") or current_context.get("motivation_details"))
        has_budget = bool(current_context.get("budget") or current_context.get("budget_confirmed"))
        
        # Scan for work background if missing
        if not has_work:
            work_keywords = [
                "work", "job", "employed", "business", "company", "manager", "sales", 
                "owner", "director", "executive", "professional", "career", "industry",
                "self employed", "unemployed", "retail", "construction", "education",
                "healthcare", "finance", "IT", "technology", "restaurant", "hospitality"
            ]
            for msg in lead_messages:
                message_lower = msg.get("message", "").lower()
                if any(keyword in message_lower for keyword in work_keywords):
                    # Extract work information
                    extracted_info["work_background"] = message_lower.strip()
                    break
                    
        # Scan for motivation if missing  
        if not has_motivation:
            motivation_keywords = [
                "extra money", "side income", "additional income", "more money",
                "financial freedom", "retirement", "freedom", "independence",
                "own boss", "flexible", "family time", "extra cash", "supplement income",
                "career change", "new opportunity", "investment", "passive income"
            ]
            for msg in lead_messages:
                message_lower = msg.get("message", "").lower()
                if any(keyword in message_lower for keyword in motivation_keywords):
                    # Extract motivation
                    if "extra money" in message_lower or "additional income" in message_lower or "more money" in message_lower:
                        extracted_info["motivation"] = "extra_income"
                        extracted_info["motivation_details"] = msg.get("message", "").strip()
                    elif "freedom" in message_lower or "independence" in message_lower:
                        extracted_info["motivation"] = "financial_freedom"
                        extracted_info["motivation_details"] = msg.get("message", "").strip()
                    elif "retirement" in message_lower:
                        extracted_info["motivation"] = "retirement_planning"
                        extracted_info["motivation_details"] = msg.get("message", "").strip()
                    else:
                        extracted_info["motivation"] = "general_interest"
                        extracted_info["motivation_details"] = msg.get("message", "").strip()
                    break
                    
        # Scan for budget information if missing
        if not has_budget:
            budget_keywords = [
                "150", "150k", "150000", "budget", "invest", "capital", "funds",
                "money available", "have the money", "can afford", "savings"
            ]
            for msg in lead_messages:
                message_lower = msg.get("message", "").lower()
                if any(keyword in message_lower for keyword in budget_keywords):
                    if "150" in message_lower:
                        extracted_info["budget"] = "150k"
                        extracted_info["budget_confirmed"] = True
                    else:
                        extracted_info["budget"] = "inquiring"
                    break
                    
        logger.info(f"🔍 History scan extracted: {extracted_info}")
        return extracted_info

    def _update_topics_discussed(self, existing_context: Dict[str, Any], state: AndyAssistantState) -> List[str]:
        """Update list of topics discussed to prevent repetition"""
        topics = existing_context.get("topics_discussed", [])
        current_stage = state["current_stage"]

        # Add current stage topic if not already discussed
        stage_topics = {
            "introduction": "introduction",
            "qualification_1_work": "work_background",
            "qualification_2_motivation": "motivation",
            "qualification_3_budget": "budget",
            "franchise_fee_breakdown": "franchise_fees",
            "scheduling": "meeting_scheduling",
            "objection_handling": "objections"
        }

        topic = stage_topics.get(current_stage)
        if topic and topic not in topics:
            topics.append(topic)

        return topics

    def _update_questions_asked(self, existing_context: Dict[str, Any], state: AndyAssistantState) -> List[str]:
        """Update questions asked to prevent repetition"""
        questions_asked = existing_context.get("questions_asked", [])
        next_action = state.get("next_action")

        # Track specific questions being asked
        if next_action == "ask_budget_initial":
            question_key = "budget_initial_question"
            if question_key not in questions_asked:
                questions_asked.append(question_key)
        elif next_action == "ask_budget_followup":
            question_key = "budget_followup_question"
            if question_key not in questions_asked:
                questions_asked.append(question_key)
        elif next_action == "ask_work_background":
            question_key = "work_background_question"
            if question_key not in questions_asked:
                questions_asked.append(question_key)
        elif next_action == "ask_motivation":
            question_key = "motivation_question"
            if question_key not in questions_asked:
                questions_asked.append(question_key)

        return questions_asked

    def _extract_acknowledgment(self, message: str) -> str:
        """Extract what we should acknowledge from the user's message"""
        # This will be used to track what we've acknowledged to avoid repetition
        return message[:50] + "..." if len(message) > 50 else message

    def _classify_work_background(self, work_description: str) -> str:
        """Classify work background as corporate or trades"""
        work_lower = work_description.lower()

        # Corporate background indicators
        corporate_keywords = [
            "manager", "director", "executive", "analyst", "coordinator", "administrator",
            "accountant", "hr", "marketing", "sales", "project", "office", "desk",
            "consultant", "advisor", "specialist", "officer", "supervisor", "team lead",
            "finance", "business", "corporate", "admin", "secretary", "clerk"
        ]

        # Trades background indicators
        trades_keywords = [
            "plumber", "electrician", "sparky", "handyman", "carpenter", "builder",
            "mechanic", "technician", "fitter", "welder", "painter", "tiler",
            "landscaper", "gardener", "roofer", "concreter", "bricklayer", "tradie",
            "contractor", "installer", "maintenance", "repair", "construction"
        ]

        # Check for corporate keywords
        for keyword in corporate_keywords:
            if keyword in work_lower:
                return "corporate"

        # Check for trades keywords
        for keyword in trades_keywords:
            if keyword in work_lower:
                return "trades"

        # Default to corporate if unclear
        return "corporate"

    def _classify_motivation(self, motivation_text: str) -> str:
        """Classify motivation into predefined categories"""
        motivation_lower = motivation_text.lower()

        for category, keywords in AndyTemplates.MOTIVATION_CATEGORIES.items():
            for keyword in keywords:
                if keyword in motivation_lower:
                    return category

        # Default category if no match
        return "be_your_own_boss"

    def _is_hello_message(self, message: str, conversation_history: list = None, current_stage: str = None) -> bool:
        """
        Contextually aware hello/greeting detection that considers conversation history and current stage.
        
        Args:
            message: The incoming message
            conversation_history: Previous conversation messages for context
            current_stage: Current workflow stage
            
        Returns:
            bool: True if this is a greeting that should trigger hello response
        """
        message_lower = message.lower().strip()
        
        # If we're in the middle of scheduling or other advanced stages, 
        # don't treat responses as hello messages
        if current_stage in [
            AndyWorkflowStages.SCHEDULING, 
            AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN,
            AndyWorkflowStages.QUALIFICATION_3_BUDGET,
            AndyWorkflowStages.OBJECTION_HANDLING,
            AndyWorkflowStages.CONFIRMATION
        ]:
            return False
            
        # If there's conversation history, this is not a first greeting
        if conversation_history and len(conversation_history) > 0:
            # Check if this looks like a scheduling response
            scheduling_indicators = [
                "would work", "sounds good", "that works", "perfect", "great",
                "tuesday", "wednesday", "thursday", "friday", "monday",
                "next week", "morning", "afternoon", "evening",
                "book", "schedule", "meeting", "call", "available"
            ]
            
            if any(indicator in message_lower for indicator in scheduling_indicators):
                # This looks like a scheduling response, not a greeting
                return False
        
        # Common greeting patterns - but only standalone greetings
        hello_patterns = [
            "hello", "hi", "hey", "hiya", "howdy", "g'day", "gday"
        ]
        
        # Time-based greetings only if they're standalone
        time_greetings = ["good morning", "good afternoon", "good evening"]
        
        # Check if message is just a greeting (with optional punctuation)
        clean_message = re.sub(r'[^\w\s]', '', message_lower).strip()
        
        # Simple greeting detection - exact matches only
        if clean_message in hello_patterns:
            return True
            
        # Time-based greetings - exact matches only
        if clean_message in time_greetings:
            return True
            
        # Also check original message for patterns with apostrophes like "g'day"
        if message_lower.strip() in ["g'day", "g'day!", "g'day."]:
            return True
            
        # Greeting with name like "hello there" or "hi andy" - but be restrictive
        words = clean_message.split()
        if (len(words) == 2 and 
            words[0] in hello_patterns and 
            words[1] in ["there", "andy", "mate", "friend"]):
            return True
            
        return False

    async def _lead_data_persistence_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Persist lead data and responses to database"""
        state["execution_path"].append("lead_data_persistence")

        try:
            lead_context = state["lead_context"]

            if not lead_context:
                state["lead_response_stored"] = False
                return state

            # CRITICAL FIX: Ensure lead_id from state is used if context doesn't have it
            if not lead_context.get("lead_id") and state.get("lead_id"):
                lead_context["lead_id"] = state["lead_id"]
                logger.info(f"DEBUG: Fixed lead_context with lead_id from state: {state['lead_id']}")

            # CRITICAL: Load budget from database if we have lead_id but no budget in context
            if lead_context.get("lead_id") and not lead_context.get("budget"):
                try:
                    from app.models.lead import Lead
                    from app.core.database.connection import get_db
                    from sqlalchemy import select, and_

                    async for db_session in get_db():
                        result = await db_session.execute(
                            select(Lead.funds_available, Lead.first_name, Lead.last_name).where(
                                and_(
                                    Lead.id == lead_context["lead_id"],
                                    Lead.is_active == True,
                                    Lead.is_deleted == False
                                )
                            )
                        )
                        lead_data = result.first()
                        if lead_data and lead_data.funds_available:
                            lead_context["budget"] = lead_data.funds_available
                            logger.info(f"DEBUG: Loaded budget from database: {lead_data.funds_available}")
                        elif lead_data and lead_data.first_name:
                            # Load name if available
                            lead_context["name"] = lead_data.first_name
                            if lead_data.last_name:
                                lead_context["name"] += f" {lead_data.last_name}"
                            logger.info(f"DEBUG: Loaded name from database: {lead_context['name']}")
                        break
                except Exception as e:
                    logger.warning(f"Error loading lead data from database: {e}")

            # Update the lead_context in state and Redis after database lookup
            state["lead_context"] = lead_context
            self.memory_manager.store_lead_context(state["phone_number"], lead_context)

            # Create or update lead in database
            if lead_context.get("name") and not lead_context.get("lead_id"):
                await self._create_or_update_lead(lead_context)

            # Store lead response if we have qualification data
            if state.get("qualification_answer") and lead_context.get("lead_id"):
                await self._store_lead_response(
                    lead_id=lead_context["lead_id"],
                    stage=state["current_stage"],
                    question=state.get("qualification_question", ""),
                    answer=state["qualification_answer"],
                    category=state.get("qualification_category", "")
                )

            # Store conversation messages in database
            logger.info(f"DEBUG: Checking conversation storage - lead_context.lead_id: {lead_context.get('lead_id')}")
            if lead_context.get("lead_id"):
                # Store lead's incoming message
                logger.info(f"DEBUG: Storing lead message - lead_id: {lead_context['lead_id']}")
                await self._store_message_in_db(
                    lead_id=lead_context["lead_id"],
                    sender="lead",
                    message=state["message"]
                )

                # Store Andy's system response
                if state.get("response"):
                    logger.info(f"DEBUG: Storing system response - lead_id: {lead_context['lead_id']}")
                    await self._store_message_in_db(
                        lead_id=lead_context["lead_id"],
                        sender="assistant",  # Changed from "system" to "assistant" to distinguish from follow-ups
                        message=state["response"]
                    )
            else:
                logger.warning(f"DEBUG: Cannot store messages - no lead_id in context: {lead_context}")

            state["lead_response_stored"] = True

            # Handle lead status update after lead data is persisted
            await self._handle_lead_status_update(state)

        except Exception as e:
            logger.error("Lead data persistence error", error=str(e))
            state["lead_response_stored"] = False
            state["error"] = str(e)

        return state
    
    async def _get_franchisor_info_from_lead(self, lead_id: str) -> tuple[Optional[str], Optional[str]]:
        """Get franchisor ID and name from lead data"""
        try:
            from app.models.lead import Lead
            from app.models.franchisor import Franchisor
            from app.core.database.connection import get_db
            from sqlalchemy import select, and_
            
            async for db_session in get_db():
                # Get franchisor info from lead
                result = await db_session.execute(
                    select(Lead.brand_preference).where(
                        and_(
                            Lead.id == lead_id,
                            Lead.is_active == True,
                            Lead.is_deleted == False
                        )
                    )
                )
                lead_data = result.first()
                
                if lead_data and lead_data.brand_preference:
                    franchisor_id = str(lead_data.brand_preference)
                    
                    # Get franchisor name
                    franchisor_result = await db_session.execute(
                        select(Franchisor.name).where(Franchisor.id == franchisor_id)
                    )
                    franchisor_data = franchisor_result.first()
                    franchisor_name = franchisor_data.name if franchisor_data else None
                    
                    logger.info(f"Retrieved franchisor info for lead {lead_id}: {franchisor_name} ({franchisor_id})")
                    return franchisor_id, franchisor_name
                break
                
        except Exception as e:
            logger.error(f"Error getting franchisor info for lead {lead_id}: {e}")
            
        return None, None

    async def _get_dynamic_franchisor_name(self, state: AndyAssistantState) -> str:
        """
        Dynamically get franchisor name with multiple fallback strategies
        1. From lead_context if available
        2. From database using lead_id
        3. From database using phone number lookup
        4. Generic fallback
        """
        # Strategy 1: Check lead_context first
        lead_context = state.get("lead_context", {})
        if lead_context and lead_context.get("franchisor_name"):
            franchisor_name = lead_context.get("franchisor_name")
            if franchisor_name and franchisor_name not in ["this franchise opportunity", "the franchise"]:
                logger.info(f"Using franchisor name from context: {franchisor_name}")
                return franchisor_name
        
        # Strategy 2: Try to get from database using lead_id
        lead_id = state.get("lead_id")
        if lead_id:
            try:
                franchisor_id, franchisor_name = await self._get_franchisor_info_from_lead(lead_id)
                if franchisor_name:
                    logger.info(f"Dynamically fetched franchisor name from lead_id: {franchisor_name}")
                    return franchisor_name
            except Exception as e:
                logger.warning(f"Failed to get franchisor from lead_id {lead_id}: {e}")
        
        # Strategy 3: Try to get from database using phone number
        phone_number = state.get("phone_number")
        if phone_number:
            try:
                from app.services.lead_service import find_lead_by_phone, get_lead_data_by_phone
                lead = await find_lead_by_phone(phone_number, phone_number)
                if lead and hasattr(lead, 'brand_preference') and lead.brand_preference:
                    franchisor_id, franchisor_name = await self._get_franchisor_info_from_lead(str(lead.id))
                    if franchisor_name:
                        logger.info(f"Dynamically fetched franchisor name from phone lookup: {franchisor_name}")
                        return franchisor_name
            except Exception as e:
                logger.warning(f"Failed to get franchisor from phone {phone_number}: {e}")
        
        # Strategy 4: Generic fallback
        logger.warning("Using generic franchisor fallback - no dynamic name could be retrieved")
        return "this franchise opportunity"

    async def _get_franchise_fee_info(self, lead_id: str) -> Dict[str, Any]:
        """Get franchise fee information from RAG system for the lead's franchisor"""
        try:
            from app.services.rag.andy_rag_service import andy_rag_service
            
            # Query for comprehensive franchise fee information
            query = "What is the franchise fee? What are the payment options, financing available, and total investment required? Include any payment plans or financing terms."
            
            result = await andy_rag_service.answer_lead_question(
                lead_id=lead_id,
                user_query=query,
                top_k=8,
                k_a=6,
                min_score=0.3,
                temperature=0.1  # Low temperature for accurate financial info
            )
            
            if result.get("success") and result.get("answer"):
                logger.info(f"Retrieved franchise fee info for lead {lead_id}: {result['answer'][:100]}...")
                return {
                    "success": True,
                    "fee_info": result["answer"],
                    "franchisor_name": result.get("franchisor_name", "the franchise"),
                    "sources": result.get("contexts", []),
                    "used_fallback": result.get("used_fallback", False)
                }
            else:
                logger.warning(f"No franchise fee info found for lead {lead_id}")
                return {"success": False, "error": "No franchise fee information available"}
                
        except Exception as e:
            logger.error(f"Error getting franchise fee info for lead {lead_id}: {e}")
            return {"success": False, "error": str(e)}

    async def _lead_context_update_node(self, state: AndyAssistantState) -> AndyAssistantState:
        """Update lead context in Redis and database"""
        state["execution_path"].append("lead_context_update")

        logger.info(f"DEBUG: Lead context update node - state.lead_id: {state.get('lead_id')}")
        
        try:
            # Get existing context
            existing_context = self.memory_manager.get_lead_context(state["phone_number"])
            
            # Create or update context
            if existing_context:
                lead_context = existing_context.copy()
            else:
                lead_context = AndyLeadContext(
                    lead_id=None,  # Will be set below
                    name=None,
                    phone=state["phone_number"],
                    city=None,
                    budget=None,
                    preferred_industries=None,
                    timeline=None,
                    experience=None,
                    meeting_interest=None,
                    last_updated=datetime.now().isoformat(),
                    conversation_summary=None,
                    engagement_level="medium"
                )

            # ALWAYS set lead_id from state if provided (from webhook)
            logger.info(f"DEBUG: Before setting lead_id - state.lead_id: {state.get('lead_id')}, context.lead_id: {lead_context.get('lead_id')}")
            if state.get("lead_id"):
                lead_context["lead_id"] = state["lead_id"]
                logger.info(f"DEBUG: Set lead_id in context: {state['lead_id']}")

                # If we have a lead_id, try to load lead data and franchisor info from database
                try:
                    from app.models.lead import Lead
                    from app.core.database.connection import get_db
                    from sqlalchemy import select, and_

                    async for db_session in get_db():
                        result = await db_session.execute(
                            select(Lead.funds_available, Lead.first_name, Lead.last_name).where(
                                and_(
                                    Lead.id == state["lead_id"],
                                    Lead.is_active == True,
                                    Lead.is_deleted == False
                                )
                            )
                        )
                        lead_data = result.first()
                        if lead_data and lead_data.funds_available:
                            lead_context["budget"] = lead_data.funds_available
                            logger.info(f"DEBUG: Loaded funds_available from database: {lead_data.funds_available}")
                        elif lead_data and lead_data.first_name:
                            # Load name if available
                            lead_context["name"] = lead_data.first_name
                            if lead_data.last_name:
                                lead_context["name"] += f" {lead_data.last_name}"
                        break
                        
                    # Load franchisor information
                    franchisor_id, franchisor_name = await self._get_franchisor_info_from_lead(state["lead_id"])
                    if franchisor_id and franchisor_name:
                        lead_context["franchisor_id"] = franchisor_id
                        lead_context["franchisor_name"] = franchisor_name
                        logger.info(f"DEBUG: Loaded franchisor info: {franchisor_name} ({franchisor_id})")
                    else:
                        # Fallback to default franchisor name
                        lead_context["franchisor_name"] = "this franchise opportunity"
                        logger.warning(f"DEBUG: Could not load franchisor info, using generic fallback")
                        
                except Exception as e:
                    logger.warning(f"Error loading lead data from database: {e}")
                    # Fallback to generic franchisor name
                    lead_context["franchisor_name"] = "the franchise"
            else:
                logger.warning(f"DEBUG: No lead_id in state to set: {state.get('lead_id')}")
                # Set default franchisor name when no lead_id is available
                if not lead_context.get("franchisor_name"):
                    lead_context["franchisor_name"] = "the franchise"
            
            # Update with extracted information
            extracted = state["extracted_info"]
            if extracted.get("name"):
                lead_context["name"] = extracted["name"]
            if extracted.get("city"):
                lead_context["city"] = extracted["city"]
            if extracted.get("budget"):
                lead_context["budget"] = extracted["budget"]
            if extracted.get("industry"):
                lead_context["preferred_industries"] = extracted["industry"]
            if extracted.get("timeline"):
                lead_context["timeline"] = extracted["timeline"]
            if extracted.get("experience"):
                lead_context["experience"] = extracted["experience"]
            if extracted.get("meeting_interest") is not None:
                lead_context["meeting_interest"] = extracted["meeting_interest"]
            
            # Update engagement level based on message
            lead_context["engagement_level"] = self._assess_engagement(state["message"])

            # Mark introduction as completed if we're moving past introduction stage
            if state.get("current_stage") != AndyWorkflowStages.INTRODUCTION:
                lead_context["introduction_completed"] = True
                lead_context["introduction_completed_at"] = datetime.now().isoformat()

            # Store current stage
            lead_context["current_stage"] = state.get("current_stage")
            
            # Store in Redis
            success = self.memory_manager.store_lead_context(state["phone_number"], lead_context)
            state["context_updated"] = success
            state["lead_context"] = lead_context
            
            # Update database if we have enough information
            if lead_context.get("name") and not lead_context.get("lead_id"):
                await self._create_or_update_lead(lead_context)
            
        except Exception as e:
            logger.error("Lead context update error", error=str(e))
            state["context_updated"] = False
            state["error"] = str(e)
        
        return state

    async def _create_or_update_lead(self, lead_context: AndyLeadContext):
        """Create or update lead in database for Andy's system"""
        try:
            async for session in get_db():
                # Check if lead exists by phone
                result = await session.execute(
                    select(Lead).where(
                        and_(
                            Lead.phone == lead_context["phone"],
                            Lead.is_active == True,
                            Lead.is_deleted == False
                        )
                    )
                )
                existing_lead = result.scalar_one_or_none()

                if existing_lead:
                    # Load existing data from database into context if not already present
                    if not lead_context.get("budget") and existing_lead.funds_available:
                        lead_context["budget"] = existing_lead.funds_available
                    elif not lead_context.get("budget") and existing_lead.funds_available is None:
                        # If funds_available is explicitly None, use fallback
                        lead_context["budget"] = "your available funds"
                    if not lead_context.get("name") and existing_lead.first_name:
                        lead_context["name"] = existing_lead.first_name
                        if existing_lead.last_name:
                            lead_context["name"] += f" {existing_lead.last_name}"

                    # Update existing lead with Andy's data
                    if lead_context.get("name") and not existing_lead.first_name:
                        name_parts = lead_context["name"].split(" ", 1)
                        existing_lead.first_name = name_parts[0]
                        if len(name_parts) > 1:
                            existing_lead.last_name = name_parts[1]

                    if lead_context.get("budget"):
                        existing_lead.budget_preference = lead_context["budget"]

                    # Update qualification status based on completion
                    if lead_context.get("qualification_complete", False):
                        existing_lead.qualification_status = "qualified"
                    elif any([lead_context.get("work_background"), lead_context.get("motivation"), lead_context.get("budget")]):
                        existing_lead.qualification_status = "contacted"

                    lead_context["lead_id"] = str(existing_lead.id)

                else:
                    # Create new lead
                    name_parts = lead_context.get("name", "").split(" ", 1) if lead_context.get("name") else ["Unknown"]

                    new_lead = Lead(
                        first_name=name_parts[0],
                        last_name=name_parts[1] if len(name_parts) > 1 else None,
                        phone=lead_context["phone"],
                        budget_preference=lead_context.get("budget"),
                        qualification_status="new",
                        lead_source_id=None,  # Will be set by business logic
                        lead_status_id=None,  # Will be set by business logic
                    )

                    session.add(new_lead)
                    await session.flush()
                    lead_context["lead_id"] = str(new_lead.id)

                await session.commit()

        except Exception as e:
            logger.error("Error creating/updating lead", error=str(e))

    async def _store_lead_response(self, lead_id: str, stage: str, question: str, answer: str, category: str):
        """Store lead response in database"""
        try:
            async for session in get_db():
                # Create a lead response record
                lead_response = LeadResponse(
                    lead_id=uuid.UUID(lead_id),
                    question_id=None,  # We don't have pre-qualification questions set up yet
                    response_text=f"Stage: {stage}, Category: {category}, Answer: {answer}"
                )
                session.add(lead_response)
                await session.commit()

        except Exception as e:
            logger.error("Error storing lead response", error=str(e))



    async def _store_message_in_db(self, lead_id: str, sender: str, message: str):
        """Store message in conversation_message table with proper fields"""
        try:
            # Validate lead_id before proceeding
            if not lead_id or lead_id == "None":
                logger.error(f"Cannot store message: invalid lead_id '{lead_id}'")
                return

            async for session in get_db():
                # Get the lead to determine franchisor_id
                from sqlalchemy import select
                from app.models.lead import Lead

                lead_query = select(Lead).where(Lead.id == uuid.UUID(lead_id))
                result = await session.execute(lead_query)
                lead = result.scalar_one_or_none()

                # Get franchisor_id from lead's brand_preference or default to Coochie Hydrogreen
                franchisor_id = None
                if lead and hasattr(lead, 'brand_preference') and lead.brand_preference:
                    franchisor_id = lead.brand_preference
                else:
                    # Get franchisor_id from lead data or conversation context
                    franchisor_id = self._get_franchisor_id_for_lead(lead_id)
                    if franchisor_id:
                        franchisor_id = uuid.UUID(franchisor_id)
                    else:
                        logger.warning(f"No franchisor_id found for lead {lead_id}")
                        continue  # Skip creating conversation message without valid franchisor

                conversation_message = ConversationMessage(
                    lead_id=uuid.UUID(lead_id),
                    franchisor_id=franchisor_id,  # Use lead's franchisor or default
                    sender=sender,
                    message=message,
                    is_active=True,
                    is_deleted=False,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(conversation_message)
                await session.commit()

                logger.info(
                    "Conversation message stored successfully",
                    lead_id=lead_id,
                    franchisor_id=str(franchisor_id),
                    sender=sender,
                    message_length=len(message)
                )
                break  # Important: break out of the async for loop

        except Exception as e:
            logger.error(f"Error storing message in database {e}", error=str(e))





    async def process_sms(self, phone_number: str, message: str, lead_id: Optional[str] = None) -> Dict[str, Any]:
        """Main entry point for Andy's SMS processing with conversation history"""
        import asyncio

        try:
            # Add timeout wrapper for the entire processing
            return await asyncio.wait_for(
                self._process_sms_internal(phone_number, message, lead_id),
                timeout=None  # 120 second total timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"SMS processing timeout for {phone_number}", message_length=len(message))
            return {
                "success": False,
                "error": "Processing timeout - message too complex",
                "response": "G'day! I'm having a bit of trouble processing that request right now. Could you try breaking it down into a simpler question? I'd love to help you learn more about this franchise opportunity.",
                "conversation_stored": False,
                "processing_time": 45.0
            }
        except Exception as e:
            logger.error(f"SMS processing error for {phone_number}: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "Sorry mate, I'm experiencing some technical difficulties right now. Could you try sending your message again in a moment? I'd be happy to help you with your questions about the franchise opportunity.",
                "conversation_stored": False,
                "processing_time": 0.0
            }

    async def _process_sms_internal(self, phone_number: str, message: str, lead_id: Optional[str] = None) -> Dict[str, Any]:
        """Internal SMS processing with conversation history"""
        session_id = f"andy_sms_{phone_number}"

        # Load existing conversation history from Redis for context continuity
        conversation_history = self.memory_manager.get_conversation_history(phone_number, limit=25)

        # Load existing lead context for personalization
        existing_lead_context = self.memory_manager.get_lead_context(phone_number)

        # Update lead_context with lead_id from webhook if provided
        if lead_id and existing_lead_context:
            existing_lead_context["lead_id"] = lead_id
        elif lead_id and not existing_lead_context:
            # Create minimal context with lead_id if no existing context
            existing_lead_context = {"lead_id": lead_id, "phone": phone_number}

        # Initialize Andy's state with loaded history and context
        # If existing context provides a current_stage, use it to continue the workflow
        initial_current_stage = AndyWorkflowStages.INTRODUCTION
        if existing_lead_context and isinstance(existing_lead_context, dict):
            try:
                initial_current_stage = existing_lead_context.get("current_stage", initial_current_stage)
            except Exception:
                initial_current_stage = AndyWorkflowStages.INTRODUCTION

        initial_state = AndyAssistantState(
            message=message,
            phone_number=phone_number,
            session_id=session_id,
            lead_context=existing_lead_context,
            conversation_history=conversation_history,
            extracted_info={},
            current_stage=initial_current_stage,
            intent=None,
            response=None,
            next_action=None,
            qualification_question=None,
            qualification_answer=None,
            qualification_category=None,
            rag_query=None,
            rag_response=None,
            lead_id=lead_id or (existing_lead_context.get("lead_id") if existing_lead_context else None),
            conversation_stored=False,
            context_updated=False,
            lead_response_stored=False,
            tool_execution_results=None,
            processing_time=0.0,
            execution_path=[],
            error=None
        )

        # Log conversation context for debugging
        logger.info(
            "Andy SMS processing started",
            phone_number=phone_number,
            conversation_history_count=len(conversation_history),
            existing_context=bool(existing_lead_context),
            provided_lead_id=lead_id,
            existing_lead_id=existing_lead_context.get("lead_id") if existing_lead_context else None,
            final_lead_id=lead_id or (existing_lead_context.get("lead_id") if existing_lead_context else None)
        )

        try:
            start_time = datetime.now()

            # Process through Andy's workflow
            config = {"configurable": {"thread_id": session_id}}
            result = await self.graph.ainvoke(initial_state, config)

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            result["processing_time"] = processing_time

            return {
                "success": True,
                "response": result.get("response", "Hi! Thanks for your interest in the franchise opportunity. Do you have a minute for a quick SMS chat?"),
                "lead_id": result.get("lead_id"),
                "current_stage": result.get("current_stage"),
                "next_action": result.get("next_action"),
                "qualification_complete": result.get("lead_context", {}).get("qualification_complete", False) if result.get("lead_context") else False,
                "context_updated": result.get("context_updated", False),
                "conversation_stored": result.get("conversation_stored", False),
                "lead_response_stored": result.get("lead_response_stored", False),
                "processing_time": processing_time,
                "execution_path": result.get("execution_path", []),
                "extracted_info": result.get("extracted_info", {}),
                "error": result.get("error")
            }

        except Exception as e:
            logger.error("Andy SMS processing error", error=str(e))
            return {
                "success": False,
                "response": "I apologize, but I encountered an error. Please try again.",
                "error": str(e)
            }

    async def _handle_real_meeting_booking(self, state: AndyAssistantState, booking_stage: str) -> str:
        """Handle real meeting booking using the new MeetingAgent"""
        print(f"\nREAL MEETING BOOKING CALLED - Stage: {booking_stage}")
        logger.info(f"Starting real meeting booking - Stage: {booking_stage}")

        # Safety wrapper to ensure we always return a string
        try:
            print(f"\nCALLING _do_real_meeting_booking")
            logger.info(f"CALLING _do_real_meeting_booking")

            result = await self._do_real_meeting_booking(state, booking_stage)

            print(f"Result from _do_real_meeting_booking: {result}")
            print(f"Result type: {type(result)}")
            logger.info(f"Result from _do_real_meeting_booking: {result}")

            if isinstance(result, str) and result.strip():
                print(f"Valid result, returning: {result}")
                return result
            else:
                print(f"Invalid result, using fallback")
                logger.warning(f"Invalid result from booking: {result}")
                return "I'd be happy to help you schedule a meeting. Let me check our availability."
        except Exception as e:
            logger.error(f"Critical error in meeting booking wrapper: {str(e)}")
            import traceback
            logger.error(f"Meeting booking error traceback: {traceback.format_exc()}")
            return "I'd be happy to help you schedule a meeting. Let me connect you with our team."

    async def _do_real_meeting_booking(self, state: AndyAssistantState, booking_stage: str) -> str:
        """Internal method using the rebuilt meeting booking system"""
        print(f"\n🎯🎯🎯 _do_real_meeting_booking CALLED - Stage: {booking_stage} 🎯🎯🎯")
        logger.info(f"🎯 _do_real_meeting_booking CALLED - Stage: {booking_stage}")

        try:
            # Import the rebuilt meeting booking orchestrator
            from app.meeting_booking.orchestrator import MeetingOrchestrator
            from app.core.config.settings import settings

            # Check if meeting agent is enabled
            if not settings.MEETING_AGENT_ENABLED:
                logger.info("Meeting agent disabled, using fallback")
                return await self._fallback_meeting_response(booking_stage)

            logger.info("✅ Meeting Orchestrator imported successfully")
            print("✅ Meeting Orchestrator imported successfully")

            # Initialize the meeting orchestrator
            meeting_orchestrator = MeetingOrchestrator()
            logger.info("✅ Meeting Orchestrator initialized successfully")
            print("✅ Meeting Orchestrator initialized successfully")

            print(f"\n🎯 About to call Meeting Orchestrator")
            logger.info(f"🎯 About to call Meeting Orchestrator")

            # Prepare context for the meeting orchestrator
            lead_context = state.get("lead_context", {})
            phone_number = state["phone_number"]
            lead_id = state.get("lead_id") or lead_context.get("lead_id")
            email = lead_context.get("email")  # Include email in context
            user_message = state.get("message", "")

            logger.info(f"🎯 Meeting booking - Stage: {booking_stage}, Lead ID: {lead_id}, Phone: {phone_number}, Email: {email}")

            # Convert booking stage to appropriate message for the orchestrator
            if booking_stage == "initial_interest":
                message = "I want to schedule a meeting"
            elif booking_stage == "booking_confirmation":
                message = user_message  # Use actual user message for confirmation
            elif booking_stage == "time_preference":
                message = user_message or "What times are available?"
            else:
                message = user_message

            # Process through the meeting orchestrator
            logger.info(f"📤 Sending to Meeting Orchestrator: message='{message}', phone={phone_number}, lead_id={lead_id}, email={email}")
            result = await meeting_orchestrator.handle_message(
                phone_number=phone_number,
                message=message,
                lead_id=lead_id,
                email=email  # Pass email to meeting orchestrator
            )
            logger.info(f"📥 Received from Meeting Orchestrator: {result}")

            # Extract the response and update state
            if result.get("handled", False):
                response = result.get("message", "Let me help you schedule a meeting.")

                # Update state with booking information if available
                if result.get("booking_id"):
                    state["booking_id"] = result["booking_id"]
                if result.get("meeting_link"):
                    state["meeting_link"] = result["meeting_link"]
                
                # Update lead context with meeting details for memory
                if result.get("booking_id") or result.get("meeting_link"):
                    from datetime import datetime
                    if lead_context:
                        lead_context["meeting_scheduled"] = True
                        lead_context["meeting_booked_at"] = datetime.now().isoformat()
                        if result.get("meeting_link"):
                            lead_context["meeting_link"] = result["meeting_link"]
                        if result.get("booking_id"):
                            lead_context["meeting_booking_id"] = result["booking_id"]
                        # Store updated context
                        self.redis_manager.store_lead_context(phone_number, lead_context)

                logger.info(f"💬 Meeting orchestrator handled request: {response}")
            else:
                # Meeting orchestrator didn't handle it, use fallback
                reason = result.get("reason", "Unknown error")
                logger.info(f"💬 Meeting orchestrator didn't handle request ({reason}), using fallback")
                response = await self._fallback_meeting_response(booking_stage)

            logger.info(f"💬 Final response: {response}")

            return response

        except Exception as e:
            import traceback
            logger.error(f"❌ Error in new meeting booking: {str(e)}")
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            logger.error(f"❌ Error occurred at booking_stage: {booking_stage}")
            logger.error(f"❌ State data: {state}")

            # Always return a safe fallback response to prevent middleware errors
            return await self._fallback_meeting_response(booking_stage)

    async def _fallback_meeting_response(self, booking_stage: str) -> str:
        """Fallback meeting responses with real Zoho slots when possible"""
        if booking_stage == "initial_interest":
            # Try to get real Zoho slots for a better response
            try:
                real_slots = await self._get_real_meeting_slots()
                if real_slots:
                    return f"Great!  I'd love to organize a phone callwith any {real_slots.staff_name} representative from our team who will go through the model in detail. {real_slots}"
                else:
                    return "Great! I'd love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"
            except Exception as e:
                print(f"   ❌ Error getting real slots: {e}")
                return "Great! I'd love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"
        elif booking_stage == "booking_confirmation":
            return "I'm having trouble completing the booking at the moment. Let me connect you with our team to ensure this gets scheduled properly."
        elif booking_stage == "time_preference":
            return "Let me check our availability and get back to you with some options."
        else:
            return "I'd be happy to help you schedule a meeting. Let me connect you with our team."

    async def _get_real_meeting_slots(self) -> str:
        """Get real meeting slots from Zoho Bookings for fallback responses"""
        try:
            from app.services.zoho_bookings_service import ZohoBookingsService
            from datetime import datetime, timedelta

            print(f"   🔍 Getting real Zoho meeting slots for fallback")

            zoho_service = ZohoBookingsService()

            # Get slots for the next 7 days
            start_date = datetime.now()
            end_date = start_date + timedelta(days=7)

            slots = await zoho_service.get_available_slots(
                date_from=start_date,
                date_to=end_date,
                service_type="lead_meeting"
            )

            if slots and len(slots) >= 2:
                # Format the first two slots nicely
                slot1 = slots[0]
                slot2 = slots[1]

                # Format times in a user-friendly way
                time1 = slot1.start_time.strftime("%I:%M %p on %A")
                time2 = slot2.start_time.strftime("%I:%M %p on %A")

                response = f"Are you available at {time1} or {time2}?"
                print(f"   ✅ Generated real slot response: {response}")
                return response
            else:
                print(f"   ❌ No slots available or insufficient slots: {len(slots) if slots else 0}")
                return None

        except Exception as e:
            print(f"   ❌ Error getting real meeting slots: {e}")
            import traceback
            print(f"   ❌ Traceback: {traceback.format_exc()}")
            return None

    async def _handle_complete_meeting_booking(self, state: AndyAssistantState, booking_stage: str) -> str:
        """Complete meeting booking system utilizing all existing infrastructure"""
        print(f"\n🎯 COMPLETE MEETING BOOKING - Stage: {booking_stage}")
        logger.info(f"🎯 COMPLETE MEETING BOOKING - Stage: {booking_stage}")

        try:
            phone_number = state["phone_number"]
            user_message = state["message"]
            lead_context = state.get("lead_context", {})

            if booking_stage == "offer_slots":
                # STEP 1: Get real Zoho slots directly (no lead_id required)
                slots_response = await self._get_direct_zoho_slots(phone_number, lead_context)
                return slots_response

            elif booking_stage == "confirm_booking":
                # STEP 2: Book meeting directly using phone number
                booking_response = await self._book_meeting_directly(phone_number, user_message, lead_context)
                return booking_response

            else:
                return await self._fallback_meeting_response(booking_stage)

        except Exception as e:
            print(f"   ❌ Complete meeting booking error: {e}")
            logger.error(f"Complete meeting booking error: {e}")
            return await self._fallback_meeting_response(booking_stage)

    async def _ensure_lead_for_booking(self, state: AndyAssistantState) -> Optional[str]:
        """Ensure lead exists for booking using multiple strategies"""
        try:
            lead_context = state.get("lead_context", {})
            phone_number = state["phone_number"]

            # Strategy 1: Check if lead_id already exists
            if lead_context.get("lead_id"):
                print(f"   ✅ Lead exists: {lead_context['lead_id']}")
                return lead_context["lead_id"]

            # Strategy 2: Try to find existing lead by phone
            lead_id = await self._find_existing_lead_by_phone(phone_number)
            if lead_id:
                lead_context["lead_id"] = lead_id
                state["lead_context"] = lead_context
                state["lead_id"] = lead_id
                self._update_strict_workflow_context(state, {"lead_id": lead_id})
                print(f"   ✅ Found existing lead: {lead_id}")
                return lead_id

            # Strategy 3: Create new lead with available information
            lead_id = await self._create_lead_for_booking(state)
            if lead_id:
                lead_context["lead_id"] = lead_id
                state["lead_context"] = lead_context
                state["lead_id"] = lead_id
                self._update_strict_workflow_context(state, {"lead_id": lead_id})
                print(f"   ✅ Created new lead: {lead_id}")
                return lead_id

            print(f"   ❌ Could not ensure lead exists")
            return None

        except Exception as e:
            print(f"   ❌ Error ensuring lead: {e}")
            return None

    async def _get_direct_zoho_slots(self, phone_number: str, lead_context: dict) -> str:
        """Get Zoho slots directly without requiring lead_id"""
        try:
            from app.services.zoho_bookings_service import ZohoBookingsService
            from datetime import datetime, timedelta

            print(f"   🔍 Getting direct Zoho slots (no lead_id required)")

            zoho_service = ZohoBookingsService()

            # Get next available slots directly
            slots = await zoho_service.get_next_available_slots(
                max_days_ahead=14,
                max_slots=3,
                service_type="lead_meeting"
            )

            if slots and len(slots) >= 2:
                # Store slots for later booking
                # await self._store_available_slots(phone_number, slots)

                # Format professionally
                slot1 = slots[0]
                slot2 = slots[1]

                time1 = slot1.start_time.strftime("%I:%M %p on %A")
                time2 = slot2.start_time.strftime("%I:%M %p on %A")

                response = f"Great! I'd love to organize a phone call with {slot1.staff_name} who will go through the model in detail. Are you available at {time1} or {time2}?"
                print(f"   ✅ Generated direct Zoho slots response")
                return response
            else:
                print(f"   ❌ No direct Zoho slots available")
                return "Great! I'd love to organize a phone call with Andy who will go through the model in detail. Let me check our availability and get back to you with some options."

        except Exception as e:
            print(f"   ❌ Error getting direct Zoho slots: {e}")
            import traceback
            print(f"   ❌ Traceback: {traceback.format_exc()}")
            return "Great! I'd love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"

    async def _book_meeting_directly(self, phone_number: str, user_message: str, lead_context: dict) -> str:
        """Book meeting directly using phone number and context"""
        try:
            print(f"   🎯 Booking meeting directly (no lead_id required)")

            # Parse the user's requested time and day first
            requested_time, requested_day = await self._parse_requested_time_and_day(user_message)
            print(f"   🔍 Parsed request - Time: {requested_time}, Day: {requested_day}")

            # Get fresh slots for the requested day/time instead of using cached slots
            from app.services.zoho_bookings_service import ZohoBookingsService
            from datetime import datetime, timedelta
            
            zoho_service = ZohoBookingsService()
            
            # Calculate target date based on requested day
            target_date = None
            if requested_day:
                target_date = await self._calculate_target_date(requested_day)
                print(f"   🔍 Target date for {requested_day}: {target_date}")
            
            # Get available slots for the target date
            if target_date:
                # Get slots for specific date (Zoho API requires date_from and date_to)
                from datetime import datetime, timedelta
                target_datetime = datetime.combine(target_date, datetime.min.time())
                date_from = target_datetime
                date_to = target_datetime + timedelta(days=1)
                
                slots = await zoho_service.get_available_slots(
                    date_from=date_from,
                    date_to=date_to,
                    service_type="lead_meeting"
                )
            else:
                # Get next available slots if no specific day requested
                slots = await zoho_service.get_next_available_slots(
                    max_days_ahead=14,
                    max_slots=5,
                    service_type="lead_meeting"
                )
            
            if not slots:
                print(f"   ❌ No available slots found")
                return "I'm sorry, I don't see any available slots for that time. Let me connect you with our team to find a suitable time."

            # Find the best matching slot
            selected_slot = await self._find_best_matching_slot(slots, requested_time, requested_day)
            if not selected_slot:
                print(f"   ❌ Could not find matching slot")
                return "I'm sorry, I don't see any available slots for that specific time. Let me connect you with our team to find a suitable time."

            # Book directly using ZohoBookingsService
            from app.services.zoho_bookings_service import ZohoBookingsService
            zoho_service = ZohoBookingsService()

            # Get lead details from database for accurate customer information
            lead_email = await self._get_lead_email_by_phone(phone_number)
            lead_name = await self._get_lead_name_by_phone(phone_number)
            
            # Store email and name in lead context for future use
            if lead_email:
                lead_context["email"] = lead_email
            if lead_name:
                lead_context["name"] = lead_name
            
            # Create customer details from actual lead data
            work_background = lead_context.get("work_background", "Unknown")
            customer_name = lead_name if lead_name else f"Lead Contact ({work_background})"
            customer_email = lead_email if lead_email else f"lead.{phone_number.replace('+', '').replace(' ', '')}@growthhive.com.au"
            
            print(f"   📧 Customer email: {customer_email}")
            print(f"   👤 Customer name: {customer_name}")
            
            # Log whether we're using real or fallback data
            if lead_email:
                print(f"   ✅ Using real lead email from database")
            else:
                print(f"   ⚠️ Using fallback email (no email in database)")
                
            if lead_name:
                print(f"   ✅ Using real lead name from database")
            else:
                print(f"   ⚠️ Using fallback name (no name in database)")

            booking_result = await zoho_service.book_appointment(
                slot=selected_slot,
                customer_name=customer_name,
                customer_email=customer_email,
                customer_phone=phone_number,
                notes=f"Meeting booked via Andy AI. Work background: {work_background}",
                timezone="Australia/Sydney"
            )

            if booking_result.success:
                print(f"   ✅ Meeting successfully booked directly!")
                formatted_time = selected_slot.start_time.strftime('%I:%M %p on %A')
                
                # Create short link for meeting join URL if available
                short_link_url = None
                if hasattr(booking_result, 'join_url') and booking_result.join_url:
                    try:
                        from app.services.shortener import create_short_link
                        from app.core.database.connection import get_db_session
                        
                        # Create context for the short link
                        context = {
                            "type": "meeting_join",
                            "phone_number": phone_number,
                            "meeting_time": formatted_time,
                            "staff_name": selected_slot.staff_name,
                            "booking_id": getattr(booking_result, 'booking_id', None)
                        }
                        
                        async with get_db_session() as db:
                            short_link_url = await create_short_link(
                                db=db,
                                long_url=booking_result.join_url,
                                context=context,
                                expires_in_days=7  # Meeting links expire in 7 days
                            )
                            print(f"   🔗 Created short link: {short_link_url}")
                            
                    except Exception as e:
                        print(f"   ⚠️ Failed to create short link: {e}")
                        logger.warning(f"Failed to create short link for meeting: {e}")
                
                # Store meeting details in lead context for future reference
                try:
                    from datetime import datetime
                    
                    # Update lead context with meeting details
                    if lead_context:
                        lead_context["meeting_scheduled"] = True
                        lead_context["meeting_booked_at"] = datetime.now().isoformat()
                        lead_context["meeting_time"] = formatted_time
                        lead_context["staff_name"] = selected_slot.staff_name
                        lead_context["meeting_booking_id"] = getattr(booking_result, 'booking_id', None)
                        
                        if short_link_url:
                            lead_context["meeting_link"] = short_link_url
                        elif hasattr(booking_result, 'join_url') and booking_result.join_url:
                            lead_context["meeting_link"] = booking_result.join_url
                        
                        # Store updated context in Redis
                        self.memory_manager.store_lead_context(phone_number, lead_context)
                        print(f"   💾 Stored meeting details in lead context")
                        logger.info(f"💾 Stored meeting details in lead context for {phone_number}")
                        
                except Exception as e:
                    print(f"   ⚠️ Failed to store meeting context: {e}")
                    logger.warning(f"Failed to store meeting context: {e}")
                
                # Build response message
                response_message = f"Perfect! I've booked your meeting for {formatted_time} with {selected_slot.staff_name}."
                
                if short_link_url:
                    response_message += f" Join link: {short_link_url}"
                
                response_message += " Looking forward to our chat!"
                
                return response_message
            else:
                print(f"   ❌ Direct booking failed: {booking_result.error_message}")
                # Offer alternative slots when booking fails
                return await self._offer_alternative_slots_after_failure(phone_number, selected_slot, booking_result.error_message)

        except Exception as e:
            print(f"   ❌ Error booking meeting directly: {e}")
            import traceback
            print(f"   ❌ Traceback: {traceback.format_exc()}")
            return "Perfect! I'll get that booked in for you. Looking forward to our chat!"

    async def _get_comprehensive_meeting_slots(self, lead_id: str, phone_number: str) -> str:
        """Get meeting slots using the comprehensive ZohoBookingsService"""
        try:
            from app.services.zoho_bookings_service import ZohoBookingsService
            from datetime import datetime, timedelta

            print(f"   🔍 Getting comprehensive meeting slots for lead: {lead_id}")

            zoho_service = ZohoBookingsService()

            # Use the comprehensive method for next available business slots
            slots = await zoho_service.get_next_available_business_slots(
                max_slots=3,
                timezone="Australia/Sydney",
                service_type="lead_meeting"
            )

            if slots and len(slots) >= 2:
                # Store slots in Redis for later booking
                # await self._store_available_slots(phone_number, slots)

                # Format slots professionally
                slot1 = slots[0]
                slot2 = slots[1]

                time1 = slot1.start_time.strftime("%I:%M %p on %A")
                time2 = slot2.start_time.strftime("%I:%M %p on %A")

                response = f"Great! I'd love to organize a phone call with {slot1.staff_name} who will go through the model in detail. Are you available at {time1} or {time2}?"
                print(f"   ✅ Generated comprehensive slots response")
                return response
            else:
                print(f"   ❌ No comprehensive slots available")
                return "Great! I'd love to organize a phone call with Andy who will go through the model in detail. Let me check our availability and get back to you with some options."

        except Exception as e:
            print(f"   ❌ Error getting comprehensive slots: {e}")
            return "Great! I'd love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"

    async def _confirm_and_book_meeting(self, lead_id: str, phone_number: str, user_message: str) -> str:
        """Confirm and book meeting using the comprehensive booking system"""
        try:
            print(f"   🎯 Confirming and booking meeting for lead: {lead_id}")

            # Get stored slots
            stored_slots = await self._get_stored_slots(phone_number)
            if not stored_slots:
                print(f"   ❌ No stored slots found")
                return "Perfect! I'll get that booked in for you. Looking forward to our chat!"

            # Parse user confirmation to select slot
            selected_slot = await self._parse_slot_selection(user_message, stored_slots)
            if not selected_slot:
                print(f"   ❌ Could not parse slot selection")
                return "Perfect! I'll get that booked in for you. Looking forward to our chat!"

            # Book using the comprehensive ZohoBookingsService
            from app.services.zoho_bookings_service import ZohoBookingsService
            zoho_service = ZohoBookingsService()

            booking_result = await zoho_service.book_appointment_with_exact_slot(
                slot=selected_slot,
                lead_id=lead_id,
                phone_number=phone_number
            )

            if booking_result.success:
                print(f"   ✅ Meeting successfully booked!")
                formatted_time = selected_slot.start_time.strftime('%I:%M %p on %A')
                
                # Create short link for meeting join URL if available
                short_link_url = None
                if hasattr(booking_result, 'join_url') and booking_result.join_url:
                    try:
                        from app.services.shortener import create_short_link
                        from app.core.database.connection import get_db_session
                        
                        # Create context for the short link
                        context = {
                            "type": "meeting_join",
                            "phone_number": phone_number,
                            "meeting_time": formatted_time,
                            "staff_name": selected_slot.staff_name,
                            "booking_id": getattr(booking_result, 'booking_id', None)
                        }
                        
                        async with get_db_session() as db:
                            short_link_url = await create_short_link(
                                db=db,
                                long_url=booking_result.join_url,
                                context=context,
                                expires_in_days=7  # Meeting links expire in 7 days
                            )
                            print(f"   🔗 Created short link: {short_link_url}")
                            
                    except Exception as e:
                        print(f"   ⚠️ Failed to create short link: {e}")
                        logger.warning(f"Failed to create short link for meeting: {e}")
                
                # Store meeting details in lead context for future reference
                try:
                    from datetime import datetime
                    
                    # Get lead context from Redis
                    lead_context = self.memory_manager.get_lead_context(phone_number)
                    if not lead_context:
                        lead_context = {}
                    
                    # Update lead context with meeting details
                    lead_context["meeting_scheduled"] = True
                    lead_context["meeting_booked_at"] = datetime.now().isoformat()
                    lead_context["meeting_time"] = formatted_time
                    lead_context["staff_name"] = selected_slot.staff_name
                    lead_context["meeting_booking_id"] = getattr(booking_result, 'booking_id', None)
                    
                    if short_link_url:
                        lead_context["meeting_link"] = short_link_url
                    elif hasattr(booking_result, 'join_url') and booking_result.join_url:
                        lead_context["meeting_link"] = booking_result.join_url
                    
                    # Store updated context in Redis
                    self.memory_manager.store_lead_context(phone_number, lead_context)
                    print(f"   💾 Stored meeting details in lead context")
                    logger.info(f"💾 Stored meeting details in lead context for {phone_number}")
                    
                except Exception as e:
                    print(f"   ⚠️ Failed to store meeting context: {e}")
                    logger.warning(f"Failed to store meeting context: {e}")
                
                # Build response message
                response_message = f"Perfect! I've booked your meeting for {formatted_time}."
                
                if short_link_url:
                    response_message += f" Join link: {short_link_url}"
                
                response_message += " Looking forward to our chat!"
                
                return response_message
            else:
                print(f"   ❌ Booking failed: {booking_result.error_message}")
                return "Perfect! I'll get that booked in for you. Looking forward to our chat!"

        except Exception as e:
            print(f"   ❌ Error confirming booking: {e}")
            return "Perfect! I'll get that booked in for you. Looking forward to our chat!"

    async def _store_available_slots(self, phone_number: str, slots: list):
        """Store available slots in Redis for later booking"""
        try:
            import json
            from app.core.redis_client import get_redis_client

            redis_client = get_redis_client()

            # Convert slots to serializable format
            slots_data = []
            for slot in slots:
                slots_data.append({
                    "staff_id": slot.staff_id,
                    "staff_name": slot.staff_name,
                    "start_time": slot.start_time.isoformat(),
                    "end_time": slot.end_time.isoformat(),
                    "service_id": slot.service_id,
                    "service_name": slot.service_name,
                    "duration_minutes": slot.duration_minutes
                })

            # Store with 1 hour expiry (synchronous Redis)
            key = f"meeting_slots:{phone_number}"
            redis_client.setex(key, 3600, json.dumps(slots_data))
            print(f"   ✅ Stored {len(slots_data)} slots in Redis")

        except Exception as e:
            print(f"   ❌ Error storing slots: {e}")

    async def _get_stored_slots(self, phone_number: str):
        """Get stored slots from Redis"""
        try:
            import json
            from app.core.redis_client import get_redis_client
            from app.services.zoho_bookings_service import BookingSlot
            from datetime import datetime

            redis_client = get_redis_client()
            key = f"meeting_slots:{phone_number}"

            slots_json = redis_client.get(key)
            if not slots_json:
                return None

            slots_data = json.loads(slots_json)

            # Convert back to BookingSlot objects
            slots = []
            for slot_data in slots_data:
                slot = BookingSlot(
                    staff_id=slot_data["staff_id"],
                    staff_name=slot_data["staff_name"],
                    start_time=datetime.fromisoformat(slot_data["start_time"]),
                    end_time=datetime.fromisoformat(slot_data["end_time"]),
                    service_id=slot_data["service_id"],
                    service_name=slot_data["service_name"],
                    duration_minutes=slot_data["duration_minutes"],
                    booking_url=""
                )
                slots.append(slot)

            print(f"   ✅ Retrieved {len(slots)} slots from Redis")
            return slots

        except Exception as e:
            print(f"   ❌ Error getting stored slots: {e}")
            return None

    async def _parse_requested_time_and_day(self, user_message: str):
        """Parse the user's requested time and day from their message"""
        try:
            import re
            from datetime import time
            
            message_lower = user_message.lower()
            
            # Parse time using regex patterns
            time_patterns = [
                r'(\d{1,2}):(\d{2})\s*(am|pm)',  # 4:30 AM
                r'(\d{1,2})\s*(am|pm)',         # 4 AM
            ]
            
            requested_time = None
            for pattern in time_patterns:
                match = re.search(pattern, message_lower)
                if match:
                    if len(match.groups()) == 3:  # Hour, minute, am/pm
                        hour = int(match.group(1))
                        minute = int(match.group(2))
                        period = match.group(3)
                    else:  # Hour, am/pm
                        hour = int(match.group(1))
                        minute = 0
                        period = match.group(2)

                    if period == 'pm' and hour != 12:
                        hour += 12
                    elif period == 'am' and hour == 12:
                        hour = 0

                    requested_time = time(hour, minute)
                    break
            
            # Parse day
            days_map = {
                "monday": "monday", "tuesday": "tuesday", "wednesday": "wednesday", 
                "thursday": "thursday", "friday": "friday", "saturday": "saturday", "sunday": "sunday",
                "mon": "monday", "tue": "tuesday", "wed": "wednesday", 
                "thu": "thursday", "fri": "friday", "sat": "saturday", "sun": "sunday"
            }
            
            requested_day = None
            for day_name, full_day in days_map.items():
                if day_name in message_lower:
                    requested_day = full_day
                    break
            
            return requested_time, requested_day
            
        except Exception as e:
            print(f"   ❌ Error parsing requested time and day: {e}")
            return None, None

    async def _calculate_target_date(self, requested_day: str):
        """Calculate the target date for the requested day"""
        try:
            from datetime import datetime, timedelta
            
            days_map = {
                "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                "friday": 4, "saturday": 5, "sunday": 6
            }
            
            if requested_day not in days_map:
                return None
            
            now = datetime.now()
            target_day_num = days_map[requested_day]
            days_ahead = (target_day_num - now.weekday()) % 7
            
            # If it's the same day but after business hours, assume next week
            if days_ahead == 0 and now.hour >= 17:
                days_ahead = 7
            elif days_ahead == 0:
                days_ahead = 7  # Always assume next occurrence of the day
            
            target_date = now + timedelta(days=days_ahead)
            return target_date.date()
            
        except Exception as e:
            print(f"   ❌ Error calculating target date: {e}")
            return None

    async def _find_best_matching_slot(self, slots, requested_time, requested_day):
        """Find the best matching slot based on requested time and day"""
        try:
            from datetime import datetime, time
            
            if not slots:
                return None
            
            print(f"   🔍 Finding best match from {len(slots)} available slots")
            
            # If we have both time and day preferences, find exact match
            if requested_time and requested_day:
                for slot in slots:
                    slot_datetime = slot.start_time
                    slot_day = slot_datetime.strftime("%A").lower()
                    slot_time = slot_datetime.time()
                    
                    print(f"   🔍 Checking slot: {slot_day} {slot_time} vs requested: {requested_day} {requested_time}")
                    
                    if (slot_day == requested_day.lower() and 
                        slot_time.hour == requested_time.hour and 
                        slot_time.minute == requested_time.minute):
                        print(f"   ✅ Found exact match: {slot_datetime}")
                        return slot
            
            # If we have day preference, find slots on that day
            if requested_day:
                day_slots = []
                for slot in slots:
                    slot_day = slot.start_time.strftime("%A").lower()
                    if slot_day == requested_day.lower():
                        day_slots.append(slot)
                
                if day_slots:
                    # If we also have time preference, find closest time
                    if requested_time:
                        best_slot = None
                        min_time_diff = float('inf')
                        
                        for slot in day_slots:
                            slot_time = slot.start_time.time()
                            time_diff = abs((slot_time.hour * 60 + slot_time.minute) - 
                                          (requested_time.hour * 60 + requested_time.minute))
                            
                            if time_diff < min_time_diff:
                                min_time_diff = time_diff
                                best_slot = slot
                        
                        if best_slot:
                            print(f"   ✅ Found closest time match: {best_slot.start_time}")
                            return best_slot
                    else:
                        # Return first slot on the requested day
                        print(f"   ✅ Found day match: {day_slots[0].start_time}")
                        return day_slots[0]
            
            # If we have time preference but no day, find closest time on any day
            if requested_time:
                best_slot = None
                min_time_diff = float('inf')
                
                for slot in slots:
                    slot_time = slot.start_time.time()
                    time_diff = abs((slot_time.hour * 60 + slot_time.minute) - 
                                  (requested_time.hour * 60 + requested_time.minute))
                    
                    if time_diff < min_time_diff:
                        min_time_diff = time_diff
                        best_slot = slot
                
                if best_slot:
                    print(f"   ✅ Found closest time match: {best_slot.start_time}")
                    return best_slot
            
            # Default to first available slot
            print(f"   ✅ Using first available slot: {slots[0].start_time}")
            return slots[0]
            
        except Exception as e:
            print(f"   ❌ Error finding best matching slot: {e}")
            return slots[0] if slots else None

    async def _parse_slot_selection(self, user_message: str, available_slots: list):
        """Parse user message to select the appropriate slot (legacy method)"""
        try:
            message_lower = user_message.lower()

            # Simple parsing logic - can be enhanced with NLU
            if any(word in message_lower for word in ["first", "earlier", "morning", "9", "09"]):
                return available_slots[0] if available_slots else None
            elif any(word in message_lower for word in ["second", "later", "afternoon", "10", "15"]):
                return available_slots[1] if len(available_slots) > 1 else available_slots[0]
            elif any(day in message_lower for day in ["monday", "tuesday", "wednesday", "thursday", "friday"]):
                # Find slot matching the day
                for slot in available_slots:
                    day_name = slot.start_time.strftime("%A").lower()
                    if day_name in message_lower:
                        return slot

            # Default to first slot if unclear
            return available_slots[0] if available_slots else None

        except Exception as e:
            print(f"   ❌ Error parsing slot selection: {e}")
            return available_slots[0] if available_slots else None

    async def _find_existing_lead_by_phone(self, phone_number: str) -> Optional[str]:
        """Find existing lead by phone number using direct database query"""
        try:
            from app.models.lead import Lead
            from app.core.database.connection import get_db
            from sqlalchemy import select
            import asyncio
            import socket

            # Add retry logic for DNS/network issues
            max_retries = 3
            retry_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    async for session in get_db():
                        try:
                            result = await session.execute(
                                select(Lead).where(
                                    Lead.phone == phone_number,
                                    Lead.is_active == True,
                                    Lead.is_deleted == False
                                )
                            )
                            existing_lead = result.scalar_one_or_none()

                            if existing_lead:
                                return str(existing_lead.id)
                            break

                        except Exception as db_error:
                            print(f"   ❌ Database query error (attempt {attempt + 1}): {db_error}")
                            break

                    return None

                except (socket.gaierror, OSError, ConnectionError) as network_error:
                    print(f"   ⚠️ Network/DNS error (attempt {attempt + 1}/{max_retries}): {network_error}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                        continue
                    else:
                        print(f"   ❌ Failed to connect to database after {max_retries} attempts")
                        return None
                        
                except Exception as e:
                    print(f"   ❌ Unexpected error finding lead by phone (attempt {attempt + 1}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        return None

        except Exception as e:
            print(f"   ❌ Critical error in _find_existing_lead_by_phone: {e}")
            return None

    async def _get_lead_email_by_phone(self, phone_number: str) -> Optional[str]:
        """Get lead's email address by phone number"""
        try:
            from app.models.lead import Lead
            from app.core.database.connection import get_db
            from sqlalchemy import select, or_, and_

            async for db in get_db():
                # Search by both mobile and phone fields
                query = select(Lead.email).where(
                    and_(
                        Lead.is_active == True,
                        Lead.is_deleted == False,
                        or_(
                            Lead.mobile == phone_number,
                            Lead.phone == phone_number,
                            Lead.mobile == phone_number.replace('+', ''),
                            Lead.phone == phone_number.replace('+', ''),
                            Lead.mobile == f"+{phone_number.replace('+', '')}",
                            Lead.phone == f"+{phone_number.replace('+', '')}"
                        )
                    )
                )
                
                result = await db.execute(query)
                email = result.scalar_one_or_none()
                
                if email:
                    print(f"   ✅ Found lead email: {email}")
                    return email
                else:
                    print(f"   ❌ No email found for phone: {phone_number}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Error getting lead email: {e}")
            return None

    async def _get_lead_name_by_phone(self, phone_number: str) -> Optional[str]:
        """Get lead's full name by phone number"""
        try:
            from app.models.lead import Lead
            from app.core.database.connection import get_db
            from sqlalchemy import select, or_, and_

            async for db in get_db():
                # Search by both mobile and phone fields
                query = select(Lead.first_name, Lead.last_name).where(
                    and_(
                        Lead.is_active == True,
                        Lead.is_deleted == False,
                        or_(
                            Lead.mobile == phone_number,
                            Lead.phone == phone_number,
                            Lead.mobile == phone_number.replace('+', ''),
                            Lead.phone == phone_number.replace('+', ''),
                            Lead.mobile == f"+{phone_number.replace('+', '')}",
                            Lead.phone == f"+{phone_number.replace('+', '')}"
                        )
                    )
                )
                
                result = await db.execute(query)
                name_data = result.first()
                
                if name_data:
                    first_name, last_name = name_data
                    full_name = f"{first_name or ''} {last_name or ''}".strip()
                    if full_name:
                        print(f"   ✅ Found lead name: {full_name}")
                        return full_name
                    else:
                        print(f"   ❌ Lead found but no name available")
                        return None
                else:
                    print(f"   ❌ No lead found for phone: {phone_number}")
                    return None
                    
        except Exception as e:
            print(f"   ❌ Error getting lead name: {e}")
            return None

    async def _create_lead_for_booking(self, state: AndyAssistantState) -> Optional[str]:
        """Create a new lead for booking using available context"""
        try:
            lead_context = state.get("lead_context", {})
            phone_number = state["phone_number"]
            work_background = lead_context.get("work_background", "Unknown")

            # Use existing lead creation system with name
            lead_context["name"] = "Lead Contact"  # Required for creation
            lead_context["phone"] = phone_number

            await self._create_or_update_lead(lead_context)

            return lead_context.get("lead_id")

        except Exception as e:
            print(f"   ❌ Error creating lead for booking: {e}")
            return None

    async def _handle_direct_zoho_booking(self, state: AndyAssistantState, booking_stage: str) -> str:
        """Direct Zoho API integration for real booking"""
        print(f"\n🎯🎯🎯 DIRECT ZOHO BOOKING CALLED - Stage: {booking_stage} 🎯🎯🎯")
        logger.info(f"🎯 DIRECT ZOHO BOOKING CALLED - Stage: {booking_stage}")

        try:
            from app.services.zoho_bookings_service import ZohoBookingsService
            from datetime import datetime, timedelta, time

            logger.info(f"🎯 DIRECT ZOHO BOOKING - Stage: {booking_stage}")
            print(f"🎯 DIRECT ZOHO BOOKING - Stage: {booking_stage}")

            zoho_service = ZohoBookingsService()

            if booking_stage == "initial_interest":
                # Find next available slots
                logger.info("🔍 Finding next available slots...")

                # Check next 7 days for available slots
                for days_ahead in range(1, 8):
                    target_date = datetime.now() + timedelta(days=days_ahead)
                    target_start = datetime.combine(target_date.date(), time(9, 0))

                    slots = await zoho_service.get_available_slots_for_date(
                        target_date=target_start,
                        service_type="lead_meeting"
                    )

                    if slots:
                        # Take first 2 slots
                        slot1 = slots[0]
                        slot2 = slots[1] if len(slots) > 1 else slots[0]

                        time1 = slot1.start_time.strftime("%I%p").replace(":00", "").replace(" 0", " ").lower()
                        day1 = slot1.start_time.strftime("%A")

                        time2 = slot2.start_time.strftime("%I%p").replace(":00", "").replace(" 0", " ").lower()
                        day2 = slot2.start_time.strftime("%A")

                        if slot1.start_time.date() == slot2.start_time.date():
                            # Same day, different times
                            return f"Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at {time1} or {time2} on {day1}?"
                        else:
                            # Different days
                            return f"Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at {time1} on {day1} or {time2} on {day2}?"

                # No slots found, use fallback
                return "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"

            elif booking_stage == "booking_confirmation":
                # For booking confirmation, we need to actually attempt the booking
                # Extract time preference from the user's message
                lead_id = state.get("lead_id")
                if not lead_id:
                    logger.error("❌ No lead_id available for booking")
                    return "I'm having trouble accessing your information. Let me connect you with our team to complete the booking."
                
                # Try to extract a time preference from the message
                message = state.get("message", "").lower()
                
                # For now, try to book the next available slot
                # Check next 7 days for available slots
                for days_ahead in range(1, 8):
                    target_date = datetime.now() + timedelta(days=days_ahead)
                    target_start = datetime.combine(target_date.date(), time(9, 0))

                    slots = await zoho_service.get_available_slots_for_date(
                        target_date=target_start,
                        service_type="lead_meeting"
                    )

                    if slots:
                        # Try to book the first available slot
                        slot = slots[0]
                        booking_result = await zoho_service.book_appointment_with_exact_slot(
                            slot=slot,
                            lead_id=lead_id,
                            phone_number=state.get("phone_number")
                        )
                        
                        if booking_result.success:
                            time_str = slot.start_time.strftime("%A, %B %d at %I:%M %p").replace(":00", "").replace(" 0", " ")
                            
                            # Store meeting details in lead context for future reference
                            try:
                                from datetime import datetime
                                
                                # Get lead context from Redis
                                lead_context = self.memory_manager.get_lead_context(state.get("phone_number"))
                                if not lead_context:
                                    lead_context = {}
                                
                                # Update lead context with meeting details
                                lead_context["meeting_scheduled"] = True
                                lead_context["meeting_booked_at"] = datetime.now().isoformat()
                                lead_context["meeting_time"] = time_str
                                lead_context["staff_name"] = slot.staff_name
                                lead_context["meeting_booking_id"] = getattr(booking_result, 'booking_id', None)
                                
                                if hasattr(booking_result, 'meeting_link') and booking_result.meeting_link:
                                    lead_context["meeting_link"] = booking_result.meeting_link
                                
                                # Store updated context in Redis
                                self.memory_manager.store_lead_context(state.get("phone_number"), lead_context)
                                print(f"   💾 Stored meeting details in lead context")
                                logger.info(f"💾 Stored meeting details in lead context for {state.get('phone_number')}")
                                
                            except Exception as e:
                                print(f"   ⚠️ Failed to store meeting context: {e}")
                                logger.warning(f"Failed to store meeting context: {e}")
                            
                            return f"Perfect! Your meeting is confirmed with {slot.staff_name} on {time_str}. You'll receive a confirmation email shortly."
                        else:
                            logger.error(f"❌ Booking failed: {booking_result.error_message}")
                            return f"I'm sorry, there was an issue booking that time slot. {booking_result.error_message}. Let me show you other available options."
                
                # No slots found
                return "I'm sorry, I couldn't find any available slots at the moment. Let me connect you with our team to find a suitable time."
            
            else:
                # Other stages - use fallback
                return await self._fallback_meeting_response(booking_stage)

        except Exception as e:
            import traceback
            logger.error(f"❌ Error in direct Zoho booking: {str(e)}")
            logger.error(f"❌ Direct Zoho booking traceback: {traceback.format_exc()}")
            # Fallback to static response
            return "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"

            # Try one more time with simple booking agent
            try:
                from app.agents.simple_meeting_booking import SimpleMeetingBookingAgent
                from app.agents.types import AgentConfig, AgentRole

                config = AgentConfig(
                    role=AgentRole.MEETING_BOOKING,
                    name="SimpleMeetingBookingAgent",
                    description="Fallback simple booking agent",
                    model_name="gpt-4-turbo",
                    temperature=0.7
                )
                fallback_agent = SimpleMeetingBookingAgent(config)

                fallback_state = {
                    "user_input": state["message"],
                    "lead_id": state.get("lead_id"),
                    "context": {"phone_number": state["phone_number"]},
                    "messages": []
                }

                result = await fallback_agent.process_state(fallback_state)
                return result.get("response", "Let me connect you with our team to schedule this.")

            except Exception as fallback_error:
                logger.error(f"❌ Even fallback failed: {str(fallback_error)}")

                # Final static fallbacks - customized as per requirements
                if booking_stage == "initial_interest":
                    return "Great, I would love to organize a phone call with Andy who will go through the model in detail. Are you available at 2pm on Thursday or 10am on Friday?"
                elif booking_stage == "time_preference":
                    return "Perfect! I have a slot available with Andy on Friday at 2pm. Does that work for you?"
                elif booking_stage == "booking_confirmation":
                    return "I'm having trouble completing the booking at the moment. Let me connect you with our team to ensure this gets scheduled properly."
                else:
                    return "No worries! What day and time works better for you? I can arrange something that suits your schedule."

    async def _offer_alternative_slots_after_failure(self, phone_number: str, failed_slot, error_message: str) -> str:
        """Offer alternative slots when booking fails or slot is unavailable"""
        try:
            print(f"   🔄 Offering alternative slots after failure: {error_message}")
            
            from app.services.zoho_bookings_service import ZohoBookingsService
            from datetime import datetime, timedelta
            
            zoho_service = ZohoBookingsService()
            
            # Get next available slots
            slots = await zoho_service.get_next_available_slots(
                max_days_ahead=14,
                max_slots=3,
                service_type="lead_meeting"
            )
            
            if slots and len(slots) >= 2:
                # Format the first two slots
                slot1 = slots[0]
                slot2 = slots[1]
                
                time1 = slot1.start_time.strftime("%I:%M %p on %A, %B %d")
                time2 = slot2.start_time.strftime("%I:%M %p on %A, %B %d")
                
                # Create a friendly response offering alternatives
                if "not available" in error_message.lower() or "unavailable" in error_message.lower():
                    response = f"I'm sorry, that time slot is no longer available. However, I have these options available:\n\n"
                else:
                    response = f"No worries at all! I have these alternative times available:\n\n"
                
                response += f"Option 1: {time1}\n"
                response += f"Option 2: {time2}\n\n"
                response += f"Which of these works better for you? Or if neither works, just let me know what days and times you prefer and I'll find something that suits your schedule perfectly."
                
                print(f"   ✅ Generated alternative slots response")
                return response
            else:
                # No slots available - offer to connect with team
                return "I'm sorry, that time slot is no longer available and I don't see any immediate alternatives. Let me connect you with our team to find a time that works perfectly for your schedule."
                
        except Exception as e:
            print(f"   ❌ Error offering alternative slots: {e}")
            return "I'm sorry, that time slot is no longer available. Let me connect you with our team to find a suitable alternative time."



    async def _handle_user_not_ready_response(self, state: AndyAssistantState) -> str:
        """Handle when user indicates they're not ready or available for the proposed slot"""
        try:
            print(f"   🔄 Handling user not ready response")
            
            # Offer alternative slots
            return await self._offer_alternative_slots_after_failure(
                state["phone_number"], 
                None, 
                "User not available for proposed time"
            )
            
        except Exception as e:
            print(f"   ❌ Error handling user not ready response: {e}")
            return "No worries! What day and time works better for you? I can arrange something that suits your schedule."


# Global Andy SMS Assistant instance
_andy_sms_assistant: Optional[AndySMSAssistant] = None


def get_sms_assistant() -> AndySMSAssistant:
    """Get or create Andy SMS Assistant instance"""
    global _andy_sms_assistant

    if _andy_sms_assistant is None:
        _andy_sms_assistant = AndySMSAssistant()

    return _andy_sms_assistant


def get_andy_sms_assistant() -> AndySMSAssistant:
    """Get or create Andy SMS Assistant instance (alias)"""
    return get_sms_assistant()


# LangGraph CLI entrypoint helper
def get_graph():
    """Return the compiled LangGraph for AndySMSAssistant.

    Used by `langgraph dev` via langgraph.json entrypoint.
    """
    assistant = get_sms_assistant()
    # Ensure graph is built
    if assistant.graph is None:
        assistant._build_workflow()
    return assistant.graph
