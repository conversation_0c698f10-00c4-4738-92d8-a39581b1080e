"""
Natural Conversation Engine for SMS Assistant
Creates human-like conversations with personalized greetings, context recall, and natural transitions
"""

import re
import random
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
import structlog

from app.core.logging import logger


class NaturalConversationEngine:
    """Engine for creating natural, human-like conversations"""
    
    def __init__(self):
        self.greeting_templates = {
            "morning": [
                "Good morning, {name}! 👋",
                "Morning {name}! Hope you're having a great start to your day! 😊",
                "Good morning! 🌅 How can I help you today?",
                "Hey {name}! Good morning! What's on your mind?",
                "Morning! 👋 Great to hear from you!"
            ],
            "afternoon": [
                "Good afternoon, {name}! 👋",
                "Afternoon {name}! How's your day going? 😊",
                "Good afternoon! ☀️ What can I help you with?",
                "Hey {name}! Hope you're having a good afternoon!",
                "Afternoon! 👋 Nice to connect with you!"
            ],
            "evening": [
                "Good evening, {name}! 👋",
                "Evening {name}! Hope you had a great day! 😊",
                "Good evening! 🌆 How can I assist you?",
                "Hey {name}! Good evening! What brings you here?",
                "Evening! 👋 Great to hear from you!"
            ]
        }
        
        self.context_recall_templates = [
            "Since you mentioned you're based in {city} and have a {budget} budget...",
            "Given that you're looking at {industry} opportunities in {city}...",
            "Based on what you shared about your {budget} investment range...",
            "Considering your interest in {industry} franchises...",
            "Since you're planning to start within {timeline}..."
        ]
        
        self.transition_phrases = [
            "By the way,",
            "I'm curious,",
            "Quick question -",
            "While we're chatting,",
            "Speaking of which,",
            "That reminds me,",
            "On that note,",
            "Actually,"
        ]
        
        self.acknowledgments = [
            "That's great!",
            "Perfect!",
            "Excellent!",
            "That makes sense!",
            "I love that!",
            "Fantastic!",
            "That's awesome!",
            "Wonderful!"
        ]
        
        self.clarification_starters = [
            "Just to clarify —",
            "Let me make sure I understand —",
            "To be clear —",
            "So you're asking about",
            "When you say",
            "Help me understand —"
        ]
        
        self.soft_endings = [
            "Would you like me to shortlist a few options?",
            "Should I send you some information about that?",
            "Want me to look into that for you?",
            "Would that be helpful?",
            "Does that sound interesting?",
            "What do you think?",
            "How does that sound?",
            "Would you like to know more?"
        ]
        
        # Industry-specific responses with more warmth and knowledge
        self.industry_responses = {
            "food": {
                "interest": "Food franchises are incredibly popular right now! 🍕 The industry has shown amazing resilience, and there's always demand for good food. What type of food concept interests you most?",
                "budget_match": "With your budget, you have some fantastic food franchise options! We're seeing great success with QSR concepts and casual dining in that range.",
                "location_match": "{city} has a thriving food scene - perfect for a franchise! The market there is really receptive to new concepts.",
                "support": "Food franchises typically offer comprehensive training programs, including kitchen operations, food safety, and customer service. Most provide 2-4 weeks of initial training plus ongoing support."
            },
            "retail": {
                "interest": "Retail franchises offer great opportunities! 🛍️ The key is finding the right location and concept that resonates with your local market.",
                "budget_match": "Your investment range opens up some excellent retail opportunities - from fashion to specialty stores to service retail.",
                "location_match": "{city} has strong retail markets with good foot traffic, especially in shopping centers and high-street locations.",
                "support": "Retail franchises usually provide store setup assistance, inventory management systems, marketing support, and ongoing operational guidance."
            },
            "services": {
                "interest": "Service-based franchises are fantastic! 💼 Lower overhead, great scalability, and often home-based options available.",
                "budget_match": "Service franchises often have lower startup costs, which fits your budget perfectly. Many can be started from home initially.",
                "location_match": "Service businesses do really well in markets like {city} - there's always demand for quality services.",
                "support": "Service franchises typically offer business development training, marketing systems, operational processes, and ongoing business coaching."
            },
            "beauty": {
                "interest": "Beauty and wellness franchises are booming! 💄 It's a recession-resistant industry with loyal customer bases.",
                "budget_match": "Your budget range works well for beauty franchises - from salons to wellness centers to beauty retail.",
                "location_match": "{city} has a great market for beauty services with good demographics for this industry.",
                "support": "Beauty franchises offer technical training, product knowledge, customer service systems, and marketing support to build your client base."
            }
        }

        # FAQ Knowledge Base
        self.faq_responses = {
            "support": "We provide comprehensive support including initial training (usually 2-4 weeks), ongoing operational guidance, marketing assistance, and regular check-ins. You're never alone in this journey! 😊",
            "requirements": "Requirements vary by franchise, but typically include meeting investment thresholds, passing background checks, and completing training programs. Most franchisors also look for commitment to their brand values.",
            "timeline": "The process usually takes 3-6 months from initial application to opening. This includes due diligence, financing, training, and setup. We can walk you through the specific timeline for any franchise you're interested in.",
            "financing": "Many franchises offer financing assistance or have relationships with lenders. SBA loans are also popular for franchise investments. We can connect you with financing specialists who understand franchising.",
            "roi": "ROI varies significantly by industry, location, and how well the business is operated. Most established franchises can provide financial performance representations to help you understand potential returns.",
            "territory": "Most franchises offer protected territories to prevent competition from other franchisees of the same brand. Territory size depends on population density and market potential."
        }
    
    def generate_greeting(self, lead_context: Optional[Dict[str, Any]], is_first_interaction: bool = False) -> str:
        """Generate personalized, time-based greeting"""
        current_hour = datetime.now().hour
        
        # Determine time of day
        if current_hour < 12:
            time_period = "morning"
        elif current_hour < 17:
            time_period = "afternoon"
        else:
            time_period = "evening"
        
        # Get appropriate templates
        templates = self.greeting_templates[time_period]
        
        # Select template and personalize
        template = random.choice(templates)
        
        if lead_context and lead_context.get("name"):
            name = lead_context["name"].split()[0]  # Use first name only
            return template.format(name=name)
        else:
            # Use templates without name
            return template.replace(", {name}", "").replace("{name}! ", "").replace("{name}", "there")
    
    def create_context_recall(self, lead_context: Dict[str, Any], focus: str = "general") -> Optional[str]:
        """Create natural context recall statement"""
        if not lead_context:
            return None
        
        # Collect available context
        context_items = {}
        if lead_context.get("city"):
            context_items["city"] = lead_context["city"]
        if lead_context.get("budget"):
            context_items["budget"] = lead_context["budget"]
        if lead_context.get("preferred_industries"):
            context_items["industry"] = ", ".join(lead_context["preferred_industries"])
        if lead_context.get("timeline"):
            context_items["timeline"] = lead_context["timeline"]
        
        # Need at least 2 context items for a good recall
        if len(context_items) < 2:
            return None
        
        # Select appropriate template
        suitable_templates = []
        for template in self.context_recall_templates:
            # Check if template variables are available
            template_vars = re.findall(r'\{(\w+)\}', template)
            if all(var in context_items for var in template_vars):
                suitable_templates.append(template)
        
        if not suitable_templates:
            return None
        
        # Format and return
        template = random.choice(suitable_templates)
        try:
            return template.format(**context_items)
        except KeyError:
            return None
    
    def get_natural_transition(self) -> str:
        """Get a natural conversation transition"""
        return random.choice(self.transition_phrases)
    
    def get_acknowledgment(self, enthusiasm_level: str = "medium") -> str:
        """Get appropriate acknowledgment based on enthusiasm"""
        if enthusiasm_level == "high":
            high_energy = ["That's fantastic!", "That's awesome!", "I love that!", "Excellent!"]
            return random.choice(high_energy)
        elif enthusiasm_level == "low":
            gentle = ["That makes sense!", "I understand.", "Got it!", "That's good to know."]
            return random.choice(gentle)
        else:
            return random.choice(self.acknowledgments)
    
    def get_clarification_starter(self) -> str:
        """Get a natural clarification starter"""
        return random.choice(self.clarification_starters)
    
    def get_soft_ending(self) -> str:
        """Get a soft conversation ending"""
        return random.choice(self.soft_endings)

    def get_faq_response(self, message: str) -> Optional[str]:
        """Get FAQ response based on message content"""
        message_lower = message.lower()

        # Map keywords to FAQ topics
        faq_mapping = {
            "support": ["support", "help", "assistance", "guidance", "training"],
            "requirements": ["requirements", "qualify", "criteria", "eligible", "need"],
            "timeline": ["timeline", "how long", "when", "time", "process", "steps"],
            "financing": ["financing", "loan", "money", "fund", "capital", "investment"],
            "roi": ["roi", "return", "profit", "money", "income", "revenue", "earnings"],
            "territory": ["territory", "location", "area", "region", "exclusive", "protected"]
        }

        for topic, keywords in faq_mapping.items():
            if any(keyword in message_lower for keyword in keywords):
                return self.faq_responses.get(topic)

        return None

    def detect_meeting_interest(self, message: str) -> bool:
        """Detect if lead is interested in scheduling a meeting"""
        message_lower = message.lower()

        meeting_indicators = [
            "call", "meeting", "discuss", "talk", "schedule", "appointment",
            "speak", "chat", "connect", "meet", "consultation", "demo"
        ]

        return any(indicator in message_lower for indicator in meeting_indicators)

    def generate_meeting_response(self, lead_context: Optional[Dict[str, Any]] = None) -> str:
        """Generate response for meeting scheduling"""
        name = lead_context.get("name", "").split()[0] if lead_context and lead_context.get("name") else ""

        responses = [
            f"Absolutely{', ' + name if name else ''}! I'd love to set up a call to discuss your franchise options in detail. 📞 What time works best for you this week?",
            f"Perfect{', ' + name if name else ''}! Let's schedule a detailed discussion. I can walk you through the specific opportunities that match your interests and budget. When are you available?",
            f"Great idea{', ' + name if name else ''}! A call would be the perfect next step. We can dive deeper into the franchises that fit your criteria. What's your preferred time for a conversation?",
            f"I'd be happy to arrange that{', ' + name if name else ''}! 😊 A detailed discussion will help us find the perfect franchise match for you. Are you free for a call this week?"
        ]

        return random.choice(responses)
    
    def create_industry_response(self, industry: str, context: Dict[str, Any], response_type: str = "interest") -> Optional[str]:
        """Create industry-specific response"""
        industry_lower = industry.lower()
        
        # Map common variations
        industry_mapping = {
            "restaurant": "food",
            "cafe": "food",
            "coffee": "food",
            "store": "retail",
            "shop": "retail",
            "boutique": "retail",
            "consulting": "services",
            "cleaning": "services",
            "fitness": "services"
        }
        
        mapped_industry = industry_mapping.get(industry_lower, industry_lower)
        
        if mapped_industry not in self.industry_responses:
            return None
        
        response_template = self.industry_responses[mapped_industry].get(response_type)
        if not response_template:
            return None
        
        # Format with context if needed
        try:
            return response_template.format(**context)
        except KeyError:
            return response_template
    
    def humanize_response(self, response: str, lead_context: Optional[Dict[str, Any]] = None) -> str:
        """Make response more human-like"""
        # Remove overly formal language
        replacements = {
            "I would be happy to": "I'd love to",
            "I would like to": "I'd love to",
            "Please let me know": "Let me know",
            "Thank you for your inquiry": "Thanks for reaching out",
            "I am pleased to": "I'm excited to",
            "I will be glad to": "I'd be happy to",
            "Please feel free to": "Feel free to",
            "I would recommend": "I'd suggest",
            "It would be advisable": "I'd recommend",
            "I would suggest that you": "You might want to"
        }
        
        for formal, casual in replacements.items():
            response = response.replace(formal, casual)
        
        # Add contractions
        contractions = {
            "I am": "I'm",
            "you are": "you're",
            "we are": "we're",
            "they are": "they're",
            "it is": "it's",
            "that is": "that's",
            "there is": "there's",
            "I will": "I'll",
            "you will": "you'll",
            "we will": "we'll",
            "I would": "I'd",
            "you would": "you'd",
            "we would": "we'd",
            "cannot": "can't",
            "do not": "don't",
            "does not": "doesn't",
            "will not": "won't",
            "should not": "shouldn't",
            "could not": "couldn't"
        }
        
        for full, contraction in contractions.items():
            response = response.replace(full, contraction)
            response = response.replace(full.title(), contraction.title())
        
        return response
    
    def split_for_sms(self, message: str, max_length: int = 160) -> List[str]:
        """Split message into SMS-friendly chunks"""
        if len(message) <= max_length:
            return [message]
        
        chunks = []
        
        # Try to split by sentences first
        sentences = re.split(r'(?<=[.!?])\s+', message)
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + " " + sentence) <= max_length:
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # If sentence itself is too long, split by words
                if len(sentence) > max_length:
                    words = sentence.split()
                    word_chunk = ""
                    for word in words:
                        if len(word_chunk + " " + word) <= max_length:
                            if word_chunk:
                                word_chunk += " " + word
                            else:
                                word_chunk = word
                        else:
                            if word_chunk:
                                chunks.append(word_chunk.strip())
                            word_chunk = word
                    current_chunk = word_chunk
                else:
                    current_chunk = sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def assess_message_intent(self, message: str) -> Dict[str, Any]:
        """Assess the intent and characteristics of a message"""
        message_lower = message.lower()
        
        # Question detection
        is_question = "?" in message or any(
            message_lower.startswith(q) for q in 
            ["what", "how", "when", "where", "why", "who", "can you", "do you", "is", "are"]
        )
        
        # Interest level detection
        high_interest_indicators = [
            "very interested", "excited", "ready", "when can", "how do i",
            "tell me more", "want to know", "looking forward", "sounds great"
        ]
        
        low_interest_indicators = [
            "maybe", "not sure", "thinking", "just looking", "just exploring",
            "might be", "possibly", "considering"
        ]
        
        interest_level = "medium"
        if any(indicator in message_lower for indicator in high_interest_indicators):
            interest_level = "high"
        elif any(indicator in message_lower for indicator in low_interest_indicators):
            interest_level = "low"
        
        # Urgency detection
        urgency_indicators = ["urgent", "asap", "quickly", "soon", "immediately", "right away"]
        is_urgent = any(indicator in message_lower for indicator in urgency_indicators)
        
        # Information request detection
        info_requests = ["tell me about", "information", "details", "explain", "describe"]
        wants_info = any(request in message_lower for request in info_requests)
        
        return {
            "is_question": is_question,
            "interest_level": interest_level,
            "is_urgent": is_urgent,
            "wants_info": wants_info,
            "message_length": len(message),
            "word_count": len(message.split())
        }
    
    def create_response_strategy(
        self, 
        message: str, 
        lead_context: Optional[Dict[str, Any]], 
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create a comprehensive response strategy"""
        
        intent = self.assess_message_intent(message)
        
        strategy = {
            "greeting_needed": len(conversation_history) == 0,
            "context_recall_needed": bool(lead_context and len(conversation_history) > 2),
            "acknowledgment_needed": not intent["is_question"],
            "clarification_needed": intent["is_question"] and intent["word_count"] < 5,
            "industry_response_available": bool(
                lead_context and 
                lead_context.get("preferred_industries") and 
                any(industry in message.lower() for industry in lead_context["preferred_industries"])
            ),
            "soft_ending_needed": not intent["is_urgent"],
            "enthusiasm_level": intent["interest_level"],
            "response_priority": "high" if intent["is_urgent"] else "normal"
        }
        
        return strategy


# Global conversation engine instance
_conversation_engine: Optional[NaturalConversationEngine] = None


def get_conversation_engine() -> NaturalConversationEngine:
    """Get or create conversation engine instance"""
    global _conversation_engine
    
    if _conversation_engine is None:
        _conversation_engine = NaturalConversationEngine()
    
    return _conversation_engine
