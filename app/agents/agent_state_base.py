"""
Agent State Base Classes
Core state management and base agent classes for the multi-agent system
"""

import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, TypedDict
from .types import AgentConfig, AgentResponse, AgentStatus
from langchain_core.messages import BaseMessage
from langchain_openai import Chat<PERSON>penAI
import structlog
from langchain_core.tools import BaseTool

from app.core.logging import logger


class AgentState(TypedDict):
    """
    Shared state structure for LangGraph workflow
    This state is passed between all agents in the workflow
    """
    # Core conversation data
    user_input: str
    messages: List[BaseMessage]
    session_id: str

    # Intent and routing
    intent: Optional[str]
    next_action: Optional[str]

    # Lead management
    lead_id: Optional[str]
    lead_data: Optional[Dict[str, Any]]
    lead_status: Optional[str]

    # Document processing
    document_id: Optional[str]
    document_content: Optional[str]
    chunks_processed: Optional[int]

    # Question answering
    question: Optional[str]
    answer: Optional[str]
    confidence_score: Optional[float]
    sources: Optional[List[str]]

    # Meeting booking
    meeting_data: Optional[Dict[str, Any]]
    availability: Optional[List[Dict[str, Any]]]

    # Response and metadata
    response: Optional[str]
    error: Optional[str]
    metadata: Optional[Dict[str, Any]]

    # Context and memory
    context: Optional[Dict[str, Any]]
    conversation_history: Optional[List[Dict[str, Any]]]


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the system
    Each agent is implemented as a LangGraph node function
    """

    def __init__(self, config: AgentConfig):
        self.config = config
        self.id = str(uuid.uuid4())
        self.status = AgentStatus.IDLE
        self.created_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        self.execution_count = 0
        self.error_count = 0

        # Initialize LLM with flexible config access
        model_name = config.get("model") if isinstance(config, dict) else getattr(config, "model", "gpt-4-turbo")
        temperature = config.get("temperature") if isinstance(config, dict) else getattr(config, "temperature", 0.1)
        max_tokens = config.get("max_tokens") if isinstance(config, dict) else getattr(config, "max_tokens", 1000)

        # Initialize LLM with error handling for missing API key
        try:
            self.llm = ChatOpenAI(
                model=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )
        except Exception as e:
            logger.warning(f"Failed to initialize OpenAI client for agent: {e}")
            self.llm = None

        # Initialize tools if specified
        self.tools: List[BaseTool] = []
        self._initialize_tools()

        config_role = config.get("role") if isinstance(config, dict) else config.role
        logger.info(f"Initialized {config_role} agent", agent_id=self.id)

    def _initialize_tools(self):
        """Initialize agent-specific tools"""
        pass

    @abstractmethod
    async def process_state(self, state: AgentState) -> AgentState:
        """
        Main processing method for the agent
        Takes current state and returns updated state
        """
        pass

    async def process(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        Bridge method to convert input_data to AgentState and process
        This enables the execute method to work with the agent system
        """
        try:
            # Convert input_data to AgentState
            state: AgentState = {
                "user_input": input_data.get("user_input", ""),
                "messages": input_data.get("messages", []),
                "session_id": input_data.get("session_id", str(uuid.uuid4())),
                "intent": input_data.get("intent"),
                "next_action": input_data.get("next_action"),
                "lead_id": input_data.get("lead_id"),
                "lead_data": input_data.get("lead_data"),
                "lead_status": input_data.get("lead_status"),
                "document_id": input_data.get("document_id"),
                "document_content": input_data.get("document_content"),
                "chunks_processed": input_data.get("chunks_processed"),
                "question": input_data.get("question"),
                "answer": input_data.get("answer"),
                "confidence_score": input_data.get("confidence_score"),
                "sources": input_data.get("sources"),
                "meeting_data": input_data.get("meeting_data"),
                "availability": input_data.get("availability"),
                "response": input_data.get("response"),
                "error": input_data.get("error"),
                "metadata": input_data.get("metadata"),
                "context": input_data.get("context"),
                "conversation_history": input_data.get("conversation_history")
            }

            # Process the state
            result_state = await self.process_state(state)

            # Convert back to AgentResponse
            return AgentResponse(
                success=not bool(result_state.get("error")),
                data={
                    "response": result_state.get("response"),
                    "chunks_processed": result_state.get("chunks_processed", 0),
                    "enhanced_features": result_state.get("enhanced_features", []),
                    "meeting_data": result_state.get("meeting_data"),
                    "availability": result_state.get("availability")
                },
                error=result_state.get("error"),
                agent_id=self.id,
                metadata=result_state.get("metadata", {})
            )

        except Exception as e:
            return AgentResponse(
                success=False,
                error=str(e),
                agent_id=self.id,
                data={}
            )

    async def execute(self, task: str, context: Optional[Dict[str, Any]] = None) -> AgentResponse:
        """
        Execute a task with proper error handling and logging
        """
        start_time = datetime.utcnow()
        self.status = AgentStatus.PROCESSING
        self.execution_count += 1

        try:
            logger.info("Executing task", agent_id=self.id, task=task)

            # Convert task to input_data format
            input_data = {
                "user_input": task,
                "context": context or {},
                "session_id": str(uuid.uuid4())
            }

            # Process using the bridge method
            response = await self.process(input_data)

            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Handle both AgentResponse objects and dictionaries
            if hasattr(response, 'execution_time'):
                response.execution_time = execution_time
            elif isinstance(response, dict):
                response['execution_time'] = execution_time
            else:
                # Convert to AgentResponse if it's neither
                response = AgentResponse(
                    success=getattr(response, 'success', True),
                    message=getattr(response, 'message', ''),
                    data=getattr(response, 'data', {}),
                    error=getattr(response, 'error', None),
                    agent_id=self.id,
                    timestamp=datetime.utcnow(),
                    processing_time=execution_time,
                    tokens_used=getattr(response, 'tokens_used', None)
                )

            self.status = AgentStatus.COMPLETED
            self.last_activity = datetime.utcnow()

            logger.info("Task completed successfully",
                        agent_id=self.id,
                        execution_time=execution_time)

            return response

        except Exception as e:
            self.error_count += 1
            self.status = AgentStatus.ERROR
            execution_time = (datetime.utcnow() - start_time).total_seconds()

            logger.error("Task execution failed",
                         agent_id=self.id,
                         error=str(e),
                         execution_time=execution_time)

            return AgentResponse(
                success=False,
                error=str(e),
                agent_id=self.id,
                execution_time=execution_time
            )

    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "id": self.id,
            "role": self.config.role,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "execution_count": self.execution_count,
            "error_count": self.error_count
        }
