"""
CORS Middleware Configuration
Handles Cross-Origin Resource Sharing (CORS) for the application
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config.settings import settings

def setup_cors(app: FastAPI) -> None:
    """Setup CORS middleware"""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_METHODS,
        allow_headers=[
            "*",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "X-Request-ID",
            "X-Remember-Token"
        ] if settings.CORS_HEADERS == ["*"] else settings.CORS_HEADERS,
        expose_headers=[
            "Content-Type",
            "Authorization",
            "X-Request-ID",
            "X-Remember-Token"
        ],
        max_age=3600,  # Cache preflight requests for 1 hour
    )