"""
Security Middleware Configuration
Handles security headers and CORS validation
"""

from fastapi import FastAP<PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config.settings import settings
from app.core.logging import setup_logging

# Setup logging
logger = setup_logging()

class SecurityMiddleware(BaseHTTPMiddleware):
    """Security middleware for handling security headers and CORS validation"""
    
    def __init__(
        self,
        app: FastAPI,
        cors_origin_regex: str = None
    ):
        super().__init__(app)
        self.cors_origin_regex = cors_origin_regex or r".*"  # Allow all origins by default
    
    async def dispatch(self, request: Request, call_next):
        """Process the request and add security headers"""
        try:
            response = await call_next(request)

            # Ensure we have a valid response
            if response is None:
                from fastapi import HTTPException
                from fastapi.responses import JSONResponse
                logger.error(f"No response returned for {request.method} {request.url}")
                return JSONResponse(
                    status_code=500,
                    content={"error": "Internal server error - no response generated"}
                )

            # Add security headers
            for header, value in settings.SECURITY_HEADERS.items():
                response.headers[header] = value

            return response

        except Exception as e:
            from fastapi.responses import J<PERSON><PERSON>esponse
            logger.error(f"Error in security middleware for {request.method} {request.url}: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"error": "Internal server error"}
            )

def setup_security_headers(app: FastAPI) -> None:
    """Configure security headers middleware"""
    app.add_middleware(SecurityMiddleware)

# Create middleware instance
security_middleware = SecurityMiddleware(app=None)  # Will be set by FastAPI 