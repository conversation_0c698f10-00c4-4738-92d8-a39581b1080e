"""
API Standards Middleware for GrowthHive
Implements consistent API standards across all endpoints
"""

from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import AS<PERSON>App

from app.core.api_standards import SecurityStandards
from app.core.logging import setup_logging
from app.core.api_standards import (
    DEFAULT_RESPONSE_HEADERS
)

# Setup logging
logger = setup_logging()

class APIStandardsMiddleware(BaseHTTPMiddleware):
    """
    API standards middleware for enforcing API standards
    """
    
    def __init__(self, app):
        """
        Initialize API standards middleware
        
        Args:
            app: FastAPI application
        """
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """
        Dispatch request to next middleware
        
        Args:
            request: FastAPI request
            call_next: Next middleware
            
        Returns:
            FastAPI response
        """
        # Add default headers
        response = await call_next(request)
        for header, value in DEFAULT_RESPONSE_HEADERS.items():
            response.headers[header] = value
        
        return response

# Create middleware instance
api_standards_middleware = APIStandardsMiddleware(app=None)  # Will be set by FastAPI

class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Middleware for request validation and sanitization"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.security_standards = SecurityStandards()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Validate and sanitize incoming requests"""
        
        # Check content type for POST/PUT requests
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if content_type and not content_type.startswith(("application/json", "multipart/form-data")):
                return JSONResponse(
                    content={
                        "success": False,
                        "message": {
                            "title": "Invalid Content Type",
                            "description": "Content-Type must be application/json or multipart/form-data"
                        },
                        "data": None,
                        "error_code": 4000
                    },
                    status_code=400
                )
        
        # Check request size (10MB limit)
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
            return JSONResponse(
                content={
                    "success": False,
                    "message": {
                        "title": "Request Too Large",
                        "description": "Request size exceeds the maximum allowed limit of 10MB"
                    },
                    "data": {"details": None},
                    "error_code": 4130
                },
                status_code=413
            )
        
        # Validate User-Agent header
        user_agent = request.headers.get("user-agent", "")
        if not user_agent or len(user_agent) > 500:
            return JSONResponse(
                content={
                    "success": False,
                    "message": {
                        "title": "Invalid User Agent",
                        "description": "Valid User-Agent header is required"
                    },
                    "data": {"details": None},
                    "error_code": 4000
                },
                status_code=400
            )
        
        return await call_next(request)

class CORSMiddleware(BaseHTTPMiddleware):
    """Enhanced CORS middleware with security considerations"""
    
    def __init__(self, app: ASGIApp, allowed_origins: list = None):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["http://localhost:3000", "http://localhost:8080"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Handle CORS with security considerations"""
        
        origin = request.headers.get("origin")
        
        # Handle preflight requests
        if request.method == "OPTIONS":
            if origin in self.allowed_origins:
                return Response(
                    headers={
                        "Access-Control-Allow-Origin": origin,
                        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
                        "Access-Control-Allow-Credentials": "true",
                        "Access-Control-Max-Age": "86400"  # 24 hours
                    }
                )
            else:
                return Response(status_code=403)
        
        response = await call_next(request)
        
        # Add CORS headers to actual requests
        if origin in self.allowed_origins:
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
        
        return response
