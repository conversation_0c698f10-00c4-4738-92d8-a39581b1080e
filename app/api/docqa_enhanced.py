"""
Enhanced DocQA API Endpoints with Parallel Processing Support
Integrates the new high-performance DocQA system with FastAPI
"""

from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from pydantic import BaseModel, Field
import structlog

from app.core.security.enhanced_auth_middleware import get_current_user
from docqa.central_api import ask_question
from docqa.tasks import ingest_document_parallel, batch_ingest_documents
from docqa.monitoring.status_tracker import get_status_tracker
from docqa.redis_store import get_redis_store

from app.core.logging import logger

router = APIRouter(prefix="/api/docqa", tags=["Enhanced DocQA"])


# Request/Response Models
class QuestionRequest(BaseModel):
    """Enhanced question request with processing options"""
    question: str = Field(..., description="Question to ask")
    source_url: Optional[str] = Field(None, description="Document URL to process")
    document_id: Optional[str] = Field(None, description="Specific document ID")
    franchisor_id: Optional[str] = Field(None, description="Specific franchisor ID")
    top_k: Optional[int] = Field(5, description="Number of results to return")
    similarity_threshold: Optional[float] = Field(0.7, description="Similarity threshold")
    force_refresh: Optional[bool] = Field(False, description="Force refresh cache")
    processing_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Processing options")


class QuestionResponse(BaseModel):
    """Enhanced question response with metadata"""
    success: bool
    answer: str
    cached: bool = False
    processing_status: Optional[str] = None
    job_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    timestamp: str
    error: Optional[str] = None


class BatchIngestRequest(BaseModel):
    """Batch document ingestion request"""
    document_urls: List[str] = Field(..., description="List of document URLs")
    batch_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Batch processing options")


class JobStatusResponse(BaseModel):
    """Job status response"""
    job_id: str
    url: Optional[str] = None
    status: str
    progress: int = 0
    progress_message: Optional[str] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    processing_time: Optional[float] = None
    chunks_created: int = 0
    tokens_processed: int = 0
    error_message: Optional[str] = None


class SystemStatusResponse(BaseModel):
    """System status response"""
    system_metrics: Dict[str, Any]
    cache_stats: Dict[str, Any]
    database_stats: Dict[str, Any]
    monitoring_active: bool
    uptime: float
    active_jobs: int


# API Endpoints

@router.post("/ask", response_model=QuestionResponse)
async def ask_enhanced_question(
    request: QuestionRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Ask a question with enhanced processing capabilities
    
    Features:
    - Automatic document processing if source_url provided
    - Redis caching for fast repeated queries
    - Real-time status tracking for long-running jobs
    - Priority-based processing
    """
    try:
        logger.info("Enhanced question request", 
                   question=request.question[:100], 
                   user_id=current_user.get("id"))
        
        # Convert request to dict for central API
        request_dict = {
            "question": request.question,
            "source_url": request.source_url,
            "document_id": request.document_id,
            "franchisor_id": request.franchisor_id,
            "top_k": request.top_k,
            "similarity_threshold": request.similarity_threshold,
            "force_refresh": request.force_refresh,
            "processing_options": {
                **request.processing_options,
                "user_id": current_user.get("id"),
                "priority": request.processing_options.get("priority", 5)
            }
        }
        
        # Call enhanced ask_question
        result = await ask_question(request_dict)
        
        return QuestionResponse(**result)
        
    except Exception as e:
        logger.error(f"Enhanced question failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ingest")
async def trigger_document_ingestion(
    source_url: str,
    background_tasks: BackgroundTasks,
    force_table: Optional[str] = None,
    translate: bool = True,
    extract_charts: bool = True,
    priority: int = 5,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Trigger background document ingestion
    
    Returns immediately with job ID for status tracking
    """
    try:
        logger.info("Document ingestion triggered", 
                   source_url=source_url, 
                   user_id=current_user.get("id"))
        
        # Trigger Celery task
        task_result = ingest_document_parallel.delay(
            source_url=source_url,
            force_table=force_table,
            translate=translate,
            extract_charts=extract_charts,
            priority=priority,
            processing_options={
                "user_id": current_user.get("id"),
                "triggered_via": "api"
            }
        )
        
        return {
            "success": True,
            "message": "Document ingestion started",
            "job_id": task_result.id,
            "source_url": source_url,
            "status": "initiated"
        }
        
    except Exception as e:
        logger.error(f"Document ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-ingest")
async def trigger_batch_ingestion(
    request: BatchIngestRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Trigger batch document ingestion
    
    Process multiple documents concurrently
    """
    try:
        logger.info("Batch ingestion triggered", 
                   document_count=len(request.document_urls),
                   user_id=current_user.get("id"))
        
        # Add user context to batch options
        batch_options = {
            **request.batch_options,
            "user_id": current_user.get("id"),
            "triggered_via": "api"
        }
        
        # Trigger batch Celery task
        task_result = batch_ingest_documents.delay(
            document_urls=request.document_urls,
            batch_options=batch_options
        )
        
        return {
            "success": True,
            "message": "Batch ingestion started",
            "job_id": task_result.id,
            "document_count": len(request.document_urls),
            "status": "initiated"
        }
        
    except Exception as e:
        logger.error(f"Batch ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}/status", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get detailed job status and progress"""
    try:
        tracker = await get_status_tracker()
        status = await tracker.get_job_status(job_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobStatusResponse(**status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get job status failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get comprehensive system status and metrics"""
    try:
        tracker = await get_status_tracker()
        status = await tracker.get_system_status()
        
        return SystemStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Get system status failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs")
async def get_job_history(
    limit: int = Query(100, description="Maximum number of jobs to return"),
    status_filter: Optional[str] = Query(None, description="Filter by job status"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get job history with optional filtering"""
    try:
        tracker = await get_status_tracker()
        jobs = await tracker.get_job_history(
            limit=limit,
            status_filter=status_filter
        )
        
        return {
            "success": True,
            "jobs": jobs,
            "total_returned": len(jobs)
        }
        
    except Exception as e:
        logger.error(f"Get job history failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cache/stats")
async def get_cache_statistics(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get Redis cache statistics"""
    try:
        redis_store = await get_redis_store()
        stats = await redis_store.get_cache_stats()
        
        return {
            "success": True,
            "cache_stats": stats
        }
        
    except Exception as e:
        logger.error(f"Get cache stats failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cache")
async def clear_cache(
    pattern: Optional[str] = Query("*", description="Cache key pattern to clear"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Clear cache entries matching pattern"""
    try:
        redis_store = await get_redis_store()
        cleared_count = await redis_store.clear_cache_pattern(pattern)
        
        logger.info("Cache cleared", pattern=pattern, count=cleared_count)
        
        return {
            "success": True,
            "message": f"Cleared {cleared_count} cache entries",
            "pattern": pattern,
            "cleared_count": cleared_count
        }
        
    except Exception as e:
        logger.error(f"Clear cache failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_performance_metrics(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get detailed performance metrics"""
    try:
        # Get metrics from various components
        tracker = await get_status_tracker()
        system_status = await tracker.get_system_status()
        
        # Get bulk operations metrics
        from docqa.vector_store.enhanced_bulk_operations import get_bulk_operations
        bulk_ops = await get_bulk_operations()
        bulk_stats = await bulk_ops.get_performance_stats()
        
        # Get parallel processor metrics
        from docqa.parallel_ingest import get_parallel_processor
        processor = await get_parallel_processor()
        processor_stats = processor.get_processing_stats()
        
        return {
            "success": True,
            "metrics": {
                "system": system_status["system_metrics"],
                "cache": system_status["cache_stats"],
                "database": system_status["database_stats"],
                "bulk_operations": bulk_stats,
                "parallel_processing": processor_stats,
                "uptime": system_status["uptime"],
                "active_jobs": system_status["active_jobs"]
            }
        }
        
    except Exception as e:
        logger.error(f"Get metrics failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
