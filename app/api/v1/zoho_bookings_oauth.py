"""
Zoho Bookings OAuth Test Endpoints
Test OAuth flow and get refresh token for Zoho Bookings
"""

import aiohttp
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional

from app.core.config.settings import settings
from app.core.logging import logger


router = APIRouter(prefix="/zoho-bookings-oauth", tags=["Zoho Bookings OAuth"])


class OAuthTestResponse(BaseModel):
    """OAuth test response"""
    success: bool
    message: str
    client_id: Optional[str] = None
    auth_url: Optional[str] = None
    error: Optional[str] = None


class TokenExchangeRequest(BaseModel):
    """Token exchange request"""
    authorization_code: str


class TokenResponse(BaseModel):
    """Token response"""
    success: bool
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    error: Optional[str] = None


@router.get("/test-config", response_model=OAuthTestResponse)
async def test_oauth_config():
    """
    Test OAuth configuration and generate authorization URL
    """
    try:
        # Check if we have the required OAuth credentials
        client_id = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_ID', '') or settings.ZOHO_CLIENT_ID
        client_secret = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_SECRET', '') or settings.ZOHO_CLIENT_SECRET

        if not client_id or not client_secret:
            return OAuthTestResponse(
                success=False,
                message="Missing Zoho OAuth credentials. Please set ZOHO_CLIENT_ID and ZOHO_CLIENT_SECRET in .env file.",
                error="Missing credentials"
            )

        # Generate authorization URL for Zoho Bookings
        redirect_uri = "http://localhost:3000/oauth/callback"
        scope = "zohobookings.data.CREATE"

        auth_url = (
            f"https://accounts.zoho.com.au/oauth/v2/auth?"
            f"scope={scope}&"
            f"client_id={client_id}&"
            f"response_type=code&"
            f"redirect_uri={redirect_uri}&"
            f"access_type=offline"
        )

        return OAuthTestResponse(
            success=True,
            message="OAuth configuration is valid. Use the auth_url to get authorization code.",
            client_id=client_id,
            auth_url=auth_url
        )

    except Exception as e:
        logger.error(f"Error testing OAuth config: {e}")
        return OAuthTestResponse(
            success=False,
            message=f"Error testing OAuth configuration: {str(e)}",
            error=str(e)
        )


@router.get("/test-scopes")
async def test_different_scopes():
    """
    Test different scope combinations for Zoho Bookings
    """
    try:
        client_id = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_ID', '') or settings.ZOHO_CLIENT_ID
        redirect_uri = "http://localhost:3000/oauth/callback"

        # Different scope options to try
        scopes_to_test = [
            "zohobookings.data.CREATE",
            "zohobookings.data.READ",
            "zohobookings.data.ALL",
            "ZohoBookings.data.CREATE",
            "ZohoBookings.data.READ",
            "ZohoBookings.data.ALL",
            "ZohoBookings.booking.CREATE",
            "ZohoBookings.booking.READ",
            "ZohoBookings.booking.ALL"
        ]

        auth_urls = {}
        for scope in scopes_to_test:
            auth_url = (
                f"https://accounts.zoho.com.au/oauth/v2/auth?"
                f"scope={scope}&"
                f"client_id={client_id}&"
                f"response_type=code&"
                f"redirect_uri={redirect_uri}&"
                f"access_type=offline"
            )
            auth_urls[scope] = auth_url

        return {
            "success": True,
            "message": "Multiple scope options generated. Try each one to see which works.",
            "auth_urls": auth_urls,
            "instructions": [
                "Try each URL in your browser",
                "If one works, use that scope for your integration",
                "The working scope will not show an error page"
            ]
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@router.post("/exchange-token", response_model=TokenResponse)
async def exchange_authorization_code(request: TokenExchangeRequest):
    """
    Exchange authorization code for access and refresh tokens
    """
    try:
        client_id = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_ID', '') or settings.ZOHO_CLIENT_ID
        client_secret = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_SECRET', '') or settings.ZOHO_CLIENT_SECRET
        redirect_uri = "http://localhost:3000/oauth/callback"
        
        if not client_id or not client_secret:
            raise HTTPException(
                status_code=400,
                detail="Missing Zoho OAuth credentials"
            )
        
        # Prepare token exchange request
        token_url = "https://accounts.zoho.com.au/oauth/v2/token"
        token_data = {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'client_secret': client_secret,
            'redirect_uri': redirect_uri,
            'code': request.authorization_code
        }
        
        # Make token exchange request
        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, data=token_data) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    logger.info("✅ Successfully exchanged authorization code for tokens")
                    return TokenResponse(
                        success=True,
                        access_token=response_data.get('access_token'),
                        refresh_token=response_data.get('refresh_token'),
                        expires_in=response_data.get('expires_in')
                    )
                else:
                    error_msg = response_data.get('error_description', response_data.get('error', 'Unknown error'))
                    logger.error(f"❌ Token exchange failed: {error_msg}")
                    return TokenResponse(
                        success=False,
                        error=error_msg
                    )
                    
    except Exception as e:
        logger.error(f"Error exchanging authorization code: {e}")
        return TokenResponse(
            success=False,
            error=str(e)
        )


@router.get("/test-api-access")
async def test_api_access():
    """
    Test API access with current refresh token
    """
    try:
        refresh_token = getattr(settings, 'ZOHO_BOOKINGS_REFRESH_TOKEN', '')
        
        if not refresh_token or refresh_token == 'YOUR_ZOHO_BOOKINGS_REFRESH_TOKEN_HERE':
            return {
                "success": False,
                "message": "No valid refresh token found. Please complete OAuth flow first.",
                "error": "Missing refresh token"
            }
        
        # Try to get access token using refresh token
        client_id = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_ID', '') or settings.ZOHO_CLIENT_ID
        client_secret = getattr(settings, 'ZOHO_BOOKINGS_CLIENT_SECRET', '') or settings.ZOHO_CLIENT_SECRET
        
        token_url = "https://accounts.zoho.com.au/oauth/v2/token"
        token_data = {
            'refresh_token': refresh_token,
            'client_id': client_id,
            'client_secret': client_secret,
            'grant_type': 'refresh_token'
        }
        
        async with aiohttp.ClientSession() as session:
            # Get access token
            async with session.post(token_url, data=token_data) as token_response:
                if token_response.status != 200:
                    error_text = await token_response.text()
                    return {
                        "success": False,
                        "message": f"Failed to refresh token: {error_text}",
                        "error": "Token refresh failed"
                    }
                
                token_info = await token_response.json()
                access_token = token_info['access_token']
            
            # Test API call with access token
            headers = {
                'Authorization': f'Zoho-oauthtoken {access_token}',
                'Content-Type': 'application/json'
            }
            
            # Try to get workspaces (basic API test)
            api_url = "https://bookings.zoho.com/api/v1/workspaces"
            async with session.get(api_url, headers=headers) as api_response:
                api_text = await api_response.text()
                
                if api_response.status == 200:
                    api_data = await api_response.json()
                    return {
                        "success": True,
                        "message": "✅ API access successful!",
                        "api_response": api_data,
                        "status_code": api_response.status
                    }
                else:
                    return {
                        "success": False,
                        "message": f"❌ API access failed: {api_text}",
                        "status_code": api_response.status,
                        "error": api_text
                    }
                    
    except Exception as e:
        logger.error(f"Error testing API access: {e}")
        return {
            "success": False,
            "message": f"Error testing API access: {str(e)}",
            "error": str(e)
        }


@router.get("/generate-oauth-script")
async def generate_oauth_script():
    """
    Generate a complete OAuth setup script with current credentials
    """
    try:
        client_id = settings.ZOHO_CLIENT_ID
        client_secret = settings.ZOHO_CLIENT_SECRET
        
        if not client_id or not client_secret:
            return {
                "success": False,
                "message": "Missing OAuth credentials in settings"
            }
        
        script_content = f'''#!/usr/bin/env python3
"""
Zoho Bookings OAuth Setup Script
Auto-generated with your current credentials
"""

import webbrowser
import urllib.parse
import requests

# Your Zoho API Configuration
CLIENT_ID = "{client_id}"
CLIENT_SECRET = "{client_secret}"
REDIRECT_URI = "http://localhost:3000/oauth/callback"
SCOPE = "zohobookings.data.CREATE"

def main():
    print("🎯 ZOHO BOOKINGS OAUTH SETUP")
    print("=" * 50)
    
    # Step 1: Generate authorization URL
    auth_params = {{
        'scope': SCOPE,
        'client_id': CLIENT_ID,
        'response_type': 'code',
        'redirect_uri': REDIRECT_URI,
        'access_type': 'offline'
    }}
    
    auth_url = f"https://accounts.zoho.com/oauth/v2/auth?{{urllib.parse.urlencode(auth_params)}}"
    
    print(f"📋 Your Configuration:")
    print(f"   Client ID: {{CLIENT_ID}}")
    print(f"   Redirect URI: {{REDIRECT_URI}}")
    print(f"   Scope: {{SCOPE}}")
    print()
    
    print("🔗 Authorization URL:")
    print(auth_url)
    print()
    
    print("📋 INSTRUCTIONS:")
    print("1. Copy the URL above and open it in your browser")
    print("2. Authorize the application")
    print("3. Copy the authorization code from the redirect URL")
    print("4. Use the code with the /exchange-token endpoint")
    print()
    
    try:
        webbrowser.open(auth_url)
        print("✅ Browser opened automatically!")
    except:
        print("❌ Could not open browser automatically")
    
    print("\\n🔄 Next step:")
    print("Use the authorization code with:")
    print("POST /zoho-bookings-oauth/exchange-token")
    print('{{"authorization_code": "YOUR_CODE_HERE"}}')

if __name__ == "__main__":
    main()
'''
        
        return {
            "success": True,
            "message": "OAuth script generated successfully",
            "script_content": script_content,
            "instructions": [
                "1. Save the script content to a .py file",
                "2. Run the script to get authorization URL",
                "3. Complete OAuth flow in browser",
                "4. Use authorization code with /exchange-token endpoint"
            ]
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"Error generating script: {str(e)}"
        }
