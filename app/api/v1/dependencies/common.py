"""
Common dependencies for API v1
"""

from typing import Optional
from fastapi import Query
from pydantic import BaseModel, Field

from app.core.config.settings import settings


class PaginationParams(BaseModel):
    """Standard pagination parameters"""
    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Maximum number of records to return")


class SearchParams(BaseModel):
    """Standard search parameters"""
    search: Optional[str] = Field(default=None, description="Search query")
    sort_by: Optional[str] = Field(default=None, description="Field to sort by")
    sort_order: Optional[str] = Field(default="asc", regex="^(asc|desc)$", description="Sort order")


class FilterParams(BaseModel):
    """Standard filter parameters"""
    status: Optional[str] = Field(default=None, description="Filter by status")
    created_after: Optional[str] = Field(default=None, description="Filter by creation date (after)")
    created_before: Optional[str] = Field(default=None, description="Filter by creation date (before)")


def get_pagination_params(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return")
) -> PaginationParams:
    """
    Get pagination parameters from query string
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        PaginationParams: Pagination parameters
    """
    return PaginationParams(skip=skip, limit=limit)


def get_search_params(
    search: Optional[str] = Query(None, description="Search query"),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", regex="^(asc|desc)$", description="Sort order")
) -> SearchParams:
    """
    Get search parameters from query string
    
    Args:
        search: Search query
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        
    Returns:
        SearchParams: Search parameters
    """
    return SearchParams(search=search, sort_by=sort_by, sort_order=sort_order)


def get_filter_params(
    status: Optional[str] = Query(None, description="Filter by status"),
    created_after: Optional[str] = Query(None, description="Filter by creation date (after)"),
    created_before: Optional[str] = Query(None, description="Filter by creation date (before)")
) -> FilterParams:
    """
    Get filter parameters from query string
    
    Args:
        status: Filter by status
        created_after: Filter by creation date (after)
        created_before: Filter by creation date (before)
        
    Returns:
        FilterParams: Filter parameters
    """
    return FilterParams(
        status=status,
        created_after=created_after,
        created_before=created_before
    )


# Common dependencies
def get_app_settings():
    """Get application settings"""
    return settings


# Re-export for convenience
__all__ = [
    "PaginationParams",
    "SearchParams", 
    "FilterParams",
    "get_pagination_params",
    "get_search_params",
    "get_filter_params",
    "get_app_settings"
]
