"""
Booking API Endpoints
FastAPI endpoints for Zoho Bookings integration
"""

from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from app.core.database.connection import get_db
from app.core.logging import logger
from app.models.booking import Booking, BookingHistory
from app.models.lead import Lead
from app.schemas.booking import (
    BookingCreate, BookingResponse, BookingDetail, BookingList,
    AvailabilityRequest, AvailabilityResponse, BookingUpdate,
    BookingStats, StaffAvailabilitySummary, QuickBookingRequest,
    BookingRescheduleRequest, BookingCancelRequest, BookingConfirmation
)
from app.services.zoho_bookings_service import ZohoBookingsService, get_zoho_bookings_service
from app.core.security.auth import get_current_user

router = APIRouter()


@router.post("/availability", response_model=AvailabilityResponse)
async def check_availability(
    request: AvailabilityRequest,
    bookings_service: ZohoBookingsService = Depends(get_zoho_bookings_service),
    db: AsyncSession = Depends(get_db)
):
    """Check availability for booking appointments"""
    try:
        logger.info(f"Checking availability for {request.service_type} from {request.date_from} to {request.date_to}")
        
        # Get available slots from Zoho Bookings
        slots = await bookings_service.get_available_slots(
            date_from=request.date_from,
            date_to=request.date_to,
            service_type=request.service_type,
            preferred_staff=request.preferred_staff
        )
        
        # Convert to response format
        availability_slots = []
        for slot in slots:
            availability_slots.append({
                "staff_id": slot.staff_id,
                "staff_name": slot.staff_name,
                "start_time": slot.start_time,
                "end_time": slot.end_time,
                "service_id": slot.service_id,
                "service_name": slot.service_name,
                "duration_minutes": slot.duration_minutes,
                "booking_url": slot.booking_url
            })
        
        return AvailabilityResponse(
            success=True,
            slots=availability_slots,
            total_slots=len(availability_slots),
            message=f"Found {len(availability_slots)} available slots"
        )
        
    except Exception as e:
        logger.error(f"Error checking availability: {str(e)}")
        return AvailabilityResponse(
            success=False,
            slots=[],
            total_slots=0,
            message=f"Error checking availability: {str(e)}"
        )


@router.post("/book", response_model=BookingResponse)
async def create_booking(
    request: BookingCreate,
    bookings_service: ZohoBookingsService = Depends(get_zoho_bookings_service),
    db: AsyncSession = Depends(get_db)
):
    """Create a new booking appointment"""
    try:
        logger.info(f"Creating booking for {request.customer.name} at {request.preferred_start_time}")
        
        # Find the best available slot
        slot = await bookings_service.find_closest_available_slot(
            preferred_start_time=request.preferred_start_time,
            service_type=request.service_type,
            timezone=request.timezone
        )
        
        if not slot:
            return BookingResponse(
                success=False,
                error_message="No available slots found for the requested time"
            )
        
        # Create booking in Zoho
        booking_result = await bookings_service.book_appointment(
            slot=slot,
            customer_name=request.customer.name,
            customer_email=request.customer.email,
            customer_phone=request.customer.phone,
            notes=request.notes,
            lead_id=str(request.lead_id) if request.lead_id else None,
            timezone=request.timezone  # Add the missing timezone parameter
        )
        
        if not booking_result.success:
            return BookingResponse(
                success=False,
                error_message=booking_result.error_message
            )
        
        # Save booking to database
        booking = Booking(
            zoho_booking_id=booking_result.booking_id,
            zoho_service_id=slot.service_id,
            zoho_staff_id=slot.staff_id,
            service_type=request.service_type,
            staff_name=slot.staff_name,
            customer_name=request.customer.name,
            customer_email=request.customer.email,
            customer_phone=request.customer.phone,
            start_time=slot.start_time,
            end_time=slot.end_time,
            duration_minutes=slot.duration_minutes,
            timezone=request.timezone,
            status="scheduled",
            booking_source="api",
            booking_url=booking_result.booking_url,
            meeting_link=booking_result.meeting_link,
            notes=request.notes,
            lead_id=request.lead_id
        )
        
        db.add(booking)
        await db.commit()
        await db.refresh(booking)
        
        # Create booking history entry
        history = BookingHistory(
            booking_id=booking.id,
            action="created",
            new_values={
                "customer_name": request.customer.name,
                "start_time": slot.start_time.isoformat(),
                "service_type": request.service_type,
                "staff_name": slot.staff_name
            },
            changed_by="api",
            change_reason="Booking created via API"
        )
        db.add(history)
        await db.commit()
        
        logger.info(f"Booking created successfully: {booking.id}")
        
        return BookingResponse(
            success=True,
            booking_id=str(booking.id),
            zoho_booking_id=booking_result.booking_id,
            booking_url=booking_result.booking_url,
            meeting_link=booking_result.meeting_link,
            message="Booking created successfully"
        )
        
    except Exception as e:
        logger.error(f"Error creating booking: {str(e)}")
        await db.rollback()
        return BookingResponse(
            success=False,
            error_message=f"Error creating booking: {str(e)}"
        )


@router.post("/quick-book", response_model=BookingResponse)
async def quick_book(
    request: QuickBookingRequest,
    bookings_service: ZohoBookingsService = Depends(get_zoho_bookings_service),
    db: AsyncSession = Depends(get_db)
):
    """Quick booking for AI agents - finds next available slot"""
    try:
        logger.info(f"Quick booking for {request.customer_name}")
        
        # Get next available slot
        slot = await bookings_service.get_next_available_slot(
            service_type=request.service_type
        )
        
        if not slot:
            return BookingResponse(
                success=False,
                error_message="No available slots found in the next 7 days"
            )
        
        # Create booking
        booking_result = await bookings_service.book_appointment(
            slot=slot,
            customer_name=request.customer_name,
            customer_email=request.customer_email or f"{request.customer_phone}@temp.com",
            customer_phone=request.customer_phone,
            notes=request.notes,
            timezone="Australia/Sydney"  # Add default timezone for quick bookings
        )
        
        if not booking_result.success:
            return BookingResponse(
                success=False,
                error_message=booking_result.error_message
            )
        
        # Save to database
        booking = Booking(
            zoho_booking_id=booking_result.booking_id,
            zoho_service_id=slot.service_id,
            zoho_staff_id=slot.staff_id,
            service_type=request.service_type,
            staff_name=slot.staff_name,
            customer_name=request.customer_name,
            customer_email=request.customer_email,
            customer_phone=request.customer_phone,
            start_time=slot.start_time,
            end_time=slot.end_time,
            duration_minutes=slot.duration_minutes,
            status="scheduled",
            booking_source="sms_ai",
            booking_url=booking_result.booking_url,
            meeting_link=booking_result.meeting_link,
            notes=request.notes
        )
        
        db.add(booking)
        await db.commit()
        await db.refresh(booking)
        
        return BookingResponse(
            success=True,
            booking_id=str(booking.id),
            zoho_booking_id=booking_result.booking_id,
            booking_url=booking_result.booking_url,
            meeting_link=booking_result.meeting_link,
            message=f"Booking confirmed for {slot.start_time.strftime('%B %d at %I:%M %p')} with {slot.staff_name}"
        )
        
    except Exception as e:
        logger.error(f"Error in quick booking: {str(e)}")
        await db.rollback()
        return BookingResponse(
            success=False,
            error_message=f"Error creating booking: {str(e)}"
        )


@router.get("/", response_model=BookingList)
async def get_bookings(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    staff_name: Optional[str] = Query(None),
    customer_name: Optional[str] = Query(None),
    date_from: Optional[datetime] = Query(None),
    date_to: Optional[datetime] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Get list of bookings with filtering and pagination"""
    try:
        # Build query
        query = select(Booking)
        
        # Apply filters
        filters = []
        if status:
            filters.append(Booking.status == status)
        if staff_name:
            filters.append(Booking.staff_name.ilike(f"%{staff_name}%"))
        if customer_name:
            filters.append(Booking.customer_name.ilike(f"%{customer_name}%"))
        if date_from:
            filters.append(Booking.start_time >= date_from)
        if date_to:
            filters.append(Booking.start_time <= date_to)
        
        if filters:
            query = query.where(and_(*filters))
        
        # Get total count
        count_query = select(func.count(Booking.id)).where(and_(*filters)) if filters else select(func.count(Booking.id))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page).order_by(Booking.created_at.desc())
        
        result = await db.execute(query)
        bookings = result.scalars().all()
        
        # Convert to response format
        booking_details = []
        for booking in bookings:
            booking_details.append(BookingDetail.from_orm(booking))
        
        return BookingList(
            bookings=booking_details,
            total=total,
            page=page,
            per_page=per_page,
            has_next=(page * per_page) < total,
            has_prev=page > 1
        )
        
    except Exception as e:
        logger.error(f"Error getting bookings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving bookings: {str(e)}"
        )
