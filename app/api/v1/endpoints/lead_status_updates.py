"""
Lead Status Update API Endpoints
Handles automatic status updates from message analysis and manual status changes.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, Depends, Path, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.logging import logger
from app.core.exceptions import NotFoundError, ValidationError
from app.schemas.lead_status import (
    StatusFromMessageRequest,
    ManualStatusUpdateRequest,
    StatusUpdateSuccessResponse,
    StatusHistorySuccessResponse,
    AvailableStatusesSuccessResponse,
    ValidationTestRequest,
    ValidationTestSuccessResponse,
    StatusUpdateResult,
    StatusHistoryResponse,
    AvailableStatusesResponse,
    ValidationTestResult,
    LeadStatusInfo
)
from app.schemas.base_response import MessageResponse
from app.services.lead_status_update_service import LeadStatusUpdateService
from app.services.lead_status_classification_service import StatusContext
from app.services.lead_status_normalization_service import status_normalizer
from app.models.lead_reference import LeadStatus

router = APIRouter()


@router.post(
    "/{lead_id}/status-from-message",
    response_model=StatusUpdateSuccessResponse,
    summary="Update Lead Status from Message",
    description="Analyze a message and automatically update the lead's status based on content analysis"
)
async def update_status_from_message(
    lead_id: str = Path(..., description="Lead ID", example="550e8400-e29b-41d4-a716-446655440000"),
    request: StatusFromMessageRequest = ...,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Analyze a lead's message and automatically update their status.
    
    This endpoint:
    - Uses rule-based pattern matching for high-confidence classifications
    - Falls back to AI analysis for complex cases
    - Updates the lead status atomically with full audit trail
    - Returns detailed information about the classification and update
    
    **Authentication Required**: Yes
    """
    try:
        # Initialize the update service
        update_service = LeadStatusUpdateService(db)
        
        # Build context from request
        context = None
        if request.context:
            context = StatusContext(
                current_status=request.context.get("current_status"),
                last_outbound_at=request.context.get("last_outbound_at"),
                last_inbound_at=request.context.get("last_inbound_at"),
                territory_available=request.context.get("territory_available", True),
                min_budget=request.context.get("min_budget"),
                call_attempts=request.context.get("call_attempts", 0)
            )
        
        # Perform the status update
        result = await update_service.update_status_from_message(
            lead_id=lead_id,
            message_text=request.message,
            context=context,
            changed_by=current_user.get("email", "system"),
            source="andy"
        )
        
        # Convert to response format
        status_result = StatusUpdateResult(
            old_status=result.old_status,
            new_status=result.new_status,
            changed=result.changed,
            confidence=result.confidence,
            rationale=result.rationale,
            lead_id=result.lead_id,
            history_id=result.history_id,
            method="automated"
        )
        
        logger.info(
            f"Status update from message completed for lead {lead_id}",
            extra={
                "lead_id": lead_id,
                "changed": result.changed,
                "new_status": result.new_status,
                "confidence": result.confidence,
                "user_id": current_user.get("id")
            }
        )
        
        return StatusUpdateSuccessResponse(
            success=True,
            message=MessageResponse(
                title="Status Update Completed",
                description=f"Lead status {'updated' if result.changed else 'analyzed'} successfully"
            ),
            data=status_result,
            error_code=0
        )
        
    except NotFoundError as e:
        logger.warning(f"Lead not found: {lead_id}")
        raise HTTPException(status_code=404, detail=str(e))
    
    except ValidationError as e:
        logger.warning(f"Validation error for lead {lead_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error updating status for lead {lead_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while updating the lead status"
        )


@router.post(
    "/{lead_id}/manual-status-update",
    response_model=StatusUpdateSuccessResponse,
    summary="Manual Lead Status Update",
    description="Manually update a lead's status with a reason"
)
async def manual_status_update(
    lead_id: str = Path(..., description="Lead ID", example="550e8400-e29b-41d4-a716-446655440000"),
    request: ManualStatusUpdateRequest = ...,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Manually update a lead's status.
    
    This endpoint:
    - Validates the new status name against canonical statuses
    - Updates the lead status with full audit trail
    - Records the reason and who made the change
    
    **Authentication Required**: Yes
    """
    try:
        # Initialize the update service
        update_service = LeadStatusUpdateService(db)
        
        # Perform the manual update
        result = await update_service.manual_status_update(
            lead_id=lead_id,
            new_status_name=request.status_name,
            reason=request.reason,
            changed_by=current_user.get("email", "manual")
        )
        
        # Convert to response format
        status_result = StatusUpdateResult(
            old_status=result.old_status,
            new_status=result.new_status,
            changed=result.changed,
            confidence=result.confidence,
            rationale=result.rationale,
            lead_id=result.lead_id,
            history_id=result.history_id,
            method="manual"
        )
        
        logger.info(
            f"Manual status update completed for lead {lead_id}",
            extra={
                "lead_id": lead_id,
                "changed": result.changed,
                "new_status": result.new_status,
                "changed_by": current_user.get("email"),
                "reason": request.reason
            }
        )
        
        return StatusUpdateSuccessResponse(
            success=True,
            message=MessageResponse(
                title="Manual Status Update Completed",
                description=f"Lead status {'updated' if result.changed else 'confirmed'} successfully"
            ),
            data=status_result,
            error_code=0
        )
        
    except NotFoundError as e:
        logger.warning(f"Lead not found: {lead_id}")
        raise HTTPException(status_code=404, detail=str(e))
    
    except ValidationError as e:
        logger.warning(f"Validation error for manual update of lead {lead_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in manual status update for lead {lead_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while updating the lead status"
        )


@router.get(
    "/{lead_id}/status-history",
    response_model=StatusHistorySuccessResponse,
    summary="Get Lead Status History",
    description="Retrieve the status change history for a lead"
)
async def get_status_history(
    lead_id: str = Path(..., description="Lead ID", example="550e8400-e29b-41d4-a716-446655440000"),
    limit: int = Query(10, description="Maximum number of history records to return", ge=1, le=100),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get the status change history for a lead.
    
    Returns a chronological list of all status changes with:
    - Previous and new status names
    - Confidence scores and rationale
    - Who made the change and when
    - Message excerpts that triggered automatic changes
    
    **Authentication Required**: Yes
    """
    try:
        # Initialize the update service
        update_service = LeadStatusUpdateService(db)
        
        # Get the history
        history = await update_service.get_status_history(lead_id, limit)
        
        history_response = StatusHistoryResponse(
            lead_id=lead_id,
            history=history,
            total_count=len(history)
        )
        
        return StatusHistorySuccessResponse(
            success=True,
            message=MessageResponse(
                title="Status History Retrieved",
                description=f"Found {len(history)} status change records"
            ),
            data=history_response,
            error_code=0
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error getting history for lead {lead_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error getting status history for lead {lead_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while retrieving status history"
        )


@router.get(
    "/available-statuses",
    response_model=AvailableStatusesSuccessResponse,
    summary="Get Available Lead Statuses",
    description="Retrieve all available lead statuses"
)
async def get_available_statuses(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all available lead statuses.
    
    Returns a list of all canonical lead statuses that can be used
    for manual updates or reference.
    
    **Authentication Required**: Yes
    """
    try:
        # Query all active statuses
        stmt = select(LeadStatus).where(
            LeadStatus.is_active == True,
            LeadStatus.is_deleted == False
        ).order_by(LeadStatus.name)
        
        result = await db.execute(stmt)
        statuses = result.scalars().all()
        
        status_list = [
            LeadStatusInfo(
                id=str(status.id),
                name=status.name,
                colour=status.colour,
                is_active=status.is_active
            )
            for status in statuses
        ]
        
        statuses_response = AvailableStatusesResponse(
            statuses=status_list,
            total_count=len(status_list)
        )
        
        return AvailableStatusesSuccessResponse(
            success=True,
            message=MessageResponse(
                title="Available Statuses Retrieved",
                description=f"Found {len(status_list)} available statuses"
            ),
            data=statuses_response,
            error_code=0
        )
        
    except Exception as e:
        logger.error(f"Unexpected error getting available statuses: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while retrieving available statuses"
        )


@router.post(
    "/validate-status",
    response_model=ValidationTestSuccessResponse,
    summary="Validate Status Name",
    description="Test status name normalization and validation"
)
async def validate_status_name(
    request: ValidationTestRequest = ...,
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Validate and normalize a status name.

    This endpoint is useful for:
    - Testing status name variations
    - Understanding how synonyms are mapped
    - Validating status names before manual updates

    **Authentication Required**: Yes
    """
    try:
        # Normalize the status name
        normalized_status, confidence = status_normalizer.normalize_status_name(request.status_name)

        # Validate the result
        is_valid, feedback = status_normalizer.validate_status_name(request.status_name)

        validation_result = ValidationTestResult(
            input_status=request.status_name,
            normalized_status=normalized_status,
            confidence=confidence,
            is_valid=is_valid,
            feedback=feedback
        )

        return ValidationTestSuccessResponse(
            success=True,
            message=MessageResponse(
                title="Status Validation Completed",
                description="Status name validation and normalization completed"
            ),
            data=validation_result,
            error_code=0
        )

    except Exception as e:
        logger.error(f"Unexpected error validating status name '{request.status_name}': {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while validating the status name"
        )
