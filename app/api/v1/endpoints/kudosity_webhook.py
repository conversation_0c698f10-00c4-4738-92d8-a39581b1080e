"""
Clean Kudosity SMS Webhook - Uses <PERSON>'s working_andy_chat logic
Handles incoming SMS messages and sends responses back via Kudosity API
"""

import datetime as dt
import os
import re
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from uuid import UUID, uuid4
import structlog
import asyncio
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy import select, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import get_db
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.agents.sms_assistant import get_andy_sms_assistant
from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
from app.services.kudosity_sms_service import get_kudosity_sms_service, SMSMessage
from app.services.question_extraction_service import get_question_extraction_service
from app.core.memory.sms_memory import get_sms_memory_manager

from app.core.logging import logger

# Create router
router = APIRouter(prefix="/webhooks", tags=["Kudosity SMS Webhook"])


def normalize_phone_number(phone_number: str) -> str:
    """
    Normalize phone number to ensure consistent format for Redis keys
    This should match the format used by <PERSON>'s SMS assistant
    """
    if not phone_number:
        return phone_number

    # Remove all non-digit characters
    clean_phone = ''.join(filter(str.isdigit, phone_number))

    # Handle different formats and convert to consistent format
    if len(clean_phone) == 10:
        # US format without country code
        clean_phone = "1" + clean_phone
    elif len(clean_phone) == 11 and clean_phone.startswith("1"):
        # US format with country code
        pass
    elif len(clean_phone) >= 10:
        # International format - keep as is
        pass

    # Add + prefix if not present
    if not phone_number.startswith("+"):
        clean_phone = "+" + clean_phone
    else:
        clean_phone = "+" + clean_phone

    return clean_phone


def is_hello_message(message: str) -> bool:
    """Detect if message is a simple hello/greeting from a lead"""
    message_lower = message.lower().strip()
    
    # Common greeting patterns
    hello_patterns = [
        "hello", "hi", "hey", "hiya", "howdy", "g'day", "gday", "good morning", 
        "good afternoon", "good evening", "morning", "afternoon", "evening"
    ]
    
    # Check if message is just a greeting (with optional punctuation)
    clean_message = re.sub(r'[^\w\s]', '', message_lower).strip()
    
    # Simple greeting detection
    if clean_message in hello_patterns:
        return True
        
    # Also check original message for patterns with apostrophes like "g'day"
    if any(pattern in message_lower for pattern in ["g'day"]):
        return True
        
    # Greeting with additional words like "hello there" or "hi andy"
    words = clean_message.split()
    if len(words) <= 3 and any(pattern in clean_message for pattern in hello_patterns):
        return True
        
    return False


async def sync_lead_context_if_needed(db: AsyncSession, normalized_phone: str, lead_id: str) -> None:
    """
    Ensure lead context is synced between database and Redis
    This helps when a lead exists in DB but Andy doesn't have the context
    """
    try:
        memory_manager = get_sms_memory_manager()
        existing_context = memory_manager.get_lead_context(normalized_phone)

        if not existing_context or not existing_context.get("lead_id"):
            # Get lead details from database
            result = await db.execute(
                select(Lead).where(
                    and_(
                        Lead.id == UUID(lead_id),
                        Lead.is_active == True,
                        Lead.is_deleted == False
                    )
                )
            )
            lead = result.scalar_one_or_none()

            if lead:
                # Create or update context with lead information
                context = existing_context or {}
                context.update({
                    "lead_id": lead_id,
                    "name": f"{lead.first_name} {lead.last_name}".strip(),
                    "phone_number": normalized_phone,
                    "city": lead.location,
                    "budget": lead.budget_preference,
                    "last_updated": dt.datetime.now().isoformat(),
                    "synced_from_db": True
                })

                memory_manager.store_lead_context(normalized_phone, context)
                logger.info(f"Synced lead context for {normalized_phone} with lead_id {lead_id}")

    except Exception as e:
        logger.error(f"Error syncing lead context: {str(e)}")


async def find_or_create_lead_by_phone(db: AsyncSession, phone_number: str) -> Optional[str]:
    """
    Find lead by phone number or create new one if not found
    """
    try:
        # Clean phone number
        clean_phone = phone_number.strip().replace(" ", "").replace("-", "").replace("(", "").replace(")", "")
        
        # Try different phone number formats
        phone_variations = [
            clean_phone,
            f"+{clean_phone}",
            f"61{clean_phone}" if not clean_phone.startswith("61") else clean_phone,
            f"+61{clean_phone[2:]}" if clean_phone.startswith("61") else f"+61{clean_phone}",
            f"0{clean_phone[2:]}" if clean_phone.startswith("61") and len(clean_phone) > 2 else clean_phone,
        ]
        
        # Remove duplicates
        phone_variations = list(set(phone_variations))
        
        # Search for lead with any of these phone variations
        query = select(Lead.id).where(
            and_(
                Lead.is_active == True,
                Lead.is_deleted == False,
                or_(
                    Lead.mobile.in_(phone_variations),
                    Lead.phone.in_(phone_variations)
                )
            )
        )
        
        result = await db.execute(query)
        leads = result.scalars().all()

        if leads:
            if len(leads) == 1:
                lead_id = leads[0]
                logger.info(f"Found existing lead for phone {phone_number}: {lead_id}")
                return str(lead_id)
            else:
                # Multiple leads found - return the most recent one
                query_with_order = select(Lead.id).where(
                    and_(
                        Lead.is_active == True,
                        Lead.is_deleted == False,
                        or_(
                            Lead.mobile.in_(phone_variations),
                            Lead.phone.in_(phone_variations)
                        )
                    )
                ).order_by(Lead.created_at.desc()).limit(1)

                result = await db.execute(query_with_order)
                lead_id = result.scalar_one()
                logger.info(f"Found multiple leads for phone {phone_number}, using most recent: {lead_id}")
                return str(lead_id)
        else:
            # Create new lead
            logger.info(f"No lead found for phone {phone_number}, creating new lead")
            return await create_new_lead_from_sms(db, phone_number)
            
    except Exception as e:
        logger.error(f"Error finding/creating lead by phone: {str(e)}")
        return None


async def create_new_lead_from_sms(db: AsyncSession, phone_number: str) -> Optional[str]:
    """
    Create a new lead from SMS message
    """
    try:
        # Default lead status ID for "New Lead" - you may need to adjust this UUID
        default_lead_status_id = "f53c50cf-9374-4d18-98c1-c905215051eb"  # New Lead status
        
        # Create new lead
        new_lead = Lead(
            id=uuid4(),
            first_name="SMS Lead",  # Default name, can be updated later
            last_name="",
            phone=phone_number,
            mobile=phone_number,
            lead_status_id=UUID(default_lead_status_id),
            is_active=True,
            is_deleted=False
        )
        
        db.add(new_lead)
        await db.commit()
        await db.refresh(new_lead)
        
        logger.info(f"Created new lead from SMS: {new_lead.id} for phone {phone_number}")
        return str(new_lead.id)
        
    except Exception as e:
        logger.error(f"Error creating new lead from SMS: {str(e)}")
        await db.rollback()
        return None


async def store_conversation_messages(
    db: AsyncSession,
    lead_id: str,
    incoming_message: str,
    outgoing_message: str,
    phone_number: str
) -> None:
    """
    Store both incoming and outgoing messages in conversation_message table
    """
    try:
        # Store incoming message from lead
        # Get franchisor_id from lead data
        lead_service = LeadService()
        lead = await lead_service.get_lead_by_id(lead_id)
        if not lead or not lead.franchisor_id:
            raise HTTPException(status_code=400, detail="Lead not found or missing franchisor_id")
        
        incoming_msg = ConversationMessage(
            lead_id=UUID(lead_id),
            franchisor_id=lead.franchisor_id,
            sender="lead",
            message=incoming_message,
            is_active=True,
            is_deleted=False
        )
        
        # Store outgoing message from system (Andy)
        outgoing_msg = ConversationMessage(
            lead_id=UUID(lead_id),
            franchisor_id=lead.franchisor_id,
            sender="system",
            message=outgoing_message,
            is_active=True,
            is_deleted=False
        )
        
        db.add(incoming_msg)
        db.add(outgoing_msg)
        await db.commit()
        
        logger.info(
            f"Stored conversation messages",
            lead_id=lead_id,
            phone=phone_number,
            incoming_length=len(incoming_message),
            outgoing_length=len(outgoing_message)
        )
        
    except Exception as e:
        logger.error(f"Error storing conversation messages: {str(e)}")
        await db.rollback()


@router.post(
    "/kudosity",
    summary="Kudosity SMS Webhook",
    description="Clean webhook for processing Kudosity SMS messages with Andy's working_andy_chat logic"
)
async def kudosity_sms_webhook(request: Request) -> JSONResponse:
    """
    Process incoming SMS messages from Kudosity and respond with Andy's logic
    Uses the exact same logic as working_andy_chat.py
    """
    try:
        # Parse webhook payload
        webhook_data = await request.json()
        
        # PRINT INCOMING MESSAGE TO TERMINAL
        print("\n" + "="*80)
        print("KUDOSITY SMS WEBHOOK - INCOMING MESSAGE")
        print("="*80)
        print(f"Timestamp: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"From: {webhook_data.get('mo', {}).get('sender', 'Unknown')}")
        print(f"To: {webhook_data.get('mo', {}).get('recipient', 'Unknown')}")
        print(f"Message: {webhook_data.get('mo', {}).get('message', 'No message')}")
        print("="*80)
        
        # Extract message details
        mo_data = webhook_data.get('mo', {})
        sender_phone = mo_data.get('sender', '')
        recipient_phone = mo_data.get('recipient', '')
        message_text = mo_data.get('message', '')

        if not sender_phone or not message_text:
            raise HTTPException(status_code=400, detail="Missing sender or message")

        # Normalize phone number for consistent Redis key usage
        normalized_phone = normalize_phone_number(sender_phone)
        
        # Get database session
        db_gen = get_db()
        db = await db_gen.__anext__()

        try:
            # Find or create lead by phone number (use original phone for database lookup)
            lead_id = await find_or_create_lead_by_phone(db, sender_phone)

            # PROCESS MESSAGE WITH ANDY'S LOGIC (same as working_andy_chat.py)
            print(f"Andy is thinking...")
            print(f"Using normalized phone: {normalized_phone}")
            print(f"Found lead ID: {lead_id}")

            # Check if Coochie Workflow is enabled
            workflow_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"

            # Initialize result variable
            result = {"success": False, "response": "", "error": "Not processed"}
            andy_response = "I apologize, but I encountered an error. Please try again later."

            try:
                # Use Andy SMS assistant (has exact workflow implementation)
                andy = get_andy_sms_assistant()

                # If we found a lead but Andy might not have the context, sync it
                # BUT only if it's not a hello message (to allow conversation restart)
                if lead_id and not is_hello_message(message_text):
                    await sync_lead_context_if_needed(db, normalized_phone, lead_id)

                # Process SMS with Andy using NORMALIZED phone number for consistent Redis keys
                result = await asyncio.wait_for(
                    andy.process_sms(normalized_phone, message_text, lead_id=lead_id),
                    timeout=None # 60 second timeout for complex AI workflows
                )

                # AUTO-UPDATE LEAD STATUS BASED ON MESSAGE
                if lead_id and result.get("success", False):
                    # Create a dedicated database session for status updates
                    # This prevents connection issues from affecting the main webhook flow
                    status_db_gen = get_db()
                    status_db = await status_db_gen.__anext__()

                    try:
                        from app.services.lead_status_update_service import LeadStatusUpdateService
                        from app.services.lead_status_classification_service import StatusContext

                        # Initialize status update service with dedicated session
                        status_service = LeadStatusUpdateService(status_db)

                        # Build context for better classification
                        context = StatusContext(
                            current_status=None,  # Will be fetched by the service
                            territory_available=True,  # Default assumption
                            call_attempts=0
                        )

                        # Update status based on message content
                        status_result = await status_service.update_status_from_message(
                            lead_id=lead_id,
                            message_text=message_text,
                            context=context,
                            changed_by="andy_webhook",
                            source="andy"
                        )

                        # Log status update result
                        if status_result.changed:
                            print(f"STATUS UPDATED: {status_result.old_status} → {status_result.new_status}")
                            print(f"Confidence: {status_result.confidence:.2f} | Method: Auto-classification")
                        else:
                            print(f"Status analyzed but unchanged: {status_result.new_status}")

                        # Add status update info to result metadata
                        result["status_update"] = {
                            "changed": status_result.changed,
                            "old_status": status_result.old_status,
                            "new_status": status_result.new_status,
                            "confidence": status_result.confidence,
                            "rationale": status_result.rationale
                        }

                    except Exception as status_error:
                        logger.error(
                            f"Status update failed for lead {lead_id}: {status_error}",
                            lead_id=lead_id,
                            message_text=message_text[:100],
                            error_type=type(status_error).__name__
                        )
                        # Continue with SMS processing even if status update fails
                    finally:
                        # Always close the dedicated status update session
                        try:
                            await status_db.close()
                        except Exception as close_error:
                            logger.warning(f"Error closing status update session: {close_error}")

                # FOLLOW-UP SUPPRESSION SYSTEM (Environment variable check)
                followup_suppression_enabled = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower() == "true"

                # HARD BLOCK: Suppress any follow-up attempts
                if followup_suppression_enabled:
                    suppression_result = {
                        "suppressed": True,
                        "reason": "WORKFLOW_ANDY_NO_FOLLOWUPS enabled",
                        "context": "webhook_followup_attempt",
                        "success": False
                    }
                else:
                    suppression_result = {"suppressed": False}

                result["followup_management"] = suppression_result
                
                if result.get("success"):
                    andy_response = result.get("response", "")
                    processing_time = result.get('processing_time', 0)
                    stage = result.get('current_stage', 'unknown')
                    
                    # PRINT ANDY'S RESPONSE TO TERMINAL
                    print("\n" + "="*80)
                    print("ANDY'S RESPONSE")
                    print("="*80)
                    print(f"To: {sender_phone} (normalized: {normalized_phone})")
                    print(f"Andy says:")
                    print(f"   {andy_response}")
                    print(f"Processing: {processing_time:.2f}s | Stage: {stage}")
                    print(f"Lead ID: {lead_id}")
                    print("="*80)
                    
                else:
                    andy_response = "I apologize, but I encountered an error. Please try again later."
                    logger.error(f"Andy processing failed: {result.get('error')}")
                    print(f"Andy processing failed: {result.get('error')}")

            except asyncio.TimeoutError:
                result = {"success": False, "error": "Processing timeout", "response": ""}
                andy_response = "I apologize, but I'm taking too long to respond. Please try again."
                logger.error("Andy processing timed out")
                print(f"Andy processing timed out")

            except Exception as e:
                result = {"success": False, "error": str(e), "response": ""}
                andy_response = "I apologize, but I encountered an error. Please try again later."
                logger.error(f"Andy processing error: {str(e)}")
                print(f"Andy processing error: {str(e)}")
            
            # SEND SMS RESPONSE - RESPECTS SMS_TEST_MODE AND KUDOSITY_SMS_ENABLED
            from app.core.sms_control import sms_controller, should_send_sms, print_sms_output, create_mock_sms_response

            # Log the SMS attempt
            sms_log_result = sms_controller.log_sms_attempt(
                phone_number=sender_phone,
                message=andy_response,
                context="coochie_workflow" if workflow_enabled else "general_response"
            )

            if should_send_sms():
                # SMS SENDING ENABLED - Send actual SMS
                print(f"📱 SMS SENDING ENABLED - Sending response via Kudosity")
                sms_status = sms_controller.get_sms_status()
                print(f"   SMS Configuration: {sms_status}")

                try:
                    kudosity_service = get_kudosity_sms_service()
                    sms_message = SMSMessage(
                        to=f"+{sender_phone}" if not sender_phone.startswith("+") else sender_phone,
                        message=andy_response
                    )

                    sms_result = await kudosity_service.send_sms(sms_message)

                    if sms_result.success:
                        print(f"✅ SMS response sent successfully!")
                        print(f"   Message ID: {sms_result.message_id}")
                        logger.info(
                            f"SMS sent successfully",
                            message_id=sms_result.message_id,
                            phone_masked=sms_controller._mask_phone_number(sender_phone),
                            message_length=len(andy_response)
                        )
                    else:
                        print(f"❌ SMS sending failed: {sms_result.error}")
                        logger.error(f"SMS sending failed: {sms_result.error}")

                except Exception as sms_error:
                    print(f"❌ SMS sending error: {str(sms_error)}")
                    logger.error(f"SMS sending error: {str(sms_error)}")
                    # Create failed response
                    from app.services.kudosity_sms_service import SMSResponse
                    sms_result = SMSResponse(
                        success=False,
                        message_id=None,
                        error=str(sms_error)
                    )
            else:
                # SMS DISABLED OR TEST MODE - Print to terminal only
                context = "Coochie Workflow Response" if workflow_enabled else "Andy AI Response"
                print_sms_output(sender_phone, andy_response, context)

                # Create a mock successful response for consistency
                mock_response = create_mock_sms_response("webhook_response")
                from app.services.kudosity_sms_service import SMSResponse
                sms_result = SMSResponse(
                    success=mock_response["success"],
                    message_id=mock_response["message_id"],
                    error=mock_response["error"]
                )
            
            # CONVERSATION STORAGE HANDLED BY ANDY
            # Andy's SMS assistant automatically stores conversations in the database
            # No need to duplicate storage here - Andy handles it in his workflow
            if result.get("conversation_stored"):
                print(f"Conversation stored by Andy for lead: {lead_id}")
            else:
                print(f"Andy did not store conversation (no lead found or error occurred)")
            
            # Return success response
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": {
                        "title": "SMS Processed",
                        "description": "Message processed with Andy's logic and response sent successfully"
                    },
                    "data": {
                        "message": "SMS conversation processed successfully",
                        "event_type": "SMS_INBOUND",
                        "ai_response": andy_response,
                        "ai_success": result.get("success", False),
                        "sms_sent": sms_result.success,
                        "lead_found": bool(lead_id),
                        "conversation_stored": result.get("conversation_stored", False),
                        "processing_metadata": {
                            "timestamp": dt.datetime.now().isoformat(),
                            "phone_number": sender_phone,
                            "normalized_phone": normalized_phone,
                            "message_length": len(message_text),
                            "response_length": len(andy_response),
                            "processing_time": result.get('processing_time', 0),
                            "conversation_stage": result.get('current_stage', 'unknown'),
                            "lead_id": lead_id
                        },
                        "question_extraction": {
                            "questions_found": result.get('question_extraction_result', {}).get('questions_found', 0),
                            "questions_stored": result.get('question_extraction_result', {}).get('questions_stored', 0),
                            "extraction_success": result.get('question_extraction_result', {}).get('success', False)
                        },
                        "followup_management": result.get("followup_management", {
                            "success": False,
                            "action": "none",
                            "message": "Follow-up not processed"
                        }),
                        "andy_response": andy_response
                    },
                    "error_code": 0
                }
            )

        finally:
            # Ensure database session is properly closed
            await db.close()

    except Exception as e:
        logger.error(f"Webhook processing error: {str(e)}")
        print(f"\nWEBHOOK ERROR: {str(e)}")

        # Ensure database session is closed even in case of error
        try:
            if 'db' in locals():
                await db.close()
        except Exception as db_error:
            logger.error(f"Error closing database session: {db_error}")

        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": {
                    "title": "Processing Error",
                    "description": "An error occurred while processing the SMS webhook"
                },
                "data": {
                    "error": str(e),
                    "timestamp": dt.datetime.now().isoformat()
                },
                "error_code": 500
            }
        )


@router.get(
    "/kudosity/health",
    summary="Kudosity Webhook Health Check",
    description="Check if the Kudosity webhook is working properly"
)
async def kudosity_webhook_health():
    """Health check for Kudosity webhook"""
    return JSONResponse(
        status_code=200,
        content={
            "status": "healthy",
            "service": "kudosity_sms_webhook",
            "timestamp": dt.datetime.now().isoformat(),
            "version": "2.0",
            "andy_integration": "working_andy_chat_logic"
        }
    )
