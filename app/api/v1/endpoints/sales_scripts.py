"""
Sales Scripts API endpoints
"""

import logging
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.responses import create_success_response, create_error_response
from app.core.responses.models import ErrorCodes
from app.services.sales_script_service import SalesScriptService
from app.schemas.sales_script import (
    SalesScriptUpdate,
    SalesScriptSuccessResponse,
    SalesScriptListSuccessResponse
)
from app.schemas.user import UserBase

logger = logging.getLogger(__name__)

router = APIRouter()

# Common responses for OpenAPI documentation
COMMON_RESPONSES = {
    400: {"description": "Bad Request"},
    401: {"description": "Unauthorized"},
    403: {"description": "Forbidden"},
    404: {"description": "Not Found"},
    500: {"description": "Internal Server Error"}
}


def get_sales_script_service(db: AsyncSession = Depends(get_db)) -> SalesScriptService:
    """Dependency to get sales script service"""
    return SalesScriptService(db)


@router.get(
    "/",
    response_model=SalesScriptListSuccessResponse,
    summary="Get All Sales Scripts",
    description="Get all sales scripts with filtering, search, and pagination",
    responses={
        200: {
            "description": "Sales scripts retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Sales Scripts Retrieved",
                            "description": "Successfully retrieved 4 sales scripts"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "f004709b-5b53-462b-9220-16276282be07",
                                    "script_title": "Initial greeting",
                                    "script_content": "Hi, this is Kay, calling you on behalf of the Coochie Hydrogreen...",
                                    "script_stage": "initial_greeting",
                                    "order_sequence": 1,
                                    "has_variables": False,
                                    "variable_schema": None,
                                    "is_active": True,
                                    "is_deleted": False,
                                    "created_at": "2025-07-16T14:29:31.852000+00:00",
                                    "updated_at": "2025-07-16T14:29:31.852000+00:00",
                                    "deleted_at": None
                                }
                            ],
                            "total_count": 4,
                            "pagination": {
                                "page": 1,
                                "limit": 20,
                                "total": 4,
                                "pages": 1
                            }
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_all_sales_scripts(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term for script title or content"),
    script_stage: Optional[str] = Query(None, description="Filter by script stage: initial_greeting, prequalification, document_qa, goodbye"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    sort_by: Optional[str] = Query(None, description="Sort by field: script_title, script_stage, order_sequence, created_at"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc (ascending) or desc (descending)"),
    current_user: UserBase = Depends(get_current_user),
    sales_script_service: SalesScriptService = Depends(get_sales_script_service)
):
    """Get all sales scripts with filtering, search, and pagination"""
    try:
        logger.info(f"Getting sales scripts with filters: search={search}, stage={script_stage}, active={is_active}")
        
        result = await sales_script_service.get_scripts_with_pagination(
            skip=skip,
            limit=limit,
            search=search,
            script_stage=script_stage,
            is_active=is_active,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        return create_success_response(
            data=result.model_dump(),
            message_title="Sales Scripts Retrieved",
            message_description=f"Successfully retrieved {len(result.items)} sales scripts"
        )
        
    except Exception as e:
        logger.error(f"Error getting sales scripts: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Sales Scripts",
            message_description=str(e),
            status_code=500
        )


@router.get(
    "/{script_id}",
    response_model=SalesScriptSuccessResponse,
    summary="Get Sales Script by ID",
    description="Get a specific sales script by its ID",
    responses={
        200: {
            "description": "Sales script retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Sales Script Retrieved",
                            "description": "Sales script retrieved successfully"
                        },
                        "data": {
                            "id": "f004709b-5b53-462b-9220-16276282be07",
                            "script_title": "Initial greeting",
                            "script_content": "Hi, this is Kay, calling you on behalf of the Coochie Hydrogreen...",
                            "script_stage": "initial_greeting",
                            "order_sequence": 1,
                            "has_variables": False,
                            "variable_schema": None,
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-07-16T14:29:31.852000+00:00",
                            "updated_at": "2025-07-16T14:29:31.852000+00:00",
                            "deleted_at": None
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_sales_script(
    script_id: Annotated[str, Path(description="Sales script ID", example="f004709b-5b53-462b-9220-16276282be07")],
    current_user: UserBase = Depends(get_current_user),
    sales_script_service: SalesScriptService = Depends(get_sales_script_service)
):
    """Get sales script by ID"""
    try:
        logger.info(f"Getting sales script by ID: {script_id}")
        
        script = await sales_script_service.get_script_by_id(script_id)
        
        if not script:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Sales Script Not Found",
                message_description=f"Sales script with ID {script_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data=script.model_dump(),
            message_title="Sales Script Retrieved",
            message_description="Sales script retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting sales script {script_id}: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Sales Script",
            message_description=str(e),
            status_code=500
        )


@router.put(
    "/{script_id}",
    response_model=SalesScriptSuccessResponse,
    summary="Update Sales Script",
    description="Update a sales script by its ID",
    responses={
        200: {
            "description": "Sales script updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Sales Script Updated",
                            "description": "Sales script updated successfully"
                        },
                        "data": {
                            "id": "f004709b-5b53-462b-9220-16276282be07",
                            "script_title": "Updated Initial greeting",
                            "script_content": "Updated script content...",
                            "script_stage": "initial_greeting",
                            "order_sequence": 1,
                            "has_variables": False,
                            "variable_schema": None,
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-07-16T14:29:31.852000+00:00",
                            "updated_at": "2025-07-16T14:29:31.852000+00:00",
                            "deleted_at": None
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def update_sales_script(
    script_id: Annotated[str, Path(description="Sales script ID", example="f004709b-5b53-462b-9220-16276282be07")],
    script_data: SalesScriptUpdate,
    current_user: UserBase = Depends(get_current_user),
    sales_script_service: SalesScriptService = Depends(get_sales_script_service)
):
    """Update sales script by ID"""
    try:
        logger.info(f"Updating sales script: {script_id}")
        
        script = await sales_script_service.update_script(script_id, script_data)
        
        if not script:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Sales Script Not Found",
                message_description=f"Sales script with ID {script_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data=script.model_dump(),
            message_title="Sales Script Updated",
            message_description="Sales script updated successfully"
        )
        
    except HTTPException as he:
        return create_error_response(
            error_code=ErrorCodes.VALIDATION_ERROR,
            message_title="Validation Error",
            message_description=he.detail,
            status_code=he.status_code
        )
    except Exception as e:
        logger.error(f"Error updating sales script {script_id}: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Updating Sales Script",
            message_description=str(e),
            status_code=500
        )
