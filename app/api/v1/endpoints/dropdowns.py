"""
Dropdown APIs for various reference data
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database.connection import get_db
from app.core.security.auth import get_current_user
from app.schemas.dropdown import (
    LeadSourceResponse,
    LeadStatusResponse,
    DropdownListResponse
)
from app.models.lead_reference import LeadSource, LeadStatus
from app.core.logging import logger

router = APIRouter(prefix="/dropdowns", tags=["Dropdowns"])


@router.get(
    "/lead-sources",
    response_model=DropdownListResponse[LeadSourceResponse],
    summary="Get Lead Sources",
    description="Retrieve all active lead sources for dropdown selection",
    responses={
        200: {
            "description": "Lead sources retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Sources Retrieved",
                            "description": "Lead sources retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440000",
                                    "name": "Website",
                                    "is_active": True
                                },
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440001",
                                    "name": "Facebook",
                                    "is_active": True
                                }
                            ],
                            "total_count": 2
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def get_lead_sources(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all active lead sources for dropdown selection.
    
    Returns a list of all active lead sources that can be used
    in lead creation and update forms.
    """
    try:
        # Query active lead sources
        query = select(LeadSource).where(
            LeadSource.is_active == True,
            LeadSource.is_deleted == False
        ).order_by(LeadSource.name)
        
        result = await db.execute(query)
        lead_sources = result.scalars().all()
        
        # Convert to response format
        items = [
            LeadSourceResponse(
                id=str(source.id),
                name=source.name,
                is_active=source.is_active
            )
            for source in lead_sources
        ]
        
        return DropdownListResponse[LeadSourceResponse](
            success=True,
            status="success",
            message={
                "title": "Lead Sources Retrieved",
                "description": "Lead sources retrieved successfully"
            },
            data={
                "items": items,
                "total_count": len(items)
            }
        )
        
    except Exception as e:
        logger.error(f"Error retrieving lead sources: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve lead sources")


@router.get(
    "/lead-statuses",
    response_model=DropdownListResponse[LeadStatusResponse],
    summary="Get Lead Statuses",
    description="Retrieve all active lead statuses for dropdown selection",
    responses={
        200: {
            "description": "Lead statuses retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Lead Statuses Retrieved",
                            "description": "Lead statuses retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440000",
                                    "name": "New Lead",
                                    "colour": "#8B00FF",
                                    "is_active": True
                                },
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440001",
                                    "name": "Qualified",
                                    "colour": "#00FF00",
                                    "is_active": True
                                }
                            ],
                            "total_count": 2
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def get_lead_statuses(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all active lead statuses for dropdown selection.
    
    Returns a list of all active lead statuses with their colors
    that can be used in lead creation and update forms.
    """
    try:
        # Query active lead statuses
        query = select(LeadStatus).where(
            LeadStatus.is_active == True,
            LeadStatus.is_deleted == False
        ).order_by(LeadStatus.name)
        
        result = await db.execute(query)
        lead_statuses = result.scalars().all()
        
        # Convert to response format
        items = [
            LeadStatusResponse(
                id=str(status.id),
                name=status.name,
                colour=status.colour,
                is_active=status.is_active
            )
            for status in lead_statuses
        ]
        
        return DropdownListResponse[LeadStatusResponse](
            success=True,
            status="success",
            message={
                "title": "Lead Statuses Retrieved",
                "description": "Lead statuses retrieved successfully"
            },
            data={
                "items": items,
                "total_count": len(items)
            }
        )
        
    except Exception as e:
        logger.error(f"Error retrieving lead statuses: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve lead statuses")
