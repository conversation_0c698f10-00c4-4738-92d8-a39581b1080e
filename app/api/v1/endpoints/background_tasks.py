"""
Background Tasks API endpoints
Endpoints for monitoring and managing background tasks
"""

from typing import Optional
from fastapi import APIRouter, Depends, Query, Path
import structlog

from app.core.security.enhanced_auth_middleware import get_current_user
from app.services.background_task_manager import get_background_task_manager
from app.core.responses.utils import create_success_response, create_error_response
from app.core.responses.models import ErrorCodes

logger = structlog.get_logger(__name__)

router = APIRouter(prefix="/background-tasks", tags=["Background Tasks"])


@router.get(
    "/status/{task_id}",
    summary="Get Task Status",
    description="Get the status and result of a background task",
    response_description="Task status information"
)
async def get_task_status(
    task_id: str = Path(..., description="Celery task ID"),
    current_user: dict = Depends(get_current_user)
):
    """Get background task status"""
    try:
        task_manager = get_background_task_manager()
        status_info = task_manager.get_task_status(task_id)
        
        return create_success_response(
            data=status_info,
            message_title="Task Status Retrieved",
            message_description=f"Status for task {task_id}"
        )
        
    except Exception as e:
        logger.error("Failed to get task status",
                    task_id=task_id,
                    error=str(e))
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Task Status Error",
            message_description="Failed to retrieve task status",
            status_code=500
        )


@router.post(
    "/cancel/{task_id}",
    summary="Cancel Task",
    description="Cancel a running background task",
    response_description="Task cancellation result"
)
async def cancel_task(
    task_id: str = Path(..., description="Celery task ID"),
    current_user: dict = Depends(get_current_user)
):
    """Cancel a background task"""
    try:
        task_manager = get_background_task_manager()
        cancelled = task_manager.cancel_task(task_id)
        
        if cancelled:
            return create_success_response(
                data={"task_id": task_id, "cancelled": True},
                message_title="Task Cancelled",
                message_description=f"Task {task_id} has been cancelled"
            )
        else:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Cancellation Failed",
                message_description="Failed to cancel task",
                status_code=400
            )
        
    except Exception as e:
        logger.error("Failed to cancel task",
                    task_id=task_id,
                    error=str(e))
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Cancellation Error",
            message_description="Failed to cancel task",
            status_code=500
        )


@router.get(
    "/active",
    summary="Get Active Tasks",
    description="Get list of currently active background tasks",
    response_description="List of active tasks"
)
async def get_active_tasks(
    current_user: dict = Depends(get_current_user)
):
    """Get list of active background tasks"""
    try:
        task_manager = get_background_task_manager()
        active_tasks = task_manager.get_active_tasks()
        
        return create_success_response(
            data={
                "active_tasks": active_tasks,
                "count": len(active_tasks)
            },
            message_title="Active Tasks Retrieved",
            message_description=f"Found {len(active_tasks)} active tasks"
        )
        
    except Exception as e:
        logger.error("Failed to get active tasks", error=str(e))
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Active Tasks Error",
            message_description="Failed to retrieve active tasks",
            status_code=500
        )


@router.get(
    "/queue/length",
    summary="Get Queue Length",
    description="Get the number of tasks waiting in the queue",
    response_description="Queue length information"
)
async def get_queue_length(
    queue_name: Optional[str] = Query(None, description="Queue name (default: document_processing)"),
    current_user: dict = Depends(get_current_user)
):
    """Get queue length"""
    try:
        task_manager = get_background_task_manager()
        queue_length = task_manager.get_queue_length(queue_name)
        
        return create_success_response(
            data={
                "queue_name": queue_name or "document_processing",
                "length": queue_length
            },
            message_title="Queue Length Retrieved",
            message_description=f"Queue has {queue_length} pending tasks"
        )
        
    except Exception as e:
        logger.error("Failed to get queue length",
                    queue_name=queue_name,
                    error=str(e))
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Queue Length Error",
            message_description="Failed to retrieve queue length",
            status_code=500
        )


@router.get(
    "/health",
    summary="Task System Health",
    description="Check the health of the background task system",
    response_description="Task system health status"
)
async def get_task_system_health(
    current_user: dict = Depends(get_current_user)
):
    """Get background task system health"""
    try:
        task_manager = get_background_task_manager()
        
        # Check Celery connection
        try:
            stats = task_manager.celery_app.control.inspect().stats()
            celery_healthy = stats is not None
        except Exception:
            celery_healthy = False
        
        # Get queue length
        queue_length = task_manager.get_queue_length()
        
        # Get active tasks count
        active_tasks = task_manager.get_active_tasks()
        active_count = len(active_tasks)
        
        health_data = {
            "celery_healthy": celery_healthy,
            "queue_length": queue_length,
            "active_tasks_count": active_count,
            "status": "healthy" if celery_healthy else "unhealthy"
        }
        
        return create_success_response(
            data=health_data,
            message_title="Task System Health",
            message_description=f"System is {health_data['status']}"
        )
        
    except Exception as e:
        logger.error("Failed to get task system health", error=str(e))
        return create_error_response(
            error_code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message_title="Health Check Error",
            message_description="Failed to check task system health",
            status_code=500
        )
