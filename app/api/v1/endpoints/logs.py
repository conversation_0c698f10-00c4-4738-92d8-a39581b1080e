"""
Log Download API Endpoints
Simple API for downloading log files without authentication
"""

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import StreamingResponse
from pathlib import Path
import os
import zipfile
from datetime import datetime
import io

from app.core.logging import logger
from app.core.app_rules import success_response

router = APIRouter()

# Define log directory and available log files
LOG_DIR = Path("logs")
AVAILABLE_LOGS = {
    "app": "app.log",
    "error": "error.log"
}


@router.get("/", summary="List available log files")
async def list_log_files():
    """
    List all available log files for download.
    
    Returns:
        List of available log files with their information
    """
    try:
        log_files = []
        
        for log_type, filename in AVAILABLE_LOGS.items():
            file_path = LOG_DIR / filename
            
            if file_path.exists():
                # Get file stats
                stat = file_path.stat()
                log_files.append({
                    "type": log_type,
                    "filename": filename,
                    "size_bytes": stat.st_size,
                    "size_mb": round(stat.st_size / (1024 * 1024), 2),
                    "last_modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "download_url": f"/api/logs/download/{log_type}"
                })
            else:
                log_files.append({
                    "type": log_type,
                    "filename": filename,
                    "status": "not_found",
                    "download_url": f"/api/logs/download/{log_type}"
                })
        
        return success_response(
            details={
                "log_files": log_files,
                "total_files": len([f for f in log_files if "status" not in f]),
                "zip_download": {
                    "url": "/api/logs/download/zip",
                    "description": "Download all log files as a ZIP archive"
                }
            },
            title="Available Log Files",
            description="List of all available log files for download"
        )
        
    except Exception as e:
        logger.error(f"Error listing log files: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list log files"
        )


@router.get("/download/zip", summary="Download all log files as ZIP")
async def download_logs_zip():
    """
    Download all available log files as a ZIP archive.

    Returns:
        ZIP file containing all available log files
    """
    try:
        # Log the zip download request
        logger.info("ZIP download of all log files requested")

        # Create in-memory ZIP file
        zip_buffer = io.BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            files_added = 0

            for log_type, filename in AVAILABLE_LOGS.items():
                file_path = LOG_DIR / filename

                if file_path.exists() and os.access(file_path, os.R_OK):
                    # Add file to ZIP with a descriptive name
                    zip_filename = f"{log_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
                    zip_file.write(file_path, zip_filename)
                    files_added += 1
                    logger.info(f"Added {filename} to ZIP as {zip_filename}")
                else:
                    logger.warning(f"Skipped {filename} - file not found or not readable")

        if files_added == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No log files available for download"
            )

        # Get ZIP content
        zip_buffer.seek(0)
        zip_content = zip_buffer.getvalue()
        zip_buffer.close()

        # Generate timestamped ZIP filename
        zip_filename = f"growthhive_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        logger.info(f"ZIP file created successfully with {files_added} log files, size: {len(zip_content)} bytes")

        # Create streaming response for ZIP file
        def zip_generator():
            yield zip_content

        return StreamingResponse(
            zip_generator(),
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename={zip_filename}",
                "Content-Length": str(len(zip_content))
            }
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating ZIP file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create ZIP file"
        )


@router.get("/download/{log_type}", summary="Download log file")
async def download_log_file(log_type: str):
    """
    Download a specific log file.

    Args:
        log_type: Type of log file to download ('app' or 'error')

    Returns:
        File download response
    """
    try:
        # Validate log type
        if log_type not in AVAILABLE_LOGS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid log type. Available types: {list(AVAILABLE_LOGS.keys())}"
            )

        filename = AVAILABLE_LOGS[log_type]
        file_path = LOG_DIR / filename

        # Check if file exists
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Log file '{filename}' not found"
            )

        # Check if file is readable
        if not os.access(file_path, os.R_OK):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Log file '{filename}' is not readable"
            )

        # Log the download request
        logger.info(f"Log file download requested: {filename}")

        # Get file size for Content-Length header
        file_size = file_path.stat().st_size

        # Create streaming response for better handling of large files
        def file_generator():
            with open(file_path, 'rb') as file:
                while True:
                    chunk = file.read(8192)  # Read in 8KB chunks
                    if not chunk:
                        break
                    yield chunk

        # Generate timestamped filename
        download_filename = f"{log_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        return StreamingResponse(
            file_generator(),
            media_type="text/plain",
            headers={
                "Content-Disposition": f"attachment; filename={download_filename}",
                "Content-Length": str(file_size)
            }
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error downloading log file '{log_type}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download log file"
        )


@router.get("/download/{log_type}/info", summary="Get log file information")
async def get_log_file_info(log_type: str):
    """
    Get detailed information about a specific log file.
    
    Args:
        log_type: Type of log file ('app' or 'error')
        
    Returns:
        Detailed information about the log file
    """
    try:
        # Validate log type
        if log_type not in AVAILABLE_LOGS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid log type. Available types: {list(AVAILABLE_LOGS.keys())}"
            )
        
        filename = AVAILABLE_LOGS[log_type]
        file_path = LOG_DIR / filename
        
        # Check if file exists
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Log file '{filename}' not found"
            )
        
        # Get file stats
        stat = file_path.stat()
        
        # Try to count lines (for small files)
        line_count = None
        try:
            if stat.st_size < 50 * 1024 * 1024:  # Only for files smaller than 50MB
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    line_count = sum(1 for _ in f)
        except Exception:
            pass  # Ignore errors in line counting
        
        file_info = {
            "type": log_type,
            "filename": filename,
            "full_path": str(file_path.absolute()),
            "size_bytes": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "size_kb": round(stat.st_size / 1024, 2),
            "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "last_modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "last_accessed": datetime.fromtimestamp(stat.st_atime).isoformat(),
            "is_readable": os.access(file_path, os.R_OK),
            "download_url": f"/api/logs/download/{log_type}"
        }
        
        if line_count is not None:
            file_info["estimated_lines"] = line_count
        
        return success_response(
            details=file_info,
            title=f"Log File Information - {log_type}",
            description=f"Detailed information about {filename}"
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting log file info for '{log_type}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get log file information"
        )


@router.get("/health", summary="Log system health check")
async def log_system_health():
    """
    Check the health of the logging system.
    
    Returns:
        Health status of the logging system
    """
    try:
        health_status = {
            "status": "healthy",
            "log_directory": str(LOG_DIR.absolute()),
            "log_directory_exists": LOG_DIR.exists(),
            "log_directory_writable": os.access(LOG_DIR, os.W_OK) if LOG_DIR.exists() else False,
            "available_log_types": list(AVAILABLE_LOGS.keys()),
            "log_files_status": {}
        }
        
        # Check each log file
        for log_type, filename in AVAILABLE_LOGS.items():
            file_path = LOG_DIR / filename
            health_status["log_files_status"][log_type] = {
                "filename": filename,
                "exists": file_path.exists(),
                "readable": os.access(file_path, os.R_OK) if file_path.exists() else False,
                "size_bytes": file_path.stat().st_size if file_path.exists() else 0
            }
        
        # Determine overall health
        if not health_status["log_directory_exists"]:
            health_status["status"] = "unhealthy"
            health_status["issues"] = ["Log directory does not exist"]
        elif not any(info["exists"] for info in health_status["log_files_status"].values()):
            health_status["status"] = "warning"
            health_status["issues"] = ["No log files found"]
        
        return success_response(
            details=health_status,
            title="Log System Health",
            description="Health status of the logging system"
        )
        
    except Exception as e:
        logger.error(f"Error checking log system health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check log system health"
        )
