"""
Kudosity Webhook - Follow-up Free Version
Clean implementation with no follow-up functionality, only immediate responses.
"""

import asyncio
import datetime as dt
from typing import Dict, Any, Optional
from uuid import UUID, uuid4

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import select, and_, desc
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import get_db
from app.core.logging import get_logger
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.schemas.webhook import KudosityWebhookPayload
from app.schemas.api_response import ApiResponse, Message
from app.services.kudosity_sms_service import KudositySMSService
from app.agents.conversation_agent import ConversationAgent
from app.utils.phone_utils import normalize_phone_number

router = APIRouter()
logger = get_logger(__name__)

class WebhookProcessor:
    """Processes webhook requests with robust error handling and no follow-ups"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.sms_service = KudositySMSService()
        self.conversation_agent = ConversationAgent()
        
    async def process_inbound_message(self, payload: KudosityWebhookPayload) -> Dict[str, Any]:
        """Process inbound SMS message with immediate response only"""
        
        result = {
            "success": False,
            "message_processed": False,
            "andy_response": "",
            "sms_sent": False,
            "conversation_stored": False,
            "lead_found": False,
            "processing_metadata": {},
            "followup_management": {
                "success": True,
                "action": "no_followups_system_disabled", 
                "message": "Follow-up system permanently disabled - immediate responses only"
            }
        }
        
        try:
            # Extract message details
            mo_data = payload.mo
            sender_phone = mo_data.sender
            recipient_phone = mo_data.recipient
            message_text = mo_data.message
            message_timestamp = mo_data.timestamp
            
            # Normalize phone number
            normalized_phone = normalize_phone_number(sender_phone)
            if not normalized_phone:
                raise ValueError(f"Invalid phone number format: {sender_phone}")
            
            logger.info(
                f"Processing inbound message",
                sender=normalized_phone,
                recipient=recipient_phone,
                message_length=len(message_text),
                timestamp=message_timestamp
            )
            
            # Find or create lead
            lead = await self._find_or_create_lead(normalized_phone)
            lead_id = str(lead.id) if lead else None
            
            result["lead_found"] = lead is not None
            result["processing_metadata"] = {
                "lead_id": lead_id,
                "normalized_phone": normalized_phone,
                "message_length": len(message_text),
                "processing_time": 0.0,
                "conversation_stage": "immediate_response_only"
            }
            
            # Generate immediate response using Andy
            start_time = dt.datetime.now()
            
            andy_response = await self._generate_andy_response(
                lead_id=lead_id,
                phone_number=normalized_phone,
                message=message_text
            )
            
            processing_time = (dt.datetime.now() - start_time).total_seconds()
            result["processing_metadata"]["processing_time"] = processing_time
            result["andy_response"] = andy_response
            
            # Send immediate SMS response
            if andy_response:
                sms_result = await self._send_sms_response(
                    phone_number=normalized_phone,
                    message=andy_response
                )
                result["sms_sent"] = sms_result.get("success", False)
            
            # Store conversation
            if lead_id:
                conversation_stored = await self._store_conversation(
                    lead_id=lead_id,
                    inbound_message=message_text,
                    outbound_message=andy_response,
                    phone_number=normalized_phone
                )
                result["conversation_stored"] = conversation_stored
            
            result["success"] = True
            result["message_processed"] = True
            
            logger.info(
                f"Message processed successfully",
                lead_id=lead_id,
                andy_response_length=len(andy_response),
                sms_sent=result["sms_sent"],
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(
                f"Error processing inbound message: {str(e)}",
                sender=payload.mo.sender,
                error_type=type(e).__name__
            )
            result["error"] = str(e)
            result["success"] = False
        
        return result
    
    async def _find_or_create_lead(self, phone_number: str) -> Optional[Lead]:
        """Find existing lead or create new one"""
        try:
            # Try to find existing lead
            query = select(Lead).where(
                and_(
                    Lead.mobile == phone_number,
                    Lead.is_active == True,
                    Lead.is_deleted == False
                )
            )
            
            result = await self.db.execute(query)
            lead = result.scalar_one_or_none()
            
            if lead:
                logger.info(f"Found existing lead: {lead.id}")
                return lead
            
            # Create new lead if not found
            new_lead = Lead(
                id=uuid4(),
                mobile=phone_number,
                first_name="Unknown",
                last_name="Lead",
                email=f"lead.{uuid4().hex[:8]}@unknown.com",
                is_active=True,
                is_deleted=False
            )
            
            self.db.add(new_lead)
            await self.db.commit()
            await self.db.refresh(new_lead)
            
            logger.info(f"Created new lead: {new_lead.id}")
            return new_lead
            
        except Exception as e:
            logger.error(f"Error finding/creating lead for {phone_number}: {e}")
            await self.db.rollback()
            return None
    
    async def _generate_andy_response(self, lead_id: str, phone_number: str, message: str) -> str:
        """Generate immediate Andy response"""
        try:
            if not lead_id:
                return "Hi! Thanks for your interest. How can I help you with franchise opportunities?"
            
            # Use conversation agent for contextual response
            response = await self.conversation_agent.process_message(
                lead_id=UUID(lead_id),
                message=message,
                phone_number=phone_number,
                db=self.db
            )
            
            return response.get("response", "Thanks for your message! How can I assist you today?")
            
        except Exception as e:
            logger.error(f"Error generating Andy response: {e}")
            return "Thanks for your message! I'll get back to you shortly."
    
    async def _send_sms_response(self, phone_number: str, message: str) -> Dict[str, Any]:
        """Send immediate SMS response"""
        try:
            result = await self.sms_service.send_sms(
                to_number=phone_number,
                message=message
            )
            
            logger.info(f"SMS sent to {phone_number}: {result.get('success', False)}")
            return result
            
        except Exception as e:
            logger.error(f"Error sending SMS to {phone_number}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _store_conversation(self, lead_id: str, inbound_message: str, 
                                outbound_message: str, phone_number: str) -> bool:
        """Store conversation messages"""
        try:
            # Store inbound message
            inbound_msg = ConversationMessage(
                id=uuid4(),
                lead_id=UUID(lead_id),
                sender="lead",
                message=inbound_message,
                phone_number=phone_number,
                is_active=True,
                is_deleted=False
            )
            
            self.db.add(inbound_msg)
            
            # Store outbound message if exists
            if outbound_message:
                outbound_msg = ConversationMessage(
                    id=uuid4(),
                    lead_id=UUID(lead_id),
                    sender="system",
                    message=outbound_message,
                    phone_number=phone_number,
                    is_active=True,
                    is_deleted=False
                )
                
                self.db.add(outbound_msg)
            
            await self.db.commit()
            logger.info(f"Conversation stored for lead {lead_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing conversation for lead {lead_id}: {e}")
            await self.db.rollback()
            return False


@router.post("/kudosity")
async def process_kudosity_webhook(
    payload: KudosityWebhookPayload,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> ApiResponse:
    """
    Process Kudosity webhook - FOLLOW-UP FREE VERSION
    
    This endpoint processes inbound SMS messages and sends immediate responses only.
    No follow-up messages are scheduled or sent.
    """
    
    try:
        logger.info(
            f"Received Kudosity webhook",
            event_type=payload.event_type,
            webhook_id=payload.webhook_id,
            sender=payload.mo.sender if payload.mo else None
        )
        
        # Validate event type
        if payload.event_type != "SMS_INBOUND":
            return ApiResponse(
                success=False,
                message=Message(
                    title="Invalid Event Type",
                    description=f"Unsupported event type: {payload.event_type}"
                ),
                data={"event_type": payload.event_type}
            )
        
        # Process the message
        processor = WebhookProcessor(db)
        result = await processor.process_inbound_message(payload)
        
        # Return response
        return ApiResponse(
            success=result["success"],
            message=Message(
                title="Message Processed" if result["success"] else "Processing Failed",
                description="Immediate response sent" if result["success"] else result.get("error", "Unknown error")
            ),
            data=result
        )
        
    except Exception as e:
        logger.error(f"Webhook processing failed: {str(e)}")
        
        return ApiResponse(
            success=False,
            message=Message(
                title="Webhook Processing Failed",
                description=str(e)
            ),
            data={"error": str(e)}
        )


@router.get("/health")
async def webhook_health_check():
    """Health check endpoint for webhook"""
    return {"status": "healthy", "followups": "disabled", "timestamp": dt.datetime.now().isoformat()}
