"""
DocQA API endpoints for document question answering
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, Path
from pydantic import BaseModel

from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.services.docqa_integration_service import get_docqa_integration_service, DocQAIntegrationService

router = APIRouter()


class QuestionRequest(BaseModel):
    """Request model for asking questions"""
    question: str
    top_k: Optional[int] = 6
    similarity_threshold: Optional[float] = 0.7
    include_metadata: Optional[bool] = False


class QuestionResponse(BaseModel):
    """Response model for question answers"""
    success: bool
    question: str
    answer: str
    document_id: Optional[str] = None
    document_name: Optional[str] = None
    franchisor_id: Optional[str] = None
    franchisor_name: Optional[str] = None
    processing_note: Optional[str] = None
    error: Optional[str] = None


@router.post("/ask", response_model=Dict[str, Any])
async def ask_general_question(
    request: QuestionRequest,
    current_user: dict = Depends(get_current_user),
    docqa_service: DocQAIntegrationService = Depends(get_docqa_integration_service)
):
    """
    Ask a general question about all documents and franchisors
    
    This endpoint searches across all ingested documents and franchisor brochures
    with priority given to franchisor information.
    """
    try:
        # Import DocQA ask_question function
        from docqa import ask_question
        
        answer = ask_question(
            question=request.question,
            top_k=request.top_k,
            similarity_threshold=request.similarity_threshold,
            include_metadata=request.include_metadata
        )
        
        return create_success_response(
            data={
                "success": True,
                "question": request.question,
                "answer": answer,
                "processing_note": "Answer based on all available documents and franchisor information"
            },
            message_title="Question Answered",
            message_description="Successfully processed your question",
            status_code=200
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.PROCESSING_ERROR,
            message_title="Question Processing Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/ask/document/{document_id}", response_model=Dict[str, Any])
async def ask_document_question(
    document_id: str = Path(..., description="Document ID to query"),
    request: QuestionRequest = ...,
    current_user: dict = Depends(get_current_user),
    docqa_service: DocQAIntegrationService = Depends(get_docqa_integration_service)
):
    """
    Ask a question about a specific document
    
    This endpoint allows you to test DocQA functionality with a specific document
    by providing the document table record ID.
    """
    try:
        result = await docqa_service.ask_question_from_document(
            document_id=document_id,
            question=request.question,
            similarity_threshold=request.similarity_threshold
        )
        
        if result["success"]:
            return create_success_response(
                data=result,
                message_title="Document Question Answered",
                message_description=f"Successfully answered question about document {document_id}",
                status_code=200
            )
        else:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Question Failed",
                message_description=result.get("error", "Unknown error"),
                status_code=404 if "not found" in result.get("error", "").lower() else 500
            )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.PROCESSING_ERROR,
            message_title="Document Question Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/ask/franchisor/{franchisor_id}", response_model=Dict[str, Any])
async def ask_franchisor_question(
    franchisor_id: str = Path(..., description="Franchisor ID to query"),
    request: QuestionRequest = ...,
    current_user: dict = Depends(get_current_user),
    docqa_service: DocQAIntegrationService = Depends(get_docqa_integration_service)
):
    """
    Ask a question about a specific franchisor
    
    This endpoint allows you to test DocQA functionality with a specific franchisor
    by providing the franchisor table record ID.
    """
    try:
        result = await docqa_service.ask_question_from_franchisor(
            franchisor_id=franchisor_id,
            question=request.question
        )
        
        if result["success"]:
            return create_success_response(
                data=result,
                message_title="Franchisor Question Answered",
                message_description=f"Successfully answered question about franchisor {franchisor_id}",
                status_code=200
            )
        else:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Question Failed",
                message_description=result.get("error", "Unknown error"),
                status_code=404 if "not found" in result.get("error", "").lower() else 500
            )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.PROCESSING_ERROR,
            message_title="Franchisor Question Failed",
            message_description=str(e),
            status_code=500
        )


@router.get("/health", response_model=Dict[str, Any])
async def docqa_health_check(
    current_user: dict = Depends(get_current_user)
):
    """
    Check DocQA system health and status
    """
    try:
        from docqa.serve import health_check
        
        health = health_check()
        
        return create_success_response(
            data=health,
            message_title="DocQA Health Check",
            message_description=f"System status: {health['status']}",
            status_code=200
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.SYSTEM_ERROR,
            message_title="Health Check Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/process/document/{document_id}", response_model=Dict[str, Any])
async def manually_process_document(
    document_id: str = Path(..., description="Document ID to process"),
    current_user: dict = Depends(get_current_user),
    docqa_service: DocQAIntegrationService = Depends(get_docqa_integration_service)
):
    """
    Manually trigger DocQA processing for a specific document
    
    This endpoint allows you to manually process a document that may have
    failed automatic processing or was uploaded before DocQA integration.
    """
    try:
        # Get document info first
        from app.core.database.connection import get_db
        from app.models.document import Document

        async for session in get_db():
            document = await session.get(Document, document_id)
            if not document:
                return create_error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    message_title="Document Not Found",
                    message_description=f"Document {document_id} not found",
                    status_code=404
                )
            
            if not document.file_path:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="No File Path",
                    message_description="Document has no file path to process",
                    status_code=400
                )
        
        # Process the document
        result = await docqa_service.process_document(
            document_id=document_id,
            file_url=document.file_path
        )
        
        if result and result.success:
            return create_success_response(
                data={
                    "success": True,
                    "document_id": document_id,
                    "chunks_created": result.chunks_created,
                    "table_name": result.table_name,
                    "processing_time": result.processing_time
                },
                message_title="Document Processed",
                message_description=f"Successfully processed document {document_id}",
                status_code=200
            )
        else:
            error_msg = result.error_message if result else "Processing failed"
            return create_error_response(
                error_code=ErrorCodes.PROCESSING_ERROR,
                message_title="Processing Failed",
                message_description=error_msg,
                status_code=500
            )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.PROCESSING_ERROR,
            message_title="Manual Processing Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/process/franchisor/{franchisor_id}", response_model=Dict[str, Any])
async def manually_process_franchisor(
    franchisor_id: str = Path(..., description="Franchisor ID to process"),
    current_user: dict = Depends(get_current_user),
    docqa_service: DocQAIntegrationService = Depends(get_docqa_integration_service)
):
    """
    Manually trigger DocQA processing for a specific franchisor brochure
    """
    try:
        # Get franchisor info first
        from app.core.database.connection import get_db
        from app.models.franchisor import Franchisor

        async for session in get_db():
            franchisor = await session.get(Franchisor, franchisor_id)
            if not franchisor:
                return create_error_response(
                    error_code=ErrorCodes.NOT_FOUND,
                    message_title="Franchisor Not Found",
                    message_description=f"Franchisor {franchisor_id} not found",
                    status_code=404
                )
            
            if not franchisor.brochure_url:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="No Brochure URL",
                    message_description="Franchisor has no brochure URL to process",
                    status_code=400
                )
        
        # Process the franchisor brochure
        result = await docqa_service.process_franchisor_brochure(
            franchisor_id=franchisor_id,
            brochure_url=franchisor.brochure_url
        )
        
        if result and result.success:
            return create_success_response(
                data={
                    "success": True,
                    "franchisor_id": franchisor_id,
                    "chunks_created": result.chunks_created,
                    "table_name": result.table_name,
                    "processing_time": result.processing_time
                },
                message_title="Franchisor Processed",
                message_description=f"Successfully processed franchisor {franchisor_id}",
                status_code=200
            )
        else:
            error_msg = result.error_message if result else "Processing failed"
            return create_error_response(
                error_code=ErrorCodes.PROCESSING_ERROR,
                message_title="Processing Failed",
                message_description=error_msg,
                status_code=500
            )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.PROCESSING_ERROR,
            message_title="Manual Processing Failed",
            message_description=str(e),
            status_code=500
        )
