"""
Dashboard API endpoints
Provides dashboard data including counts and recent activity
"""

import logging
from datetime import datetime
from typing import Annotated, Optional
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.schemas.user import UserBase
from app.schemas.dashboard import (
    DashboardCountsSuccessResponse,
    RecentActivitySuccessResponse
)
from app.schemas.analytics import DashboardAnalyticsSuccessResponse
from app.services.dashboard_service import DashboardService
from app.services.analytics_service import AnalyticsService
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.utils.exception_manager.custom_exceptions import (
    DatabaseError,
    ValidationError
)
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

router = APIRouter()

# Common response examples for documentation
COMMON_RESPONSES = {
    400: {
        "description": "Bad Request",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Bad Request",
                        "description": "Invalid request parameters"
                    },
                    "data": {},
                    "error_code": 4000
                }
            }
        }
    },
    401: {
        "description": "Unauthorized",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Unauthorized",
                        "description": "Authentication required"
                    },
                    "data": {},
                    "error_code": 2001
                }
            }
        }
    },
    500: {
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": {
                        "title": "Internal Server Error",
                        "description": "An unexpected error occurred"
                    },
                    "data": {},
                    "error_code": 1000
                }
            }
        }
    }
}


def get_dashboard_service(db: AsyncSession = Depends(get_db)) -> DashboardService:
    """Dependency to get dashboard service"""
    return DashboardService(db)


def get_analytics_service(db: AsyncSession = Depends(get_db)) -> AnalyticsService:
    """Dependency to get analytics service"""
    return AnalyticsService(db)


@router.get(
    "/counts",
    response_model=DashboardCountsSuccessResponse,
    summary="Get Dashboard Counts (DEPRECATED)",
    description="⚠️ DEPRECATED: Use /analytics endpoint instead. Get total counts for leads, franchisors, SMS, and meetings",
    deprecated=True,
    responses={
        200: {
            "description": "Dashboard counts retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Dashboard Counts Retrieved",
                            "description": "Dashboard counts retrieved successfully"
                        },
                        "data": {
                            "total_leads": 150,
                            "total_franchisors": 45,
                            "total_sms": 0,
                            "total_meetings": 0
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_dashboard_counts(
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """
    Get dashboard counts including total leads, franchisors, SMS, and meetings.
    
    This endpoint provides summary statistics for the dashboard:
    - Total leads count (from leads table)
    - Total franchisors count (from franchisors table)
    - Total SMS count (static 0 for now)
    - Total meetings count (static 0 for now)
    
    Requires authentication.
    """
    try:
        logger.warning("DEPRECATED: /counts endpoint called. Use /analytics endpoint instead.")
        logger.info("Getting dashboard counts")
        
        # Get dashboard counts
        counts = await dashboard_service.get_dashboard_counts()
        
        return create_success_response(
            data=counts.model_dump(),
            message_title="Dashboard Counts Retrieved",
            message_description="Dashboard counts retrieved successfully"
        )
        
    except DatabaseError as de:
        logger.error(f"Database error getting dashboard counts: {de}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description=str(de),
            status_code=500
        )
        
    except SQLAlchemyError as db_error:
        logger.error(f"Database error getting dashboard counts: {db_error}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description="Failed to retrieve dashboard counts due to database error",
            status_code=500
        )
        
    except Exception as e:
        logger.error(f"Unexpected error getting dashboard counts: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Internal Server Error",
            message_description="An unexpected error occurred while retrieving dashboard counts",
            status_code=500
        )


@router.get(
    "/recent-activity",
    response_model=RecentActivitySuccessResponse,
    summary="Get Recent Activity (DEPRECATED)",
    description="⚠️ DEPRECATED: Use /analytics endpoint instead. Get recent activity including latest lead, franchisor, question, and exception",
    deprecated=True,
    responses={
        200: {
            "description": "Recent activity retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Recent Activity Retrieved",
                            "description": "Recent activity retrieved successfully"
                        },
                        "data": {
                            "latest_lead": {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "full_name": "John Doe",
                                "email": "<EMAIL>",
                                "phone": "+1234567890",
                                "status": "new",
                                "created_at": "2025-07-15T10:30:00Z"
                            },
                            "latest_franchisor": {
                                "id": "123e4567-e89b-12d3-a456-426614174001",
                                "name": "Coffee Club Melbourne",
                                "contactFirstName": "Jane",
                                "contactLastName": "Smith",
                                "email": "<EMAIL>",
                                "region": "Melbourne",
                                "is_active": True,
                                "created_at": "2025-07-15T10:25:00Z"
                            },
                            "latest_question": {
                                "id": "123e4567-e89b-12d3-a456-426614174002",
                                "question_text": "What is the initial investment required?",
                                "category": "Investment",
                                "is_active": True,
                                "created_at": "2025-07-15T10:20:00Z"
                            },
                            "latest_exception": {
                                "id": "123e4567-e89b-12d3-a456-426614174003",
                                "error_type": "ValidationError",
                                "error_message": "Sample exception for dashboard display",
                                "endpoint": "/api/sample/",
                                "user_id": None,
                                "severity": "info",
                                "created_at": "2025-07-15T10:15:00Z"
                            }
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_recent_activity(
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """
    Get recent activity including latest records from different modules.
    
    This endpoint provides recent activity data:
    - Latest lead (most recently created lead)
    - Latest franchisor (most recently created franchisor)
    - Latest question (most recently created question)
    - Latest exception (mock data for now)
    
    Requires authentication.
    """
    try:
        logger.warning("DEPRECATED: /recent-activity endpoint called. Use /analytics endpoint instead.")
        logger.info("Getting recent activity")
        
        # Get recent activity
        activity = await dashboard_service.get_recent_activity()
        
        return create_success_response(
            data=activity.model_dump(),
            message_title="Recent Activity Retrieved",
            message_description="Recent activity retrieved successfully"
        )
        
    except DatabaseError as de:
        logger.error(f"Database error getting recent activity: {de}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description=str(de),
            status_code=500
        )
        
    except SQLAlchemyError as db_error:
        logger.error(f"Database error getting recent activity: {db_error}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description="Failed to retrieve recent activity due to database error",
            status_code=500
        )
        
    except Exception as e:
        logger.error(f"Unexpected error getting recent activity: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Internal Server Error",
            message_description="An unexpected error occurred while retrieving recent activity",
            status_code=500
        )


@router.get(
    "/analytics",
    response_model=DashboardAnalyticsSuccessResponse,
    summary="Get Dashboard Analytics",
    description="Get comprehensive dashboard analytics including counts, recent activity, and question analytics with filtering",
    responses={
        200: {
            "description": "Dashboard analytics retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Dashboard Analytics Retrieved",
                            "description": "Dashboard analytics data retrieved successfully"
                        },
                        "data": {
                            "counts": {
                                "total_leads": 170,
                                "total_franchisors": 24,
                                "total_questions": 89,
                                "total_meetings": 0,
                                "leads_change_percent": 5.2,
                                "franchisors_change_percent": -1.2,
                                "questions_change_percent": 15.6,
                                "meetings_change_percent": 0.0,
                                "avg_questions_per_day": 3.4,
                                "avg_escalations_per_day": 1.2
                            },
                            "recent_activity": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440000",
                                    "type": "lead",
                                    "title": "New Lead: John Doe",
                                    "description": "Lead created for McDonald's franchise",
                                    "timestamp": "2024-05-07T10:30:00Z",
                                    "metadata": {"status": "new"}
                                }
                            ],
                            "chart_data": [
                                {
                                    "date": "May 1",
                                    "question_count": 24,
                                    "escalation_count": 3,
                                    "escalation_rate": 12.5
                                }
                            ],
                            "detailed_analytics": [
                                {
                                    "date": "May 1",
                                    "question_count": 24,
                                    "escalation_count": 3,
                                    "escalation_rate": 12.5
                                }
                            ],
                            "applied_filters": {
                                "date_range": "last_7_days",
                                "time_period": "day"
                            },
                            "available_filters": {
                                "date_ranges": ["last_7_days", "last_30_days", "custom"],
                                "time_periods": ["day", "week", "month"]
                            },
                            "period_label": "Last 7 days",
                            "generated_at": "2024-05-07T10:30:00Z"
                        },
                        "error_code": 0
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_dashboard_analytics(
    date_range: str = Query("last_7_days", description="Date range filter: last_7_days, last_30_days, last_3_months, last_6_months, last_year, custom"),
    time_period: str = Query("day", description="Time period grouping: day, week, month, year"),
    custom_start: Optional[str] = Query(None, description="Custom start date (YYYY-MM-DD) - required if date_range=custom"),
    custom_end: Optional[str] = Query(None, description="Custom end date (YYYY-MM-DD) - required if date_range=custom"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get comprehensive dashboard analytics data.

    This endpoint provides:
    - Dashboard counts (leads, franchisors, questions, meetings) with percentage changes
    - Recent activity from all modules
    - Chart data for visualization (question counts, escalation rates)
    - Detailed analytics table data
    - Filter options and applied filters

    Question analytics explanation:
    - Questions in question_bank = AI successfully answered user questions
    - Questions in escalation_question_bank = AI failed to answer, escalated to support
    - Total questions = question_bank + escalation_question_bank
    - Escalation rate = (escalation_count / total_questions) * 100

    Requires authentication.
    """
    try:
        logger.info(f"Getting dashboard analytics with filters: date_range={date_range}, time_period={time_period}")

        # Parse custom dates if provided
        custom_start_date = None
        custom_end_date = None

        if date_range == "custom":
            if not custom_start or not custom_end:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Validation Error",
                    message_description="custom_start and custom_end are required when date_range=custom",
                    status_code=400
                )

            try:
                custom_start_date = datetime.fromisoformat(custom_start)
                custom_end_date = datetime.fromisoformat(custom_end)
            except ValueError:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Validation Error",
                    message_description="Invalid date format. Use YYYY-MM-DD format",
                    status_code=400
                )

        # Get analytics data
        analytics = await analytics_service.get_dashboard_analytics(
            date_range=date_range,
            time_period=time_period,
            custom_start=custom_start_date,
            custom_end=custom_end_date
        )

        return create_success_response(
            data=analytics.model_dump(),
            message_title="Dashboard Analytics Retrieved",
            message_description="Dashboard analytics data retrieved successfully"
        )

    except DatabaseError as de:
        logger.error(f"Database error getting dashboard analytics: {de}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Database Error",
            message_description=str(de),
            status_code=500
        )

    except ValidationError as ve:
        logger.error(f"Validation error getting dashboard analytics: {ve}")
        return create_error_response(
            error_code=ErrorCodes.VALIDATION_ERROR,
            message_title="Validation Error",
            message_description=str(ve),
            status_code=400
        )

    except Exception as e:
        logger.error(f"Unexpected error getting dashboard analytics: {e}", exc_info=True)
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Internal Server Error",
            message_description="An unexpected error occurred while retrieving dashboard analytics",
            status_code=500
        )
