"""
Conversation Message API Endpoints
REST API endpoints for conversation message management with AI agent processing
"""

from typing import Dict, Any, Optional, List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import get_db
from app.services.conversation_message_service import ConversationMessageService
from app.core.factory import get_conversation_message_service
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.api_standards import APIStandards
from app.schemas.base_response import StandardResponse
from app.schemas.conversation_message import (
    ConversationMessageCreateRequest,
    ConversationMessageUpdateRequest,
    ConversationMessageResponse,
    ConversationMessageListResponse,
    ConversationMessageBulkCreateRequest,
    ConversationMessageBulkCreateResponse,
    ConversationParseRequest,
    ConversationParseResponse,
    ConversationStatsResponse,
    ConversationMessageSuccessResponse,
    ConversationMessageListSuccessResponse,
    ConversationMessageBulkSuccessResponse,
    ConversationParseSuccessResponse,
    ConversationStatsSuccessResponse
)
from app.core.logging import logger
from app.core.responses.models import ErrorCodes

router = APIRouter()


@router.post(
    "/conversation-messages",
    response_model=StandardResponse[ConversationMessageResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Create Conversation Message",
    description="Create a new conversation message between a lead and the system",
)
async def create_conversation_message(
    message_data: ConversationMessageCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationMessageResponse]:
    """Create a new conversation message"""
    try:
        logger.info(f"User {current_user.get('user_id')} creating conversation message for lead {message_data.lead_id}")
        
        created_message = await service.create_message(message_data)
        
        return APIStandards.create_success_response(
            data=created_message,
            message="Conversation message created successfully",
            title="Message Created"
        )
        
    except Exception as e:
        logger.error(f"Error creating conversation message: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Message Creation Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.get(
    "/conversation-messages/{message_id}",
    response_model=StandardResponse[ConversationMessageResponse],
    summary="Get Conversation Message",
    description="Get a specific conversation message by ID",
)
async def get_conversation_message(
    message_id: UUID = Path(..., description="Conversation message ID"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationMessageResponse]:
    """Get a conversation message by ID"""
    try:
        message = await service.get_message_by_id(message_id)
        
        if not message:
            return APIStandards.create_error_response(
                error_message="Conversation message not found",
                error_title="Message Not Found",
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCodes.RESOURCE_NOT_FOUND
            )
        
        return APIStandards.create_success_response(
            data=message,
            message="Conversation message retrieved successfully",
            title="Message Retrieved"
        )
        
    except Exception as e:
        logger.error(f"Error getting conversation message {message_id}: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Message Retrieval Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.get(
    "/conversation-messages/lead/{lead_id}",
    response_model=StandardResponse[ConversationMessageListResponse],
    summary="Get Messages by Lead ID",
    description="Get all conversation messages for a specific lead with pagination",
)
async def get_messages_by_lead_id(
    lead_id: UUID = Path(..., description="Lead ID"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    order: str = Query("asc", regex="^(asc|desc)$", description="Order by created_at (asc or desc)"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationMessageListResponse]:
    """Get conversation messages for a specific lead"""
    try:
        skip = (page - 1) * per_page
        
        messages, total_count = await service.get_messages_by_lead_id(
            lead_id=lead_id,
            skip=skip,
            limit=per_page,
            order_by_created_at=order
        )
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page

        response_data = ConversationMessageListResponse(
            items=messages,
            pagination={
                "current_page": page,
                "items_per_page": per_page,
                "total_items": total_count,
                "total_pages": total_pages
            }
        )
        
        return APIStandards.create_success_response(
            data=response_data,
            message=f"Retrieved {len(messages)} conversation messages for lead",
            title="Messages Retrieved"
        )
        
    except Exception as e:
        logger.error(f"Error getting messages for lead {lead_id}: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Message Retrieval Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.get(
    "/conversation-messages/conversation/{lead_id}/{franchisor_id}",
    response_model=StandardResponse[ConversationMessageListResponse],
    summary="Get Conversation Between Lead and Franchisor",
    description="Get conversation messages between a specific lead and franchisor",
)
async def get_conversation_between_lead_and_franchisor(
    lead_id: UUID = Path(..., description="Lead ID"),
    franchisor_id: UUID = Path(..., description="Franchisor ID"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    order: str = Query("asc", regex="^(asc|desc)$", description="Order by created_at (asc or desc)"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationMessageListResponse]:
    """Get conversation between a specific lead and franchisor"""
    try:
        skip = (page - 1) * per_page
        
        messages, total_count = await service.get_conversation_between_lead_and_franchisor(
            lead_id=lead_id,
            franchisor_id=franchisor_id,
            skip=skip,
            limit=per_page,
            order_by_created_at=order
        )
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page

        response_data = ConversationMessageListResponse(
            items=messages,
            pagination={
                "current_page": page,
                "items_per_page": per_page,
                "total_items": total_count,
                "total_pages": total_pages
            }
        )
        
        return APIStandards.create_success_response(
            data=response_data,
            message=f"Retrieved {len(messages)} conversation messages",
            title="Conversation Retrieved"
        )
        
    except Exception as e:
        logger.error(f"Error getting conversation between lead {lead_id} and franchisor {franchisor_id}: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Conversation Retrieval Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.put(
    "/conversation-messages/{message_id}",
    response_model=StandardResponse[ConversationMessageResponse],
    summary="Update Conversation Message",
    description="Update a conversation message",
)
async def update_conversation_message(
    message_id: UUID = Path(..., description="Conversation message ID"),
    update_data: ConversationMessageUpdateRequest = ...,
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationMessageResponse]:
    """Update a conversation message"""
    try:
        updated_message = await service.update_message(message_id, update_data)
        
        if not updated_message:
            return APIStandards.create_error_response(
                error_message="Conversation message not found",
                error_title="Message Not Found",
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCodes.RESOURCE_NOT_FOUND
            )
        
        return APIStandards.create_success_response(
            data=updated_message,
            message="Conversation message updated successfully",
            title="Message Updated"
        )
        
    except Exception as e:
        logger.error(f"Error updating conversation message {message_id}: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Message Update Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.delete(
    "/conversation-messages/{message_id}",
    response_model=StandardResponse[Dict[str, str]],
    summary="Delete Conversation Message",
    description="Soft delete a conversation message",
)
async def delete_conversation_message(
    message_id: UUID = Path(..., description="Conversation message ID"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[Dict[str, str]]:
    """Soft delete a conversation message"""
    try:
        deleted = await service.soft_delete_message(message_id)
        
        if not deleted:
            return APIStandards.create_error_response(
                error_message="Conversation message not found",
                error_title="Message Not Found",
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=ErrorCodes.RESOURCE_NOT_FOUND
            )
        
        return APIStandards.create_success_response(
            data={"message": "Conversation message deleted successfully"},
            message="Message deleted successfully",
            title="Message Deleted"
        )
        
    except Exception as e:
        logger.error(f"Error deleting conversation message {message_id}: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Message Deletion Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.post(
    "/conversation-messages/bulk",
    response_model=StandardResponse[ConversationMessageBulkCreateResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Create Bulk Conversation Messages",
    description="Create multiple conversation messages in bulk",
)
async def create_bulk_conversation_messages(
    bulk_data: ConversationMessageBulkCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationMessageBulkCreateResponse]:
    """Create multiple conversation messages in bulk"""
    try:
        logger.info(f"User {current_user.get('user_id')} creating {len(bulk_data.messages)} conversation messages in bulk")

        created_messages = await service.create_bulk_messages(bulk_data)

        response_data = ConversationMessageBulkCreateResponse(
            created_messages=created_messages,
            total_created=len(created_messages)
        )

        return APIStandards.create_success_response(
            data=response_data,
            message=f"Successfully created {len(created_messages)} conversation messages",
            title="Bulk Messages Created"
        )

    except Exception as e:
        logger.error(f"Error creating bulk conversation messages: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Bulk Message Creation Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.post(
    "/conversation-messages/parse-and-store",
    response_model=StandardResponse[ConversationParseResponse],
    status_code=status.HTTP_201_CREATED,
    summary="AI Agent: Parse and Store Conversation",
    description="AI agent endpoint to parse conversation data from webhooks and store messages",
)
async def parse_and_store_conversation(
    parse_request: ConversationParseRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationParseResponse]:
    """AI agent endpoint to parse and store conversation data"""
    try:
        logger.info(f"AI Agent processing conversation with {len(parse_request.conversation_data)} messages")

        parse_response = await service.parse_and_store_conversation(parse_request)

        return APIStandards.create_success_response(
            data=parse_response,
            message=f"Successfully parsed and stored {parse_response.total_parsed} conversation messages",
            title="Conversation Processed"
        )

    except Exception as e:
        logger.error(f"Error in AI agent conversation processing: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Conversation Processing Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.get(
    "/conversation-messages/stats",
    response_model=StandardResponse[ConversationStatsResponse],
    summary="Get Conversation Statistics",
    description="Get conversation statistics (optionally filtered by lead)",
)
async def get_conversation_stats(
    lead_id: Optional[UUID] = Query(None, description="Optional lead ID to filter stats"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    service: ConversationMessageService = Depends(get_conversation_message_service)
) -> StandardResponse[ConversationStatsResponse]:
    """Get conversation statistics"""
    try:
        stats = await service.get_conversation_stats(lead_id)

        response_data = ConversationStatsResponse(
            total_messages=stats["total_messages"],
            lead_messages=stats["lead_messages"],
            system_messages=stats["system_messages"],
            messages_by_sender=stats["messages_by_sender"]
        )

        return APIStandards.create_success_response(
            data=response_data,
            message="Conversation statistics retrieved successfully",
            title="Statistics Retrieved"
        )

    except Exception as e:
        logger.error(f"Error getting conversation stats: {e}")
        return APIStandards.create_error_response(
            error_message=str(e),
            error_title="Statistics Retrieval Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
