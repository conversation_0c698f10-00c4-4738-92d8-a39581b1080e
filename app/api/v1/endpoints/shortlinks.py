"""
Short Link API Endpoints
Handles short link creation and redirection
"""

from fastapi import <PERSON><PERSON><PERSON>er, Depends, HTTPException, Request, Response
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from app.core.database.connection import get_db
from app.services.shortener import shortener_service
from app.core.logging import logger
from app.core.config.settings import settings

router = APIRouter()


@router.get("/r/{slug}")
async def redirect_short_link(
    slug: str,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> RedirectResponse:
    """
    Redirect short link to long URL
    
    Args:
        slug: The short link slug
        request: FastAPI request object
        db: Database session
        
    Returns:
        RedirectResponse: 302 redirect to long URL or 404 if not found/expired
    """
    try:
        # Log the redirect attempt
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        logger.info(f"Short link redirect attempt: {slug} from IP: {client_ip}")
        
        # Resolve the short link
        long_url = await shortener_service.resolve_short_link(db, slug)
        
        if not long_url:
            logger.warning(f"Short link not found or expired: {slug}")
            raise HTTPException(status_code=404, detail="Short link not found or expired")
        
        # Log successful redirect
        logger.info(f"Short link redirect successful: {slug} -> {long_url}")
        
        # Create redirect response
        return RedirectResponse(url=long_url, status_code=302)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error redirecting short link {slug}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/create")
async def create_short_link_endpoint(
    long_url: str,
    context: Optional[Dict[str, Any]] = None,
    expires_in_days: Optional[int] = None,
    custom_slug: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new short link
    
    Args:
        long_url: The URL to shorten
        context: Optional context metadata
        expires_in_days: Optional expiration in days from now
        custom_slug: Optional custom slug
        db: Database session
        
    Returns:
        Dict containing the short URL and metadata
    """
    try:
        # Calculate expiration date if provided
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # Create the short link
        short_url = await shortener_service.create_short_link(
            db=db,
            long_url=long_url,
            context=context,
            expires_at=expires_at,
            custom_slug=custom_slug
        )
        
        # Extract slug from short URL
        slug = short_url.split("/r/")[-1]
        
        return {
            "short_url": short_url,
            "long_url": long_url,
            "slug": slug,
            "expires_at": expires_at.isoformat() if expires_at else None,
            "context": context or {}
        }
        
    except ValueError as e:
        logger.warning(f"Invalid request for short link creation: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating short link: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/info/{slug}")
async def get_short_link_info(
    slug: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get information about a short link
    
    Args:
        slug: The short link slug
        db: Database session
        
    Returns:
        Dict containing short link information
    """
    try:
        short_link = await shortener_service.get_short_link(db, slug)
        
        if not short_link:
            raise HTTPException(status_code=404, detail="Short link not found")
        
        return {
            "slug": short_link.slug,
            "long_url": short_link.long_url,
            "context": short_link.context_json,
            "expires_at": short_link.expires_at.isoformat() if short_link.expires_at else None,
            "created_at": short_link.created_at.isoformat(),
            "updated_at": short_link.updated_at.isoformat(),
            "is_expired": short_link.is_expired()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting short link info for {slug}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{slug}")
async def delete_short_link(
    slug: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Delete a short link
    
    Args:
        slug: The short link slug
        db: Database session
        
    Returns:
        Dict confirming deletion
    """
    try:
        success = await shortener_service.delete_short_link(db, slug)
        
        if not success:
            raise HTTPException(status_code=404, detail="Short link not found")
        
        return {"message": f"Short link {slug} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting short link {slug}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/cleanup")
async def cleanup_expired_links(
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Clean up expired short links
    
    Args:
        db: Database session
        
    Returns:
        Dict with cleanup results
    """
    try:
        count = await shortener_service.cleanup_expired_links(db)
        
        return {
            "message": f"Cleaned up {count} expired short links",
            "deleted_count": count
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up expired links: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
