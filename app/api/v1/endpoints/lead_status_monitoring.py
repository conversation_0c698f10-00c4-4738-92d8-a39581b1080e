"""
Lead Status Monitoring API Endpoints
Endpoints for managing automatic lead status updates based on conversation analysis
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from pydantic import BaseModel, Field

# Authentication removed - not needed for this endpoint
from app.tasks.lead_status_tasks import (
    monitor_lead_status,
    bulk_monitor_lead_statuses,
    auto_monitor_active_conversations,
    get_lead_status_insights
)
from app.agents.lead_status_agent import get_lead_status_agent
from app.schemas.base_response import ResponseMessage

router = APIRouter()


class LeadStatusMonitorRequest(BaseModel):
    """Request schema for monitoring lead status"""
    phone_number: str = Field(..., description="Phone number to monitor", example="+61423456789")
    force_update: bool = Field(False, description="Force status update regardless of confidence")


class BulkLeadStatusMonitorRequest(BaseModel):
    """Request schema for bulk monitoring lead statuses"""
    phone_numbers: List[str] = Field(..., description="List of phone numbers to monitor", min_items=1, max_items=100)


class LeadStatusMonitorResponse(BaseModel):
    """Response schema for lead status monitoring"""
    success: bool
    message: ResponseMessage
    data: Dict[str, Any]


@router.post(
    "/monitor",
    response_model=LeadStatusMonitorResponse,
    summary="Monitor Lead Status",
    description="Monitor a single lead's conversation and update status based on analysis"
)
async def monitor_single_lead_status(
    request: LeadStatusMonitorRequest
):
    """
    Monitor a single lead's conversation and automatically update their status
    based on conversation analysis and engagement patterns.
    
    The system analyzes:
    - Message content and sentiment
    - Response patterns and engagement level
    - Conversation stage and progression
    - Key indicators for status classification
    
    Status updates are made when confidence level is >= 70% unless force_update is True.
    """
    try:
        # Trigger monitoring task
        task = monitor_lead_status.delay(
            phone_number=request.phone_number,
            force_update=request.force_update
        )
        
        return LeadStatusMonitorResponse(
            success=True,
            message=ResponseMessage(
                title="Lead Status Monitoring Started",
                description=f"Monitoring task initiated for {request.phone_number}"
            ),
            data={
                "task_id": task.id,
                "phone_number": request.phone_number,
                "force_update": request.force_update,
                "status": "processing"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start lead status monitoring: {str(e)}"
        )


@router.post(
    "/monitor/bulk",
    response_model=LeadStatusMonitorResponse,
    summary="Bulk Monitor Lead Statuses",
    description="Monitor multiple leads' conversations and update statuses"
)
async def monitor_bulk_lead_statuses(
    request: BulkLeadStatusMonitorRequest
):
    """
    Monitor multiple leads' conversations simultaneously and update their statuses
    based on conversation analysis.
    
    This endpoint is useful for:
    - Batch processing of lead status updates
    - Regular maintenance of lead database
    - Bulk analysis of conversation patterns
    
    Maximum 100 phone numbers per request to prevent system overload.
    """
    try:
        # Trigger bulk monitoring task
        task = bulk_monitor_lead_statuses.delay(request.phone_numbers)
        
        return LeadStatusMonitorResponse(
            success=True,
            message=ResponseMessage(
                title="Bulk Lead Status Monitoring Started",
                description=f"Bulk monitoring task initiated for {len(request.phone_numbers)} leads"
            ),
            data={
                "task_id": task.id,
                "total_leads": len(request.phone_numbers),
                "phone_numbers": request.phone_numbers[:5],  # Show first 5 for reference
                "status": "processing"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start bulk lead status monitoring: {str(e)}"
        )


@router.post(
    "/monitor/auto",
    response_model=LeadStatusMonitorResponse,
    summary="Auto Monitor Active Conversations",
    description="Automatically monitor all active conversations from recent hours"
)
async def auto_monitor_conversations(
    hours_back: int = Query(24, description="Hours to look back for active conversations", ge=1, le=168)
):
    """
    Automatically monitor all leads with conversation activity in the specified time period.
    
    This endpoint:
    - Finds all leads with recent conversation activity
    - Triggers status monitoring for each active conversation
    - Useful for scheduled/automated status maintenance
    
    Default looks back 24 hours, maximum 168 hours (1 week).
    """
    try:
        # Trigger automatic monitoring task
        task = auto_monitor_active_conversations.delay(hours_back=hours_back)
        
        return LeadStatusMonitorResponse(
            success=True,
            message=ResponseMessage(
                title="Automatic Lead Status Monitoring Started",
                description=f"Automatic monitoring initiated for conversations from last {hours_back} hours"
            ),
            data={
                "task_id": task.id,
                "hours_back": hours_back,
                "status": "processing"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start automatic lead status monitoring: {str(e)}"
        )


@router.get(
    "/insights/{phone_number}",
    response_model=LeadStatusMonitorResponse,
    summary="Get Lead Status Insights",
    description="Get detailed conversation analysis and status insights for a lead"
)
async def get_lead_insights(
    phone_number: str = Path(..., description="Phone number to analyze", example="+61423456789")
):
    """
    Get detailed insights about a lead's conversation for analysis and debugging.
    
    Returns:
    - Conversation analysis and suggested status
    - Confidence levels and reasoning
    - Engagement metrics and conversation stage
    - Message counts and activity patterns
    - Lead context information
    
    Useful for understanding why certain status updates were made.
    """
    try:
        # Trigger insights task
        task = get_lead_status_insights.delay(phone_number)
        
        return LeadStatusMonitorResponse(
            success=True,
            message=ResponseMessage(
                title="Lead Status Insights Request Started",
                description=f"Generating insights for {phone_number}"
            ),
            data={
                "task_id": task.id,
                "phone_number": phone_number,
                "status": "processing"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get lead status insights: {str(e)}"
        )


@router.get(
    "/analyze/{phone_number}",
    response_model=LeadStatusMonitorResponse,
    summary="Analyze Conversation (Sync)",
    description="Synchronously analyze a conversation and return status recommendation"
)
async def analyze_conversation_sync(
    phone_number: str = Path(..., description="Phone number to analyze", example="+61423456789")
):
    """
    Synchronously analyze a lead's conversation and return status recommendation.
    
    This endpoint provides immediate analysis without triggering background tasks.
    Useful for:
    - Real-time status recommendations
    - Testing conversation analysis logic
    - Quick status checks
    
    Note: This is a synchronous operation and may take a few seconds to complete.
    """
    try:
        # Get lead status agent
        agent = get_lead_status_agent()
        
        # Analyze conversation
        analysis = await agent.analyze_conversation(phone_number)
        
        if not analysis:
            raise HTTPException(
                status_code=404,
                detail=f"No conversation data found for {phone_number}"
            )
        
        return LeadStatusMonitorResponse(
            success=True,
            message=ResponseMessage(
                title="Conversation Analysis Complete",
                description=f"Analysis completed for {phone_number}"
            ),
            data={
                "phone_number": phone_number,
                "analysis": {
                    "suggested_status": analysis.suggested_status.value,
                    "confidence": analysis.confidence,
                    "reasoning": analysis.reasoning,
                    "key_indicators": analysis.key_indicators,
                    "conversation_stage": analysis.conversation_stage,
                    "engagement_level": analysis.engagement_level,
                    "response_count": analysis.response_count,
                    "last_activity": analysis.last_activity.isoformat()
                },
                "recommendation": {
                    "should_update": analysis.confidence >= 0.7,
                    "confidence_threshold": 0.7,
                    "update_reasoning": "Confidence level meets threshold for automatic update" if analysis.confidence >= 0.7 else "Confidence level below threshold, manual review recommended"
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze conversation: {str(e)}"
        )
