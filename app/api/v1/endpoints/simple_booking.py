"""
Simple Booking API Endpoints
Simplified booking endpoints that work directly with Zoho Bookings API
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

from app.core.logging import logger
from app.services.zoho_bookings_service import ZohoBookingsService
from app.agents.tools.booking_tools import quick_book_from_sms, execute_booking_tool

router = APIRouter()


class AvailabilityRequest(BaseModel):
    """Request to check availability"""
    service_type: str = Field(default="lead_meeting", description="Service type")
    days_ahead: int = Field(default=7, ge=1, le=30, description="Days to look ahead")
    preferred_staff: Optional[str] = Field(None, description="Preferred staff (saumil or frank)")


class QuickBookingRequest(BaseModel):
    """Request for quick booking"""
    customer_name: str = Field(..., min_length=1, description="Customer name")
    customer_phone: str = Field(..., min_length=10, description="Customer phone")
    customer_email: Optional[str] = Field(None, description="Customer email")
    service_type: str = Field(default="lead_meeting", description="Service type")
    notes: Optional[str] = Field(None, description="Additional notes")


class BookingResponse(BaseModel):
    """Booking response"""
    success: bool
    message: str
    booking_details: Optional[dict] = None


@router.get("/test")
async def test_booking_service():
    """Test the Zoho Bookings service connection"""
    logger.info("🧪 Testing Zoho Bookings service connection")

    try:
        bookings_service = ZohoBookingsService()

        # Test getting services
        logger.info("📋 Testing service retrieval")
        services = await bookings_service.get_services()

        if services:
            logger.info(f"✅ Service test successful - found {len(services)} services")
            for service in services:
                logger.debug(f"   Service: {service.get('name')} (ID: {service.get('id')})")

            return {
                "success": True,
                "message": "Zoho Bookings service is working",
                "services_count": len(services),
                "services": [
                    {
                        "id": service.get("id"),
                        "name": service.get("name"),
                        "duration": service.get("duration")
                    }
                    for service in services
                ]
            }
        else:
            logger.warning("⚠️ Service test returned no services")
            return {
                "success": False,
                "message": "No services found or connection issue"
            }

    except Exception as e:
        logger.error(f"💥 Error testing booking service: {str(e)}")
        import traceback
        logger.debug(f"   Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service test failed: {str(e)}"
        )


@router.post("/check-availability")
async def check_availability(request: AvailabilityRequest):
    """Check availability for appointments"""
    try:
        result = execute_booking_tool(
            "check_availability",
            service_type=request.service_type,
            days_ahead=request.days_ahead,
            preferred_staff=request.preferred_staff
        )
        
        return {
            "success": True,
            "message": result
        }
        
    except Exception as e:
        logger.error(f"Error checking availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking availability: {str(e)}"
        )


@router.post("/next-available")
async def get_next_available(service_type: str = "lead_meeting", preferred_staff: Optional[str] = None):
    """Get the next available appointment slot"""
    try:
        result = execute_booking_tool(
            "get_next_available",
            service_type=service_type,
            preferred_staff=preferred_staff
        )
        
        return {
            "success": True,
            "message": result
        }
        
    except Exception as e:
        logger.error(f"Error getting next available slot: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting next available slot: {str(e)}"
        )


@router.post("/quick-book", response_model=BookingResponse)
async def quick_book(request: QuickBookingRequest):
    """Quick booking - finds next available slot and books it"""
    try:
        result = await quick_book_from_sms(
            customer_name=request.customer_name,
            customer_phone=request.customer_phone,
            customer_email=request.customer_email,
            service_type=request.service_type,
            notes=request.notes
        )
        
        return BookingResponse(
            success=result["success"],
            message=result["message"],
            booking_details=result.get("booking_details")
        )
        
    except Exception as e:
        logger.error(f"Error in quick booking: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating booking: {str(e)}"
        )


@router.post("/book-appointment")
async def book_appointment(
    customer_name: str,
    customer_phone: str,
    customer_email: Optional[str] = None,
    preferred_time: Optional[str] = None,
    service_type: str = "lead_meeting",
    notes: Optional[str] = None
):
    """Book an appointment with specific preferences"""
    try:
        result = execute_booking_tool(
            "book_appointment",
            customer_name=customer_name,
            customer_phone=customer_phone,
            customer_email=customer_email,
            preferred_time=preferred_time,
            service_type=service_type,
            notes=notes
        )
        
        return {
            "success": True,
            "message": result
        }
        
    except Exception as e:
        logger.error(f"Error booking appointment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error booking appointment: {str(e)}"
        )


@router.get("/services")
async def get_services():
    """Get available services from Zoho Bookings"""
    try:
        bookings_service = ZohoBookingsService()
        services = await bookings_service.get_services()
        
        formatted_services = []
        for service in services:
            formatted_services.append({
                "id": service.get("id"),
                "name": service.get("name"),
                "duration": service.get("duration"),
                "price": service.get("price", 0),
                "currency": service.get("currency", "USD"),
                "assigned_staff": service.get("assigned_staffs", [])
            })
        
        return {
            "success": True,
            "services": formatted_services,
            "count": len(formatted_services)
        }
        
    except Exception as e:
        logger.error(f"Error getting services: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting services: {str(e)}"
        )


@router.get("/staff")
async def get_staff():
    """Get available staff from Zoho Bookings"""
    try:
        bookings_service = ZohoBookingsService()
        staff = await bookings_service.get_staff()
        
        if not staff:
            # Return configured staff from service
            return {
                "success": True,
                "staff": [
                    {
                        "id": "26044000000040008",
                        "name": "Saumil",
                        "email": "<EMAIL>",
                        "specialties": ["franchise_consultation", "business_planning"]
                    },
                    {
                        "id": "26044000000186094",
                        "name": "Frank",
                        "email": "<EMAIL>",
                        "specialties": ["investment_planning", "roi_analysis"]
                    }
                ],
                "count": 2
            }
        
        return {
            "success": True,
            "staff": staff,
            "count": len(staff)
        }
        
    except Exception as e:
        logger.error(f"Error getting staff: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting staff: {str(e)}"
        )


@router.get("/workspace")
async def get_workspace():
    """Get workspace information"""
    try:
        bookings_service = ZohoBookingsService()
        workspace = await bookings_service.get_workspace()
        
        return {
            "success": True,
            "workspace": workspace
        }
        
    except Exception as e:
        logger.error(f"Error getting workspace: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting workspace: {str(e)}"
        )
