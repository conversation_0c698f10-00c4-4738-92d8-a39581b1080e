"""
Agent System API Endpoints
REST API for interacting with the multi-agent system
"""

from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, UploadFile, File
from pydantic import BaseModel, Field
import structlog
import uuid
from datetime import datetime

from app.core.security.enhanced_auth_middleware import get_current_user
from app.agents.orchestrator import AgentOrchestrator

from app.core.logging import logger

router = APIRouter(prefix="/agents", tags=["Agent System"])

# Global orchestrator instance
orchestrator = AgentOrchestrator()


class ChatMessage(BaseModel):
    """Chat message input schema"""
    message: str = Field(..., description="User message")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")


class ChatResponse(BaseModel):
    """Chat response schema"""
    success: bool
    response: str
    session_id: str
    intent: Optional[str] = None
    lead_id: Optional[str] = None
    next_action: Optional[str] = None
    execution_path: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None


class DocumentUpload(BaseModel):
    """Document upload schema"""
    session_id: Optional[str] = Field(None, description="Session ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    document_type: str = Field("brochure", description="Type of document")


class AgentStatus(BaseModel):
    """Agent status response schema"""
    agents: Dict[str, Any]
    workflow_compiled: bool
    checkpointer_enabled: bool


@router.post("/chat", response_model=ChatResponse)
async def chat_with_agents(
    message: ChatMessage,
    current_user: dict = Depends(get_current_user)
):
    """
    Chat with the multi-agent system
    Routes messages to appropriate agents based on intent
    """
    try:
        # Generate session ID if not provided
        session_id = message.session_id or str(uuid.uuid4())
        
        # Add user context
        context = message.context or {}
        context.update({
            "user_id": current_user.get("id") if isinstance(current_user, dict) else str(current_user.id),
            "user_email": current_user.get("email") if isinstance(current_user, dict) else current_user.email,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Process message through orchestrator
        result = await orchestrator.process_message(
            message=message.message,
            session_id=session_id,
            context=context
        )
        
        return ChatResponse(
            success=result["success"],
            response=result.get("response", "I'm here to help!"),
            session_id=session_id,
            intent=result.get("intent"),
            lead_id=result.get("lead_id"),
            next_action=result.get("next_action"),
            execution_path=result.get("execution_path", []),
            metadata=result.get("metadata", {}),
            error=result.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-document")
async def upload_document(
    file: UploadFile = File(..., description="PDF document to upload"),
    session_id: Optional[str] = None,
    franchisor_id: Optional[str] = None,
    document_type: str = "brochure",
    current_user: dict = Depends(get_current_user)
):
    """
    Upload a PDF document for processing by the document ingestion agent.
    Only PDF files are supported for document ingestion.
    """
    try:
        # Validate file type - only PDF allowed
        import os
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension != '.pdf':
            raise HTTPException(
                status_code=400,
                detail=f"Only PDF files are allowed for document ingestion. Provided: {file_extension}"
            )

        # Generate session ID if not provided
        session_id = session_id or str(uuid.uuid4())

        # Save uploaded file temporarily
        import tempfile

        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        # Prepare context for document processing
        context = {
            "user_id": current_user.get("id") if isinstance(current_user, dict) else str(current_user.id),
            "document_path": tmp_file_path,
            "original_filename": file.filename,
            "file_size": len(content),
            "franchisor_id": franchisor_id,
            "document_type": document_type,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Process through orchestrator with document upload intent
        result = await orchestrator.process_message(
            message=f"Please process the uploaded document: {file.filename}",
            session_id=session_id,
            context=context
        )
        
        # Clean up temporary file
        try:
            os.unlink(tmp_file_path)
        except:
            pass
        
        return {
            "success": result["success"],
            "message": result.get("response", "Document processed successfully"),
            "session_id": session_id,
            "document_id": result.get("metadata", {}).get("document_id"),
            "error": result.get("error")
        }
        
    except Exception as e:
        logger.error(f"Error in document upload endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=AgentStatus)
async def get_agent_status(
    current_user: dict = Depends(get_current_user)
):
    """
    Get the current status of all agents and the workflow system
    """
    try:
        status = orchestrator.get_workflow_status()
        return AgentStatus(**status)
        
    except Exception as e:
        logger.error(f"Error getting agent status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhook/message")
async def webhook_message_handler(
    payload: Dict[str, Any],
    background_tasks: BackgroundTasks
):
    """
    Webhook endpoint for handling incoming messages from external systems
    (SMS, WhatsApp, etc.)
    """
    try:
        # Extract message data from webhook payload
        message = payload.get("message", "")
        sender = payload.get("sender", "")
        platform = payload.get("platform", "webhook")
        
        if not message or not sender:
            raise HTTPException(status_code=400, detail="Message and sender are required")
        
        # Generate session ID based on sender
        session_id = f"{platform}_{sender}"
        
        # Prepare context
        context = {
            "platform": platform,
            "sender": sender,
            "webhook_payload": payload,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Process message asynchronously
        background_tasks.add_task(
            process_webhook_message,
            message=message,
            session_id=session_id,
            context=context
        )
        
        return {
            "success": True,
            "message": "Message received and will be processed",
            "session_id": session_id
        }
        
    except Exception as e:
        logger.error(f"Error in webhook endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def process_webhook_message(message: str, session_id: str, context: Dict[str, Any]):
    """
    Background task to process webhook messages
    """
    try:
        result = await orchestrator.process_message(
            message=message,
            session_id=session_id,
            context=context
        )
        
        # Here you would typically send the response back to the originating platform
        # For example, send SMS response, WhatsApp message, etc.
        
        logger.info(f"Processed webhook message for session {session_id}")
        
    except Exception as e:
        logger.error(f"Error processing webhook message: {str(e)}")


@router.get("/conversations/{session_id}")
async def get_conversation_history(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get conversation history for a specific session
    """
    try:
        # This would typically retrieve from Redis or database
        # For now, return a placeholder
        
        return {
            "session_id": session_id,
            "messages": [],
            "metadata": {
                "total_messages": 0,
                "created_at": datetime.utcnow().isoformat(),
                "last_activity": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting conversation history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/conversations/{session_id}")
async def clear_conversation(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Clear conversation history for a specific session
    """
    try:
        # This would typically clear from Redis or database
        
        return {
            "success": True,
            "message": f"Conversation {session_id} cleared successfully"
        }
        
    except Exception as e:
        logger.error(f"Error clearing conversation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agents/{agent_name}/health-check")
async def agent_health_check(
    agent_name: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Perform health check on a specific agent
    """
    try:
        agent = orchestrator.agents.get(agent_name)
        if not agent:
            raise HTTPException(status_code=404, detail=f"Agent {agent_name} not found")
        
        is_healthy = await agent.health_check()
        
        return {
            "agent_name": agent_name,
            "healthy": is_healthy,
            "status": agent.get_status(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in agent health check: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
