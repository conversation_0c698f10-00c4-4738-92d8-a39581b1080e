"""
Simple Zoho Sync API Endpoints
Focused implementation for your exact requirements
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
from datetime import datetime

from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user
from app.services.simple_zoho_sync import SimpleZohoSync, get_simple_zoho_sync
from app.schemas.base_response import SuccessResponse, ResponseMessage
from app.core.logging import logger

router = APIRouter(prefix="/zoho", tags=["Zoho Sync"])


@router.post("/sync", response_model=SuccessResponse)
async def sync_with_zoho(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Sync with Zoho CRM - Main sync button functionality
    
    This endpoint performs complete bidirectional sync:
    - Pulls new/updated leads from Zoho CRM
    - Pushes new/updated leads to Zoho CRM
    - Handles conflicts by latest timestamp
    - Only syncs existing DB fields
    """
    try:
        logger.info(f"Zoho sync triggered by user: {current_user.get('email', 'unknown')}")
        
        sync_service = get_simple_zoho_sync(db)
        result = await sync_service.sync_with_zoho()
        
        if result["success"]:
            return SuccessResponse(
                success=True,
                status="success",
                message=ResponseMessage(
                    title="Zoho Sync Completed",
                    description=result["message"]
                ),
                data=result["data"]
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=result["message"]
            )
        
    except Exception as e:
        logger.error(f"Zoho sync failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Sync failed: {str(e)}"
        )


@router.get("/status", response_model=SuccessResponse)
async def get_sync_status(
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current sync status
    Shows how many leads are synced vs unsynced
    """
    try:
        sync_service = get_simple_zoho_sync(db)
        result = await sync_service.get_sync_status()
        
        if result["success"]:
            return SuccessResponse(
                success=True,
                status="success",
                message=ResponseMessage(
                    title="Sync Status Retrieved",
                    description="Sync status retrieved successfully"
                ),
                data=result["data"]
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=result["message"]
            )
        
    except Exception as e:
        logger.error(f"Failed to get sync status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get sync status: {str(e)}"
        )
