"""
Messaging Rule API Endpoints
FastAPI router for messaging rule configuration under /settings/messaging-rules
"""

from fastapi import APIRouter, Depends, status, Query
from fastapi.responses import JSONResponse
from app.schemas.messaging_rule import (
    MessagingRuleCreateRequest,
    MessagingRuleUpdateRequest,
    MessagingRuleResponse,
    MessagingRuleListResponse,
)
from app.services.messaging_rule_service import MessagingRuleService
from app.core.factory import get_messaging_rule_service
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.api_standards import APIStandards
from app.schemas.base_response import StandardResponse
from app.core.logging import logger
from app.core.responses.models import ErrorCodes


router = APIRouter()


@router.post(
    "/messaging-rules",
    response_model=StandardResponse[MessagingRuleResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Create Messaging Rule",
    description="Create a new messaging rule. Only one rule can be active at a time - creating a new rule will deactivate all existing active rules.",
)
async def create_messaging_rule(
    rule_in: MessagingRuleCreateRequest,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Create a new messaging rule with single active rule enforcement."""
    try:
        rule = await service.create_messaging_rule(rule_in)

        # If error response (dict or JSONResponse), return it directly
        if isinstance(rule, (dict, JSONResponse)):
            return rule

        # Log successful creation
        logger.info(
            f"Messaging rule created successfully: {rule.id}",
            extra={
                "context": {
                    "rule_id": str(rule.id),
                    "user_id": str(current_user.get("id", "unknown")),
                }
            },
        )

        # Convert to response schema
        rule_dict = rule.__dict__.copy()
        if "id" in rule_dict and rule_dict["id"] is not None:
            rule_dict["id"] = str(rule_dict["id"])

        return APIStandards.create_success_response(
            data=MessagingRuleResponse(**rule_dict),
            message="Messaging rule created successfully",
            title="Messaging Rule Created",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error creating messaging rule: {str(e)}", exc_info=True
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while creating the messaging rule",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.get(
    "/messaging-rules",
    response_model=StandardResponse[MessagingRuleListResponse],
    summary="List Messaging Rules",
    description="Get a list of messaging rules with optional filtering",
)
async def list_messaging_rules(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return"
    ),
    include_inactive: bool = Query(
        False, description="Include inactive rules in the response"
    ),
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Get a list of messaging rules with optional filtering."""
    try:
        rules = await service.list_messaging_rules(
            skip=skip, limit=limit, include_inactive=include_inactive
        )

        # If error response (dict or JSONResponse), return it directly
        if isinstance(rules, (dict, JSONResponse)):
            return rules

        # Get total count for pagination
        total_count = await service.count_messaging_rules()

        # Convert rules to response format
        rule_responses = []
        for rule in rules:
            rule_dict = rule.__dict__.copy()
            if "id" in rule_dict and rule_dict["id"] is not None:
                rule_dict["id"] = str(rule_dict["id"])
            rule_responses.append(MessagingRuleResponse(**rule_dict))

        return APIStandards.create_success_response(
            data={
                "items": rule_responses,
                "total_count": total_count
            },
            message="Messaging rules retrieved successfully",
            title="Messaging Rules List",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error listing messaging rules: {str(e)}", exc_info=True
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while retrieving messaging rules",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.get(
    "/messaging-rules/active",
    response_model=StandardResponse[MessagingRuleResponse],
    summary="Get Active Messaging Rule",
    description="Get the currently active messaging rule",
)
async def get_active_messaging_rule(
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Get the currently active messaging rule."""
    try:
        rule = await service.get_current_active_rule()

        # If error response (dict or JSONResponse), return it directly
        if isinstance(rule, (dict, JSONResponse)):
            return rule

        # If no active rule found
        if rule is None:
            return APIStandards.create_error_response(
                error_message="No active messaging rule found",
                error_title="Not Found",
                status_code=status.HTTP_404_NOT_FOUND,
                error_code=4004,
            )

        # Convert to response schema
        rule_dict = rule.__dict__.copy()
        if "id" in rule_dict and rule_dict["id"] is not None:
            rule_dict["id"] = str(rule_dict["id"])

        return APIStandards.create_success_response(
            data=MessagingRuleResponse(**rule_dict),
            message="Active messaging rule retrieved successfully",
            title="Active Messaging Rule",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error retrieving active messaging rule: {str(e)}",
            exc_info=True,
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while retrieving the active messaging rule",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.get(
    "/messaging-rules/{rule_id}",
    response_model=StandardResponse[MessagingRuleResponse],
    summary="Get Messaging Rule",
    description="Get a single messaging rule by its UUID",
)
async def get_messaging_rule(
    rule_id: str,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Get a single messaging rule by UUID."""
    try:
        rule = await service.get_messaging_rule(rule_id)

        # If error response (dict or JSONResponse), return it directly
        if isinstance(rule, (dict, JSONResponse)):
            return rule

        # Convert to response schema
        rule_dict = rule.__dict__.copy()
        if "id" in rule_dict and rule_dict["id"] is not None:
            rule_dict["id"] = str(rule_dict["id"])

        return APIStandards.create_success_response(
            data={
                "details": rule_dict,
                "pagination": None
            },
            message="Messaging rule retrieved successfully",
            title="Messaging Rules List",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error retrieving messaging rule {rule_id}: {str(e)}",
            exc_info=True,
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while retrieving the messaging rule",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.put(
    "/messaging-rules/{rule_id}",
    response_model=StandardResponse[MessagingRuleResponse],
    summary="Update Messaging Rule",
    description="Update an existing messaging rule by its UUID. Setting is_active=true will deactivate all other rules.",
)
async def update_messaging_rule(
    rule_id: str,
    rule_in: MessagingRuleUpdateRequest,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Update an existing messaging rule with single active rule enforcement."""
    try:
        rule = await service.update_messaging_rule(rule_id, rule_in)

        # If error response, return it directly
        if isinstance(rule, dict):
            return rule

        # Log successful update
        logger.info(
            f"Messaging rule updated successfully: {rule_id}",
            extra={
                "context": {
                    "rule_id": rule_id,
                    "user_id": str(current_user.get("id", "unknown")),
                }
            },
        )

        # Convert to response schema
        rule_dict = rule.__dict__.copy()
        if "id" in rule_dict and rule_dict["id"] is not None:
            rule_dict["id"] = str(rule_dict["id"])

        return APIStandards.create_success_response(
            data=MessagingRuleResponse(**rule_dict),
            message="Messaging rule updated successfully",
            title="Messaging Rule Updated",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error updating messaging rule {rule_id}: {str(e)}",
            exc_info=True,
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while updating the messaging rule",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.delete(
    "/messaging-rules/{rule_id}",
    response_model=StandardResponse[dict],
    summary="Delete Messaging Rule",
    description="Soft delete a messaging rule by its UUID",
)
async def delete_messaging_rule(
    rule_id: str,
    service: MessagingRuleService = Depends(get_messaging_rule_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Soft delete a messaging rule."""
    try:
        result = await service.delete_messaging_rule(rule_id)

        # If error response, return it directly
        if isinstance(result, dict):
            return result

        # Log successful deletion
        logger.info(
            f"Messaging rule deleted successfully: {rule_id}",
            extra={
                "context": {
                    "rule_id": rule_id,
                    "user_id": str(current_user.get("id", "unknown")),
                }
            },
        )

        return APIStandards.create_success_response(
            data={"deleted": True, "rule_id": rule_id},
            message="Messaging rule deleted successfully",
            title="Messaging Rule Deleted",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error deleting messaging rule {rule_id}: {str(e)}",
            exc_info=True,
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while deleting the messaging rule",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )
