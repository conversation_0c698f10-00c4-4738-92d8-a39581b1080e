"""
Sample Documents API endpoints
"""

import logging
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.sample_doc import SampleDocSuccessResponse, SampleDocResponse
from app.services.sample_doc_service import SampleDocService
from app.repositories.sample_doc_repository import SampleDocRepository
from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user
from app.schemas.user import UserBase
from app.core.responses import create_success_response, create_error_response, ErrorCodes

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/sample-docs", tags=["Sample Documents"])


def get_sample_doc_service(db: AsyncSession = Depends(get_db)) -> SampleDocService:
    """Dependency to get sample document service"""
    repository = SampleDocRepository(db)
    return SampleDocService(repository)


@router.get(
    "/",
    response_model=SampleDocSuccessResponse,
    summary="Get All Sample Documents",
    description="Retrieve all sample documents without any filtering, sorting, or pagination",
    responses={
        200: {
            "description": "Sample documents retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Sample Documents Retrieved",
                            "description": "Successfully retrieved all sample documents"
                        },
                        "data": [
                            {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "title": "Sample Franchisor CSV",
                                "url": "https://example.com/sample-franchisor.csv"
                            },
                            {
                                "id": "123e4567-e89b-12d3-a456-426614174001",
                                "title": "Sample Leads CSV",
                                "url": "https://example.com/sample-leads.csv"
                            }
                        ]
                    }
                }
            }
        },
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_all_sample_docs(
    current_user: UserBase = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all sample documents.

    This endpoint returns all sample documents without any filtering,
    sorting, or pagination. It's a simple GET all operation.
    """
    try:
        # Handle current_user being either dict or object
        user_id = current_user.get('id') if isinstance(current_user, dict) else getattr(current_user, 'id', 'unknown')
        logger.info(f"User {user_id} requesting all sample documents")

        # Direct database query to avoid service layer issues
        from sqlalchemy import text

        query = text("SELECT id, title, url FROM sample_docs")
        result = await db.execute(query)
        rows = result.fetchall()

        logger.info(f"Retrieved {len(rows)} documents from database")

        # Convert to response format
        doc_responses = []
        for row in rows:
            row_dict = dict(row._mapping)
            doc_response = SampleDocResponse(
                id=str(row_dict.get('id', '')),
                title=row_dict.get('title'),
                url=row_dict.get('url')
            )
            doc_responses.append(doc_response)

        # Return doc_responses directly as array
        return create_success_response(
            data=doc_responses,
            message_title="Sample Documents Retrieved",
            message_description=f"Successfully retrieved {len(doc_responses)} sample documents"
        )

    except Exception as e:
        logger.error(f"Error retrieving sample documents: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Failed to Retrieve Sample Documents",
            message_description="An error occurred while retrieving sample documents",
            status_code=500
        )
