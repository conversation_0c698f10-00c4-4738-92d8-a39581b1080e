"""
Forgot Password API Endpoints
Handles password reset functionality with OTP verification
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import get_db
from app.schemas.forgot_password import (
    ForgotPasswordRequest,
    ForgotPasswordResponse,
    VerifyOTPRequest,
    VerifyOTPResponse,
    ResetPasswordRequest,
    ResetPasswordResponse
)
from app.schemas.base_response import SuccessR<PERSON>ponse, ErrorResponse
from app.services.forgot_password_service import forgot_password_service
from app.core.logging import logger
from app.core.responses.models import ErrorCodes
from app.core.api_standards import APIStandards

router = APIRouter(prefix="/forgot-password", tags=["Forgot Password"])


@router.post(
    "/initiate",
    response_model=SuccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Initiate Password Reset",
    description="Send OTP to user's email for password reset"
)
async def initiate_password_reset(
    request: ForgotPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Initiate password reset process
    
    - Validates if email exists in database
    - Generates 4-digit OTP
    - Sends OTP to user's email
    - Returns user_id for OTP verification
    - OTP expires in 5 minutes
    """
    try:
        logger.info(f"Password reset initiated for email: {request.email}")
        
        success, message, user_id = await forgot_password_service.initiate_password_reset(
            email=request.email,
            db=db
        )
        
        if not success:
            if "not found" in message.lower():
                return APIStandards.create_error_response(
                    error_message=message,
                    error_title="User Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=ErrorCodes.USER_NOT_FOUND
                )
            else:
                return APIStandards.create_error_response(
                    error_message=message,
                    error_title="Password Reset Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=ErrorCodes.UNKNOWN_ERROR
                )
        
        response_data = ForgotPasswordResponse(
            message=message,
            user_id=user_id
        )
        
        return APIStandards.create_success_response(
            data=response_data.dict(),
            message="OTP sent successfully",
            title="Password Reset Initiated",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Unexpected error in initiate_password_reset: {str(e)}")
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.post(
    "/verify-otp",
    response_model=SuccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Verify OTP",
    description="Verify OTP and get reset code for password reset"
)
async def verify_otp(
    request: VerifyOTPRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Verify OTP code
    
    - Validates OTP against user_id
    - Checks if OTP is not expired (5 minutes)
    - Returns reset_code for password reset
    - Marks OTP as used
    """
    try:
        logger.info(f"OTP verification attempted for user: {request.user_id}")
        
        success, message, reset_code = await forgot_password_service.verify_otp(
            user_id=request.user_id,
            otp_code=request.otp,
            db=db
        )
        
        if not success:
            return APIStandards.create_error_response(
                error_message=message,
                error_title="OTP Verification Failed",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=ErrorCodes.INVALID_OTP
            )
        
        response_data = VerifyOTPResponse(
            message=message,
            reset_code=reset_code
        )
        
        return APIStandards.create_success_response(
            data=response_data.dict(),
            message="OTP verified successfully",
            title="OTP Verification Successful",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Unexpected error in verify_otp: {str(e)}")
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )


@router.post(
    "/reset-password",
    response_model=SuccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Reset Password",
    description="Reset user password using reset code"
)
async def reset_password(
    request: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Reset user password
    
    - Validates reset_code and user_id
    - Encrypts new password using bcrypt
    - Updates user password in database
    - Removes used OTP record
    """
    try:
        logger.info(f"Password reset attempted for user: {request.user_id}")
        
        success, message = await forgot_password_service.reset_password(
            reset_code=request.reset_code,
            user_id=request.user_id,
            new_password=request.new_password,
            db=db
        )
        
        if not success:
            if "invalid" in message.lower() or "expired" in message.lower():
                return APIStandards.create_error_response(
                    error_message=message,
                    error_title="Password Reset Failed",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    error_code=ErrorCodes.INVALID_RESET_CODE
                )
            else:
                return APIStandards.create_error_response(
                    error_message=message,
                    error_title="Password Reset Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=ErrorCodes.UNKNOWN_ERROR
                )
        
        response_data = ResetPasswordResponse(message=message)
        
        return APIStandards.create_success_response(
            data=response_data.dict(),
            message="Password reset successfully",
            title="Password Reset Successful",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Unexpected error in reset_password: {str(e)}")
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
