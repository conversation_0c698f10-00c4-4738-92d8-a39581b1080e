"""
Legacy webhook endpoints - <PERSON><PERSON>ity webhook moved to kudosity_webhook.py
Only health check endpoint remains here for backward compatibility
"""

from datetime import datetime
from fastapi import APIRouter, HTTPException
from fastapi.responses import J<PERSON>NResponse
import structlog

from app.schemas.webhook import WebhookHealthResponse

from app.core.logging import logger

# Create router
router = APIRouter(prefix="/webhooks", tags=["Legacy Webhooks"])


@router.get(
    "/health",
    response_model=WebhookHealthResponse,
    summary="Webhook Health Check",
    description="Check the health status of the webhook service (no authentication required)"
)
async def webhook_health():
    """Health check for webhook service"""
    try:
        return WebhookHealthResponse(
            status="healthy",
            qna_available=True,
            timestamp=datetime.now(),
            details={
                "message": "Legacy webhook health check - Kudosity webhook moved to /api/webhooks/kudosity",
                "new_endpoint": "/api/webhooks/kudosity",
                "service": "legacy_webhooks"
            }
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to determine service health status")
