"""
Bookings API Endpoints
Simple API for booking appointments via Zoho Bookings
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
import uuid

from app.services.zoho_bookings_service import ZohoBookingsService, get_zoho_bookings_service
from app.repositories.booking_repository import BookingRepository
from app.core.database.connection import AsyncSessionLocal
from app.core.logging import logger


router = APIRouter(prefix="/bookings", tags=["bookings"])


class BookingRequest(BaseModel):
    """Request model for booking an appointment"""
    lead_id: str = Field(..., description="Lead ID from the database")
    preferred_start_time: datetime = Field(..., description="Preferred appointment start time")
    timezone: str = Field(default="Australia/Canberra", description="Timezone for the appointment")
    service_type: str = Field(default="lead_meeting", description="Type of service (lead_meeting, saumil_consultation, frank_consultation)")


class BookingResponse(BaseModel):
    """Response model for booking result"""
    success: bool
    message: str
    booking_id: Optional[str] = None
    meeting_link: Optional[str] = None
    booking_url: Optional[str] = None
    scheduled_time: Optional[datetime] = None
    staff_name: Optional[str] = None
    error: Optional[str] = None


class AvailabilityRequest(BaseModel):
    """Request model for checking availability"""
    date_from: datetime
    date_to: datetime
    service_type: str = Field(default="lead_meeting")
    preferred_staff: Optional[str] = None


class AvailabilitySlot(BaseModel):
    """Available slot model"""
    staff_id: str
    staff_name: str
    start_time: datetime
    end_time: datetime
    service_name: str
    duration_minutes: int


class AvailabilityResponse(BaseModel):
    """Response model for availability check"""
    success: bool
    available_slots: list[AvailabilitySlot]
    total_slots: int


@router.post("/book", response_model=BookingResponse)
async def book_appointment(
    request: BookingRequest,
    zoho_service: ZohoBookingsService = Depends(get_zoho_bookings_service)
):
    """
    Book an appointment for a lead
    
    This endpoint:
    1. Takes lead ID, preferred time, and timezone
    2. Finds the closest available slot to the preferred time
    3. Books the appointment with an available sales person
    4. Stores the booking in the database
    5. Returns booking confirmation details
    """
    try:
        logger.info(f"Booking request for lead {request.lead_id} at {request.preferred_start_time}")
        
        # Validate lead_id format
        try:
            lead_uuid = uuid.UUID(request.lead_id)
        except ValueError:
            error_msg = f"Invalid lead ID format: {request.lead_id}. Must be a valid UUID."
            logger.error(f"❌ {error_msg}")
            return BookingResponse(
                success=False,
                message="Invalid lead ID format",
                error=error_msg
            )
        
        # Validate service type
        valid_service_types = ['lead_meeting', 'saumil_consultation', 'frank_consultation']
        if request.service_type not in valid_service_types:
            error_msg = f"Invalid service type: {request.service_type}. Must be one of: {valid_service_types}"
            logger.error(f"❌ {error_msg}")
            return BookingResponse(
                success=False,
                message="Invalid service type",
                error=error_msg
            )
        
        # Validate timezone
        valid_timezones = ['Australia/Canberra', 'Australia/Sydney', 'Australia/Melbourne', 'Australia/Brisbane', 'Australia/Perth']
        if request.timezone not in valid_timezones:
            error_msg = f"Invalid timezone: {request.timezone}. Must be one of: {valid_timezones}"
            logger.error(f"❌ {error_msg}")
            return BookingResponse(
                success=False,
                message="Invalid timezone",
                error=error_msg
            )
        
        # Book appointment for the lead
        booking_result = await zoho_service.book_appointment_for_lead(
            lead_id=request.lead_id,
            preferred_start_time=request.preferred_start_time,
            timezone=request.timezone,
            service_type=request.service_type
        )
        
        if not booking_result.success:
            error_msg = f"Zoho booking failed: {booking_result.error_message}"
            logger.error(f"❌ {error_msg}")
            return BookingResponse(
                success=False,
                message=f"Booking failed: {booking_result.error_message}",
                error=booking_result.error_message
            )
        
        # Get the booked slot details for response
        slot = await zoho_service.find_closest_available_slot(
            preferred_start_time=request.preferred_start_time,
            service_type=request.service_type,
            timezone=request.timezone
        )
        
        if not slot:
            error_msg = "Failed to retrieve booked slot details"
            logger.error(f"❌ {error_msg}")
            return BookingResponse(
                success=False,
                message="Booking completed but failed to retrieve slot details",
                error=error_msg
            )
        
        # Store booking in database
        try:
            async with AsyncSessionLocal() as db:
                booking_repo = BookingRepository(db)
                
                # Get lead information for customer details
                from app.models.lead import Lead
                lead = await db.get(Lead, lead_uuid)
                if not lead:
                    error_msg = f"Lead with ID '{request.lead_id}' not found in database"
                    logger.error(f"❌ {error_msg}")
                    return BookingResponse(
                        success=False,
                        message="Lead not found in database",
                        error=error_msg
                    )
                
                # Prepare booking data for database
                booking_data = {
                    'zoho_booking_id': booking_result.booking_id,
                    'zoho_service_id': slot.service_id,
                    'zoho_staff_id': slot.staff_id,
                    'lead_id': lead_uuid,
                    'customer_name': f"{lead.first_name} {lead.last_name}",
                    'customer_email': lead.email,
                    'customer_phone': lead.phone,
                    'service_type': request.service_type,
                    'staff_name': slot.staff_name,
                    'staff_email': None,  # Will be populated from sales team config
                    'start_time': slot.start_time,
                    'end_time': slot.end_time,
                    'duration_minutes': slot.duration_minutes,
                    'timezone': request.timezone,
                    'status': 'scheduled',
                    'booking_source': 'api',
                    'booking_url': booking_result.booking_url,
                    'meeting_link': booking_result.meeting_link,
                    'notes': f"Booking for lead: {request.lead_id}",
                    'booking_metadata': {
                        'zoho_response': {
                            'booking_id': booking_result.booking_id,
                            'booking_url': booking_result.booking_url,
                            'meeting_link': booking_result.meeting_link
                        },
                        'slot_details': {
                            'staff_id': slot.staff_id,
                            'staff_name': slot.staff_name,
                            'service_id': slot.service_id,
                            'service_name': slot.service_name
                        }
                    }
                }
                
                # Create booking record
                db_booking = await booking_repo.create_booking(booking_data)
                
                logger.info(f"✅ Successfully created booking record in database: {db_booking.id}")
                
                return BookingResponse(
                    success=True,
                    message="Appointment booked successfully",
                    booking_id=str(db_booking.id),
                    meeting_link=booking_result.meeting_link,
                    booking_url=booking_result.booking_url,
                    scheduled_time=slot.start_time,
                    staff_name=slot.staff_name
                )
                
        except Exception as db_error:
            error_msg = f"Database error while storing booking: {str(db_error)}"
            logger.error(f"❌ {error_msg}")
            # Even if database fails, the Zoho booking was successful
            return BookingResponse(
                success=True,
                message="Appointment booked successfully in Zoho, but failed to store in local database",
                booking_id=booking_result.booking_id,
                meeting_link=booking_result.meeting_link,
                booking_url=booking_result.booking_url,
                scheduled_time=slot.start_time,
                staff_name=slot.staff_name,
                error=f"Database storage failed: {str(db_error)}"
            )
            
    except ValueError as ve:
        error_msg = f"Validation error: {str(ve)}"
        logger.error(f"❌ {error_msg}")
        return BookingResponse(
            success=False,
            message="Validation error occurred",
            error=error_msg
        )
    except ConnectionError as ce:
        error_msg = f"Connection error: {str(ce)}"
        logger.error(f"❌ {error_msg}")
        return BookingResponse(
            success=False,
            message="Connection error occurred while booking appointment",
            error=error_msg
        )
    except TimeoutError as te:
        error_msg = f"Timeout error: {str(te)}"
        logger.error(f"❌ {error_msg}")
        return BookingResponse(
            success=False,
            message="Request timeout occurred while booking appointment",
            error=error_msg
        )
    except Exception as e:
        error_msg = f"Unexpected error in book_appointment endpoint: {str(e)}"
        logger.error(f"❌ {error_msg}")
        import traceback
        logger.debug(f"   Traceback: {traceback.format_exc()}")
        return BookingResponse(
            success=False,
            message="Internal server error occurred during booking",
            error="Internal server error"
        )


@router.post("/availability", response_model=AvailabilityResponse)
async def check_availability(
    request: AvailabilityRequest,
    zoho_service: ZohoBookingsService = Depends(get_zoho_bookings_service)
):
    """
    Check availability for appointments
    
    Returns all available slots in the specified date range
    """
    try:
        logger.info(f"Checking availability from {request.date_from} to {request.date_to}")
        
        # Validate date range
        if request.date_from >= request.date_to:
            error_msg = "Start date must be before end date"
            logger.error(f"❌ {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        
        # Validate service type
        valid_service_types = ['lead_meeting', 'saumil_consultation', 'frank_consultation']
        if request.service_type not in valid_service_types:
            error_msg = f"Invalid service type: {request.service_type}. Must be one of: {valid_service_types}"
            logger.error(f"❌ {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        
        # Validate date range is not too large (max 30 days)
        date_diff = request.date_to - request.date_from
        if date_diff.days > 30:
            error_msg = "Date range cannot exceed 30 days"
            logger.error(f"❌ {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        
        # Convert timezone-aware datetimes to naive datetimes if needed
        date_from = request.date_from
        date_to = request.date_to
        
        if date_from.tzinfo is not None:
            date_from = date_from.replace(tzinfo=None)
        if date_to.tzinfo is not None:
            date_to = date_to.replace(tzinfo=None)
        
        # Get available slots
        slots = await zoho_service.get_available_slots(
            date_from=date_from,
            date_to=date_to,
            service_type=request.service_type,
            preferred_staff=request.preferred_staff
        )
        
        # Convert to response format
        availability_slots = [
            AvailabilitySlot(
                staff_id=slot.staff_id,
                staff_name=slot.staff_name,
                start_time=slot.start_time,
                end_time=slot.end_time,
                service_name=slot.service_name,
                duration_minutes=slot.duration_minutes
            )
            for slot in slots
        ]
        
        logger.info(f"✅ Found {len(availability_slots)} available slots")
        
        return AvailabilityResponse(
            success=True,
            available_slots=availability_slots,
            total_slots=len(availability_slots)
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except ValueError as ve:
        error_msg = f"Validation error: {str(ve)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except ConnectionError as ce:
        error_msg = f"Connection error while checking availability: {str(ce)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service temporarily unavailable"
        )
    except TimeoutError as te:
        error_msg = f"Timeout error while checking availability: {str(te)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request timeout"
        )
    except Exception as e:
        error_msg = f"Unexpected error in check_availability endpoint: {str(e)}"
        logger.error(f"❌ {error_msg}")
        import traceback
        logger.debug(f"   Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while checking availability"
        )


@router.get("/staff-summary")
async def get_staff_availability_summary(
    zoho_service: ZohoBookingsService = Depends(get_zoho_bookings_service)
):
    """
    Get availability summary for all staff members
    
    Returns overview of each staff member's availability
    """
    try:
        summary = await zoho_service.get_staff_availability_summary()
        return {
            "success": True,
            "staff_summary": summary
        }
        
    except Exception as e:
        logger.error(f"Error in get_staff_availability_summary endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.delete("/cancel/{booking_id}")
async def cancel_booking(
    booking_id: str,
    reason: Optional[str] = None,
    zoho_service: ZohoBookingsService = Depends(get_zoho_bookings_service)
):
    """
    Cancel a booking
    
    Cancels the booking in Zoho Bookings and updates local database
    """
    try:
        logger.info(f"Cancelling booking {booking_id}")
        
        success = await zoho_service.cancel_booking(booking_id, reason)
        
        if success:
            return {
                "success": True,
                "message": "Booking cancelled successfully"
            }
        else:
            return {
                "success": False,
                "message": "Failed to cancel booking"
            }
            
    except Exception as e:
        logger.error(f"Error in cancel_booking endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/next-available")
async def get_next_available_slot(
    service_type: str = "consultation",
    preferred_staff: Optional[str] = None,
    zoho_service: ZohoBookingsService = Depends(get_zoho_bookings_service)
):
    """
    Get the next available appointment slot
    
    Returns the earliest available slot within the next 7 days
    """
    try:
        slot = await zoho_service.get_next_available_slot(
            service_type=service_type,
            preferred_staff=preferred_staff
        )
        
        if slot:
            return {
                "success": True,
                "slot": {
                    "staff_id": slot.staff_id,
                    "staff_name": slot.staff_name,
                    "start_time": slot.start_time.isoformat(),
                    "end_time": slot.end_time.isoformat(),
                    "service_name": slot.service_name,
                    "duration_minutes": slot.duration_minutes
                }
            }
        else:
            return {
                "success": False,
                "message": "No available slots found in the next 7 days"
            }
            
    except Exception as e:
        logger.error(f"Error in get_next_available_slot endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )
