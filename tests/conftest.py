"""
Pytest configuration and fixtures for GrowthHive test suite
Provides comprehensive test infrastructure with database management and mocking
"""

import os
import pytest
import asyncio
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import AsyncGenerator, Dict, Any
from unittest.mock import Mock, patch, AsyncMock

# Set test environment variables before any imports
os.environ.update({
    "ENVIRONMENT": "test",
    "DATABASE_URL": "postgresql+asyncpg://postgres:root@localhost:5432/growthhive_test",
    "REDIS_URL": "redis://localhost:6379/15",
    "CELERY_BROKER_URL": "redis://localhost:6379/14",
    "CELERY_RESULT_BACKEND": "redis://localhost:6379/13",
    "SMS_TEST_MODE": "true",
    "KUDOSITY_SMS_ENABLED": "false",
    "OPENAI_API_KEY": "test-key-sk-**********",
    "LOG_LEVEL": "DEBUG",
    # Meeting Agent Test Configuration
    "MEETING_AGENT_ENABLED": "true",
    "MEETING_AGENT_PROVIDER": "zoho",
    "MEETING_AGENT_LOCALE": "en_IN",
    "MEETING_AGENT_DEFAULT_TIMEZONE": "Asia/Kolkata",
    "MEETING_AGENT_MAX_CLARIFICATIONS": "2",
    "MEETING_AGENT_CONVERSATION_TIMEOUT": "1800"
})

import sqlalchemy as sa
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.core.database.connection import get_db
from app.models.base import Base
from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.models.conversation_message import ConversationMessage
from app.models.messaging_rule import MessagingRule
from app.models.booking import Booking
from app.services.kudosity_sms_service import KudositySMSService
# from app.agents.followup_agent import FollowUpAgent  # Removed for Andy AI
from app.agents.sms_assistant import AndySMSAssistant


# Test database configuration
TEST_DATABASE_URL = "postgresql+asyncpg://postgres:root@localhost:5432/growthhive_test"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=300
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture(scope="function")
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session with automatic cleanup"""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        # Start a transaction
        transaction = await session.begin()
        
        yield session
        
        # Rollback transaction to cleanup
        await transaction.rollback()


@pytest.fixture
def client(db_session):
    """Create test client with database override"""
    def override_get_db():
        return db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Cleanup
    app.dependency_overrides.clear()


@pytest.fixture
async def test_franchisor(db_session: AsyncSession) -> Franchisor:
    """Create a test franchisor"""
    franchisor = Franchisor(
        id=str(uuid.uuid4()),
        name="Test Franchise",
        description="Test franchise for testing",
        region="Melbourne",
        is_active=True,
        is_deleted=False
    )
    db_session.add(franchisor)
    await db_session.commit()
    await db_session.refresh(franchisor)
    return franchisor


@pytest.fixture
async def test_lead(db_session: AsyncSession, test_franchisor: Franchisor) -> Lead:
    """Create a test lead"""
    lead = Lead(
        id=str(uuid.uuid4()),
        first_name="Test",
        last_name="User",
        email="<EMAIL>",
        phone_number="+************",
        brand_preference=test_franchisor.id,
        is_active=True,
        is_deleted=False
    )
    db_session.add(lead)
    await db_session.commit()
    await db_session.refresh(lead)
    return lead


@pytest.fixture
async def test_messaging_rule(db_session: AsyncSession) -> MessagingRule:
    """Create a test messaging rule"""
    rule = MessagingRule(
        id=str(uuid.uuid4()),
        lead_init_delay_m=0,  # No delay for testing
        no_response_delay_m=1,  # 1 minute for testing
        max_followups=3,
        is_active=True,
        is_deleted=False
    )
    db_session.add(rule)
    await db_session.commit()
    await db_session.refresh(rule)
    return rule


@pytest.fixture
def mock_sms_service():
    """Mock SMS service for testing"""
    with patch('app.services.kudosity_sms_service.KudositySMSService') as mock_service:
        mock_instance = Mock(spec=KudositySMSService)
        mock_instance.test_mode = True
        mock_instance.send_sms = AsyncMock(return_value={
            "success": True,
            "message_id": "test-message-123",
            "parts_sent": 1,
            "total_characters": 100
        })
        mock_instance.split_sms = Mock(return_value=[{
            "text": "Test message",
            "part_number": 1,
            "total_parts": 1,
            "character_count": 12,
            "encoding": "GSM-7"
        }])
        mock_service.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_openai():
    """Mock OpenAI client for testing"""
    with patch('openai.AsyncOpenAI') as mock_client:
        mock_instance = Mock()
        
        # Mock chat completion
        mock_chat_response = Mock()
        mock_chat_response.choices = [Mock(message=Mock(content="Test AI response"))]
        mock_chat_response.usage = Mock(total_tokens=100)
        mock_instance.chat.completions.create = AsyncMock(return_value=mock_chat_response)
        
        # Mock embeddings
        mock_embedding_response = Mock()
        mock_embedding_response.data = [Mock(embedding=[0.1] * 1536)]
        mock_instance.embeddings.create = AsyncMock(return_value=mock_embedding_response)
        
        mock_client.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_celery():
    """Mock Celery for testing"""
    with patch('app.core.celery_app.celery_app') as mock_celery_app:
        mock_task = Mock()
        mock_task.apply_async = Mock(return_value=Mock(id="test-task-123"))
        mock_celery_app.send_task = Mock(return_value=mock_task)
        yield mock_celery_app


@pytest.fixture
async def clean_database(db_session: AsyncSession):
    """Clean database before and after test"""
    # Clean before test
    await _flush_test_data(db_session)
    
    yield
    
    # Clean after test
    await _flush_test_data(db_session)


async def _flush_test_data(session: AsyncSession):
    """Flush all test data from database"""
    try:
        # Delete in reverse dependency order
        await session.execute(sa.text("DELETE FROM conversation_message WHERE created_at >= NOW() - INTERVAL '1 day'"))
        await session.execute(sa.text("DELETE FROM booking WHERE created_at >= NOW() - INTERVAL '1 day'"))
        await session.execute(sa.text("DELETE FROM lead WHERE created_at >= NOW() - INTERVAL '1 day'"))
        await session.execute(sa.text("DELETE FROM messaging_rule WHERE created_at >= NOW() - INTERVAL '1 day'"))
        await session.commit()
    except Exception as e:
        await session.rollback()
        print(f"Warning: Could not flush test data: {e}")


# Test data factories
class TestDataFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_lead_data(**kwargs) -> Dict[str, Any]:
        """Create lead test data"""
        defaults = {
            "first_name": "Test",
            "last_name": "User", 
            "email": "<EMAIL>",
            "phone_number": "+************",
            "is_active": True,
            "is_deleted": False
        }
        defaults.update(kwargs)
        return defaults
    
    @staticmethod
    def create_sms_webhook_data(**kwargs) -> Dict[str, Any]:
        """Create SMS webhook test data"""
        defaults = {
            "mo": {
                "sender": "************",
                "recipient": "+61400000000",
                "message": "Test message"
            }
        }
        defaults.update(kwargs)
        return defaults
    
    @staticmethod
    def create_booking_data(**kwargs) -> Dict[str, Any]:
        """Create booking test data"""
        defaults = {
            "customer_name": "Test User",
            "customer_phone": "+************",
            "customer_email": "<EMAIL>",
            "service_type": "lead_meeting",
            "preferred_date": datetime.now() + timedelta(days=1),
            "duration_minutes": 30
        }
        defaults.update(kwargs)
        return defaults


@pytest.fixture
def test_data_factory():
    """Provide test data factory"""
    return TestDataFactory


# Utility fixtures
@pytest.fixture
def mock_redis():
    """Mock Redis for testing"""
    with patch('redis.Redis') as mock_redis:
        mock_instance = Mock()
        mock_instance.get = Mock(return_value=None)
        mock_instance.set = Mock(return_value=True)
        mock_instance.delete = Mock(return_value=1)
        mock_instance.exists = Mock(return_value=False)
        mock_redis.return_value = mock_instance
        yield mock_instance


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state before each test"""
    # Reset any global caches or state
    # from app.agents.followup_agent import PENDING_FOLLOWUP_TASKS  # Removed for Andy AI
    # PENDING_FOLLOWUP_TASKS.clear()

    yield

    # Cleanup after test
    # PENDING_FOLLOWUP_TASKS.clear()  # Removed for Andy AI


# Test utilities
def assert_sms_response(response_data: Dict[str, Any], expected_success: bool = True):
    """Assert SMS response structure"""
    assert "success" in response_data
    assert response_data["success"] == expected_success
    assert "message" in response_data
    assert "data" in response_data

    if expected_success:
        assert "sms_sent" in response_data["data"]
        assert "ai_response" in response_data["data"]


def assert_followup_scheduled(response_data: Dict[str, Any]):
    """Assert follow-up was scheduled"""
    assert response_data.get("success") == True
    data = response_data.get("data", {})
    metadata = data.get("processing_metadata", {})
    assert "followup_scheduled" in str(data) or "followup" in str(metadata)


def assert_followup_cancelled(response_data: Dict[str, Any]):
    """Assert follow-up was cancelled"""
    assert response_data.get("success") == True
    data = response_data.get("data", {})
    assert "followup_cancelled" in str(data) or "cancelled" in str(data)


# Meeting Agent Test Fixtures

@pytest.fixture
def frozen_time():
    """Freeze time to consistent test timestamp for meeting agent tests."""
    from freezegun import freeze_time
    with freeze_time("2025-08-29T12:00:00+05:30") as frozen:
        yield frozen


@pytest.fixture
def meeting_agent_mocks():
    """Mock all external services for meeting agent tests."""
    from tests.mocks.kudosity_stub import KudosityStub
    from tests.mocks.zoho_bookings_stub import ZohoBookingsStub
    from tests.mocks.search_stub import SearchStub

    kudosity_stub = KudosityStub()
    zoho_stub = ZohoBookingsStub()
    search_stub = SearchStub()

    with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub), \
         patch('app.services.zoho_bookings_service.ZohoBookingsService', return_value=zoho_stub), \
         patch('app.rag.search.SearchService', return_value=search_stub):
        yield {
            'kudosity': kudosity_stub,
            'zoho': zoho_stub,
            'search': search_stub
        }


@pytest.fixture
def andy_assertions():
    """Custom assertions for Andy AI testing."""
    class AndyAssertions:
        @staticmethod
        def assert_no_emojis(text: str):
            """Assert that text contains no emoji characters."""
            import re
            emoji_pattern = re.compile(
                "["
                "\U0001F600-\U0001F64F"  # emoticons
                "\U0001F300-\U0001F5FF"  # symbols & pictographs
                "\U0001F680-\U0001F6FF"  # transport & map symbols
                "\U0001F1E0-\U0001F1FF"  # flags (iOS)
                "\U00002702-\U000027B0"
                "\U000024C2-\U0001F251"
                "]+", flags=re.UNICODE
            )
            assert not emoji_pattern.search(text), f"Text contains emojis: {text}"

        @staticmethod
        def assert_timezone_format(datetime_str: str):
            """Assert datetime string is properly formatted with timezone."""
            from datetime import datetime
            try:
                dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
                assert dt.tzinfo is not None, f"Datetime missing timezone info: {datetime_str}"
            except ValueError as e:
                pytest.fail(f"Invalid datetime format: {datetime_str}, error: {e}")

        @staticmethod
        def assert_utc_storage(db_datetime):
            """Assert database datetime is stored in UTC."""
            from datetime import timezone
            if db_datetime and hasattr(db_datetime, 'tzinfo'):
                assert db_datetime.tzinfo == timezone.utc, f"Database datetime not in UTC: {db_datetime}"

    return AndyAssertions()


# Test data constants for meeting agent
TEST_AVAILABILITY_SEED = {
    "2025-08-30": ["09:30", "12:00", "15:00"],
    "2025-09-01": ["10:00", "13:00", "16:30"],
    "2025-09-03": ["11:00", "13:00", "15:00"],
    "2025-09-05": ["09:00", "11:30", "14:00"]
}

TEST_HOLIDAYS = ["2025-08-31"]
