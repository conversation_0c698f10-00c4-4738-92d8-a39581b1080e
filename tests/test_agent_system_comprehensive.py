"""
Comprehensive tests for the agent system
Tests agent orchestration, SMS assistant, and booking agent integration
"""

import pytest
import uuid
from unittest.mock import patch, AsyncMock, Mock

from app.agents.sms_assistant import Andy<PERSON>SAssistant, AndyWorkflowStages
from app.agents.booking_agent import BookingAgent
from app.agents.orchestrator import AgentOrchestrator
from app.agents.followup_agent import FollowUpAgent
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage


@pytest.mark.agent
@pytest.mark.asyncio
class TestAgentSystemComprehensive:
    """Comprehensive agent system tests"""

    async def test_sms_assistant_workflow_stages(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test SMS assistant workflow stage transitions"""
        
        # Configure OpenAI mock
        mock_openai.chat.completions.create.return_value.choices[0].message.content = (
            "Hello! I'd be happy to help you with franchise opportunities. "
            "To get started, could you tell me about your investment budget?"
        )
        
        assistant = AndySMSAssistant()
        
        # Test introduction stage
        result = await assistant.process_sms(
            phone_number=test_lead.mobile,
            message="Hello, I'm interested in franchises",
            lead_id=test_lead.id
        )
        
        assert result["success"] == True
        assert result["conversation_stage"] in ["introduction", "qualification"]
        assert len(result["response"]) > 0
        
        # Test qualification stage
        result = await assistant.process_message(
            db=db_session,
            lead_id=test_lead.id,
            message="I have $150,000 to invest",
            phone_number=test_lead.phone_number
        )
        
        assert result["success"] == True
        assert result["conversation_stage"] in ["qualification", "budget"]
        
        # Test scheduling stage
        result = await assistant.process_message(
            db=db_session,
            lead_id=test_lead.id,
            message="Yes, I'd like to schedule a meeting",
            phone_number=test_lead.phone_number
        )
        
        assert result["success"] == True
        assert result["conversation_stage"] == "scheduling"

    async def test_agent_orchestrator_initialization(
        self,
        mock_openai
    ):
        """Test agent orchestrator initialization"""
        
        with patch('app.agents.base.AgentFactory.create_agent') as mock_factory:
            # Mock agent creation
            mock_agent = Mock()
            mock_agent.id = str(uuid.uuid4())
            mock_agent.status = "idle"
            mock_factory.return_value = mock_agent
            
            # Test orchestrator initialization
            orchestrator = AgentOrchestrator()
            
            assert orchestrator is not None
            assert hasattr(orchestrator, 'agents')
            assert len(orchestrator.agents) >= 0  # Depends on successful initialization

    async def test_booking_agent_slot_detection(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test booking agent time slot detection"""
        
        booking_agent = BookingAgent({
            "role": "booking_agent",
            "name": "Booking Agent",
            "model": "gpt-4-turbo",
            "temperature": 0.1,
            "max_tokens": 1000
        })
        
        # Test various time expressions
        test_messages = [
            "I'm available Monday at 10am",
            "How about Tuesday at 2:30 PM?",
            "I can do Wednesday morning around 9",
            "Friday afternoon works for me",
            "Next week Tuesday at 10:00"
        ]
        
        for message in test_messages:
            slots = await booking_agent.detect_time_slots(message)
            # Should detect at least some time information
            assert isinstance(slots, list)

    async def test_followup_agent_context_awareness(
        self,
        db_session,
        test_lead,
        test_messaging_rule
    ):
        """Test follow-up agent context awareness"""
        
        # Create conversation history
        conversation_msg = ConversationMessage(
            id=str(uuid.uuid4()),
            lead_id=test_lead.id,
            sender="lead",
            message="I'm interested in food franchises with $200k budget",
            conversation_stage="budget",
            is_active=True,
            is_deleted=False
        )
        db_session.add(conversation_msg)
        await db_session.commit()
        
        followup_agent = FollowUpAgent()
        
        # Test context building
        context = await followup_agent._build_followup_context(
            db=db_session,
            lead_id=test_lead.id,
            followup_attempt=1
        )
        
        assert context is not None
        assert context.lead_name == f"{test_lead.first_name} {test_lead.last_name}"
        assert context.conversation_stage == "budget"
        assert "200k" in context.last_message or "$200" in context.last_message

    async def test_agent_error_handling(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent error handling"""
        
        # Test OpenAI API failure
        mock_openai.chat.completions.create.side_effect = Exception("API Error")
        
        assistant = AndySMSAssistant()
        
        result = await assistant.process_message(
            db=db_session,
            lead_id=test_lead.id,
            message="Test message",
            phone_number=test_lead.phone_number
        )
        
        # Should handle error gracefully
        assert "error" in result or result["success"] == False

    async def test_agent_memory_and_context(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent memory and context management"""
        
        # Configure OpenAI mock for consistent responses
        mock_openai.chat.completions.create.return_value.choices[0].message.content = (
            "I remember you mentioned having $150,000 to invest. "
            "Let's discuss some franchise options in that range."
        )
        
        assistant = AndySMSAssistant()
        
        # First message
        result1 = await assistant.process_message(
            db=db_session,
            lead_id=test_lead.id,
            message="I have $150,000 to invest",
            phone_number=test_lead.phone_number
        )
        
        assert result1["success"] == True
        
        # Second message (should remember context)
        result2 = await assistant.process_message(
            db=db_session,
            lead_id=test_lead.id,
            message="What franchises do you recommend?",
            phone_number=test_lead.phone_number
        )
        
        assert result2["success"] == True
        # Response should reference previous context
        assert "$150" in result2["response"] or "150" in result2["response"]

    async def test_agent_tool_integration(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent tool integration"""
        
        with patch('app.agents.tools.booking_tools.quick_book_from_sms') as mock_booking_tool:
            mock_booking_tool.return_value = {
                "success": True,
                "booking_id": "TEST123",
                "message": "Meeting booked successfully"
            }
            
            assistant = AndySMSAssistant()
            
            # Test message that should trigger booking tool
            result = await assistant.process_message(
                db=db_session,
                lead_id=test_lead.id,
                message="Book me for Monday at 10am",
                phone_number=test_lead.phone_number
            )
            
            assert result["success"] == True
            # Should have attempted to use booking tool
            assert result["conversation_stage"] == "scheduling"

    async def test_agent_performance_metrics(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent performance metrics"""
        
        import time
        
        assistant = AndySMSAssistant()
        
        start_time = time.time()
        
        result = await assistant.process_message(
            db=db_session,
            lead_id=test_lead.id,
            message="Test performance",
            phone_number=test_lead.phone_number
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        assert result["success"] == True
        assert processing_time < 30.0  # Should process within 30 seconds
        
        # Check if processing time is tracked
        if "processing_time" in result:
            assert result["processing_time"] > 0

    async def test_agent_concurrent_processing(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent concurrent message processing"""
        
        import asyncio
        
        assistant = AndySMSAssistant()
        
        # Create multiple concurrent tasks
        tasks = []
        for i in range(3):
            task = assistant.process_message(
                db=db_session,
                lead_id=test_lead.id,
                message=f"Concurrent test message {i}",
                phone_number=test_lead.phone_number
            )
            tasks.append(task)
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed or handle gracefully
        for result in results:
            if not isinstance(result, Exception):
                assert result["success"] == True

    async def test_agent_state_management(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent state management across conversations"""
        
        assistant = AndySMSAssistant()
        
        # Test state progression
        messages_and_expected_stages = [
            ("Hello", ["introduction", "greeting"]),
            ("I have $100k to invest", ["qualification", "budget"]),
            ("I want food franchises", ["qualification", "preferences"]),
            ("Can we schedule a call?", ["scheduling"])
        ]
        
        for message, expected_stages in messages_and_expected_stages:
            result = await assistant.process_message(
                db=db_session,
                lead_id=test_lead.id,
                message=message,
                phone_number=test_lead.phone_number
            )
            
            assert result["success"] == True
            assert any(stage in result["conversation_stage"] for stage in expected_stages)

    async def test_agent_integration_with_external_services(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test agent integration with external services"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_zoho:
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {"date": "2024-08-26", "time": "10:00", "staff": "saumil"}
                ]
            })
            mock_zoho.return_value = mock_instance
            
            booking_agent = BookingAgent({
                "role": "booking_agent",
                "name": "Booking Agent",
                "model": "gpt-4-turbo",
                "temperature": 0.1,
                "max_tokens": 1000
            })
            
            # Test availability check
            availability = await booking_agent.get_availability("lead_meeting", 7)
            
            assert availability["success"] == True
            assert len(availability["available_slots"]) >= 1

    async def test_agent_configuration_flexibility(
        self,
        mock_openai
    ):
        """Test agent configuration flexibility"""
        
        # Test different configurations
        configs = [
            {
                "role": "sms_assistant",
                "name": "Andy SMS Assistant",
                "model": "gpt-4-turbo",
                "temperature": 0.1,
                "max_tokens": 1000
            },
            {
                "role": "booking_agent", 
                "name": "Booking Agent",
                "model": "gpt-3.5-turbo",
                "temperature": 0.2,
                "max_tokens": 500
            }
        ]
        
        for config in configs:
            # Should be able to create agents with different configs
            if config["role"] == "sms_assistant":
                agent = AndySMSAssistant()
                assert agent is not None
            elif config["role"] == "booking_agent":
                agent = BookingAgent(config)
                assert agent is not None
