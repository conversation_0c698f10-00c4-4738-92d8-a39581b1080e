"""
Non-Meeting Flow Regression Tests
Guard rails: ensure lead ingestion, follow-ups, RAG, escalation bank still behave exactly as before
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from tests.meeting_agent.helpers.conversation_harness import ConversationHarness


class TestNonMeetingRegressions:
    """Test that non-meeting flows are unaffected by meeting agent integration"""
    
    @pytest.mark.asyncio
    async def test_lead_ingestion_unaffected(self, mock_zoho_client, mock_db_session,
                                           fsm_state_tracker, emoji_detector):
        """Test that lead ingestion still works exactly as before"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test standard lead ingestion flow (non-meeting)
        script = [
            {"u": "Hi, I'm interested in franchise opportunities"},
            {"a~": "Great to hear from you! I'm Andy"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888050", "test_lead_050")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Should handle as normal lead inquiry, not meeting booking
        response = trace.turns[0].agent_response.lower()
        assert "andy" in response or "franchise" in response or "opportunities" in response
        
        # Should not trigger meeting booking flow
        assert not any("schedule" in turn.agent_response.lower() or "meeting" in turn.agent_response.lower() 
                      for turn in trace.turns), "Should not trigger meeting booking for general inquiry"
        
        # Check database operations for lead creation
        db_operations = []
        for turn in trace.turns:
            db_operations.extend(turn.db_operations)
        
        # Should have lead-related operations, not booking operations
        lead_operations = [op for op in db_operations if "lead" in op.lower()]
        booking_operations = [op for op in db_operations if "booking" in op.lower()]
        
        assert len(booking_operations) == 0, "Should not create booking operations for general inquiry"
        
        print("✅ Lead Ingestion Unaffected: PASSED")
    
    @pytest.mark.asyncio
    async def test_kudosity_sms_behavior_intact(self, mock_zoho_client, mock_db_session,
                                              fsm_state_tracker, emoji_detector):
        """Test that Kudosity SMS behavior is unchanged"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test with KUDOSITY_SMS_ENABLED=false (should print to terminal/logs)
        with patch('app.core.config.settings.KUDOSITY_SMS_ENABLED', False):
            script = [
                {"u": "Tell me about franchise fees"},
                {"a~": "franchise fees"}
            ]
            
            with patch('builtins.print') as mock_print:
                trace = await harness.run_conversation_script(script, "+61940888051", "test_lead_051")
                
                # Should handle non-meeting query normally
                assert trace.has_no_emojis(), "No emojis should be present"
                
                response = trace.turns[0].agent_response.lower()
                assert "franchise" in response or "fees" in response, "Should handle franchise inquiry"
                
                # With KUDOSITY_SMS_ENABLED=false, should print instead of send
                # (Exact behavior depends on implementation)
        
        print("✅ Kudosity SMS Behavior Intact: PASSED")
    
    @pytest.mark.asyncio
    async def test_follow_up_scheduler_unchanged(self, mock_zoho_client, mock_db_session,
                                                fsm_state_tracker, emoji_detector):
        """Test that follow-up scheduler and timers work unchanged"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Mock follow-up scheduling
        with patch('app.agents.sms_assistant.AndySMSAssistant._schedule_follow_up') as mock_schedule:
            mock_schedule.return_value = True
            
            script = [
                {"u": "Thanks for the information"},
                {"a~": "You're welcome"}
            ]
            
            trace = await harness.run_conversation_script(script, "+61940888052", "test_lead_052")
            
            # Should handle thank you message normally
            assert trace.has_no_emojis(), "No emojis should be present"
            
            # Follow-up scheduling should work as before
            # (Exact behavior depends on implementation)
            response = trace.turns[0].agent_response
            assert len(response) > 0, "Should provide response to thank you"
        
        print("✅ Follow-up Scheduler Unchanged: PASSED")
    
    @pytest.mark.asyncio
    async def test_lead_status_updates_preserved(self, mock_zoho_client, mock_db_session,
                                                fsm_state_tracker, emoji_detector):
        """Test that lead status updates continue to work"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Mock lead status update
        with patch('app.agents.sms_assistant.AndySMSAssistant._update_lead_status') as mock_update:
            mock_update.return_value = True
            
            script = [
                {"u": "I'm very interested in the opportunity"},
                {"a~": "That's fantastic"}
            ]
            
            trace = await harness.run_conversation_script(script, "+61940888053", "test_lead_053")
            
            # Should handle interest expression normally
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            assert any(word in response for word in ["fantastic", "great", "excellent", "wonderful"]), \
                   "Should respond positively to interest"
            
            # Lead status updates should work as before
            # (Exact behavior depends on implementation)
        
        print("✅ Lead Status Updates Preserved: PASSED")
    
    @pytest.mark.asyncio
    async def test_rag_escalation_behavior_intact(self, mock_zoho_client, mock_db_session,
                                                 fsm_state_tracker, emoji_detector):
        """Test that RAG + escalation question bank behavior is intact"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Mock RAG query handling
        with patch('app.agents.sms_assistant.AndySMSAssistant._handle_rag_query') as mock_rag:
            mock_rag.return_value = "Based on our franchise documentation, the initial investment ranges from..."
            
            script = [
                {"u": "What's the initial investment required?"},
                {"a~": "initial investment ranges"}
            ]
            
            trace = await harness.run_conversation_script(script, "+***********", "test_lead_054")
            
            # Should handle RAG query normally
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            assert "investment" in response, "Should handle investment question via RAG"
            
            # Should not trigger meeting booking for factual questions
            assert not any("schedule" in turn.agent_response.lower() or "meeting" in turn.agent_response.lower() 
                          for turn in trace.turns), "Should not trigger meeting booking for RAG queries"
        
        # Test escalation question bank
        with patch('app.agents.sms_assistant.AndySMSAssistant._handle_escalation') as mock_escalation:
            mock_escalation.return_value = "That's a great question. Let me connect you with our specialist team."
            
            escalation_script = [
                {"u": "What are the specific legal requirements in Queensland?"},
                {"a~": "connect you with our specialist team"}
            ]
            
            escalation_trace = await harness.run_conversation_script(
                escalation_script, "+***********", "test_lead_055"
            )
            
            # Should handle escalation normally
            assert escalation_trace.has_no_emojis(), "No emojis should be present"
            
            escalation_response = escalation_trace.turns[0].agent_response.lower()
            assert "specialist team" in escalation_response or "connect" in escalation_response
        
        print("✅ RAG Escalation Behavior Intact: PASSED")
    
    @pytest.mark.asyncio
    async def test_conversation_flow_preservation(self, mock_zoho_client, mock_db_session,
                                                 fsm_state_tracker, emoji_detector):
        """Test that existing conversation flows are preserved"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test standard Andy workflow stages
        workflow_script = [
            {"u": "Hi there"},
            {"a~": "Andy"},  # Introduction
            {"u": "I work in marketing"},
            {"a~": "marketing"},  # Work background
            {"u": "I want to be my own boss"},
            {"a~": "own boss"},  # Motivation
            {"u": "I have about 100k to invest"},
            {"a~": "investment"}  # Budget discussion
        ]
        
        trace = await harness.run_conversation_script(workflow_script, "+61940888056", "test_lead_056")
        
        # Should follow normal Andy workflow
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Each response should be contextually appropriate
        responses = [turn.agent_response.lower() for turn in trace.turns]
        
        # Should handle each stage appropriately
        assert any("andy" in resp for resp in responses[:1]), "Should introduce Andy"
        assert any("marketing" in resp for resp in responses[1:2]), "Should acknowledge work background"
        assert any("boss" in resp for resp in responses[2:3]), "Should acknowledge motivation"
        assert any("investment" in resp or "100k" in resp for resp in responses[3:4]), "Should discuss budget"
        
        # Should not automatically trigger meeting booking
        meeting_triggers = sum(1 for resp in responses if "schedule" in resp or "meeting" in resp)
        assert meeting_triggers <= 1, "Should not automatically trigger meeting booking in normal workflow"
        
        print("✅ Conversation Flow Preservation: PASSED")
    
    @pytest.mark.asyncio
    async def test_andy_personality_unchanged(self, mock_zoho_client, mock_db_session,
                                            fsm_state_tracker, emoji_detector):
        """Test that Andy's personality and tone remain unchanged"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test various personality aspects
        personality_tests = [
            ("How are you today?", ["good", "great", "andy"]),
            ("What's your name?", ["andy"]),
            ("Tell me about yourself", ["andy", "help", "franchise"]),
            ("Thanks for your help", ["welcome", "pleasure", "happy"])
        ]
        
        for i, (question, expected_elements) in enumerate(personality_tests):
            script = [
                {"u": question},
                {"a~": "andy"}  # Should maintain Andy personality
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088805{i+7}", f"test_lead_05{i+7}"
            )
            
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            
            # Should maintain Andy's personality
            assert any(element in response for element in expected_elements), \
                   f"Response to '{question}' should contain one of {expected_elements}"
            
            # Should not trigger meeting booking for personality questions
            assert not any("schedule" in resp or "book" in resp for resp in [response]), \
                   "Personality questions should not trigger meeting booking"
        
        print("✅ Andy Personality Unchanged: PASSED")
    
    @pytest.mark.asyncio
    async def test_error_handling_preservation(self, mock_zoho_client, mock_db_session,
                                             fsm_state_tracker, emoji_detector):
        """Test that error handling patterns are preserved"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test error scenarios that should be handled gracefully
        error_scenarios = [
            ("", "help"),  # Empty message
            ("???", "help"),  # Unclear message
            ("asdfghjkl", "help"),  # Nonsense message
        ]
        
        for i, (error_input, expected_type) in enumerate(error_scenarios):
            script = [
                {"u": error_input},
                {"a~": "help"}  # Should handle gracefully
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088806{i}", f"test_lead_06{i}"
            )
            
            # Should handle errors gracefully
            assert len(trace.turns) > 0, "Should respond to error input"
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response
            assert len(response) > 0, "Should provide meaningful response to error input"
            assert "error" not in response.lower(), "Should not expose errors to user"
            
            # Should not crash or trigger unintended flows
            assert not any("schedule" in resp.lower() for resp in [response]), \
                   "Error handling should not trigger meeting booking"
        
        print("✅ Error Handling Preservation: PASSED")
    
    @pytest.mark.asyncio
    async def test_feature_flag_behavior_unchanged(self, mock_zoho_client, mock_db_session,
                                                  fsm_state_tracker, emoji_detector):
        """Test that feature flag behavior is unchanged"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test with MEETING_AGENT_ENABLED=false
        with patch('app.core.config.settings.MEETING_AGENT_ENABLED', False):
            script = [
                {"u": "I want to schedule a meeting"},
                {"a~": "help"}  # Should handle without meeting agent
            ]
            
            trace = await harness.run_conversation_script(script, "+61940888061", "test_lead_061")
            
            # Should handle gracefully without meeting agent
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response
            assert len(response) > 0, "Should provide response even with meeting agent disabled"
            
            # Should not create actual booking
            assert not trace.booking_created, "Should not create booking with meeting agent disabled"
        
        print("✅ Feature Flag Behavior Unchanged: PASSED")
