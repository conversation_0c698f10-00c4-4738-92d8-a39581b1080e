# Meeting Agent Test Suite

## Overview

Comprehensive automated test suite for <PERSON>'s meeting booking functionality with strict validation of all 11 meeting booking scenarios, timezone handling, loop prevention, and regression testing.

## Test Structure

### Core Test Files

- **`conftest.py`** - Global fixtures with frozen time (2025-08-29T12:00:00+05:30), mocked Zoho client, seeded availability
- **`helpers/conversation_harness.py`** - Conversation driver with FSM state tracking and side effect capture
- **`test_meeting_cases.py`** - All 11 primary meeting booking scenarios with exact transcript validation
- **`test_reschedule_cancel.py`** - Reschedule and cancel flows with provider + DB assertions
- **`test_timezones_and_parsing.py`** - Timezone conversions, date/time parsing, calendar correctness
- **`test_stability_and_loops.py`** - Loop prevention, backoff behavior, provider failure handling
- **`test_regressions_non_meeting.py`** - Non-meeting flow regression tests

### Test Categories

#### 1. Primary Meeting Cases (11 Scenarios)
- **Case 1**: Clear Date & Time → Direct booking
- **Case 2**: Range of Dates → Clarification needed
- **Case 3**: Time Only → Need date clarification
- **Case 4**: Anytime Tomorrow → Show available slots
- **Case 5**: Weekday Availability → Calendar-aware slot display
- **Case 6**: Date Only → Time slot presentation
- **Case 9**: Next Week → Weekday options with proper date resolution
- **Case 10**: User Defers → Context parking with extended TTL
- **Case 11**: Holiday/No Slots → Graceful fallback with alternatives

#### 2. Reschedule & Cancel Flows
- Complete reschedule workflow after initial booking
- Cancellation with confirmation requirements
- Provider failure handling during reschedule/cancel
- Multiple reschedule operations
- Graceful handling when no existing booking

#### 3. Timezone & Parsing Tests
- UTC storage with local timezone display
- Weekday/date disambiguation and calendar correctness
- 24-hour time format parsing
- Noon/midnight references
- Relative date expressions (today, tomorrow, next week)
- Date range parsing ("3rd or 4th")
- "Anytime" expression handling

#### 4. Stability & Loop Prevention
- Repeated input detection and handling
- Clarification turn capping (max 2)
- Provider failure with retry backoff
- Context corruption recovery
- Concurrent conversation isolation
- Malformed input handling
- Session timeout recovery

#### 5. Non-Meeting Regression Tests
- Lead ingestion unchanged
- Kudosity SMS behavior intact
- Follow-up scheduler preservation
- RAG + escalation question bank behavior
- Andy personality and conversation flow preservation
- Error handling patterns maintained
- Feature flag behavior unchanged

## Mock Configuration

### Seeded Availability Data
```python
{
    "2025-08-30": ["09:30", "12:00", "15:00"],  # Saturday
    "2025-09-01": ["10:00", "13:00", "16:30"],  # Monday
    "2025-09-03": ["11:00", "13:00", "15:00"],  # Wednesday
    "2025-09-05": ["09:00", "11:30", "14:00"]   # Friday
}
```

### Holidays/Closed Days
- `2025-08-31` (Sunday) - No availability

### Frozen Time
- **Test Time**: 2025-08-29T12:00:00+05:30 (Friday noon, Asia/Kolkata)
- **Tomorrow**: 2025-08-30 (Saturday)
- **Next Monday**: 2025-09-01

## Key Assertions

### Style Requirements
- ✅ **No Emojis**: All responses must be emoji-free
- ✅ **Concise Human-like Tone**: Natural conversational style
- ✅ **Local Timezone Display**: Confirmations show user's local time
- ✅ **Calendar Correctness**: Weekday/date alignment matches real 2025 calendar

### Content Requirements
- ✅ **Final Confirmations**: Restate date with month name and AM/PM
- ✅ **Meeting Links**: Include meeting links in booking confirmations
- ✅ **Graceful Failures**: Handle provider failures without exposing errors

### Database Requirements
- ✅ **UTC Storage**: All timestamps stored in UTC
- ✅ **Booking Records**: Complete booking records with provider IDs
- ✅ **Audit Trail**: User prompts and agent responses logged
- ✅ **Idempotency**: Prevent duplicate bookings with idempotency keys

### FSM Requirements
- ✅ **State Transitions**: Proper FSM state progression
- ✅ **Loop Prevention**: Max 2 clarification attempts
- ✅ **Context Persistence**: Redis-based context storage with TTL
- ✅ **Recovery**: Graceful recovery from corrupted contexts

## Running Tests

### Quick Test Run
```bash
# Run all meeting agent tests
python run_meeting_agent_tests.py

# Run specific test category
pytest tests/meeting_agent/test_meeting_cases.py -v
pytest tests/meeting_agent/test_timezones_and_parsing.py -v
```

### Detailed Test Run with Coverage
```bash
# Full suite with coverage
pytest tests/meeting_agent/ --cov=app.meeting_agent --cov=app.agents.sms_assistant --cov-report=html

# Specific scenario with verbose output
pytest tests/meeting_agent/test_meeting_cases.py::TestMeetingBookingCases::test_case_1_clear_date_time -v -s
```

### CI/CD Integration
```bash
# CI-friendly run with XML output
pytest tests/meeting_agent/ --junitxml=junit.xml --cov-report=xml --maxfail=5 --disable-warnings
```

## Test Fixtures

### Global Fixtures (conftest.py)
- `frozen_clock` - Frozen time at 2025-08-29T12:00:00+05:30
- `mock_zoho_client` - Mocked Zoho Bookings with seeded availability
- `mock_db_session` - Database session mock with operation tracking
- `fsm_state_tracker` - FSM state transition tracking
- `emoji_detector` - Emoji detection in responses
- `calendar_validator` - Calendar correctness validation

### Test Utilities
- `ConversationHarness` - Drive Andy pipeline with scripted conversations
- `ConversationTrace` - Complete conversation history with assertions
- `ConversationTurn` - Individual conversation turn with metadata

## Expected Outputs

### Successful Test Run
```
🎉 ALL TESTS PASSED! Andy's meeting booking system is ready for production.

✅ VALIDATION CHECKLIST:
   ✅ All 11 meeting booking cases implemented and tested
   ✅ Timezone handling and calendar correctness verified
   ✅ Loop prevention and stability confirmed
   ✅ No regressions in non-meeting flows
   ✅ No emojis in any responses
   ✅ Database writes and idempotency validated
   ✅ Provider failure handling tested
   ✅ Reschedule and cancel flows working
```

### Test Artifacts
- `tests/_artifacts/meeting_transcripts/run_YYYYMMDD_HHMMSS/`
  - `test_report.json` - Comprehensive test results
  - `*.log` - Individual test suite outputs
  - `htmlcov/` - Coverage reports
  - `junit.xml` - CI-compatible test results

## Debugging Failed Tests

### Common Issues
1. **Calendar Correctness**: Verify 2025 calendar dates match expected weekdays
2. **Timezone Handling**: Check UTC storage vs local display
3. **Mock Configuration**: Ensure Zoho client mocks return expected data
4. **FSM State Tracking**: Verify state transitions are properly tracked
5. **Emoji Detection**: Check for accidental emoji inclusion in responses

### Debug Commands
```bash
# Run single test with full output
pytest tests/meeting_agent/test_meeting_cases.py::TestMeetingBookingCases::test_case_1_clear_date_time -v -s --tb=long

# Check specific fixture behavior
pytest tests/meeting_agent/test_meeting_cases.py -v -s --setup-show

# Run with pdb debugging
pytest tests/meeting_agent/test_meeting_cases.py --pdb
```

## Maintenance

### Adding New Test Cases
1. Add test method to appropriate test class
2. Use `ConversationHarness` for conversation simulation
3. Include all required assertions (emojis, FSM states, DB operations)
4. Update seeded availability data if needed
5. Add to test runner script if critical

### Updating Mock Data
1. Modify `seeded_availability` in `conftest.py`
2. Update `holidays_or_closed` list for new closed dates
3. Adjust frozen time if testing different date ranges
4. Verify calendar correctness for new dates

### Performance Considerations
- Tests use mocked external services (no real API calls)
- Frozen time ensures consistent test results
- Redis mocking prevents external dependencies
- Database mocking eliminates I/O overhead

---

**This test suite ensures Andy's meeting booking functionality works flawlessly with comprehensive validation of all scenarios, edge cases, and regression prevention.**
