"""
Meeting Agent Test Configuration
Global fixtures: freezegun clock, TZ, db session, mocked Zoho client, seeded availability, log capture
"""

import pytest
import asyncio
import json
import pytz
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from freezegun import freeze_time
import structlog
from io import String<PERSON>

from app.services.zoho_bookings_service import ZohoBookingsService, BookingSlot, BookingResult
from app.meeting_agent.fsm import MeetingBookingFSM, MeetingState
from app.core.database.connection import get_db
from app.core.redis_client import get_redis_client


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
def frozen_clock():
    """Freeze time to 2025-08-29T12:00:00+05:30 for consistent testing"""
    with freeze_time("2025-08-29 12:00:00", tz_offset=5.5):
        yield datetime(2025, 8, 29, 12, 0, 0, tzinfo=pytz.timezone('Asia/Kolkata'))


@pytest.fixture
def default_timezone():
    """Default timezone for testing"""
    return "Asia/Kolkata"


@pytest.fixture
def test_phone_number():
    """Test phone number for consistent testing"""
    return "+61940888001"


@pytest.fixture
def test_lead_id():
    """Test lead ID for consistent testing"""
    return "test_lead_12345"


@pytest.fixture
def mock_redis():
    """Mock Redis client with conversation context storage"""
    mock_redis = MagicMock()
    mock_redis.get.return_value = None
    mock_redis.setex.return_value = True
    mock_redis.delete.return_value = True
    mock_redis.exists.return_value = False
    
    # Store for context data
    mock_redis._data = {}
    
    def mock_setex(key, ttl, value):
        mock_redis._data[key] = value
        return True
    
    def mock_get(key):
        return mock_redis._data.get(key)
    
    def mock_exists(key):
        return key in mock_redis._data
    
    mock_redis.setex.side_effect = mock_setex
    mock_redis.get.side_effect = mock_get
    mock_redis.exists.side_effect = mock_exists
    
    with patch('app.core.redis_client.get_redis_client', return_value=mock_redis):
        yield mock_redis


@pytest.fixture
def seeded_availability():
    """Seeded availability data for mocked Zoho client"""
    return {
        "2025-08-30": [
            {"time": "09:30", "staff": "Andy", "staff_id": "staff_1"},
            {"time": "12:00", "staff": "Andy", "staff_id": "staff_1"},
            {"time": "15:00", "staff": "Frank", "staff_id": "staff_2"}
        ],
        "2025-09-01": [
            {"time": "10:00", "staff": "Andy", "staff_id": "staff_1"},
            {"time": "13:00", "staff": "Frank", "staff_id": "staff_2"},
            {"time": "16:30", "staff": "Saumil", "staff_id": "staff_3"}
        ],
        "2025-09-03": [
            {"time": "11:00", "staff": "Andy", "staff_id": "staff_1"},
            {"time": "13:00", "staff": "Frank", "staff_id": "staff_2"},
            {"time": "15:00", "staff": "Saumil", "staff_id": "staff_3"}
        ],
        "2025-09-05": [
            {"time": "09:00", "staff": "Andy", "staff_id": "staff_1"},
            {"time": "11:30", "staff": "Frank", "staff_id": "staff_2"},
            {"time": "14:00", "staff": "Saumil", "staff_id": "staff_3"}
        ]
    }


@pytest.fixture
def holidays_or_closed():
    """Dates that are holidays or closed"""
    return ["2025-08-31"]  # Sunday


@pytest.fixture
def mock_zoho_client(seeded_availability, holidays_or_closed):
    """Mock Zoho Bookings client with seeded availability"""
    mock_client = AsyncMock(spec=ZohoBookingsService)
    
    # Mock get_access_token
    mock_client.get_access_token.return_value = "mock_access_token_12345"
    
    # Mock list_availability
    async def mock_list_availability(date_local, tz="Asia/Kolkata"):
        date_str = date_local.strftime("%Y-%m-%d")
        
        # Check if it's a holiday/closed day
        if date_str in holidays_or_closed:
            return []
        
        # Get seeded availability
        day_slots = seeded_availability.get(date_str, [])
        
        # Convert to BookingSlot objects
        slots = []
        for slot_data in day_slots:
            # Parse time
            hour, minute = map(int, slot_data["time"].split(":"))
            start_time = datetime(date_local.year, date_local.month, date_local.day, hour, minute)
            start_time = pytz.timezone(tz).localize(start_time)
            end_time = start_time + timedelta(minutes=30)
            
            slot = BookingSlot(
                staff_id=slot_data["staff_id"],
                staff_name=slot_data["staff"],
                start_time=start_time,
                end_time=end_time,
                service_id="lead_meeting_service",
                service_name="Lead Meeting",
                duration_minutes=30,
                booking_url=f"https://bookings.zoho.com/book/{slot_data['staff_id']}"
            )
            slots.append(slot)
        
        return slots
    
    # Mock the actual methods from ZohoBookingsService
    mock_client.get_available_slots_for_date.side_effect = mock_list_availability
    mock_client.get_available_slots.side_effect = lambda date_from, date_to, **kwargs: mock_list_availability(date_from)
    mock_client.get_next_available_slots.side_effect = lambda **kwargs: mock_list_availability(datetime(2025, 8, 30))
    
    # Mock book_appointment
    async def mock_book_appointment(slot, customer_name, customer_email, customer_phone, **kwargs):
        return BookingResult(
            success=True,
            booking_id=f"booking_{datetime.now().timestamp()}",
            booking_url="https://bookings.zoho.com/booking/12345",
            meeting_link="https://zoom.us/j/123456789",
            staff_name=getattr(slot, 'staff_name', 'Andy')
        )

    # Mock book_appointment_with_exact_slot
    async def mock_book_appointment_with_exact_slot(slot, lead_id, phone_number=None):
        return BookingResult(
            success=True,
            booking_id=f"booking_{datetime.now().timestamp()}",
            booking_url="https://bookings.zoho.com/booking/12345",
            meeting_link="https://zoom.us/j/123456789",
            staff_name=getattr(slot, 'staff_name', 'Andy')
        )

    # Mock book_appointment_for_lead
    async def mock_book_appointment_for_lead(lead_id, preferred_start_time, timezone="Asia/Kolkata", service_type="lead_meeting"):
        return BookingResult(
            success=True,
            booking_id=f"booking_{datetime.now().timestamp()}",
            booking_url="https://bookings.zoho.com/booking/12345",
            meeting_link="https://zoom.us/j/123456789",
            staff_name="Andy"
        )

    # Mock book_slot_for_meeting_agent
    async def mock_book_slot_for_meeting_agent(slot, lead_data, timezone="Asia/Kolkata"):
        return BookingResult(
            success=True,
            booking_id=f"booking_{datetime.now().timestamp()}",
            booking_url="https://bookings.zoho.com/booking/12345",
            meeting_link="https://zoom.us/j/123456789",
            staff_name=getattr(slot, 'staff_name', 'Andy')
        )

    mock_client.book_appointment.side_effect = mock_book_appointment
    mock_client.book_appointment_with_exact_slot.side_effect = mock_book_appointment_with_exact_slot
    mock_client.book_appointment_for_lead.side_effect = mock_book_appointment_for_lead
    mock_client.book_slot_for_meeting_agent.side_effect = mock_book_slot_for_meeting_agent
    
    # Mock reschedule_booking (if it exists)
    async def mock_reschedule_booking(booking_id, new_slot, **kwargs):
        return BookingResult(
            success=True,
            booking_id=booking_id,
            booking_url="https://bookings.zoho.com/booking/12345",
            meeting_link="https://zoom.us/j/123456789",
            staff_name=getattr(new_slot, 'staff_name', 'Andy') if new_slot else 'Andy'
        )

    # Add reschedule method if it exists
    if hasattr(mock_client, 'reschedule_booking'):
        mock_client.reschedule_booking.side_effect = mock_reschedule_booking
    
    # Mock cancel_booking
    async def mock_cancel_booking(booking_id, reason=None):
        return True  # cancel_booking returns bool, not BookingResult

    mock_client.cancel_booking.side_effect = mock_cancel_booking
    
    # Patch the ZohoBookingsService wherever it's used
    with patch('app.services.zoho_bookings_service.ZohoBookingsService', return_value=mock_client):
        yield mock_client


@pytest.fixture
def mock_db_session():
    """Mock database session for testing"""
    mock_session = AsyncMock()
    
    # Mock database operations
    mock_session.add.return_value = None
    mock_session.commit.return_value = None
    mock_session.refresh.return_value = None
    mock_session.execute.return_value = AsyncMock()
    mock_session.scalar_one_or_none.return_value = None
    
    # Store for tracking database operations
    mock_session._added_objects = []
    mock_session._committed = False
    
    def mock_add(obj):
        mock_session._added_objects.append(obj)
    
    async def mock_commit():
        mock_session._committed = True
    
    mock_session.add.side_effect = mock_add
    mock_session.commit.side_effect = mock_commit
    
    with patch('app.core.database.connection.get_db') as mock_get_db:
        async def mock_db_generator():
            yield mock_session
        
        mock_get_db.return_value = mock_db_generator()
        yield mock_session


@pytest.fixture
def log_capture():
    """Capture logs for testing"""
    log_stream = StringIO()
    handler = structlog.testing.LogCapture()
    
    # Configure structlog for testing
    structlog.configure(
        processors=[
            handler,
            structlog.processors.JSONRenderer()
        ],
        wrapper_class=structlog.make_filtering_bound_logger(20),  # INFO level
        logger_factory=structlog.testing.TestingLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    yield handler


@pytest.fixture
def fsm_state_tracker():
    """Track FSM state transitions for testing"""
    state_transitions = []
    
    original_transition = MeetingBookingFSM.transition_to if hasattr(MeetingBookingFSM, 'transition_to') else None
    
    def track_transition(self, from_state, to_state, reason=None):
        state_transitions.append({
            "from": from_state.value if hasattr(from_state, 'value') else str(from_state),
            "to": to_state.value if hasattr(to_state, 'value') else str(to_state),
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        })
        if original_transition:
            return original_transition(self, from_state, to_state, reason)
    
    if hasattr(MeetingBookingFSM, 'transition_to'):
        with patch.object(MeetingBookingFSM, 'transition_to', track_transition):
            yield state_transitions
    else:
        yield state_transitions


@pytest.fixture
def emoji_detector():
    """Detect emojis in responses"""
    emoji_chars = (
        "😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🥸🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾"
        "❤️🧡💛💚💙💜🖤🤍🤎💔❣️💕💞💓💗💖💘💝💟☮️✝️☪️🕉️☸️✡️🔯🕎☯️☦️🛐⛎♈♉♊♋♌♍♎♏♐♑♒♓🆔⚛️🉑☢️☣️📴📳🈶🈚🈸🈺🈷️✴️🆚💮🉐㊙️㊗️🈴🈵🈹🈲🅰️🅱️🆎🆑🅾️🆘❌⭕🛑⛔📛🚫💯💢♨️🚷🚯🚳🚱🔞📵🚭❗❕❓❔‼️⁉️🔅🔆〽️⚠️🚸🔱⚜️🔰♻️✅🈯💹❇️✳️❎🌐💠Ⓜ️🌀💤🏧🚾♿🅿️🈳🈂️🛂🛃🛄🛅🚹🚺🚼🚻🚮🎦📶🈁🔣ℹ️🔤🔡🔠🆖🆗🆙🆒🆕🆓0️⃣1️⃣2️⃣3️⃣4️⃣5️⃣6️⃣7️⃣8️⃣9️⃣🔟🔢#️⃣*️⃣⏏️▶️⏸️⏯️⏹️⏺️⏭️⏮️⏩⏪⏫⏬◀️🔼🔽➡️⬅️⬆️⬇️↗️↘️↙️↖️↕️↔️↪️↩️⤴️⤵️🔀🔁🔂🔄🔃🎵🎶➕➖➗✖️♾️💲💱™️©️®️👁️‍🗨️🔚🔙🔛🔝🔜〰️➰➿✔️☑️🔘🔴🟠🟡🟢🔵🟣⚫⚪🟤🔺🔻🔸🔹🔶🔷🔳🔲▪️▫️◾◽◼️◻️🟥🟧🟨🟩🟦🟪⬛⬜🟫🔈🔇🔉🔊🔔🔕📣📢💬💭🗯️♠️♣️♥️♦️🃏🎴🀄🕐🕑🕒🕓🕔🕕🕖🕗🕘🕙🕚🕛🕧🕐🕜🕝🕞🕟🕠🕡🕢🕣🕤🕥🕦🕧"
    )
    
    def has_emoji(text):
        return any(char in text for char in emoji_chars)
    
    return has_emoji


@pytest.fixture
def calendar_validator():
    """Validate calendar correctness for 2025"""
    def validate_weekday(date_str, expected_weekday):
        """Validate that a date string matches the expected weekday"""
        try:
            if isinstance(date_str, str):
                # Try to parse various date formats
                for fmt in ["%Y-%m-%d", "%d %B", "%B %d", "%d %B %Y"]:
                    try:
                        if fmt in ["%d %B", "%B %d"]:
                            date_str_with_year = f"{date_str} 2025"
                            dt = datetime.strptime(date_str_with_year, f"{fmt} %Y")
                        else:
                            dt = datetime.strptime(date_str, fmt)
                        break
                    except ValueError:
                        continue
                else:
                    return False
            else:
                dt = date_str
            
            weekday_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
            actual_weekday = weekday_names[dt.weekday()]
            
            return actual_weekday.lower() == expected_weekday.lower()
        except Exception:
            return False
    
    return validate_weekday
