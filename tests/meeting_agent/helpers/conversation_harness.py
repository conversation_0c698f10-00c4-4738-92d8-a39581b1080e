"""
Conversation Harness
Drive Andy pipeline with scripted user turns; capture agent messages, FSM state trace, and side effects
"""

import asyncio
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime

from app.agents.sms_assistant import Andy<PERSON>SAssistant


@dataclass
class ConversationTurn:
    """Represents a single turn in the conversation"""
    user_message: str
    agent_response: str
    timestamp: datetime
    fsm_state: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None
    db_operations: List[str] = field(default_factory=list)
    side_effects: List[str] = field(default_factory=list)


@dataclass
class ConversationTrace:
    """Complete trace of a conversation"""
    phone_number: str
    lead_id: str
    turns: List[ConversationTurn] = field(default_factory=list)
    fsm_transitions: List[Dict[str, Any]] = field(default_factory=list)
    db_writes: List[Dict[str, Any]] = field(default_factory=list)
    final_state: Optional[str] = None
    booking_created: bool = False
    booking_id: Optional[str] = None
    meeting_link: Optional[str] = None
    
    def get_agent_responses(self) -> List[str]:
        """Get all agent responses"""
        return [turn.agent_response for turn in self.turns]
    
    def get_user_messages(self) -> List[str]:
        """Get all user messages"""
        return [turn.user_message for turn in self.turns]
    
    def has_fsm_states(self, expected_states: List[str]) -> bool:
        """Check if FSM went through expected states"""
        actual_states = [t.get("to", "") for t in self.fsm_transitions]
        return all(state in actual_states for state in expected_states)
    
    def has_no_emojis(self) -> bool:
        """Check that no agent responses contain emojis"""
        emoji_chars = "😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🥸🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾"
        
        for response in self.get_agent_responses():
            if any(char in response for char in emoji_chars):
                return False
        return True
    
    def contains_text_in_responses(self, text_patterns: List[str]) -> bool:
        """Check if responses contain expected text patterns"""
        responses_text = " ".join(self.get_agent_responses()).lower()
        return all(pattern.lower() in responses_text for pattern in text_patterns)


class ConversationHarness:
    """
    Harness for driving Andy's meeting booking conversations with comprehensive tracking
    """
    
    def __init__(self, mock_zoho_client=None, mock_db_session=None, 
                 fsm_state_tracker=None, log_capture=None):
        self.mock_zoho_client = mock_zoho_client
        self.mock_db_session = mock_db_session
        self.fsm_state_tracker = fsm_state_tracker or []
        self.log_capture = log_capture
        
        # Initialize Andy SMS Assistant
        self.andy_assistant = AndySMSAssistant()

        # Meeting agent functionality is integrated into Andy SMS Assistant
        self.meeting_agent = None
    
    async def run_conversation_script(self, script: List[Dict[str, str]], 
                                    phone_number: str, lead_id: str) -> ConversationTrace:
        """
        Run a scripted conversation and capture all interactions
        
        Args:
            script: List of conversation turns with 'u' (user) and 'a~' (expected agent pattern)
            phone_number: Phone number for the conversation
            lead_id: Lead ID for the conversation
        
        Returns:
            ConversationTrace with complete interaction history
        """
        trace = ConversationTrace(phone_number=phone_number, lead_id=lead_id)
        
        for i, turn_script in enumerate(script):
            if 'u' in turn_script:
                # User message
                user_message = turn_script['u']
                
                # Send message through Andy's pipeline
                agent_response = await self._send_message_to_andy(
                    phone_number=phone_number,
                    message=user_message,
                    lead_id=lead_id
                )
                
                # Capture current FSM state
                current_state = await self._get_current_fsm_state(phone_number)
                
                # Capture context data
                context_data = await self._get_conversation_context(phone_number)
                
                # Create conversation turn
                turn = ConversationTurn(
                    user_message=user_message,
                    agent_response=agent_response,
                    timestamp=datetime.now(),
                    fsm_state=current_state,
                    context_data=context_data,
                    db_operations=self._capture_db_operations(),
                    side_effects=self._capture_side_effects()
                )
                
                trace.turns.append(turn)
                
                # Check if booking was created
                if context_data and context_data.get('booking_id'):
                    trace.booking_created = True
                    trace.booking_id = context_data.get('booking_id')
                    trace.meeting_link = context_data.get('meeting_link')
                
                print(f"Turn {i+1}:")
                print(f"  User: {user_message}")
                print(f"  Andy: {agent_response}")
                print(f"  State: {current_state}")
                print()
                
            elif 'a~' in turn_script:
                # Expected agent pattern (for validation, not execution)
                expected_pattern = turn_script['a~']
                if trace.turns:
                    last_response = trace.turns[-1].agent_response
                    if expected_pattern.lower() not in last_response.lower():
                        print(f"⚠️  Expected pattern '{expected_pattern}' not found in response: '{last_response}'")
            
            elif 'mock' in turn_script:
                # Mock configuration (for provider failure simulation)
                mock_instruction = turn_script['mock']
                await self._configure_mock_behavior(mock_instruction)
        
        # Capture final state and FSM transitions
        trace.final_state = await self._get_current_fsm_state(phone_number)
        trace.fsm_transitions = self.fsm_state_tracker.copy() if self.fsm_state_tracker else []
        trace.db_writes = self._capture_all_db_writes()
        
        return trace
    
    async def _send_message_to_andy(self, phone_number: str, message: str, lead_id: str) -> str:
        """Send message through Andy's SMS assistant pipeline"""
        try:
            # Create Andy state
            state = {
                "phone_number": phone_number,
                "message": message,
                "lead_id": lead_id,
                "lead_context": {"lead_id": lead_id},
                "timestamp": int(time.time())
            }
            
            # Process through Andy's workflow
            response = await self.andy_assistant.process_sms(
                phone_number=phone_number,
                message=message,
                lead_id=lead_id
            )
            
            # Extract response text
            if isinstance(response, dict):
                return response.get("response", response.get("message", str(response)))
            else:
                return str(response)
                
        except Exception as e:
            print(f"Error in Andy pipeline: {e}")
            return f"Error: {str(e)}"
    
    async def _get_current_fsm_state(self, phone_number: str) -> Optional[str]:
        """Get current FSM state for the conversation"""
        try:
            if self.meeting_agent and hasattr(self.meeting_agent, 'get_conversation_state'):
                state = await self.meeting_agent.get_conversation_state(phone_number)
                return state.value if hasattr(state, 'value') else str(state)
            return None
        except Exception:
            return None
    
    async def _get_conversation_context(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """Get conversation context data"""
        try:
            if self.meeting_agent and hasattr(self.meeting_agent, 'get_context'):
                context = await self.meeting_agent.get_context(phone_number)
                return context.to_dict() if hasattr(context, 'to_dict') else context
            return None
        except Exception:
            return None
    
    def _capture_db_operations(self) -> List[str]:
        """Capture database operations from mock session"""
        operations = []
        if self.mock_db_session:
            if hasattr(self.mock_db_session, '_added_objects'):
                for obj in self.mock_db_session._added_objects:
                    operations.append(f"ADD: {type(obj).__name__}")
            if hasattr(self.mock_db_session, '_committed') and self.mock_db_session._committed:
                operations.append("COMMIT")
        return operations
    
    def _capture_side_effects(self) -> List[str]:
        """Capture side effects like SMS sends, API calls"""
        effects = []
        
        # Capture Zoho API calls
        if self.mock_zoho_client:
            for method_name in ['list_availability', 'create_booking', 'reschedule_booking', 'cancel_booking']:
                if hasattr(self.mock_zoho_client, method_name):
                    method = getattr(self.mock_zoho_client, method_name)
                    if hasattr(method, 'call_count') and method.call_count > 0:
                        effects.append(f"ZOHO_API: {method_name} called {method.call_count} times")
        
        return effects
    
    def _capture_all_db_writes(self) -> List[Dict[str, Any]]:
        """Capture all database write operations"""
        writes = []
        if self.mock_db_session and hasattr(self.mock_db_session, '_added_objects'):
            for obj in self.mock_db_session._added_objects:
                writes.append({
                    "type": type(obj).__name__,
                    "data": getattr(obj, '__dict__', {}),
                    "timestamp": datetime.now().isoformat()
                })
        return writes
    
    async def _configure_mock_behavior(self, instruction: str):
        """Configure mock behavior for failure simulation"""
        if "intermittent 500" in instruction and self.mock_zoho_client:
            # Configure intermittent failures
            original_method = self.mock_zoho_client.list_availability
            call_count = 0
            
            async def failing_method(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count <= 2:
                    raise Exception("HTTP 500: Internal Server Error")
                return await original_method(*args, **kwargs)
            
            self.mock_zoho_client.list_availability.side_effect = failing_method
        
        elif "intermittent 502" in instruction and self.mock_zoho_client:
            # Configure booking failures
            original_method = self.mock_zoho_client.create_booking
            call_count = 0
            
            async def failing_booking(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count == 1:
                    raise Exception("HTTP 502: Bad Gateway")
                return await original_method(*args, **kwargs)
            
            self.mock_zoho_client.create_booking.side_effect = failing_booking
    
    def assert_conversation_flow(self, trace: ConversationTrace, 
                               expected_patterns: List[str],
                               expected_fsm_states: List[str] = None,
                               should_create_booking: bool = False,
                               should_have_no_emojis: bool = True):
        """Assert conversation flow meets expectations"""
        
        # Check response patterns
        if expected_patterns:
            assert trace.contains_text_in_responses(expected_patterns), \
                f"Expected patterns {expected_patterns} not found in responses: {trace.get_agent_responses()}"
        
        # Check FSM states
        if expected_fsm_states:
            assert trace.has_fsm_states(expected_fsm_states), \
                f"Expected FSM states {expected_fsm_states} not found in transitions: {trace.fsm_transitions}"
        
        # Check booking creation
        if should_create_booking:
            assert trace.booking_created, "Expected booking to be created but none was found"
            assert trace.booking_id, "Expected booking_id but none was found"
        
        # Check no emojis
        if should_have_no_emojis:
            assert trace.has_no_emojis(), f"Found emojis in responses: {trace.get_agent_responses()}"
        
        print("✅ All conversation flow assertions passed")
