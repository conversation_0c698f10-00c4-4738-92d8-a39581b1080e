"""
Timezone and Parsing Tests
Tests for timezone conversions, weekday/date disambiguations, 'anytime', ranges, 24h, noon/midnight, DST edges
"""

import pytest
from datetime import datetime, time
import pytz
from tests.meeting_agent.helpers.conversation_harness import ConversationHarness


class TestTimezoneHandling:
    """Test timezone conversions and date/time parsing"""
    
    @pytest.mark.asyncio
    async def test_utc_storage_local_display(self, mock_zoho_client, mock_db_session,
                                           fsm_state_tracker, calendar_validator):
        """Test that timestamps are stored in UTC but displayed in local timezone"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "I want to schedule a meeting."},
            {"u": "1 September at 2:30 PM."},
            {"a~": "Your meeting has been booked for 1 September at 2:30 PM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888030", "test_lead_030")
        
        # Check that confirmation shows local time
        final_response = trace.turns[-1].agent_response
        assert "2:30 pm" in final_response.lower(), "Should display local time in confirmation"
        assert "1 september" in final_response.lower(), "Should display local date"
        
        # Check database writes contain UTC timestamps
        if trace.db_writes:
            for write in trace.db_writes:
                if 'timestamp' in write.get('data', {}):
                    # Should be UTC timestamp
                    timestamp_str = write['data']['timestamp']
                    assert 'Z' in timestamp_str or '+00:00' in timestamp_str, "DB timestamps should be UTC"
        
        print("✅ UTC Storage Local Display: PASSED")
    
    @pytest.mark.asyncio
    async def test_weekday_date_disambiguation(self, mock_zoho_client, mock_db_session,
                                             fsm_state_tracker, calendar_validator):
        """Test weekday/date disambiguation and calendar correctness"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test various weekday references
        test_cases = [
            ("Monday", "1 September", "Monday"),
            ("Tuesday", "2 September", "Tuesday"),
            ("Wednesday", "3 September", "Wednesday"),
            ("Thursday", "4 September", "Thursday"),
            ("Friday", "5 September", "Friday")
        ]
        
        for weekday, expected_date, expected_weekday in test_cases:
            script = [
                {"u": f"What times do you have on {weekday}?"},
                {"a~": f"On {weekday}"}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088803{ord(weekday[0])}", f"test_lead_03{ord(weekday[0])}"
            )
            
            response = trace.turns[0].agent_response
            
            # Validate calendar correctness
            assert calendar_validator(expected_date, expected_weekday), \
                f"{expected_date} should be {expected_weekday}"
            
            # Check response mentions correct date
            assert expected_date.lower() in response.lower() or weekday.lower() in response.lower()
        
        print("✅ Weekday Date Disambiguation: PASSED")
    
    @pytest.mark.asyncio
    async def test_anytime_parsing(self, mock_zoho_client, mock_db_session,
                                 fsm_state_tracker, emoji_detector):
        """Test parsing of 'anytime' expressions"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        anytime_expressions = [
            "I'm free anytime tomorrow",
            "Any time tomorrow works",
            "Tomorrow is flexible for me",
            "I'm available all day tomorrow"
        ]
        
        for i, expression in enumerate(anytime_expressions):
            script = [
                {"u": expression},
                {"a~": "Tomorrow is 30 August"}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088804{i}", f"test_lead_04{i}"
            )
            
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response
            assert "30 august" in response.lower(), "Should resolve 'tomorrow' to 30 August"
            assert any(time_str in response.lower() for time_str in ["9:30", "12:00", "3:00"]), \
                "Should show available time slots"
        
        print("✅ Anytime Parsing: PASSED")
    
    @pytest.mark.asyncio
    async def test_date_ranges_parsing(self, mock_zoho_client, mock_db_session,
                                     fsm_state_tracker, emoji_detector):
        """Test parsing of date ranges"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        range_expressions = [
            ("I'm available on the 3rd or 4th", ["3 september", "4 september"]),
            ("Either Monday or Tuesday works", ["monday", "tuesday"]),
            ("1st or 2nd September", ["1 september", "2 september"]),
            ("This week or next week", ["week"])
        ]
        
        for i, (expression, expected_parts) in enumerate(range_expressions):
            script = [
                {"u": expression},
                {"a~": "Which day would you prefer"}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088805{i}", f"test_lead_05{i}"
            )
            
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            
            # Should ask for clarification and mention the options
            assert "which day" in response or "prefer" in response
            
            # Should mention at least one of the expected parts
            assert any(part in response for part in expected_parts), \
                f"Response should mention one of {expected_parts}"
        
        print("✅ Date Ranges Parsing: PASSED")
    
    @pytest.mark.asyncio
    async def test_24hour_time_parsing(self, mock_zoho_client, mock_db_session,
                                     fsm_state_tracker, emoji_detector):
        """Test parsing of 24-hour time formats"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        time_formats = [
            ("14:30", "2:30 pm"),
            ("09:00", "9:00 am"),
            ("13:00", "1:00 pm"),
            ("16:30", "4:30 pm")
        ]
        
        for i, (input_time, expected_display) in enumerate(time_formats):
            script = [
                {"u": f"I'm free at {input_time}"},
                {"a~": f"which day you'd like the meeting at {expected_display}"}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088806{i}", f"test_lead_06{i}"
            )
            
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            
            # Should convert 24-hour to 12-hour format
            assert expected_display in response, f"Should convert {input_time} to {expected_display}"
            assert "day" in response, "Should ask for day clarification"
        
        print("✅ 24-Hour Time Parsing: PASSED")
    
    @pytest.mark.asyncio
    async def test_noon_midnight_parsing(self, mock_zoho_client, mock_db_session,
                                       fsm_state_tracker, emoji_detector):
        """Test parsing of noon and midnight references"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        special_times = [
            ("noon", "12:00 pm"),
            ("midnight", "12:00 am"),
            ("midday", "12:00 pm")
        ]
        
        for i, (input_time, expected_display) in enumerate(special_times):
            script = [
                {"u": f"I'm free at {input_time}"},
                {"a~": f"which day you'd like the meeting at {expected_display}"}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088807{i}", f"test_lead_07{i}"
            )
            
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            
            # Should handle special time references
            if input_time != "midnight":  # Midnight meetings are unusual for business
                assert expected_display in response or "12:00" in response, \
                    f"Should handle {input_time} reference"
        
        print("✅ Noon Midnight Parsing: PASSED")
    
    @pytest.mark.asyncio
    async def test_relative_date_parsing(self, mock_zoho_client, mock_db_session,
                                       fsm_state_tracker, calendar_validator):
        """Test parsing of relative date expressions"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # From frozen time 2025-08-29 (Friday)
        relative_dates = [
            ("today", "29 august", "Friday"),
            ("tomorrow", "30 august", "Saturday"),
            ("next week", "monday", "Monday"),  # Should suggest next Monday
            ("this week", "week", None)
        ]
        
        for i, (input_date, expected_part, expected_weekday) in enumerate(relative_dates):
            script = [
                {"u": f"I'm free {input_date}"},
                {"a~": expected_part}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088808{i}", f"test_lead_08{i}"
            )
            
            response = trace.turns[0].agent_response.lower()
            
            # Should resolve relative date correctly
            assert expected_part in response, f"Should resolve '{input_date}' to include '{expected_part}'"
            
            # Validate calendar correctness where applicable
            if expected_weekday and expected_part != "week":
                date_for_validation = expected_part.replace(" august", " August")
                if date_for_validation != "monday":  # Skip weekday-only validation
                    assert calendar_validator(date_for_validation, expected_weekday), \
                        f"{date_for_validation} should be {expected_weekday}"
        
        print("✅ Relative Date Parsing: PASSED")
    
    @pytest.mark.asyncio
    async def test_timezone_edge_cases(self, mock_zoho_client, mock_db_session,
                                     fsm_state_tracker, emoji_detector):
        """Test timezone edge cases and boundary conditions"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Test edge times (early morning, late evening)
        edge_times = [
            ("6 AM", "early morning"),
            ("11 PM", "late evening"),
            ("7:30 AM", "early morning"),
            ("9:30 PM", "evening")
        ]
        
        for i, (input_time, time_category) in enumerate(edge_times):
            script = [
                {"u": f"I'm free at {input_time}"},
                {"a~": "which day"}
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088809{i}", f"test_lead_09{i}"
            )
            
            assert trace.has_no_emojis(), "No emojis should be present"
            
            response = trace.turns[0].agent_response.lower()
            
            # Should handle edge times gracefully
            assert "day" in response, "Should ask for day clarification"
            
            # For very early/late times, might suggest business hours
            if time_category in ["early morning", "late evening"]:
                # System might suggest business hours, but should still process the request
                assert len(response) > 10, "Should provide meaningful response"
        
        print("✅ Timezone Edge Cases: PASSED")
    
    @pytest.mark.asyncio
    async def test_calendar_correctness_2025(self, calendar_validator):
        """Test calendar correctness for key dates in 2025"""
        # Key dates to validate for 2025
        test_dates = [
            ("1 January 2025", "Wednesday"),
            ("1 September 2025", "Monday"),
            ("2 September 2025", "Tuesday"),
            ("3 September 2025", "Wednesday"),
            ("4 September 2025", "Thursday"),
            ("5 September 2025", "Friday"),
            ("30 August 2025", "Saturday"),
            ("31 August 2025", "Sunday"),
            ("25 December 2025", "Thursday"),
            ("31 December 2025", "Wednesday")
        ]
        
        for date_str, expected_weekday in test_dates:
            assert calendar_validator(date_str, expected_weekday), \
                f"{date_str} should be {expected_weekday}"
        
        print("✅ Calendar Correctness 2025: PASSED")
