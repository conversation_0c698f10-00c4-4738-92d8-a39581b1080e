"""
Primary Meeting Booking Scenario Tests
Tests for Cases 1,2,3,4,5,6,9,10,11 including exact transcript expectations
"""

import pytest
from datetime import datetime
from tests.meeting_agent.helpers.conversation_harness import ConversationHarness


class TestMeetingBookingCases:
    """Test all primary meeting booking scenarios with exact transcript validation"""
    
    @pytest.mark.asyncio
    async def test_case_1_clear_date_time(self, mock_zoho_client, mock_db_session, 
                                        fsm_state_tracker, test_phone_number, test_lead_id,
                                        emoji_detector, calendar_validator):
        """Case 1 – Clear Date & Time: Direct booking with validation"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Yes, I want to schedule a meeting."},
            {"a~": "Which day will work best for you"},
            {"u": "1 September will work."},
            {"a~": "available slots on 1 September: 10:00 AM, 1:00 PM, 4:30 PM"},
            {"u": "10 AM will work."},
            {"a~": "Your meeting has been booked for 1 September at 10:00 AM. Here is your meeting link:"}
        ]
        
        trace = await harness.run_conversation_script(script, test_phone_number, test_lead_id)
        
        # Assertions
        assert trace.has_fsm_states(["COLLECT_DAY", "SHOW_SLOTS", "AWAIT_CONFIRM", "BOOKING", "BOOKED"])
        assert trace.booking_created, "Booking should be created"
        assert trace.booking_id, "Booking ID should be present"
        assert trace.meeting_link, "Meeting link should be present"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Validate calendar correctness
        assert calendar_validator("1 September", "Monday"), "1 September 2025 should be Monday"
        
        # Check database operations
        db_operations = [op for turn in trace.turns for op in turn.db_operations]
        assert any("booking" in op.lower() for op in db_operations), "Booking should be written to DB"
        
        # Validate final response format
        final_response = trace.turns[-1].agent_response
        assert "1 september" in final_response.lower()
        assert "10:00 am" in final_response.lower()
        assert "meeting link" in final_response.lower()
        
        print("✅ Case 1 - Clear Date & Time: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_2_range_of_dates(self, mock_zoho_client, mock_db_session,
                                       fsm_state_tracker, emoji_detector):
        """Case 2 – Range of Dates: Clarification and selection"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "I'm available for a meeting on the 3rd or 4th."},
            {"a~": "Which day would you prefer — 3 September or 4 September"},
            {"u": "3 September."},
            {"a~": "available slots on 3 September: 11:00 AM, 1:00 PM, 3:00 PM"},
            {"u": "3 PM."},
            {"a~": "booked for 3 September at 3:00 PM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888002", "test_lead_002")
        
        # Assertions
        assert trace.has_fsm_states(["COLLECT_DAY", "SHOW_SLOTS", "AWAIT_CONFIRM", "BOOKING", "BOOKED"])
        assert trace.booking_created, "Booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check clarification response
        clarification_response = trace.turns[0].agent_response
        assert "3 september" in clarification_response.lower()
        assert "4 september" in clarification_response.lower()
        
        print("✅ Case 2 - Range of Dates: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_3_time_only(self, mock_zoho_client, mock_db_session,
                                  fsm_state_tracker, emoji_detector):
        """Case 3 – Time Only: Need date clarification"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "I'm free at 5 PM."},
            {"a~": "confirm which day you'd like the meeting at 5:00 PM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888003", "test_lead_003")
        
        # Assertions
        assert trace.has_fsm_states(["COLLECT_TIME", "COLLECT_DAY"])
        assert not trace.booking_created, "No booking should be created yet"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check time clarification
        response = trace.turns[0].agent_response
        assert "5:00 pm" in response.lower()
        assert "day" in response.lower()
        
        print("✅ Case 3 - Time Only: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_4_anytime_tomorrow(self, mock_zoho_client, mock_db_session,
                                         fsm_state_tracker, emoji_detector, calendar_validator):
        """Case 4 – Anytime Tomorrow: Show available slots"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "I'm free anytime tomorrow."},
            {"a~": "Tomorrow is 30 August. Here are the available slots: 9:30 AM, 12:00 PM, 3:00 PM"},
            {"u": "12 works."},
            {"a~": "booked your meeting on 30 August at 12:00 PM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888004", "test_lead_004")
        
        # Assertions
        assert trace.booking_created, "Booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Validate date resolution
        first_response = trace.turns[0].agent_response
        assert "30 august" in first_response.lower()
        assert "9:30 am" in first_response.lower()
        assert "12:00 pm" in first_response.lower()
        assert "3:00 pm" in first_response.lower()
        
        # Validate calendar correctness (30 August 2025 is Saturday)
        assert calendar_validator("30 August", "Saturday"), "30 August 2025 should be Saturday"
        
        print("✅ Case 4 - Anytime Tomorrow: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_5_weekday_availability(self, mock_zoho_client, mock_db_session,
                                             fsm_state_tracker, emoji_detector, calendar_validator):
        """Case 5 – Weekday Availability Question"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "What times do you have on Monday?"},
            {"a~": "On Monday (1 September)"},
            {"a~": "10:00 AM, 1:00 PM, 4:30 PM"},
            {"u": "1 PM."},
            {"a~": "confirmed for 1 September at 1:00 PM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888005", "test_lead_005")
        
        # Assertions
        assert trace.booking_created, "Booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Validate calendar correctness
        assert calendar_validator("1 September", "Monday"), "1 September 2025 should be Monday"
        
        # Check availability response
        availability_response = trace.turns[0].agent_response
        assert "monday (1 september)" in availability_response.lower()
        assert "10:00 am" in availability_response.lower()
        
        print("✅ Case 5 - Weekday Availability: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_6_date_only(self, mock_zoho_client, mock_db_session,
                                  fsm_state_tracker, emoji_detector):
        """Case 6 – Date Only: Show available slots"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Can we do 5th September?"},
            {"a~": "On 5 September, these slots are available: 9:00 AM, 11:30 AM, 2:00 PM"},
            {"u": "11:30 AM."},
            {"a~": "set for 5 September at 11:30 AM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888006", "test_lead_006")
        
        # Assertions
        assert trace.booking_created, "Booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check slots response
        slots_response = trace.turns[0].agent_response
        assert "5 september" in slots_response.lower()
        assert "9:00 am" in slots_response.lower()
        assert "11:30 am" in slots_response.lower()
        assert "2:00 pm" in slots_response.lower()
        
        print("✅ Case 6 - Date Only: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_9_next_week(self, mock_zoho_client, mock_db_session,
                                  fsm_state_tracker, emoji_detector, calendar_validator):
        """Case 9 – Next Week: Show weekday options"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Can we do sometime next week?"},
            {"a~": "Which day next week works best for you — Monday (1st), Tuesday (2nd), or Wednesday (3rd)?"},
            {"u": "Tuesday."},
            {"a~": "On 2 September (Tuesday), I have 11:00 AM, 1:30 PM, and 3:00 PM available."},
            {"u": "11."},
            {"a~": "confirmed for 2 September at 11:00 AM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888009", "test_lead_009")
        
        # Assertions
        assert trace.booking_created, "Booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Validate weekday window correctness
        first_response = trace.turns[0].agent_response
        assert "monday" in first_response.lower()
        assert "tuesday" in first_response.lower()
        assert "wednesday" in first_response.lower()
        
        # Validate calendar correctness
        assert calendar_validator("2 September", "Tuesday"), "2 September 2025 should be Tuesday"
        
        print("✅ Case 9 - Next Week: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_10_user_defers(self, mock_zoho_client, mock_db_session,
                                     fsm_state_tracker, emoji_detector):
        """Case 10 – User Defers: Park context"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Let's do 7 September."},
            {"a~": "On 7 September, I have 10:00 AM, 12:30 PM, and 4:00 PM available."},
            {"u": "I'll check and get back."},
            {"a~": "I'll keep these options handy. Please confirm when you're ready."}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888010", "test_lead_010")
        
        # Assertions
        assert not trace.booking_created, "No booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check defer response
        defer_response = trace.turns[-1].agent_response
        assert "keep these options handy" in defer_response.lower()
        assert "when you're ready" in defer_response.lower()
        
        # Verify context is parked (not immediately cleared)
        assert trace.final_state != "IDLE", "Context should be parked, not reset to IDLE"
        
        print("✅ Case 10 - User Defers: PASSED")
    
    @pytest.mark.asyncio
    async def test_case_11_holiday_no_slots(self, mock_zoho_client, mock_db_session,
                                          fsm_state_tracker, emoji_detector, calendar_validator):
        """Case 11 – Holiday / No Slots: Suggest alternatives"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Can we do 31 August?"},
            {"a~": "31 August falls on a Sunday, and I don't have any slots that day."},
            {"a~": "I do have availability on Monday (1 September): 10:00 AM, 1:00 PM, 4:00 PM."},
            {"u": "Yes, 1 PM."},
            {"a~": "booked your meeting for 1 September at 1:00 PM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888011", "test_lead_011")
        
        # Assertions
        assert trace.booking_created, "Booking should be created after alternative offered"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Validate holiday handling
        holiday_response = trace.turns[0].agent_response
        assert "31 august falls on a sunday" in holiday_response.lower()
        assert "don't have any slots that day" in holiday_response.lower()
        assert "monday (1 september)" in holiday_response.lower()
        
        # Validate calendar correctness
        assert calendar_validator("31 August", "Sunday"), "31 August 2025 should be Sunday"
        assert calendar_validator("1 September", "Monday"), "1 September 2025 should be Monday"
        
        print("✅ Case 11 - Holiday / No Slots: PASSED")
