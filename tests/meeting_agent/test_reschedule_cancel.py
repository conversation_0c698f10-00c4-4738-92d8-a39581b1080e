"""
Reschedule and Cancel Flow Tests
Tests for reschedule and cancel flows with provider + DB assertions
"""

import pytest
from datetime import datetime
from tests.meeting_agent.helpers.conversation_harness import ConversationHarness


class TestRescheduleAndCancel:
    """Test meeting reschedule and cancellation flows"""
    
    @pytest.mark.asyncio
    async def test_reschedule_after_booking(self, mock_zoho_client, mock_db_session,
                                          fsm_state_tracker, emoji_detector):
        """Test complete reschedule flow after initial booking"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888020"
        lead_id = "test_lead_020"
        
        # First, book an initial meeting
        initial_script = [
            {"u": "I want to schedule a meeting."},
            {"u": "1 September at 10 AM."},
            {"a~": "Your meeting has been booked for 1 September at 10:00 AM"}
        ]
        
        initial_trace = await harness.run_conversation_script(initial_script, phone_number, lead_id)
        assert initial_trace.booking_created, "Initial booking should be created"
        
        # Now test reschedule flow
        reschedule_script = [
            {"u": "Can we move my meeting to Friday afternoon?"},
            {"a~": "I can help reschedule. Which day would you prefer?"},
            {"u": "Friday 5th, after 1 PM."},
            {"a~": "On 5 September, I have 2:00 PM available. Should I book that?"},
            {"u": "Yes."},
            {"a~": "Your meeting has been rescheduled to 5 September at 2:00 PM."}
        ]
        
        reschedule_trace = await harness.run_conversation_script(reschedule_script, phone_number, lead_id)
        
        # Assertions
        assert reschedule_trace.has_no_emojis(), "No emojis should be present"
        
        # Check reschedule confirmation
        final_response = reschedule_trace.turns[-1].agent_response
        assert "rescheduled" in final_response.lower()
        assert "5 september" in final_response.lower()
        assert "2:00 pm" in final_response.lower()
        
        # Verify Zoho API calls
        assert mock_zoho_client.reschedule_booking.called, "Reschedule API should be called"
        
        # Check database operations
        db_operations = []
        for turn in reschedule_trace.turns:
            db_operations.extend(turn.db_operations)
        
        # Should have operations for old booking closure and new booking creation
        assert any("booking" in op.lower() for op in db_operations), "DB operations should include booking updates"
        
        print("✅ Reschedule After Booking: PASSED")
    
    @pytest.mark.asyncio
    async def test_cancel_after_booking(self, mock_zoho_client, mock_db_session,
                                      fsm_state_tracker, emoji_detector):
        """Test complete cancellation flow after initial booking"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888021"
        lead_id = "test_lead_021"
        
        # First, book an initial meeting
        initial_script = [
            {"u": "I want to schedule a meeting."},
            {"u": "1 September at 10 AM."},
            {"a~": "Your meeting has been booked for 1 September at 10:00 AM"}
        ]
        
        initial_trace = await harness.run_conversation_script(initial_script, phone_number, lead_id)
        assert initial_trace.booking_created, "Initial booking should be created"
        
        # Now test cancellation flow
        cancel_script = [
            {"u": "Cancel my meeting."},
            {"a~": "I can cancel your meeting. Would you like me to proceed now?"},
            {"u": "Yes."},
            {"a~": "Your meeting has been canceled."}
        ]
        
        cancel_trace = await harness.run_conversation_script(cancel_script, phone_number, lead_id)
        
        # Assertions
        assert cancel_trace.has_no_emojis(), "No emojis should be present"
        
        # Check cancellation confirmation
        confirmation_response = cancel_trace.turns[0].agent_response
        assert "cancel your meeting" in confirmation_response.lower()
        assert "proceed now" in confirmation_response.lower()
        
        final_response = cancel_trace.turns[-1].agent_response
        assert "canceled" in final_response.lower()
        
        # Verify Zoho API calls
        assert mock_zoho_client.cancel_booking.called, "Cancel API should be called"
        
        # Check database operations
        db_operations = []
        for turn in cancel_trace.turns:
            db_operations.extend(turn.db_operations)
        
        assert any("booking" in op.lower() for op in db_operations), "DB operations should include booking cancellation"
        
        print("✅ Cancel After Booking: PASSED")
    
    @pytest.mark.asyncio
    async def test_reschedule_without_existing_booking(self, mock_zoho_client, mock_db_session,
                                                     fsm_state_tracker, emoji_detector):
        """Test reschedule request when no existing booking exists"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Can we reschedule my meeting?"},
            {"a~": "I don't see an existing booking to reschedule. Would you like to schedule a new meeting?"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888022", "test_lead_022")
        
        # Assertions
        assert not trace.booking_created, "No booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check response handles missing booking gracefully
        response = trace.turns[0].agent_response
        assert "don't see an existing booking" in response.lower()
        assert "schedule a new meeting" in response.lower()
        
        print("✅ Reschedule Without Existing Booking: PASSED")
    
    @pytest.mark.asyncio
    async def test_cancel_without_existing_booking(self, mock_zoho_client, mock_db_session,
                                                 fsm_state_tracker, emoji_detector):
        """Test cancel request when no existing booking exists"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Cancel my meeting."},
            {"a~": "I don't see an existing booking to cancel. Is there anything else I can help you with?"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888023", "test_lead_023")
        
        # Assertions
        assert not trace.booking_created, "No booking should be created"
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check response handles missing booking gracefully
        response = trace.turns[0].agent_response
        assert "don't see an existing booking" in response.lower()
        
        print("✅ Cancel Without Existing Booking: PASSED")
    
    @pytest.mark.asyncio
    async def test_reschedule_with_provider_failure(self, mock_zoho_client, mock_db_session,
                                                  fsm_state_tracker, emoji_detector):
        """Test reschedule flow when provider fails"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888024"
        lead_id = "test_lead_024"
        
        # First, book an initial meeting
        initial_script = [
            {"u": "I want to schedule a meeting."},
            {"u": "1 September at 10 AM."}
        ]
        
        initial_trace = await harness.run_conversation_script(initial_script, phone_number, lead_id)
        
        # Configure provider failure for reschedule
        async def failing_reschedule(*args, **kwargs):
            raise Exception("Provider temporarily unavailable")
        
        mock_zoho_client.reschedule_booking.side_effect = failing_reschedule
        
        # Test reschedule with failure
        reschedule_script = [
            {"u": "Can we move my meeting to Friday?"},
            {"a~": "I'm having trouble rescheduling right now. Let me connect you with our team."}
        ]
        
        reschedule_trace = await harness.run_conversation_script(reschedule_script, phone_number, lead_id)
        
        # Assertions
        assert reschedule_trace.has_no_emojis(), "No emojis should be present"
        
        # Should handle failure gracefully
        response = reschedule_trace.turns[0].agent_response
        assert ("trouble" in response.lower() or 
               "connect you with our team" in response.lower() or
               "try again" in response.lower()), "Should handle provider failure gracefully"
        
        print("✅ Reschedule With Provider Failure: PASSED")
    
    @pytest.mark.asyncio
    async def test_multiple_reschedules(self, mock_zoho_client, mock_db_session,
                                      fsm_state_tracker, emoji_detector):
        """Test multiple reschedule operations"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888025"
        lead_id = "test_lead_025"
        
        # Initial booking
        initial_script = [
            {"u": "I want to schedule a meeting."},
            {"u": "1 September at 10 AM."}
        ]
        
        await harness.run_conversation_script(initial_script, phone_number, lead_id)
        
        # First reschedule
        first_reschedule = [
            {"u": "Can we move my meeting to Tuesday?"},
            {"u": "Yes, 2 PM works."}
        ]
        
        first_trace = await harness.run_conversation_script(first_reschedule, phone_number, lead_id)
        
        # Second reschedule
        second_reschedule = [
            {"u": "Actually, can we move it to Wednesday morning?"},
            {"u": "11 AM is perfect."}
        ]
        
        second_trace = await harness.run_conversation_script(second_reschedule, phone_number, lead_id)
        
        # Assertions
        assert first_trace.has_no_emojis(), "No emojis in first reschedule"
        assert second_trace.has_no_emojis(), "No emojis in second reschedule"
        
        # Should handle multiple reschedules
        assert mock_zoho_client.reschedule_booking.call_count >= 2, "Multiple reschedule calls should be made"
        
        print("✅ Multiple Reschedules: PASSED")
    
    @pytest.mark.asyncio
    async def test_cancel_confirmation_required(self, mock_zoho_client, mock_db_session,
                                              fsm_state_tracker, emoji_detector):
        """Test that cancellation requires explicit confirmation"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888026"
        lead_id = "test_lead_026"
        
        # Initial booking
        initial_script = [
            {"u": "I want to schedule a meeting."},
            {"u": "1 September at 10 AM."}
        ]
        
        await harness.run_conversation_script(initial_script, phone_number, lead_id)
        
        # Cancel request without confirmation
        cancel_script = [
            {"u": "Cancel my meeting."},
            {"a~": "I can cancel your meeting. Would you like me to proceed now?"},
            {"u": "Actually, never mind."},
            {"a~": "No problem. Your meeting is still scheduled."}
        ]
        
        cancel_trace = await harness.run_conversation_script(cancel_script, phone_number, lead_id)
        
        # Assertions
        assert cancel_trace.has_no_emojis(), "No emojis should be present"
        
        # Should not cancel without explicit confirmation
        assert not mock_zoho_client.cancel_booking.called, "Cancel API should not be called without confirmation"
        
        # Check that meeting is preserved
        final_response = cancel_trace.turns[-1].agent_response
        assert "still scheduled" in final_response.lower() or "no problem" in final_response.lower()
        
        print("✅ Cancel Confirmation Required: PASSED")
