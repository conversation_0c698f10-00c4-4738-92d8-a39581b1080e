"""
Stability and Loop Prevention Tests
Tests for loop-prevention, backoff behavior, repeated/ambiguous inputs, provider flakiness
"""

import pytest
from datetime import datetime
from tests.meeting_agent.helpers.conversation_harness import ConversationHarness


class TestStabilityAndLoops:
    """Test loop prevention and conversation stability"""
    
    @pytest.mark.asyncio
    async def test_ambiguity_and_loop_prevention(self, mock_zoho_client, mock_db_session,
                                                fsm_state_tracker, emoji_detector):
        """Test ambiguity handling and loop prevention with clarification turn caps"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Maybe late evening sometime."},
            {"a~": "Could you share a specific day? I'll list available evening slots."},
            {"u": "same."},
            {"a~": "I can suggest specific options for the next few days. Would 30 August at 6:00 PM or 31 August at 6:00 PM work?"},
            {"u": "stop."}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888040", "test_lead_040")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Check clarification turn capping (max 2 clarifications)
        clarification_responses = []
        for turn in trace.turns:
            if any(word in turn.agent_response.lower() for word in ["specific", "clarify", "could you"]):
                clarification_responses.append(turn.agent_response)
        
        assert len(clarification_responses) <= 2, "Should cap clarification turns at 2"
        
        # Final response should provide specific options or escalate
        final_response = trace.turns[-2].agent_response.lower()  # Before "stop"
        assert ("specific options" in final_response or 
               "30 august" in final_response or
               "connect you with our team" in final_response), \
               "Should provide specific options or escalate after max clarifications"
        
        print("✅ Ambiguity and Loop Prevention: PASSED")
    
    @pytest.mark.asyncio
    async def test_repeated_input_detection(self, mock_zoho_client, mock_db_session,
                                          fsm_state_tracker, emoji_detector):
        """Test detection and handling of repeated user inputs"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Send same ambiguous message multiple times
        script = [
            {"u": "maybe sometime"},
            {"a~": "Could you share a specific day"},
            {"u": "maybe sometime"},
            {"a~": "specific options"},
            {"u": "maybe sometime"},
            {"a~": "connect you with our team"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888041", "test_lead_041")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Should detect repeated input and escalate
        responses = [turn.agent_response.lower() for turn in trace.turns]
        
        # First response: normal clarification
        assert any("specific day" in resp for resp in responses[:1])
        
        # Later responses: should provide options or escalate
        later_responses = responses[1:]
        assert any("specific options" in resp or "connect you with our team" in resp 
                  for resp in later_responses), "Should escalate after repeated inputs"
        
        print("✅ Repeated Input Detection: PASSED")
    
    @pytest.mark.asyncio
    async def test_provider_failure_with_backoff(self, mock_zoho_client, mock_db_session,
                                                fsm_state_tracker, emoji_detector):
        """Test provider failure handling with backoff behavior"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        script = [
            {"u": "Book 1 September at 10 AM."},
            {"mock": "list_availability intermittent 500 for 2 attempts then succeed"},
            {"a~": "slots on 1 September"},
            {"u": "10 AM."},
            {"mock": "create_booking intermittent 502 then succeed"},
            {"a~": "booked for 1 September at 10:00 AM"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888042", "test_lead_042")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        assert trace.booking_created, "Booking should eventually succeed after retries"
        
        # Check that retries occurred
        side_effects = []
        for turn in trace.turns:
            side_effects.extend(turn.side_effects)
        
        # Should have multiple API calls due to retries
        api_calls = [effect for effect in side_effects if "ZOHO_API" in effect]
        
        # Verify final booking success
        final_response = trace.turns[-1].agent_response.lower()
        assert "booked" in final_response
        assert "1 september" in final_response
        assert "10:00 am" in final_response
        
        # Should have single final booking (idempotency)
        assert len([write for write in trace.db_writes if "booking" in write.get("type", "").lower()]) <= 1, \
               "Should have single final booking record"
        
        print("✅ Provider Failure With Backoff: PASSED")
    
    @pytest.mark.asyncio
    async def test_infinite_loop_prevention(self, mock_zoho_client, mock_db_session,
                                          fsm_state_tracker, emoji_detector):
        """Test prevention of infinite conversation loops"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Try to create an infinite loop scenario
        script = [
            {"u": "I want to schedule"},
            {"a~": "Which day"},
            {"u": "I don't know"},
            {"a~": "specific day"},
            {"u": "I don't know"},
            {"a~": "options"},
            {"u": "I don't know"},
            {"a~": "connect you with our team"}
        ]
        
        trace = await harness.run_conversation_script(script, "+61940888043", "test_lead_043")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        assert len(trace.turns) <= 10, "Should not create excessively long conversations"
        
        # Should eventually escalate or provide specific options
        final_responses = [turn.agent_response.lower() for turn in trace.turns[-2:]]
        assert any("connect you with our team" in resp or "specific options" in resp 
                  for resp in final_responses), "Should escalate to prevent infinite loops"
        
        print("✅ Infinite Loop Prevention: PASSED")
    
    @pytest.mark.asyncio
    async def test_context_corruption_recovery(self, mock_zoho_client, mock_db_session,
                                             fsm_state_tracker, emoji_detector, mock_redis):
        """Test recovery from corrupted conversation context"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888044"
        
        # Start a conversation
        initial_script = [
            {"u": "I want to schedule a meeting"},
            {"a~": "Which day"}
        ]
        
        await harness.run_conversation_script(initial_script, phone_number, "test_lead_044")
        
        # Corrupt the context in Redis
        context_key = f"meeting_context:{phone_number}"
        mock_redis._data[context_key] = "corrupted_json_data"
        
        # Continue conversation - should recover gracefully
        recovery_script = [
            {"u": "Tomorrow at 2 PM"},
            {"a~": "schedule"}  # Should start fresh or handle gracefully
        ]
        
        trace = await harness.run_conversation_script(recovery_script, phone_number, "test_lead_044")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Should handle corrupted context gracefully
        response = trace.turns[0].agent_response
        assert len(response) > 0, "Should provide meaningful response despite corrupted context"
        assert "error" not in response.lower(), "Should not expose errors to user"
        
        print("✅ Context Corruption Recovery: PASSED")
    
    @pytest.mark.asyncio
    async def test_concurrent_conversation_isolation(self, mock_zoho_client, mock_db_session,
                                                   fsm_state_tracker, emoji_detector):
        """Test that concurrent conversations don't interfere with each other"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        # Start two concurrent conversations
        phone1 = "+61940888045"
        phone2 = "+61940888046"
        
        script1 = [
            {"u": "I want to schedule for Monday"},
            {"a~": "Monday"}
        ]
        
        script2 = [
            {"u": "I want to schedule for Friday"},
            {"a~": "Friday"}
        ]
        
        # Run conversations concurrently (simulated)
        trace1 = await harness.run_conversation_script(script1, phone1, "test_lead_045")
        trace2 = await harness.run_conversation_script(script2, phone2, "test_lead_046")
        
        # Assertions
        assert trace1.has_no_emojis(), "No emojis in conversation 1"
        assert trace2.has_no_emojis(), "No emojis in conversation 2"
        
        # Conversations should be isolated
        response1 = trace1.turns[0].agent_response.lower()
        response2 = trace2.turns[0].agent_response.lower()
        
        # Each should reference their own day
        assert "monday" in response1 or "1 september" in response1
        assert "friday" in response2 or "5 september" in response2
        
        # Should not cross-contaminate
        assert "friday" not in response1
        assert "monday" not in response2
        
        print("✅ Concurrent Conversation Isolation: PASSED")
    
    @pytest.mark.asyncio
    async def test_malformed_input_handling(self, mock_zoho_client, mock_db_session,
                                          fsm_state_tracker, emoji_detector):
        """Test handling of malformed or unusual inputs"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        malformed_inputs = [
            "",  # Empty message
            "   ",  # Whitespace only
            "🎉🎊🎈",  # Emoji only
            "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",  # Repeated characters
            "!@#$%^&*()",  # Special characters only
            "a" * 1000,  # Very long message
        ]
        
        for i, malformed_input in enumerate(malformed_inputs):
            script = [
                {"u": malformed_input},
                {"a~": "schedule"}  # Should handle gracefully
            ]
            
            trace = await harness.run_conversation_script(
                script, f"+6194088804{i+7}", f"test_lead_04{i+7}"
            )
            
            # Should handle malformed input gracefully
            assert len(trace.turns) > 0, "Should respond to malformed input"
            assert trace.has_no_emojis(), "No emojis should be present in response"
            
            response = trace.turns[0].agent_response
            assert len(response) > 0, "Should provide meaningful response"
            assert "error" not in response.lower(), "Should not expose errors to user"
        
        print("✅ Malformed Input Handling: PASSED")
    
    @pytest.mark.asyncio
    async def test_session_timeout_handling(self, mock_zoho_client, mock_db_session,
                                          fsm_state_tracker, emoji_detector, mock_redis):
        """Test handling of session timeouts"""
        harness = ConversationHarness(
            mock_zoho_client=mock_zoho_client,
            mock_db_session=mock_db_session,
            fsm_state_tracker=fsm_state_tracker
        )
        
        phone_number = "+61940888048"
        
        # Start conversation
        initial_script = [
            {"u": "I want to schedule a meeting"},
            {"a~": "Which day"}
        ]
        
        await harness.run_conversation_script(initial_script, phone_number, "test_lead_048")
        
        # Simulate session timeout by clearing Redis
        context_key = f"meeting_context:{phone_number}"
        if context_key in mock_redis._data:
            del mock_redis._data[context_key]
        
        # Continue conversation after timeout
        timeout_script = [
            {"u": "Tomorrow at 2 PM"},
            {"a~": "schedule"}  # Should start fresh
        ]
        
        trace = await harness.run_conversation_script(timeout_script, phone_number, "test_lead_048")
        
        # Assertions
        assert trace.has_no_emojis(), "No emojis should be present"
        
        # Should handle timeout gracefully
        response = trace.turns[0].agent_response
        assert len(response) > 0, "Should provide meaningful response after timeout"
        
        # Should either start fresh or acknowledge the request
        assert any(keyword in response.lower() for keyword in 
                  ["schedule", "meeting", "tomorrow", "2 pm"]), \
               "Should acknowledge the meeting request"
        
        print("✅ Session Timeout Handling: PASSED")
