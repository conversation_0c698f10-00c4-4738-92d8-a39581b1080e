"""
Integration tests for the merged SMS + Meeting Booking system
Tests the complete workflow from SMS to meeting booking
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, Mock

from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.models.booking import Booking
from tests.conftest import assert_sms_response, assert_followup_scheduled, assert_followup_cancelled


@pytest.mark.integration
@pytest.mark.asyncio
class TestMergedSystemIntegration:
    """Test the complete merged system workflow"""

    async def test_complete_sms_to_meeting_workflow(
        self, 
        client, 
        db_session, 
        test_lead, 
        test_messaging_rule,
        mock_sms_service,
        mock_openai,
        test_data_factory
    ):
        """Test complete workflow: SMS → Qualification → Meeting Booking"""
        
        # Step 1: Initial SMS message
        sms_data = test_data_factory.create_sms_webhook_data(
            mo={
                "sender": "919408869824",
                "recipient": "+61400000000",
                "message": "I'm interested in franchise opportunities"
            }
        )
        
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        assert response_data["data"]["conversation_stage"] in ["introduction", "qualification"]
        
        # Step 2: Budget qualification
        sms_data["mo"]["message"] = "I have $150,000 to invest"
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        
        # Step 3: Meeting scheduling request
        sms_data["mo"]["message"] = "Yes, I'd like to schedule a meeting"
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        assert response_data["data"]["conversation_stage"] == "scheduling"

    async def test_followup_system_with_meeting_booking(
        self,
        client,
        db_session,
        test_lead,
        test_messaging_rule,
        mock_sms_service,
        mock_celery,
        test_data_factory
    ):
        """Test follow-up system integration with meeting booking"""
        
        # Step 1: Send initial message that should trigger follow-up
        sms_data = test_data_factory.create_sms_webhook_data(
            mo={
                "sender": "919408869824", 
                "recipient": "+61400000000",
                "message": "Tell me about franchise opportunities"
            }
        )
        
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        
        # Verify follow-up was scheduled
        mock_celery.send_task.assert_called()
        
        # Step 2: Lead responds after follow-up (should cancel remaining follow-ups)
        sms_data["mo"]["message"] = "Yes, I'm very interested. Can we schedule a call?"
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        assert response_data["data"]["conversation_stage"] == "scheduling"

    async def test_sms_splitting_with_meeting_details(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test SMS splitting when sending meeting details"""
        
        # Configure mock to return long meeting details response
        mock_sms_service.send_sms.return_value = {
            "success": True,
            "message_id": "test-123",
            "parts_sent": 3,
            "total_characters": 450
        }
        
        # Send message asking for meeting details
        sms_data = test_data_factory.create_sms_webhook_data(
            mo={
                "sender": "919408869824",
                "recipient": "+61400000000", 
                "message": "What are the available meeting times and what will we discuss?"
            }
        )
        
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        
        # Verify SMS service was called
        mock_sms_service.send_sms.assert_called()

    async def test_meeting_booking_api_integration(
        self,
        client,
        db_session,
        test_lead,
        test_data_factory
    ):
        """Test meeting booking API endpoints"""
        
        # Test availability check
        availability_data = {
            "service_type": "lead_meeting",
            "days_ahead": 7,
            "preferred_staff": "saumil"
        }
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {
                        "date": "2024-08-26",
                        "time": "10:00",
                        "staff": "saumil",
                        "duration": 30
                    }
                ]
            })
            mock_service.return_value = mock_instance
            
            response = client.post("/api/v1/simple-booking/availability", json=availability_data)
            assert response.status_code == 200
            
            response_data = response.json()
            assert "available_slots" in response_data

    async def test_agent_system_integration(
        self,
        client,
        db_session,
        test_lead,
        mock_openai,
        test_data_factory
    ):
        """Test agent system integration with SMS and booking"""
        
        # Configure OpenAI mock for agent responses
        mock_openai.chat.completions.create.return_value.choices[0].message.content = (
            "I'd be happy to help you with franchise opportunities. "
            "Let me gather some information about your investment budget and preferences."
        )
        
        sms_data = test_data_factory.create_sms_webhook_data(
            mo={
                "sender": "919408869824",
                "recipient": "+61400000000",
                "message": "I want to learn about franchise opportunities"
            }
        )
        
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert_sms_response(response_data)
        assert "ai_response" in response_data["data"]
        assert len(response_data["data"]["ai_response"]) > 0

    async def test_database_consistency_across_systems(
        self,
        client,
        db_session,
        test_lead,
        test_messaging_rule,
        test_data_factory
    ):
        """Test database consistency between SMS, follow-up, and booking systems"""
        
        # Send SMS message
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response.status_code == 200
        
        # Verify conversation message was stored
        from sqlalchemy import select
        result = await db_session.execute(
            select(ConversationMessage).where(
                ConversationMessage.lead_id == test_lead.id
            )
        )
        messages = result.scalars().all()
        assert len(messages) >= 1
        
        # Verify lead was updated
        await db_session.refresh(test_lead)
        assert test_lead.updated_at is not None

    async def test_error_handling_across_systems(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test error handling across integrated systems"""
        
        # Configure SMS service to fail
        mock_sms_service.send_sms.side_effect = Exception("SMS service unavailable")
        
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        
        # Should handle error gracefully
        assert response.status_code in [200, 500]  # Depending on error handling strategy
        
        if response.status_code == 200:
            response_data = response.json()
            assert "error" in response_data or response_data["success"] == False

    async def test_concurrent_sms_processing(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test concurrent SMS processing doesn't cause conflicts"""
        
        # Create multiple SMS requests
        sms_data = test_data_factory.create_sms_webhook_data()
        
        # Send multiple concurrent requests
        tasks = []
        for i in range(3):
            sms_data["mo"]["message"] = f"Test message {i}"
            task = asyncio.create_task(
                asyncio.to_thread(
                    lambda: client.post("/api/webhooks/kudosity", json=sms_data)
                )
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed or handle gracefully
        for response in responses:
            if not isinstance(response, Exception):
                assert response.status_code == 200

    async def test_system_performance_under_load(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        mock_openai,
        test_data_factory
    ):
        """Test system performance under moderate load"""
        
        # Configure fast mock responses
        mock_sms_service.send_sms.return_value = {
            "success": True,
            "message_id": "fast-test",
            "parts_sent": 1
        }
        
        sms_data = test_data_factory.create_sms_webhook_data()
        
        # Measure response times
        import time
        start_time = time.time()
        
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 10.0  # Should respond within 10 seconds
        
        response_data = response.json()
        assert_sms_response(response_data)
