"""
Kudosity SMS Client Stub for Testing
Fake send/receive endpoints with segment tracking and validation
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
import re


@dataclass
class SentMessage:
    """Represents a sent SMS message."""
    phone: str
    message: str
    segments: List[str]
    timestamp: datetime
    message_id: str
    success: bool = True
    error: Optional[str] = None


class KudosityStub:
    """
    Mock implementation of Kudosity SMS client for testing.
    Records outbound messages and enforces segment limits.
    """
    
    def __init__(self, max_segment_chars: int = 160):
        self.max_segment_chars = max_segment_chars
        self.sent_messages: List[SentMessage] = []
        self.message_counter = 0
        self.should_fail = False
        self.failure_reason = "Network error"
        
        # Configuration
        self.enabled = False  # Matches KUDOSITY_SMS_ENABLED=false
        self.test_mode = True
    
    def reset(self):
        """Reset the stub state."""
        self.sent_messages.clear()
        self.message_counter = 0
        self.should_fail = False
    
    def set_failure_mode(self, should_fail: bool, reason: str = "Network error"):
        """Configure the stub to simulate failures."""
        self.should_fail = should_fail
        self.failure_reason = reason
    
    async def send_sms(self, phone: str, message: str) -> Dict[str, Any]:
        """
        Mock SMS sending with segment validation.
        
        Args:
            phone: Phone number to send to
            message: Message content
            
        Returns:
            Response dict with success/failure info
        """
        self.message_counter += 1
        message_id = f"test_msg_{self.message_counter}"
        
        # Simulate failure if configured
        if self.should_fail:
            sent_msg = SentMessage(
                phone=phone,
                message=message,
                segments=[],
                timestamp=datetime.utcnow(),
                message_id=message_id,
                success=False,
                error=self.failure_reason
            )
            self.sent_messages.append(sent_msg)
            
            return {
                "success": False,
                "error": self.failure_reason,
                "message_id": message_id
            }
        
        # Split message into segments
        segments = self._split_message(message)
        
        # Validate segments
        for segment in segments:
            if len(segment) > self.max_segment_chars:
                sent_msg = SentMessage(
                    phone=phone,
                    message=message,
                    segments=segments,
                    timestamp=datetime.utcnow(),
                    message_id=message_id,
                    success=False,
                    error=f"Segment too long: {len(segment)} chars"
                )
                self.sent_messages.append(sent_msg)
                
                return {
                    "success": False,
                    "error": f"Segment exceeds {self.max_segment_chars} character limit",
                    "message_id": message_id
                }
        
        # Record successful send
        sent_msg = SentMessage(
            phone=phone,
            message=message,
            segments=segments,
            timestamp=datetime.utcnow(),
            message_id=message_id,
            success=True
        )
        self.sent_messages.append(sent_msg)
        
        # In test mode, print to console instead of sending
        if self.test_mode:
            print(f"[TEST SMS] To: {phone}")
            for i, segment in enumerate(segments, 1):
                print(f"[SEGMENT {i}/{len(segments)}] {segment}")
        
        return {
            "success": True,
            "message_id": message_id,
            "segments_sent": len(segments),
            "total_chars": len(message)
        }
    
    def _split_message(self, message: str) -> List[str]:
        """
        Split message into segments respecting character limits.
        Implements smart splitting to avoid breaking URLs and words.
        """
        if len(message) <= self.max_segment_chars:
            return [message]
        
        segments = []
        remaining = message
        
        while remaining:
            if len(remaining) <= self.max_segment_chars:
                segments.append(remaining)
                break
            
            # Find the best split point
            split_point = self._find_split_point(remaining, self.max_segment_chars)
            
            segment = remaining[:split_point].rstrip()
            segments.append(segment)
            remaining = remaining[split_point:].lstrip()
        
        return segments
    
    def _find_split_point(self, text: str, max_length: int) -> int:
        """
        Find the best point to split text without breaking URLs or words.
        
        Args:
            text: Text to split
            max_length: Maximum segment length
            
        Returns:
            Index where to split the text
        """
        if len(text) <= max_length:
            return len(text)
        
        # Don't break URLs
        url_pattern = r'https?://[^\s]+'
        urls = list(re.finditer(url_pattern, text))
        
        for url_match in urls:
            url_start, url_end = url_match.span()
            if url_start < max_length < url_end:
                # URL would be broken, split before it
                return url_start
        
        # Try to split at word boundary
        split_candidates = [max_length]
        
        # Look for spaces near the max length
        for i in range(max_length - 20, max_length):
            if i > 0 and i < len(text) and text[i] == ' ':
                split_candidates.append(i)
        
        # Look for sentence endings
        for i in range(max_length - 30, max_length):
            if i > 0 and i < len(text) and text[i] in '.!?':
                split_candidates.append(i + 1)
        
        # Choose the best split point (closest to max_length)
        best_split = max(split_candidates)
        return min(best_split, len(text))
    
    def get_sent_messages(self, phone: Optional[str] = None) -> List[SentMessage]:
        """Get sent messages, optionally filtered by phone number."""
        if phone:
            return [msg for msg in self.sent_messages if msg.phone == phone]
        return self.sent_messages.copy()
    
    def get_last_message(self, phone: Optional[str] = None) -> Optional[SentMessage]:
        """Get the last sent message, optionally filtered by phone number."""
        messages = self.get_sent_messages(phone)
        return messages[-1] if messages else None
    
    def assert_message_sent(self, phone: str, contains: str):
        """Assert that a message containing specific text was sent to a phone number."""
        messages = self.get_sent_messages(phone)
        for msg in messages:
            if contains.lower() in msg.message.lower():
                return True
        
        raise AssertionError(
            f"No message containing '{contains}' sent to {phone}. "
            f"Sent messages: {[msg.message for msg in messages]}"
        )
    
    def assert_no_emojis_sent(self):
        """Assert that no sent messages contain emojis."""
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+", flags=re.UNICODE
        )
        
        for msg in self.sent_messages:
            if emoji_pattern.search(msg.message):
                raise AssertionError(f"Message contains emojis: {msg.message}")
    
    def assert_segment_count(self, phone: str, expected_segments: int):
        """Assert that the last message to a phone number has expected segment count."""
        last_msg = self.get_last_message(phone)
        if not last_msg:
            raise AssertionError(f"No messages sent to {phone}")
        
        actual_segments = len(last_msg.segments)
        if actual_segments != expected_segments:
            raise AssertionError(
                f"Expected {expected_segments} segments, got {actual_segments}. "
                f"Message: {last_msg.message}"
            )
    
    def assert_no_duplicate_sends(self):
        """Assert that no duplicate messages were sent."""
        seen_messages = set()
        
        for msg in self.sent_messages:
            key = (msg.phone, msg.message)
            if key in seen_messages:
                raise AssertionError(f"Duplicate message sent to {msg.phone}: {msg.message}")
            seen_messages.add(key)
    
    def get_total_segments_sent(self) -> int:
        """Get total number of SMS segments sent."""
        return sum(len(msg.segments) for msg in self.sent_messages if msg.success)
    
    def get_failed_sends(self) -> List[SentMessage]:
        """Get list of failed message sends."""
        return [msg for msg in self.sent_messages if not msg.success]
    
    # Webhook simulation methods
    
    def simulate_incoming_message(self, phone: str, message: str) -> Dict[str, Any]:
        """Simulate an incoming message webhook payload."""
        return {
            "phone": phone,
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": f"incoming_{self.message_counter}",
            "direction": "inbound"
        }
    
    def simulate_delivery_receipt(self, message_id: str, status: str = "delivered") -> Dict[str, Any]:
        """Simulate a delivery receipt webhook payload."""
        return {
            "message_id": message_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "type": "delivery_receipt"
        }


# Test utilities

def create_long_message(target_segments: int, segment_length: int = 160) -> str:
    """Create a message that will split into a specific number of segments."""
    # Create a message slightly longer than target_segments * segment_length
    # to ensure it splits into the desired number of segments
    base_text = "This is a test message that will be split into multiple segments. "
    
    # Calculate how much text we need
    target_length = (target_segments * segment_length) - 50  # Leave some buffer
    
    # Repeat base text to reach target length
    repetitions = (target_length // len(base_text)) + 1
    message = (base_text * repetitions)[:target_length]
    
    # Add a final sentence to push it over the edge
    message += " This should create the final segment."
    
    return message


def create_message_with_url(url: str = "https://example.com/very/long/path/to/resource") -> str:
    """Create a message containing a URL that shouldn't be broken."""
    return f"Please visit our website at {url} for more information about our services."
