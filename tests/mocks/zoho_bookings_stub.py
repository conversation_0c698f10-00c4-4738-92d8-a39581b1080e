"""
Zoho Bookings Service Stub for Testing
Mock token refresh, availability, booking operations with programmable failures
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta, time
import uuid


@dataclass
class MockBookingSlot:
    """Mock booking slot for testing."""
    staff_id: str
    staff_name: str
    start_time: datetime
    end_time: datetime
    service_id: str
    service_name: str
    duration_minutes: int
    booking_url: str


@dataclass
class MockBookingResult:
    """Mock booking result for testing."""
    success: bool
    booking_id: Optional[str] = None
    meeting_link: Optional[str] = None
    booking_url: Optional[str] = None
    error_message: Optional[str] = None


class ZohoBookingsStub:
    """
    Mock implementation of Zoho Bookings service for testing.
    Provides programmable availability and booking operations.
    """
    
    def __init__(self):
        self.reset()
        
        # Default availability seed
        self.availability_seed = {
            "2025-08-30": ["09:30", "12:00", "15:00"],
            "2025-09-01": ["10:00", "13:00", "16:30"], 
            "2025-09-03": ["11:00", "13:00", "15:00"],
            "2025-09-05": ["09:00", "11:30", "14:00"]
        }
        
        # Holidays/closed days
        self.holidays = ["2025-08-31"]
        
        # Staff configuration
        self.staff = [
            {"id": "staff1", "name": "John Smith", "timezone": "Asia/Kolkata"},
            {"id": "staff2", "name": "Jane Doe", "timezone": "Asia/Kolkata"}
        ]
        
        # Service configuration
        self.services = [
            {"id": "service1", "name": "Lead Meeting", "duration": 30},
            {"id": "service2", "name": "Discovery Call", "duration": 45}
        ]
    
    def reset(self):
        """Reset stub state."""
        self.bookings: Dict[str, Dict[str, Any]] = {}
        self.booking_counter = 0
        self.token_refresh_count = 0
        self.should_fail_token = False
        self.should_fail_availability = False
        self.should_fail_booking = False
        self.failure_reason = "Network error"
        self.api_call_log: List[Dict[str, Any]] = []
    
    def set_availability(self, date_slots: Dict[str, List[str]]):
        """Set custom availability for testing."""
        self.availability_seed = date_slots
    
    def set_failure_mode(self, 
                        token: bool = False, 
                        availability: bool = False, 
                        booking: bool = False,
                        reason: str = "Network error"):
        """Configure failure modes for testing."""
        self.should_fail_token = token
        self.should_fail_availability = availability
        self.should_fail_booking = booking
        self.failure_reason = reason
    
    def log_api_call(self, method: str, endpoint: str, success: bool, **kwargs):
        """Log API call for testing assertions."""
        self.api_call_log.append({
            "method": method,
            "endpoint": endpoint,
            "success": success,
            "timestamp": datetime.utcnow(),
            **kwargs
        })
    
    async def get_access_token(self) -> Optional[str]:
        """Mock token refresh."""
        self.token_refresh_count += 1
        self.log_api_call("POST", "/oauth/token", not self.should_fail_token)
        
        if self.should_fail_token:
            return None
        
        return f"mock_token_{self.token_refresh_count}"
    
    async def get_available_slots_for_date(self, 
                                         target_date: datetime,
                                         service_type: str = "lead_meeting") -> List[MockBookingSlot]:
        """Mock availability checking."""
        self.log_api_call("GET", "/availability", not self.should_fail_availability, 
                         date=target_date.date().isoformat())
        
        if self.should_fail_availability:
            return []
        
        date_str = target_date.date().isoformat()
        
        # Check if it's a holiday
        if date_str in self.holidays:
            return []
        
        # Check if it's a weekend
        if target_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return []
        
        # Get available times for this date
        available_times = self.availability_seed.get(date_str, [])
        
        slots = []
        for time_str in available_times:
            hour, minute = map(int, time_str.split(':'))
            start_time = target_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # Use first staff member and service
            staff = self.staff[0]
            service = self.services[0]
            
            slot = MockBookingSlot(
                staff_id=staff["id"],
                staff_name=staff["name"],
                start_time=start_time,
                end_time=start_time + timedelta(minutes=service["duration"]),
                service_id=service["id"],
                service_name=service["name"],
                duration_minutes=service["duration"],
                booking_url=f"https://bookings.zoho.com/book/{service['id']}"
            )
            slots.append(slot)
        
        return slots
    
    async def get_available_slots_for_date_range(self,
                                               start_date: datetime,
                                               end_date: datetime,
                                               timezone: str = "Asia/Kolkata",
                                               service_type: str = "lead_meeting") -> List[MockBookingSlot]:
        """Mock date range availability."""
        all_slots = []
        current_date = start_date.date()
        end_date_only = end_date.date()
        
        while current_date <= end_date_only:
            day_start = datetime.combine(current_date, time.min)
            daily_slots = await self.get_available_slots_for_date(day_start, service_type)
            all_slots.extend(daily_slots)
            current_date += timedelta(days=1)
        
        return all_slots
    
    async def get_slots_for_time_preference(self,
                                          date_preference: datetime,
                                          time_preference: str,
                                          timezone: str = "Asia/Kolkata",
                                          service_type: str = "lead_meeting") -> List[MockBookingSlot]:
        """Mock time preference filtering."""
        all_slots = await self.get_available_slots_for_date(date_preference, service_type)
        
        if not all_slots:
            return []
        
        # Filter by time preference
        filtered_slots = []
        time_pref_lower = time_preference.lower()
        
        for slot in all_slots:
            slot_hour = slot.start_time.hour
            
            if "morning" in time_pref_lower and 6 <= slot_hour < 12:
                filtered_slots.append(slot)
            elif "afternoon" in time_pref_lower and 12 <= slot_hour < 17:
                filtered_slots.append(slot)
            elif "evening" in time_pref_lower and 17 <= slot_hour < 21:
                filtered_slots.append(slot)
            elif "anytime" in time_pref_lower or "any time" in time_pref_lower:
                filtered_slots.append(slot)
            else:
                # Try to match specific time
                import re
                time_match = re.search(r'(\d{1,2}):?(\d{2})?\s*(am|pm)?', time_pref_lower)
                if time_match:
                    target_hour = int(time_match.group(1))
                    period = time_match.group(3)
                    
                    if period == "pm" and target_hour < 12:
                        target_hour += 12
                    elif period == "am" and target_hour == 12:
                        target_hour = 0
                    
                    # Match slots within 1 hour of target time
                    if abs(slot_hour - target_hour) <= 1:
                        filtered_slots.append(slot)
        
        return filtered_slots
    
    async def get_next_available_business_slots(self,
                                              from_date: datetime = None,
                                              max_slots: int = 5,
                                              timezone: str = "Asia/Kolkata",
                                              service_type: str = "lead_meeting") -> List[MockBookingSlot]:
        """Mock next available business slots."""
        if from_date is None:
            from_date = datetime.utcnow()
        
        # Look ahead up to 14 days
        end_date = from_date + timedelta(days=14)
        
        all_slots = await self.get_available_slots_for_date_range(
            from_date, end_date, timezone, service_type
        )
        
        # Filter to business hours only (9 AM - 5 PM, Monday-Friday)
        business_slots = []
        for slot in all_slots:
            if (slot.start_time.weekday() < 5 and  # Monday-Friday
                9 <= slot.start_time.hour < 17):  # 9 AM - 5 PM
                business_slots.append(slot)
                
                if len(business_slots) >= max_slots:
                    break
        
        return business_slots
    
    async def book_slot_for_meeting_agent(self,
                                        slot: MockBookingSlot,
                                        lead_data: Dict[str, Any],
                                        timezone: str = "Asia/Kolkata") -> MockBookingResult:
        """Mock booking creation."""
        self.booking_counter += 1
        booking_id = f"booking_{self.booking_counter}"
        
        self.log_api_call("POST", "/bookings", not self.should_fail_booking,
                         booking_id=booking_id, slot_time=slot.start_time.isoformat())
        
        if self.should_fail_booking:
            return MockBookingResult(
                success=False,
                error_message=self.failure_reason
            )
        
        # Store booking
        booking_data = {
            "id": booking_id,
            "slot": slot,
            "lead_data": lead_data,
            "timezone": timezone,
            "status": "confirmed",
            "created_at": datetime.utcnow()
        }
        self.bookings[booking_id] = booking_data
        
        # Remove slot from availability
        date_str = slot.start_time.date().isoformat()
        time_str = slot.start_time.strftime("%H:%M")
        if date_str in self.availability_seed and time_str in self.availability_seed[date_str]:
            self.availability_seed[date_str].remove(time_str)
        
        return MockBookingResult(
            success=True,
            booking_id=booking_id,
            meeting_link=f"https://zoom.us/meeting/{booking_id}",
            booking_url=f"https://bookings.zoho.com/booking/{booking_id}"
        )
    
    async def book_appointment(self,
                             slot: MockBookingSlot,
                             customer_name: str,
                             customer_email: str,
                             customer_phone: str,
                             notes: str = "",
                             lead_id: str = None,
                             timezone: str = "Asia/Kolkata") -> MockBookingResult:
        """Mock appointment booking (legacy method)."""
        lead_data = {
            "id": lead_id,
            "name": customer_name,
            "email": customer_email,
            "phone": customer_phone
        }
        
        return await self.book_slot_for_meeting_agent(slot, lead_data, timezone)
    
    async def reschedule_booking(self,
                               booking_id: str,
                               new_slot: MockBookingSlot) -> MockBookingResult:
        """Mock booking rescheduling."""
        self.log_api_call("PUT", f"/bookings/{booking_id}/reschedule", 
                         not self.should_fail_booking)
        
        if self.should_fail_booking:
            return MockBookingResult(
                success=False,
                error_message=self.failure_reason
            )
        
        if booking_id not in self.bookings:
            return MockBookingResult(
                success=False,
                error_message="Booking not found"
            )
        
        # Update booking
        self.bookings[booking_id]["slot"] = new_slot
        self.bookings[booking_id]["status"] = "rescheduled"
        self.bookings[booking_id]["updated_at"] = datetime.utcnow()
        
        return MockBookingResult(
            success=True,
            booking_id=booking_id,
            meeting_link=f"https://zoom.us/meeting/{booking_id}",
            booking_url=f"https://bookings.zoho.com/booking/{booking_id}"
        )
    
    async def cancel_booking_by_id(self, booking_id: str) -> bool:
        """Mock booking cancellation."""
        self.log_api_call("POST", f"/bookings/{booking_id}/cancel", 
                         not self.should_fail_booking)
        
        if self.should_fail_booking:
            return False
        
        if booking_id in self.bookings:
            self.bookings[booking_id]["status"] = "cancelled"
            self.bookings[booking_id]["cancelled_at"] = datetime.utcnow()
            return True
        
        return False
    
    def format_slot_for_display(self, slot: MockBookingSlot, timezone: str = "Asia/Kolkata") -> str:
        """Mock slot formatting."""
        return slot.start_time.strftime("%A, %B %d at %I:%M %p")
    
    def is_slot_available_now(self, slot: MockBookingSlot) -> bool:
        """Mock slot availability check."""
        return slot.start_time > datetime.utcnow()
    
    # Test assertion methods
    
    def assert_token_refreshed(self, times: int = 1):
        """Assert token was refreshed specified number of times."""
        assert self.token_refresh_count == times, \
            f"Expected {times} token refreshes, got {self.token_refresh_count}"
    
    def assert_availability_checked(self, date: str):
        """Assert availability was checked for a specific date."""
        availability_calls = [call for call in self.api_call_log 
                            if call["endpoint"] == "/availability" and call.get("date") == date]
        assert len(availability_calls) > 0, f"No availability check for date {date}"
    
    def assert_booking_created(self, booking_id: str):
        """Assert a booking was created."""
        assert booking_id in self.bookings, f"Booking {booking_id} not found"
        assert self.bookings[booking_id]["status"] in ["confirmed", "scheduled"]
    
    def assert_booking_cancelled(self, booking_id: str):
        """Assert a booking was cancelled."""
        assert booking_id in self.bookings, f"Booking {booking_id} not found"
        assert self.bookings[booking_id]["status"] == "cancelled"
    
    def get_booking(self, booking_id: str) -> Optional[Dict[str, Any]]:
        """Get booking data for testing."""
        return self.bookings.get(booking_id)
    
    def get_all_bookings(self) -> List[Dict[str, Any]]:
        """Get all bookings for testing."""
        return list(self.bookings.values())
    
    def get_api_calls(self, endpoint: str = None) -> List[Dict[str, Any]]:
        """Get API call log for testing."""
        if endpoint:
            return [call for call in self.api_call_log if call["endpoint"] == endpoint]
        return self.api_call_log.copy()
