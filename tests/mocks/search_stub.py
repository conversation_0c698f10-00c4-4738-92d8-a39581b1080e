"""
RAG Search Service Stub for Testing
Mock vector search with deterministic results for Q&A testing
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import uuid


@dataclass
class MockSearchResult:
    """Mock search result for testing."""
    content: str
    score: float
    metadata: Dict[str, Any]
    source: str


@dataclass
class MockEscalationQuestion:
    """Mock escalation question for testing."""
    id: str
    question: str
    answer: Optional[str]
    confidence: float
    category: str


class SearchStub:
    """
    Mock implementation of RAG search service for testing.
    Provides deterministic search results and escalation question handling.
    """
    
    def __init__(self):
        self.reset()
        
        # Pre-seeded knowledge base
        self.knowledge_base = [
            {
                "content": "Our franchise fees start at $50,000 plus GST for most territories.",
                "metadata": {"category": "fees", "source": "franchise_guide"},
                "keywords": ["franchise fee", "cost", "price", "fees", "investment"]
            },
            {
                "content": "We provide comprehensive training including 2 weeks of initial training and ongoing support.",
                "metadata": {"category": "training", "source": "training_manual"},
                "keywords": ["training", "support", "education", "learn", "course"]
            },
            {
                "content": "Our territories are available in major cities across Australia including Sydney, Melbourne, Brisbane, and Perth.",
                "metadata": {"category": "territories", "source": "territory_map"},
                "keywords": ["territory", "location", "area", "city", "region"]
            },
            {
                "content": "The typical ROI for our franchisees is 15-25% within the first 2 years of operation.",
                "metadata": {"category": "roi", "source": "financial_projections"},
                "keywords": ["roi", "return", "profit", "earnings", "income"]
            },
            {
                "content": "We offer financing options through our preferred lending partners with competitive rates.",
                "metadata": {"category": "financing", "source": "finance_guide"},
                "keywords": ["financing", "loan", "funding", "payment", "credit"]
            }
        ]
        
        # Escalation questions database
        self.escalation_questions = {}
        self.escalation_counter = 0
    
    def reset(self):
        """Reset stub state."""
        self.search_calls: List[Dict[str, Any]] = []
        self.escalation_questions.clear()
        self.escalation_counter = 0
        self.should_fail_search = False
        self.confidence_threshold = 0.7
    
    def set_failure_mode(self, should_fail: bool = False):
        """Configure search failure mode."""
        self.should_fail_search = should_fail
    
    def set_confidence_threshold(self, threshold: float):
        """Set confidence threshold for search results."""
        self.confidence_threshold = threshold
    
    async def search(self, 
                    query: str, 
                    top_k: int = 5, 
                    min_confidence: float = 0.5) -> List[MockSearchResult]:
        """
        Mock vector search with deterministic results.
        
        Args:
            query: Search query
            top_k: Number of results to return
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of mock search results
        """
        self.search_calls.append({
            "query": query,
            "top_k": top_k,
            "min_confidence": min_confidence,
            "timestamp": "2025-08-29T12:00:00+05:30"
        })
        
        if self.should_fail_search:
            return []
        
        # Simple keyword matching for deterministic results
        results = []
        query_lower = query.lower()
        
        for item in self.knowledge_base:
            # Calculate mock confidence based on keyword matches
            matches = sum(1 for keyword in item["keywords"] if keyword in query_lower)
            confidence = min(0.95, matches * 0.3 + 0.1)  # Cap at 0.95
            
            if confidence >= min_confidence and matches > 0:
                result = MockSearchResult(
                    content=item["content"],
                    score=confidence,
                    metadata=item["metadata"],
                    source=item["metadata"]["source"]
                )
                results.append(result)
        
        # Sort by confidence and return top_k
        results.sort(key=lambda x: x.score, reverse=True)
        return results[:top_k]
    
    async def search_with_context(self,
                                query: str,
                                context: Dict[str, Any],
                                top_k: int = 5) -> Tuple[List[MockSearchResult], bool]:
        """
        Mock contextual search with escalation detection.
        
        Returns:
            Tuple of (search_results, should_escalate)
        """
        results = await self.search(query, top_k)
        
        # Determine if we should escalate based on confidence
        should_escalate = False
        if not results or (results and results[0].score < self.confidence_threshold):
            should_escalate = True
            await self.create_escalation_question(query, context)
        
        return results, should_escalate
    
    async def create_escalation_question(self,
                                       question: str,
                                       context: Dict[str, Any]) -> str:
        """
        Mock escalation question creation.
        
        Args:
            question: The unanswered question
            context: Context from the conversation
            
        Returns:
            Escalation question ID
        """
        self.escalation_counter += 1
        escalation_id = f"escalation_{self.escalation_counter}"
        
        # Determine category based on keywords
        category = "general"
        question_lower = question.lower()
        
        if any(word in question_lower for word in ["fee", "cost", "price", "money"]):
            category = "fees"
        elif any(word in question_lower for word in ["training", "support", "help"]):
            category = "training"
        elif any(word in question_lower for word in ["territory", "location", "area"]):
            category = "territories"
        elif any(word in question_lower for word in ["roi", "profit", "return", "earnings"]):
            category = "roi"
        elif any(word in question_lower for word in ["financing", "loan", "funding"]):
            category = "financing"
        
        escalation = MockEscalationQuestion(
            id=escalation_id,
            question=question,
            answer=None,  # Will be populated by admin later
            confidence=0.0,  # Low confidence triggered escalation
            category=category
        )
        
        self.escalation_questions[escalation_id] = escalation
        return escalation_id
    
    async def get_escalation_question(self, escalation_id: str) -> Optional[MockEscalationQuestion]:
        """Get escalation question by ID."""
        return self.escalation_questions.get(escalation_id)
    
    async def answer_escalation_question(self,
                                       escalation_id: str,
                                       answer: str) -> bool:
        """
        Mock admin answering an escalation question.
        
        Args:
            escalation_id: ID of the escalation question
            answer: The answer provided by admin
            
        Returns:
            True if successful
        """
        if escalation_id in self.escalation_questions:
            self.escalation_questions[escalation_id].answer = answer
            self.escalation_questions[escalation_id].confidence = 0.9  # High confidence after admin answer
            
            # Add to knowledge base for future searches
            question_obj = self.escalation_questions[escalation_id]
            self.knowledge_base.append({
                "content": answer,
                "metadata": {
                    "category": question_obj.category,
                    "source": "escalation_answer",
                    "escalation_id": escalation_id
                },
                "keywords": self._extract_keywords(question_obj.question + " " + answer)
            })
            
            return True
        
        return False
    
    async def search_similar_escalations(self, question: str) -> List[MockEscalationQuestion]:
        """
        Mock search for similar escalation questions.
        
        Args:
            question: Question to find similar escalations for
            
        Returns:
            List of similar escalation questions
        """
        similar = []
        question_lower = question.lower()
        
        for escalation in self.escalation_questions.values():
            if escalation.answer:  # Only return answered escalations
                # Simple similarity based on common words
                escalation_words = set(escalation.question.lower().split())
                question_words = set(question_lower.split())
                
                overlap = len(escalation_words.intersection(question_words))
                if overlap >= 2:  # At least 2 common words
                    similar.append(escalation)
        
        return similar
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text for search indexing."""
        # Simple keyword extraction
        words = text.lower().split()
        # Filter out common stop words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        return list(set(keywords))  # Remove duplicates
    
    # Test assertion methods
    
    def assert_search_called(self, query_contains: str):
        """Assert that search was called with query containing specific text."""
        for call in self.search_calls:
            if query_contains.lower() in call["query"].lower():
                return True
        
        raise AssertionError(
            f"No search call found containing '{query_contains}'. "
            f"Actual queries: {[call['query'] for call in self.search_calls]}"
        )
    
    def assert_escalation_created(self, question_contains: str) -> str:
        """Assert that an escalation question was created containing specific text."""
        for escalation_id, escalation in self.escalation_questions.items():
            if question_contains.lower() in escalation.question.lower():
                return escalation_id
        
        raise AssertionError(
            f"No escalation question found containing '{question_contains}'. "
            f"Actual questions: {[eq.question for eq in self.escalation_questions.values()]}"
        )
    
    def assert_no_escalation_created(self):
        """Assert that no escalation questions were created."""
        assert len(self.escalation_questions) == 0, \
            f"Expected no escalations, but found: {list(self.escalation_questions.keys())}"
    
    def get_search_calls(self) -> List[Dict[str, Any]]:
        """Get all search calls for testing."""
        return self.search_calls.copy()
    
    def get_escalation_questions(self) -> List[MockEscalationQuestion]:
        """Get all escalation questions for testing."""
        return list(self.escalation_questions.values())
    
    def seed_knowledge_item(self, content: str, category: str, keywords: List[str]):
        """Add a custom knowledge item for testing."""
        self.knowledge_base.append({
            "content": content,
            "metadata": {"category": category, "source": "test_seed"},
            "keywords": keywords
        })
    
    def seed_answered_escalation(self, question: str, answer: str, category: str = "general") -> str:
        """Seed an already-answered escalation question for testing."""
        escalation_id = f"seed_escalation_{len(self.escalation_questions) + 1}"
        
        escalation = MockEscalationQuestion(
            id=escalation_id,
            question=question,
            answer=answer,
            confidence=0.9,
            category=category
        )
        
        self.escalation_questions[escalation_id] = escalation
        
        # Also add to knowledge base
        self.knowledge_base.append({
            "content": answer,
            "metadata": {
                "category": category,
                "source": "escalation_answer",
                "escalation_id": escalation_id
            },
            "keywords": self._extract_keywords(question + " " + answer)
        })
        
        return escalation_id


# Test utilities for search scenarios

def create_high_confidence_query() -> str:
    """Create a query that should return high-confidence results."""
    return "What are the franchise fees and costs?"


def create_low_confidence_query() -> str:
    """Create a query that should trigger escalation."""
    return "What is your policy on purple unicorns in the franchise agreement?"


def create_partial_match_query() -> str:
    """Create a query that should return partial matches."""
    return "Tell me about the training program duration"


def create_no_match_query() -> str:
    """Create a query that should return no matches."""
    return "xyz123 nonexistent query terms abc456"
