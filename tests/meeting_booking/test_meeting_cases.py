"""
Meeting Booking Cases Tests
Tests for all 11 specific meeting booking scenarios with exact transcript expectations
"""

import pytest
from datetime import datetime
from unittest.mock import patch, AsyncMock

from app.meeting_booking.orchestrator import MeetingOrchestrator
from app.meeting_booking.fsm import MeetingState


class TestMeetingBookingCases:
    """Test all 11 meeting booking cases with exact workflow patterns"""
    
    @pytest.mark.asyncio
    async def test_case_1_clear_date_time(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 1: Clear Date & Time - Direct booking"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User provides clear date and time
            result = await orchestrator.handle_message(
                phone_number="+61940888001",
                message="1 September at 10 AM",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "booked" in result["message"].lower()
            assert "1 september" in result["message"].lower()
            assert "10:00 am" in result["message"].lower()
            assert result["booking_id"] is not None
            assert result["meeting_link"] is not None
            
            # Verify no emojis
            assert not any(char in result["message"] for char in "😀😃😄😁😆😅😂🤣")
    
    @pytest.mark.asyncio
    async def test_case_2_range_of_dates(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 2: Range of Dates - Clarification needed"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User provides date range
            result = await orchestrator.handle_message(
                phone_number="+61940888002",
                message="I'm available on the 3rd or 4th",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "which day would you prefer" in result["message"].lower()
            assert "3 september" in result["message"].lower()
            assert "4 september" in result["message"].lower()
            assert result["state"] == MeetingState.COLLECT_DAY.value
    
    @pytest.mark.asyncio
    async def test_case_3_time_only(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 3: Time Only - Need date clarification"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User provides only time
            result = await orchestrator.handle_message(
                phone_number="+61940888003",
                message="I'm free at 5 PM",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "which day" in result["message"].lower()
            assert "5:00 pm" in result["message"].lower()
            assert result["state"] == MeetingState.COLLECT_DAY.value
    
    @pytest.mark.asyncio
    async def test_case_4_anytime_tomorrow(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 4: Anytime Tomorrow - Show available slots"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User says anytime tomorrow
            result = await orchestrator.handle_message(
                phone_number="+61940888004",
                message="I'm free anytime tomorrow",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "tomorrow is 30 august" in result["message"].lower()
            assert "available slots" in result["message"].lower()
            assert "9:30 am" in result["message"].lower()
            assert "12:00 pm" in result["message"].lower()
            assert "3:00 pm" in result["message"].lower()
            assert result["state"] == MeetingState.AWAIT_CONFIRM.value
    
    @pytest.mark.asyncio
    async def test_case_5_weekday_availability(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 5: Weekday Availability Question"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User asks about Monday availability
            result = await orchestrator.handle_message(
                phone_number="+61940888005",
                message="What times do you have on Monday?",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "monday (1 september)" in result["message"].lower()
            assert "10:00 am" in result["message"].lower()
            assert "1:00 pm" in result["message"].lower()
            assert "4:30 pm" in result["message"].lower()
            assert result["state"] == MeetingState.AWAIT_CONFIRM.value
    
    @pytest.mark.asyncio
    async def test_case_6_date_only(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 6: Date Only - Show available slots"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User provides only date
            result = await orchestrator.handle_message(
                phone_number="+61940888006",
                message="Can we do 5th September?",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "5 september" in result["message"].lower()
            assert "available" in result["message"].lower()
            assert "9:00 am" in result["message"].lower()
            assert "11:30 am" in result["message"].lower()
            assert "2:00 pm" in result["message"].lower()
            assert result["state"] == MeetingState.AWAIT_CONFIRM.value
    
    @pytest.mark.asyncio
    async def test_case_9_next_week(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 9: Next Week - Show weekday options"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # User says next week
            result = await orchestrator.handle_message(
                phone_number="+61940888009",
                message="Can we do sometime next week?",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "which day next week" in result["message"].lower()
            assert "monday" in result["message"].lower()
            assert "tuesday" in result["message"].lower()
            assert "wednesday" in result["message"].lower()
            assert result["state"] == MeetingState.COLLECT_DAY.value
    
    @pytest.mark.asyncio
    async def test_case_10_user_defers(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 10: User Defers - Park context"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # First show slots
            await orchestrator.handle_message(
                phone_number="+**********0",
                message="Can we do 7 September?",
                lead_id="test_lead_123"
            )
            
            # User defers decision
            result = await orchestrator.handle_message(
                phone_number="+**********0",
                message="I'll check and get back",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "keep these options handy" in result["message"].lower()
            assert "when you're ready" in result["message"].lower()
            # Context should be parked with longer expiry
    
    @pytest.mark.asyncio
    async def test_case_11_holiday_no_slots(self, mock_zoho_provider, mock_openai, frozen_time):
        """Case 11: Holiday/No Slots - Suggest alternatives"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            # Mock no availability for Sunday (31 August)
            async def mock_no_availability(date_local, timezone_str=None):
                date_key = date_local.strftime("%Y-%m-%d")
                if date_key == "2025-08-31":
                    return []  # No slots on Sunday
                # Return normal availability for other days
                availability_seed = {
                    "2025-09-01": [
                        # Monday slots as fallback
                    ]
                }
                return availability_seed.get(date_key, [])
            
            mock_zoho_provider.get_availability.side_effect = mock_no_availability
            
            orchestrator = MeetingOrchestrator()
            
            # User requests Sunday (holiday)
            result = await orchestrator.handle_message(
                phone_number="+**********1",
                message="Can we do 31 August?",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "31 august falls on a sunday" in result["message"].lower()
            assert "don't have any slots that day" in result["message"].lower()
            assert "monday (1 september)" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_slot_confirmation_flow(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test complete slot confirmation flow"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # First, show available slots
            result1 = await orchestrator.handle_message(
                phone_number="+**********2",
                message="Can we do 1 September?",
                lead_id="test_lead_123"
            )
            
            assert result1["handled"] is True
            assert result1["state"] == MeetingState.AWAIT_CONFIRM.value
            
            # Then confirm a slot
            result2 = await orchestrator.handle_message(
                phone_number="+**********2",
                message="1 PM works for me",
                lead_id="test_lead_123"
            )
            
            assert result2["handled"] is True
            assert result2["state"] == MeetingState.BOOKED.value
            assert "booked" in result2["message"].lower()
            assert "1 september" in result2["message"].lower()
            assert "1:00 pm" in result2["message"].lower()
            assert result2["booking_id"] is not None
            assert result2["meeting_link"] is not None
    
    @pytest.mark.asyncio
    async def test_reschedule_flow(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test meeting reschedule flow"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # First book a meeting
            await orchestrator.handle_message(
                phone_number="+**********3",
                message="1 September at 10 AM",
                lead_id="test_lead_123"
            )
            
            # Then reschedule
            result = await orchestrator.handle_message(
                phone_number="+**********3",
                message="Can we move my meeting to Friday afternoon?",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            assert "reschedule" in result["message"].lower()
            assert "which day would you prefer" in result["message"].lower()
            assert result["state"] == MeetingState.RESCHEDULING.value
    
    @pytest.mark.asyncio
    async def test_cancel_flow(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test meeting cancellation flow"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # First book a meeting
            await orchestrator.handle_message(
                phone_number="+**********4",
                message="1 September at 10 AM",
                lead_id="test_lead_123"
            )
            
            # Then cancel
            result1 = await orchestrator.handle_message(
                phone_number="+**********4",
                message="Cancel my meeting",
                lead_id="test_lead_123"
            )
            
            assert result1["handled"] is True
            assert "cancel your meeting" in result1["message"].lower()
            assert "proceed now" in result1["message"].lower()
            assert result1["state"] == MeetingState.CANCELING.value
            
            # Confirm cancellation
            result2 = await orchestrator.handle_message(
                phone_number="+**********4",
                message="Yes",
                lead_id="test_lead_123"
            )
            
            assert result2["handled"] is True
            assert "canceled" in result2["message"].lower()
            assert result2["state"] == MeetingState.IDLE.value
    
    @pytest.mark.asyncio
    async def test_no_emojis_in_responses(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test that no emojis appear in any responses"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            test_messages = [
                "I want to schedule a meeting",
                "Can we do Tuesday at 2pm?",
                "What times are available?",
                "I'm free anytime tomorrow",
                "Cancel my meeting"
            ]
            
            emoji_chars = "😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🥸🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾"
            
            for message in test_messages:
                result = await orchestrator.handle_message(
                    phone_number=f"+**********{len(message)}",
                    message=message,
                    lead_id="test_lead_123"
                )
                
                # Check no emojis in response
                assert not any(char in result["message"] for char in emoji_chars), f"Emoji found in response: {result['message']}"
