"""
Stability and Loop Prevention Tests
Tests for loop prevention, backoff behavior, and handling of edge cases
"""

import pytest
from unittest.mock import patch, AsyncMock
from datetime import datetime

from app.meeting_booking.orchestrator import MeetingOrchestrator
from app.meeting_booking.fsm import MeetingBookingFSM, MeetingContext, MeetingState
from app.meeting_booking.providers import BookingResult


class TestLoopPrevention:
    """Test loop prevention and conversation stability"""
    
    @pytest.mark.asyncio
    async def test_repeated_message_detection(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test detection and handling of repeated user messages"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            phone_number = "+**********0"
            
            # Send same message multiple times
            repeated_message = "maybe sometime"
            
            # First time - normal response
            result1 = await orchestrator.handle_message(
                phone_number=phone_number,
                message=repeated_message,
                lead_id="test_lead_123"
            )
            assert result1["handled"] is True
            
            # Second time - still normal
            result2 = await orchestrator.handle_message(
                phone_number=phone_number,
                message=repeated_message,
                lead_id="test_lead_123"
            )
            assert result2["handled"] is True
            
            # Third time - should trigger loop prevention
            result3 = await orchestrator.handle_message(
                phone_number=phone_number,
                message=repeated_message,
                lead_id="test_lead_123"
            )
            assert result3["handled"] is True
            # Should provide specific options instead of asking again
            assert "specific options" in result3["message"].lower() or "connect you with our team" in result3["message"].lower()
    
    @pytest.mark.asyncio
    async def test_clarification_count_limit(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test maximum clarification attempts"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            phone_number = "+**********1"
            
            # Send unclear messages that require clarification
            unclear_messages = [
                "maybe",
                "not sure",
                "hmm"
            ]
            
            results = []
            for message in unclear_messages:
                result = await orchestrator.handle_message(
                    phone_number=phone_number,
                    message=message,
                    lead_id="test_lead_123"
                )
                results.append(result)
            
            # After max clarifications, should provide specific options or escalate
            final_result = results[-1]
            assert final_result["handled"] is True
            assert ("specific options" in final_result["message"].lower() or 
                   "connect you with our team" in final_result["message"].lower())
    
    @pytest.mark.asyncio
    async def test_ambiguous_input_handling(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of ambiguous user inputs"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            ambiguous_inputs = [
                "late evening sometime",
                "whenever",
                "flexible",
                "not sure about timing"
            ]
            
            for i, message in enumerate(ambiguous_inputs):
                phone_number = f"+**********{i}"
                result = await orchestrator.handle_message(
                    phone_number=phone_number,
                    message=message,
                    lead_id="test_lead_123"
                )
                
                assert result["handled"] is True
                # Should ask for clarification or provide options
                assert any(keyword in result["message"].lower() for keyword in 
                          ["which day", "what time", "specific", "available"])
    
    @pytest.mark.asyncio
    async def test_context_expiry_handling(self, mock_redis, mock_zoho_provider, frozen_time):
        """Test handling of expired conversation context"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            # Mock expired context
            mock_redis.get.return_value = None  # Simulate expired context
            
            orchestrator = MeetingOrchestrator()
            
            # Try to continue a conversation with expired context
            result = await orchestrator.handle_message(
                phone_number="+**********2",
                message="Yes, that time works",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Should start fresh conversation
            assert any(keyword in result["message"].lower() for keyword in 
                      ["schedule", "meeting", "when would you like"])
    
    @pytest.mark.asyncio
    async def test_invalid_slot_selection(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of invalid slot selections"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            phone_number = "+**********3"
            
            # First show slots
            await orchestrator.handle_message(
                phone_number=phone_number,
                message="Can we do 1 September?",
                lead_id="test_lead_123"
            )
            
            # Try to select invalid slot
            result = await orchestrator.handle_message(
                phone_number=phone_number,
                message="5 PM",  # Not in available slots
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Should clarify or show available options again
            assert any(keyword in result["message"].lower() for keyword in 
                      ["which", "available", "prefer", "specify"])
    
    @pytest.mark.asyncio
    async def test_conversation_state_recovery(self, mock_redis, mock_zoho_provider, frozen_time):
        """Test recovery from corrupted conversation state"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            # Mock corrupted context data
            import json
            corrupted_data = json.dumps({"invalid": "data"})
            mock_redis.get.return_value = corrupted_data
            
            orchestrator = MeetingOrchestrator()
            
            result = await orchestrator.handle_message(
                phone_number="+**********4",
                message="I want to schedule a meeting",
                lead_id="test_lead_123"
            )
            
            # Should handle gracefully and start fresh
            assert result["handled"] is True
            assert any(keyword in result["message"].lower() for keyword in 
                      ["schedule", "meeting", "when"])


class TestProviderFailureHandling:
    """Test handling of provider failures and retries"""
    
    @pytest.mark.asyncio
    async def test_provider_timeout_retry(self, mock_openai, frozen_time):
        """Test retry behavior on provider timeouts"""
        # Mock provider that fails first few times then succeeds
        mock_provider = AsyncMock()
        
        # First 2 calls fail, 3rd succeeds
        mock_provider.get_availability.side_effect = [
            Exception("Timeout"),
            Exception("Timeout"),
            []  # Success but no slots
        ]
        
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_provider):
            orchestrator = MeetingOrchestrator()
            
            result = await orchestrator.handle_message(
                phone_number="+**********5",
                message="Can we do tomorrow?",
                lead_id="test_lead_123"
            )
            
            # Should eventually succeed or provide graceful fallback
            assert result["handled"] is True
            # Should have retried and either succeeded or provided fallback
            assert mock_provider.get_availability.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_booking_failure_recovery(self, mock_openai, frozen_time):
        """Test recovery from booking failures"""
        mock_provider = AsyncMock()
        
        # Mock availability success but booking failure
        from app.meeting_booking.providers import BookingSlot
        mock_slots = [
            BookingSlot(
                staff_id="staff_1",
                staff_name="Andy",
                start_time=datetime(2025, 9, 1, 10, 0),
                end_time=datetime(2025, 9, 1, 10, 30),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            )
        ]
        mock_provider.get_availability.return_value = mock_slots
        
        # Booking fails
        mock_provider.book_appointment.return_value = BookingResult(
            success=False,
            error_message="Booking failed"
        )
        
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_provider):
            orchestrator = MeetingOrchestrator()
            
            # Show slots
            await orchestrator.handle_message(
                phone_number="+**********6",
                message="Can we do 1 September?",
                lead_id="test_lead_123"
            )
            
            # Try to book
            result = await orchestrator.handle_message(
                phone_number="+**********6",
                message="10 AM works",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Should handle booking failure gracefully
            assert "issue" in result["message"].lower() or "other" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, mock_openai, frozen_time):
        """Test handling of API rate limits"""
        mock_provider = AsyncMock()
        
        # Mock rate limit error
        mock_provider.get_availability.side_effect = Exception("Rate limit exceeded")
        
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_provider):
            orchestrator = MeetingOrchestrator()
            
            result = await orchestrator.handle_message(
                phone_number="+**********7",
                message="What times are available?",
                lead_id="test_lead_123"
            )
            
            # Should provide graceful fallback
            assert result["handled"] is True
            assert any(keyword in result["message"].lower() for keyword in 
                      ["connect", "team", "shortly", "help"])


class TestEdgeCases:
    """Test edge cases and unusual scenarios"""
    
    @pytest.mark.asyncio
    async def test_very_long_message(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of very long user messages"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # Very long message
            long_message = "I would like to schedule a meeting " * 50
            
            result = await orchestrator.handle_message(
                phone_number="+**********8",
                message=long_message,
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Should handle gracefully
            assert len(result["message"]) < 500  # Response should be reasonable length
    
    @pytest.mark.asyncio
    async def test_special_characters_in_message(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of special characters and emojis in user messages"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # Message with special characters and emojis
            special_message = "Can we do Tuesday @ 2pm? 😊 Thanks!"
            
            result = await orchestrator.handle_message(
                phone_number="+**********9",
                message=special_message,
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Response should not contain emojis
            emoji_chars = "😀😃😄😁😆😅😂🤣😊"
            assert not any(char in result["message"] for char in emoji_chars)
    
    @pytest.mark.asyncio
    async def test_multiple_date_time_mentions(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of messages with multiple date/time mentions"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # Message with multiple times
            multi_time_message = "I'm free Monday at 10am, Tuesday at 2pm, or Wednesday at 3pm"
            
            result = await orchestrator.handle_message(
                phone_number="+61940888110",
                message=multi_time_message,
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Should handle gracefully, possibly asking for clarification
            assert any(keyword in result["message"].lower() for keyword in 
                      ["which", "prefer", "available", "clarify"])
    
    @pytest.mark.asyncio
    async def test_past_date_handling(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of past dates"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # Request past date
            result = await orchestrator.handle_message(
                phone_number="+61940888111",
                message="Can we do yesterday?",
                lead_id="test_lead_123"
            )
            
            assert result["handled"] is True
            # Should handle gracefully and suggest future dates
            assert any(keyword in result["message"].lower() for keyword in 
                      ["available", "future", "upcoming", "next"])
    
    @pytest.mark.asyncio
    async def test_concurrent_conversations(self, mock_zoho_provider, mock_openai, frozen_time):
        """Test handling of concurrent conversations from different numbers"""
        with patch('app.meeting_booking.orchestrator.ZohoBookingsProvider', return_value=mock_zoho_provider):
            orchestrator = MeetingOrchestrator()
            
            # Start conversations from different numbers simultaneously
            phone1 = "+61940888112"
            phone2 = "+61940888113"
            
            # Both start scheduling
            result1 = await orchestrator.handle_message(
                phone_number=phone1,
                message="I want to schedule a meeting",
                lead_id="test_lead_123"
            )
            
            result2 = await orchestrator.handle_message(
                phone_number=phone2,
                message="Can we do Tuesday?",
                lead_id="test_lead_456"
            )
            
            # Both should be handled independently
            assert result1["handled"] is True
            assert result2["handled"] is True
            
            # Contexts should be separate
            assert result1["context"]["phone_number"] == phone1
            assert result2["context"]["phone_number"] == phone2
