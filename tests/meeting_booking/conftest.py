"""
Meeting Booking Test Configuration
Global fixtures for meeting booking tests with mocks and frozen time
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
import pytz
from freezegun import freeze_time

from app.meeting_booking.fsm import MeetingBookingFSM, MeetingContext, MeetingState
from app.meeting_booking.orchestrator import MeetingOrchestrator
from app.meeting_booking.providers import ZohoBookingsProvider, BookingSlot, BookingResult
from app.meeting_booking.timezone_handler import TimezoneHandler
from app.core.redis_client import get_redis_client


@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def frozen_time():
    """Freeze time to 2025-08-29T12:00:00+05:30 for consistent testing"""
    with freeze_time("2025-08-29 12:00:00", tz_offset=5.5):
        yield datetime(2025, 8, 29, 12, 0, 0, tzinfo=pytz.timezone('Asia/Kolkata'))


@pytest.fixture
def default_timezone():
    """Default timezone for testing"""
    return "Asia/Kolkata"


@pytest.fixture
def mock_redis():
    """Mock Redis client"""
    mock_redis = MagicMock()
    mock_redis.get.return_value = None
    mock_redis.setex.return_value = True
    mock_redis.delete.return_value = True
    
    with patch('app.core.redis_client.get_redis_client', return_value=mock_redis):
        yield mock_redis


@pytest.fixture
def mock_zoho_provider():
    """Mock Zoho Bookings Provider with seeded availability"""
    mock_provider = AsyncMock(spec=ZohoBookingsProvider)
    
    # Seeded availability data
    availability_seed = {
        "2025-08-30": [
            BookingSlot(
                staff_id="staff_1",
                staff_name="Andy",
                start_time=datetime(2025, 8, 30, 9, 30, tzinfo=pytz.UTC),
                end_time=datetime(2025, 8, 30, 10, 0, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_1", 
                staff_name="Andy",
                start_time=datetime(2025, 8, 30, 12, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 8, 30, 12, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting", 
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_2",
                staff_name="Frank",
                start_time=datetime(2025, 8, 30, 15, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 8, 30, 15, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            )
        ],
        "2025-09-01": [
            BookingSlot(
                staff_id="staff_1",
                staff_name="Andy", 
                start_time=datetime(2025, 9, 1, 10, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 1, 10, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_2",
                staff_name="Frank",
                start_time=datetime(2025, 9, 1, 13, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 1, 13, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_3",
                staff_name="Saumil",
                start_time=datetime(2025, 9, 1, 16, 30, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 1, 17, 0, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            )
        ],
        "2025-09-03": [
            BookingSlot(
                staff_id="staff_1",
                staff_name="Andy",
                start_time=datetime(2025, 9, 3, 11, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 3, 11, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_2",
                staff_name="Frank",
                start_time=datetime(2025, 9, 3, 13, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 3, 13, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_3",
                staff_name="Saumil",
                start_time=datetime(2025, 9, 3, 15, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 3, 15, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            )
        ],
        "2025-09-05": [
            BookingSlot(
                staff_id="staff_1",
                staff_name="Andy",
                start_time=datetime(2025, 9, 5, 9, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 5, 9, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_2",
                staff_name="Frank",
                start_time=datetime(2025, 9, 5, 11, 30, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 5, 12, 0, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            ),
            BookingSlot(
                staff_id="staff_3",
                staff_name="Saumil",
                start_time=datetime(2025, 9, 5, 14, 0, tzinfo=pytz.UTC),
                end_time=datetime(2025, 9, 5, 14, 30, tzinfo=pytz.UTC),
                service_id="lead_meeting",
                service_name="Lead Meeting",
                duration_minutes=30
            )
        ]
    }
    
    # Mock get_availability method
    async def mock_get_availability(date_local, timezone_str=None):
        date_key = date_local.strftime("%Y-%m-%d")
        return availability_seed.get(date_key, [])
    
    mock_provider.get_availability.side_effect = mock_get_availability
    
    # Mock successful booking
    async def mock_book_appointment(*args, **kwargs):
        return BookingResult(
            success=True,
            booking_id="test_booking_123",
            zoho_booking_id="zoho_123",
            meeting_link="https://zoom.us/j/123456789",
            booking_url="https://bookings.zoho.com/booking/123"
        )
    
    mock_provider.book_appointment.side_effect = mock_book_appointment
    
    return mock_provider


@pytest.fixture
def timezone_handler():
    """Timezone handler for testing"""
    return TimezoneHandler("Asia/Kolkata")


@pytest.fixture
def meeting_fsm(mock_redis):
    """Meeting FSM with mocked Redis"""
    return MeetingBookingFSM(redis_client=mock_redis)


@pytest.fixture
def sample_context():
    """Sample meeting context for testing"""
    return MeetingContext(
        lead_id="test_lead_123",
        phone_number="+61940888001",
        session_id="test_session_123",
        timezone="Asia/Kolkata"
    )


@pytest.fixture
def mock_openai():
    """Mock OpenAI responses for NLU"""
    with patch('langchain_openai.ChatOpenAI') as mock_llm:
        mock_instance = AsyncMock()
        mock_llm.return_value = mock_instance
        
        # Default responses for different scenarios
        async def mock_ainvoke(messages):
            content = messages[-1].content.lower()
            
            if "intent" in content:
                if "schedule" in content or "meeting" in content:
                    return MagicMock(content="SCHEDULE")
                elif "cancel" in content:
                    return MagicMock(content="CANCEL")
                elif "reschedule" in content:
                    return MagicMock(content="RESCHEDULE")
                elif "yes" in content or "confirm" in content:
                    return MagicMock(content="CONFIRM")
                else:
                    return MagicMock(content="UNKNOWN")
            
            elif "extract datetime" in content:
                if "tuesday" in content and "2pm" in content:
                    return MagicMock(content='{"dates": [], "times": ["14:00"], "weekdays": ["tuesday"], "date_ranges": [], "time_ranges": [], "relative_dates": [], "anytime_indicators": []}')
                elif "tomorrow" in content:
                    return MagicMock(content='{"dates": [], "times": [], "weekdays": [], "date_ranges": [], "time_ranges": [], "relative_dates": ["tomorrow"], "anytime_indicators": []}')
                elif "anytime" in content:
                    return MagicMock(content='{"dates": [], "times": [], "weekdays": [], "date_ranges": [], "time_ranges": [], "relative_dates": [], "anytime_indicators": ["anytime"]}')
                else:
                    return MagicMock(content='{"dates": [], "times": [], "weekdays": [], "date_ranges": [], "time_ranges": [], "relative_dates": [], "anytime_indicators": []}')
            
            else:
                # Natural response generation
                return MagicMock(content="Which day would work best for you?")
        
        mock_instance.ainvoke.side_effect = mock_ainvoke
        yield mock_instance


@pytest.fixture
def mock_db():
    """Mock database session"""
    with patch('app.core.database.connection.get_db') as mock_get_db:
        mock_session = AsyncMock()
        mock_get_db.return_value.__aenter__.return_value = mock_session
        yield mock_session


@pytest.fixture
def log_capture():
    """Capture logs for testing"""
    import logging
    import io
    
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    logger = logging.getLogger('app.meeting_booking')
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    yield log_capture
    
    logger.removeHandler(handler)
