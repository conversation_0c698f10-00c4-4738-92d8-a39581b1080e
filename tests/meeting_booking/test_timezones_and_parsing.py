"""
Timezone and Parsing Tests
Tests for timezone conversions, date/time parsing, and calendar correctness
"""

import pytest
from datetime import datetime, time
import pytz
from freezegun import freeze_time

from app.meeting_booking.timezone_handler import TimezoneHandler


class TestTimezoneHandling:
    """Test timezone conversions and date/time resolution"""
    
    def test_timezone_initialization(self):
        """Test timezone handler initialization"""
        handler = TimezoneHandler("Asia/Kolkata")
        assert handler.default_timezone == "Asia/Kolkata"
        assert handler.default_tz.zone == "Asia/Kolkata"
    
    def test_get_user_timezone(self):
        """Test getting user timezone with fallback"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Valid timezone
        tz = handler.get_user_timezone("America/New_York")
        assert tz.zone == "America/New_York"
        
        # Invalid timezone - should fallback
        tz = handler.get_user_timezone("Invalid/Timezone")
        assert tz.zone == "Asia/Kolkata"
        
        # None timezone - should use default
        tz = handler.get_user_timezone(None)
        assert tz.zone == "Asia/Kolkata"
    
    @freeze_time("2025-08-29 12:00:00")
    def test_now_in_timezone(self):
        """Test getting current time in timezone"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Default timezone
        now = handler.now_in_timezone()
        assert now.tzinfo.zone == "Asia/Kolkata"
        
        # Specific timezone
        now_ny = handler.now_in_timezone("America/New_York")
        assert now_ny.tzinfo.zone == "America/New_York"
    
    def test_utc_conversions(self):
        """Test UTC to/from local timezone conversions"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Create a local datetime
        local_tz = pytz.timezone("Asia/Kolkata")
        local_dt = local_tz.localize(datetime(2025, 8, 29, 15, 30, 0))
        
        # Convert to UTC
        utc_dt = handler.to_utc(local_dt)
        assert utc_dt.tzinfo == pytz.UTC
        assert utc_dt.hour == 10  # 15:30 IST = 10:00 UTC
        
        # Convert back to local
        back_to_local = handler.from_utc(utc_dt, "Asia/Kolkata")
        assert back_to_local.hour == 15
        assert back_to_local.minute == 30
    
    @freeze_time("2025-08-29 12:00:00")
    def test_resolve_relative_dates(self):
        """Test resolving relative date terms"""
        handler = TimezoneHandler("Asia/Kolkata")
        reference = handler.now_in_timezone()
        
        # Today
        today = handler.resolve_relative_date("today", reference)
        assert today.date() == reference.date()
        assert today.hour == 0
        
        # Tomorrow
        tomorrow = handler.resolve_relative_date("tomorrow", reference)
        assert tomorrow.date() == datetime(2025, 8, 30).date()
        
        # Next week (should be next Monday)
        next_week = handler.resolve_relative_date("next week", reference)
        assert next_week.weekday() == 0  # Monday
        assert next_week.date() == datetime(2025, 9, 1).date()
    
    @freeze_time("2025-08-29 12:00:00")  # Friday
    def test_resolve_weekdays(self):
        """Test resolving weekday names to dates"""
        handler = TimezoneHandler("Asia/Kolkata")
        reference = handler.now_in_timezone()
        
        # Next Monday
        monday = handler.resolve_weekday("monday", reference)
        assert monday.weekday() == 0
        assert monday.date() == datetime(2025, 9, 1).date()
        
        # Next Tuesday
        tuesday = handler.resolve_weekday("tuesday", reference)
        assert tuesday.weekday() == 1
        assert tuesday.date() == datetime(2025, 9, 2).date()
        
        # Next Friday (should be next week since today is Friday)
        friday = handler.resolve_weekday("friday", reference)
        assert friday.weekday() == 4
        assert friday.date() == datetime(2025, 9, 5).date()
    
    def test_resolve_time_ranges(self):
        """Test resolving time range terms"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Morning
        start, end = handler.resolve_time_range("morning")
        assert start == time(9, 0)
        assert end == time(12, 0)
        
        # Afternoon
        start, end = handler.resolve_time_range("afternoon")
        assert start == time(13, 0)
        assert end == time(17, 0)
        
        # Evening
        start, end = handler.resolve_time_range("evening")
        assert start == time(17, 0)
        assert end == time(20, 0)
        
        # Noon
        start, end = handler.resolve_time_range("noon")
        assert start == time(12, 0)
        assert end == time(12, 30)
    
    def test_parse_time_strings(self):
        """Test parsing various time string formats"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # 12-hour format with AM/PM
        time_obj = handler.parse_time_string("2:30 PM")
        assert time_obj == time(14, 30)
        
        time_obj = handler.parse_time_string("9:00 AM")
        assert time_obj == time(9, 0)
        
        # 24-hour format
        time_obj = handler.parse_time_string("14:30")
        assert time_obj == time(14, 30)
        
        # Hour only (assume business context)
        time_obj = handler.parse_time_string("2")
        assert time_obj == time(14, 0)  # Assume PM for business hours
        
        time_obj = handler.parse_time_string("9")
        assert time_obj == time(9, 0)  # AM for business hours
    
    @freeze_time("2025-08-29 12:00:00")  # Friday
    def test_business_day_checks(self):
        """Test business day and hours validation"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Friday (business day)
        friday = datetime(2025, 8, 29)
        assert handler.is_business_day(friday) is True
        
        # Saturday (weekend)
        saturday = datetime(2025, 8, 30)
        assert handler.is_business_day(saturday) is False
        
        # Sunday (weekend)
        sunday = datetime(2025, 8, 31)
        assert handler.is_business_day(sunday) is False
        
        # Monday (business day)
        monday = datetime(2025, 9, 1)
        assert handler.is_business_day(monday) is True
    
    def test_business_hours_checks(self):
        """Test business hours validation"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # 10 AM (business hours)
        dt = datetime(2025, 8, 29, 10, 0)
        assert handler.is_business_hours(dt) is True
        
        # 6 AM (before business hours)
        dt = datetime(2025, 8, 29, 6, 0)
        assert handler.is_business_hours(dt) is False
        
        # 8 PM (after business hours)
        dt = datetime(2025, 8, 29, 20, 0)
        assert handler.is_business_hours(dt) is False
    
    @freeze_time("2025-08-29 12:00:00")  # Friday
    def test_next_business_day(self):
        """Test getting next business day"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # From Friday, next business day should be Monday
        friday = datetime(2025, 8, 29)
        next_bday = handler.get_next_business_day(friday)
        assert next_bday.date() == datetime(2025, 9, 2).date()  # Monday
        assert next_bday.weekday() == 0
        
        # From Wednesday, next business day should be Thursday
        wednesday = datetime(2025, 8, 27)
        next_bday = handler.get_next_business_day(wednesday)
        assert next_bday.date() == datetime(2025, 8, 28).date()  # Thursday
        assert next_bday.weekday() == 3
    
    def test_datetime_formatting(self):
        """Test datetime formatting for user display"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Create a UTC datetime
        utc_dt = pytz.UTC.localize(datetime(2025, 9, 1, 10, 30, 0))
        
        # Format with date and time
        formatted = handler.format_datetime_local(utc_dt, "Asia/Kolkata")
        assert "monday, 1 september at 4:00 pm" in formatted.lower()
        
        # Format date only
        date_formatted = handler.format_date_long(utc_dt, "Asia/Kolkata")
        assert "monday, 1 september" in date_formatted.lower()
        
        # Format time only
        time_formatted = handler.format_time_local(utc_dt, "Asia/Kolkata")
        assert "4:00 pm" in time_formatted.lower()
    
    def test_datetime_range_validation(self):
        """Test datetime range validation"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        start = datetime(2025, 9, 1, 10, 0)
        
        # Valid 30-minute meeting
        end = datetime(2025, 9, 1, 10, 30)
        assert handler.validate_datetime_range(start, end) is True
        
        # Valid 1-hour meeting
        end = datetime(2025, 9, 1, 11, 0)
        assert handler.validate_datetime_range(start, end) is True
        
        # Too short (5 minutes)
        end = datetime(2025, 9, 1, 10, 5)
        assert handler.validate_datetime_range(start, end) is False
        
        # Too long (5 hours)
        end = datetime(2025, 9, 1, 15, 0)
        assert handler.validate_datetime_range(start, end) is False
        
        # End before start
        end = datetime(2025, 9, 1, 9, 30)
        assert handler.validate_datetime_range(start, end) is False
    
    def test_calendar_correctness_2025(self):
        """Test calendar correctness for 2025"""
        handler = TimezoneHandler("Asia/Kolkata")
        
        # Verify specific dates in 2025
        test_dates = [
            ("2025-09-01", 0),  # Monday
            ("2025-09-02", 1),  # Tuesday
            ("2025-09-03", 2),  # Wednesday
            ("2025-09-04", 3),  # Thursday
            ("2025-09-05", 4),  # Friday
            ("2025-09-06", 5),  # Saturday
            ("2025-09-07", 6),  # Sunday
        ]
        
        for date_str, expected_weekday in test_dates:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            assert dt.weekday() == expected_weekday, f"{date_str} should be weekday {expected_weekday}"
    
    def test_dst_edge_cases(self):
        """Test DST transition edge cases"""
        handler = TimezoneHandler("America/New_York")
        
        # Spring forward (2025-03-09 2:00 AM -> 3:00 AM)
        # This test ensures we handle DST transitions gracefully
        spring_dt = datetime(2025, 3, 9, 2, 30)
        ny_tz = pytz.timezone("America/New_York")
        
        try:
            # This might raise an exception for non-existent time
            localized = ny_tz.localize(spring_dt)
            utc_converted = handler.to_utc(localized, "America/New_York")
            assert utc_converted.tzinfo == pytz.UTC
        except pytz.NonExistentTimeError:
            # This is expected for DST transition times
            pass
        
        # Fall back (2025-11-02 2:00 AM -> 1:00 AM)
        fall_dt = datetime(2025, 11, 2, 1, 30)
        try:
            # This might be ambiguous
            localized = ny_tz.localize(fall_dt, is_dst=False)
            utc_converted = handler.to_utc(localized, "America/New_York")
            assert utc_converted.tzinfo == pytz.UTC
        except pytz.AmbiguousTimeError:
            # Handle ambiguous time by choosing standard time
            localized = ny_tz.localize(fall_dt, is_dst=False)
            utc_converted = handler.to_utc(localized, "America/New_York")
            assert utc_converted.tzinfo == pytz.UTC
