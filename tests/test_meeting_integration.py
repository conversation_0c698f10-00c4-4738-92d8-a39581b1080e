"""
Comprehensive tests for meeting booking integration
Tests meeting scheduling, booking, and timezone handling.
"""

import pytest
import os
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
import pytz

from app.services.coochie_meeting_service import CoochieMeetingService, coochie_meeting_service
from app.services.zoho_bookings_service import BookingSlot


class TestCoochieMeetingService:
    """Test the Coochie meeting integration service"""
    
    @pytest.fixture
    def meeting_service(self):
        """Create meeting service"""
        return CoochieMeetingService()
    
    @pytest.fixture
    def mock_booking_slot(self):
        """Create mock booking slot"""
        start_time = datetime.now(pytz.UTC) + timedelta(days=1)
        end_time = start_time + timedelta(minutes=30)
        
        return BookingSlot(
            staff_id="staff_001",
            staff_name="<PERSON>",
            start_time=start_time,
            end_time=end_time,
            service_id="service_001",
            service_name="Lead Meeting",
            duration_minutes=30,
            booking_url="https://example.com/book"
        )
    
    def test_service_initialization(self, meeting_service):
        """Test service initializes correctly"""
        assert meeting_service.default_timezone == "Asia/Kolkata"
        assert hasattr(meeting_service, 'zoho_service')
        assert hasattr(meeting_service, 'meeting_agent')
        assert hasattr(meeting_service, 'timezone_handler')
    
    @pytest.mark.asyncio
    async def test_generate_mock_scheduling_options(self, meeting_service):
        """Test mock scheduling options generation"""
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "false"}):
            service = CoochieMeetingService()
            
            options = await service.generate_scheduling_options("test-lead")
            
            assert options["success"] is True
            assert options["mock"] is True
            assert len(options["slots"]) == 4
            assert options["timezone"] == "Asia/Kolkata"
            assert "time1" in options
            assert "day1" in options
            assert "time2" in options
            assert "day2" in options
    
    @pytest.mark.asyncio
    async def test_generate_real_scheduling_options(self, meeting_service, mock_booking_slot):
        """Test real scheduling options generation"""
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "true"}):
            with patch.object(meeting_service, '_get_next_available_slots') as mock_slots:
                mock_slots.return_value = [mock_booking_slot]
                
                options = await meeting_service.generate_scheduling_options("test-lead")
                
                assert options["success"] is True
                assert len(options["slots"]) == 1
                assert options["slots"][0]["staff_name"] == "Andy Smith"
                assert options["slots"][0]["duration_minutes"] == 30
                mock_slots.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_fallback_options(self, meeting_service):
        """Test fallback options when no slots available"""
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "true"}):
            with patch.object(meeting_service, '_get_next_available_slots') as mock_slots:
                mock_slots.return_value = []
                
                options = await meeting_service.generate_scheduling_options("test-lead")
                
                assert options["success"] is False
                assert options["fallback"] is True
                assert options["time1"] == "2:00 PM"
                assert options["day1"] == "Tomorrow"
    
    def test_format_scheduling_options(self, meeting_service, mock_booking_slot):
        """Test formatting of scheduling options"""
        slots = [mock_booking_slot]
        
        formatted = meeting_service._format_scheduling_options(slots, "Asia/Kolkata")
        
        assert formatted["success"] is True
        assert formatted["timezone"] == "Asia/Kolkata"
        assert len(formatted["slots"]) == 1
        assert "time1" in formatted
        assert "day1" in formatted
        
        slot = formatted["slots"][0]
        assert slot["staff_name"] == "Andy Smith"
        assert slot["duration_minutes"] == 30
        assert "display_text" in slot
    
    @pytest.mark.asyncio
    async def test_book_meeting_mock(self, meeting_service):
        """Test booking meeting with mock option"""
        mock_option = {
            "option_number": 1,
            "day": "Monday",
            "time": "2:00 PM",
            "datetime_local": "2024-01-15T14:00:00+05:30",
            "staff_name": "Andy (Mock)",
            "staff_id": "mock_staff_001",
            "duration_minutes": 30,
            "mock": True
        }
        
        result = await meeting_service.book_meeting("test-lead", mock_option)
        
        assert result["success"] is True
        assert result["mock"] is True
        assert "booking_id" in result
        assert result["staff_name"] == "Andy (Mock)"
        assert result["meeting_time"] == mock_option["datetime_local"]
    
    @pytest.mark.asyncio
    async def test_book_meeting_real(self, meeting_service):
        """Test booking meeting with real option"""
        real_option = {
            "option_number": 1,
            "day": "Monday",
            "time": "2:00 PM",
            "datetime_local": "2024-01-15T14:00:00+05:30",
            "datetime_utc": "2024-01-15T08:30:00+00:00",
            "staff_name": "Andy Smith",
            "staff_id": "staff_001",
            "service_id": "service_001",
            "duration_minutes": 30,
            "mock": False
        }
        
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "true"}):
            with patch.object(meeting_service.zoho_service, 'book_appointment_for_lead') as mock_book:
                mock_book.return_value = MagicMock(
                    success=True,
                    booking_id="real_booking_123",
                    booking_url="https://example.com/booking/123",
                    meeting_link="https://meet.example.com/123"
                )
                
                result = await meeting_service.book_meeting("test-lead", real_option)
                
                assert result["success"] is True
                assert result["booking_id"] == "real_booking_123"
                assert result["booking_url"] == "https://example.com/booking/123"
                assert result["meeting_link"] == "https://meet.example.com/123"
                mock_book.assert_called_once()
    
    def test_parse_time_selection(self, meeting_service):
        """Test parsing time selection from user message"""
        options = [
            {"option_number": 1, "day": "Monday", "time": "2:00 PM"},
            {"option_number": 2, "day": "Tuesday", "time": "10:00 AM"}
        ]
        
        # Test option number selection
        result = meeting_service.parse_time_selection("I'll take option 1", options)
        assert result["option_number"] == 1
        
        # Test day name selection
        result = meeting_service.parse_time_selection("Tuesday works for me", options)
        assert result["day"] == "Tuesday"
        
        # Test time selection
        result = meeting_service.parse_time_selection("10:00 AM is good", options)
        assert result["time"] == "10:00 AM"
        
        # Test unclear selection - should default to first
        result = meeting_service.parse_time_selection("That sounds good", options)
        assert result["option_number"] == 1
    
    def test_format_scheduling_text(self, meeting_service):
        """Test formatting scheduling text for conversation"""
        options = {
            "success": True,
            "slots": [
                {"time": "2:00 PM", "day": "Monday"},
                {"time": "10:00 AM", "day": "Tuesday"}
            ]
        }
        
        text = meeting_service.format_scheduling_text(options)
        
        assert "2:00 PM" in text
        assert "Monday" in text
        assert "10:00 AM" in text
        assert "Tuesday" in text
        assert "Are you available" in text
    
    def test_format_scheduling_text_fallback(self, meeting_service):
        """Test formatting scheduling text fallback"""
        options = {"success": False, "slots": []}
        
        text = meeting_service.format_scheduling_text(options)
        
        assert "2:00 PM tomorrow" in text or "Are you available" in text


class TestMeetingWorkflowIntegration:
    """Test meeting integration with workflow agent"""
    
    @pytest.mark.asyncio
    async def test_workflow_agent_scheduling_options(self):
        """Test workflow agent generates scheduling options"""
        from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
        from app.models.lead import Lead
        
        mock_lead = MagicMock(spec=Lead)
        mock_lead.id = "test-lead"
        mock_lead.phone_number = "+61400000000"
        
        with patch('app.services.coochie_meeting_service.coochie_meeting_service') as mock_service:
            mock_service.generate_scheduling_options.return_value = {
                "success": True,
                "time1": "2:00 PM",
                "day1": "Monday",
                "time2": "10:00 AM",
                "day2": "Tuesday",
                "slots": [{"option_number": 1, "time": "2:00 PM", "day": "Monday"}]
            }
            
            agent = CoochieWorkflowAgent()
            options = await agent._generate_scheduling_options(mock_lead)
            
            assert options["time1"] == "2:00 PM"
            assert options["day1"] == "Monday"
            assert options["success"] is True
            mock_service.generate_scheduling_options.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_workflow_agent_book_meeting(self):
        """Test workflow agent books meeting"""
        from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
        from app.models.lead import Lead
        
        mock_lead = MagicMock(spec=Lead)
        mock_lead.id = "test-lead"
        mock_lead.phone_number = "+61400000000"
        
        scheduling_options = {
            "full_options": {
                "slots": [
                    {
                        "option_number": 1,
                        "time": "2:00 PM",
                        "day": "Monday",
                        "datetime_local": "2024-01-15T14:00:00+05:30",
                        "staff_name": "Andy"
                    }
                ]
            }
        }
        
        with patch('app.services.coochie_meeting_service.coochie_meeting_service') as mock_service:
            mock_service.parse_time_selection.return_value = scheduling_options["full_options"]["slots"][0]
            mock_service.book_meeting.return_value = {
                "success": True,
                "booking_id": "booking_123",
                "display_time": "2:00 PM on Monday"
            }
            
            agent = CoochieWorkflowAgent()
            result = await agent._book_meeting(mock_lead, "Monday 2:00 PM", scheduling_options)
            
            assert result["success"] is True
            assert result["booking_id"] == "booking_123"
            mock_service.parse_time_selection.assert_called_once()
            mock_service.book_meeting.assert_called_once()


class TestTimezoneHandling:
    """Test timezone handling in meeting integration"""
    
    @pytest.mark.asyncio
    async def test_timezone_conversion(self):
        """Test timezone conversion for scheduling options"""
        service = CoochieMeetingService()
        
        # Create UTC time
        utc_time = datetime(2024, 1, 15, 8, 30, 0, tzinfo=pytz.UTC)
        
        mock_slot = BookingSlot(
            staff_id="staff_001",
            staff_name="Andy",
            start_time=utc_time,
            end_time=utc_time + timedelta(minutes=30),
            service_id="service_001",
            service_name="Meeting",
            duration_minutes=30,
            booking_url=""
        )
        
        formatted = service._format_scheduling_options([mock_slot], "Asia/Kolkata")
        
        # Should convert to Asia/Kolkata time (UTC+5:30)
        slot = formatted["slots"][0]
        assert "14:00" in slot["time_24h"] or "2:00 PM" in slot["time"]  # 8:30 UTC + 5:30 = 14:00
    
    def test_default_timezone_setting(self):
        """Test default timezone setting"""
        with patch.dict(os.environ, {"DEFAULT_TIMEZONE": "America/New_York"}):
            service = CoochieMeetingService()
            assert service.default_timezone == "America/New_York"


class TestMeetingServiceConfiguration:
    """Test meeting service configuration"""
    
    def test_meeting_agent_enabled(self):
        """Test meeting agent enabled configuration"""
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "true"}):
            service = CoochieMeetingService()
            assert service.meeting_agent_enabled is True
    
    def test_meeting_agent_disabled(self):
        """Test meeting agent disabled configuration"""
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "false"}):
            service = CoochieMeetingService()
            assert service.meeting_agent_enabled is False
    
    @pytest.mark.asyncio
    async def test_service_behavior_when_disabled(self):
        """Test service behavior when meeting agent is disabled"""
        with patch.dict(os.environ, {"MEETING_AGENT_ENABLED": "false"}):
            service = CoochieMeetingService()
            
            options = await service.generate_scheduling_options("test-lead")
            
            assert options["mock"] is True
            assert options["success"] is True
            assert len(options["slots"]) > 0


if __name__ == "__main__":
    pytest.main([__file__])
