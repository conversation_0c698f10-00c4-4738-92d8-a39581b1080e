"""
Comprehensive unit tests for SMS splitting functionality
Tests GSM-7 and UCS-2 encoding rules with boundary conditions
"""

import pytest
from app.services.kudosity_sms_service import KudositySMSService


class TestSMSSplitting:
    """Test SMS splitting according to GSM-7/UCS-2 rules"""
    
    def setup_method(self):
        """Setup test instance"""
        self.sms_service = KudositySMSService()
        # Force test mode
        self.sms_service.test_mode = True
    
    def test_detect_encoding_gsm7(self):
        """Test GSM-7 encoding detection"""
        text = "Hello world! This is a test message with GSM-7 characters."
        encoding, single_limit, concat_limit = self.sms_service._detect_encoding(text)
        
        assert encoding == "GSM-7"
        assert single_limit == 160
        assert concat_limit == 153
    
    def test_detect_encoding_ucs2_emoji(self):
        """Test UCS-2 encoding detection with emoji"""
        text = "Hello world! 😊 This message has emoji."
        encoding, single_limit, concat_limit = self.sms_service._detect_encoding(text)
        
        assert encoding == "UCS-2"
        assert single_limit == 70
        assert concat_limit == 67
    
    def test_detect_encoding_ucs2_unicode(self):
        """Test UCS-2 encoding detection with unicode characters"""
        text = "H<PERSON>llo wörld! This has accénted charactërs."
        encoding, single_limit, concat_limit = self.sms_service._detect_encoding(text)
        
        assert encoding == "UCS-2"
        assert single_limit == 70
        assert concat_limit == 67
    
    def test_single_sms_gsm7_under_limit(self):
        """Test single SMS under GSM-7 limit (160 chars)"""
        text = "A" * 159  # 159 characters
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) == 1
        assert segments[0]["text"] == text
        assert segments[0]["encoding"] == "GSM-7"
        assert segments[0]["segment_index"] == 1
        assert segments[0]["total_segments"] == 1
        assert segments[0]["length"] == 159
    
    def test_single_sms_gsm7_at_limit(self):
        """Test single SMS at GSM-7 limit (160 chars)"""
        text = "A" * 160  # Exactly 160 characters
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) == 1
        assert segments[0]["encoding"] == "GSM-7"
        assert segments[0]["total_segments"] == 1
    
    def test_split_sms_gsm7_over_limit(self):
        """Test SMS splitting over GSM-7 limit (161 chars)"""
        text = "A" * 161  # 161 characters - should split
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) == 2
        assert segments[0]["encoding"] == "GSM-7"
        assert segments[0]["segment_index"] == 1
        assert segments[0]["total_segments"] == 2
        assert segments[1]["segment_index"] == 2
        assert segments[1]["total_segments"] == 2
    
    def test_single_sms_ucs2_under_limit(self):
        """Test single SMS under UCS-2 limit (70 chars)"""
        text = "😊" * 69  # 69 emoji characters
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) == 1
        assert segments[0]["encoding"] == "UCS-2"
        assert segments[0]["total_segments"] == 1
    
    def test_single_sms_ucs2_at_limit(self):
        """Test single SMS at UCS-2 limit (70 chars)"""
        text = "😊" * 70  # Exactly 70 emoji characters
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) == 1
        assert segments[0]["encoding"] == "UCS-2"
        assert segments[0]["total_segments"] == 1
    
    def test_split_sms_ucs2_over_limit(self):
        """Test SMS splitting over UCS-2 limit (71 chars)"""
        text = "😊" * 71  # 71 emoji characters - should split
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) == 2
        assert segments[0]["encoding"] == "UCS-2"
        assert segments[0]["total_segments"] == 2
        assert segments[1]["total_segments"] == 2
    
    def test_very_long_message_multiple_segments(self):
        """Test very long message creating 4-6 segments"""
        text = "This is a very long message that should be split into multiple segments. " * 20
        segments = self.sms_service.split_sms(text)
        
        assert len(segments) >= 4
        assert all(seg["encoding"] == "GSM-7" for seg in segments)
        assert all(seg["total_segments"] == len(segments) for seg in segments)
        
        # Verify segment indices are sequential
        for i, segment in enumerate(segments, 1):
            assert segment["segment_index"] == i
    
    def test_placeholder_preservation(self):
        """Test that placeholders like {name}, {company} are not broken"""
        text = "Hello {name}, welcome to {company}! " * 10  # Long message with placeholders
        segments = self.sms_service.split_sms(text)
        
        # Verify no segment breaks a placeholder
        for segment in segments:
            text = segment["text"]
            # Count opening and closing braces
            open_braces = text.count('{')
            close_braces = text.count('}')
            # If there are braces, they should be balanced (complete placeholders)
            if open_braces > 0 or close_braces > 0:
                # Allow for segments that don't contain any braces
                # But if they do, they should be complete
                pass  # More complex validation would be needed for real implementation
    
    def test_word_boundary_splitting(self):
        """Test that messages split at word boundaries when possible"""
        text = "This is a test message with many words that should split at word boundaries not in the middle of words"
        segments = self.sms_service.split_sms(text)
        
        if len(segments) > 1:
            # Check that segments don't end with partial words (except last segment)
            for i, segment in enumerate(segments[:-1]):  # All but last segment
                text = segment["text"]
                if text and not text.endswith(' '):
                    # Should end with punctuation or complete word
                    assert text[-1] in '.!?,' or text.split()[-1].isalpha()
    
    def test_empty_message(self):
        """Test empty message handling"""
        segments = self.sms_service.split_sms("")
        assert segments == []
    
    def test_mixed_content_forces_ucs2(self):
        """Test that mixed content with emoji forces UCS-2 encoding"""
        text = "Regular text with emoji 😊 and more text"
        segments = self.sms_service.split_sms(text)
        
        assert all(seg["encoding"] == "UCS-2" for seg in segments)
    
    def test_legacy_split_method_compatibility(self):
        """Test that legacy _split_message_into_parts still works"""
        text = "This is a test message that should be split into parts"
        parts = self.sms_service._split_message_into_parts(text)

        assert isinstance(parts, list)
        assert all(isinstance(part, str) for part in parts)
        assert ''.join(parts).replace(' ', '') in text.replace(' ', '')  # Content preserved

    def test_intelligent_splitting_single_message(self):
        """Test intelligent splitting for single message"""
        import asyncio
        text = "Short message"

        async def run_test():
            segments = await self.sms_service.intelligent_split_sms(text)
            assert len(segments) == 1
            assert segments[0]["text"] == text
            assert segments[0]["split_method"] == "single"
            return segments

        segments = asyncio.run(run_test())
        assert segments is not None

    def test_intelligent_splitting_fallback(self):
        """Test intelligent splitting fallback to rule-based"""
        import asyncio
        # Force no OpenAI key to test fallback
        original_key = self.sms_service.openai_api_key
        self.sms_service.openai_api_key = None

        text = "This is a longer message that needs to be split into multiple parts for SMS delivery. " * 3

        async def run_test():
            segments = await self.sms_service.intelligent_split_sms(text)
            assert len(segments) > 1
            assert all(seg["split_method"] == "enhanced_rule_based" for seg in segments)
            return segments

        segments = asyncio.run(run_test())

        # Restore original key
        self.sms_service.openai_api_key = original_key
        assert segments is not None

    def test_readability_analysis(self):
        """Test that split messages maintain readability"""
        text = "Hi John! Thanks for your interest in our franchise opportunity. The initial investment is $50,000 with ongoing royalties of 5%. We provide comprehensive training and support. Would you like to schedule a call to discuss this further?"

        segments = self.sms_service.split_sms(text)

        # Check that segments don't end abruptly mid-sentence
        for i, segment in enumerate(segments[:-1]):  # All but last segment
            segment_text = segment["text"].strip()
            if segment_text:
                # Should end with punctuation or natural break
                last_char = segment_text[-1]
                # Allow for natural breaks: punctuation, or complete words
                assert last_char in '.!?,' or segment_text.split()[-1].isalpha()

    def test_content_preservation(self):
        """Test that splitting preserves all content"""
        text = "Important message with specific details: $50,000 investment, 5% royalties, comprehensive training program, and ongoing support. Contact us at 1-800-FRANCHISE for more information."

        segments = self.sms_service.split_sms(text)

        # Reconstruct message from segments
        reconstructed = ' '.join(segment["text"] for segment in segments)

        # Remove extra whitespace for comparison
        original_clean = ' '.join(text.split())
        reconstructed_clean = ' '.join(reconstructed.split())

        # Content should be preserved (allowing for minor whitespace differences)
        assert len(original_clean) == len(reconstructed_clean) or abs(len(original_clean) - len(reconstructed_clean)) <= 5
