"""
Regression Tests — Non-Meeting Flows
Ensures existing non-meeting functionality remains intact
"""

import pytest
from unittest.mock import patch, AsyncMock
import json

from app.models.lead import Lead
from app.services.lead_service import LeadService
from app.rag.search import SearchService
from tests.helpers.factories import LeadFactory


class TestLeadIngestionBaseline:
    """Test that lead ingestion baseline functionality is unchanged"""
    
    @pytest.mark.asyncio
    async def test_lead_creation_baseline(self, db_session):
        """Test that lead creation works as before"""
        
        lead_data = {
            "first_name": "<PERSON>",
            "last_name": "<PERSON>", 
            "email": "<EMAIL>",
            "phone_number": "+61400000000",
            "source": "website_form",
            "funds_to_invest": 120000
        }
        
        lead_service = LeadService(db_session)
        
        # Create lead
        lead = await lead_service.create_lead(**lead_data)
        
        # Verify baseline properties
        assert lead.id is not None
        assert lead.first_name == "<PERSON>"
        assert lead.last_name == "<PERSON>"
        assert lead.email == "<EMAIL>"
        assert lead.phone_number == "+61400000000"
        assert lead.source == "website_form"
        assert lead.funds_to_invest == 120000
        
        # Verify audit fields
        assert lead.is_active is True
        assert lead.is_deleted is False
        assert lead.created_at is not None
        assert lead.updated_at is not None
        assert lead.deleted_at is None
        
        # Verify lead can be retrieved
        retrieved_lead = await lead_service.get_lead_by_id(lead.id)
        assert retrieved_lead is not None
        assert retrieved_lead.id == lead.id
    
    @pytest.mark.asyncio
    async def test_lead_update_baseline(self, db_session):
        """Test that lead updates work as before"""
        
        factory = LeadFactory(db_session)
        lead = await factory.create_lead(
            first_name="Jane",
            last_name="Doe",
            email="<EMAIL>"
        )
        
        lead_service = LeadService(db_session)
        
        # Update lead
        updated_lead = await lead_service.update_lead(
            lead.id,
            first_name="Janet",
            phone_number="+61400111111"
        )
        
        # Verify updates
        assert updated_lead.first_name == "Janet"
        assert updated_lead.last_name == "Doe"  # Unchanged
        assert updated_lead.phone_number == "+61400111111"
        assert updated_lead.email == "<EMAIL>"  # Unchanged
        
        # Verify updated_at changed
        assert updated_lead.updated_at > lead.created_at
    
    @pytest.mark.asyncio
    async def test_lead_soft_delete_baseline(self, db_session):
        """Test that lead soft delete works as before"""
        
        factory = LeadFactory(db_session)
        lead = await factory.create_lead(first_name="Delete", last_name="Me")
        
        lead_service = LeadService(db_session)
        
        # Soft delete
        deleted_lead = await lead_service.soft_delete_lead(lead.id)
        
        # Verify soft delete
        assert deleted_lead.is_deleted is True
        assert deleted_lead.deleted_at is not None
        assert deleted_lead.is_active is False
        
        # Verify lead is not returned in normal queries
        active_leads = await lead_service.get_active_leads()
        lead_ids = [l.id for l in active_leads]
        assert lead.id not in lead_ids
    
    @pytest.mark.asyncio
    async def test_lead_search_baseline(self, db_session):
        """Test that lead search functionality works as before"""
        
        factory = LeadFactory(db_session)
        
        # Create test leads
        lead1 = await factory.create_lead(
            first_name="Search",
            last_name="Test1",
            email="<EMAIL>"
        )
        lead2 = await factory.create_lead(
            first_name="Search",
            last_name="Test2", 
            email="<EMAIL>"
        )
        lead3 = await factory.create_lead(
            first_name="Other",
            last_name="Person",
            email="<EMAIL>"
        )
        
        lead_service = LeadService(db_session)
        
        # Search by first name
        search_results = await lead_service.search_leads(query="Search")
        result_ids = [l.id for l in search_results]
        
        assert lead1.id in result_ids
        assert lead2.id in result_ids
        assert lead3.id not in result_ids
        
        # Search by email domain
        email_results = await lead_service.search_leads(query="<EMAIL>")
        assert len(email_results) >= 1
        assert lead1.id in [l.id for l in email_results]


class TestFollowUpEngineSuppressionSemantics:
    """Test that follow-up engine is suppressed but semantics unchanged elsewhere"""
    
    @pytest.mark.asyncio
    async def test_followup_scheduler_exists_but_suppressed(self):
        """Test that follow-up scheduler exists but is suppressed by flag"""
        
        from app.schedulers.followups import FollowUpScheduler
        from app.core.followup_suppression import followup_suppressor
        
        # Scheduler should exist
        scheduler = FollowUpScheduler()
        assert scheduler is not None
        
        # But suppression should be active
        assert followup_suppressor.is_suppression_enabled() is True
        
        # Scheduling should be suppressed
        result = followup_suppressor.suppress_followup(
            "test_followup",
            lead_id="test-lead"
        )
        
        assert result["suppressed"] is True
        assert "WORKFLOW_ANDY_NO_FOLLOWUPS" in result["reason"]
    
    @pytest.mark.asyncio
    async def test_followup_models_unchanged(self, db_session):
        """Test that follow-up related models are unchanged"""
        
        # Follow-up models should still exist and work
        from app.models.followup_schedule import FollowUpSchedule
        from app.models.followup_message import FollowUpMessage
        
        # Should be able to create instances (even if not used)
        followup_schedule = FollowUpSchedule(
            lead_id="test-lead",
            message_template="Test message",
            scheduled_at=datetime.now(),
            status="scheduled"
        )
        
        assert followup_schedule.lead_id == "test-lead"
        assert followup_schedule.status == "scheduled"
        
        # Models should have proper structure
        assert hasattr(followup_schedule, 'is_active')
        assert hasattr(followup_schedule, 'created_at')
        assert hasattr(followup_schedule, 'updated_at')
    
    def test_followup_configuration_semantics(self):
        """Test that follow-up configuration semantics are preserved"""
        
        from app.core.config.settings import settings
        
        # Configuration should exist
        assert hasattr(settings, 'FOLLOWUP_ENABLED')
        assert hasattr(settings, 'FOLLOWUP_DEFAULT_DELAY_HOURS')
        assert hasattr(settings, 'FOLLOWUP_MAX_ATTEMPTS')
        
        # But workflow flag should override
        import os
        workflow_flag = os.getenv("WORKFLOW_ANDY_NO_FOLLOWUPS", "false").lower()
        assert workflow_flag == "true", "Workflow flag should be enabled for tests"


class TestRAGEscalationUnchanged:
    """Test that RAG and escalation functionality is unchanged"""
    
    @pytest.fixture
    def mock_search_service(self):
        """Mock search service with deterministic results"""
        with patch('app.rag.search.SearchService') as mock_service:
            mock_instance = mock_service.return_value
            
            # High confidence response
            mock_instance.search.return_value = {
                "confidence": 0.85,
                "answer": "Coochie Hydrogreen is Australia's largest lawn care franchise with over 30 years of experience.",
                "sources": ["franchise_info.pdf"],
                "query_embedding": [0.1] * 1536
            }
            
            yield mock_instance
    
    @pytest.mark.asyncio
    async def test_rag_search_baseline(self, mock_search_service):
        """Test that RAG search functionality works as before"""
        
        search_service = SearchService()
        
        query = "Tell me about Coochie Hydrogreen"
        result = await search_service.search(query)
        
        # Verify baseline functionality
        assert result is not None
        assert "confidence" in result
        assert "answer" in result
        assert "sources" in result
        
        # High confidence should return answer
        assert result["confidence"] >= 0.8
        assert "Coochie Hydrogreen" in result["answer"]
        assert len(result["sources"]) > 0
    
    @pytest.mark.asyncio
    async def test_low_confidence_escalation_creation(self, db_session, mock_search_service):
        """Test that low confidence queries create escalation questions"""
        
        # Mock low confidence response
        mock_search_service.search.return_value = {
            "confidence": 0.3,
            "answer": "I'm not sure about that specific question.",
            "sources": [],
            "query_embedding": [0.1] * 1536
        }
        
        from app.services.escalation_service import EscalationService
        
        escalation_service = EscalationService(db_session)
        
        query = "What is the specific ROI for franchise partners in Queensland?"
        lead_id = "test-lead-123"
        
        # Process low confidence query
        result = await escalation_service.handle_low_confidence_query(
            query=query,
            lead_id=lead_id,
            confidence=0.3
        )
        
        # Should create escalation question
        assert result["escalated"] is True
        assert result["escalation_id"] is not None
        
        # Verify escalation question was created
        escalation = await escalation_service.get_escalation_by_id(result["escalation_id"])
        assert escalation is not None
        assert escalation.question == query
        assert escalation.lead_id == lead_id
        assert escalation.confidence_score == 0.3
        assert escalation.status == "pending"
    
    @pytest.mark.asyncio
    async def test_escalation_question_embedding_queued(self, db_session, task_queue_monitor):
        """Test that escalation questions queue embedding generation"""
        
        from app.services.escalation_service import EscalationService
        
        escalation_service = EscalationService(db_session)
        
        # Clear task queue
        task_queue_monitor.clear()
        
        # Create escalation question
        result = await escalation_service.handle_low_confidence_query(
            query="Test escalation question",
            lead_id="test-lead",
            confidence=0.2
        )
        
        # Should queue embedding generation task
        embedding_tasks = [
            task for task in task_queue_monitor
            if "embedding" in task["task_name"].lower() or "generate_embedding" in task["task_name"].lower()
        ]
        
        assert len(embedding_tasks) > 0, "Should queue embedding generation task"
    
    @pytest.mark.asyncio
    async def test_known_answer_retrieval(self, db_session, mock_search_service):
        """Test that known answers are retrieved when confidence >= threshold"""
        
        # Mock high confidence response
        mock_search_service.search.return_value = {
            "confidence": 0.9,
            "answer": "The franchise fee is $120K excluding GST.",
            "sources": ["pricing.pdf"],
            "query_embedding": [0.1] * 1536
        }
        
        from app.services.escalation_service import EscalationService
        
        escalation_service = EscalationService(db_session)
        
        query = "What is the franchise fee?"
        
        # Process high confidence query
        result = await escalation_service.handle_query(query, "test-lead")
        
        # Should return answer directly
        assert result["escalated"] is False
        assert result["answer"] is not None
        assert "franchise fee" in result["answer"].lower()
        assert result["confidence"] >= 0.8
    
    @pytest.mark.asyncio
    async def test_clarification_loop_prevention(self, db_session):
        """Test that clarifications are capped at 2 to prevent loops"""
        
        from app.services.escalation_service import EscalationService
        from app.models.escalation_question import EscalationQuestion
        
        escalation_service = EscalationService(db_session)
        
        # Create escalation with multiple clarification attempts
        escalation = EscalationQuestion(
            question="Unclear question",
            lead_id="test-lead",
            confidence_score=0.1,
            clarification_count=2,  # Already at limit
            status="pending"
        )
        
        db_session.add(escalation)
        await db_session.commit()
        
        # Attempt another clarification
        result = await escalation_service.request_clarification(
            escalation.id,
            "Still unclear"
        )
        
        # Should be rejected due to limit
        assert result["success"] is False
        assert "maximum clarifications" in result["message"].lower()


class TestLeadStatusUpdatesUnchanged:
    """Test that lead status update functionality is unchanged"""
    
    @pytest.mark.asyncio
    async def test_lead_status_transitions(self, db_session):
        """Test that lead status transitions work as before"""
        
        factory = LeadFactory(db_session)
        lead = await factory.create_lead(
            first_name="Status",
            last_name="Test",
            status="new"
        )
        
        from app.services.lead_service import LeadService
        
        lead_service = LeadService(db_session)
        
        # Test status transitions
        status_transitions = [
            ("new", "contacted"),
            ("contacted", "qualified"),
            ("qualified", "meeting_scheduled"),
            ("meeting_scheduled", "meeting_completed"),
            ("meeting_completed", "proposal_sent")
        ]
        
        current_lead = lead
        for from_status, to_status in status_transitions:
            assert current_lead.status == from_status, f"Expected {from_status}, got {current_lead.status}"
            
            # Update status
            current_lead = await lead_service.update_lead_status(
                current_lead.id,
                to_status
            )
            
            assert current_lead.status == to_status
            assert current_lead.updated_at > current_lead.created_at
    
    @pytest.mark.asyncio
    async def test_lead_status_history_tracking(self, db_session):
        """Test that lead status history is tracked"""
        
        factory = LeadFactory(db_session)
        lead = await factory.create_lead(status="new")
        
        from app.services.lead_service import LeadService
        
        lead_service = LeadService(db_session)
        
        # Update status multiple times
        await lead_service.update_lead_status(lead.id, "contacted")
        await lead_service.update_lead_status(lead.id, "qualified")
        
        # Get status history
        history = await lead_service.get_lead_status_history(lead.id)
        
        # Should have history entries
        assert len(history) >= 2
        
        # Should be in chronological order
        statuses = [h.status for h in history]
        assert "new" in statuses
        assert "contacted" in statuses
        assert "qualified" in statuses


class TestBaselineSnapshotMatching:
    """Test that baseline functionality matches expected snapshots"""
    
    @pytest.mark.asyncio
    async def test_lead_creation_snapshot(self, db_session):
        """Test that lead creation produces expected data structure"""
        
        factory = LeadFactory(db_session)
        lead = await factory.create_lead(
            first_name="Snapshot",
            last_name="Test",
            email="<EMAIL>",
            phone_number="+61400000000",
            source="test",
            funds_to_invest=100000
        )
        
        # Convert to dict for snapshot comparison
        lead_dict = {
            "first_name": lead.first_name,
            "last_name": lead.last_name,
            "email": lead.email,
            "phone_number": lead.phone_number,
            "source": lead.source,
            "funds_to_invest": lead.funds_to_invest,
            "status": lead.status,
            "is_active": lead.is_active,
            "is_deleted": lead.is_deleted,
            "has_created_at": lead.created_at is not None,
            "has_updated_at": lead.updated_at is not None,
            "deleted_at_is_none": lead.deleted_at is None
        }
        
        # Expected baseline structure
        expected_snapshot = {
            "first_name": "Snapshot",
            "last_name": "Test",
            "email": "<EMAIL>",
            "phone_number": "+61400000000",
            "source": "test",
            "funds_to_invest": 100000,
            "status": "new",  # Default status
            "is_active": True,
            "is_deleted": False,
            "has_created_at": True,
            "has_updated_at": True,
            "deleted_at_is_none": True
        }
        
        assert lead_dict == expected_snapshot, "Lead creation snapshot mismatch"
    
    def test_model_field_baseline(self):
        """Test that model fields match baseline expectations"""
        
        from app.models.lead import Lead
        
        # Check that required fields exist
        required_fields = [
            'id', 'first_name', 'last_name', 'email', 'phone_number',
            'source', 'status', 'funds_to_invest', 'is_active', 'is_deleted',
            'created_at', 'updated_at', 'deleted_at'
        ]
        
        lead_fields = [field.name for field in Lead.__table__.columns]
        
        for field in required_fields:
            assert field in lead_fields, f"Required field '{field}' missing from Lead model"
