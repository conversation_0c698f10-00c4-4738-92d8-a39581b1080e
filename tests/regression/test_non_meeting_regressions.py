"""
Regression Tests - Non-Meeting Functionality
Tests to ensure existing functionality remains unchanged after meeting agent implementation
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock
import json

from app.api.v1.endpoints.webhooks import process_kudosity_webhook
from app.services.followup_service import FollowupService
from app.services.lead_status_service import LeadStatusService
from tests.helpers.factories import LeadFactory, EscalationQuestionFactory
from tests.mocks.kudosity_stub import KudosityStub
from tests.mocks.search_stub import SearchStub


@pytest.mark.regression
class TestLeadIngestionRegression:
    """Test that lead ingestion remains unchanged."""
    
    @pytest.fixture
    def kudosity_stub(self):
        return KudosityStub()
    
    @pytest.fixture
    async def baseline_lead(self, db_session):
        """Create baseline lead for comparison."""
        factory = LeadFactory(db_session)
        return await factory.create_lead(
            first_name="Baseline",
            last_name="User",
            phone="+61400000001",
            email="<EMAIL>",
            status="New"
        )
    
    @pytest.mark.asyncio
    async def test_lead_ingestion_identical_outputs(self, baseline_lead, kudosity_stub, frozen_time):
        """Test that lead ingestion produces identical outputs as baseline snapshots."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # Test standard lead ingestion webhook
            webhook_payload = {
                "phone": baseline_lead.phone,
                "message": "Hi, I'm interested in your franchise opportunity",
                "timestamp": "2025-08-29T12:00:00+05:30",
                "message_id": "regression_test_001"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            # Verify standard response structure
            assert response["success"] is True
            assert "data" in response
            assert "ai_response" in response["data"]
            
            # Verify response content patterns (baseline expectations)
            ai_response = response["data"]["ai_response"]
            assert len(ai_response) > 0
            assert isinstance(ai_response, str)
            
            # Should contain franchise-related content
            assert any(word in ai_response.lower() 
                      for word in ["franchise", "opportunity", "business", "investment"])
            
            # Should not contain emojis (regression check)
            import re
            emoji_pattern = re.compile(
                "["
                "\U0001F600-\U0001F64F"  # emoticons
                "\U0001F300-\U0001F5FF"  # symbols & pictographs
                "\U0001F680-\U0001F6FF"  # transport & map symbols
                "\U0001F1E0-\U0001F1FF"  # flags (iOS)
                "\U00002702-\U000027B0"
                "\U000024C2-\U0001F251"
                "]+", flags=re.UNICODE
            )
            assert not emoji_pattern.search(ai_response)
    
    @pytest.mark.asyncio
    async def test_lead_status_updates_unchanged(self, baseline_lead, db_session):
        """Test that lead status update logic remains unchanged."""
        status_service = LeadStatusService()
        
        # Test standard status transitions
        original_status = baseline_lead.status
        original_updated_at = baseline_lead.updated_at
        
        # Update to Contacted
        updated_lead = status_service.update_lead_status(baseline_lead, "Contacted")
        
        assert updated_lead.status == "Contacted"
        assert updated_lead.updated_at > original_updated_at
        assert updated_lead.updated_at.tzinfo is not None  # Should have timezone info
        
        # Update to Qualified
        qualified_lead = status_service.update_lead_status(updated_lead, "Qualified")
        
        assert qualified_lead.status == "Qualified"
        assert qualified_lead.updated_at > updated_lead.updated_at
    
    @pytest.mark.asyncio
    async def test_lead_data_integrity(self, baseline_lead, db_session):
        """Test that lead data integrity is maintained."""
        # Verify all expected fields are present
        required_fields = [
            'id', 'first_name', 'last_name', 'email', 'phone', 
            'status', 'created_at', 'updated_at', 'is_active', 'is_deleted'
        ]
        
        for field in required_fields:
            assert hasattr(baseline_lead, field), f"Lead missing required field: {field}"
            assert getattr(baseline_lead, field) is not None or field in ['deleted_at'], \
                f"Lead field {field} should not be None"
        
        # Verify data types
        assert isinstance(baseline_lead.is_active, bool)
        assert isinstance(baseline_lead.is_deleted, bool)
        assert isinstance(baseline_lead.created_at, datetime)
        assert isinstance(baseline_lead.updated_at, datetime)


@pytest.mark.regression
class TestFollowupTimerRegression:
    """Test that follow-up timer semantics remain unchanged."""
    
    @pytest.fixture
    def followup_service(self):
        return FollowupService()
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(
            phone="+61400000002",
            status="New"
        )
    
    @pytest.mark.asyncio
    async def test_followup_timer_semantics_unchanged(self, followup_service, test_lead, frozen_time):
        """Test that follow-up timer semantics are unchanged."""
        from app.models.messaging_rule import MessagingRule
        
        # Create standard messaging rule
        rule = MessagingRule(
            lead_init_delay_h=2.0,      # 2 hours initial delay
            no_response_delay_h=24.0,   # 24 hours no-response delay
            max_followups=3,            # Maximum 3 follow-ups
            is_active=True
        )
        
        # Test delay calculations (should be unchanged)
        now = datetime.utcnow()
        
        # Initial delay calculation
        initial_time = followup_service.calculate_initial_delay(now, rule)
        expected_initial = now + timedelta(hours=2)
        time_diff = abs((initial_time - expected_initial).total_seconds())
        assert time_diff < 60  # Within 1 minute tolerance
        
        # No-response delay calculation
        last_message_time = now - timedelta(hours=1)
        followup_time = followup_service.calculate_followup_delay(last_message_time, rule)
        expected_followup = last_message_time + timedelta(hours=24)
        time_diff = abs((followup_time - expected_followup).total_seconds())
        assert time_diff < 60
        
        # Max followups enforcement
        assert followup_service.can_send_followup(test_lead.id, rule, 0) is True   # First
        assert followup_service.can_send_followup(test_lead.id, rule, 1) is True   # Second
        assert followup_service.can_send_followup(test_lead.id, rule, 2) is True   # Third
        assert followup_service.can_send_followup(test_lead.id, rule, 3) is False  # Fourth (blocked)
    
    @pytest.mark.asyncio
    async def test_followup_cancellation_unchanged(self, followup_service, test_lead):
        """Test that follow-up cancellation behavior is unchanged."""
        with patch.object(followup_service, 'cancel_pending_followups') as mock_cancel:
            # Lead response should cancel followups
            await followup_service.handle_lead_response(test_lead.id)
            
            mock_cancel.assert_called_once_with(test_lead.id)
    
    @pytest.mark.asyncio
    async def test_followup_rule_hierarchy_unchanged(self, followup_service):
        """Test that rule hierarchy resolution is unchanged."""
        from app.models.messaging_rule import MessagingRule
        
        # Create rule hierarchy
        global_rule = MessagingRule(
            lead_init_delay_h=1.0,
            no_response_delay_h=12.0,
            max_followups=5,
            level="global"
        )
        
        brand_rule = MessagingRule(
            lead_init_delay_h=2.0,
            no_response_delay_h=18.0,
            max_followups=4,
            level="brand"
        )
        
        lead_rule = MessagingRule(
            lead_init_delay_h=3.0,
            no_response_delay_h=24.0,
            max_followups=3,
            level="lead"
        )
        
        rules = [global_rule, brand_rule, lead_rule]
        
        # Lead rule should take precedence (unchanged behavior)
        resolved = followup_service.resolve_rule_hierarchy(rules)
        
        assert resolved.lead_init_delay_h == 3.0    # From lead rule
        assert resolved.no_response_delay_h == 24.0  # From lead rule
        assert resolved.max_followups == 3          # From lead rule


@pytest.mark.regression
class TestRAGEscalationRegression:
    """Test that RAG/escalation behavior remains intact."""
    
    @pytest.fixture
    def search_stub(self):
        stub = SearchStub()
        # Seed with baseline knowledge
        stub.seed_knowledge_item(
            "Our franchise fees start at $50,000 plus GST.",
            "fees",
            ["franchise", "fees", "cost", "price"]
        )
        return stub
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(phone="+61400000003")
    
    @pytest.mark.asyncio
    async def test_rag_answers_unchanged(self, search_stub, test_lead):
        """Test that RAG answers are unchanged for fixture questions."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            # Test high-confidence query
            query = "What are your franchise fees?"
            results = await search_stub.search(query, top_k=5)
            
            # Should return expected result
            assert len(results) > 0
            assert results[0].score >= 0.7  # High confidence
            assert "$50,000" in results[0].content
            assert "GST" in results[0].content
    
    @pytest.mark.asyncio
    async def test_escalation_insertion_unchanged(self, search_stub, test_lead):
        """Test that escalation insertion schema & content are unchanged."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            # Create escalation for unknown question
            question = "What are your refund policies?"
            context = {
                "lead_id": str(test_lead.id),
                "conversation_id": "test_conv_123"
            }
            
            escalation_id = await search_stub.create_escalation_question(question, context)
            
            # Verify escalation structure
            escalation = await search_stub.get_escalation_question(escalation_id)
            
            assert escalation is not None
            assert escalation.question == question
            assert escalation.answer is None  # Initially unanswered
            assert escalation.confidence == 0.0  # Low confidence
            assert escalation.category in ["general", "fees", "training", "territories", "roi", "financing"]
    
    @pytest.mark.asyncio
    async def test_escalation_learning_cycle_unchanged(self, search_stub):
        """Test that escalation learning cycle behavior is unchanged."""
        # Create escalation
        question = "Do you offer financing options?"
        escalation_id = await search_stub.create_escalation_question(question, {})
        
        # Admin provides answer
        answer = "Yes, we offer financing through our preferred lending partners."
        success = await search_stub.answer_escalation_question(escalation_id, answer)
        
        assert success is True
        
        # Verify answer is now available for future queries
        results = await search_stub.search("financing options")
        assert len(results) > 0
        
        # Should find the answered escalation
        found_answer = False
        for result in results:
            if "financing" in result.content.lower() and "lending partners" in result.content.lower():
                found_answer = True
                break
        
        assert found_answer is True


@pytest.mark.regression
class TestKudositySMSRegression:
    """Test that Kudosity SMS behavior is unchanged when disabled."""
    
    @pytest.fixture
    def kudosity_stub(self):
        stub = KudosityStub()
        # Ensure SMS is disabled (test environment default)
        stub.enabled = False
        stub.test_mode = True
        return stub
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(phone="+61400000004")
    
    @pytest.mark.asyncio
    async def test_kudosity_sms_disabled_prints_to_terminal(self, kudosity_stub, test_lead, caplog, frozen_time):
        """Test that KUDOSITY_SMS_ENABLED=false prints to terminal/logs only."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # Process webhook
            webhook_payload = {
                "phone": test_lead.phone,
                "message": "Test message for regression",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            # Should process successfully
            assert response["success"] is True
            
            # Should print to logs instead of sending SMS
            assert "[TEST SMS]" in caplog.text
            assert test_lead.phone in caplog.text
            
            # Should not actually send SMS
            sent_messages = kudosity_stub.get_sent_messages(test_lead.phone)
            # Messages are recorded in test mode but marked as test
            if sent_messages:
                assert sent_messages[0].success is True  # Processing succeeded
    
    @pytest.mark.asyncio
    async def test_sms_segmentation_unchanged(self, kudosity_stub):
        """Test that SMS segmentation behavior is unchanged."""
        # Test standard message (single segment)
        short_message = "Hello, this is a test message."
        segments = kudosity_stub._split_message(short_message)
        
        assert len(segments) == 1
        assert segments[0] == short_message
        
        # Test long message (multiple segments)
        long_message = "A" * 400  # Will require multiple segments
        segments = kudosity_stub._split_message(long_message)
        
        assert len(segments) >= 2
        
        # Each segment should be within limits
        for segment in segments:
            assert len(segment) <= 160
        
        # Reassembly should work
        reassembled = "".join(segments)
        assert reassembled == long_message
    
    def test_sms_url_preservation_unchanged(self, kudosity_stub):
        """Test that URL preservation in SMS is unchanged."""
        url = "https://example.com/very/long/path/to/resource"
        message = f"Please visit our website at {url} for more information."
        
        segments = kudosity_stub._split_message(message)
        
        # URL should appear complete in one segment
        url_found = False
        for segment in segments:
            if url in segment:
                url_found = True
                break
        
        assert url_found, f"URL was broken across segments: {segments}"


@pytest.mark.regression
class TestSystemIntegrationRegression:
    """Test that overall system integration remains stable."""
    
    @pytest.fixture
    async def integration_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(
            first_name="Integration",
            last_name="Test",
            phone="+61400000005",
            status="New"
        )
    
    @pytest.mark.asyncio
    async def test_webhook_processing_unchanged(self, integration_lead, frozen_time):
        """Test that webhook processing pipeline is unchanged."""
        with patch('app.integrations.kudosity.client.KudosityClient') as mock_kudosity:
            mock_kudosity.return_value.send_sms.return_value = {
                "success": True,
                "message_id": "test_123"
            }
            
            # Standard webhook payload
            webhook_payload = {
                "phone": integration_lead.phone,
                "message": "Standard test message",
                "timestamp": "2025-08-29T12:00:00+05:30",
                "message_id": "webhook_regression_001"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            # Verify standard response structure
            assert isinstance(response, dict)
            assert "success" in response
            assert "data" in response
            assert response["success"] is True
            
            # Verify data structure
            data = response["data"]
            assert isinstance(data, dict)
            assert "ai_response" in data
            assert isinstance(data["ai_response"], str)
            assert len(data["ai_response"]) > 0
    
    @pytest.mark.asyncio
    async def test_database_operations_unchanged(self, integration_lead, db_session):
        """Test that database operations remain unchanged."""
        # Test lead retrieval
        from app.models.lead import Lead
        from sqlalchemy import select
        
        result = await db_session.execute(
            select(Lead).where(Lead.id == integration_lead.id)
        )
        retrieved_lead = result.scalar_one()
        
        # Verify lead data integrity
        assert retrieved_lead.id == integration_lead.id
        assert retrieved_lead.first_name == integration_lead.first_name
        assert retrieved_lead.phone == integration_lead.phone
        assert retrieved_lead.is_active is True
        assert retrieved_lead.is_deleted is False
        
        # Test lead update
        original_updated_at = retrieved_lead.updated_at
        retrieved_lead.status = "Contacted"
        
        await db_session.commit()
        await db_session.refresh(retrieved_lead)
        
        assert retrieved_lead.status == "Contacted"
        assert retrieved_lead.updated_at > original_updated_at
    
    @pytest.mark.asyncio
    async def test_error_handling_unchanged(self, integration_lead):
        """Test that error handling patterns are unchanged."""
        # Test invalid phone number handling
        invalid_payload = {
            "phone": "invalid-phone-number",
            "message": "Test message",
            "timestamp": "2025-08-29T12:00:00+05:30"
        }
        
        response = await process_kudosity_webhook(invalid_payload)
        
        # Should handle error gracefully
        assert response["success"] is False
        assert "error" in response
        assert isinstance(response["error"], str)
        assert len(response["error"]) > 0
    
    def test_configuration_unchanged(self):
        """Test that system configuration patterns are unchanged."""
        from app.core.config.settings import settings
        
        # Verify critical settings exist
        critical_settings = [
            'DATABASE_URL',
            'REDIS_URL', 
            'OPENAI_API_KEY',
            'KUDOSITY_SMS_ENABLED',
            'LOG_LEVEL'
        ]
        
        for setting in critical_settings:
            assert hasattr(settings, setting), f"Missing critical setting: {setting}"
        
        # Verify test environment settings
        assert settings.KUDOSITY_SMS_ENABLED is False  # Should be disabled in tests
        assert settings.LOG_LEVEL == "DEBUG"  # Should be debug in tests
