"""
Integration Tests — Meeting Changes
Tests meeting reschedule and cancel operations
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch
import pytz

from app.services.coochie_meeting_service import coochie_meeting_service


class TestMeetingReschedule:
    """Test meeting reschedule operations"""
    
    @pytest.mark.asyncio
    async def test_reschedule_updates_provider_and_db(self):
        """Test that reschedule updates provider + DB; old closed, new confirmed"""
        
        # Mock original booking
        original_booking = {
            "booking_id": "booking_123",
            "meeting_time": "2025-08-30T14:00:00+05:30",
            "staff_name": "<PERSON>",
            "status": "confirmed"
        }
        
        # Mock new time option
        new_option = {
            "option_number": 1,
            "day": "Tuesday",
            "time": "10:00 AM",
            "datetime_local": "2025-09-02T10:00:00+05:30",
            "datetime_utc": "2025-09-02T04:30:00+00:00",
            "staff_name": "<PERSON>",
            "staff_id": "staff_001",
            "service_id": "service_001",
            "duration_minutes": 30
        }
        
        with patch.object(coochie_meeting_service.zoho_service, 'reschedule_appointment') as mock_reschedule:
            mock_reschedule.return_value = AsyncMock(
                success=True,
                booking_id="booking_456",
                old_booking_cancelled=True,
                new_booking_confirmed=True
            )
            
            result = await coochie_meeting_service.reschedule_meeting(
                original_booking_id="booking_123",
                new_option=new_option,
                lead_id="test-lead"
            )
            
            # Should successfully reschedule
            assert result["success"] is True
            assert result["new_booking_id"] == "booking_456"
            assert result["old_booking_cancelled"] is True
            assert result["new_booking_confirmed"] is True
            
            # Should call Zoho service
            mock_reschedule.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_reschedule_failure_handling(self):
        """Test handling of reschedule failures"""
        
        new_option = {
            "datetime_local": "2025-09-02T10:00:00+05:30",
            "staff_name": "Andy"
        }
        
        with patch.object(coochie_meeting_service.zoho_service, 'reschedule_appointment') as mock_reschedule:
            mock_reschedule.return_value = AsyncMock(
                success=False,
                error_message="Time slot no longer available"
            )
            
            result = await coochie_meeting_service.reschedule_meeting(
                original_booking_id="booking_123",
                new_option=new_option,
                lead_id="test-lead"
            )
            
            # Should handle failure gracefully
            assert result["success"] is False
            assert "error" in result
            assert result.get("retry_available") is True


class TestMeetingCancel:
    """Test meeting cancel operations"""
    
    @pytest.mark.asyncio
    async def test_cancel_updates_provider_and_db(self):
        """Test that cancel updates provider + DB; confirmation message sent"""
        
        booking_to_cancel = {
            "booking_id": "booking_123",
            "meeting_time": "2025-08-30T14:00:00+05:30",
            "staff_name": "Andy",
            "lead_id": "test-lead"
        }
        
        with patch.object(coochie_meeting_service.zoho_service, 'cancel_appointment') as mock_cancel:
            mock_cancel.return_value = AsyncMock(
                success=True,
                booking_cancelled=True,
                cancellation_confirmed=True
            )
            
            result = await coochie_meeting_service.cancel_meeting(
                booking_id="booking_123",
                lead_id="test-lead",
                reason="Lead requested cancellation"
            )
            
            # Should successfully cancel
            assert result["success"] is True
            assert result["booking_cancelled"] is True
            assert result["cancellation_confirmed"] is True
            
            # Should call Zoho service
            mock_cancel.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cancel_generates_confirmation_message(self):
        """Test that cancel generates appropriate confirmation message"""
        
        with patch.object(coochie_meeting_service.zoho_service, 'cancel_appointment') as mock_cancel:
            mock_cancel.return_value = AsyncMock(
                success=True,
                booking_cancelled=True
            )
            
            result = await coochie_meeting_service.cancel_meeting(
                booking_id="booking_123",
                lead_id="test-lead"
            )
            
            # Should include confirmation message
            assert result["success"] is True
            assert "confirmation_message" in result
            
            confirmation = result["confirmation_message"]
            assert "cancelled" in confirmation.lower()
            assert "confirmed" in confirmation.lower()
    
    @pytest.mark.asyncio
    async def test_cancel_failure_handling(self):
        """Test handling of cancel failures"""
        
        with patch.object(coochie_meeting_service.zoho_service, 'cancel_appointment') as mock_cancel:
            mock_cancel.return_value = AsyncMock(
                success=False,
                error_message="Booking not found"
            )
            
            result = await coochie_meeting_service.cancel_meeting(
                booking_id="invalid_booking",
                lead_id="test-lead"
            )
            
            # Should handle failure gracefully
            assert result["success"] is False
            assert "error" in result


class TestMeetingChangeWorkflow:
    """Test meeting change workflow integration"""
    
    @pytest.mark.asyncio
    async def test_reschedule_workflow_integration(self):
        """Test reschedule integration with workflow agent"""
        
        from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
        
        agent = CoochieWorkflowAgent()
        
        # Mock existing booking context
        mock_session = AsyncMock()
        mock_session.context = {
            "current_booking": {
                "booking_id": "booking_123",
                "meeting_time": "2025-08-30T14:00:00+05:30"
            }
        }
        
        mock_lead = AsyncMock()
        mock_lead.id = "test-lead"
        
        # Mock reschedule request
        with patch.object(coochie_meeting_service, 'reschedule_meeting') as mock_reschedule:
            mock_reschedule.return_value = {
                "success": True,
                "new_booking_id": "booking_456",
                "new_meeting_time": "2025-09-02T10:00:00+05:30",
                "display_time": "10:00 AM on Tuesday"
            }
            
            # This would be called when user requests reschedule
            result = await agent._handle_meeting_reschedule(
                mock_session,
                mock_lead,
                "Can we reschedule to Tuesday 10 AM?"
            )
            
            # Should handle reschedule request
            assert result is not None
            # In a real implementation, this would update the session context
            # and provide confirmation to the user
    
    @pytest.mark.asyncio
    async def test_cancel_workflow_integration(self):
        """Test cancel integration with workflow agent"""
        
        from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
        
        agent = CoochieWorkflowAgent()
        
        # Mock existing booking context
        mock_session = AsyncMock()
        mock_session.context = {
            "current_booking": {
                "booking_id": "booking_123",
                "meeting_time": "2025-08-30T14:00:00+05:30"
            }
        }
        
        mock_lead = AsyncMock()
        mock_lead.id = "test-lead"
        
        # Mock cancel request
        with patch.object(coochie_meeting_service, 'cancel_meeting') as mock_cancel:
            mock_cancel.return_value = {
                "success": True,
                "booking_cancelled": True,
                "confirmation_message": "Your meeting has been cancelled successfully."
            }
            
            # This would be called when user requests cancellation
            result = await agent._handle_meeting_cancellation(
                mock_session,
                mock_lead,
                "I need to cancel my meeting"
            )
            
            # Should handle cancellation request
            assert result is not None
            # In a real implementation, this would provide confirmation


class TestMeetingChangeValidation:
    """Test validation of meeting change operations"""
    
    @pytest.mark.asyncio
    async def test_reschedule_validation(self):
        """Test validation of reschedule requests"""
        
        # Test invalid booking ID
        result = await coochie_meeting_service.reschedule_meeting(
            original_booking_id="",
            new_option={"datetime_local": "2025-09-02T10:00:00+05:30"},
            lead_id="test-lead"
        )
        
        assert result["success"] is False
        assert "invalid" in result.get("error", "").lower()
        
        # Test invalid new option
        result = await coochie_meeting_service.reschedule_meeting(
            original_booking_id="booking_123",
            new_option={},  # Empty option
            lead_id="test-lead"
        )
        
        assert result["success"] is False
    
    @pytest.mark.asyncio
    async def test_cancel_validation(self):
        """Test validation of cancel requests"""
        
        # Test invalid booking ID
        result = await coochie_meeting_service.cancel_meeting(
            booking_id="",
            lead_id="test-lead"
        )
        
        assert result["success"] is False
        assert "invalid" in result.get("error", "").lower()
        
        # Test missing lead ID
        result = await coochie_meeting_service.cancel_meeting(
            booking_id="booking_123",
            lead_id=""
        )
        
        assert result["success"] is False


class TestMeetingChangeNotifications:
    """Test notifications for meeting changes"""
    
    @pytest.mark.asyncio
    async def test_reschedule_notification(self):
        """Test that reschedule sends appropriate notifications"""
        
        new_option = {
            "datetime_local": "2025-09-02T10:00:00+05:30",
            "display_time": "10:00 AM on Tuesday",
            "staff_name": "Andy"
        }
        
        with patch.object(coochie_meeting_service.zoho_service, 'reschedule_appointment') as mock_reschedule:
            mock_reschedule.return_value = AsyncMock(
                success=True,
                booking_id="booking_456",
                notification_sent=True
            )
            
            result = await coochie_meeting_service.reschedule_meeting(
                original_booking_id="booking_123",
                new_option=new_option,
                lead_id="test-lead"
            )
            
            # Should indicate notification was sent
            assert result["success"] is True
            assert result.get("notification_sent") is True
    
    @pytest.mark.asyncio
    async def test_cancel_notification(self):
        """Test that cancel sends appropriate notifications"""
        
        with patch.object(coochie_meeting_service.zoho_service, 'cancel_appointment') as mock_cancel:
            mock_cancel.return_value = AsyncMock(
                success=True,
                booking_cancelled=True,
                notification_sent=True
            )
            
            result = await coochie_meeting_service.cancel_meeting(
                booking_id="booking_123",
                lead_id="test-lead"
            )
            
            # Should indicate notification was sent
            assert result["success"] is True
            assert result.get("notification_sent") is True
