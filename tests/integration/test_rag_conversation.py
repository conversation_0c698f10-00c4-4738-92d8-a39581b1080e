"""
Integration Tests - RAG + Escalation Bank
Tests for RAG answer selection, escalation creation, and conversation flow
"""

import pytest
from unittest.mock import patch, AsyncMock
from datetime import datetime

from app.agents.sms_assistant import AndyAssistantState
from tests.helpers.conversation_harness import ConversationHarness, ConversationTurn
from tests.helpers.factories import LeadFactory, EscalationQuestionFactory
from tests.mocks.search_stub import SearchStub, create_high_confidence_query, create_low_confidence_query
from tests.mocks.kudosity_stub import KudosityStub


@pytest.mark.integration
class TestRAGAnswerSelection:
    """Test RAG answer selection with high confidence."""
    
    @pytest.fixture
    def search_stub(self):
        stub = SearchStub()
        # Seed with franchise-related knowledge
        stub.seed_knowledge_item(
            "Our franchise fees start at $50,000 plus GST for most territories.",
            "fees",
            ["franchise", "fees", "cost", "price", "investment"]
        )
        stub.seed_knowledge_item(
            "We provide comprehensive training including 2 weeks of initial training and ongoing support.",
            "training", 
            ["training", "support", "education", "learn"]
        )
        return stub
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(
            first_name="John",
            last_name="Doe",
            phone="+61412345678"
        )
    
    @pytest.mark.asyncio
    async def test_high_confidence_rag_answer(self, conversation_harness, test_lead, search_stub, andy_assertions):
        """Test that high-confidence RAG answers are selected and returned."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="What are your franchise fees?",
                    expected_response_contains="$50,000",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            assert len(results) == 1
            assert results[0].success is True
            
            # Should contain the RAG answer
            response = results[0].agent_response
            assert "$50,000" in response
            assert "GST" in response
            
            # Should not create escalation for high-confidence answer
            search_stub.assert_no_escalation_created()
            
            # Verify no emojis
            andy_assertions.assert_no_emojis(response)
    
    @pytest.mark.asyncio
    async def test_multiple_rag_queries_in_conversation(self, conversation_harness, test_lead, search_stub):
        """Test multiple RAG queries in a single conversation."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="What are your franchise fees?",
                    expected_response_contains="$50,000",
                    expected_db_writes=["conversation_messages_created"]
                ),
                ConversationTurn(
                    user_message="What about training?",
                    expected_response_contains="2 weeks",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            assert len(results) == 2
            assert all(result.success for result in results)
            
            # First answer about fees
            assert "$50,000" in results[0].agent_response
            
            # Second answer about training
            assert "2 weeks" in results[1].agent_response
            assert "training" in results[1].agent_response.lower()
    
    @pytest.mark.asyncio
    async def test_rag_context_awareness(self, conversation_harness, test_lead, search_stub):
        """Test that RAG considers conversation context."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="I'm interested in your franchise opportunity",
                    expected_response_contains="franchise",
                    expected_db_writes=["conversation_messages_created"]
                ),
                ConversationTurn(
                    user_message="What does it cost?",  # Context: should understand this is about franchise fees
                    expected_response_contains="$50,000",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            # Second question should be understood in context of franchise fees
            assert "$50,000" in results[1].agent_response


@pytest.mark.integration
class TestEscalationCreation:
    """Test escalation creation for low-confidence queries."""
    
    @pytest.fixture
    def search_stub(self):
        stub = SearchStub()
        stub.set_confidence_threshold(0.7)  # High threshold to trigger escalations
        return stub
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61423456789")
    
    @pytest.mark.asyncio
    async def test_low_confidence_creates_escalation(self, conversation_harness, test_lead, search_stub):
        """Test that low-confidence queries create escalation questions."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="What is your policy on purple unicorns?",
                    expected_response_contains="let me",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            assert len(results) == 1
            assert results[0].success is True
            
            # Should indicate that question will be escalated
            response = results[0].agent_response
            assert any(phrase in response.lower() 
                      for phrase in ["let me", "get back", "find out", "check"])
            
            # Should have created escalation
            search_stub.assert_escalation_created("purple unicorns")
    
    @pytest.mark.asyncio
    async def test_escalation_with_context(self, conversation_harness, test_lead, search_stub):
        """Test that escalations include conversation context."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="I'm looking at franchises in Sydney",
                    expected_db_writes=["conversation_messages_created"]
                ),
                ConversationTurn(
                    user_message="Do you have any special requirements for Sydney locations?",
                    expected_response_contains="get back",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            # Should create escalation with Sydney context
            escalation_id = search_stub.assert_escalation_created("Sydney")
            escalation = await search_stub.get_escalation_question(escalation_id)
            
            assert "Sydney" in escalation.question
    
    @pytest.mark.asyncio
    async def test_escalation_prevents_loops(self, conversation_harness, test_lead, search_stub):
        """Test that escalation prevents infinite clarification loops."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            # Ask the same unanswerable question multiple times
            turns = [
                ConversationTurn(
                    user_message="What about the purple unicorn policy?",
                    expected_response_contains="get back"
                ),
                ConversationTurn(
                    user_message="But what about purple unicorns though?",
                    expected_response_contains="get back"
                ),
                ConversationTurn(
                    user_message="I really need to know about purple unicorns",
                    expected_response_contains="team"  # Should escalate to human
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            # After multiple attempts, should escalate to human
            final_response = results[2].agent_response
            assert any(word in final_response.lower() 
                      for word in ["team", "specialist", "contact", "help"])


@pytest.mark.integration
class TestEscalationAnswerRetrieval:
    """Test retrieval of answers when admin provides them."""
    
    @pytest.fixture
    def search_stub(self):
        stub = SearchStub()
        # Seed with an answered escalation
        stub.seed_answered_escalation(
            "Do you offer financing options?",
            "Yes, we offer financing through our preferred lending partners with competitive rates.",
            "financing"
        )
        return stub
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61434567890")
    
    @pytest.mark.asyncio
    async def test_answered_escalation_retrieval(self, conversation_harness, test_lead, search_stub):
        """Test that previously answered escalations are retrieved."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="Do you have financing available?",
                    expected_response_contains="financing",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            assert len(results) == 1
            response = results[0].agent_response
            
            # Should return the previously answered escalation
            assert "financing" in response.lower()
            assert "lending partners" in response.lower()
            
            # Should not create new escalation
            search_stub.assert_no_escalation_created()
    
    @pytest.mark.asyncio
    async def test_similar_question_matching(self, conversation_harness, test_lead, search_stub):
        """Test matching of similar questions to answered escalations."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="Can I get a loan for the franchise?",  # Similar to financing question
                    expected_response_contains="financing",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            response = results[0].agent_response
            
            # Should match to the financing answer
            assert "financing" in response.lower() or "lending" in response.lower()


@pytest.mark.integration
class TestRAGEscalationFlow:
    """Test complete RAG to escalation flow."""
    
    @pytest.fixture
    def search_stub(self):
        stub = SearchStub()
        # Mix of high and low confidence content
        stub.seed_knowledge_item(
            "Our franchise fees start at $50,000 plus GST.",
            "fees",
            ["franchise", "fees", "cost"]
        )
        return stub
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61445566778")
    
    @pytest.mark.asyncio
    async def test_mixed_confidence_conversation(self, conversation_harness, test_lead, search_stub, andy_assertions):
        """Test conversation with mix of high and low confidence queries."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            turns = [
                ConversationTurn(
                    user_message="What are your franchise fees?",  # High confidence
                    expected_response_contains="$50,000",
                    expected_db_writes=["conversation_messages_created"]
                ),
                ConversationTurn(
                    user_message="What about your refund policy?",  # Low confidence
                    expected_response_contains="get back",
                    expected_db_writes=["conversation_messages_created"]
                ),
                ConversationTurn(
                    user_message="OK, and what are the fees again?",  # High confidence again
                    expected_response_contains="$50,000",
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            assert len(results) == 3
            
            # First and third should have direct answers
            assert "$50,000" in results[0].agent_response
            assert "$50,000" in results[2].agent_response
            
            # Second should indicate escalation
            assert any(phrase in results[1].agent_response.lower() 
                      for phrase in ["get back", "find out", "check"])
            
            # Should have created one escalation
            search_stub.assert_escalation_created("refund")
            
            # All responses should be emoji-free
            for result in results:
                andy_assertions.assert_no_emojis(result.agent_response)
    
    @pytest.mark.asyncio
    async def test_escalation_learning_cycle(self, conversation_harness, test_lead, search_stub):
        """Test that system learns from escalation answers."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone
            })
            
            # First conversation - creates escalation
            turns_1 = [
                ConversationTurn(
                    user_message="What are your territory requirements?",
                    expected_response_contains="get back"
                )
            ]
            
            results_1 = await conversation_harness.run_conversation(turns_1)
            
            # Admin answers the escalation
            escalation_id = search_stub.assert_escalation_created("territory")
            await search_stub.answer_escalation_question(
                escalation_id,
                "Territory requirements include minimum population of 50,000 and no competing franchises within 5km."
            )
            
            # Second conversation - should now have answer
            turns_2 = [
                ConversationTurn(
                    user_message="Tell me about territory requirements",
                    expected_response_contains="50,000"
                )
            ]
            
            results_2 = await conversation_harness.run_conversation(turns_2)
            
            # Should now return the learned answer
            assert "50,000" in results_2[0].agent_response
            assert "5km" in results_2[0].agent_response
    
    @pytest.mark.asyncio
    async def test_escalation_priority_handling(self, conversation_harness, test_lead, search_stub):
        """Test that high-priority escalations are handled appropriately."""
        with patch('app.rag.search.SearchService', return_value=search_stub):
            await conversation_harness.setup_lead({
                "first_name": test_lead.first_name,
                "phone": test_lead.phone,
                "lead_value": "high"  # High-value lead
            })
            
            turns = [
                ConversationTurn(
                    user_message="I need to know about your exclusive territory rights ASAP",
                    expected_response_contains="priority"
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            # Should indicate priority handling
            response = results[0].agent_response
            assert any(word in response.lower() 
                      for word in ["priority", "urgent", "quickly", "soon"])
