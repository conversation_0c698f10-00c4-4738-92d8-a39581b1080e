"""
Integration Tests - Kudosity SMS I/O
Tests for inbound webhook processing, outbound SMS, and message handling
"""

import pytest
import asyncio
from unittest.mock import patch, AsyncMock
from datetime import datetime

from app.api.v1.endpoints.webhooks import process_kudosity_webhook
from app.integrations.kudosity.client import KudosityClient
from tests.mocks.kudosity_stub import KudosityStub
from tests.helpers.factories import LeadFactory


@pytest.mark.integration
class TestKudosityInboundWebhook:
    """Test inbound SMS webhook processing."""
    
    @pytest.fixture
    def kudosity_stub(self):
        """Create Kudosity stub for testing."""
        return KudosityStub()
    
    @pytest.fixture
    async def test_lead(self, db_session):
        """Create test lead."""
        factory = LeadFactory(db_session)
        return await factory.create_lead(
            first_name="<PERSON>",
            last_name="Doe",
            phone="+61412345678"
        )
    
    @pytest.mark.asyncio
    async def test_inbound_webhook_processing(self, kudosity_stub, test_lead, frozen_time):
        """Test processing of inbound SMS webhook."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            webhook_payload = {
                "phone": test_lead.phone,
                "message": "Hello, I'm interested in your franchise opportunity",
                "timestamp": "2025-08-29T12:00:00+05:30",
                "message_id": "incoming_123"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            assert response["success"] is True
            assert "ai_response" in response["data"]
            
            # Should have processed through Andy pipeline
            ai_response = response["data"]["ai_response"]
            assert len(ai_response) > 0
            assert "franchise" in ai_response.lower() or "opportunity" in ai_response.lower()
    
    @pytest.mark.asyncio
    async def test_webhook_with_kudosity_disabled(self, kudosity_stub, test_lead, caplog):
        """Test webhook processing when KUDOSITY_SMS_ENABLED=false."""
        # Kudosity is disabled in test environment
        assert kudosity_stub.enabled is False
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            webhook_payload = {
                "phone": test_lead.phone,
                "message": "Test message",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            assert response["success"] is True
            
            # Should print to logs instead of sending SMS
            assert "[TEST SMS]" in caplog.text
            assert test_lead.phone in caplog.text
    
    @pytest.mark.asyncio
    async def test_webhook_invalid_phone_number(self, kudosity_stub):
        """Test webhook with invalid phone number."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            webhook_payload = {
                "phone": "invalid-phone",
                "message": "Test message",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            # Should handle gracefully
            assert response["success"] is False
            assert "invalid phone" in response.get("error", "").lower()
    
    @pytest.mark.asyncio
    async def test_webhook_missing_lead(self, kudosity_stub):
        """Test webhook for phone number not in system."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            webhook_payload = {
                "phone": "+61400000000",  # Not in system
                "message": "Test message",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            # Should handle gracefully
            assert response["success"] is False
            assert "lead not found" in response.get("error", "").lower()
    
    @pytest.mark.asyncio
    async def test_webhook_malformed_payload(self, kudosity_stub):
        """Test webhook with malformed payload."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # Missing required fields
            webhook_payload = {
                "phone": "+61412345678"
                # Missing message and timestamp
            }
            
            response = await process_kudosity_webhook(webhook_payload)
            
            assert response["success"] is False
            assert "validation" in response.get("error", "").lower()


@pytest.mark.integration
class TestKudosityOutboundSMS:
    """Test outbound SMS sending and segmentation."""
    
    @pytest.fixture
    def kudosity_stub(self):
        return KudosityStub(max_segment_chars=160)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(phone="+61412345678")
    
    @pytest.mark.asyncio
    async def test_outbound_sms_single_segment(self, kudosity_stub, test_lead):
        """Test sending single-segment SMS."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            message = "Hello John, thanks for your interest in our franchise opportunity!"
            
            result = await kudosity_stub.send_sms(test_lead.phone, message)
            
            assert result["success"] is True
            assert result["segments_sent"] == 1
            
            # Verify message was recorded
            sent_messages = kudosity_stub.get_sent_messages(test_lead.phone)
            assert len(sent_messages) == 1
            assert sent_messages[0].message == message
            assert len(sent_messages[0].segments) == 1
    
    @pytest.mark.asyncio
    async def test_outbound_sms_multiple_segments(self, kudosity_stub, test_lead):
        """Test sending multi-segment SMS."""
        # Create long message that will require multiple segments
        long_message = "A" * 400  # Will require 3 segments at 160 chars each
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            result = await kudosity_stub.send_sms(test_lead.phone, long_message)
            
            assert result["success"] is True
            assert result["segments_sent"] >= 2
            
            # Verify segmentation
            sent_message = kudosity_stub.get_last_message(test_lead.phone)
            assert len(sent_message.segments) >= 2
            
            # Verify each segment is within limits
            for segment in sent_message.segments:
                assert len(segment) <= 160
            
            # Verify reassembly
            reassembled = "".join(sent_message.segments)
            assert reassembled == long_message
    
    @pytest.mark.asyncio
    async def test_outbound_sms_with_url_preservation(self, kudosity_stub, test_lead):
        """Test that URLs are not broken across segments."""
        url = "https://example.com/very/long/path/to/franchise/information"
        message = f"Please visit our website at {url} for detailed information about our franchise opportunities and requirements."
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            result = await kudosity_stub.send_sms(test_lead.phone, message)
            
            assert result["success"] is True
            
            # Verify URL wasn't broken
            sent_message = kudosity_stub.get_last_message(test_lead.phone)
            
            # URL should appear complete in one segment
            url_found = False
            for segment in sent_message.segments:
                if url in segment:
                    url_found = True
                    break
            
            assert url_found, f"URL was broken across segments: {sent_message.segments}"
    
    @pytest.mark.asyncio
    async def test_outbound_sms_deduplication(self, kudosity_stub, test_lead):
        """Test that duplicate messages are prevented."""
        message = "Test message for deduplication"
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # Send same message twice
            result1 = await kudosity_stub.send_sms(test_lead.phone, message)
            result2 = await kudosity_stub.send_sms(test_lead.phone, message)
            
            assert result1["success"] is True
            assert result2["success"] is True
            
            # Should have two separate sends (stub doesn't prevent duplicates by default)
            sent_messages = kudosity_stub.get_sent_messages(test_lead.phone)
            assert len(sent_messages) == 2
            
            # But in real system, deduplication would prevent this
            # Test the assertion method
            try:
                kudosity_stub.assert_no_duplicate_sends()
                assert False, "Should have detected duplicates"
            except AssertionError:
                pass  # Expected
    
    @pytest.mark.asyncio
    async def test_outbound_sms_error_handling(self, kudosity_stub, test_lead):
        """Test SMS error handling and retry logic."""
        # Configure stub to fail
        kudosity_stub.set_failure_mode(True, "Network timeout")
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            message = "Test message"
            
            result = await kudosity_stub.send_sms(test_lead.phone, message)
            
            assert result["success"] is False
            assert "Network timeout" in result["error"]
            
            # Verify failure was recorded
            failed_sends = kudosity_stub.get_failed_sends()
            assert len(failed_sends) == 1
            assert failed_sends[0].error == "Network timeout"
    
    def test_sms_segment_validation(self, kudosity_stub):
        """Test SMS segment validation rules."""
        # Test segment length validation
        valid_segment = "A" * 160  # Exactly at limit
        invalid_segment = "A" * 161  # Over limit
        
        # Valid segment should pass
        segments = kudosity_stub._split_message(valid_segment)
        assert len(segments) == 1
        assert len(segments[0]) == 160
        
        # Invalid segment should be split
        segments = kudosity_stub._split_message(invalid_segment)
        assert len(segments) == 2
        for segment in segments:
            assert len(segment) <= 160
    
    def test_sms_encoding_handling(self, kudosity_stub):
        """Test handling of different SMS encodings."""
        # GSM 7-bit message
        gsm_message = "Hello world with GSM characters!"
        segments = kudosity_stub._split_message(gsm_message)
        assert len(segments) == 1
        
        # Unicode message (would use UCS-2 encoding)
        unicode_message = "Hello 🌟 world with emojis 😊"
        segments = kudosity_stub._split_message(unicode_message)
        # Unicode messages have lower character limits per segment
        assert all(len(segment) <= 160 for segment in segments)


@pytest.mark.integration
class TestKudosityMessageFlow:
    """Test complete message flow through Kudosity integration."""
    
    @pytest.fixture
    def kudosity_stub(self):
        return KudosityStub()
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(
            first_name="Jane",
            last_name="Smith",
            phone="+61423456789"
        )
    
    @pytest.mark.asyncio
    async def test_complete_conversation_flow(self, kudosity_stub, test_lead, andy_assertions):
        """Test complete conversation flow through Kudosity."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # Simulate inbound message
            inbound_payload = {
                "phone": test_lead.phone,
                "message": "I'm interested in your franchise",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response = await process_kudosity_webhook(inbound_payload)
            
            assert response["success"] is True
            
            # Should have generated outbound response
            sent_messages = kudosity_stub.get_sent_messages(test_lead.phone)
            assert len(sent_messages) >= 1
            
            # Verify response quality
            last_message = kudosity_stub.get_last_message(test_lead.phone)
            andy_assertions.assert_no_emojis(last_message.message)
            
            # Response should be relevant to franchise inquiry
            assert any(word in last_message.message.lower() 
                      for word in ["franchise", "opportunity", "business", "investment"])
    
    @pytest.mark.asyncio
    async def test_meeting_booking_flow(self, kudosity_stub, test_lead, meeting_agent_mocks):
        """Test meeting booking through Kudosity integration."""
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # Meeting request
            meeting_request = {
                "phone": test_lead.phone,
                "message": "I'd like to schedule a meeting for tomorrow at 2pm",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response = await process_kudosity_webhook(meeting_request)
            
            assert response["success"] is True
            
            # Should have responded about meeting availability
            last_message = kudosity_stub.get_last_message(test_lead.phone)
            assert any(word in last_message.message.lower() 
                      for word in ["meeting", "available", "schedule", "book"])
            
            # Follow up with confirmation
            confirmation = {
                "phone": test_lead.phone,
                "message": "Yes, that works for me",
                "timestamp": "2025-08-29T12:05:00+05:30"
            }
            
            response = await process_kudosity_webhook(confirmation)
            
            assert response["success"] is True
            
            # Should confirm booking
            confirmation_message = kudosity_stub.get_last_message(test_lead.phone)
            assert any(word in confirmation_message.message.lower() 
                      for word in ["booked", "confirmed", "scheduled"])
    
    @pytest.mark.asyncio
    async def test_error_recovery_flow(self, kudosity_stub, test_lead):
        """Test error recovery in message flow."""
        # Configure intermittent failures
        kudosity_stub.set_failure_mode(True, "Temporary network error")
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub):
            # First message should fail
            payload1 = {
                "phone": test_lead.phone,
                "message": "Test message 1",
                "timestamp": "2025-08-29T12:00:00+05:30"
            }
            
            response1 = await process_kudosity_webhook(payload1)
            # Webhook processing might succeed even if SMS sending fails
            
            # Reset failure mode
            kudosity_stub.set_failure_mode(False)
            
            # Second message should succeed
            payload2 = {
                "phone": test_lead.phone,
                "message": "Test message 2",
                "timestamp": "2025-08-29T12:01:00+05:30"
            }
            
            response2 = await process_kudosity_webhook(payload2)
            assert response2["success"] is True
            
            # Should have at least one successful send
            successful_sends = [msg for msg in kudosity_stub.get_sent_messages(test_lead.phone) if msg.success]
            assert len(successful_sends) >= 1
    
    def test_message_ordering_preservation(self, kudosity_stub):
        """Test that message ordering is preserved."""
        phone = "+61412345678"
        messages = [
            "Message 1",
            "Message 2", 
            "Message 3"
        ]
        
        # Send messages in order
        for i, message in enumerate(messages):
            asyncio.run(kudosity_stub.send_sms(phone, message))
        
        # Verify order is preserved
        sent_messages = kudosity_stub.get_sent_messages(phone)
        assert len(sent_messages) == 3
        
        for i, sent_msg in enumerate(sent_messages):
            assert sent_msg.message == messages[i]
            if i > 0:
                assert sent_msg.timestamp >= sent_messages[i-1].timestamp
