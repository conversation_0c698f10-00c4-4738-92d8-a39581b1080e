"""
Integration Tests — SMS IO & Splitter
Tests inbound webhook processing, SMS output when disabled, segment limits, and no follow-up scheduling
"""

import pytest
import json
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.api.v1.endpoints.kudosity_webhook import process_kudosity_webhook


class TestSMSWebhookProcessing:
    """Test SMS webhook processing with KUDOSITY_SMS_ENABLED=false"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.mark.asyncio
    async def test_inbound_webhook_response_printed(self, client, mock_kudosity_client, log_capture):
        """Test that inbound webhook generates response printed to terminal when SMS disabled"""
        
        # Webhook payload
        webhook_payload = {
            "from": "+61400000000",
            "to": "+61400000001", 
            "message": "Yes, I have time to chat",
            "timestamp": "2025-08-29T12:00:00Z",
            "message_id": "msg_123"
        }
        
        # Mock lead lookup
        with patch('app.api.v1.endpoints.kudosity_webhook.get_lead_by_phone') as mock_get_lead:
            mock_lead = AsyncMock()
            mock_lead.id = "test-lead-123"
            mock_lead.name = "John Smith"
            mock_lead.phone_number = "+61400000000"
            mock_get_lead.return_value = mock_lead
            
            # Mock conversation session
            with patch('app.api.v1.endpoints.kudosity_webhook.get_or_create_conversation_session') as mock_session:
                mock_conv_session = AsyncMock()
                mock_conv_session.id = "session-123"
                mock_conv_session.workflow_stage = "introduction"
                mock_session.return_value = mock_conv_session
                
                # Mock workflow agent
                with patch('app.agents.coochie_workflow_agent.CoochieWorkflowAgent') as mock_agent_class:
                    mock_agent = AsyncMock()
                    mock_agent.process_message.return_value = {
                        "response": "Great, thanks for your enquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?",
                        "stage": "work_background",
                        "next_stage": "work_background",
                        "awaiting_response": True
                    }
                    mock_agent_class.return_value = mock_agent
                    
                    # Process webhook
                    response = client.post(
                        "/api/v1/kudosity/webhook",
                        json=webhook_payload,
                        headers={"Content-Type": "application/json"}
                    )
                    
                    assert response.status_code == 200
                    
                    # Check that SMS was not actually sent (mock should not be called for real sending)
                    # But response should be generated and logged
                    log_content = log_capture.getvalue()
                    
                    # Should contain evidence of SMS being mocked/printed
                    assert "SMS OUTPUT" in log_content or "SMS mocked" in log_content or "SMS disabled" in log_content
    
    @pytest.mark.asyncio
    async def test_webhook_processes_coochie_workflow(self, client):
        """Test that webhook processes Coochie workflow correctly"""
        
        webhook_payload = {
            "from": "+61400000000",
            "to": "+61400000001",
            "message": "I'm a marketing manager", 
            "timestamp": "2025-08-29T12:00:00Z",
            "message_id": "msg_124"
        }
        
        with patch('app.api.v1.endpoints.kudosity_webhook.get_lead_by_phone') as mock_get_lead:
            mock_lead = AsyncMock()
            mock_lead.id = "test-lead-123"
            mock_lead.name = "John Smith"
            mock_get_lead.return_value = mock_lead
            
            with patch('app.api.v1.endpoints.kudosity_webhook.get_or_create_conversation_session') as mock_session:
                mock_conv_session = AsyncMock()
                mock_conv_session.workflow_stage = "work_background"
                mock_session.return_value = mock_conv_session
                
                with patch('app.agents.coochie_workflow_agent.CoochieWorkflowAgent') as mock_agent_class:
                    mock_agent = AsyncMock()
                    mock_agent.process_message.return_value = {
                        "response": "That sounds interesting, what made you enquire about this franchise opportunity given you come from a different background?",
                        "stage": "motivation",
                        "next_stage": "motivation", 
                        "awaiting_response": True
                    }
                    mock_agent_class.return_value = mock_agent
                    
                    response = client.post(
                        "/api/v1/kudosity/webhook",
                        json=webhook_payload
                    )
                    
                    assert response.status_code == 200
                    
                    # Verify workflow agent was called
                    mock_agent.process_message.assert_called_once()
                    
                    # Verify response follows workflow pattern
                    call_args = mock_agent.process_message.call_args
                    assert "I'm a marketing manager" in str(call_args)


class TestSMSSegmentLimits:
    """Test SMS segment limits and URL handling"""
    
    def test_segment_calculation(self):
        """Test SMS segment calculation"""
        from app.services.sms_service import calculate_sms_segments
        
        # Single segment (160 chars)
        short_message = "Hi John, thanks for your enquiry about Coochie Hydrogreen. Do you have time to chat?"
        segments = calculate_sms_segments(short_message)
        assert segments == 1
        
        # Multiple segments
        long_message = "Hi John, thanks for your enquiry about Coochie Hydrogreen franchise. I'm Andy, Lead Qualification Specialist with over 5 years of experience. I'd love to walk you through the business model and answer any questions you may have. The franchise fee is $120K but we have flexible payment options including 50% upfront and 50% after 12 months. We also provide comprehensive training for 4 weeks plus ongoing support. Some of our franchise partners earn over $200K net annually. Would you like to schedule a call to discuss further?"
        segments = calculate_sms_segments(long_message)
        assert segments > 1
    
    def test_url_preservation_in_segments(self):
        """Test that URLs are not broken across SMS segments"""
        from app.services.sms_service import split_sms_segments
        
        message_with_url = "Thanks for booking! Your meeting link is https://zoom.us/j/1234567890?pwd=abcdef123456 and the booking confirmation is at https://bookings.zoho.com/confirmation/abc123. See you then!"
        
        segments = split_sms_segments(message_with_url)
        
        # Check that no segment breaks a URL
        for segment in segments:
            # If segment contains part of a URL, it should contain the complete URL
            if "https://" in segment:
                # Count complete URLs in this segment
                url_starts = segment.count("https://")
                url_ends = segment.count(" ") + segment.count("\n") + (1 if segment.endswith("https://") else 0)
                
                # Each URL should be complete within the segment
                assert url_starts <= url_ends or segment.endswith("https://"), \
                    f"URL broken across segments in: {segment}"
    
    def test_segment_limit_enforcement(self):
        """Test that messages respect segment limits"""
        from app.services.sms_service import enforce_segment_limit
        
        # Very long message that would exceed reasonable limits
        very_long_message = "This is a very long message. " * 50  # 1500+ chars
        
        limited_message = enforce_segment_limit(very_long_message, max_segments=3)
        
        segments = calculate_sms_segments(limited_message)
        assert segments <= 3, f"Message should be limited to 3 segments, got {segments}"
        
        # Should end with truncation indicator
        assert limited_message.endswith("...") or limited_message.endswith("(cont'd)"), \
            "Long message should indicate truncation"


class TestNoFollowupScheduling:
    """Test that no follow-up tasks are scheduled"""
    
    @pytest.mark.asyncio
    async def test_webhook_no_followup_tasks_scheduled(self, task_queue_monitor, client):
        """Test that webhook processing doesn't schedule follow-up tasks"""
        
        # Clear task queue
        task_queue_monitor.clear()
        
        webhook_payload = {
            "from": "+61400000000",
            "to": "+61400000001",
            "message": "Yes, I have time",
            "timestamp": "2025-08-29T12:00:00Z",
            "message_id": "msg_125"
        }
        
        with patch('app.api.v1.endpoints.kudosity_webhook.get_lead_by_phone') as mock_get_lead:
            mock_lead = AsyncMock()
            mock_lead.id = "test-lead-123"
            mock_get_lead.return_value = mock_lead
            
            with patch('app.api.v1.endpoints.kudosity_webhook.get_or_create_conversation_session') as mock_session:
                mock_conv_session = AsyncMock()
                mock_conv_session.workflow_stage = "introduction"
                mock_session.return_value = mock_conv_session
                
                with patch('app.agents.coochie_workflow_agent.CoochieWorkflowAgent') as mock_agent_class:
                    mock_agent = AsyncMock()
                    mock_agent.process_message.return_value = {
                        "response": "Great, thanks for your enquiry.",
                        "stage": "work_background",
                        "awaiting_response": True
                    }
                    mock_agent_class.return_value = mock_agent
                    
                    response = client.post(
                        "/api/v1/kudosity/webhook",
                        json=webhook_payload
                    )
                    
                    assert response.status_code == 200
                    
                    # Check that no follow-up tasks were scheduled
                    followup_tasks = [
                        task for task in task_queue_monitor 
                        if "followup" in task["task_name"].lower() or 
                           "follow_up" in task["task_name"].lower()
                    ]
                    
                    assert len(followup_tasks) == 0, \
                        f"No follow-up tasks should be scheduled: {followup_tasks}"
    
    @pytest.mark.asyncio
    async def test_lead_silence_no_automatic_followup(self, task_queue_monitor, log_capture):
        """Test that lead silence doesn't trigger automatic follow-ups"""
        
        # Clear task queue
        task_queue_monitor.clear()
        
        # Simulate a scenario where lead goes silent
        # This would normally trigger follow-up scheduling, but should be suppressed
        
        from app.core.followup_suppression import followup_suppressor
        
        # Test that suppression is active
        suppression_result = followup_suppressor.suppress_followup(
            "lead_silence_followup",
            lead_id="test-lead-123"
        )
        
        assert suppression_result["suppressed"] is True
        assert "WORKFLOW_ANDY_NO_FOLLOWUPS" in suppression_result["reason"]
        
        # Verify no tasks were scheduled
        followup_tasks = [
            task for task in task_queue_monitor 
            if "followup" in task["task_name"].lower()
        ]
        
        assert len(followup_tasks) == 0, \
            f"Lead silence should not schedule follow-ups: {followup_tasks}"
        
        # Check logs for suppression evidence
        log_content = log_capture.getvalue()
        assert "suppressed" in log_content.lower() or "WORKFLOW_ANDY_NO_FOLLOWUPS" in log_content
    
    @pytest.mark.asyncio
    async def test_followup_scheduler_suppression(self, mock_followup_scheduler):
        """Test that follow-up scheduler is properly suppressed"""
        
        # Attempt to schedule a follow-up
        result = mock_followup_scheduler.schedule_followup(
            lead_id="test-lead-123",
            message="Test follow-up",
            delay_hours=3
        )
        
        # Should be suppressed
        assert result["suppressed"] is True
        assert "WORKFLOW_ANDY_NO_FOLLOWUPS" in result["reason"]
        
        # Verify scheduler was called (but returned suppressed result)
        mock_followup_scheduler.schedule_followup.assert_called_once()


class TestSMSOutputFormatting:
    """Test SMS output formatting when disabled"""
    
    @pytest.mark.asyncio
    async def test_sms_output_terminal_formatting(self, log_capture):
        """Test that SMS output is properly formatted for terminal when disabled"""
        
        from app.core.sms_control import print_sms_output
        
        phone_number = "+61400000000"
        message = "Hi John, thanks for your enquiry about Coochie Hydrogreen. Do you have time to chat?"
        context = "Coochie Workflow Response"
        
        print_sms_output(phone_number, message, context)
        
        log_content = log_capture.getvalue()
        
        # Should contain formatted output
        assert "SMS OUTPUT" in log_content
        assert "NOT SENT" in log_content
        assert phone_number[:6] in log_content  # Masked phone number
        assert str(len(message)) in log_content  # Message length
        assert context in log_content
    
    def test_sms_mock_response_generation(self):
        """Test generation of mock SMS responses"""
        
        from app.core.sms_control import create_mock_sms_response
        
        mock_response = create_mock_sms_response("webhook_test")
        
        assert mock_response["success"] is True
        assert mock_response["message_id"].startswith("mock_webhook_test_")
        assert mock_response["error"] is None
        assert mock_response["mock"] is True
    
    @pytest.mark.asyncio
    async def test_sms_status_reporting(self):
        """Test SMS status reporting"""
        
        from app.core.sms_control import get_sms_status
        
        status = get_sms_status()
        
        # Should reflect current configuration
        assert "sms_test_mode" in status
        assert "kudosity_sms_enabled" in status
        assert "will_send_sms" in status
        assert "reason" in status
        
        # With current test configuration, SMS should be disabled
        assert status["will_send_sms"] is False
        assert "test mode" in status["reason"].lower() or "disabled" in status["reason"].lower()


class TestWebhookErrorHandling:
    """Test webhook error handling scenarios"""
    
    @pytest.mark.asyncio
    async def test_invalid_webhook_payload(self, client):
        """Test handling of invalid webhook payloads"""
        
        invalid_payloads = [
            {},  # Empty payload
            {"from": "+61400000000"},  # Missing required fields
            {"invalid": "payload"},  # Wrong structure
            None  # Null payload
        ]
        
        for payload in invalid_payloads:
            response = client.post(
                "/api/v1/kudosity/webhook",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # Should handle gracefully (not crash)
            assert response.status_code in [200, 400, 422], \
                f"Should handle invalid payload gracefully: {payload}"
    
    @pytest.mark.asyncio
    async def test_lead_not_found_handling(self, client):
        """Test handling when lead is not found"""
        
        webhook_payload = {
            "from": "+61400999999",  # Unknown number
            "to": "+61400000001",
            "message": "Hello",
            "timestamp": "2025-08-29T12:00:00Z",
            "message_id": "msg_126"
        }
        
        with patch('app.api.v1.endpoints.kudosity_webhook.get_lead_by_phone') as mock_get_lead:
            mock_get_lead.return_value = None  # Lead not found
            
            response = client.post(
                "/api/v1/kudosity/webhook",
                json=webhook_payload
            )
            
            # Should handle gracefully
            assert response.status_code == 200
            
            # Should not crash or schedule follow-ups
            # (Implementation should handle unknown leads appropriately)
