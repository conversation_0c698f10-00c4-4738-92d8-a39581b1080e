"""
Integration Tests - Meeting Reschedule & Cancel
Tests for rescheduling and cancelling existing meetings with provider updates
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock

from app.meeting_agent.agent import MeetingAgent
from app.meeting_agent.fsm import MeetingState
from tests.helpers.conversation_harness import ConversationHarness, ConversationTurn
from tests.helpers.factories import LeadFactory, BookingFactory
from tests.mocks.zoho_bookings_stub import ZohoBookingsStub
from tests.mocks.kudosity_stub import KudosityStub


@pytest.mark.integration
class TestMeetingReschedule:
    """Test meeting rescheduling functionality."""
    
    @pytest.fixture
    def zoho_stub(self):
        stub = ZohoBookingsStub()
        # Set up availability for rescheduling
        stub.set_availability({
            "2025-08-30": ["09:30", "12:00", "15:00"],
            "2025-09-01": ["10:00", "13:00", "16:30"],
            "2025-09-02": ["09:00", "14:00", "17:00"],
            "2025-09-03": ["11:00", "13:00", "15:00"]
        })
        return stub
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead_with_booking(self, db_session):
        # Create lead
        lead_factory = LeadFactory(db_session)
        lead = await lead_factory.create_qualified_lead(
            first_name="John",
            last_name="Doe",
            phone="+61412345678"
        )
        
        # Create existing booking
        booking_factory = BookingFactory(db_session)
        booking = await booking_factory.create_confirmed_booking(
            lead_id=lead.id,
            customer_name=f"{lead.first_name} {lead.last_name}",
            customer_email=lead.email,
            customer_phone=lead.phone,
            start_time=datetime(2025, 8, 30, 14, 0),  # Tomorrow 2 PM
            zoho_booking_id="existing_booking_123",
            meeting_link="https://zoom.us/meeting/existing123"
        )
        
        return lead, booking
    
    @pytest.mark.asyncio
    async def test_reschedule_existing_meeting(self, conversation_harness, test_lead_with_booking, zoho_stub, frozen_time):
        """Test rescheduling an existing meeting."""
        lead, existing_booking = test_lead_with_booking
        
        # Add existing booking to Zoho stub
        zoho_stub.bookings[existing_booking.zoho_booking_id] = {
            "id": existing_booking.zoho_booking_id,
            "lead_id": str(lead.id),
            "start_time": existing_booking.start_time,
            "status": "confirmed"
        }
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "last_name": lead.last_name,
            "phone": lead.phone,
            "email": lead.email
        })
        
        turns = [
            ConversationTurn(
                user_message="I need to reschedule our meeting",
                expected_response_contains="reschedule",
                expected_fsm_state=MeetingState.RESCHEDULING,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="How about Monday at 1pm instead?",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Yes, that works better",
                expected_response_contains="rescheduled",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 3
        assert all(result.success for result in results)
        
        # Verify reschedule was processed
        reschedule_response = results[2].agent_response
        assert "rescheduled" in reschedule_response.lower()
        assert "Monday" in reschedule_response
        
        # Verify Zoho booking was updated
        zoho_stub.assert_booking_created(existing_booking.zoho_booking_id)
    
    @pytest.mark.asyncio
    async def test_reschedule_no_existing_booking(self, conversation_harness, db_session, frozen_time):
        """Test reschedule request when no existing booking exists."""
        # Create lead without existing booking
        lead_factory = LeadFactory(db_session)
        lead = await lead_factory.create_qualified_lead(phone="+61423456789")
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I need to reschedule our meeting",
                expected_response_contains="don't see",
                expected_fsm_state=MeetingState.COLLECT_DAY,
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should indicate no existing meeting found
        response = results[0].agent_response
        assert any(phrase in response.lower() 
                  for phrase in ["don't see", "no meeting", "schedule a new"])
    
    @pytest.mark.asyncio
    async def test_reschedule_with_date_range(self, conversation_harness, test_lead_with_booking, frozen_time):
        """Test rescheduling with a date range request."""
        lead, existing_booking = test_lead_with_booking
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Can we reschedule for sometime next week?",
                expected_response_contains="next week",
                expected_fsm_state=MeetingState.RESCHEDULING,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Tuesday at 2pm would be perfect",
                expected_response_contains="rescheduled",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        assert "Tuesday" in results[1].agent_response
    
    @pytest.mark.asyncio
    async def test_reschedule_failure_recovery(self, conversation_harness, test_lead_with_booking, meeting_agent_mocks, frozen_time):
        """Test recovery when reschedule fails."""
        lead, existing_booking = test_lead_with_booking
        
        # Configure Zoho to fail reschedule
        meeting_agent_mocks['zoho'].set_failure_mode(booking=True, reason="Reschedule failed")
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Reschedule for Monday at 10am",
                expected_response_contains="trouble",
                expected_fsm_state=MeetingState.RESCHEDULING
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should handle failure gracefully
        failure_response = results[0].agent_response
        assert any(word in failure_response.lower() 
                  for word in ["trouble", "issue", "try again", "contact"])


@pytest.mark.integration
class TestMeetingCancellation:
    """Test meeting cancellation functionality."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead_with_booking(self, db_session):
        # Create lead
        lead_factory = LeadFactory(db_session)
        lead = await lead_factory.create_qualified_lead(
            first_name="Jane",
            last_name="Smith",
            phone="+61423456789"
        )
        
        # Create existing booking
        booking_factory = BookingFactory(db_session)
        booking = await booking_factory.create_confirmed_booking(
            lead_id=lead.id,
            customer_name=f"{lead.first_name} {lead.last_name}",
            customer_email=lead.email,
            customer_phone=lead.phone,
            start_time=datetime(2025, 9, 1, 10, 0),  # Monday 10 AM
            zoho_booking_id="cancel_booking_123",
            meeting_link="https://zoom.us/meeting/cancel123"
        )
        
        return lead, booking
    
    @pytest.mark.asyncio
    async def test_cancel_existing_meeting(self, conversation_harness, test_lead_with_booking, meeting_agent_mocks, frozen_time):
        """Test cancelling an existing meeting."""
        lead, existing_booking = test_lead_with_booking
        
        # Add booking to Zoho stub
        meeting_agent_mocks['zoho'].bookings[existing_booking.zoho_booking_id] = {
            "id": existing_booking.zoho_booking_id,
            "lead_id": str(lead.id),
            "status": "confirmed"
        }
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I need to cancel our meeting",
                expected_response_contains="cancel",
                expected_fsm_state=MeetingState.CANCELING,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Yes, please cancel it",
                expected_response_contains="cancelled",
                expected_fsm_state=MeetingState.IDLE,
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        assert all(result.success for result in results)
        
        # Verify cancellation confirmation
        cancel_response = results[1].agent_response
        assert "cancelled" in cancel_response.lower()
        
        # Verify Zoho booking was cancelled
        meeting_agent_mocks['zoho'].assert_booking_cancelled(existing_booking.zoho_booking_id)
    
    @pytest.mark.asyncio
    async def test_cancel_no_existing_booking(self, conversation_harness, db_session, frozen_time):
        """Test cancel request when no existing booking exists."""
        # Create lead without existing booking
        lead_factory = LeadFactory(db_session)
        lead = await lead_factory.create_qualified_lead(phone="+61434567890")
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Cancel my appointment",
                expected_response_contains="don't see",
                expected_fsm_state=MeetingState.IDLE,
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should indicate no existing meeting found
        response = results[0].agent_response
        assert any(phrase in response.lower() 
                  for phrase in ["don't see", "no meeting", "existing"])
    
    @pytest.mark.asyncio
    async def test_cancel_confirmation_required(self, conversation_harness, test_lead_with_booking, frozen_time):
        """Test that cancellation requires confirmation."""
        lead, existing_booking = test_lead_with_booking
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Cancel my meeting",
                expected_response_contains="sure",
                expected_fsm_state=MeetingState.CANCELING,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Actually, never mind",
                expected_response_contains="kept",
                expected_fsm_state=MeetingState.IDLE,
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should ask for confirmation
        confirm_response = results[0].agent_response
        assert any(word in confirm_response.lower() 
                  for word in ["sure", "confirm", "proceed"])
        
        # Should handle cancellation of cancellation
        keep_response = results[1].agent_response
        assert any(word in keep_response.lower() 
                  for word in ["kept", "scheduled", "no problem"])
    
    @pytest.mark.asyncio
    async def test_cancel_failure_recovery(self, conversation_harness, test_lead_with_booking, meeting_agent_mocks, frozen_time):
        """Test recovery when cancellation fails."""
        lead, existing_booking = test_lead_with_booking
        
        # Configure Zoho to fail cancellation
        meeting_agent_mocks['zoho'].set_failure_mode(booking=True, reason="Cancellation failed")
        
        await conversation_harness.setup_lead({
            "first_name": lead.first_name,
            "phone": lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Cancel my meeting",
                expected_fsm_state=MeetingState.CANCELING
            ),
            ConversationTurn(
                user_message="Yes, cancel it",
                expected_response_contains="trouble"
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should handle failure gracefully
        failure_response = results[1].agent_response
        assert any(word in failure_response.lower() 
                  for word in ["trouble", "issue", "contact", "help"])


@pytest.mark.integration
class TestMeetingChangeDatabase:
    """Test database updates for meeting changes."""
    
    @pytest.fixture
    async def test_lead_with_booking(self, db_session):
        # Create lead and booking
        lead_factory = LeadFactory(db_session)
        lead = await lead_factory.create_qualified_lead(phone="+61445566778")
        
        booking_factory = BookingFactory(db_session)
        booking = await booking_factory.create_confirmed_booking(
            lead_id=lead.id,
            customer_phone=lead.phone,
            start_time=datetime(2025, 8, 30, 15, 0),
            zoho_booking_id="db_test_booking_123"
        )
        
        return lead, booking
    
    @pytest.mark.asyncio
    async def test_reschedule_updates_database(self, test_lead_with_booking, db_session, meeting_agent_mocks, andy_assertions):
        """Test that rescheduling updates database records correctly."""
        lead, original_booking = test_lead_with_booking
        
        # Mock successful reschedule
        new_start_time = datetime(2025, 9, 2, 11, 0)  # Tuesday 11 AM
        meeting_agent_mocks['zoho'].bookings[original_booking.zoho_booking_id] = {
            "id": original_booking.zoho_booking_id,
            "start_time": new_start_time,
            "status": "rescheduled"
        }
        
        # Simulate reschedule through meeting agent
        meeting_agent = MeetingAgent()
        
        with patch.object(meeting_agent, 'zoho_service', meeting_agent_mocks['zoho']):
            result = await meeting_agent.handle_reschedule(
                lead.phone,
                original_booking.zoho_booking_id,
                new_start_time
            )
        
        assert result["success"] is True
        
        # Verify database was updated
        from app.models.booking import Booking
        from sqlalchemy import select
        
        updated_booking_result = await db_session.execute(
            select(Booking).where(Booking.id == original_booking.id)
        )
        updated_booking = updated_booking_result.scalar_one()
        
        # Check that times are stored in UTC
        andy_assertions.assert_utc_storage(updated_booking.start_time)
        andy_assertions.assert_utc_storage(updated_booking.updated_at)
        
        # Check that status was updated
        assert updated_booking.status in ["rescheduled", "confirmed"]
    
    @pytest.mark.asyncio
    async def test_cancellation_updates_database(self, test_lead_with_booking, db_session, meeting_agent_mocks, andy_assertions):
        """Test that cancellation updates database records correctly."""
        lead, original_booking = test_lead_with_booking
        
        # Mock successful cancellation
        meeting_agent_mocks['zoho'].bookings[original_booking.zoho_booking_id] = {
            "id": original_booking.zoho_booking_id,
            "status": "cancelled",
            "cancelled_at": datetime.utcnow()
        }
        
        # Simulate cancellation through meeting agent
        meeting_agent = MeetingAgent()
        
        with patch.object(meeting_agent, 'zoho_service', meeting_agent_mocks['zoho']):
            result = await meeting_agent.handle_cancellation(
                lead.phone,
                original_booking.zoho_booking_id
            )
        
        assert result["success"] is True
        
        # Verify database was updated
        from app.models.booking import Booking
        from sqlalchemy import select
        
        cancelled_booking_result = await db_session.execute(
            select(Booking).where(Booking.id == original_booking.id)
        )
        cancelled_booking = cancelled_booking_result.scalar_one()
        
        # Check that cancellation was recorded
        assert cancelled_booking.status == "cancelled"
        assert cancelled_booking.cancelled_at is not None
        
        # Check UTC storage
        andy_assertions.assert_utc_storage(cancelled_booking.cancelled_at)
        andy_assertions.assert_utc_storage(cancelled_booking.updated_at)
    
    @pytest.mark.asyncio
    async def test_meeting_history_tracking(self, test_lead_with_booking, db_session):
        """Test that meeting changes are tracked in history."""
        lead, original_booking = test_lead_with_booking
        
        # Create history entries for booking changes
        from app.models.booking_history import BookingHistory
        
        history_entries = [
            BookingHistory(
                booking_id=original_booking.id,
                action="created",
                old_start_time=None,
                new_start_time=original_booking.start_time,
                created_at=datetime.utcnow()
            ),
            BookingHistory(
                booking_id=original_booking.id,
                action="rescheduled",
                old_start_time=original_booking.start_time,
                new_start_time=datetime(2025, 9, 2, 11, 0),
                created_at=datetime.utcnow()
            )
        ]
        
        for entry in history_entries:
            db_session.add(entry)
        await db_session.commit()
        
        # Verify history was recorded
        from sqlalchemy import select
        
        history_result = await db_session.execute(
            select(BookingHistory).where(BookingHistory.booking_id == original_booking.id)
        )
        history_records = history_result.scalars().all()
        
        assert len(history_records) == 2
        assert history_records[0].action == "created"
        assert history_records[1].action == "rescheduled"
