"""
Integration Tests - Meeting Booking
Tests for all 11 meeting booking cases with DB writes and timezone handling
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock

from app.meeting_agent.agent import MeetingAgent
from app.meeting_agent.fsm import MeetingState
from tests.helpers.conversation_harness import ConversationHarness, ConversationTurn
from tests.helpers.factories import LeadFactory
from tests.mocks.zoho_bookings_stub import ZohoBookingsStub
from tests.mocks.kudosity_stub import KudosityStub


@pytest.mark.integration
class TestMeetingBookingCases:
    """Test all 11 meeting booking cases."""
    
    @pytest.fixture
    def meeting_agent(self):
        return MeetingAgent()
    
    @pytest.fixture
    def zoho_stub(self):
        stub = ZohoBookingsStub()
        # Set up availability for test dates
        stub.set_availability({
            "2025-08-30": ["09:30", "12:00", "15:00"],  # Tomorrow
            "2025-09-01": ["10:00", "13:00", "16:30"],  # Monday
            "2025-09-03": ["11:00", "13:00", "15:00"],  # Wednesday
            "2025-09-05": ["09:00", "11:30", "14:00"]   # Friday
        })
        return stub
    
    @pytest.fixture
    def kudosity_stub(self):
        return KudosityStub()
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(
            first_name="John",
            last_name="Doe",
            phone="+61412345678"
        )
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.mark.asyncio
    async def test_case_1_clear_date_time_booking(self, conversation_harness, test_lead, andy_assertions, frozen_time):
        """
        Case 1: Clear date & time booking
        User: "Can we meet on Friday at 2pm?"
        Expected: Direct booking confirmation with DB writes
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "last_name": test_lead.last_name,
            "phone": test_lead.phone,
            "email": test_lead.email
        })
        
        turns = [
            ConversationTurn(
                user_message="Can we meet on Friday at 2pm?",
                expected_response_contains="Friday",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"],
                should_cancel_followup=True
            ),
            ConversationTurn(
                user_message="Yes, that works perfectly",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Verify booking was created
        assert len(results) == 2
        assert results[1].success is True
        
        # Verify no emojis in responses
        for result in results:
            andy_assertions.assert_no_emojis(result.agent_response)
        
        # Verify timezone handling - response should show local time
        booking_response = results[1].agent_response
        assert "PM" in booking_response  # Should show AM/PM format
        assert "Friday" in booking_response  # Should show day name
    
    @pytest.mark.asyncio
    async def test_case_2_date_range_disambiguation(self, conversation_harness, test_lead, frozen_time):
        """
        Case 2: Range of dates with disambiguation
        User: "I'm available Monday through Wednesday"
        Expected: Show options for each day
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I'm available Monday through Wednesday",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Monday at 1pm sounds good",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        assert results[0].success is True
        assert "Monday" in results[0].agent_response
        assert "Wednesday" in results[0].agent_response
    
    @pytest.mark.asyncio
    async def test_case_3_time_only_then_date(self, conversation_harness, test_lead, frozen_time):
        """
        Case 3: Time-only -> asks for date
        User: "2pm works for me"
        Expected: Ask for date, then show slots
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="2pm works for me",
                expected_response_contains="which day",
                expected_fsm_state=MeetingState.COLLECT_DAY,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Tomorrow",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Perfect, book it",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 3
        assert all(result.success for result in results)
        
        # First response should ask for day
        assert "day" in results[0].agent_response.lower()
        
        # Final response should confirm booking with time
        assert "2" in results[2].agent_response  # Should mention 2pm
    
    @pytest.mark.asyncio
    async def test_case_4_anytime_tomorrow(self, conversation_harness, test_lead, frozen_time):
        """
        Case 4: 'Anytime tomorrow' resolves to 2025-08-30 slots
        User: "Anytime tomorrow works"
        Expected: Show available slots for 2025-08-30
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Anytime tomorrow works",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="The first one looks good",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        
        # Should show tomorrow's date (August 30)
        first_response = results[0].agent_response
        assert "August 30" in first_response or "30" in first_response
        
        # Should show available times
        assert any(time in first_response for time in ["9:30", "12:00", "3:00"])
    
    @pytest.mark.asyncio
    async def test_case_5_availability_question(self, conversation_harness, test_lead, frozen_time):
        """
        Case 5: 'What do you have on Monday' -> Monday = 2025-09-01
        User: "What times do you have available on Monday?"
        Expected: Show Monday slots
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="What times do you have available on Monday?",
                expected_response_contains="Monday",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="1pm works",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        
        # Should show Monday's availability
        monday_response = results[0].agent_response
        assert "Monday" in monday_response
        
        # Should show available times for Monday (10:00, 13:00, 16:30)
        assert any(time in monday_response for time in ["10:00", "1:00", "4:30"])
    
    @pytest.mark.asyncio
    async def test_case_6_date_only_then_time(self, conversation_harness, test_lead, frozen_time):
        """
        Case 6: Date-only -> shows slots -> confirm
        User: "How about Friday?"
        Expected: Show Friday slots, then book
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="How about Friday?",
                expected_response_contains="Friday",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="2pm looks perfect",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        
        # Should show Friday's availability
        friday_response = results[0].agent_response
        assert "Friday" in friday_response
        
        # Should show available times for Friday
        assert any(time in friday_response for time in ["9:00", "11:30", "2:00"])
    
    @pytest.mark.asyncio
    async def test_case_9_next_week_options(self, conversation_harness, test_lead, frozen_time):
        """
        Case 9: 'Next week' day options and booking
        User: "Next week would be better"
        Expected: Show next week options
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Next week would be better",
                expected_response_contains="next week",
                expected_fsm_state=MeetingState.COLLECT_DAY,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Wednesday at 1pm",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        
        # Should mention next week
        next_week_response = results[0].agent_response
        assert "next week" in next_week_response.lower() or "week" in next_week_response.lower()
    
    @pytest.mark.asyncio
    async def test_case_10_user_defers(self, conversation_harness, test_lead, frozen_time):
        """
        Case 10: User defers -> context parked; no booking
        User: "Let me think about it"
        Expected: Acknowledge, keep context available
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="What times do you have tomorrow?",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Let me think about it",
                expected_response_contains="no problem",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,  # Context preserved
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        
        # Should acknowledge deferral politely
        defer_response = results[1].agent_response
        assert any(phrase in defer_response.lower() 
                  for phrase in ["no problem", "take your time", "ready", "let me know"])
        
        # Should NOT create booking
        assert "bookings_created" not in results[1].db_changes
    
    @pytest.mark.asyncio
    async def test_case_11_holiday_no_slots_alternative(self, conversation_harness, test_lead, zoho_stub, frozen_time):
        """
        Case 11: Holiday/no slots -> propose next open day -> booking
        User: "How about Saturday?" (weekend/closed)
        Expected: Suggest alternative day
        """
        # Saturday is in holidays list
        zoho_stub.holidays.append("2025-08-30")  # Make Saturday unavailable
        
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="How about Saturday?",
                expected_response_contains="available",  # Should suggest alternatives
                expected_fsm_state=MeetingState.COLLECT_DAY,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Monday works then",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 2
        
        # Should suggest alternative since Saturday is unavailable
        saturday_response = results[0].agent_response
        assert any(word in saturday_response.lower() 
                  for word in ["monday", "tuesday", "wednesday", "available", "other"])


@pytest.mark.integration
class TestMeetingBookingTimezones:
    """Test timezone handling in meeting bookings."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61412345678")
    
    @pytest.mark.asyncio
    async def test_timezone_display_in_confirmations(self, conversation_harness, test_lead, andy_assertions, frozen_time):
        """Test that confirmations show proper timezone formatting."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Can we meet tomorrow at 2pm?",
                expected_response_contains="2:00 PM",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        response = results[0].agent_response
        
        # Should show proper AM/PM format
        assert "PM" in response or "AM" in response
        
        # Should show month name
        assert "August" in response or "September" in response
        
        # Should show day name
        assert any(day in response for day in ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"])
    
    @pytest.mark.asyncio
    async def test_utc_storage_in_database(self, conversation_harness, test_lead, db_session, andy_assertions, frozen_time):
        """Test that booking times are stored in UTC in database."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Tomorrow at 2pm",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM
            ),
            ConversationTurn(
                user_message="Yes, book it",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Check that booking was created with UTC timestamp
        from app.models.booking import Booking
        from sqlalchemy import select
        
        result = await db_session.execute(select(Booking).where(Booking.lead_id == test_lead.id))
        booking = result.scalar_one_or_none()
        
        assert booking is not None
        andy_assertions.assert_utc_storage(booking.start_time)
        andy_assertions.assert_utc_storage(booking.created_at)


@pytest.mark.integration
class TestMeetingBookingEdgeCases:
    """Test edge cases in meeting booking."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61412345678")
    
    @pytest.mark.asyncio
    async def test_booking_failure_recovery(self, conversation_harness, test_lead, meeting_agent_mocks, frozen_time):
        """Test recovery when booking fails."""
        # Configure Zoho to fail booking
        meeting_agent_mocks['zoho'].set_failure_mode(booking=True, reason="Slot no longer available")
        
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Tomorrow at 2pm",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM
            ),
            ConversationTurn(
                user_message="Yes, book it",
                expected_response_contains="trouble",  # Should indicate issue
                expected_fsm_state=MeetingState.COLLECT_DAY  # Should retry
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should handle booking failure gracefully
        failure_response = results[1].agent_response
        assert any(word in failure_response.lower() 
                  for word in ["trouble", "issue", "try", "other", "available"])
    
    @pytest.mark.asyncio
    async def test_ambiguous_time_clarification(self, conversation_harness, test_lead, frozen_time):
        """Test clarification for ambiguous time expressions."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Can we meet sometime in the evening?",
                expected_response_contains="evening",
                expected_fsm_state=MeetingState.COLLECT_TIME
            ),
            ConversationTurn(
                user_message="6pm would be perfect",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should ask for clarification on vague time
        evening_response = results[0].agent_response
        assert any(word in evening_response.lower() 
                  for word in ["time", "specific", "prefer", "evening"])
