"""
Test Suite for Meeting Agent
Comprehensive tests for FSM, NLU, timezone handling, and all 11 meeting booking cases
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

from app.meeting_agent.agent import MeetingAgent
from app.meeting_agent.fsm import MeetingBookingFSM, MeetingState, MeetingContext
from app.meeting_agent.nlu import MeetingNLU, MeetingIntent
from app.meeting_agent.timezone_utils import TimezoneHandler
from app.meeting_agent.case_handlers import MeetingCaseHandlers
from app.services.zoho_bookings_service import BookingSlot, BookingResult


class TestMeetingAgentFSM:
    """Test the Meeting Booking FSM"""
    
    @pytest.fixture
    def fsm(self):
        """Create FSM instance with mock Redis"""
        mock_redis = Mock()
        return MeetingBookingFSM(mock_redis)
    
    @pytest.fixture
    def sample_context(self):
        """Create sample meeting context"""
        return MeetingContext(
            lead_id="test-lead-123",
            phone_number="+1234567890",
            session_id="test-session-123"
        )
    
    def test_fsm_initialization(self, fsm):
        """Test FSM initialization"""
        assert fsm is not None
        assert MeetingState.IDLE in fsm.transitions
        assert MeetingState.COLLECT_DAY in fsm.transitions[MeetingState.IDLE]
    
    def test_context_creation(self, sample_context):
        """Test meeting context creation"""
        assert sample_context.lead_id == "test-lead-123"
        assert sample_context.phone_number == "+1234567890"
        assert sample_context.current_state == MeetingState.IDLE
        assert sample_context.clarification_count == 0
    
    def test_state_transitions(self, fsm, sample_context):
        """Test valid state transitions"""
        # Test valid transition
        result = fsm.transition_to(sample_context, MeetingState.COLLECT_DAY)
        assert result is True
        assert sample_context.current_state == MeetingState.COLLECT_DAY
        
        # Test invalid transition
        result = fsm.transition_to(sample_context, MeetingState.BOOKED)
        assert result is False
    
    def test_clarification_tracking(self, sample_context):
        """Test clarification count tracking"""
        assert sample_context.clarification_count == 0
        
        # Increment clarifications
        exceeded = sample_context.increment_clarification()
        assert sample_context.clarification_count == 1
        assert exceeded is False
        
        # Exceed limit
        sample_context.max_clarifications = 1
        exceeded = sample_context.increment_clarification()
        assert exceeded is True
    
    def test_context_expiry(self, sample_context):
        """Test context expiry logic"""
        # Fresh context should not be expired
        assert sample_context.is_expired(timeout_seconds=3600) is False
        
        # Manually set old timestamp
        old_time = (datetime.utcnow() - timedelta(hours=2)).isoformat()
        sample_context.updated_at = old_time
        
        # Should be expired with 1 hour timeout
        assert sample_context.is_expired(timeout_seconds=3600) is True


class TestMeetingAgentNLU:
    """Test the Meeting NLU component"""
    
    @pytest.fixture
    def nlu(self):
        """Create NLU instance"""
        return MeetingNLU()
    
    def test_meeting_related_detection(self, nlu):
        """Test meeting-related message detection"""
        # Meeting-related messages
        assert nlu.is_meeting_related("I'd like to schedule a meeting") is True
        assert nlu.is_meeting_related("Can we meet tomorrow?") is True
        assert nlu.is_meeting_related("What times are available?") is True
        
        # Non-meeting messages
        assert nlu.is_meeting_related("Hello there") is False
        assert nlu.is_meeting_related("Tell me about your services") is False
    
    def test_confirmation_intent_extraction(self, nlu):
        """Test confirmation intent extraction"""
        # Confirmation messages
        assert nlu.extract_confirmation_intent("Yes, that works") is True
        assert nlu.extract_confirmation_intent("Sounds good") is True
        assert nlu.extract_confirmation_intent("Perfect") is True
        
        # Non-confirmation messages
        assert nlu.extract_confirmation_intent("No, that doesn't work") is False
        assert nlu.extract_confirmation_intent("Maybe later") is False
    
    def test_rejection_intent_extraction(self, nlu):
        """Test rejection intent extraction"""
        # Rejection messages
        assert nlu.extract_rejection_intent("No, that doesn't work") is True
        assert nlu.extract_rejection_intent("Different time please") is True
        
        # Non-rejection messages
        assert nlu.extract_rejection_intent("Yes, that's perfect") is False
    
    @pytest.mark.asyncio
    async def test_message_analysis(self, nlu):
        """Test full message analysis"""
        with patch.object(nlu.llm, 'ainvoke') as mock_llm:
            # Mock OpenAI response
            mock_response = Mock()
            mock_response.content = '''
            {
                "intent": "schedule",
                "confidence": 0.9,
                "extracted_slots": {
                    "date_preference": "tomorrow",
                    "time_preference": "2pm"
                },
                "reasoning": "User wants to schedule for tomorrow at 2pm",
                "suggested_response": "Great! Tomorrow at 2pm works."
            }
            '''
            mock_llm.return_value = mock_response
            
            result = await nlu.analyze_message("Can we meet tomorrow at 2pm?")
            
            assert result["intent"] == "schedule"
            assert result["confidence"] == 0.9
            assert "date_preference" in result["extracted_slots"]
            assert "time_preference" in result["extracted_slots"]


class TestTimezoneHandler:
    """Test timezone handling utilities"""
    
    @pytest.fixture
    def tz_handler(self):
        """Create timezone handler"""
        return TimezoneHandler()
    
    @pytest.mark.asyncio
    async def test_date_expression_parsing(self, tz_handler):
        """Test natural language date parsing"""
        # Test "tomorrow"
        result = await tz_handler.parse_date_expression("tomorrow")
        assert result is not None
        assert result["is_relative"] is True
        
        # Test "next Friday"
        result = await tz_handler.parse_date_expression("next Friday")
        assert result is not None
        assert "Friday" in result["weekday"]
        
        # Test "next week"
        result = await tz_handler.parse_date_expression("next week")
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_time_expression_parsing(self, tz_handler):
        """Test natural language time parsing"""
        # Test specific time
        result = await tz_handler.parse_time_expression("2pm")
        assert result is not None
        assert result["is_specific"] is True
        
        # Test time period
        result = await tz_handler.parse_time_expression("morning")
        assert result is not None
        assert result["time_period"] == "morning"
        
        # Test "anytime"
        result = await tz_handler.parse_time_expression("anytime")
        assert result is not None
        assert result["is_flexible"] is True
    
    def test_timezone_conversion(self, tz_handler):
        """Test timezone conversion utilities"""
        # Test UTC conversion
        local_dt = datetime(2024, 3, 15, 14, 30)  # 2:30 PM
        utc_dt = tz_handler.to_utc(local_dt)
        assert utc_dt.tzinfo is not None
        
        # Test local conversion
        utc_dt = datetime(2024, 3, 15, 9, 0)  # 9:00 AM UTC
        local_dt = tz_handler.to_local(utc_dt, "Asia/Kolkata")
        assert local_dt.tzinfo is not None
    
    def test_business_hours_check(self, tz_handler):
        """Test business hours validation"""
        # Business day, business hours
        business_dt = datetime(2024, 3, 15, 10, 0)  # Friday 10 AM
        assert tz_handler.is_business_hours(business_dt) is True
        
        # Weekend
        weekend_dt = datetime(2024, 3, 16, 10, 0)  # Saturday 10 AM
        assert tz_handler.is_business_hours(weekend_dt) is False
        
        # After hours
        after_hours_dt = datetime(2024, 3, 15, 18, 0)  # Friday 6 PM
        assert tz_handler.is_business_hours(after_hours_dt) is False


class TestMeetingCaseHandlers:
    """Test the 11 meeting booking cases"""
    
    @pytest.fixture
    def case_handlers(self):
        """Create case handlers with mocked services"""
        mock_zoho = Mock()
        mock_tz_handler = Mock()
        return MeetingCaseHandlers(mock_zoho, mock_tz_handler)
    
    @pytest.fixture
    def sample_context(self):
        """Create sample context for testing"""
        return MeetingContext(
            lead_id="test-lead-123",
            phone_number="+1234567890",
            session_id="test-session-123",
            timezone="Asia/Kolkata"
        )
    
    @pytest.mark.asyncio
    async def test_case_1_clear_date_time(self, case_handlers, sample_context):
        """Test Case 1: Clear date & time"""
        analysis = {
            "intent": "schedule",
            "extracted_slots": {
                "parsed_date": {"date": "2024-03-15", "formatted": "March 15, 2024"},
                "parsed_time": {"time_str": "14:00", "formatted": "2:00 PM"}
            },
            "message": "Can we meet on March 15 at 2pm?"
        }
        
        # Mock timezone handler
        case_handlers.timezone_handler.combine_date_time = Mock(return_value=datetime(2024, 3, 15, 14, 0))
        case_handlers.timezone_handler.to_utc = Mock(return_value=datetime(2024, 3, 15, 8, 30))
        case_handlers.timezone_handler.format_datetime_local = Mock(return_value="Friday, March 15 at 2:00 PM")
        
        # Mock available slots
        case_handlers._get_slots_near_time = AsyncMock(return_value=[
            {"formatted_time": "Friday, March 15 at 2:00 PM", "staff_name": "John"}
        ])
        
        result = await case_handlers.handle_case_1_clear_date_time(sample_context, analysis)
        
        assert result["handled"] is True
        assert "2:00 PM" in result["response"]
        assert sample_context.target_date_local == "2024-03-15"
    
    @pytest.mark.asyncio
    async def test_case_3_time_only(self, case_handlers, sample_context):
        """Test Case 3: Time-only"""
        analysis = {
            "intent": "schedule",
            "extracted_slots": {
                "parsed_time": {"time_str": "14:00", "formatted": "2:00 PM"}
            },
            "message": "2pm works for me"
        }
        
        result = await case_handlers.handle_case_3_time_only(sample_context, analysis)
        
        assert result["handled"] is True
        assert "2:00 PM" in result["response"]
        assert "Which day" in result["response"]
        assert sample_context.target_time_local == "14:00"
    
    @pytest.mark.asyncio
    async def test_case_4_anytime(self, case_handlers, sample_context):
        """Test Case 4: Anytime"""
        analysis = {
            "intent": "schedule",
            "extracted_slots": {},
            "message": "I'm flexible, anytime works"
        }
        
        # Mock Zoho service
        mock_slots = [
            Mock(staff_id="1", staff_name="John", start_time=datetime(2024, 3, 15, 10, 0),
                 end_time=datetime(2024, 3, 15, 10, 30), service_id="service1", duration_minutes=30)
        ]
        case_handlers.zoho_service.get_next_available_business_slots = AsyncMock(return_value=mock_slots)
        case_handlers.zoho_service.format_slot_for_display = Mock(return_value="Friday, March 15 at 10:00 AM")
        
        result = await case_handlers.handle_case_4_anytime(sample_context, analysis)
        
        assert result["handled"] is True
        assert "available" in result["response"].lower()
        assert len(sample_context.candidate_slots_local) > 0
    
    @pytest.mark.asyncio
    async def test_case_6_date_only(self, case_handlers, sample_context):
        """Test Case 6: Date-only"""
        analysis = {
            "intent": "schedule",
            "extracted_slots": {
                "parsed_date": {"date": "2024-03-15", "formatted": "March 15, 2024"}
            },
            "message": "How about Friday?"
        }
        
        result = await case_handlers.handle_case_6_date_only(sample_context, analysis)
        
        assert result["handled"] is True
        assert "March 15, 2024" in result["response"]
        assert "What time" in result["response"]
        assert sample_context.target_date_local == "2024-03-15"
    
    @pytest.mark.asyncio
    async def test_case_8_user_defers(self, case_handlers, sample_context):
        """Test Case 8: User defers confirmation"""
        analysis = {
            "intent": "defer",
            "extracted_slots": {},
            "message": "Let me think about it"
        }
        
        result = await case_handlers.handle_case_8_user_defers(sample_context, analysis)
        
        assert result["handled"] is True
        assert "no problem" in result["response"].lower()
        assert "ready" in result["response"].lower()


class TestMeetingAgentIntegration:
    """Integration tests for the complete meeting agent"""
    
    @pytest.fixture
    def meeting_agent(self):
        """Create meeting agent with mocked dependencies"""
        with patch('app.meeting_agent.agent.get_redis_client'), \
             patch('app.meeting_agent.agent.ZohoBookingsService'):
            agent = MeetingAgent()
            agent.enabled = True
            return agent
    
    @pytest.mark.asyncio
    async def test_meeting_agent_disabled(self):
        """Test meeting agent when disabled"""
        with patch('app.core.config.settings.settings') as mock_settings:
            mock_settings.MEETING_AGENT_ENABLED = False
            
            agent = MeetingAgent()
            result = await agent.process_message("I'd like to schedule a meeting", "+1234567890", "lead-123")
            
            assert result["handled"] is False
            assert "disabled" in result["reason"]
    
    @pytest.mark.asyncio
    async def test_non_meeting_message(self, meeting_agent):
        """Test non-meeting related message"""
        result = await meeting_agent.process_message("Hello there", "+1234567890", "lead-123")
        
        assert result["handled"] is False
        assert "Not meeting-related" in result["reason"]
    
    @pytest.mark.asyncio
    async def test_loop_prevention(self, meeting_agent):
        """Test loop prevention mechanism"""
        # Mock context with high clarification count
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        mock_context.clarification_count = 5  # Exceed limit
        
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        
        result = await meeting_agent.process_message("I want to meet", "+1234567890", "lead-123")
        
        assert result["handled"] is False
        assert "Loop prevention" in result["reason"]


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
