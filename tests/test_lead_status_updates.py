"""
Comprehensive test suite for lead status update functionality
"""

import pytest
import uuid
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient
from datetime import datetime

from app.services.lead_status_normalization_service import LeadStatusNormalizationService
from app.services.lead_status_classification_service import (
    LeadStatusClassificationService, 
    StatusContext,
    ClassificationResult
)
from app.services.lead_status_update_service import LeadStatusUpdateService, StatusUpdateResult
from app.models.lead import Lead
from app.models.lead_reference import LeadStatus, LeadStatusHistory
from app.core.exceptions import NotFoundError, CustomValidationError


class TestLeadStatusNormalizationService:
    """Test the status normalization service"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.normalizer = LeadStatusNormalizationService()
    
    def test_exact_canonical_match(self):
        """Test exact canonical status matching"""
        result, confidence = self.normalizer.normalize_status_name("New Lead")
        assert result == "New Lead"
        assert confidence == 1.0
    
    def test_synonym_mapping(self):
        """Test synonym to canonical mapping"""
        test_cases = [
            ("new", "New Lead"),
            ("qualified", "Qualified"),
            ("not qualified", "Unqualified"),
            ("callback", "Call Back"),
            ("wrong_number", "Wrong Number"),
            ("franchise_sold", "Franchise Sold")
        ]
        
        for synonym, expected in test_cases:
            result, confidence = self.normalizer.normalize_status_name(synonym)
            assert result == expected
            assert confidence == 1.0
    
    def test_case_insensitive_matching(self):
        """Test case insensitive matching"""
        test_cases = [
            ("NEW LEAD", "New Lead"),
            ("not interested", "Not Interested"),
            ("QUALIFIED", "Qualified")
        ]
        
        for input_name, expected in test_cases:
            result, confidence = self.normalizer.normalize_status_name(input_name)
            assert result == expected
            assert confidence == 1.0
    
    def test_fuzzy_matching(self):
        """Test fuzzy string matching"""
        # Test close matches that should work
        result, confidence = self.normalizer.normalize_status_name("New Leads")
        assert confidence >= 0.92  # Should meet threshold
        
        # Test very different strings that shouldn't match
        result, confidence = self.normalizer.normalize_status_name("xyz123")
        assert confidence < 0.92  # Should not meet threshold
    
    def test_empty_input(self):
        """Test handling of empty input"""
        result, confidence = self.normalizer.normalize_status_name("")
        assert confidence == 0.0
        
        result, confidence = self.normalizer.normalize_status_name("   ")
        assert confidence == 0.0
    
    def test_validation(self):
        """Test status validation"""
        # Valid status
        is_valid, feedback = self.normalizer.validate_status_name("Qualified")
        assert is_valid
        assert "Valid status" in feedback
        
        # Invalid status
        is_valid, feedback = self.normalizer.validate_status_name("xyz123")
        assert not is_valid
        assert "No suitable canonical status found" in feedback
    
    def test_add_synonym(self):
        """Test adding new synonyms"""
        # Add valid synonym
        success = self.normalizer.add_synonym("interested", "Qualified")
        assert success
        
        # Test the new synonym works
        result, confidence = self.normalizer.normalize_status_name("interested")
        assert result == "Qualified"
        assert confidence == 1.0
        
        # Try to add synonym for non-existent canonical
        success = self.normalizer.add_synonym("test", "NonExistent")
        assert not success


class TestLeadStatusClassificationService:
    """Test the status classification service"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.classifier = LeadStatusClassificationService()
    
    def test_rule_based_not_interested(self):
        """Test rule-based classification for 'Not Interested'"""
        test_messages = [
            "Not interested, please stop messaging.",
            "I don't want this, remove me from your list",
            "Stop contacting me, not interested",
            "No thanks, not for me"
        ]
        
        for message in test_messages:
            result = self.classifier._classify_with_rules(message)
            assert result.label == "Not Interested"
            assert result.confidence >= 0.8
            assert result.method == "rule_based"
    
    def test_rule_based_wrong_number(self):
        """Test rule-based classification for 'Wrong Number'"""
        test_messages = [
            "This is the wrong number",
            "You have the wrong person",
            "I never inquired about this",
            "This is not me, wrong number"
        ]
        
        for message in test_messages:
            result = self.classifier._classify_with_rules(message)
            assert result.label == "Wrong Number"
            assert result.confidence >= 0.8
    
    def test_rule_based_call_back(self):
        """Test rule-based classification for 'Call Back'"""
        test_messages = [
            "Call me later, I'm busy now",
            "Can you text me tomorrow?",
            "I'm at work, call me after 5pm",
            "Not now, maybe next week"
        ]
        
        for message in test_messages:
            result = self.classifier._classify_with_rules(message)
            assert result.label == "Call Back"
            assert result.confidence >= 0.8
    
    def test_rule_based_follow_up_required(self):
        """Test rule-based classification for 'Follow up Required'"""
        test_messages = [
            "Send me more details please",
            "Can you email me the information?",
            "I'll check this out, send me more info",
            "What's the pricing like?"
        ]
        
        for message in test_messages:
            result = self.classifier._classify_with_rules(message)
            assert result.label == "Follow up Required"
            assert result.confidence >= 0.8
    
    def test_rule_based_out_of_budget(self):
        """Test rule-based classification for 'Out of Budget'"""
        test_messages = [
            "Too expensive for me",
            "Can't afford this right now",
            "Out of my budget range",
            "Looking for something cheaper"
        ]
        
        for message in test_messages:
            result = self.classifier._classify_with_rules(message)
            assert result.label == "Out of Budget"
            assert result.confidence >= 0.8
    
    def test_rule_based_precedence(self):
        """Test status precedence when multiple rules match"""
        # Message that could match multiple patterns
        message = "Not interested and wrong number"
        result = self.classifier._classify_with_rules(message)
        
        # Should pick "Not Interested" due to higher precedence
        assert result.label == "Not Interested"
        assert result.confidence >= 0.8
    
    def test_no_rule_match(self):
        """Test handling when no rules match"""
        message = "Hello there, how are you?"
        result = self.classifier._classify_with_rules(message)
        
        assert result.label == "Follow up Required"
        assert result.confidence == 0.2
        assert result.method == "rule_based"
    
    @pytest.mark.asyncio
    async def test_llm_response_parsing(self):
        """Test parsing of LLM responses"""
        # Valid response format
        response_text = "Label: Qualified • Confidence: 0.85 • Rationale: Lead shows strong interest"
        result = self.classifier._parse_llm_response(response_text)
        
        assert result.label == "Qualified"
        assert result.confidence == 0.85
        assert "strong interest" in result.rationale
        assert result.method == "llm"
    
    def test_llm_response_parsing_invalid_format(self):
        """Test handling of invalid LLM response format"""
        response_text = "Invalid response format"
        result = self.classifier._parse_llm_response(response_text)
        
        assert result.label == "Follow up Required"
        assert result.confidence == 0.3
        assert "Failed to parse" in result.rationale
    
    @pytest.mark.asyncio
    async def test_empty_message_classification(self):
        """Test classification of empty messages"""
        result = await self.classifier.classify_message("")
        
        assert result.label == "New Lead"
        assert result.confidence == 0.0
        assert "Empty message" in result.rationale
        assert result.method == "rule_based"


class TestLeadStatusUpdateService:
    """Test the status update service"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.mock_db = Mock(spec=AsyncSession)
        self.service = LeadStatusUpdateService(self.mock_db)
    
    @pytest.mark.asyncio
    async def test_invalid_lead_id_format(self):
        """Test handling of invalid lead ID format"""
        with pytest.raises(CustomValidationError) as exc_info:
            await self.service.update_status_from_message(
                lead_id="invalid-uuid",
                message_text="Test message"
            )
        
        assert "Invalid Lead ID" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_lead_not_found(self):
        """Test handling when lead is not found"""
        # Mock the database to return None for the lead
        self.mock_db.begin = AsyncMock()
        self.mock_db.begin.return_value.__aenter__ = AsyncMock()
        self.mock_db.begin.return_value.__aexit__ = AsyncMock()
        
        with patch.object(self.service, '_lock_lead', return_value=None):
            with pytest.raises(NotFoundError) as exc_info:
                await self.service.update_status_from_message(
                    lead_id=str(uuid.uuid4()),
                    message_text="Test message"
                )
            
            assert "No lead found" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_status_unchanged_idempotency(self):
        """Test idempotency when status doesn't change"""
        lead_id = str(uuid.uuid4())
        status_id = uuid.uuid4()
        
        # Mock lead with current status
        mock_lead = Mock()
        mock_lead.lead_status_id = status_id
        mock_lead.lead_status_rel = Mock()
        mock_lead.lead_status_rel.name = "Qualified"
        
        # Mock classification result
        mock_classification = ClassificationResult(
            label="Qualified",
            confidence=0.9,
            rationale="Test rationale",
            method="rule_based"
        )
        
        self.mock_db.begin = AsyncMock()
        self.mock_db.begin.return_value.__aenter__ = AsyncMock()
        self.mock_db.begin.return_value.__aexit__ = AsyncMock()
        
        with patch.object(self.service, '_lock_lead', return_value=mock_lead), \
             patch.object(self.service, '_get_status_id', return_value=status_id), \
             patch('app.services.lead_status_update_service.lead_classifier.classify_message', 
                   return_value=mock_classification):
            
            result = await self.service.update_status_from_message(
                lead_id=lead_id,
                message_text="I'm still qualified"
            )
            
            assert not result.changed
            assert result.old_status == "Qualified"
            assert result.new_status == "Qualified"
            assert result.confidence == 0.9
    
    def test_get_status_history_invalid_id(self):
        """Test get_status_history with invalid lead ID"""
        with pytest.raises(CustomValidationError) as exc_info:
            # This would be async in real usage, but testing the validation
            try:
                uuid.UUID("invalid-uuid")
            except ValueError:
                raise CustomValidationError(
                    title="Invalid Lead ID",
                    description="Lead ID must be a valid UUID: invalid-uuid"
                )
        
        assert "Invalid Lead ID" in str(exc_info.value)


class TestAPIEndpoints:
    """Test the API endpoints"""
    
    def setup_method(self):
        """Setup test fixtures"""
        # This would require a proper test client setup
        # For now, we'll test the core logic
        pass
    
    def test_status_from_message_request_validation(self):
        """Test request validation for status from message endpoint"""
        from app.schemas.lead_status import StatusFromMessageRequest
        
        # Valid request
        valid_request = StatusFromMessageRequest(
            message="Not interested",
            channel="sms"
        )
        assert valid_request.message == "Not interested"
        assert valid_request.channel == "sms"
        
        # Test minimum length validation would be handled by Pydantic
        with pytest.raises(ValueError):
            StatusFromMessageRequest(message="")  # Too short
    
    def test_manual_status_update_request_validation(self):
        """Test request validation for manual status update"""
        from app.schemas.lead_status import ManualStatusUpdateRequest
        
        # Valid request
        valid_request = ManualStatusUpdateRequest(
            status_name="Qualified",
            reason="Lead showed strong interest"
        )
        assert valid_request.status_name == "Qualified"
        assert valid_request.reason == "Lead showed strong interest"


class TestIntegrationScenarios:
    """Test end-to-end integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_complete_status_update_flow(self):
        """Test complete flow from message to status update"""
        # This would require database setup and mocking
        # Testing the conceptual flow
        
        # 1. Message comes in: "Not interested, please stop"
        # 2. Classification service identifies it as "Not Interested" 
        # 3. Normalization service confirms canonical name
        # 4. Update service changes status atomically
        # 5. History record is created
        # 6. API returns success response
        
        # For now, we verify the components work together
        normalizer = LeadStatusNormalizationService()
        classifier = LeadStatusClassificationService()
        
        # Test the flow conceptually
        message = "Not interested, please stop messaging"
        
        # Classification
        classification_result = classifier._classify_with_rules(message)
        assert classification_result.label == "Not Interested"
        
        # Normalization
        normalized, confidence = normalizer.normalize_status_name(classification_result.label)
        assert normalized == "Not Interested"
        assert confidence == 1.0
    
    def test_acceptance_criteria_scenarios(self):
        """Test all acceptance criteria scenarios"""
        classifier = LeadStatusClassificationService()
        
        # Scenario 1: "Not interested, please stop" -> Not Interested
        result = classifier._classify_with_rules("Not interested, please stop")
        assert result.label == "Not Interested"
        assert result.confidence >= 0.8
        
        # Scenario 2: "call me next week" -> Call Back  
        result = classifier._classify_with_rules("call me next week")
        assert result.label == "Call Back"
        assert result.confidence >= 0.8
        
        # Scenario 3: "wrong number" -> Wrong Number
        result = classifier._classify_with_rules("wrong number")
        assert result.label == "Wrong Number"
        assert result.confidence >= 0.8
        
        # Scenario 4: "send eoi" -> EOI/NDA Sent
        result = classifier._classify_with_rules("send eoi")
        assert result.label == "EOI/NDA Sent"
        assert result.confidence >= 0.8


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
