"""
Andy Complete Stages Test Suite
Tests that <PERSON> goes through all conversation stages completely and finishes them successfully
"""

import pytest
import asyncio
import uuid
from datetime import datetime
from unittest.mock import patch, AsyncMock, <PERSON>ck
from typing import Dict, Any, List

import pytest_asyncio

from app.agents.sms_assistant import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_andy_sms_assistant, <PERSON><PERSON><PERSON><PERSON>flowStages
from app.core.memory.sms_memory import get_sms_memory_manager


@pytest.mark.asyncio
class TestAndyCompleteStages:
    """Test Andy's complete conversation flow through all stages"""

    def setup_method(self):
        """Setup for each test method"""
        self.test_phone = "+61412345678"
        self.test_lead_id = str(uuid.uuid4())
        self.andy = get_andy_sms_assistant()
        self.memory_manager = get_sms_memory_manager()

    async def test_complete_conversation_flow(self):
        """Test Andy goes through all stages from start to finish"""
        print("\n🎯 TESTING COMPLETE ANDY CONVERSATION FLOW")
        print("=" * 60)
        
        # Stage 1: Introduction
        print("\n📍 STAGE 1: INTRODUCTION")
        result1 = await self._test_introduction_stage()
        assert result1["success"], f"Introduction stage failed: {result1.get('error')}"
        print(f"✅ Introduction completed: {result1['current_stage']}")
        
        # Stage 2: Work Background Qualification
        print("\n📍 STAGE 2: WORK BACKGROUND QUALIFICATION")
        result2 = await self._test_work_background_stage()
        assert result2["success"], f"Work background stage failed: {result2.get('error')}"
        print(f"✅ Work background completed: {result2['current_stage']}")
        
        # Stage 3: Motivation Qualification
        print("\n📍 STAGE 3: MOTIVATION QUALIFICATION")
        result3 = await self._test_motivation_stage()
        assert result3["success"], f"Motivation stage failed: {result3.get('error')}"
        print(f"✅ Motivation completed: {result3['current_stage']}")
        
        # Stage 4: Budget Qualification
        print("\n📍 STAGE 4: BUDGET QUALIFICATION")
        result4 = await self._test_budget_stage()
        assert result4["success"], f"Budget stage failed: {result4.get('error')}"
        print(f"✅ Budget completed: {result4['current_stage']}")
        
        # Stage 5: Meeting Scheduling
        print("\n📍 STAGE 5: MEETING SCHEDULING")
        result5 = await self._test_scheduling_stage()
        assert result5["success"], f"Scheduling stage failed: {result5.get('error')}"
        print(f"✅ Scheduling completed: {result5['current_stage']}")
        
        # Stage 6: Confirmation
        print("\n📍 STAGE 6: CONFIRMATION")
        result6 = await self._test_confirmation_stage()
        assert result6["success"], f"Confirmation stage failed: {result6.get('error')}"
        print(f"✅ Confirmation completed: {result6['current_stage']}")
        
        print("\n🎉 ALL STAGES COMPLETED SUCCESSFULLY!")
        print("=" * 60)

    async def _test_introduction_stage(self) -> Dict[str, Any]:
        """Test introduction stage completion"""
        try:
            # Clear any existing context (use Redis delete)
            try:
                context_key = f"sms_lead_context:{self.test_phone}"
                self.memory_manager.redis_client.delete(context_key)
            except Exception as e:
                print(f"⚠️ Could not clear context: {e}")
            
            # Send initial message
            result = await self.andy.process_sms(
                phone_number=self.test_phone,
                message="Hi, I'm interested in franchise opportunities",
                lead_id=self.test_lead_id
            )
            
            # Verify introduction response
            if result.get("success"):
                response = result.get("response", "")
                current_stage = result.get("current_stage", "")
                
                # Check for Andy introduction elements
                intro_indicators = ["Andy", "franchise", "opportunities", "help"]
                has_intro = any(indicator.lower() in response.lower() for indicator in intro_indicators)
                
                # Respond positively to move to next stage
                follow_up = await self.andy.process_sms(
                    phone_number=self.test_phone,
                    message="Yes, I'm interested. Tell me more.",
                    lead_id=self.test_lead_id
                )
                
                return {
                    "success": True,
                    "current_stage": follow_up.get("current_stage", current_stage),
                    "response": response,
                    "has_intro": has_intro,
                    "stage_completed": follow_up.get("current_stage") != AndyWorkflowStages.INTRODUCTION
                }
            else:
                return {"success": False, "error": result.get("error", "Unknown error")}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_work_background_stage(self) -> Dict[str, Any]:
        """Test work background qualification stage"""
        try:
            # Send work background response
            result = await self.andy.process_sms(
                phone_number=self.test_phone,
                message="I work in corporate finance and have 10 years of experience",
                lead_id=self.test_lead_id
            )
            
            if result.get("success"):
                current_stage = result.get("current_stage", "")
                response = result.get("response", "")
                
                # Check if Andy acknowledged work background and moved to next stage
                work_indicators = ["experience", "background", "corporate", "finance"]
                acknowledged = any(indicator.lower() in response.lower() for indicator in work_indicators)
                
                # Check if stage progressed
                stage_progressed = current_stage == AndyWorkflowStages.QUALIFICATION_2_MOTIVATION
                
                return {
                    "success": True,
                    "current_stage": current_stage,
                    "response": response,
                    "acknowledged": acknowledged,
                    "stage_progressed": stage_progressed
                }
            else:
                return {"success": False, "error": result.get("error", "Unknown error")}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_motivation_stage(self) -> Dict[str, Any]:
        """Test motivation qualification stage"""
        try:
            # Send motivation response
            result = await self.andy.process_sms(
                phone_number=self.test_phone,
                message="I want to be my own boss and build something for my family's future",
                lead_id=self.test_lead_id
            )
            
            if result.get("success"):
                current_stage = result.get("current_stage", "")
                response = result.get("response", "")
                
                # Check if Andy acknowledged motivation
                motivation_indicators = ["boss", "family", "future", "build", "own"]
                acknowledged = any(indicator.lower() in response.lower() for indicator in motivation_indicators)
                
                # Check if stage progressed to budget
                stage_progressed = current_stage == AndyWorkflowStages.QUALIFICATION_3_BUDGET
                
                return {
                    "success": True,
                    "current_stage": current_stage,
                    "response": response,
                    "acknowledged": acknowledged,
                    "stage_progressed": stage_progressed
                }
            else:
                return {"success": False, "error": result.get("error", "Unknown error")}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_budget_stage(self) -> Dict[str, Any]:
        """Test budget qualification stage"""
        try:
            # Send budget response
            result = await self.andy.process_sms(
                phone_number=self.test_phone,
                message="I have around $200k available for investment",
                lead_id=self.test_lead_id
            )
            
            if result.get("success"):
                current_stage = result.get("current_stage", "")
                response = result.get("response", "")
                
                # Check if Andy acknowledged budget
                budget_indicators = ["200k", "investment", "budget", "available"]
                acknowledged = any(indicator.lower() in response.lower() for indicator in budget_indicators)
                
                # Check if stage progressed to scheduling or fee breakdown
                stage_progressed = current_stage in [AndyWorkflowStages.SCHEDULING, AndyWorkflowStages.FRANCHISE_FEE_BREAKDOWN]
                
                return {
                    "success": True,
                    "current_stage": current_stage,
                    "response": response,
                    "acknowledged": acknowledged,
                    "stage_progressed": stage_progressed
                }
            else:
                return {"success": False, "error": result.get("error", "Unknown error")}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_scheduling_stage(self) -> Dict[str, Any]:
        """Test meeting scheduling stage"""
        try:
            # Express interest in meeting
            result = await self.andy.process_sms(
                phone_number=self.test_phone,
                message="Yes, I'd like to schedule a meeting to discuss this further",
                lead_id=self.test_lead_id
            )
            
            if result.get("success"):
                current_stage = result.get("current_stage", "")
                response = result.get("response", "")
                
                # Check if Andy offered meeting options
                meeting_indicators = ["meeting", "schedule", "available", "time", "book"]
                offered_meeting = any(indicator.lower() in response.lower() for indicator in meeting_indicators)
                
                # Respond with time preference
                time_result = await self.andy.process_sms(
                    phone_number=self.test_phone,
                    message="Tomorrow at 2pm would work well for me",
                    lead_id=self.test_lead_id
                )
                
                return {
                    "success": True,
                    "current_stage": time_result.get("current_stage", current_stage),
                    "response": response,
                    "offered_meeting": offered_meeting,
                    "time_response": time_result.get("response", "")
                }
            else:
                return {"success": False, "error": result.get("error", "Unknown error")}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_confirmation_stage(self) -> Dict[str, Any]:
        """Test confirmation stage"""
        try:
            # Confirm the meeting
            result = await self.andy.process_sms(
                phone_number=self.test_phone,
                message="Yes, that time works perfectly. Please confirm the booking.",
                lead_id=self.test_lead_id
            )
            
            if result.get("success"):
                current_stage = result.get("current_stage", "")
                response = result.get("response", "")
                
                # Check if Andy confirmed the meeting
                confirmation_indicators = ["confirmed", "booked", "looking forward", "see you"]
                confirmed = any(indicator.lower() in response.lower() for indicator in confirmation_indicators)
                
                # Check if conversation is complete
                conversation_complete = current_stage == AndyWorkflowStages.CONFIRMATION
                
                return {
                    "success": True,
                    "current_stage": current_stage,
                    "response": response,
                    "confirmed": confirmed,
                    "conversation_complete": conversation_complete
                }
            else:
                return {"success": False, "error": result.get("error", "Unknown error")}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def test_stage_completion_criteria(self):
        """Test that each stage has proper completion criteria"""
        print("\n🔍 TESTING STAGE COMPLETION CRITERIA")
        
        stages_to_test = [
            AndyWorkflowStages.INTRODUCTION,
            AndyWorkflowStages.QUALIFICATION_1_WORK,
            AndyWorkflowStages.QUALIFICATION_2_MOTIVATION,
            AndyWorkflowStages.QUALIFICATION_3_BUDGET,
            AndyWorkflowStages.SCHEDULING,
            AndyWorkflowStages.CONFIRMATION
        ]
        
        for stage in stages_to_test:
            completion_criteria = self._get_stage_completion_criteria(stage)
            print(f"✅ {stage}: {len(completion_criteria)} completion criteria defined")
            assert len(completion_criteria) > 0, f"No completion criteria defined for {stage}"

    def _get_stage_completion_criteria(self, stage: str) -> List[str]:
        """Get completion criteria for a specific stage"""
        criteria_map = {
            AndyWorkflowStages.INTRODUCTION: [
                "Lead responds positively",
                "Lead shows interest",
                "Lead asks questions"
            ],
            AndyWorkflowStages.QUALIFICATION_1_WORK: [
                "Work background provided",
                "Experience level identified",
                "Industry category determined"
            ],
            AndyWorkflowStages.QUALIFICATION_2_MOTIVATION: [
                "Motivation clearly stated",
                "Personal goals identified",
                "Commitment level assessed"
            ],
            AndyWorkflowStages.QUALIFICATION_3_BUDGET: [
                "Budget range provided",
                "Investment capacity confirmed",
                "Financial readiness assessed"
            ],
            AndyWorkflowStages.SCHEDULING: [
                "Meeting interest expressed",
                "Time preference provided",
                "Availability confirmed"
            ],
            AndyWorkflowStages.CONFIRMATION: [
                "Meeting confirmed",
                "Details acknowledged",
                "Next steps clear"
            ]
        }
        
        return criteria_map.get(stage, [])

    async def test_stage_progression_logic(self):
        """Test the logic that determines stage progression"""
        print("\n⚡ TESTING STAGE PROGRESSION LOGIC")
        
        # Test progression from introduction to qualification
        context = {"current_stage": AndyWorkflowStages.INTRODUCTION}
        message = "Yes, I'm interested in learning more"
        
        # This would normally be handled by Andy's workflow determination
        next_stage = self._determine_next_stage(context, message)
        print(f"✅ Introduction → {next_stage}")
        assert next_stage == AndyWorkflowStages.QUALIFICATION_1_WORK
        
        # Test progression through qualification stages
        stages_progression = [
            (AndyWorkflowStages.QUALIFICATION_1_WORK, "I work in IT", AndyWorkflowStages.QUALIFICATION_2_MOTIVATION),
            (AndyWorkflowStages.QUALIFICATION_2_MOTIVATION, "I want financial freedom", AndyWorkflowStages.QUALIFICATION_3_BUDGET),
            (AndyWorkflowStages.QUALIFICATION_3_BUDGET, "I have $150k available", AndyWorkflowStages.SCHEDULING)
        ]
        
        for current, message, expected in stages_progression:
            context = {"current_stage": current}
            next_stage = self._determine_next_stage(context, message)
            print(f"✅ {current} → {next_stage}")
            assert next_stage == expected, f"Expected {expected}, got {next_stage}"

    def _determine_next_stage(self, context: Dict[str, Any], message: str) -> str:
        """Simplified stage progression logic for testing"""
        current_stage = context.get("current_stage")
        message_lower = message.lower()
        
        if current_stage == AndyWorkflowStages.INTRODUCTION:
            if any(word in message_lower for word in ["yes", "interested", "tell me", "more"]):
                return AndyWorkflowStages.QUALIFICATION_1_WORK
                
        elif current_stage == AndyWorkflowStages.QUALIFICATION_1_WORK:
            if any(word in message_lower for word in ["work", "job", "experience", "career"]):
                return AndyWorkflowStages.QUALIFICATION_2_MOTIVATION
                
        elif current_stage == AndyWorkflowStages.QUALIFICATION_2_MOTIVATION:
            if any(word in message_lower for word in ["want", "goal", "dream", "freedom", "boss"]):
                return AndyWorkflowStages.QUALIFICATION_3_BUDGET
                
        elif current_stage == AndyWorkflowStages.QUALIFICATION_3_BUDGET:
            if any(word in message_lower for word in ["$", "k", "thousand", "budget", "invest"]):
                return AndyWorkflowStages.SCHEDULING
                
        elif current_stage == AndyWorkflowStages.SCHEDULING:
            if any(word in message_lower for word in ["yes", "book", "schedule", "meeting"]):
                return AndyWorkflowStages.CONFIRMATION
        
        return current_stage

    async def test_conversation_memory_persistence(self):
        """Test that conversation context persists across stages"""
        print("\n💾 TESTING CONVERSATION MEMORY PERSISTENCE")
        
        # Clear existing context
        try:
            context_key = f"sms_lead_context:{self.test_phone}"
            self.memory_manager.redis_client.delete(context_key)
        except Exception as e:
            print(f"⚠️ Could not clear context: {e}")
        
        # Build context through multiple stages
        stages_data = [
            ("introduction", "Hi, I'm interested in franchises"),
            ("work_background", "I work in corporate finance"),
            ("motivation", "I want to be my own boss"),
            ("budget", "I have $200k available")
        ]
        
        accumulated_context = {}
        
        for stage, message in stages_data:
            try:
                result = await self.andy.process_sms(
                    phone_number=self.test_phone,
                    message=message,
                    lead_id=self.test_lead_id
                )
                
                if result.get("success"):
                    # Check that previous context is still available
                    current_context = self.memory_manager.get_lead_context(self.test_phone)
                    
                    # Verify accumulated data is preserved
                    for key, value in accumulated_context.items():
                        if key in current_context:
                            print(f"✅ {key} preserved: {current_context[key]}")
                        else:
                            print(f"⚠️ {key} not found in current context")
                    
                    # Add new data to accumulated context
                    if current_context:
                        accumulated_context.update(current_context)
                        
                    print(f"✅ Stage {stage} completed, context size: {len(accumulated_context)}")
                    
            except Exception as e:
                print(f"❌ Error in stage {stage}: {e}")
        
        # Verify final context has all accumulated data
        final_context = self.memory_manager.get_lead_context(self.test_phone)
        assert final_context is not None, "Final context should not be None"
        print(f"✅ Final context preserved with {len(final_context)} fields")

    async def test_error_recovery_between_stages(self):
        """Test that Andy can recover from errors and continue conversation flow"""
        print("\n🔄 TESTING ERROR RECOVERY BETWEEN STAGES")
        
        # Clear context
        try:
            context_key = f"sms_lead_context:{self.test_phone}"
            self.memory_manager.redis_client.delete(context_key)
        except Exception as e:
            print(f"⚠️ Could not clear context: {e}")
        
        # Start conversation normally
        result1 = await self.andy.process_sms(
            phone_number=self.test_phone,
            message="Hi, I'm interested in franchises",
            lead_id=self.test_lead_id
        )
        
        assert result1.get("success"), "Initial message should succeed"
        print("✅ Initial conversation started")
        
        # Simulate error scenario with invalid input
        result2 = await self.andy.process_sms(
            phone_number=self.test_phone,
            message="",  # Empty message
            lead_id=self.test_lead_id
        )
        
        # Andy should handle gracefully
        print(f"✅ Empty message handled: {result2.get('success', False)}")
        
        # Continue conversation normally
        result3 = await self.andy.process_sms(
            phone_number=self.test_phone,
            message="I work in finance and want to learn more",
            lead_id=self.test_lead_id
        )
        
        assert result3.get("success"), "Conversation should continue after error"
        print("✅ Conversation recovered and continued")
        
        # Verify context is still intact
        context = self.memory_manager.get_lead_context(self.test_phone)
        assert context is not None, "Context should be preserved after error"
        print("✅ Context preserved through error recovery")
