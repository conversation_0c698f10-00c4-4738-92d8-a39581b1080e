"""
Meeting Agent Scenario Tests
Tests for specific meeting booking scenarios and edge cases
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from app.meeting_agent.agent import MeetingAgent
from app.meeting_agent.fsm import MeetingContext, MeetingState
from app.services.zoho_bookings_service import BookingSlot, BookingResult


class TestMeetingBookingScenarios:
    """Test realistic meeting booking scenarios"""
    
    @pytest.fixture
    def meeting_agent(self):
        """Create meeting agent with mocked dependencies"""
        with patch('app.meeting_agent.agent.get_redis_client'), \
             patch('app.meeting_agent.agent.ZohoBookingsService') as mock_zoho:
            
            agent = MeetingAgent()
            agent.enabled = True
            
            # Mock Zoho service methods
            mock_zoho_instance = Mock()
            mock_zoho_instance.get_available_slots_for_date = AsyncMock(return_value=[
                BookingSlot(
                    staff_id="staff1",
                    staff_name="<PERSON>",
                    start_time=datetime(2024, 3, 15, 14, 0),
                    end_time=datetime(2024, 3, 15, 14, 30),
                    service_id="service1",
                    service_name="Lead Meeting",
                    duration_minutes=30,
                    booking_url="https://bookings.zoho.com/book"
                )
            ])
            mock_zoho_instance.book_slot_for_meeting_agent = AsyncMock(return_value=BookingResult(
                success=True,
                booking_id="booking123",
                meeting_link="https://zoom.us/meeting/123",
                booking_url="https://bookings.zoho.com/booking123"
            ))
            mock_zoho_instance.format_slot_for_display = Mock(return_value="Friday, March 15 at 2:00 PM")
            
            agent.zoho_service = mock_zoho_instance
            return agent
    
    @pytest.mark.asyncio
    async def test_scenario_1_clear_date_time_booking(self, meeting_agent):
        """
        Scenario 1: User provides clear date and time, successful booking
        User: "Can we meet on Friday at 2pm?"
        Expected: Direct booking confirmation
        """
        # Mock FSM context
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        meeting_agent.fsm.get_context = Mock(return_value=None)
        meeting_agent.fsm.create_context = Mock(return_value=mock_context)
        meeting_agent.fsm.save_context = Mock(return_value=True)
        
        # Mock lead lookup
        meeting_agent._find_lead_by_phone = AsyncMock(return_value="lead-123")
        
        # Mock repository
        meeting_agent.repository.get_lead_details = AsyncMock(return_value={
            "id": "lead-123",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        })
        meeting_agent.repository.create_booking_intent = AsyncMock(return_value="booking-intent-123")
        meeting_agent.repository.finalize_booking = AsyncMock(return_value=True)
        meeting_agent.repository.store_conversation_messages = AsyncMock(return_value=True)
        
        # Mock NLU analysis
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "schedule",
                "confidence": 0.9,
                "extracted_slots": {
                    "parsed_date": {"date": "2024-03-15", "formatted": "March 15, 2024"},
                    "parsed_time": {"time_str": "14:00", "formatted": "2:00 PM"}
                }
            }
            
            result = await meeting_agent.process_message(
                "Can we meet on Friday at 2pm?",
                "+1234567890",
                "lead-123"
            )
        
        assert result["handled"] is True
        assert "booked" in result["response"].lower()
        assert "2:00 PM" in result["response"]
    
    @pytest.mark.asyncio
    async def test_scenario_2_time_only_then_date(self, meeting_agent):
        """
        Scenario 2: User provides time first, then date
        User: "2pm works for me"
        System: "Which day would you like to meet at 2:00 PM?"
        User: "Tomorrow"
        Expected: Show available slots
        """
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        meeting_agent.fsm.save_context = Mock(return_value=True)
        meeting_agent.fsm.transition_to = Mock(return_value=True)
        
        # First message: time only
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "schedule",
                "confidence": 0.8,
                "extracted_slots": {
                    "parsed_time": {"time_str": "14:00", "formatted": "2:00 PM"}
                }
            }
            
            result1 = await meeting_agent.process_message(
                "2pm works for me",
                "+1234567890",
                "lead-123"
            )
        
        assert result1["handled"] is True
        assert "which day" in result1["response"].lower()
        assert "2:00 PM" in result1["response"]
        
        # Second message: date
        mock_context.current_state = MeetingState.COLLECT_DAY
        mock_context.target_time_local = "14:00"
        
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "clarify_date",
                "confidence": 0.9,
                "extracted_slots": {
                    "parsed_date": {"date": "2024-03-16", "formatted": "March 16, 2024"}
                }
            }
            
            result2 = await meeting_agent.process_message(
                "Tomorrow",
                "+1234567890",
                "lead-123"
            )
        
        assert result2["handled"] is True
        assert "available" in result2["response"].lower()
    
    @pytest.mark.asyncio
    async def test_scenario_3_anytime_flexible(self, meeting_agent):
        """
        Scenario 3: User is flexible with timing
        User: "I'm flexible, anytime works"
        Expected: Show next available business slots
        """
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        meeting_agent.fsm.save_context = Mock(return_value=True)
        meeting_agent.fsm.transition_to = Mock(return_value=True)
        
        # Mock next available slots
        meeting_agent.zoho_service.get_next_available_business_slots = AsyncMock(return_value=[
            BookingSlot(
                staff_id="staff1", staff_name="John Smith",
                start_time=datetime(2024, 3, 15, 10, 0),
                end_time=datetime(2024, 3, 15, 10, 30),
                service_id="service1", service_name="Lead Meeting",
                duration_minutes=30, booking_url=""
            ),
            BookingSlot(
                staff_id="staff1", staff_name="John Smith",
                start_time=datetime(2024, 3, 15, 14, 0),
                end_time=datetime(2024, 3, 15, 14, 30),
                service_id="service1", service_name="Lead Meeting",
                duration_minutes=30, booking_url=""
            )
        ])
        
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "schedule",
                "confidence": 0.8,
                "extracted_slots": {}
            }
            
            result = await meeting_agent.process_message(
                "I'm flexible, anytime works",
                "+1234567890",
                "lead-123"
            )
        
        assert result["handled"] is True
        assert "available" in result["response"].lower()
        assert len(mock_context.candidate_slots_local) > 0
    
    @pytest.mark.asyncio
    async def test_scenario_4_no_slots_available(self, meeting_agent):
        """
        Scenario 4: No slots available on requested day
        User: "Can we meet on Saturday?"
        Expected: Suggest alternative day
        """
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        meeting_agent.fsm.save_context = Mock(return_value=True)
        meeting_agent.fsm.transition_to = Mock(return_value=True)
        
        # Mock no slots available
        meeting_agent.zoho_service.get_available_slots_for_date = AsyncMock(return_value=[])
        
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "schedule",
                "confidence": 0.8,
                "extracted_slots": {
                    "parsed_date": {"date": "2024-03-16", "formatted": "March 16, 2024"}
                }
            }
            
            result = await meeting_agent.process_message(
                "Can we meet on Saturday?",
                "+1234567890",
                "lead-123"
            )
        
        assert result["handled"] is True
        assert ("available" in result["response"].lower() or 
                "weekend" in result["response"].lower() or
                "closed" in result["response"].lower())
    
    @pytest.mark.asyncio
    async def test_scenario_5_user_defers_decision(self, meeting_agent):
        """
        Scenario 5: User defers decision
        User: "Let me think about it and get back to you"
        Expected: Acknowledge and keep options available
        """
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        mock_context.candidate_slots_local = [
            {"formatted_time": "Friday, March 15 at 2:00 PM", "staff_name": "John"}
        ]
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        meeting_agent.fsm.save_context = Mock(return_value=True)
        
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "defer",
                "confidence": 0.9,
                "extracted_slots": {}
            }
            
            result = await meeting_agent.process_message(
                "Let me think about it and get back to you",
                "+1234567890",
                "lead-123"
            )
        
        assert result["handled"] is True
        assert "no problem" in result["response"].lower()
        assert "ready" in result["response"].lower()
    
    @pytest.mark.asyncio
    async def test_scenario_6_booking_failure_recovery(self, meeting_agent):
        """
        Scenario 6: Booking fails, system recovers gracefully
        Expected: Appropriate error message and fallback
        """
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        mock_context.selected_slot = {
            "staff_id": "staff1",
            "staff_name": "John Smith",
            "start_time": "2024-03-15T14:00:00",
            "end_time": "2024-03-15T14:30:00",
            "service_id": "service1",
            "duration_minutes": 30
        }
        
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        meeting_agent.fsm.save_context = Mock(return_value=True)
        meeting_agent.fsm.transition_to = Mock(return_value=True)
        
        # Mock repository methods
        meeting_agent.repository.get_lead_details = AsyncMock(return_value={
            "id": "lead-123", "first_name": "John", "last_name": "Doe",
            "email": "<EMAIL>", "phone": "+1234567890"
        })
        
        # Mock booking failure
        meeting_agent.zoho_service.book_slot_for_meeting_agent = AsyncMock(
            return_value=BookingResult(success=False, error_message="Slot no longer available")
        )
        
        with patch.object(meeting_agent.nlu, 'analyze_message') as mock_nlu:
            mock_nlu.return_value = {
                "intent": "confirm",
                "confidence": 0.9,
                "extracted_slots": {}
            }
            
            result = await meeting_agent.process_message(
                "Yes, book that time",
                "+1234567890",
                "lead-123"
            )
        
        assert result["handled"] is False
        assert "trouble" in result["response"].lower()


class TestMeetingAgentEdgeCases:
    """Test edge cases and error scenarios"""
    
    @pytest.fixture
    def meeting_agent(self):
        """Create meeting agent for edge case testing"""
        with patch('app.meeting_agent.agent.get_redis_client'), \
             patch('app.meeting_agent.agent.ZohoBookingsService'):
            agent = MeetingAgent()
            agent.enabled = True
            return agent
    
    @pytest.mark.asyncio
    async def test_context_expiry(self, meeting_agent):
        """Test expired context handling"""
        # Create expired context
        expired_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        expired_context.updated_at = (datetime.utcnow() - timedelta(hours=2)).isoformat()
        
        meeting_agent.fsm.get_context = Mock(return_value=expired_context)
        meeting_agent.fsm.clear_context = Mock(return_value=True)
        meeting_agent._find_lead_by_phone = AsyncMock(return_value="lead-123")
        
        # Should create new context
        meeting_agent.fsm.create_context = Mock(return_value=MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="new-session"
        ))
        
        result = await meeting_agent.process_message(
            "I'd like to schedule a meeting",
            "+1234567890",
            "lead-123"
        )
        
        # Should handle gracefully
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_invalid_lead_id(self, meeting_agent):
        """Test handling of invalid lead ID"""
        meeting_agent.fsm.get_context = Mock(return_value=None)
        meeting_agent._find_lead_by_phone = AsyncMock(return_value=None)
        
        result = await meeting_agent.process_message(
            "I'd like to schedule a meeting",
            "+1234567890",
            None
        )
        
        assert result["handled"] is False
        assert "Lead not found" in result["reason"]
    
    @pytest.mark.asyncio
    async def test_network_error_handling(self, meeting_agent):
        """Test network error handling"""
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        
        # Mock network error
        meeting_agent.nlu.analyze_message = AsyncMock(side_effect=Exception("Network timeout"))
        
        result = await meeting_agent.process_message(
            "I'd like to schedule a meeting",
            "+1234567890",
            "lead-123"
        )
        
        assert result["handled"] is False
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_malformed_message_handling(self, meeting_agent):
        """Test handling of malformed or empty messages"""
        mock_context = MeetingContext(
            lead_id="lead-123",
            phone_number="+1234567890",
            session_id="session-123"
        )
        meeting_agent.fsm.get_context = Mock(return_value=mock_context)
        
        # Test empty message
        result = await meeting_agent.process_message("", "+1234567890", "lead-123")
        assert result["handled"] is False
        
        # Test very long message
        long_message = "a" * 10000
        result = await meeting_agent.process_message(long_message, "+1234567890", "lead-123")
        # Should handle gracefully without crashing
        assert result is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
