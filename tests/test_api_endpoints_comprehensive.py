"""
Comprehensive API endpoint tests for the merged system
Tests all API endpoints with authentication, validation, and error handling
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, Mock

from app.models.lead import Lead
from app.models.booking import Booking


@pytest.mark.api
@pytest.mark.asyncio
class TestAPIEndpointsComprehensive:
    """Comprehensive API endpoint tests"""

    async def test_kudosity_webhook_endpoint(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test Kudosity webhook endpoint comprehensively"""
        
        # Test valid SMS webhook
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] == True
        assert "data" in response_data
        assert "ai_response" in response_data["data"]
        
        # Test invalid webhook data
        invalid_data = {"invalid": "data"}
        response = client.post("/api/webhooks/kudosity", json=invalid_data)
        assert response.status_code in [400, 422]  # Validation error
        
        # Test missing required fields
        incomplete_data = {"mo": {"sender": "123"}}  # Missing recipient and message
        response = client.post("/api/webhooks/kudosity", json=incomplete_data)
        assert response.status_code in [400, 422]
        
        # Test empty message
        empty_message_data = test_data_factory.create_sms_webhook_data()
        empty_message_data["mo"]["message"] = ""
        response = client.post("/api/webhooks/kudosity", json=empty_message_data)
        assert response.status_code in [200, 400]  # Depends on validation

    async def test_booking_endpoints_with_auth(
        self,
        client,
        db_session,
        test_lead
    ):
        """Test booking endpoints with authentication"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": []
            })
            mock_service.return_value = mock_instance
            
            # Test without authentication (should fail)
            response = client.post("/api/v1/bookings/availability", json={
                "service_type": "lead_meeting",
                "days_ahead": 7
            })
            assert response.status_code == 401  # Unauthorized
            
            # Test simple booking endpoints (no auth required)
            response = client.post("/api/v1/simple-booking/availability", json={
                "service_type": "lead_meeting",
                "days_ahead": 7
            })
            assert response.status_code == 200

    async def test_api_validation_and_error_handling(
        self,
        client,
        db_session,
        test_data_factory
    ):
        """Test API validation and error handling"""
        
        # Test invalid JSON
        response = client.post(
            "/api/webhooks/kudosity",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
        
        # Test oversized request
        large_message = "A" * 10000  # Very large message
        large_data = test_data_factory.create_sms_webhook_data()
        large_data["mo"]["message"] = large_message
        
        response = client.post("/api/webhooks/kudosity", json=large_data)
        assert response.status_code in [200, 400, 413]  # Depends on limits
        
        # Test invalid phone number format
        invalid_phone_data = test_data_factory.create_sms_webhook_data()
        invalid_phone_data["mo"]["sender"] = "invalid-phone"
        
        response = client.post("/api/webhooks/kudosity", json=invalid_phone_data)
        # Should handle gracefully
        assert response.status_code in [200, 400]

    async def test_api_response_formats(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test API response formats consistency"""
        
        # Test successful response format
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        
        assert response.status_code == 200
        response_data = response.json()
        
        # Verify standard response structure
        required_fields = ["success", "message", "data"]
        for field in required_fields:
            assert field in response_data
        
        assert isinstance(response_data["success"], bool)
        assert isinstance(response_data["message"], dict)
        assert "title" in response_data["message"]
        assert "description" in response_data["message"]
        assert isinstance(response_data["data"], dict)

    async def test_api_performance_and_timeouts(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test API performance and timeout handling"""
        
        # Configure fast mock responses
        mock_sms_service.send_sms.return_value = {
            "success": True,
            "message_id": "fast-test",
            "parts_sent": 1
        }
        
        import time
        
        # Test normal response time
        start_time = time.time()
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        assert response.status_code == 200
        assert response_time < 30.0  # Should respond within 30 seconds
        
        # Test with slow AI response
        with patch('app.agents.sms_assistant.AndySMSAssistant') as mock_assistant:
            mock_instance = Mock()
            
            # Simulate slow response
            async def slow_process(*args, **kwargs):
                await asyncio.sleep(2)  # 2 second delay
                return {
                    "success": True,
                    "response": "Test response",
                    "conversation_stage": "introduction"
                }
            
            mock_instance.process_message = slow_process
            mock_assistant.return_value = mock_instance
            
            start_time = time.time()
            response = client.post("/api/webhooks/kudosity", json=sms_data)
            end_time = time.time()
            
            response_time = end_time - start_time
            assert response.status_code == 200
            assert response_time >= 2.0  # Should include the delay

    async def test_api_concurrent_requests(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test API handling of concurrent requests"""
        
        import asyncio
        import threading
        
        # Configure mock for concurrent testing
        mock_sms_service.send_sms.return_value = {
            "success": True,
            "message_id": "concurrent-test",
            "parts_sent": 1
        }
        
        def make_request(message_suffix):
            sms_data = test_data_factory.create_sms_webhook_data()
            sms_data["mo"]["message"] = f"Test message {message_suffix}"
            return client.post("/api/webhooks/kudosity", json=sms_data)
        
        # Make concurrent requests
        threads = []
        results = []
        
        for i in range(3):
            thread = threading.Thread(
                target=lambda i=i: results.append(make_request(i))
            )
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Verify all requests succeeded
        for response in results:
            assert response.status_code == 200

    async def test_api_data_persistence(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test API data persistence across requests"""
        
        # Send first message
        sms_data = test_data_factory.create_sms_webhook_data(
            mo={
                "sender": "919408869824",
                "recipient": "+61400000000",
                "message": "I'm interested in franchises"
            }
        )
        
        response1 = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response1.status_code == 200
        
        # Send follow-up message
        sms_data["mo"]["message"] = "What's the investment required?"
        response2 = client.post("/api/webhooks/kudosity", json=sms_data)
        assert response2.status_code == 200
        
        # Verify conversation continuity
        response2_data = response2.json()
        assert response2_data["success"] == True
        
        # Check database for conversation messages
        from sqlalchemy import select
        from app.models.conversation_message import ConversationMessage
        
        result = await db_session.execute(
            select(ConversationMessage).where(
                ConversationMessage.lead_id == test_lead.id
            ).order_by(ConversationMessage.created_at)
        )
        messages = result.scalars().all()
        
        assert len(messages) >= 2  # At least 2 messages stored

    async def test_api_error_recovery(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test API error recovery mechanisms"""
        
        # Test database connection error
        with patch('app.core.database.connection.get_db') as mock_db:
            mock_db.side_effect = Exception("Database connection failed")
            
            sms_data = test_data_factory.create_sms_webhook_data()
            response = client.post("/api/webhooks/kudosity", json=sms_data)
            
            # Should handle gracefully
            assert response.status_code in [200, 500]
        
        # Test SMS service error
        mock_sms_service.send_sms.side_effect = Exception("SMS service down")
        
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        
        # Should handle gracefully
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            response_data = response.json()
            # Should indicate error in response
            assert response_data["success"] == False or "error" in response_data

    async def test_api_security_headers(
        self,
        client,
        test_data_factory
    ):
        """Test API security headers"""
        
        sms_data = test_data_factory.create_sms_webhook_data()
        response = client.post("/api/webhooks/kudosity", json=sms_data)
        
        # Check for security headers
        headers = response.headers
        
        # These headers might be set by middleware
        security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection"
        ]
        
        # Note: Not all headers may be present depending on configuration
        # This test documents expected security headers

    async def test_api_rate_limiting(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test API rate limiting (if implemented)"""
        
        # Configure fast mock
        mock_sms_service.send_sms.return_value = {
            "success": True,
            "message_id": "rate-test",
            "parts_sent": 1
        }
        
        sms_data = test_data_factory.create_sms_webhook_data()
        
        # Make multiple rapid requests
        responses = []
        for i in range(10):
            sms_data["mo"]["message"] = f"Rate limit test {i}"
            response = client.post("/api/webhooks/kudosity", json=sms_data)
            responses.append(response)
        
        # All should succeed or some should be rate limited
        success_count = sum(1 for r in responses if r.status_code == 200)
        rate_limited_count = sum(1 for r in responses if r.status_code == 429)
        
        # Either all succeed (no rate limiting) or some are rate limited
        assert success_count + rate_limited_count == 10
