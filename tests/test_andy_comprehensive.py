"""
Comprehensive Andy Test Suite
Tests all Andy functionality including SMS processing, follow-ups, question classification, and lead status monitoring
"""

import pytest
import asyncio
import uuid
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, Mock
from typing import Dict, Any, List

import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.agents.sms_assistant import <PERSON><PERSON>SAssistant, get_andy_sms_assistant
from app.agents.followup_agent import FollowUpAgent, FollowUpType
from app.agents.question_classification import QuestionClassificationAgent
from app.agents.lead_status_agent import get_lead_status_agent
from app.core.memory.sms_memory import get_sms_memory_manager
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.models.messaging_rule import MessagingRule
from app.models.franchisor import Franchisor
from app.core.database.connection import get_db


@pytest.mark.asyncio
class TestAndyComprehensive:
    """Comprehensive test suite for Andy functionality"""

    @pytest_asyncio.fixture
    async def db_session(self):
        """Database session fixture"""
        async for db in get_db():
            yield db
            break

    @pytest_asyncio.fixture
    async def test_franchisor(self, db_session):
        """Create test franchisor"""
        franchisor = Franchisor(
            id="test-franchisor-123",
            name="Test Franchise",
            email="<EMAIL>",
            is_active=True,
            is_deleted=False
        )
        db_session.add(franchisor)
        await db_session.commit()
        return franchisor

    @pytest_asyncio.fixture
    async def test_lead(self, db_session, test_franchisor):
        """Create test lead"""
        lead = Lead(
            id=str(uuid.uuid4()),
            first_name="John",
            last_name="Doe",
            mobile="+61412345678",
            email="<EMAIL>",
            franchisor_id=test_franchisor.id,
            is_active=True,
            is_deleted=False
        )
        db_session.add(lead)
        await db_session.commit()
        return lead

    @pytest_asyncio.fixture
    async def test_messaging_rule(self, db_session, test_franchisor):
        """Create test messaging rule"""
        rule = MessagingRule(
            id=str(uuid.uuid4()),
            franchisor_id=test_franchisor.id,
            lead_init_delay_m=5,
            no_response_delay_h=3,
            max_followups=3,
            is_active=True,
            is_deleted=False
        )
        db_session.add(rule)
        await db_session.commit()
        return rule

    async def test_andy_sms_processing_basic(self, test_lead):
        """Test basic Andy SMS processing"""
        andy = get_andy_sms_assistant()
        
        # Test basic message processing
        result = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="Hi, I'm interested in franchise opportunities",
            lead_id=test_lead.id
        )
        
        assert result["success"] is True
        assert "response" in result
        assert len(result["response"]) > 0
        assert "current_stage" in result

    async def test_andy_conversation_flow(self, test_lead):
        """Test Andy's conversation flow through qualification stages"""
        andy = get_andy_sms_assistant()
        
        # Stage 1: Introduction
        result1 = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="Hi, I'm interested in Coochie Hydrogreen franchise",
            lead_id=test_lead.id
        )
        
        assert result1["success"] is True
        assert "Andy" in result1["response"]
        
        # Stage 2: Work background question
        result2 = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="I work in corporate finance",
            lead_id=test_lead.id
        )
        
        assert result2["success"] is True
        
        # Stage 3: Motivation question
        result3 = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="I want to be my own boss",
            lead_id=test_lead.id
        )
        
        assert result3["success"] is True

    async def test_andy_memory_persistence(self, test_lead):
        """Test Andy's memory persistence across conversations"""
        andy = get_andy_sms_assistant()
        memory_manager = get_sms_memory_manager()
        
        # First conversation
        await andy.process_sms(
            phone_number=test_lead.mobile,
            message="Hi, I'm John and I work in IT",
            lead_id=test_lead.id
        )
        
        # Check memory storage
        context = memory_manager.get_lead_context(test_lead.mobile)
        assert context is not None
        assert context.get("name") == "John"
        
        # Second conversation - should remember context
        result = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="What are the investment requirements?",
            lead_id=test_lead.id
        )
        
        assert result["success"] is True
        # Andy should remember the lead's name
        assert "John" in result.get("lead_context", {}).get("name", "")

    async def test_andy_question_answering(self, test_lead):
        """Test Andy's question answering capabilities"""
        andy = get_andy_sms_assistant()
        
        # Test franchise-related question
        result = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="What's the initial investment for Coochie Hydrogreen?",
            lead_id=test_lead.id
        )
        
        assert result["success"] is True
        assert len(result["response"]) > 0
        # Should contain relevant information about investment

    async def test_andy_objection_handling(self, test_lead):
        """Test Andy's objection handling"""
        andy = get_andy_sms_assistant()
        
        # Test price objection
        result = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="This seems too expensive for me",
            lead_id=test_lead.id
        )
        
        assert result["success"] is True
        assert len(result["response"]) > 0
        # Should handle objection professionally

    async def test_followup_agent_basic(self, test_lead, test_messaging_rule):
        """Test basic follow-up agent functionality"""
        followup_agent = FollowUpAgent()

        # Test follow-up scheduling
        result = await followup_agent.schedule_initial_followup(
            lead_id=test_lead.id,
            messaging_rule=test_messaging_rule
        )

        assert result["success"] is True
        assert "task_id" in result

    async def test_followup_message_generation(self, test_lead):
        """Test follow-up message generation"""
        followup_agent = FollowUpAgent()
        
        # Create mock context
        context = {
            "lead_id": test_lead.id,
            "phone_number": test_lead.mobile,
            "conversation_history": [],
            "followup_count": 1,
            "max_followups": 3,
            "lead_engagement_level": "medium"
        }
        
        # Test different follow-up types
        no_response_msg = followup_agent.generate_followup_message(context, FollowUpType.NO_RESPONSE)
        engagement_msg = followup_agent.generate_followup_message(context, FollowUpType.ENGAGEMENT)
        
        assert len(no_response_msg) > 0
        assert len(engagement_msg) > 0
        assert no_response_msg != engagement_msg

    async def test_lead_status_monitoring(self, test_lead):
        """Test lead status monitoring"""
        status_agent = get_lead_status_agent()
        
        # Test conversation analysis
        analysis = await status_agent.analyze_conversation(test_lead.mobile)
        
        # Should handle case where no conversation exists
        assert analysis is None or hasattr(analysis, 'suggested_status')

    async def test_andy_error_handling(self, test_lead):
        """Test Andy's error handling"""
        andy = get_andy_sms_assistant()
        
        # Test with invalid input
        result = await andy.process_sms(
            phone_number="invalid_phone",
            message="",
            lead_id="invalid_id"
        )
        
        # Should handle gracefully
        assert "success" in result
        assert "error" in result or result["success"] is True

    async def test_andy_conversation_storage(self, test_lead, db_session):
        """Test that Andy stores conversations in database"""
        andy = get_andy_sms_assistant()
        
        # Process a message
        await andy.process_sms(
            phone_number=test_lead.mobile,
            message="Test message for storage",
            lead_id=test_lead.id
        )
        
        # Check if conversation was stored
        result = await db_session.execute(
            select(ConversationMessage).where(
                ConversationMessage.phone_number == test_lead.mobile
            )
        )
        messages = result.scalars().all()
        
        # Should have at least one message stored
        assert len(messages) > 0

    async def test_andy_performance(self, test_lead):
        """Test Andy's performance metrics"""
        andy = get_andy_sms_assistant()
        
        start_time = datetime.now()
        
        result = await andy.process_sms(
            phone_number=test_lead.mobile,
            message="Performance test message",
            lead_id=test_lead.id
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        assert result["success"] is True
        assert processing_time < 30  # Should process within 30 seconds
        
        # Check if processing time is included in result
        if "processing_time" in result:
            assert result["processing_time"] > 0

    async def test_andy_concurrent_processing(self, test_lead):
        """Test Andy's ability to handle concurrent requests"""
        andy = get_andy_sms_assistant()
        
        # Create multiple concurrent requests
        tasks = []
        for i in range(3):
            task = andy.process_sms(
                phone_number=f"+6141234567{i}",
                message=f"Concurrent test message {i}",
                lead_id=test_lead.id
            )
            tasks.append(task)
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed or handle gracefully
        for result in results:
            if isinstance(result, dict):
                assert "success" in result
            else:
                # Exception occurred - should be handled gracefully
                assert isinstance(result, Exception)
