"""
Tests for SMS Control System
Verifies that SMS messages are properly controlled based on environment flags.
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from app.core.sms_control import SMS<PERSON>ontroller, sms_controller


class TestSMSController:
    """Test SMS Controller functionality"""
    
    def test_sms_disabled_both_flags_false(self):
        """Test SMS disabled when both flags are false"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            assert controller.should_send_sms() is False
            assert controller.sms_test_mode is False
            assert controller.kudosity_sms_enabled is False
    
    def test_sms_disabled_test_mode_true(self):
        """Test SMS disabled when test mode is true (even if Kudosity enabled)"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "true", "KUDOSITY_SMS_ENABLED": "true"}):
            controller = SMSController()
            assert controller.should_send_sms() is False
            assert controller.sms_test_mode is True
            assert controller.kudosity_sms_enabled is True
    
    def test_sms_disabled_kudosity_false(self):
        """Test SMS disabled when Kudosity is disabled"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            assert controller.should_send_sms() is False
            assert controller.sms_test_mode is False
            assert controller.kudosity_sms_enabled is False
    
    def test_sms_enabled_correct_config(self):
        """Test SMS enabled when both flags are correctly set"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "true"}):
            controller = SMSController()
            assert controller.should_send_sms() is True
            assert controller.sms_test_mode is False
            assert controller.kudosity_sms_enabled is True
    
    def test_get_sms_status(self):
        """Test SMS status reporting"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "true", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            status = controller.get_sms_status()
            
            assert status["sms_test_mode"] is True
            assert status["kudosity_sms_enabled"] is False
            assert status["will_send_sms"] is False
            assert "test mode" in status["reason"].lower()
    
    def test_log_sms_attempt_enabled(self):
        """Test SMS attempt logging when SMS is enabled"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "true"}):
            controller = SMSController()
            result = controller.log_sms_attempt("+61400000000", "Test message", "test")
            
            assert result["action"] == "send_sms"
            assert result["will_send"] is True
            assert "enabled" in result["reason"].lower()
    
    def test_log_sms_attempt_disabled(self):
        """Test SMS attempt logging when SMS is disabled"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "true", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            result = controller.log_sms_attempt("+61400000000", "Test message", "test")
            
            assert result["action"] == "mock_sms"
            assert result["will_send"] is False
            assert "disabled" in result["reason"].lower() or "test mode" in result["reason"].lower()
    
    def test_mask_phone_number(self):
        """Test phone number masking for privacy"""
        controller = SMSController()
        
        # Long phone number
        masked = controller._mask_phone_number("+61400123456")
        assert masked == "+61400***"
        
        # Short phone number
        masked = controller._mask_phone_number("123")
        assert masked == "***"
    
    def test_create_mock_sms_response(self):
        """Test mock SMS response creation"""
        controller = SMSController()
        mock_response = controller.create_mock_sms_response("test")
        
        assert mock_response["success"] is True
        assert mock_response["message_id"].startswith("mock_test_")
        assert mock_response["error"] is None
        assert mock_response["mock"] is True
    
    def test_validate_configuration_warnings(self):
        """Test configuration validation with warnings"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "true", "KUDOSITY_SMS_ENABLED": "true"}):
            controller = SMSController()
            validation = controller.validate_configuration()
            
            assert validation["valid"] is True
            assert len(validation["warnings"]) > 0
            assert any("overrides" in warning for warning in validation["warnings"])
            assert len(validation["recommendations"]) > 0
    
    def test_validate_configuration_both_disabled(self):
        """Test configuration validation when both flags are disabled"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            validation = controller.validate_configuration()
            
            assert validation["valid"] is True
            assert len(validation["warnings"]) > 0
            assert any("no SMS will be sent" in warning for warning in validation["warnings"])


class TestSMSControlIntegration:
    """Test SMS control integration functions"""
    
    def test_should_send_sms_function(self):
        """Test global should_send_sms function"""
        from app.core.sms_control import should_send_sms
        
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "true"}):
            # Need to recreate controller to pick up new env vars
            with patch('app.core.sms_control.sms_controller') as mock_controller:
                mock_controller.should_send_sms.return_value = True
                assert should_send_sms() is True
    
    def test_get_sms_status_function(self):
        """Test global get_sms_status function"""
        from app.core.sms_control import get_sms_status
        
        with patch('app.core.sms_control.sms_controller') as mock_controller:
            mock_status = {"will_send_sms": False, "reason": "test"}
            mock_controller.get_sms_status.return_value = mock_status
            
            status = get_sms_status()
            assert status == mock_status
    
    def test_log_sms_attempt_function(self):
        """Test global log_sms_attempt function"""
        from app.core.sms_control import log_sms_attempt
        
        with patch('app.core.sms_control.sms_controller') as mock_controller:
            mock_result = {"action": "mock_sms", "will_send": False}
            mock_controller.log_sms_attempt.return_value = mock_result
            
            result = log_sms_attempt("+61400000000", "Test", "context")
            assert result == mock_result
    
    def test_create_mock_sms_response_function(self):
        """Test global create_mock_sms_response function"""
        from app.core.sms_control import create_mock_sms_response
        
        with patch('app.core.sms_control.sms_controller') as mock_controller:
            mock_response = {"success": True, "message_id": "mock_123", "mock": True}
            mock_controller.create_mock_sms_response.return_value = mock_response
            
            response = create_mock_sms_response("test")
            assert response == mock_response
    
    def test_validate_sms_configuration_function(self):
        """Test global validate_sms_configuration function"""
        from app.core.sms_control import validate_sms_configuration
        
        with patch('app.core.sms_control.sms_controller') as mock_controller:
            mock_validation = {"valid": True, "warnings": [], "recommendations": []}
            mock_controller.validate_configuration.return_value = mock_validation
            
            validation = validate_sms_configuration()
            assert validation == mock_validation


class TestSMSControlScenarios:
    """Test specific SMS control scenarios"""
    
    def test_production_scenario(self):
        """Test production scenario: SMS_TEST_MODE=false, KUDOSITY_SMS_ENABLED=true"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "true"}):
            controller = SMSController()
            
            assert controller.should_send_sms() is True
            
            status = controller.get_sms_status()
            assert status["will_send_sms"] is True
            assert "enabled" in status["reason"].lower()
            
            log_result = controller.log_sms_attempt("+61400000000", "Hello", "production")
            assert log_result["will_send"] is True
    
    def test_testing_scenario(self):
        """Test testing scenario: SMS_TEST_MODE=true, KUDOSITY_SMS_ENABLED=false"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "true", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            
            assert controller.should_send_sms() is False
            
            status = controller.get_sms_status()
            assert status["will_send_sms"] is False
            assert "test mode" in status["reason"].lower()
            
            log_result = controller.log_sms_attempt("+61400000000", "Hello", "testing")
            assert log_result["will_send"] is False
    
    def test_development_scenario(self):
        """Test development scenario: SMS_TEST_MODE=true, KUDOSITY_SMS_ENABLED=true"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "true", "KUDOSITY_SMS_ENABLED": "true"}):
            controller = SMSController()
            
            # Test mode overrides Kudosity enabled
            assert controller.should_send_sms() is False
            
            status = controller.get_sms_status()
            assert status["will_send_sms"] is False
            assert "test mode" in status["reason"].lower()
            
            validation = controller.validate_configuration()
            assert len(validation["warnings"]) > 0
            assert any("overrides" in warning for warning in validation["warnings"])
    
    def test_disabled_scenario(self):
        """Test disabled scenario: SMS_TEST_MODE=false, KUDOSITY_SMS_ENABLED=false"""
        with patch.dict(os.environ, {"SMS_TEST_MODE": "false", "KUDOSITY_SMS_ENABLED": "false"}):
            controller = SMSController()
            
            assert controller.should_send_sms() is False
            
            status = controller.get_sms_status()
            assert status["will_send_sms"] is False
            assert "disabled" in status["reason"].lower()
            
            validation = controller.validate_configuration()
            assert len(validation["warnings"]) > 0
            assert any("no SMS will be sent" in warning for warning in validation["warnings"])


if __name__ == "__main__":
    pytest.main([__file__])
