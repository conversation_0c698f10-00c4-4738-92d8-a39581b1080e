"""
Conversation Harness for Andy AI Testing
Drives <PERSON>'s pipeline with scripted turns, captures responses, FSM state, and side effects
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import json
import structlog

from app.agents.sms_assistant import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.meeting_agent.fsm import MeetingState, MeetingContext
from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.models.booking import Booking

from app.core.logging import logger


@dataclass
class ConversationTurn:
    """Represents a single turn in the conversation."""
    user_message: str
    expected_response_contains: Optional[str] = None
    expected_fsm_state: Optional[MeetingState] = None
    expected_db_writes: List[str] = field(default_factory=list)
    expected_sms_segments: int = 1
    should_schedule_followup: bool = False
    should_cancel_followup: bool = False


@dataclass
class ConversationResult:
    """Results from a conversation turn."""
    agent_response: str
    fsm_state: Optional[MeetingState]
    db_changes: Dict[str, Any]
    sms_segments: List[str]
    scheduled_jobs: List[Dict[str, Any]]
    processing_metadata: Dict[str, Any]
    success: bool
    error: Optional[str] = None


class ConversationHarness:
    """
    Test harness for driving Andy AI conversations with assertions.
    Captures all side effects including DB writes, SMS segments, and scheduled jobs.
    """
    
    def __init__(self, db_session, mocks: Dict[str, Any]):
        self.db_session = db_session
        self.mocks = mocks
        self.conversation_history: List[ConversationResult] = []
        self.lead_id: Optional[str] = None
        self.phone_number: str = "+61412345678"
        
    async def setup_lead(self, lead_data: Dict[str, Any]) -> str:
        """Create a test lead and return its ID."""
        from tests.helpers.factories import LeadFactory
        
        factory = LeadFactory(self.db_session)
        lead = await factory.create_lead(**lead_data)
        self.lead_id = str(lead.id)
        return self.lead_id
    
    async def run_conversation(self, turns: List[ConversationTurn]) -> List[ConversationResult]:
        """
        Run a complete conversation script and return results.
        
        Args:
            turns: List of conversation turns to execute
            
        Returns:
            List of conversation results with assertions
        """
        results = []
        
        for i, turn in enumerate(turns):
            logger.info(f"Executing turn {i+1}: {turn.user_message}")
            
            try:
                result = await self._execute_turn(turn)
                results.append(result)
                self.conversation_history.append(result)
                
                # Run assertions
                self._assert_turn_expectations(turn, result)
                
            except Exception as e:
                logger.error(f"Turn {i+1} failed: {e}")
                error_result = ConversationResult(
                    agent_response="",
                    fsm_state=None,
                    db_changes={},
                    sms_segments=[],
                    scheduled_jobs=[],
                    processing_metadata={},
                    success=False,
                    error=str(e)
                )
                results.append(error_result)
                raise
        
        return results
    
    async def _execute_turn(self, turn: ConversationTurn) -> ConversationResult:
        """Execute a single conversation turn."""
        # Capture initial DB state
        initial_db_state = await self._capture_db_state()
        
        # Clear mock call history
        self._reset_mocks()
        
        # Execute the turn through Andy's pipeline
        from app.api.v1.endpoints.webhooks import process_kudosity_webhook
        
        webhook_payload = {
            "message": turn.user_message,
            "phone": self.phone_number,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Process through webhook
        response = await process_kudosity_webhook(webhook_payload)
        
        # Capture results
        agent_response = self._extract_agent_response(response)
        fsm_state = await self._get_current_fsm_state()
        db_changes = await self._capture_db_changes(initial_db_state)
        sms_segments = self._extract_sms_segments()
        scheduled_jobs = self._extract_scheduled_jobs()
        processing_metadata = self._extract_processing_metadata(response)
        
        return ConversationResult(
            agent_response=agent_response,
            fsm_state=fsm_state,
            db_changes=db_changes,
            sms_segments=sms_segments,
            scheduled_jobs=scheduled_jobs,
            processing_metadata=processing_metadata,
            success=True
        )
    
    def _assert_turn_expectations(self, turn: ConversationTurn, result: ConversationResult):
        """Assert that turn results match expectations."""
        # Assert response contains expected text
        if turn.expected_response_contains:
            assert turn.expected_response_contains.lower() in result.agent_response.lower(), \
                f"Response '{result.agent_response}' does not contain '{turn.expected_response_contains}'"
        
        # Assert FSM state
        if turn.expected_fsm_state:
            assert result.fsm_state == turn.expected_fsm_state, \
                f"Expected FSM state {turn.expected_fsm_state}, got {result.fsm_state}"
        
        # Assert DB writes
        for expected_write in turn.expected_db_writes:
            assert expected_write in result.db_changes, \
                f"Expected DB write '{expected_write}' not found in {list(result.db_changes.keys())}"
        
        # Assert SMS segments
        assert len(result.sms_segments) == turn.expected_sms_segments, \
            f"Expected {turn.expected_sms_segments} SMS segments, got {len(result.sms_segments)}"
        
        # Assert no emojis in response
        self._assert_no_emojis(result.agent_response)
        
        # Assert followup scheduling
        if turn.should_schedule_followup:
            assert any("followup" in job.get("type", "") for job in result.scheduled_jobs), \
                "Expected followup to be scheduled"
        
        if turn.should_cancel_followup:
            assert any("cancel" in job.get("action", "") for job in result.scheduled_jobs), \
                "Expected followup to be cancelled"
    
    async def _capture_db_state(self) -> Dict[str, Any]:
        """Capture current database state for comparison."""
        state = {}
        
        # Count records in key tables
        from sqlalchemy import select, func
        
        # Conversation messages
        result = await self.db_session.execute(
            select(func.count(ConversationMessage.id))
        )
        state['conversation_messages'] = result.scalar()
        
        # Bookings
        result = await self.db_session.execute(
            select(func.count(Booking.id))
        )
        state['bookings'] = result.scalar()
        
        # Leads
        result = await self.db_session.execute(
            select(func.count(Lead.id))
        )
        state['leads'] = result.scalar()
        
        return state
    
    async def _capture_db_changes(self, initial_state: Dict[str, Any]) -> Dict[str, Any]:
        """Capture changes in database state."""
        current_state = await self._capture_db_state()
        changes = {}
        
        for table, initial_count in initial_state.items():
            current_count = current_state[table]
            if current_count > initial_count:
                changes[f"{table}_created"] = current_count - initial_count
        
        return changes
    
    async def _get_current_fsm_state(self) -> Optional[MeetingState]:
        """Get current FSM state from meeting agent context."""
        try:
            from app.meeting_agent.agent import MeetingAgent
            agent = MeetingAgent()
            context = agent.fsm.get_context(self.phone_number)
            return context.current_state if context else None
        except Exception:
            return None
    
    def _extract_agent_response(self, response: Dict[str, Any]) -> str:
        """Extract agent response from webhook response."""
        if response.get("success"):
            data = response.get("data", {})
            return data.get("ai_response", data.get("response", ""))
        return ""
    
    def _extract_sms_segments(self) -> List[str]:
        """Extract SMS segments from mock calls."""
        segments = []
        if 'kudosity' in self.mocks:
            segments = self.mocks['kudosity'].sent_messages
        return segments
    
    def _extract_scheduled_jobs(self) -> List[Dict[str, Any]]:
        """Extract scheduled jobs from Celery mock."""
        # This would capture Celery task scheduling
        # For now, return empty list as we'd need to mock Celery properly
        return []
    
    def _extract_processing_metadata(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Extract processing metadata from response."""
        if response.get("success"):
            data = response.get("data", {})
            return data.get("processing_metadata", {})
        return {}
    
    def _reset_mocks(self):
        """Reset mock call history."""
        for mock in self.mocks.values():
            if hasattr(mock, 'reset'):
                mock.reset()
    
    def _assert_no_emojis(self, text: str):
        """Assert text contains no emojis."""
        import re
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+", flags=re.UNICODE
        )
        assert not emoji_pattern.search(text), f"Text contains emojis: {text}"
    
    # Convenience methods for common conversation patterns
    
    def create_meeting_request_turn(self, message: str, expected_state: MeetingState = None) -> ConversationTurn:
        """Create a turn for meeting request."""
        return ConversationTurn(
            user_message=message,
            expected_response_contains="meeting",
            expected_fsm_state=expected_state,
            expected_db_writes=["conversation_messages_created"],
            should_cancel_followup=True
        )
    
    def create_booking_confirmation_turn(self, message: str = "Yes, book it") -> ConversationTurn:
        """Create a turn for booking confirmation."""
        return ConversationTurn(
            user_message=message,
            expected_response_contains="booked",
            expected_fsm_state=MeetingState.BOOKED,
            expected_db_writes=["conversation_messages_created", "bookings_created"]
        )
    
    def create_clarification_turn(self, message: str, expected_contains: str) -> ConversationTurn:
        """Create a turn for clarification."""
        return ConversationTurn(
            user_message=message,
            expected_response_contains=expected_contains,
            expected_db_writes=["conversation_messages_created"]
        )


# Conversation script builders for common scenarios

class ConversationScripts:
    """Pre-built conversation scripts for common testing scenarios."""
    
    @staticmethod
    def happy_path_meeting_booking() -> List[ConversationTurn]:
        """Happy path: clear date/time -> booking confirmation."""
        return [
            ConversationTurn(
                user_message="I'd like to schedule a meeting for tomorrow at 2pm",
                expected_response_contains="tomorrow",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Yes, that works",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
    
    @staticmethod
    def time_only_then_date() -> List[ConversationTurn]:
        """Time-only -> date clarification -> booking."""
        return [
            ConversationTurn(
                user_message="2pm works for me",
                expected_response_contains="which day",
                expected_fsm_state=MeetingState.COLLECT_DAY,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Tomorrow",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Perfect",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
    
    @staticmethod
    def anytime_flexible() -> List[ConversationTurn]:
        """Anytime -> show options -> confirm."""
        return [
            ConversationTurn(
                user_message="I'm flexible, anytime works",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="The first one sounds good",
                expected_response_contains="booked",
                expected_fsm_state=MeetingState.BOOKED,
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
    
    @staticmethod
    def user_defers() -> List[ConversationTurn]:
        """User defers decision."""
        return [
            ConversationTurn(
                user_message="What times do you have tomorrow?",
                expected_response_contains="available",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Let me think about it",
                expected_response_contains="no problem",
                expected_fsm_state=MeetingState.AWAIT_CONFIRM,
                expected_db_writes=["conversation_messages_created"]
            )
        ]
