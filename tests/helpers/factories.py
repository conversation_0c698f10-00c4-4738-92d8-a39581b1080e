"""
Test data factories for Andy AI testing
Creates test instances of leads, conversations, bookings, etc.
"""

import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.lead import Lead
from app.models.conversation_message import ConversationMessage
from app.models.booking import Booking
from app.models.escalation_question import EscalationQuestion


class LeadFactory:
    """Factory for creating test leads."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def create_lead(
        self,
        first_name: str = "<PERSON>",
        last_name: str = "<PERSON><PERSON>",
        email: str = "<EMAIL>",
        phone: str = "+61412345678",
        mobile: str = None,
        location: str = "Sydney, NSW",
        status: str = "New",
        source: str = "Test",
        **kwargs
    ) -> Lead:
        """Create a test lead with default or provided values."""
        
        lead_data = {
            "id": uuid.uuid4(),
            "first_name": first_name,
            "last_name": last_name,
            "email": email,
            "phone": phone,
            "mobile": mobile or phone,
            "location": location,
            "status": status,
            "source": source,
            "is_active": True,
            "is_deleted": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            **kwargs
        }
        
        lead = Lead(**lead_data)
        self.db_session.add(lead)
        await self.db_session.commit()
        await self.db_session.refresh(lead)
        
        return lead
    
    async def create_qualified_lead(self, **kwargs) -> Lead:
        """Create a qualified lead ready for meeting booking."""
        defaults = {
            "status": "Qualified",
            "first_name": "Jane",
            "last_name": "Smith",
            "email": "<EMAIL>",
            "phone": "+61423456789"
        }
        defaults.update(kwargs)
        return await self.create_lead(**defaults)
    
    async def create_contacted_lead(self, **kwargs) -> Lead:
        """Create a lead that has been contacted."""
        defaults = {
            "status": "Contacted",
            "first_name": "Bob",
            "last_name": "Wilson",
            "email": "<EMAIL>",
            "phone": "+61434567890"
        }
        defaults.update(kwargs)
        return await self.create_lead(**defaults)


class ConversationFactory:
    """Factory for creating test conversation messages."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def create_message(
        self,
        lead_id: uuid.UUID,
        sender: str = "lead",
        message: str = "Hello",
        franchisor_id: Optional[uuid.UUID] = None,
        message_metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> ConversationMessage:
        """Create a conversation message."""
        
        message_data = {
            "id": uuid.uuid4(),
            "lead_id": lead_id,
            "franchisor_id": franchisor_id,
            "sender": sender,
            "message": message,
            "message_metadata": message_metadata or {},
            "is_active": True,
            "is_deleted": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            **kwargs
        }
        
        conv_message = ConversationMessage(**message_data)
        self.db_session.add(conv_message)
        await self.db_session.commit()
        await self.db_session.refresh(conv_message)
        
        return conv_message
    
    async def create_conversation_thread(
        self,
        lead_id: uuid.UUID,
        messages: list[tuple[str, str]],  # [(sender, message), ...]
        franchisor_id: Optional[uuid.UUID] = None
    ) -> list[ConversationMessage]:
        """Create a thread of conversation messages."""
        
        thread = []
        for sender, message in messages:
            msg = await self.create_message(
                lead_id=lead_id,
                sender=sender,
                message=message,
                franchisor_id=franchisor_id
            )
            thread.append(msg)
        
        return thread
    
    async def create_meeting_conversation(
        self,
        lead_id: uuid.UUID,
        franchisor_id: Optional[uuid.UUID] = None
    ) -> list[ConversationMessage]:
        """Create a typical meeting booking conversation."""
        
        messages = [
            ("lead", "I'd like to schedule a meeting"),
            ("system", "I'd be happy to help you schedule a meeting. What day works best for you?"),
            ("lead", "Tomorrow at 2pm"),
            ("system", "Great! I have availability around Friday, August 30 at 2:00 PM. Would you like me to book that for you?"),
            ("lead", "Yes, that works"),
            ("system", "Perfect! Your meeting is booked for Friday, August 30 at 2:00 PM with John Smith.")
        ]
        
        return await self.create_conversation_thread(lead_id, messages, franchisor_id)


class BookingFactory:
    """Factory for creating test bookings."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def create_booking(
        self,
        lead_id: uuid.UUID,
        customer_name: str = "John Doe",
        customer_email: str = "<EMAIL>",
        customer_phone: str = "+61412345678",
        service_type: str = "lead_meeting",
        staff_name: str = "John Smith",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        duration_minutes: int = 30,
        timezone: str = "Asia/Kolkata",
        status: str = "scheduled",
        **kwargs
    ) -> Booking:
        """Create a test booking."""
        
        if start_time is None:
            start_time = datetime.now(timezone.utc) + timedelta(days=1)
        if end_time is None:
            end_time = start_time + timedelta(minutes=duration_minutes)
        
        booking_data = {
            "id": uuid.uuid4(),
            "lead_id": lead_id,
            "customer_name": customer_name,
            "customer_email": customer_email,
            "customer_phone": customer_phone,
            "service_type": service_type,
            "staff_name": staff_name,
            "start_time": start_time,
            "end_time": end_time,
            "duration_minutes": duration_minutes,
            "timezone": timezone,
            "status": status,
            "booking_source": "meeting_agent",
            "notes": "Test booking created by factory",
            "is_active": True,
            "is_deleted": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            **kwargs
        }
        
        booking = Booking(**booking_data)
        self.db_session.add(booking)
        await self.db_session.commit()
        await self.db_session.refresh(booking)
        
        return booking
    
    async def create_booking_intent(
        self,
        lead_id: uuid.UUID,
        **kwargs
    ) -> Booking:
        """Create a booking intent (not yet confirmed)."""
        defaults = {
            "status": "intent",
            "notes": "Booking intent created during conversation"
        }
        defaults.update(kwargs)
        return await self.create_booking(lead_id, **defaults)
    
    async def create_confirmed_booking(
        self,
        lead_id: uuid.UUID,
        zoho_booking_id: str = "zoho_123",
        meeting_link: str = "https://zoom.us/meeting/123",
        **kwargs
    ) -> Booking:
        """Create a confirmed booking with external IDs."""
        defaults = {
            "status": "confirmed",
            "zoho_booking_id": zoho_booking_id,
            "meeting_link": meeting_link,
            "booking_url": f"https://bookings.zoho.com/{zoho_booking_id}",
            "confirmation_sent": True
        }
        defaults.update(kwargs)
        return await self.create_booking(lead_id, **defaults)


class EscalationQuestionFactory:
    """Factory for creating test escalation questions."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def create_escalation_question(
        self,
        question: str = "What are your franchise fees?",
        answer: Optional[str] = None,
        category: str = "general",
        confidence_threshold: float = 0.8,
        **kwargs
    ) -> EscalationQuestion:
        """Create an escalation question."""
        
        question_data = {
            "id": uuid.uuid4(),
            "question": question,
            "answer": answer,
            "category": category,
            "confidence_threshold": confidence_threshold,
            "is_active": True,
            "is_deleted": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            **kwargs
        }
        
        escalation_q = EscalationQuestion(**question_data)
        self.db_session.add(escalation_q)
        await self.db_session.commit()
        await self.db_session.refresh(escalation_q)
        
        return escalation_q
    
    async def create_answered_question(
        self,
        question: str = "What are your franchise fees?",
        answer: str = "Our franchise fees start at $50,000 plus GST.",
        **kwargs
    ) -> EscalationQuestion:
        """Create an escalation question with an answer."""
        return await self.create_escalation_question(
            question=question,
            answer=answer,
            **kwargs
        )
    
    async def create_unanswered_question(
        self,
        question: str = "Do you offer financing options?",
        **kwargs
    ) -> EscalationQuestion:
        """Create an unanswered escalation question."""
        return await self.create_escalation_question(
            question=question,
            answer=None,
            **kwargs
        )


# Utility functions for test data

def create_test_lead_data(
    first_name: str = "Test",
    last_name: str = "User",
    phone: str = "+61412345678"
) -> Dict[str, Any]:
    """Create test lead data dictionary."""
    return {
        "first_name": first_name,
        "last_name": last_name,
        "email": f"{first_name.lower()}.{last_name.lower()}@example.com",
        "phone": phone,
        "mobile": phone,
        "location": "Sydney, NSW",
        "status": "New",
        "source": "Test"
    }


def create_test_booking_data(
    lead_id: uuid.UUID,
    days_ahead: int = 1,
    hour: int = 14  # 2 PM
) -> Dict[str, Any]:
    """Create test booking data dictionary."""
    start_time = datetime.now(timezone.utc).replace(
        hour=hour, minute=0, second=0, microsecond=0
    ) + timedelta(days=days_ahead)
    
    return {
        "lead_id": lead_id,
        "customer_name": "Test User",
        "customer_email": "<EMAIL>",
        "customer_phone": "+61412345678",
        "service_type": "lead_meeting",
        "staff_name": "John Smith",
        "start_time": start_time,
        "end_time": start_time + timedelta(minutes=30),
        "duration_minutes": 30,
        "timezone": "Asia/Kolkata",
        "status": "scheduled"
    }


# Test data sets for different scenarios

class TestDataSets:
    """Pre-defined test data sets for common scenarios."""
    
    @staticmethod
    def new_lead_data() -> Dict[str, Any]:
        """Data for a new lead."""
        return create_test_lead_data("New", "Lead", "+61400000001")
    
    @staticmethod
    def qualified_lead_data() -> Dict[str, Any]:
        """Data for a qualified lead."""
        return {
            **create_test_lead_data("Qualified", "Lead", "+61400000002"),
            "status": "Qualified"
        }
    
    @staticmethod
    def contacted_lead_data() -> Dict[str, Any]:
        """Data for a contacted lead."""
        return {
            **create_test_lead_data("Contacted", "Lead", "+61400000003"),
            "status": "Contacted"
        }
    
    @staticmethod
    def meeting_booking_data(lead_id: uuid.UUID) -> Dict[str, Any]:
        """Data for a meeting booking."""
        return create_test_booking_data(lead_id, days_ahead=1, hour=14)
    
    @staticmethod
    def escalation_questions() -> list[Dict[str, Any]]:
        """Common escalation questions."""
        return [
            {
                "question": "What are your franchise fees?",
                "answer": "Our franchise fees start at $50,000 plus GST.",
                "category": "fees"
            },
            {
                "question": "Do you provide training?",
                "answer": "Yes, we provide comprehensive training including 2 weeks of initial training.",
                "category": "training"
            },
            {
                "question": "What ongoing support do you offer?",
                "answer": None,  # Unanswered
                "category": "support"
            }
        ]
