"""
Comprehensive tests for the meeting booking system
Tests Zoho integration, booking workflows, and API endpoints
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock, Mock

from app.models.booking import Booking
from app.models.lead import Lead
from app.services.zoho_bookings_service import ZohoBookingsService
from app.agents.booking_agent import BookingAgent
from app.agents.simple_meeting_booking import SimpleMeetingBookingAgent


@pytest.mark.booking
@pytest.mark.asyncio
class TestMeetingBookingComprehensive:
    """Comprehensive meeting booking system tests"""

    async def test_zoho_bookings_service_integration(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test Zoho Bookings service integration"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            # Configure mock service
            mock_instance = Mock(spec=ZohoBookingsService)
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {
                        "date": "2024-08-26",
                        "time": "10:00",
                        "staff": "saumil",
                        "duration": 30,
                        "service_id": "lead_meeting"
                    },
                    {
                        "date": "2024-08-26", 
                        "time": "14:00",
                        "staff": "frank",
                        "duration": 30,
                        "service_id": "lead_meeting"
                    }
                ]
            })
            
            mock_instance.book_meeting = AsyncMock(return_value={
                "success": True,
                "booking_id": "ZB123456789",
                "meeting_url": "https://zoom.us/j/123456789",
                "confirmation_code": "CONF123"
            })
            
            mock_service.return_value = mock_instance
            
            # Test availability check
            service = ZohoBookingsService()
            availability = await service.get_availability(
                service_type="lead_meeting",
                days_ahead=7
            )
            
            assert availability["success"] == True
            assert len(availability["available_slots"]) == 2
            
            # Test booking
            booking_result = await service.book_meeting(
                customer_name="Test User",
                customer_email="<EMAIL>",
                customer_phone="+919408869824",
                preferred_date="2024-08-26",
                preferred_time="10:00",
                service_type="lead_meeting"
            )
            
            assert booking_result["success"] == True
            assert "booking_id" in booking_result
            assert "meeting_url" in booking_result

    async def test_booking_agent_workflow(
        self,
        db_session,
        test_lead,
        mock_openai
    ):
        """Test booking agent complete workflow"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            # Configure mock service
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {
                        "date": "2024-08-26",
                        "time": "10:00",
                        "staff": "saumil",
                        "duration": 30
                    }
                ]
            })
            mock_instance.book_meeting = AsyncMock(return_value={
                "success": True,
                "booking_id": "ZB123456789",
                "meeting_url": "https://zoom.us/j/123456789"
            })
            mock_service.return_value = mock_instance
            
            # Test booking agent
            booking_agent = BookingAgent({
                "role": "booking_agent",
                "name": "Booking Agent",
                "model": "gpt-4-turbo",
                "temperature": 0.1,
                "max_tokens": 1000
            })
            
            # Test slot detection
            message = "I'm available Monday at 10am or Tuesday at 2pm"
            slots = await booking_agent.detect_time_slots(message)
            
            assert len(slots) >= 1
            
            # Test booking execution
            booking_data = {
                "customer_name": f"{test_lead.first_name} {test_lead.last_name}",
                "customer_email": test_lead.email,
                "customer_phone": test_lead.phone_number,
                "preferred_date": "2024-08-26",
                "preferred_time": "10:00",
                "service_type": "lead_meeting"
            }
            
            result = await booking_agent.execute_booking(booking_data)
            assert result["success"] == True

    async def test_simple_booking_api_endpoints(
        self,
        client,
        db_session,
        test_lead
    ):
        """Test simple booking API endpoints"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {
                        "date": "2024-08-26",
                        "time": "10:00",
                        "staff": "saumil",
                        "duration": 30
                    }
                ]
            })
            mock_service.return_value = mock_instance
            
            # Test availability endpoint
            response = client.post("/api/v1/simple-booking/availability", json={
                "service_type": "lead_meeting",
                "days_ahead": 7,
                "preferred_staff": "saumil"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert "available_slots" in data
            assert len(data["available_slots"]) >= 1

    async def test_booking_database_operations(
        self,
        db_session,
        test_lead
    ):
        """Test booking database operations"""
        
        # Create booking
        booking = Booking(
            id=str(uuid.uuid4()),
            lead_id=test_lead.id,
            customer_name=f"{test_lead.first_name} {test_lead.last_name}",
            customer_email=test_lead.email,
            customer_phone=test_lead.phone_number,
            service_type="lead_meeting",
            booking_date=datetime.now() + timedelta(days=1),
            duration_minutes=30,
            status="confirmed",
            zoho_booking_id="ZB123456789",
            meeting_url="https://zoom.us/j/123456789",
            is_active=True,
            is_deleted=False
        )
        
        db_session.add(booking)
        await db_session.commit()
        await db_session.refresh(booking)
        
        # Verify booking was created
        assert booking.id is not None
        assert booking.lead_id == test_lead.id
        assert booking.status == "confirmed"
        
        # Test booking retrieval
        from sqlalchemy import select
        result = await db_session.execute(
            select(Booking).where(Booking.lead_id == test_lead.id)
        )
        retrieved_booking = result.scalar_one_or_none()
        
        assert retrieved_booking is not None
        assert retrieved_booking.id == booking.id

    async def test_booking_sms_integration(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test booking integration with SMS system"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {
                        "date": "2024-08-26",
                        "time": "10:00",
                        "staff": "saumil",
                        "duration": 30
                    }
                ]
            })
            mock_service.return_value = mock_instance
            
            # Send SMS requesting meeting
            sms_data = test_data_factory.create_sms_webhook_data(
                mo={
                    "sender": "919408869824",
                    "recipient": "+61400000000",
                    "message": "I'd like to schedule a meeting for Monday at 10am"
                }
            )
            
            response = client.post("/api/webhooks/kudosity", json=sms_data)
            assert response.status_code == 200
            
            response_data = response.json()
            assert response_data["success"] == True
            assert response_data["data"]["conversation_stage"] == "scheduling"

    async def test_booking_error_handling(
        self,
        db_session,
        test_lead
    ):
        """Test booking error handling scenarios"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            # Test service unavailable
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(side_effect=Exception("Service unavailable"))
            mock_service.return_value = mock_instance
            
            booking_agent = BookingAgent({
                "role": "booking_agent",
                "name": "Booking Agent",
                "model": "gpt-4-turbo",
                "temperature": 0.1,
                "max_tokens": 1000
            })
            
            # Should handle error gracefully
            try:
                result = await booking_agent.get_availability("lead_meeting", 7)
                assert result["success"] == False
            except Exception:
                # Error handling depends on implementation
                pass

    async def test_booking_confirmation_workflow(
        self,
        client,
        db_session,
        test_lead,
        mock_sms_service,
        test_data_factory
    ):
        """Test complete booking confirmation workflow"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.book_meeting = AsyncMock(return_value={
                "success": True,
                "booking_id": "ZB123456789",
                "meeting_url": "https://zoom.us/j/123456789",
                "confirmation_code": "CONF123"
            })
            mock_service.return_value = mock_instance
            
            # Step 1: Request meeting
            sms_data = test_data_factory.create_sms_webhook_data(
                mo={
                    "sender": "919408869824",
                    "recipient": "+61400000000",
                    "message": "I want to schedule a meeting"
                }
            )
            
            response = client.post("/api/webhooks/kudosity", json=sms_data)
            assert response.status_code == 200
            
            # Step 2: Confirm specific time
            sms_data["mo"]["message"] = "Yes, Monday at 10am works for me"
            response = client.post("/api/webhooks/kudosity", json=sms_data)
            assert response.status_code == 200
            
            response_data = response.json()
            assert response_data["success"] == True

    async def test_booking_cancellation_and_rescheduling(
        self,
        db_session,
        test_lead
    ):
        """Test booking cancellation and rescheduling"""
        
        # Create existing booking
        booking = Booking(
            id=str(uuid.uuid4()),
            lead_id=test_lead.id,
            customer_name=f"{test_lead.first_name} {test_lead.last_name}",
            customer_email=test_lead.email,
            customer_phone=test_lead.phone_number,
            service_type="lead_meeting",
            booking_date=datetime.now() + timedelta(days=1),
            duration_minutes=30,
            status="confirmed",
            zoho_booking_id="ZB123456789",
            is_active=True,
            is_deleted=False
        )
        
        db_session.add(booking)
        await db_session.commit()
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.cancel_booking = AsyncMock(return_value={
                "success": True,
                "message": "Booking cancelled successfully"
            })
            mock_service.return_value = mock_instance
            
            # Test cancellation
            service = ZohoBookingsService()
            result = await service.cancel_booking("ZB123456789")
            
            assert result["success"] == True
            
            # Update booking status
            booking.status = "cancelled"
            await db_session.commit()
            
            # Verify status update
            await db_session.refresh(booking)
            assert booking.status == "cancelled"

    async def test_booking_staff_preferences(
        self,
        db_session,
        test_lead
    ):
        """Test booking with staff preferences"""
        
        with patch('app.services.zoho_bookings_service.ZohoBookingsService') as mock_service:
            mock_instance = Mock()
            mock_instance.get_availability = AsyncMock(return_value={
                "success": True,
                "available_slots": [
                    {
                        "date": "2024-08-26",
                        "time": "10:00",
                        "staff": "saumil",
                        "duration": 30
                    },
                    {
                        "date": "2024-08-26",
                        "time": "14:00", 
                        "staff": "frank",
                        "duration": 30
                    }
                ]
            })
            mock_service.return_value = mock_instance
            
            # Test with staff preference
            service = ZohoBookingsService()
            availability = await service.get_availability(
                service_type="lead_meeting",
                days_ahead=7,
                preferred_staff="saumil"
            )
            
            assert availability["success"] == True
            # Should filter or prioritize saumil's slots
            assert len(availability["available_slots"]) >= 1
