"""
Tests for Short Link Integration with Meeting Booking
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from app.agents.sms_assistant import Andy<PERSON>SAssistant
from app.models.short_link import ShortLink


class TestMeetingShortLinkIntegration:
    """Test cases for meeting booking with short links"""
    
    @pytest.fixture
    def andy_assistant(self):
        """Create AndySMSAssistant instance"""
        return AndySMSAssistant()
    
    @pytest.fixture
    def mock_booking_result(self):
        """Create mock booking result with join URL"""
        result = MagicMock()
        result.success = True
        result.join_url = "https://meet.zoho.com/join/abc123"
        result.booking_id = "booking_123"
        result.error_message = None
        return result
    
    @pytest.fixture
    def mock_slot(self):
        """Create mock meeting slot"""
        slot = MagicMock()
        slot.start_time = datetime(2025, 9, 15, 14, 0)  # Monday 2pm
        slot.staff_name = "<PERSON>"
        return slot
    
    @pytest.mark.asyncio
    async def test_meeting_booking_with_short_link_creation(self, andy_assistant, mock_booking_result, mock_slot):
        """Test meeting booking creates short link for join URL"""
        phone_number = "+1234567890"
        user_message = "Yes, 2pm on Monday works"
        lead_context = {
            "work_background": "Marketing Manager",
            "email": "<EMAIL>"
        }
        
        # Mock the booking process
        with patch('app.agents.sms_assistant.ZohoBookingsService') as mock_zoho_service:
            mock_service_instance = AsyncMock()
            mock_zoho_service.return_value = mock_service_instance
            
            # Mock slot finding
            mock_service_instance.get_available_slots.return_value = [mock_slot]
            mock_service_instance.book_appointment.return_value = mock_booking_result
            
            # Mock short link creation
            with patch('app.agents.sms_assistant.create_short_link') as mock_create_short_link:
                mock_create_short_link.return_value = "https://ghv.li/r/abc123"
                
                # Mock database session
                with patch('app.agents.sms_assistant.get_db_session') as mock_get_db:
                    mock_db = AsyncMock()
                    mock_get_db.return_value.__aenter__.return_value = mock_db
                    
                    # Mock lead data retrieval
                    with patch.object(andy_assistant, '_get_lead_email_by_phone') as mock_get_email, \
                         patch.object(andy_assistant, '_get_lead_name_by_phone') as mock_get_name, \
                         patch.object(andy_assistant, '_parse_requested_time_and_day') as mock_parse, \
                         patch.object(andy_assistant, '_calculate_target_date') as mock_calc_date, \
                         patch.object(andy_assistant, '_find_best_matching_slot') as mock_find_slot:
                        
                        mock_get_email.return_value = "<EMAIL>"
                        mock_get_name.return_value = "John Doe"
                        mock_parse.return_value = ("2pm", "Monday")
                        mock_calc_date.return_value = datetime(2025, 9, 15).date()
                        mock_find_slot.return_value = mock_slot
                        
                        result = await andy_assistant._book_meeting_directly(
                            phone_number, user_message, lead_context
                        )
                        
                        # Verify short link was created
                        mock_create_short_link.assert_called_once()
                        call_args = mock_create_short_link.call_args
                        
                        assert call_args[1]["long_url"] == "https://meet.zoho.com/join/abc123"
                        assert call_args[1]["expires_in_days"] == 7
                        
                        context = call_args[1]["context"]
                        assert context["type"] == "meeting_join"
                        assert context["phone_number"] == phone_number
                        assert context["meeting_time"] == "2:00 PM on Monday"
                        assert context["staff_name"] == "Andy Smith"
                        assert context["booking_id"] == "booking_123"
                        
                        # Verify response includes short link
                        assert "https://ghv.li/r/abc123" in result
                        assert "Join link:" in result
    
    @pytest.mark.asyncio
    async def test_meeting_booking_without_join_url(self, andy_assistant, mock_slot):
        """Test meeting booking when no join URL is available"""
        phone_number = "+1234567890"
        user_message = "Yes, 2pm on Monday works"
        lead_context = {"work_background": "Marketing Manager"}
        
        # Mock booking result without join URL
        mock_booking_result = MagicMock()
        mock_booking_result.success = True
        mock_booking_result.join_url = None  # No join URL
        mock_booking_result.booking_id = "booking_123"
        
        with patch('app.agents.sms_assistant.ZohoBookingsService') as mock_zoho_service:
            mock_service_instance = AsyncMock()
            mock_zoho_service.return_value = mock_service_instance
            
            mock_service_instance.get_available_slots.return_value = [mock_slot]
            mock_service_instance.book_appointment.return_value = mock_booking_result
            
            # Mock lead data retrieval
            with patch.object(andy_assistant, '_get_lead_email_by_phone') as mock_get_email, \
                 patch.object(andy_assistant, '_get_lead_name_by_phone') as mock_get_name, \
                 patch.object(andy_assistant, '_parse_requested_time_and_day') as mock_parse, \
                 patch.object(andy_assistant, '_calculate_target_date') as mock_calc_date, \
                 patch.object(andy_assistant, '_find_best_matching_slot') as mock_find_slot:
                
                mock_get_email.return_value = "<EMAIL>"
                mock_get_name.return_value = "John Doe"
                mock_parse.return_value = ("2pm", "Monday")
                mock_calc_date.return_value = datetime(2025, 9, 15).date()
                mock_find_slot.return_value = mock_slot
                
                # Mock short link creation (should not be called)
                with patch('app.agents.sms_assistant.create_short_link') as mock_create_short_link:
                    result = await andy_assistant._book_meeting_directly(
                        phone_number, user_message, lead_context
                    )
                    
                    # Verify short link was not created
                    mock_create_short_link.assert_not_called()
                    
                    # Verify response does not include join link
                    assert "Join link:" not in result
                    assert "Perfect! I've booked your meeting" in result
    
    @pytest.mark.asyncio
    async def test_meeting_booking_short_link_creation_failure(self, andy_assistant, mock_booking_result, mock_slot):
        """Test meeting booking when short link creation fails"""
        phone_number = "+1234567890"
        user_message = "Yes, 2pm on Monday works"
        lead_context = {"work_background": "Marketing Manager"}
        
        with patch('app.agents.sms_assistant.ZohoBookingsService') as mock_zoho_service:
            mock_service_instance = AsyncMock()
            mock_zoho_service.return_value = mock_service_instance
            
            mock_service_instance.get_available_slots.return_value = [mock_slot]
            mock_service_instance.book_appointment.return_value = mock_booking_result
            
            # Mock lead data retrieval
            with patch.object(andy_assistant, '_get_lead_email_by_phone') as mock_get_email, \
                 patch.object(andy_assistant, '_get_lead_name_by_phone') as mock_get_name, \
                 patch.object(andy_assistant, '_parse_requested_time_and_day') as mock_parse, \
                 patch.object(andy_assistant, '_calculate_target_date') as mock_calc_date, \
                 patch.object(andy_assistant, '_find_best_matching_slot') as mock_find_slot:
                
                mock_get_email.return_value = "<EMAIL>"
                mock_get_name.return_value = "John Doe"
                mock_parse.return_value = ("2pm", "Monday")
                mock_calc_date.return_value = datetime(2025, 9, 15).date()
                mock_find_slot.return_value = mock_slot
                
                # Mock short link creation failure
                with patch('app.agents.sms_assistant.create_short_link') as mock_create_short_link:
                    mock_create_short_link.side_effect = Exception("Database error")
                    
                    result = await andy_assistant._book_meeting_directly(
                        phone_number, user_message, lead_context
                    )
                    
                    # Verify response still includes booking confirmation
                    assert "Perfect! I've booked your meeting" in result
                    assert "Looking forward to our chat!" in result
                    # Should not include join link due to failure
                    assert "Join link:" not in result
    
    @pytest.mark.asyncio
    async def test_meeting_booking_with_booking_url_short_link(self, andy_assistant):
        """Test meeting booking creates short link for booking URL"""
        phone_number = "+1234567890"
        
        # Mock booking result with booking URL
        mock_booking_result = MagicMock()
        mock_booking_result.success = True
        mock_booking_result.booking_url = "https://bookings.zoho.com/book/123"
        mock_booking_result.meeting_link = "https://meet.zoho.com/join/abc123"
        mock_booking_result.booking_id = "booking_123"
        
        # Mock slot
        mock_slot = MagicMock()
        mock_slot.start_time = datetime(2025, 9, 15, 14, 0)
        mock_slot.staff_name = "Andy Smith"
        
        with patch('app.agents.sms_assistant.ZohoBookingsService') as mock_zoho_service:
            mock_service_instance = AsyncMock()
            mock_zoho_service.return_value = mock_service_instance
            
            mock_service_instance.get_available_slots.return_value = [mock_slot]
            mock_service_instance.book_appointment.return_value = mock_booking_result
            
            # Mock lead data retrieval
            with patch.object(andy_assistant, '_get_lead_email_by_phone') as mock_get_email, \
                 patch.object(andy_assistant, '_get_lead_name_by_phone') as mock_get_name, \
                 patch.object(andy_assistant, '_parse_requested_time_and_day') as mock_parse, \
                 patch.object(andy_assistant, '_calculate_target_date') as mock_calc_date, \
                 patch.object(andy_assistant, '_find_best_matching_slot') as mock_find_slot:
                
                mock_get_email.return_value = "<EMAIL>"
                mock_get_name.return_value = "John Doe"
                mock_parse.return_value = ("2pm", "Monday")
                mock_calc_date.return_value = datetime(2025, 9, 15).date()
                mock_find_slot.return_value = mock_slot
                
                # Mock short link creation
                with patch('app.agents.sms_assistant.create_short_link') as mock_create_short_link:
                    mock_create_short_link.return_value = "https://ghv.li/r/booking123"
                    
                    # Mock database session
                    with patch('app.agents.sms_assistant.get_db_session') as mock_get_db:
                        mock_db = AsyncMock()
                        mock_get_db.return_value.__aenter__.return_value = mock_db
                        
                        result = await andy_assistant._book_meeting_directly(
                            phone_number, "Yes, 2pm on Monday works", {}
                        )
                        
                        # Verify short link was created for join URL
                        mock_create_short_link.assert_called_once()
                        call_args = mock_create_short_link.call_args
                        
                        assert call_args[1]["long_url"] == "https://meet.zoho.com/join/abc123"
                        assert call_args[1]["context"]["type"] == "meeting_join"
                        
                        # Verify response includes short link
                        assert "https://ghv.li/r/booking123" in result
                        assert "Join link:" in result


class TestShortLinkContext:
    """Test cases for short link context metadata"""
    
    def test_meeting_join_context(self):
        """Test context structure for meeting join links"""
        context = {
            "type": "meeting_join",
            "phone_number": "+1234567890",
            "meeting_time": "2:00 PM on Monday",
            "staff_name": "Andy Smith",
            "booking_id": "booking_123"
        }
        
        # Verify all required fields are present
        assert context["type"] == "meeting_join"
        assert context["phone_number"] == "+1234567890"
        assert context["meeting_time"] == "2:00 PM on Monday"
        assert context["staff_name"] == "Andy Smith"
        assert context["booking_id"] == "booking_123"
    
    def test_meeting_booking_context(self):
        """Test context structure for meeting booking links"""
        context = {
            "type": "meeting_booking",
            "phone_number": "+1234567890",
            "meeting_time": "Monday at 2pm",
            "booking_id": "booking_123"
        }
        
        # Verify all required fields are present
        assert context["type"] == "meeting_booking"
        assert context["phone_number"] == "+1234567890"
        assert context["meeting_time"] == "Monday at 2pm"
        assert context["booking_id"] == "booking_123"
    
    def test_short_link_expiration_for_meetings(self):
        """Test that meeting short links expire in 7 days"""
        expires_in_days = 7
        
        # This should be the standard expiration for meeting links
        assert expires_in_days == 7
        
        # Verify it's reasonable (not too short, not too long)
        assert 1 <= expires_in_days <= 30
