"""
Tests for Short Link API Endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from datetime import datetime, timedelta

from app.main import app
from app.models.short_link import ShortLink


class TestShortLinkAPI:
    """Test cases for short link API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_short_link(self):
        """Create mock short link"""
        return ShortLink(
            id="123e4567-e89b-12d3-a456-426614174000",
            slug="test123",
            long_url="https://example.com",
            context_json={"type": "test"},
            expires_at=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    def test_redirect_short_link_success(self, client, mock_short_link):
        """Test successful short link redirect"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.resolve_short_link.return_value = "https://example.com"
            
            response = client.get("/api/v1/shortlinks/r/test123")
            
            assert response.status_code == 302
            assert response.headers["location"] == "https://example.com"
    
    def test_redirect_short_link_not_found(self, client):
        """Test redirect for non-existent short link"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.resolve_short_link.return_value = None
            
            response = client.get("/api/v1/shortlinks/r/nonexistent")
            
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
    
    def test_redirect_short_link_expired(self, client):
        """Test redirect for expired short link"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.resolve_short_link.return_value = None
            
            response = client.get("/api/v1/shortlinks/r/expired123")
            
            assert response.status_code == 404
            assert "expired" in response.json()["detail"].lower()
    
    def test_create_short_link_success(self, client):
        """Test successful short link creation"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.return_value = "https://ghv.li/r/test123"
            
            response = client.post(
                "/api/v1/shortlinks/create",
                params={
                    "long_url": "https://example.com",
                    "context": '{"type": "test"}',
                    "expires_in_days": 7
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["short_url"] == "https://ghv.li/r/test123"
            assert data["long_url"] == "https://example.com"
            assert data["slug"] == "test123"
            assert data["context"] == {"type": "test"}
    
    def test_create_short_link_custom_slug(self, client):
        """Test short link creation with custom slug"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.return_value = "https://ghv.li/r/custom123"
            
            response = client.post(
                "/api/v1/shortlinks/create",
                params={
                    "long_url": "https://example.com",
                    "custom_slug": "custom123"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["slug"] == "custom123"
            assert data["short_url"] == "https://ghv.li/r/custom123"
    
    def test_create_short_link_invalid_url(self, client):
        """Test short link creation with invalid URL"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.side_effect = ValueError("Long URL cannot be empty")
            
            response = client.post(
                "/api/v1/shortlinks/create",
                params={"long_url": ""}
            )
            
            assert response.status_code == 400
            assert "Long URL cannot be empty" in response.json()["detail"]
    
    def test_create_short_link_custom_slug_taken(self, client):
        """Test short link creation with taken custom slug"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.side_effect = ValueError("Custom slug 'taken123' is already taken")
            
            response = client.post(
                "/api/v1/shortlinks/create",
                params={
                    "long_url": "https://example.com",
                    "custom_slug": "taken123"
                }
            )
            
            assert response.status_code == 400
            assert "already taken" in response.json()["detail"]
    
    def test_get_short_link_info_success(self, client, mock_short_link):
        """Test getting short link information"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.get_short_link.return_value = mock_short_link
            
            response = client.get("/api/v1/shortlinks/info/test123")
            
            assert response.status_code == 200
            data = response.json()
            assert data["slug"] == "test123"
            assert data["long_url"] == "https://example.com"
            assert data["context"] == {"type": "test"}
            assert data["is_expired"] is False
            assert "created_at" in data
            assert "updated_at" in data
    
    def test_get_short_link_info_not_found(self, client):
        """Test getting info for non-existent short link"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.get_short_link.return_value = None
            
            response = client.get("/api/v1/shortlinks/info/nonexistent")
            
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
    
    def test_delete_short_link_success(self, client):
        """Test successful short link deletion"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.delete_short_link.return_value = True
            
            response = client.delete("/api/v1/shortlinks/test123")
            
            assert response.status_code == 200
            assert "deleted successfully" in response.json()["message"]
    
    def test_delete_short_link_not_found(self, client):
        """Test deleting non-existent short link"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.delete_short_link.return_value = False
            
            response = client.delete("/api/v1/shortlinks/nonexistent")
            
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
    
    def test_cleanup_expired_links(self, client):
        """Test cleanup of expired links"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.cleanup_expired_links.return_value = 5
            
            response = client.post("/api/v1/shortlinks/cleanup")
            
            assert response.status_code == 200
            data = response.json()
            assert data["deleted_count"] == 5
            assert "Cleaned up 5 expired short links" in data["message"]
    
    def test_cleanup_expired_links_error(self, client):
        """Test cleanup with error"""
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.cleanup_expired_links.side_effect = Exception("Database error")
            
            response = client.post("/api/v1/shortlinks/cleanup")
            
            assert response.status_code == 500
            assert "Internal server error" in response.json()["detail"]


class TestShortLinkIntegration:
    """Integration tests for short link functionality"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    def test_create_and_redirect_flow(self, client):
        """Test complete flow: create short link and redirect"""
        # Create short link
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.return_value = "https://ghv.li/r/test123"
            
            create_response = client.post(
                "/api/v1/shortlinks/create",
                params={"long_url": "https://example.com"}
            )
            
            assert create_response.status_code == 200
            short_url = create_response.json()["short_url"]
            assert short_url == "https://ghv.li/r/test123"
        
        # Redirect short link
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.resolve_short_link.return_value = "https://example.com"
            
            redirect_response = client.get("/api/v1/shortlinks/r/test123")
            
            assert redirect_response.status_code == 302
            assert redirect_response.headers["location"] == "https://example.com"
    
    def test_short_link_with_expiration(self, client):
        """Test short link with expiration"""
        expires_at = datetime.utcnow() + timedelta(days=7)
        
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.return_value = "https://ghv.li/r/expires123"
            
            response = client.post(
                "/api/v1/shortlinks/create",
                params={
                    "long_url": "https://example.com",
                    "expires_in_days": 7
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["expires_at"] is not None
            assert data["short_url"] == "https://ghv.li/r/expires123"
    
    def test_short_link_with_context(self, client):
        """Test short link with context metadata"""
        context = {
            "type": "meeting_join",
            "phone_number": "+1234567890",
            "meeting_time": "Monday at 2pm"
        }
        
        with patch('app.api.v1.endpoints.shortlinks.shortener_service') as mock_service:
            mock_service.create_short_link.return_value = "https://ghv.li/r/context123"
            
            response = client.post(
                "/api/v1/shortlinks/create",
                params={
                    "long_url": "https://meet.example.com/join/123",
                    "context": str(context).replace("'", '"')  # Convert to JSON string
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["context"] == context
            assert data["short_url"] == "https://ghv.li/r/context123"
