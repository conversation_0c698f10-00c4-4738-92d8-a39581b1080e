"""
Tests for Short Link Service
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import AsyncMock, patch

from app.services.shortener import ShortenerService, create_short_link, resolve_short_link
from app.models.short_link import ShortLink
from app.core.config.settings import settings


class TestShortenerService:
    """Test cases for ShortenerService"""
    
    @pytest.fixture
    def shortener_service(self):
        """Create a ShortenerService instance"""
        return ShortenerService()
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session"""
        return AsyncMock(spec=AsyncSession)
    
    def test_generate_slug(self, shortener_service):
        """Test slug generation"""
        slug = shortener_service._generate_slug(8)
        assert len(slug) == 8
        assert all(c in shortener_service.BASE62_CHARS for c in slug)
        
        # Test different lengths
        for length in range(8, 13):
            slug = shortener_service._generate_slug(length)
            assert len(slug) == length
    
    @pytest.mark.asyncio
    async def test_is_slug_available(self, shortener_service, mock_db):
        """Test slug availability checking"""
        # Mock no existing slug
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        result = await shortener_service._is_slug_available(mock_db, "test123")
        assert result is True
        
        # Mock existing slug
        mock_short_link = ShortLink(slug="test123", long_url="https://example.com")
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_short_link
        result = await shortener_service._is_slug_available(mock_db, "test123")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_generate_unique_slug(self, shortener_service, mock_db):
        """Test unique slug generation"""
        # Mock slug availability - first attempt fails, second succeeds
        mock_db.execute.return_value.scalar_one_or_none.side_effect = [
            ShortLink(slug="abc12345", long_url="https://example.com"),  # First slug taken
            None  # Second slug available
        ]
        
        with patch.object(shortener_service, '_generate_slug') as mock_generate:
            mock_generate.side_effect = ["abc12345", "def67890"]
            
            slug = await shortener_service._generate_unique_slug(mock_db)
            assert slug == "def67890"
            assert mock_generate.call_count == 2
    
    @pytest.mark.asyncio
    async def test_create_short_link_success(self, shortener_service, mock_db):
        """Test successful short link creation"""
        long_url = "https://example.com/very/long/url"
        context = {"type": "test", "user_id": "123"}
        expires_at = datetime.utcnow() + timedelta(days=7)
        
        # Mock slug generation and availability
        with patch.object(shortener_service, '_generate_unique_slug') as mock_generate:
            mock_generate.return_value = "test123"
            
            # Mock database operations
            mock_short_link = ShortLink(
                slug="test123",
                long_url=long_url,
                context_json=context,
                expires_at=expires_at
            )
            mock_db.add = AsyncMock()
            mock_db.commit = AsyncMock()
            mock_db.refresh = AsyncMock()
            
            result = await shortener_service.create_short_link(
                db=mock_db,
                long_url=long_url,
                context=context,
                expires_at=expires_at
            )
            
            expected_url = f"{settings.SHORTLINK_BASE_URL}/r/test123"
            assert result == expected_url
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_short_link_custom_slug(self, shortener_service, mock_db):
        """Test short link creation with custom slug"""
        long_url = "https://example.com"
        custom_slug = "custom123"
        
        # Mock slug availability
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        # Mock database operations
        mock_db.add = AsyncMock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        result = await shortener_service.create_short_link(
            db=mock_db,
            long_url=long_url,
            custom_slug=custom_slug
        )
        
        expected_url = f"{settings.SHORTLINK_BASE_URL}/r/{custom_slug}"
        assert result == expected_url
    
    @pytest.mark.asyncio
    async def test_create_short_link_custom_slug_taken(self, shortener_service, mock_db):
        """Test short link creation with taken custom slug"""
        long_url = "https://example.com"
        custom_slug = "taken123"
        
        # Mock slug is taken
        mock_short_link = ShortLink(slug=custom_slug, long_url="https://other.com")
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_short_link
        
        with pytest.raises(ValueError, match="Custom slug 'taken123' is already taken"):
            await shortener_service.create_short_link(
                db=mock_db,
                long_url=long_url,
                custom_slug=custom_slug
            )
    
    @pytest.mark.asyncio
    async def test_create_short_link_invalid_url(self, shortener_service, mock_db):
        """Test short link creation with invalid URL"""
        with pytest.raises(ValueError, match="Long URL cannot be empty"):
            await shortener_service.create_short_link(
                db=mock_db,
                long_url=""
            )
    
    @pytest.mark.asyncio
    async def test_get_short_link(self, shortener_service, mock_db):
        """Test getting short link by slug"""
        slug = "test123"
        mock_short_link = ShortLink(
            slug=slug,
            long_url="https://example.com",
            context_json={"type": "test"}
        )
        
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_short_link
        
        result = await shortener_service.get_short_link(mock_db, slug)
        
        assert result == mock_short_link
        assert result.slug == slug
        assert result.long_url == "https://example.com"
    
    @pytest.mark.asyncio
    async def test_get_short_link_not_found(self, shortener_service, mock_db):
        """Test getting non-existent short link"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await shortener_service.get_short_link(mock_db, "nonexistent")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_resolve_short_link_success(self, shortener_service, mock_db):
        """Test successful short link resolution"""
        slug = "test123"
        long_url = "https://example.com"
        
        mock_short_link = ShortLink(
            slug=slug,
            long_url=long_url,
            expires_at=None  # Not expired
        )
        
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_short_link
        
        result = await shortener_service.resolve_short_link(mock_db, slug)
        
        assert result == long_url
    
    @pytest.mark.asyncio
    async def test_resolve_short_link_not_found(self, shortener_service, mock_db):
        """Test resolving non-existent short link"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await shortener_service.resolve_short_link(mock_db, "nonexistent")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_resolve_short_link_expired(self, shortener_service, mock_db):
        """Test resolving expired short link"""
        slug = "expired123"
        expired_date = datetime.utcnow() - timedelta(days=1)
        
        mock_short_link = ShortLink(
            slug=slug,
            long_url="https://example.com",
            expires_at=expired_date
        )
        
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_short_link
        
        result = await shortener_service.resolve_short_link(mock_db, slug)
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_delete_short_link_success(self, shortener_service, mock_db):
        """Test successful short link deletion"""
        slug = "test123"
        mock_short_link = ShortLink(slug=slug, long_url="https://example.com")
        
        mock_db.execute.return_value.scalar_one_or_none.return_value = mock_short_link
        mock_db.delete = AsyncMock()
        mock_db.commit = AsyncMock()
        
        result = await shortener_service.delete_short_link(mock_db, slug)
        
        assert result is True
        mock_db.delete.assert_called_once_with(mock_short_link)
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_short_link_not_found(self, shortener_service, mock_db):
        """Test deleting non-existent short link"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await shortener_service.delete_short_link(mock_db, "nonexistent")
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_links(self, shortener_service, mock_db):
        """Test cleanup of expired links"""
        expired_date = datetime.utcnow() - timedelta(days=1)
        expired_link1 = ShortLink(
            slug="expired1",
            long_url="https://example1.com",
            expires_at=expired_date
        )
        expired_link2 = ShortLink(
            slug="expired2",
            long_url="https://example2.com",
            expires_at=expired_date
        )
        
        mock_db.execute.return_value.scalars.return_value.all.return_value = [
            expired_link1, expired_link2
        ]
        mock_db.delete = AsyncMock()
        mock_db.commit = AsyncMock()
        
        result = await shortener_service.cleanup_expired_links(mock_db)
        
        assert result == 2
        assert mock_db.delete.call_count == 2
        mock_db.commit.assert_called_once()


class TestShortLinkModel:
    """Test cases for ShortLink model"""
    
    def test_short_link_creation(self):
        """Test ShortLink model creation"""
        short_link = ShortLink(
            slug="test123",
            long_url="https://example.com",
            context_json={"type": "test"}
        )
        
        assert short_link.slug == "test123"
        assert short_link.long_url == "https://example.com"
        assert short_link.context_json == {"type": "test"}
        assert short_link.expires_at is None
        assert not short_link.is_expired()
    
    def test_short_link_expiration(self):
        """Test ShortLink expiration logic"""
        # Not expired
        future_date = datetime.utcnow() + timedelta(days=1)
        short_link = ShortLink(
            slug="test123",
            long_url="https://example.com",
            expires_at=future_date
        )
        assert not short_link.is_expired()
        
        # Expired
        past_date = datetime.utcnow() - timedelta(days=1)
        short_link.expires_at = past_date
        assert short_link.is_expired()
    
    def test_short_link_to_dict(self):
        """Test ShortLink to_dict method"""
        expires_at = datetime.utcnow() + timedelta(days=1)
        short_link = ShortLink(
            slug="test123",
            long_url="https://example.com",
            context_json={"type": "test"},
            expires_at=expires_at
        )
        
        result = short_link.to_dict()
        
        assert result["slug"] == "test123"
        assert result["long_url"] == "https://example.com"
        assert result["context_json"] == {"type": "test"}
        assert result["expires_at"] == expires_at.isoformat()
        assert result["is_expired"] is False
        assert "id" in result
        assert "created_at" in result
        assert "updated_at" in result


class TestConvenienceFunctions:
    """Test cases for convenience functions"""
    
    @pytest.mark.asyncio
    async def test_create_short_link_function(self, mock_db):
        """Test create_short_link convenience function"""
        long_url = "https://example.com"
        context = {"type": "test"}
        
        with patch('app.services.shortener.shortener_service') as mock_service:
            mock_service.create_short_link.return_value = "https://ghv.li/r/test123"
            
            result = await create_short_link(
                db=mock_db,
                long_url=long_url,
                context=context
            )
            
            assert result == "https://ghv.li/r/test123"
            mock_service.create_short_link.assert_called_once_with(
                mock_db, long_url, context, None, None
            )
    
    @pytest.mark.asyncio
    async def test_resolve_short_link_function(self, mock_db):
        """Test resolve_short_link convenience function"""
        slug = "test123"
        
        with patch('app.services.shortener.shortener_service') as mock_service:
            mock_service.resolve_short_link.return_value = "https://example.com"
            
            result = await resolve_short_link(mock_db, slug)
            
            assert result == "https://example.com"
            mock_service.resolve_short_link.assert_called_once_with(mock_db, slug)
