"""
Comprehensive tests for Coochie Hydrogreen workflow
Tests all conversation paths, follow-up suppression, timezone handling, and regression prevention.
"""

import pytest
import os
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession

from app.agents.coochie_workflow_agent import Coochie<PERSON>orkflowAgent, CoochieWorkflowStage
from app.core.followup_suppression import followup_suppressor, FollowUpGuard
from app.services.coochie_conversation_service import coochie_conversation_service
from app.models.lead import Lead
from app.models.conversation_session import ConversationSession


class TestCoochieWorkflowAgent:
    """Test the Coochie workflow agent"""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session"""
        db = AsyncMock(spec=AsyncSession)
        return db
    
    @pytest.fixture
    def mock_lead(self):
        """Mock lead object"""
        lead = MagicMock(spec=Lead)
        lead.id = "test-lead-id"
        lead.first_name = "<PERSON>"
        lead.last_name = "<PERSON><PERSON>"
        lead.phone_number = "+61400000000"
        lead.budget = "$100,000"
        return lead
    
    @pytest.fixture
    def mock_session(self):
        """Mock conversation session"""
        session = MagicMock(spec=ConversationSession)
        session.id = "test-session-id"
        session.workflow_stage = "introduction"
        session.messages_count = 0
        session.context = {}
        return session
    
    @pytest.fixture
    def workflow_agent(self):
        """Create workflow agent with mocked dependencies"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            agent = CoochieWorkflowAgent()
            return agent
    
    @pytest.mark.asyncio
    async def test_workflow_enabled_check(self, workflow_agent):
        """Test that workflow is enabled when feature flag is set"""
        assert workflow_agent.workflow_enabled is True
    
    @pytest.mark.asyncio
    async def test_introduction_stage_initial_contact(self, workflow_agent, mock_session, mock_lead):
        """Test introduction stage for initial contact"""
        mock_session.messages_count = 0
        
        result = await workflow_agent._handle_introduction(mock_session, mock_lead, "")
        
        assert "Hi John, My name is Andy" in result["response"]
        assert "Coochie Hydrogreen franchise" in result["response"]
        assert result["stage"] == "introduction"
        assert result["next_stage"] == "work_background"
        assert result["awaiting_response"] is True
    
    @pytest.mark.asyncio
    async def test_introduction_stage_has_time_response(self, workflow_agent, mock_session, mock_lead):
        """Test introduction stage when lead has time"""
        mock_session.messages_count = 1
        
        with patch.object(workflow_agent, '_analyze_intent') as mock_analyze:
            mock_analyze.return_value = {"has_time": True, "no_time": False}
            
            with patch('app.services.coochie_conversation_service.coochie_conversation_service.generate_natural_response') as mock_natural:
                mock_natural.return_value = "Great! Thanks for your enquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?"
                
                result = await workflow_agent._handle_introduction(mock_session, mock_lead, "Yes, I have time")
                
                assert result["stage"] == "work_background"
                assert result["next_stage"] == "work_background"
                assert "what you do for work" in result["response"]
    
    @pytest.mark.asyncio
    async def test_work_background_corporate(self, workflow_agent, mock_session, mock_lead):
        """Test work background qualification for corporate background"""
        with patch.object(workflow_agent, '_analyze_work_background') as mock_analyze:
            mock_analyze.return_value = {
                "type": workflow_agent.WorkBackground.CORPORATE,
                "reasoning": "Office work"
            }
            
            with patch.object(workflow_agent, '_generate_acknowledgment') as mock_ack:
                mock_ack.return_value = "That sounds interesting"
                
                result = await workflow_agent._handle_work_background(mock_session, mock_lead, "I'm a marketing manager")
                
                assert result["stage"] == "motivation"
                assert "different background" in result["response"]
                assert "context_update" in result
    
    @pytest.mark.asyncio
    async def test_work_background_trades(self, workflow_agent, mock_session, mock_lead):
        """Test work background qualification for trades background"""
        with patch.object(workflow_agent, '_analyze_work_background') as mock_analyze:
            mock_analyze.return_value = {
                "type": workflow_agent.WorkBackground.TRADES,
                "reasoning": "Hands-on work"
            }
            
            with patch.object(workflow_agent, '_generate_acknowledgment') as mock_ack:
                mock_ack.return_value = "Great"
                
                result = await workflow_agent._handle_work_background(mock_session, mock_lead, "I'm a plumber")
                
                assert result["stage"] == "motivation"
                assert "what made you enquire about this franchise opportunity?" in result["response"]
    
    @pytest.mark.asyncio
    async def test_budget_confirmed(self, workflow_agent, mock_session, mock_lead):
        """Test budget stage when budget is confirmed"""
        with patch.object(workflow_agent, '_analyze_budget_response') as mock_analyze:
            mock_analyze.return_value = {"confirmed": True, "declined": False}
            
            result = await workflow_agent._handle_budget(mock_session, mock_lead, "Yes, that's correct")
            
            assert result["stage"] == "scheduling"
            assert "$120K" in result["response"]
            assert "50% in advance" in result["response"]
    
    @pytest.mark.asyncio
    async def test_budget_declined(self, workflow_agent, mock_session, mock_lead):
        """Test budget stage when budget is declined"""
        with patch.object(workflow_agent, '_analyze_budget_response') as mock_analyze:
            mock_analyze.return_value = {"confirmed": False, "declined": True}
            
            result = await workflow_agent._handle_budget(mock_session, mock_lead, "No, I don't have that much")
            
            assert result["stage"] == "budget"
            assert "How much funds do you have access to invest?" in result["response"]
    
    @pytest.mark.asyncio
    async def test_scheduling_positive_response(self, workflow_agent, mock_session, mock_lead):
        """Test scheduling stage with positive response"""
        with patch.object(workflow_agent, '_analyze_scheduling_response') as mock_analyze:
            mock_analyze.return_value = {"positive_response": True}
            
            with patch.object(workflow_agent, '_generate_scheduling_options') as mock_options:
                mock_options.return_value = {
                    "time1": "2:00 PM",
                    "day1": "Monday",
                    "time2": "10:00 AM",
                    "day2": "Tuesday"
                }
                
                result = await workflow_agent._handle_scheduling(mock_session, mock_lead, "That sounds good")
                
                assert result["stage"] == "confirmation"
                assert "2:00 PM" in result["response"]
                assert "Monday" in result["response"]
                assert "10:00 AM" in result["response"]
    
    @pytest.mark.asyncio
    async def test_confirmation_meeting_booked(self, workflow_agent, mock_session, mock_lead):
        """Test confirmation stage when meeting is confirmed"""
        with patch.object(workflow_agent, '_analyze_confirmation_response') as mock_analyze:
            mock_analyze.return_value = {"confirmed": True, "selected_time": "2:00 PM Monday"}
            
            with patch.object(workflow_agent, '_book_meeting') as mock_book:
                mock_book.return_value = {"success": True, "booking_id": "test-booking"}
                
                result = await workflow_agent._handle_confirmation(mock_session, mock_lead, "Yes, 2:00 PM Monday works")
                
                assert result["stage"] == "completed"
                assert result["awaiting_response"] is False
                assert "Thanks for the confirmation" in result["response"]
    
    @pytest.mark.asyncio
    async def test_objection_handling_no_value(self, workflow_agent, mock_session, mock_lead):
        """Test objection handling for 'no value' objection"""
        with patch.object(workflow_agent, '_analyze_objection') as mock_analyze:
            mock_analyze.return_value = {"type": "no_value"}
            
            result = await workflow_agent._handle_objection_or_general(mock_session, mock_lead, "I don't see the value")
            
            assert "Australia's largest lawn care company" in result["response"]
            assert result["objection_handled"] == "no_value"
    
    @pytest.mark.asyncio
    async def test_objection_handling_not_heard(self, workflow_agent, mock_session, mock_lead):
        """Test objection handling for 'not heard' objection"""
        with patch.object(workflow_agent, '_analyze_objection') as mock_analyze:
            mock_analyze.return_value = {"type": "not_heard"}
            
            result = await workflow_agent._handle_objection_or_general(mock_session, mock_lead, "I haven't heard of you")
            
            assert "30 years" in result["response"]
            assert "NSW & QLD" in result["response"]


class TestFollowUpSuppression:
    """Test follow-up suppression system"""

    @pytest.fixture
    def suppressor(self):
        """Create suppressor with enabled flag"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            from app.core.followup_suppression import FollowUpSuppressor
            return FollowUpSuppressor()

    def test_suppression_enabled(self, suppressor):
        """Test that suppression is enabled when flag is set"""
        assert suppressor.is_suppression_enabled() is True

    def test_suppress_followup(self, suppressor):
        """Test follow-up suppression"""
        result = suppressor.suppress_followup("test_context", lead_id="test-lead")

        assert result["suppressed"] is True
        assert result["success"] is False
        assert result["reason"] == "WORKFLOW_ANDY_NO_FOLLOWUPS enabled"

    def test_followup_guard_should_suppress(self):
        """Test FollowUpGuard suppression check"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            assert FollowUpGuard.should_suppress() is True

    def test_followup_guard_suppress_and_return(self):
        """Test FollowUpGuard suppress and return"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            result = FollowUpGuard.suppress_and_return("test_operation")
            assert result["suppressed"] is True

    @pytest.mark.asyncio
    async def test_sms_onboarding_suppression(self):
        """Test that SMS onboarding is suppressed"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            from app.core.followup_suppression import block_followup_if_enabled

            result = block_followup_if_enabled("sms_onboarding_scheduling", lead_id="test-lead")

            assert result is not None
            assert result["suppressed"] is True
            assert result["reason"] == "WORKFLOW_ANDY_NO_FOLLOWUPS enabled"

    @pytest.mark.asyncio
    async def test_sms_scheduler_suppression(self):
        """Test that SMS scheduler is suppressed"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            from app.utils.sms_scheduler import schedule_sms
            from datetime import datetime, timedelta

            future_time = datetime.now() + timedelta(minutes=30)
            task_id = schedule_sms("test-lead", "Test message", future_time)

            # Should return a suppressed task ID
            assert task_id.startswith("suppressed_task_")

    @pytest.mark.asyncio
    async def test_periodic_task_suppression(self):
        """Test that periodic tasks are suppressed"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            from app.core.celery_app import _get_beat_schedule

            schedule = _get_beat_schedule()

            # Should not contain follow-up related tasks
            assert "detect-new-leads-every-30-minutes" not in schedule
            assert "cleanup-completed-followups-every-hour" not in schedule

    @pytest.mark.asyncio
    async def test_lead_sms_integration_suppression(self):
        """Test that lead SMS integration scheduling is suppressed"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            from app.core.followup_suppression import block_followup_if_enabled

            result = block_followup_if_enabled(
                "lead_sms_integration_scheduling",
                lead_id="test-lead",
                phone_number="+61400000000"
            )

            assert result is not None
            assert result["suppressed"] is True


class TestOpenAIPoweredConversation:
    """Test OpenAI-powered conversation responses"""

    @pytest.fixture
    def mock_openai_service(self):
        """Mock OpenAI service"""
        with patch('app.services.openai_service.openai_service') as mock_service:
            yield mock_service

    @pytest.mark.asyncio
    async def test_openai_service_initialization(self):
        """Test OpenAI service initializes correctly"""
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            from app.services.openai_service import OpenAIService
            service = OpenAIService()
            assert service.api_key == "test-key"
            assert service.default_model == "gpt-4"

    @pytest.mark.asyncio
    async def test_generate_natural_response(self, mock_openai_service):
        """Test natural response generation"""
        mock_openai_service.enhance_response_with_workflow.return_value = "Great! Thanks for your enquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?"

        result = await coochie_conversation_service.generate_natural_response(
            user_message="Yes, I have time",
            workflow_response="Great, thanks for your enquiry. I will answer any questions you may have and just to start with can you please tell me what you do for work?",
            context={"stage": "introduction_to_work_background"}
        )

        assert "Great!" in result
        assert "what you do for work" in result
        mock_openai_service.enhance_response_with_workflow.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_acknowledgment(self, mock_openai_service):
        """Test acknowledgment generation"""
        mock_openai_service.generate_acknowledgment.return_value = "That sounds interesting"

        result = await coochie_conversation_service.generate_acknowledgment("I work as a marketing manager")

        assert result == "That sounds interesting"
        mock_openai_service.generate_acknowledgment.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_intent_with_openai(self):
        """Test intent analysis using OpenAI"""
        with patch('app.services.openai_service.openai_service') as mock_service:
            mock_service.analyze_intent.return_value = {
                "has_time": True,
                "no_time": False,
                "proposed_time": None
            }

            from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
            agent = CoochieWorkflowAgent()

            result = await agent._analyze_intent("Yes, I have time", "introduction_response")

            assert result["has_time"] is True
            assert result["no_time"] is False
            mock_service.analyze_intent.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_work_background_with_openai(self):
        """Test work background analysis using OpenAI"""
        with patch('app.services.openai_service.openai_service') as mock_service:
            mock_service.analyze_intent.return_value = {
                "type": "corporate",
                "reasoning": "Office-based management role"
            }

            from app.agents.coochie_workflow_agent import CoochieWorkflowAgent, WorkBackground
            agent = CoochieWorkflowAgent()

            result = await agent._analyze_work_background("I'm a marketing manager")

            assert result["type"] == WorkBackground.CORPORATE
            assert "management" in result["reasoning"]
            mock_service.analyze_intent.assert_called_once()

    @pytest.mark.asyncio
    async def test_conversation_style_compliance(self):
        """Test that conversation responses follow style guidelines"""
        # Test the conversation style configuration
        style = coochie_conversation_service.conversation_style

        assert style["tone"] == "conversational, warm, approachable"
        assert "colloquial expressions" in style["style"]
        assert "contractions" in style["contractions"]
        assert "never use emojis" in style["no_emojis"]
        assert "acknowledge" in style["acknowledge_then_progress"]

    @pytest.mark.asyncio
    async def test_emoji_removal_comprehensive(self):
        """Test comprehensive emoji removal"""
        test_cases = [
            ("Hello 😊 world 🌍!", "Hello  world !"),
            ("Great! 👍 That sounds interesting 🤔", "Great!  That sounds interesting "),
            ("No emojis here", "No emojis here"),
            ("🎉🎊🎈", ""),
            ("Mixed 😀 content 🚀 with text", "Mixed  content  with text")
        ]

        for input_text, expected in test_cases:
            result = coochie_conversation_service._remove_emojis(input_text)
            assert result.strip() == expected.strip()

    @pytest.mark.asyncio
    async def test_response_compliance_validation(self):
        """Test response compliance validation"""
        # Test compliant response
        compliant_response = "Great! That sounds interesting. What made you enquire about this opportunity?"
        result = await coochie_conversation_service.validate_response_compliance(compliant_response)

        assert result["compliant"] is True
        assert result["has_emojis"] is False
        assert len(result["issues"]) == 0

        # Test non-compliant response with emojis
        non_compliant_response = "Great! 😊 That sounds interesting!"
        result = await coochie_conversation_service.validate_response_compliance(non_compliant_response)

        assert result["compliant"] is False
        assert result["has_emojis"] is True
        assert "Contains emojis" in result["issues"]

        # Test overly long response
        long_response = "This is a very long response " * 20
        result = await coochie_conversation_service.validate_response_compliance(long_response)

        assert "Response too long" in result["issues"]

    @pytest.mark.asyncio
    async def test_openai_error_handling(self, mock_openai_service):
        """Test error handling in OpenAI calls"""
        # Test when OpenAI service fails
        mock_openai_service.enhance_response_with_workflow.side_effect = Exception("API Error")

        result = await coochie_conversation_service.generate_natural_response(
            user_message="Test message",
            workflow_response="Fallback response",
            context={"stage": "test"}
        )

        # Should fallback to workflow response
        assert result == "Fallback response"

    @pytest.mark.asyncio
    async def test_conversation_context_handling(self, mock_openai_service):
        """Test conversation context handling"""
        mock_openai_service.enhance_response_with_workflow.return_value = "Enhanced response"

        # Test with different contexts
        contexts = [
            {"stage": "introduction"},
            {"stage": "work_background"},
            {"stage": "motivation"},
            {"stage": "budget"},
            {"stage": "scheduling"}
        ]

        for context in contexts:
            result = await coochie_conversation_service.generate_natural_response(
                user_message="Test",
                workflow_response="Test response",
                context=context
            )

            assert result == "Enhanced response"

        # Verify OpenAI service was called with correct context
        assert mock_openai_service.enhance_response_with_workflow.call_count == len(contexts)


class TestTimezoneHandling:
    """Test timezone handling"""
    
    @pytest.mark.asyncio
    async def test_default_timezone_setting(self):
        """Test that default timezone is set correctly"""
        with patch.dict(os.environ, {"DEFAULT_TIMEZONE": "Asia/Kolkata"}):
            agent = CoochieWorkflowAgent()
            assert agent.default_timezone == "Asia/Kolkata"
    
    @pytest.mark.asyncio
    async def test_scheduling_options_timezone_aware(self, workflow_agent, mock_lead):
        """Test that scheduling options are timezone aware"""
        options = await workflow_agent._generate_scheduling_options(mock_lead)
        
        # Should have time and day options
        assert "time1" in options
        assert "day1" in options
        assert "time2" in options
        assert "day2" in options


class TestRegressionPrevention:
    """Test that existing functionality is not broken"""
    
    @pytest.mark.asyncio
    async def test_workflow_disabled_fallback(self):
        """Test that when workflow is disabled, system falls back gracefully"""
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "false"}):
            agent = CoochieWorkflowAgent()
            
            result = await agent.process_message("test-lead", "+61400000000", "Hello", AsyncMock())
            
            assert result["response"] == "Workflow not enabled"
            assert result["stage"] == "disabled"
    
    def test_feature_flag_validation(self):
        """Test feature flag validation"""
        from app.core.followup_suppression import validate_suppression_config
        
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "true"}):
            result = validate_suppression_config()
            assert result is True
        
        with patch.dict(os.environ, {"WORKFLOW_ANDY_NO_FOLLOWUPS": "false"}):
            result = validate_suppression_config()
            assert result is False


if __name__ == "__main__":
    pytest.main([__file__])
