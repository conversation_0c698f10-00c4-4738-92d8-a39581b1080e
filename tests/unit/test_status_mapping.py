"""
Unit Tests - Lead Status Mapping
Tests for mapping user responses to lead status updates
"""

import pytest
from datetime import datetime, timezone

from app.services.lead_status_service import LeadStatusService, StatusMapping
from app.models.lead import Lead


@pytest.mark.unit
class TestLeadStatusMapping:
    """Test lead status mapping functionality."""
    
    @pytest.fixture
    def status_service(self):
        """Create lead status service instance."""
        return LeadStatusService()
    
    @pytest.fixture
    def sample_lead(self):
        """Create sample lead for testing."""
        return Lead(
            id="test-lead-123",
            first_name="<PERSON>",
            last_name="<PERSON>e",
            email="<EMAIL>",
            phone="+61412345678",
            status="New",
            created_at=datetime.now(timezone.utc)
        )
    
    def test_not_interested_mapping(self, status_service):
        """Test mapping 'not interested' responses."""
        not_interested_phrases = [
            "not interested",
            "Not interested thanks",
            "I'm not interested",
            "No thanks, not interested",
            "Not for me",
            "No interest",
            "Not interested at this time"
        ]
        
        for phrase in not_interested_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status == "Not Interested", \
                f"'{phrase}' should map to 'Not Interested', got '{status}'"
    
    def test_call_back_mapping(self, status_service):
        """Test mapping 'call me later' responses."""
        call_back_phrases = [
            "call me later",
            "Call me back",
            "Can you call me?",
            "Please call me",
            "I'd prefer a phone call",
            "Call me tomorrow",
            "Ring me later",
            "Give me a call"
        ]
        
        for phrase in call_back_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status == "Call Back", \
                f"'{phrase}' should map to 'Call Back', got '{status}'"
    
    def test_wrong_number_mapping(self, status_service):
        """Test mapping 'wrong number' responses."""
        wrong_number_phrases = [
            "wrong number",
            "Wrong number",
            "You have the wrong number",
            "This is the wrong number",
            "Not my number",
            "Incorrect number",
            "You've got the wrong person"
        ]
        
        for phrase in wrong_number_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status == "Wrong Number", \
                f"'{phrase}' should map to 'Wrong Number', got '{status}'"
    
    def test_out_of_budget_mapping(self, status_service):
        """Test mapping 'budget too low' responses."""
        budget_phrases = [
            "budget too low",
            "Can't afford it",
            "Too expensive",
            "Out of my budget",
            "Budget is too tight",
            "Don't have the money",
            "Too costly",
            "Price is too high",
            "Beyond my budget"
        ]
        
        for phrase in budget_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status == "Out of Budget", \
                f"'{phrase}' should map to 'Out of Budget', got '{status}'"
    
    def test_qualified_mapping(self, status_service):
        """Test mapping 'ready to proceed' responses."""
        qualified_phrases = [
            "ready to proceed",
            "Let's do this",
            "I'm interested",
            "Sign me up",
            "Ready to move forward",
            "Let's get started",
            "I want to proceed",
            "Count me in",
            "Yes, I'm ready"
        ]
        
        for phrase in qualified_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status == "Qualified", \
                f"'{phrase}' should map to 'Qualified', got '{status}'"
    
    def test_case_insensitive_mapping(self, status_service):
        """Test that status mapping is case insensitive."""
        test_cases = [
            ("NOT INTERESTED", "Not Interested"),
            ("call me LATER", "Call Back"),
            ("WRONG NUMBER", "Wrong Number"),
            ("too EXPENSIVE", "Out of Budget"),
            ("READY to proceed", "Qualified")
        ]
        
        for input_phrase, expected_status in test_cases:
            status = status_service.map_response_to_status(input_phrase)
            assert status == expected_status, \
                f"'{input_phrase}' should map to '{expected_status}', got '{status}'"
    
    def test_partial_match_mapping(self, status_service):
        """Test partial phrase matching."""
        partial_phrases = [
            ("I think I'm not interested in this opportunity", "Not Interested"),
            ("Could you please call me back tomorrow?", "Call Back"),
            ("Sorry, you have the wrong number here", "Wrong Number"),
            ("This seems too expensive for my current budget", "Out of Budget"),
            ("Yes, I'm ready to proceed with the next steps", "Qualified")
        ]
        
        for phrase, expected_status in partial_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status == expected_status, \
                f"'{phrase}' should map to '{expected_status}', got '{status}'"
    
    def test_no_mapping_returns_none(self, status_service):
        """Test that unmappable responses return None."""
        unmappable_phrases = [
            "Hello there",
            "What's the weather like?",
            "Tell me more",
            "I have a question",
            "Random text here"
        ]
        
        for phrase in unmappable_phrases:
            status = status_service.map_response_to_status(phrase)
            assert status is None, \
                f"'{phrase}' should not map to any status, got '{status}'"
    
    def test_priority_mapping(self, status_service):
        """Test that more specific mappings take priority."""
        # "Not interested" should take priority over "call me"
        mixed_phrase = "I'm not interested, don't call me"
        status = status_service.map_response_to_status(mixed_phrase)
        assert status == "Not Interested"
        
        # "Wrong number" should take priority over other mappings
        wrong_but_polite = "Wrong number, but thanks for calling"
        status = status_service.map_response_to_status(wrong_but_polite)
        assert status == "Wrong Number"
    
    def test_status_update_with_timestamp(self, status_service, sample_lead):
        """Test status update includes proper timestamp."""
        original_updated_at = sample_lead.updated_at
        
        # Update status
        updated_lead = status_service.update_lead_status(sample_lead, "Qualified")
        
        assert updated_lead.status == "Qualified"
        assert updated_lead.updated_at > original_updated_at
        assert updated_lead.updated_at.tzinfo == timezone.utc
    
    def test_status_transition_validation(self, status_service, sample_lead):
        """Test valid status transitions."""
        # Valid transitions from "New"
        valid_transitions = [
            "Contacted",
            "Qualified", 
            "Not Interested",
            "Call Back",
            "Wrong Number",
            "Out of Budget"
        ]
        
        for new_status in valid_transitions:
            # Reset lead status
            sample_lead.status = "New"
            
            is_valid = status_service.is_valid_transition(sample_lead.status, new_status)
            assert is_valid is True, \
                f"Transition from 'New' to '{new_status}' should be valid"
    
    def test_invalid_status_transitions(self, status_service, sample_lead):
        """Test invalid status transitions."""
        # Set lead to "Not Interested"
        sample_lead.status = "Not Interested"
        
        # Should not be able to transition to "Qualified"
        is_valid = status_service.is_valid_transition("Not Interested", "Qualified")
        assert is_valid is False
        
        # Should not be able to transition back to "New"
        is_valid = status_service.is_valid_transition("Not Interested", "New")
        assert is_valid is False
    
    def test_status_history_tracking(self, status_service, sample_lead):
        """Test that status changes are tracked in history."""
        # Update status multiple times
        lead1 = status_service.update_lead_status(sample_lead, "Contacted")
        lead2 = status_service.update_lead_status(lead1, "Qualified")
        
        # Check status history
        history = status_service.get_status_history(lead2.id)
        
        assert len(history) >= 2
        assert history[0]["status"] == "New"  # Original status
        assert history[1]["status"] == "Contacted"
        assert history[2]["status"] == "Qualified"
        
        # Check timestamps are in order
        for i in range(1, len(history)):
            assert history[i]["timestamp"] >= history[i-1]["timestamp"]
    
    @pytest.mark.parametrize("response,expected_status", [
        # Not Interested variations
        ("nope not interested", "Not Interested"),
        ("no thank you not for me", "Not Interested"),
        ("I'll pass", "Not Interested"),
        
        # Call Back variations  
        ("can you ring me", "Call Back"),
        ("phone me instead", "Call Back"),
        ("prefer a call", "Call Back"),
        
        # Wrong Number variations
        ("you've got wrong number", "Wrong Number"),
        ("this isn't my number", "Wrong Number"),
        ("wrong person", "Wrong Number"),
        
        # Budget variations
        ("can't afford this", "Out of Budget"),
        ("too much money", "Out of Budget"),
        ("outside my budget", "Out of Budget"),
        
        # Qualified variations
        ("let's go ahead", "Qualified"),
        ("I'm keen", "Qualified"),
        ("sounds good to me", "Qualified")
    ])
    def test_comprehensive_mapping_cases(self, status_service, response, expected_status):
        """Test comprehensive mapping cases with parametrized inputs."""
        status = status_service.map_response_to_status(response)
        assert status == expected_status


@pytest.mark.unit
class TestStatusMappingIntegration:
    """Integration tests for status mapping with other components."""
    
    @pytest.fixture
    def status_service(self):
        return LeadStatusService()
    
    def test_status_mapping_with_conversation_context(self, status_service):
        """Test status mapping considers conversation context."""
        # Previous context: discussing pricing
        context = {
            "previous_messages": [
                "What are your franchise fees?",
                "Our fees start at $50,000 plus GST"
            ],
            "topic": "pricing"
        }
        
        # Response that could be ambiguous
        response = "That's too much for me"
        
        # Should map to budget-related status given context
        status = status_service.map_response_to_status(response, context=context)
        assert status == "Out of Budget"
    
    def test_status_mapping_triggers_followup_rules(self, status_service):
        """Test that certain status changes trigger followup rule changes."""
        from unittest.mock import patch
        
        with patch('app.services.followup_service.FollowupService') as mock_followup:
            # Update to "Not Interested" should stop followups
            lead = Lead(id="test-lead", status="New")
            
            status_service.update_lead_status(lead, "Not Interested")
            
            # Should have called followup service to cancel followups
            mock_followup.return_value.cancel_followups_for_lead.assert_called_once()
    
    def test_status_mapping_with_meeting_booking(self, status_service):
        """Test status mapping when meeting is booked."""
        lead = Lead(id="test-lead", status="Contacted")
        
        # Meeting booked should update status to "Meeting Scheduled"
        updated_lead = status_service.handle_meeting_booked(lead)
        
        assert updated_lead.status == "Meeting Scheduled"
        assert updated_lead.updated_at.tzinfo == timezone.utc
    
    def test_bulk_status_updates(self, status_service):
        """Test bulk status updates for multiple leads."""
        leads = [
            Lead(id=f"lead-{i}", status="New") 
            for i in range(5)
        ]
        
        # Bulk update to "Contacted"
        updated_leads = status_service.bulk_update_status(leads, "Contacted")
        
        assert len(updated_leads) == 5
        for lead in updated_leads:
            assert lead.status == "Contacted"
            assert lead.updated_at.tzinfo == timezone.utc
