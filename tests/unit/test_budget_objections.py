"""
Unit Tests — Budget & Objections
Tests budget question paths, objection responses with exact workflow text, and no follow-ups
"""

import pytest
from unittest.mock import AsyncMock, patch

from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
from app.services.objection_handling_service import objection_handling_service, ObjectionType


class TestBudgetQuestionPath:
    """Test budget question flow with exact workflow text"""
    
    @pytest.mark.asyncio
    async def test_budget_confirmation_yes_path(self):
        """Test budget confirmation when user says yes"""
        agent = CoochieWorkflowAgent()
        
        # Mock budget analysis for "yes" response
        with patch.object(agent, '_analyze_budget_response') as mock_analysis:
            mock_analysis.return_value = {
                "confirmed": True,
                "amount": 120000,
                "reasoning": "User confirmed budget"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "budget"
            mock_session.context = {"budget_amount": 120000}
            
            mock_lead = AsyncMock()
            mock_lead.id = "test-lead"
            
            result = await agent._handle_budget_confirmation(
                mock_session, mock_lead, "Yes, that's correct"
            )
            
            response = result["response"]
            
            # Should contain exact workflow text
            assert "Total Franchise Fee for Coochie Hydrogreen is $120K(ex. GST)" in response
            assert "50% in advance and 50% after 12 months" in response
            assert "finance option" in response
            assert "How does that sound?" in response
            
            # Should progress to scheduling
            assert result["next_stage"] == "scheduling"
    
    @pytest.mark.asyncio
    async def test_budget_confirmation_no_path(self):
        """Test budget confirmation when user says no"""
        agent = CoochieWorkflowAgent()
        
        with patch.object(agent, '_analyze_budget_response') as mock_analysis:
            mock_analysis.return_value = {
                "confirmed": False,
                "amount": None,
                "reasoning": "User denied budget"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "budget"
            mock_session.context = {"budget_amount": 100000}
            
            mock_lead = AsyncMock()
            
            result = await agent._handle_budget_confirmation(
                mock_session, mock_lead, "No, that's not right"
            )
            
            response = result["response"]
            
            # Should ask for actual amount
            assert "How much funds do you have access to invest?" in response
            
            # Should stay in budget stage
            assert result["next_stage"] == "budget"
    
    @pytest.mark.asyncio
    async def test_budget_no_amount_provided_path(self):
        """Test when user doesn't provide amount after being asked"""
        agent = CoochieWorkflowAgent()
        
        with patch.object(agent, '_analyze_budget_response') as mock_analysis:
            mock_analysis.return_value = {
                "confirmed": False,
                "amount": None,
                "reasoning": "No amount provided"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "budget"
            mock_session.context = {"budget_asked_twice": True}
            
            mock_lead = AsyncMock()
            
            result = await agent._handle_budget_confirmation(
                mock_session, mock_lead, "I'm not sure"
            )
            
            response = result["response"]
            
            # Should contain exact workflow text for this scenario
            assert "We'll walk you through the financials and real examples from our 70 franchise partners" in response
            assert "clear picture of the ROI" in response
            assert "confirm you have funds to get started" in response
    
    @pytest.mark.asyncio
    async def test_budget_amount_provided_after_asking(self):
        """Test when user provides amount after being asked"""
        agent = CoochieWorkflowAgent()
        
        with patch.object(agent, '_analyze_budget_response') as mock_analysis:
            mock_analysis.return_value = {
                "confirmed": True,
                "amount": 150000,
                "reasoning": "User provided amount"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "budget"
            mock_session.context = {}
            
            mock_lead = AsyncMock()
            
            result = await agent._handle_budget_confirmation(
                mock_session, mock_lead, "I have about $150,000"
            )
            
            response = result["response"]
            
            # Should proceed with franchise fee explanation
            assert "Total Franchise Fee for Coochie Hydrogreen is $120K(ex. GST)" in response
            assert result["next_stage"] == "scheduling"


class TestObjectionResponses:
    """Test objection responses match exact workflow strings"""
    
    @pytest.mark.asyncio
    async def test_no_value_objection_exact_text(self):
        """Test 'no value' objection gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "I don't see the value in this"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.NO_VALUE
        
        # Check exact workflow text
        exact_response = objection_handling_service.get_objection_response(ObjectionType.NO_VALUE)
        assert "I understand however we are yet to walk you through the business potential" in exact_response
        assert "Australia's largest lawn care company" in exact_response
        assert "Let's walk you through all on the call" in exact_response
    
    @pytest.mark.asyncio
    async def test_not_heard_objection_exact_text(self):
        """Test 'not heard' objection gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "I haven't heard of your company"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.NOT_HEARD
        
        exact_response = objection_handling_service.get_objection_response(ObjectionType.NOT_HEARD)
        assert "Fair enough. Coochie has been operating for over 30 years" in exact_response
        assert "prominent brand in the NSW & QLD" in exact_response
        assert "looking to grow in other states due to demand" in exact_response
    
    @pytest.mark.asyncio
    async def test_marketing_objection_exact_text(self):
        """Test marketing objection gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "Who does marketing and lead generation?"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.MARKETING
        
        exact_response = objection_handling_service.get_objection_response(ObjectionType.MARKETING)
        assert "We will provide you lead guarantee during the initial period" in exact_response
        assert "income guarantee in the first year" in exact_response
        assert "business development activities in your local area" in exact_response
    
    @pytest.mark.asyncio
    async def test_experience_objection_exact_text(self):
        """Test experience objection gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "I don't have any experience in this field"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.EXPERIENCE
        
        exact_response = objection_handling_service.get_objection_response(ObjectionType.EXPERIENCE)
        assert "We will provide you comprehensive training for 4 weeks" in exact_response
        assert "ongoing support" in exact_response
        assert "spend time with our existing franchise partners" in exact_response
        assert "Rest assured, you won't be left alone" in exact_response
    
    @pytest.mark.asyncio
    async def test_income_objection_exact_text(self):
        """Test income objection gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "I don't see this business generating good income"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.INCOME
        
        exact_response = objection_handling_service.get_objection_response(ObjectionType.INCOME)
        assert "A totally fair question" in exact_response
        assert "Some of our franchise partners, who follow our systems and processes very well, earn over $200K net" in exact_response
        assert "your results will depend on your effort and local conditions" in exact_response
        assert "3-year projections together in our meeting" in exact_response
    
    @pytest.mark.asyncio
    async def test_income_guarantee_question_exact_text(self):
        """Test income guarantee question gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "What's the income guarantee?"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.INCOME_GUARANTEE
        
        exact_response = objection_handling_service.get_objection_response(ObjectionType.INCOME_GUARANTEE)
        assert "In the first year, we guarantee $60K net income" in exact_response
        assert "after all your expenses" in exact_response
    
    @pytest.mark.asyncio
    async def test_royalty_question_exact_text(self):
        """Test royalty question gets exact workflow response"""
        result = await objection_handling_service.handle_objection(
            "What's the royalty fee?"
        )
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.ROYALTY
        
        exact_response = objection_handling_service.get_objection_response(ObjectionType.ROYALTY)
        assert "10% royalty and 3% marketing fund" in exact_response
        assert "total is 13% of your gross sales" in exact_response


class TestNoFollowupsInBudgetObjections:
    """Test that no follow-ups are scheduled in budget/objection paths"""
    
    @pytest.mark.asyncio
    async def test_budget_path_no_followups(self, task_queue_monitor, no_followups_assertion):
        """Test that budget confirmation doesn't schedule follow-ups"""
        agent = CoochieWorkflowAgent()
        
        with patch.object(agent, '_analyze_budget_response') as mock_analysis:
            mock_analysis.return_value = {
                "confirmed": True,
                "amount": 120000,
                "reasoning": "User confirmed"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "budget"
            mock_session.context = {"budget_amount": 120000}
            
            mock_lead = AsyncMock()
            
            # Clear task queue before test
            task_queue_monitor.clear()
            
            result = await agent._handle_budget_confirmation(
                mock_session, mock_lead, "Yes, that's correct"
            )
            
            # Assert no follow-up tasks were scheduled
            followup_tasks = [
                task for task in task_queue_monitor 
                if "followup" in task["task_name"].lower()
            ]
            
            assert len(followup_tasks) == 0, f"No follow-up tasks should be scheduled: {followup_tasks}"
    
    @pytest.mark.asyncio
    async def test_objection_handling_no_followups(self, task_queue_monitor, log_capture):
        """Test that objection handling doesn't schedule follow-ups"""
        # Clear task queue
        task_queue_monitor.clear()
        
        result = await objection_handling_service.handle_objection(
            "I don't see the value"
        )
        
        # Check that objection was handled
        assert result["objection_detected"] is True
        
        # Assert no follow-up tasks were scheduled
        followup_tasks = [
            task for task in task_queue_monitor 
            if "followup" in task["task_name"].lower()
        ]
        
        assert len(followup_tasks) == 0, f"Objection handling should not schedule follow-ups: {followup_tasks}"
        
        # Check logs for suppression evidence
        log_content = log_capture.getvalue()
        # Should have evidence of follow-up suppression
        assert "suppressed" in log_content.lower() or "WORKFLOW_ANDY_NO_FOLLOWUPS" in log_content
    
    @pytest.mark.asyncio
    async def test_objection_followup_message_no_scheduling(self, task_queue_monitor):
        """Test that objection follow-up messages don't schedule actual follow-ups"""
        agent = CoochieWorkflowAgent()
        
        # Clear task queue
        task_queue_monitor.clear()
        
        # Simulate objection follow-up scenario
        mock_session = AsyncMock()
        mock_session.workflow_stage = "budget"
        mock_session.context = {"last_objection": "no_value"}
        
        mock_lead = AsyncMock()
        
        # This should generate the follow-up message but NOT schedule a task
        result = await agent._handle_objection_or_general(
            mock_session, mock_lead, "Does that make sense?"
        )
        
        # Should get appropriate response
        assert result["response"] is not None
        
        # But no follow-up tasks should be scheduled
        followup_tasks = [
            task for task in task_queue_monitor 
            if "followup" in task["task_name"].lower()
        ]
        
        assert len(followup_tasks) == 0, f"Objection follow-up should not schedule tasks: {followup_tasks}"


class TestBudgetAnalysis:
    """Test budget response analysis accuracy"""
    
    @pytest.mark.asyncio
    async def test_budget_confirmation_analysis(self):
        """Test analysis of budget confirmation responses"""
        agent = CoochieWorkflowAgent()
        
        # Test positive confirmations
        positive_cases = [
            "Yes, that's correct",
            "Yes, I have that amount",
            "That's right",
            "Correct",
            "Yes"
        ]
        
        for case in positive_cases:
            with patch.object(agent.openai_service, 'analyze_intent') as mock_ai:
                mock_ai.return_value = {
                    "confirmed": True,
                    "amount": 120000,
                    "reasoning": "User confirmed budget"
                }
                
                result = await agent._analyze_budget_response(case)
                
                assert result["confirmed"] is True, f"Should confirm budget for: {case}"
    
    @pytest.mark.asyncio
    async def test_budget_denial_analysis(self):
        """Test analysis of budget denial responses"""
        agent = CoochieWorkflowAgent()
        
        negative_cases = [
            "No, that's not right",
            "No, I don't have that much",
            "That's incorrect",
            "No"
        ]
        
        for case in negative_cases:
            with patch.object(agent.openai_service, 'analyze_intent') as mock_ai:
                mock_ai.return_value = {
                    "confirmed": False,
                    "amount": None,
                    "reasoning": "User denied budget"
                }
                
                result = await agent._analyze_budget_response(case)
                
                assert result["confirmed"] is False, f"Should deny budget for: {case}"
    
    @pytest.mark.asyncio
    async def test_budget_amount_extraction(self):
        """Test extraction of budget amounts from responses"""
        agent = CoochieWorkflowAgent()
        
        amount_cases = [
            ("I have $150,000", 150000),
            ("About 200k", 200000),
            ("Around $100,000", 100000),
            ("150 thousand", 150000)
        ]
        
        for case, expected_amount in amount_cases:
            with patch.object(agent.openai_service, 'analyze_intent') as mock_ai:
                mock_ai.return_value = {
                    "confirmed": True,
                    "amount": expected_amount,
                    "reasoning": "Amount extracted from response"
                }
                
                result = await agent._analyze_budget_response(case)
                
                assert result["amount"] == expected_amount, \
                    f"Should extract {expected_amount} from: {case}"
