"""
Unit Tests - Time Utilities
Tests for timezone handling, date parsing, and calendar correctness
"""

import pytest
from datetime import datetime, timezone, timedelta
import pytz
from freezegun import freeze_time

from app.meeting_agent.timezone_utils import TimezoneHandler
from tests.conftest import TEST_AVAILABILITY_SEED


@pytest.mark.unit
class TestTimezoneHandler:
    """Test timezone handling utilities."""
    
    @pytest.fixture
    def tz_handler(self):
        """Create timezone handler instance."""
        return TimezoneHandler()
    
    @pytest.fixture
    def frozen_test_time(self):
        """Freeze time to consistent test timestamp."""
        with freeze_time("2025-08-29T12:00:00+05:30") as frozen:
            yield frozen
    
    def test_timezone_initialization(self, tz_handler):
        """Test timezone handler initialization."""
        assert tz_handler.default_timezone == "Asia/Kolkata"
        assert "monday" in tz_handler.weekdays
        assert tz_handler.weekdays["monday"] == 0
        assert tz_handler.weekdays["sunday"] == 6
    
    @pytest.mark.asyncio
    async def test_parse_tomorrow(self, tz_handler, frozen_test_time):
        """Test parsing 'tomorrow' expression."""
        result = await tz_handler.parse_date_expression("tomorrow")
        
        assert result is not None
        assert result["is_relative"] is True
        assert result["days_ahead"] == 1
        
        # Tomorrow from 2025-08-29 should be 2025-08-30
        expected_date = "2025-08-30"
        assert result["date"] == expected_date
        assert result["formatted"] == "Saturday, August 30"
    
    @pytest.mark.asyncio
    async def test_parse_weekday_names(self, tz_handler, frozen_test_time):
        """Test parsing weekday names."""
        # Test "Friday" - should be next Friday (2025-08-29 is Friday, so next Friday is 2025-09-05)
        result = await tz_handler.parse_date_expression("Friday")
        
        assert result is not None
        assert result["weekday"] == "Friday"
        assert result["date"] == "2025-09-05"  # Next Friday
        
        # Test "Monday" - should be next Monday (2025-09-01)
        result = await tz_handler.parse_date_expression("Monday")
        
        assert result is not None
        assert result["weekday"] == "Monday"
        assert result["date"] == "2025-09-01"
    
    @pytest.mark.asyncio
    async def test_parse_next_week(self, tz_handler, frozen_test_time):
        """Test parsing 'next week' expression."""
        result = await tz_handler.parse_date_expression("next week")
        
        assert result is not None
        assert result["is_relative"] is True
        assert result["week_offset"] == 1
        
        # Should provide date range for next week
        assert "start_date" in result
        assert "end_date" in result
    
    @pytest.mark.asyncio
    async def test_parse_date_ranges(self, tz_handler, frozen_test_time):
        """Test parsing date ranges like '3rd or 4th'."""
        # Test "3rd or 4th" - should parse as range
        result = await tz_handler.parse_date_expression("3rd or 4th")
        
        assert result is not None
        assert result["is_range"] is True
        assert len(result["dates"]) == 2
        
        # Test "Monday through Wednesday"
        result = await tz_handler.parse_date_expression("Monday through Wednesday")
        
        assert result is not None
        assert result["is_range"] is True
        assert result["start_weekday"] == "Monday"
        assert result["end_weekday"] == "Wednesday"
    
    @pytest.mark.asyncio
    async def test_parse_time_expressions(self, tz_handler):
        """Test parsing time expressions."""
        # Test specific time "5 PM"
        result = await tz_handler.parse_time_expression("5 PM")
        
        assert result is not None
        assert result["is_specific"] is True
        assert result["hour"] == 17
        assert result["minute"] == 0
        assert result["formatted"] == "5:00 PM"
        
        # Test "2:30 PM"
        result = await tz_handler.parse_time_expression("2:30 PM")
        
        assert result is not None
        assert result["hour"] == 14
        assert result["minute"] == 30
        assert result["formatted"] == "2:30 PM"
        
        # Test "morning"
        result = await tz_handler.parse_time_expression("morning")
        
        assert result is not None
        assert result["time_period"] == "morning"
        assert result["is_specific"] is False
        
        # Test "anytime"
        result = await tz_handler.parse_time_expression("anytime")
        
        assert result is not None
        assert result["is_flexible"] is True
    
    def test_utc_conversion(self, tz_handler):
        """Test UTC conversion utilities."""
        # Test local to UTC
        kolkata_tz = pytz.timezone("Asia/Kolkata")
        local_dt = kolkata_tz.localize(datetime(2025, 8, 29, 14, 30))  # 2:30 PM IST
        
        utc_dt = tz_handler.to_utc(local_dt)
        
        assert utc_dt.tzinfo == timezone.utc
        assert utc_dt.hour == 9  # 2:30 PM IST = 9:00 AM UTC
        assert utc_dt.minute == 0
    
    def test_local_conversion(self, tz_handler):
        """Test local timezone conversion."""
        # Test UTC to local
        utc_dt = datetime(2025, 8, 29, 9, 0, tzinfo=timezone.utc)  # 9:00 AM UTC
        
        local_dt = tz_handler.to_local(utc_dt, "Asia/Kolkata")
        
        assert local_dt.hour == 14  # 9:00 AM UTC = 2:30 PM IST
        assert local_dt.minute == 30
    
    def test_business_hours_validation(self, tz_handler):
        """Test business hours validation."""
        # Business day, business hours (Friday 10 AM)
        business_dt = datetime(2025, 8, 29, 10, 0)  # Friday 10 AM
        assert tz_handler.is_business_hours(business_dt) is True
        
        # Weekend (Saturday 10 AM)
        weekend_dt = datetime(2025, 8, 30, 10, 0)  # Saturday 10 AM
        assert tz_handler.is_business_hours(weekend_dt) is False
        
        # After hours (Friday 6 PM)
        after_hours_dt = datetime(2025, 8, 29, 18, 0)  # Friday 6 PM
        assert tz_handler.is_business_hours(after_hours_dt) is False
        
        # Before hours (Friday 7 AM)
        before_hours_dt = datetime(2025, 8, 29, 7, 0)  # Friday 7 AM
        assert tz_handler.is_business_hours(before_hours_dt) is False
    
    def test_calendar_correctness_2025(self, tz_handler):
        """Test calendar correctness for 2025."""
        # Verify key dates in 2025
        test_dates = [
            ("2025-01-01", "Wednesday"),  # New Year's Day
            ("2025-08-29", "Friday"),     # Our frozen test date
            ("2025-08-30", "Saturday"),   # Tomorrow from test date
            ("2025-09-01", "Monday"),     # Next Monday
            ("2025-12-25", "Thursday"),   # Christmas
            ("2025-12-31", "Wednesday")   # New Year's Eve
        ]
        
        for date_str, expected_weekday in test_dates:
            dt = datetime.fromisoformat(date_str)
            actual_weekday = dt.strftime("%A")
            assert actual_weekday == expected_weekday, \
                f"Date {date_str} should be {expected_weekday}, got {actual_weekday}"
    
    def test_round_trip_formatting(self, tz_handler):
        """Test UTC to local formatting round trip."""
        # Create UTC datetime
        utc_dt = datetime(2025, 8, 29, 9, 0, tzinfo=timezone.utc)
        
        # Convert to local and format
        local_formatted = tz_handler.format_datetime_local(utc_dt, "Asia/Kolkata")
        
        # Should include AM/PM and proper formatting
        assert "PM" in local_formatted or "AM" in local_formatted
        assert "August" in local_formatted
        assert "29" in local_formatted
        
        # Test with different timezone
        sydney_formatted = tz_handler.format_datetime_local(utc_dt, "Australia/Sydney")
        assert sydney_formatted != local_formatted  # Should be different
    
    @pytest.mark.asyncio
    async def test_combine_date_time(self, tz_handler, frozen_test_time):
        """Test combining parsed date and time."""
        # Parse date and time separately
        date_result = await tz_handler.parse_date_expression("tomorrow")
        time_result = await tz_handler.parse_time_expression("2 PM")
        
        # Combine them
        combined = tz_handler.combine_date_time(date_result, time_result, "Asia/Kolkata")
        
        assert combined is not None
        assert combined.date().isoformat() == "2025-08-30"  # Tomorrow
        assert combined.hour == 14  # 2 PM
        assert combined.minute == 0
    
    def test_timezone_edge_cases(self, tz_handler):
        """Test timezone edge cases."""
        # Test with None timezone (should use default)
        dt = datetime(2025, 8, 29, 14, 30)
        local_dt = tz_handler.to_local(dt, None)
        assert local_dt.tzinfo is not None
        
        # Test with invalid timezone (should handle gracefully)
        try:
            tz_handler.to_local(dt, "Invalid/Timezone")
            assert False, "Should have raised an exception"
        except Exception:
            pass  # Expected
    
    @pytest.mark.parametrize("expression,expected_relative", [
        ("tomorrow", True),
        ("next week", True),
        ("Monday", False),  # Specific weekday
        ("2025-08-30", False),  # Specific date
        ("in 3 days", True)
    ])
    @pytest.mark.asyncio
    async def test_relative_vs_absolute_dates(self, tz_handler, expression, expected_relative, frozen_test_time):
        """Test distinction between relative and absolute date expressions."""
        result = await tz_handler.parse_date_expression(expression)
        
        if result:
            assert result.get("is_relative", False) == expected_relative
    
    @pytest.mark.parametrize("time_expr,expected_period", [
        ("morning", "morning"),
        ("afternoon", "afternoon"),
        ("evening", "evening"),
        ("night", "night"),
        ("anytime", None)  # Flexible, no specific period
    ])
    @pytest.mark.asyncio
    async def test_time_periods(self, tz_handler, time_expr, expected_period):
        """Test time period parsing."""
        result = await tz_handler.parse_time_expression(time_expr)
        
        assert result is not None
        if expected_period:
            assert result.get("time_period") == expected_period
        else:
            assert result.get("is_flexible") is True


@pytest.mark.unit
class TestTimezoneIntegration:
    """Integration tests for timezone utilities with meeting agent."""
    
    @pytest.fixture
    def tz_handler(self):
        return TimezoneHandler()
    
    @pytest.mark.asyncio
    async def test_meeting_scheduling_timezone_flow(self, tz_handler, frozen_test_time):
        """Test complete timezone flow for meeting scheduling."""
        # User says "tomorrow at 2pm"
        date_result = await tz_handler.parse_date_expression("tomorrow")
        time_result = await tz_handler.parse_time_expression("2pm")
        
        # Combine in user's timezone
        user_tz = "Asia/Kolkata"
        combined_local = tz_handler.combine_date_time(date_result, time_result, user_tz)
        
        # Convert to UTC for storage
        utc_dt = tz_handler.to_utc(combined_local)
        
        # Format for confirmation (back to local)
        confirmation_text = tz_handler.format_datetime_local(utc_dt, user_tz)
        
        # Assertions
        assert combined_local.date().isoformat() == "2025-08-30"
        assert combined_local.hour == 14  # 2 PM local
        assert utc_dt.hour == 8  # 2 PM IST = 8:30 AM UTC (but we round to hour)
        assert "PM" in confirmation_text
        assert "August 30" in confirmation_text
    
    @pytest.mark.asyncio
    async def test_different_user_timezones(self, tz_handler, frozen_test_time):
        """Test handling different user timezones."""
        timezones_to_test = [
            "Asia/Kolkata",
            "Australia/Sydney", 
            "America/New_York",
            "Europe/London"
        ]
        
        for tz in timezones_to_test:
            # Parse "tomorrow at 2pm" in each timezone
            date_result = await tz_handler.parse_date_expression("tomorrow")
            time_result = await tz_handler.parse_time_expression("2pm")
            
            combined = tz_handler.combine_date_time(date_result, time_result, tz)
            utc_dt = tz_handler.to_utc(combined)
            
            # All should be valid datetimes
            assert combined is not None
            assert utc_dt.tzinfo == timezone.utc
            
            # Local time should always be 2 PM in their timezone
            assert combined.hour == 14
