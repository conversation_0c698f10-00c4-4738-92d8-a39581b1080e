"""
Unit Tests - SMS Message Splitter
Tests for GSM vs UCS-2 detection, segment limits, URL preservation, and reassembly
"""

import pytest
from typing import List

from app.utils.sms_splitter import SMSSplitter, detect_encoding, split_message
from tests.mocks.kudosity_stub import create_long_message, create_message_with_url


@pytest.mark.unit
class TestSMSSplitter:
    """Test SMS message splitting functionality."""
    
    @pytest.fixture
    def splitter(self):
        """Create SMS splitter instance."""
        return SMSSplitter(max_segment_length=160)
    
    def test_gsm_encoding_detection(self, splitter):
        """Test GSM 7-bit encoding detection."""
        # Pure GSM characters
        gsm_message = "Hello world! This is a test message with numbers 123 and symbols @#$"
        encoding = detect_encoding(gsm_message)
        assert encoding == "GSM"
        
        # GSM extended characters
        gsm_extended = "Hello [world] with {extended} characters ^~"
        encoding = detect_encoding(gsm_extended)
        assert encoding == "GSM"
    
    def test_ucs2_encoding_detection(self, splitter):
        """Test UCS-2 encoding detection."""
        # Unicode characters
        unicode_message = "Hello 🌟 world with emojis 😊"
        encoding = detect_encoding(unicode_message)
        assert encoding == "UCS-2"
        
        # Non-Latin characters
        hindi_message = "नमस्ते दुनिया"
        encoding = detect_encoding(hindi_message)
        assert encoding == "UCS-2"
        
        # Chinese characters
        chinese_message = "你好世界"
        encoding = detect_encoding(chinese_message)
        assert encoding == "UCS-2"
    
    @pytest.mark.parametrize("max_length", [160, 140, 70])
    def test_segment_length_limits(self, max_length):
        """Test that segments never exceed specified limits."""
        splitter = SMSSplitter(max_segment_length=max_length)
        
        # Create a long message
        long_message = "A" * (max_length * 3)  # 3 segments worth
        
        segments = splitter.split(long_message)
        
        # Check each segment length
        for i, segment in enumerate(segments):
            assert len(segment) <= max_length, \
                f"Segment {i+1} exceeds {max_length} chars: {len(segment)}"
    
    def test_url_preservation(self, splitter):
        """Test that URLs are not broken across segments."""
        url = "https://example.com/very/long/path/to/resource/that/might/cause/splitting"
        message = create_message_with_url(url)
        
        segments = splitter.split(message)
        
        # URL should appear complete in one segment
        url_found = False
        for segment in segments:
            if url in segment:
                url_found = True
                break
        
        assert url_found, f"URL was broken across segments: {segments}"
    
    def test_word_boundary_splitting(self, splitter):
        """Test soft-wrap on word boundaries."""
        # Create message that would split mid-word without soft wrap
        words = ["This", "is", "a", "test", "message", "with", "many", "words"] * 10
        message = " ".join(words)
        
        segments = splitter.split(message)
        
        # Check that segments don't end mid-word (except for very long words)
        for segment in segments[:-1]:  # All but last segment
            if segment.endswith(" "):
                continue  # Ends with space, good
            
            # Check if it ends at a word boundary
            last_char = segment[-1]
            assert last_char in " .,!?;:", f"Segment ends mid-word: '{segment[-10:]}'"
    
    def test_hard_wrap_fallback(self, splitter):
        """Test hard wrap when soft wrap isn't possible."""
        # Create a very long word that can't be soft-wrapped
        long_word = "A" * 200  # Longer than any reasonable segment
        message = f"Start {long_word} end"
        
        segments = splitter.split(message)
        
        # Should still split, even if it breaks the long word
        assert len(segments) > 1
        
        # Each segment should be within limits
        for segment in segments:
            assert len(segment) <= splitter.max_segment_length
    
    def test_message_reassembly(self, splitter):
        """Test that split messages can be reassembled to original."""
        original_messages = [
            "Short message",
            "A" * 500,  # Long message
            create_message_with_url(),
            "Message with\nmultiple\nlines",
            "Mixed content: Hello 🌟 world with emojis 😊 and regular text"
        ]
        
        for original in original_messages:
            segments = splitter.split(original)
            reassembled = "".join(segments)
            
            assert reassembled == original, \
                f"Reassembly failed for: {original[:50]}..."
    
    def test_segment_count_optimization(self, splitter):
        """Test that segment count is minimized."""
        # Create message just over 1 segment
        message = "A" * 170  # Just over 160
        
        segments = splitter.split(message)
        
        # Should be exactly 2 segments
        assert len(segments) == 2
        
        # First segment should be close to max length
        assert len(segments[0]) >= 150  # At least 150 chars used
    
    def test_empty_and_edge_cases(self, splitter):
        """Test empty messages and edge cases."""
        # Empty message
        segments = splitter.split("")
        assert segments == [""]
        
        # Single character
        segments = splitter.split("A")
        assert segments == ["A"]
        
        # Exactly max length
        exact_message = "A" * splitter.max_segment_length
        segments = splitter.split(exact_message)
        assert len(segments) == 1
        assert segments[0] == exact_message
        
        # One character over max length
        over_message = "A" * (splitter.max_segment_length + 1)
        segments = splitter.split(over_message)
        assert len(segments) == 2
    
    def test_special_characters_handling(self, splitter):
        """Test handling of special characters."""
        special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
        message = f"Message with special chars: {special_chars}"
        
        segments = splitter.split(message)
        reassembled = "".join(segments)
        
        assert reassembled == message
        
        # All segments should be within limits
        for segment in segments:
            assert len(segment) <= splitter.max_segment_length
    
    def test_newline_handling(self, splitter):
        """Test handling of newlines and whitespace."""
        message = "Line 1\nLine 2\n\nLine 4\tTabbed content"
        
        segments = splitter.split(message)
        reassembled = "".join(segments)
        
        assert reassembled == message
        
        # Newlines should be preserved
        assert "\n" in reassembled
        assert "\t" in reassembled
    
    @pytest.mark.parametrize("target_segments,expected_count", [
        (1, 1),
        (2, 2),
        (3, 3),
        (5, 5)
    ])
    def test_predictable_segment_counts(self, splitter, target_segments, expected_count):
        """Test that we can create messages with predictable segment counts."""
        message = create_long_message(target_segments, splitter.max_segment_length)
        
        segments = splitter.split(message)
        
        # Should be close to target (within 1 segment)
        assert abs(len(segments) - expected_count) <= 1


@pytest.mark.unit
class TestSMSEncodingDetection:
    """Test SMS encoding detection specifically."""
    
    def test_gsm_character_set(self):
        """Test GSM 7-bit character set detection."""
        # Basic GSM characters
        gsm_basic = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        assert detect_encoding(gsm_basic) == "GSM"
        
        # GSM symbols
        gsm_symbols = "@£$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞ"
        assert detect_encoding(gsm_symbols) == "GSM"
        
        # GSM extended (requires escape sequence)
        gsm_extended = "{}[]~^\\|€"
        assert detect_encoding(gsm_extended) == "GSM"
    
    def test_ucs2_character_detection(self):
        """Test UCS-2 character detection."""
        # Emojis
        emoji_text = "Hello 😊 World 🌟"
        assert detect_encoding(emoji_text) == "UCS-2"
        
        # Accented characters not in GSM
        accented = "café naïve résumé"
        assert detect_encoding(accented) == "UCS-2"
        
        # Asian characters
        asian = "こんにちは 안녕하세요 你好"
        assert detect_encoding(asian) == "UCS-2"
    
    def test_mixed_content_encoding(self):
        """Test encoding detection with mixed content."""
        # Mostly GSM with one Unicode character
        mixed = "This is mostly GSM text with one emoji 😊"
        assert detect_encoding(mixed) == "UCS-2"
        
        # GSM with extended characters
        gsm_with_extended = "Price: €50 for {premium} package"
        assert detect_encoding(gsm_with_extended) == "GSM"


@pytest.mark.unit
class TestSMSIntegration:
    """Integration tests for SMS splitting with Kudosity."""
    
    def test_kudosity_segment_validation(self):
        """Test that SMS splitter works with Kudosity validation."""
        from tests.mocks.kudosity_stub import KudosityStub
        
        kudosity = KudosityStub(max_segment_chars=160)
        splitter = SMSSplitter(max_segment_length=160)
        
        # Create a long message
        long_message = "A" * 500
        
        # Split using our splitter
        segments = splitter.split(long_message)
        
        # Each segment should pass Kudosity validation
        for segment in segments:
            # Simulate sending through Kudosity
            result = kudosity._split_message(segment)
            
            # Should not need further splitting
            assert len(result) == 1
            assert len(result[0]) <= 160
    
    def test_url_preservation_with_kudosity(self):
        """Test URL preservation works with Kudosity stub."""
        from tests.mocks.kudosity_stub import KudosityStub
        
        kudosity = KudosityStub()
        
        # Create message with URL
        url = "https://example.com/very/long/path/to/resource"
        message = f"Please visit {url} for more information about our services and offerings."
        
        # Send through Kudosity (which uses its own splitting)
        import asyncio
        result = asyncio.run(kudosity.send_sms("+61412345678", message))
        
        assert result["success"] is True
        
        # Check that URL wasn't broken
        sent_message = kudosity.get_last_message("+61412345678")
        segments = sent_message.segments
        
        # URL should appear complete in one segment
        url_found = False
        for segment in segments:
            if url in segment:
                url_found = True
                break
        
        assert url_found, f"URL was broken across segments: {segments}"
    
    @pytest.mark.parametrize("max_chars", [160, 140, 70])
    def test_different_segment_limits(self, max_chars):
        """Test SMS splitting with different segment limits."""
        splitter = SMSSplitter(max_segment_length=max_chars)
        
        # Test with various message lengths
        test_messages = [
            "Short",
            "A" * max_chars,  # Exactly at limit
            "A" * (max_chars + 1),  # Just over limit
            "A" * (max_chars * 2),  # Double the limit
            create_long_message(3, max_chars)  # 3 segments worth
        ]
        
        for message in test_messages:
            segments = splitter.split(message)
            
            # All segments should be within limit
            for segment in segments:
                assert len(segment) <= max_chars
            
            # Reassembly should work
            assert "".join(segments) == message
