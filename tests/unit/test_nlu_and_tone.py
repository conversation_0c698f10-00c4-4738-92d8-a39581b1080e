"""
Unit Tests — NLU & Tone
Tests acknowledgement-first phrasing, emoji-free responses, colloquial tone, and background classification
"""

import pytest
import re
from unittest.mock import AsyncMock, patch

from app.agents.coochie_workflow_agent import CoochieWorkflowAgent, WorkBackground
from app.services.coochie_conversation_service import coochie_conversation_service
from tests.helpers.conversation_harness import ConversationHarness


class TestAcknowledgmentFirst:
    """Test that responses always acknowledge user input before advancing topic"""
    
    @pytest.mark.asyncio
    async def test_work_background_acknowledgment(self, mock_openai_service):
        """Test acknowledgment before asking motivation question"""
        mock_openai_service.enhance_response_with_workflow.return_value = \
            "That sounds interesting, what made you enquire about this franchise opportunity given you come from a different background?"
        
        agent = CoochieWorkflowAgent()
        
        # Mock work background analysis
        with patch.object(agent, '_analyze_work_background') as mock_analysis:
            mock_analysis.return_value = {
                "type": WorkBackground.CORPORATE,
                "reasoning": "Office-based management role"
            }
            
            # Mock session and lead
            mock_session = AsyncMock()
            mock_session.workflow_stage = "work_background"
            mock_session.context = {}
            
            mock_lead = AsyncMock()
            mock_lead.id = "test-lead"
            
            result = await agent._handle_work_background_to_motivation(
                mock_session, mock_lead, "I'm a marketing manager"
            )
            
            response = result["response"]
            
            # Should start with acknowledgment
            acknowledgment_phrases = [
                "that sounds interesting", "great", "that makes sense",
                "okay", "right", "i see", "perfect"
            ]
            
            response_lower = response.lower()
            first_part = response_lower.split(',')[0].split('.')[0]
            
            has_acknowledgment = any(phrase in first_part for phrase in acknowledgment_phrases)
            assert has_acknowledgment, f"Response should start with acknowledgment: {response}"
            
            # Should contain the motivation question
            assert "what made you enquire" in response_lower
    
    @pytest.mark.asyncio
    async def test_budget_response_acknowledgment(self, mock_openai_service):
        """Test acknowledgment before budget confirmation"""
        mock_openai_service.enhance_response_with_workflow.return_value = \
            "Thanks for confirming. Total Franchise Fee for Coochie Hydrogreen is $120K(ex. GST) however we have an option to pay 50% in advance and 50% after 12 months."
        
        agent = CoochieWorkflowAgent()
        
        # Mock budget analysis
        with patch.object(agent, '_analyze_budget_response') as mock_analysis:
            mock_analysis.return_value = {
                "confirmed": True,
                "amount": 100000,
                "reasoning": "User confirmed budget"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "budget"
            mock_session.context = {"budget_amount": 100000}
            
            mock_lead = AsyncMock()
            mock_lead.id = "test-lead"
            
            result = await agent._handle_budget_confirmation(
                mock_session, mock_lead, "Yes, that's correct"
            )
            
            response = result["response"]
            
            # Should start with acknowledgment
            assert response.lower().startswith("thanks for confirming") or \
                   "thanks" in response.lower()[:20], \
                   f"Budget response should acknowledge confirmation: {response}"
    
    @pytest.mark.asyncio
    async def test_objection_acknowledgment(self, mock_openai_service):
        """Test acknowledgment in objection responses"""
        from app.services.objection_handling_service import objection_handling_service
        
        mock_openai_service.enhance_response_with_workflow.return_value = \
            "I understand however we are yet to walk you through the business potential. There is a reason we're Australia's largest lawn care company."
        
        result = await objection_handling_service.handle_objection(
            "I don't see the value in this"
        )
        
        response = result["response"]
        
        # Should acknowledge the concern
        acknowledgment_phrases = ["i understand", "fair enough", "totally fair"]
        response_lower = response.lower()
        
        has_acknowledgment = any(phrase in response_lower[:30] for phrase in acknowledgment_phrases)
        assert has_acknowledgment, f"Objection response should acknowledge concern: {response}"


class TestEmojiAndToneCompliance:
    """Test emoji-free responses and proper conversational tone"""
    
    def test_no_emojis_in_workflow_responses(self):
        """Test that all workflow responses are emoji-free"""
        agent = CoochieWorkflowAgent()
        
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+",
            flags=re.UNICODE
        )
        
        for response_key, response_text in agent.workflow_responses.items():
            emojis_found = emoji_pattern.findall(response_text)
            assert len(emojis_found) == 0, \
                f"Emojis found in {response_key}: {emojis_found}"
    
    @pytest.mark.asyncio
    async def test_conversation_service_removes_emojis(self):
        """Test that conversation service removes emojis from generated responses"""
        # Test emoji removal function
        text_with_emojis = "Great! 😊 That sounds interesting 🤔 Let's proceed 👍"
        clean_text = coochie_conversation_service._remove_emojis(text_with_emojis)
        
        assert "😊" not in clean_text
        assert "🤔" not in clean_text
        assert "👍" not in clean_text
        assert "Great!" in clean_text
        assert "That sounds interesting" in clean_text
    
    def test_colloquial_expressions_allowed(self):
        """Test that colloquial expressions are present in responses"""
        agent = CoochieWorkflowAgent()
        
        # Check for natural, conversational elements
        all_responses = " ".join(agent.workflow_responses.values()).lower()
        
        # Should contain contractions
        contractions = ["you're", "we're", "that's", "it's", "don't", "won't"]
        found_contractions = [c for c in contractions if c in all_responses]
        
        assert len(found_contractions) > 0, \
            "Workflow responses should contain contractions for natural tone"
        
        # Should contain conversational phrases
        conversational_phrases = ["fair enough", "totally fair", "rest assured"]
        found_phrases = [p for p in conversational_phrases if p in all_responses]
        
        assert len(found_phrases) > 0, \
            "Workflow responses should contain conversational phrases"
    
    @pytest.mark.asyncio
    async def test_response_compliance_validation(self):
        """Test response compliance validation catches issues"""
        # Test compliant response
        compliant_response = "Great! That sounds interesting. What made you enquire about this opportunity?"
        result = await coochie_conversation_service.validate_response_compliance(compliant_response)
        
        assert result["compliant"] is True
        assert result["has_emojis"] is False
        assert len(result["issues"]) == 0
        
        # Test non-compliant response with emojis
        non_compliant_response = "Great! 😊 That sounds interesting!"
        result = await coochie_conversation_service.validate_response_compliance(non_compliant_response)
        
        assert result["compliant"] is False
        assert result["has_emojis"] is True
        assert "Contains emojis" in result["issues"]


class TestWorkBackgroundClassification:
    """Test background classification (corporate vs trades) from free-text"""
    
    @pytest.mark.asyncio
    async def test_corporate_background_classification(self):
        """Test classification of corporate backgrounds"""
        agent = CoochieWorkflowAgent()
        
        corporate_examples = [
            "I'm a marketing manager",
            "I work as a project manager in IT",
            "I'm an accountant at a firm",
            "HR manager for a large company",
            "Business analyst in finance",
            "Office administrator",
            "Sales manager in retail"
        ]
        
        for example in corporate_examples:
            with patch.object(agent.openai_service, 'analyze_intent') as mock_ai:
                mock_ai.return_value = {
                    "type": "corporate",
                    "reasoning": "Office-based management role"
                }
                
                result = await agent._analyze_work_background(example)
                
                assert result["type"] == WorkBackground.CORPORATE, \
                    f"Should classify '{example}' as corporate"
    
    @pytest.mark.asyncio
    async def test_trades_background_classification(self):
        """Test classification of trades backgrounds"""
        agent = CoochieWorkflowAgent()
        
        trades_examples = [
            "I'm a plumber",
            "I work as an electrician",
            "I'm a handyman",
            "Carpenter by trade",
            "I do landscaping work",
            "I'm a mechanic",
            "Builder/construction worker"
        ]
        
        for example in trades_examples:
            with patch.object(agent.openai_service, 'analyze_intent') as mock_ai:
                mock_ai.return_value = {
                    "type": "trades",
                    "reasoning": "Hands-on trade work"
                }
                
                result = await agent._analyze_work_background(example)
                
                assert result["type"] == WorkBackground.TRADES, \
                    f"Should classify '{example}' as trades"
    
    @pytest.mark.asyncio
    async def test_unclear_background_classification(self):
        """Test classification of unclear backgrounds"""
        agent = CoochieWorkflowAgent()
        
        unclear_examples = [
            "I work",
            "Various things",
            "It's complicated",
            "Different jobs"
        ]
        
        for example in unclear_examples:
            with patch.object(agent.openai_service, 'analyze_intent') as mock_ai:
                mock_ai.return_value = {
                    "type": "unclear",
                    "reasoning": "Cannot determine background clearly"
                }
                
                result = await agent._analyze_work_background(example)
                
                assert result["type"] == WorkBackground.UNCLEAR, \
                    f"Should classify '{example}' as unclear"
    
    @pytest.mark.asyncio
    async def test_background_determines_motivation_question(self):
        """Test that background type determines the correct motivation question"""
        agent = CoochieWorkflowAgent()
        
        # Corporate background should get "different background" question
        with patch.object(agent, '_analyze_work_background') as mock_analysis:
            mock_analysis.return_value = {
                "type": WorkBackground.CORPORATE,
                "reasoning": "Office work"
            }
            
            mock_session = AsyncMock()
            mock_session.workflow_stage = "work_background"
            mock_session.context = {}
            
            mock_lead = AsyncMock()
            
            result = await agent._handle_work_background_to_motivation(
                mock_session, mock_lead, "I'm a manager"
            )
            
            response = result["response"]
            assert "different background" in response.lower(), \
                "Corporate background should mention 'different background'"
        
        # Trades background should get general question
        with patch.object(agent, '_analyze_work_background') as mock_analysis:
            mock_analysis.return_value = {
                "type": WorkBackground.TRADES,
                "reasoning": "Trade work"
            }
            
            result = await agent._handle_work_background_to_motivation(
                mock_session, mock_lead, "I'm a plumber"
            )
            
            response = result["response"]
            assert "different background" not in response.lower(), \
                "Trades background should not mention 'different background'"
            assert "what made you enquire" in response.lower(), \
                "Should ask general motivation question"


class TestConversationalFlow:
    """Test natural conversational flow and context retention"""
    
    @pytest.mark.asyncio
    async def test_context_retention_across_stages(self):
        """Test that context is retained across conversation stages"""
        harness = ConversationHarness()
        
        # Start conversation
        lead_data = {
            "name": "John Smith",
            "phone_number": "+61400000000",
            "email": "<EMAIL>"
        }
        
        conversation_script = [
            {"user": "Yes, I have time", "expected_contains": "what you do for work"},
            {"user": "I'm a marketing manager", "expected_contains": "different background"},
            {"user": "Looking for more flexibility", "expected_contains": "budget"}
        ]
        
        results = await harness.run_conversation_script(
            lead_data=lead_data,
            script=conversation_script
        )
        
        # Check that each response builds on previous context
        for i, result in enumerate(results):
            assert result.success, f"Turn {i+1} failed: {result.error}"
            
            # Check that work background context is retained
            if i >= 1:  # After work background question
                assert "corporate" in str(result.processing_metadata).lower() or \
                       "manager" in str(result.processing_metadata).lower(), \
                       "Work background context should be retained"
    
    @pytest.mark.asyncio
    async def test_natural_conversation_progression(self):
        """Test that conversation progresses naturally through stages"""
        agent = CoochieWorkflowAgent()
        
        # Test stage progression
        stages = ["introduction", "work_background", "motivation", "budget", "scheduling"]
        
        for i, stage in enumerate(stages[:-1]):
            next_stage = stages[i + 1]
            
            # Each stage should naturally progress to the next
            # This is tested through the workflow agent's stage handling
            assert hasattr(agent, f'_handle_{stage}'), \
                f"Agent should have handler for {stage} stage"
            
            if i < len(stages) - 2:
                next_handler = f'_handle_{stage}_to_{next_stage}'
                if hasattr(agent, next_handler):
                    # Stage transition handler exists
                    pass
                else:
                    # General stage handler should handle progression
                    assert hasattr(agent, f'_handle_{next_stage}'), \
                        f"Agent should have handler for {next_stage} stage"
