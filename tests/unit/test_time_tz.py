"""
Unit Tests — Time & TZ
Tests time parsing, timezone handling, UTC persistence, and calendar correctness for 2025
"""

import pytest
from datetime import datetime, timedelta
import pytz
from freezegun import freeze_time

from app.services.coochie_meeting_service import coochie_meeting_service
from app.meeting_agent.timezone_utils import TimezoneHandler
from app.meeting_agent.time_parser import TimeParser


class TestTimeParsing:
    """Test parsing of various time expressions"""
    
    @pytest.fixture
    def time_parser(self):
        """Create time parser instance"""
        return TimeParser(default_timezone="Asia/Kolkata")
    
    @pytest.mark.asyncio
    async def test_parse_tomorrow(self, time_parser, frozen_datetime):
        """Test parsing 'tomorrow' expressions"""
        test_cases = [
            "tomorrow",
            "tomorrow morning",
            "tomorrow afternoon", 
            "tomorrow at 2pm",
            "how about tomorrow"
        ]
        
        for case in test_cases:
            result = await time_parser.parse_time_expression(case)
            
            assert result is not None, f"Should parse '{case}'"
            assert result["type"] == "relative_date"
            assert result["days_offset"] == 1
            
            # Should be August 30, 2025 (tomorrow from frozen time)
            expected_date = frozen_datetime.date() + timedelta(days=1)
            assert result["date"] == expected_date
    
    @pytest.mark.asyncio
    async def test_parse_weekdays(self, time_parser, frozen_datetime):
        """Test parsing weekday names"""
        # From frozen time 2025-08-29 (Friday), test next week's days
        weekday_cases = [
            ("Monday", 3),      # 3 days ahead (2025-09-01)
            ("Tuesday", 4),     # 4 days ahead (2025-09-02)
            ("Wednesday", 5),   # 5 days ahead (2025-09-03)
            ("Thursday", 6),    # 6 days ahead (2025-09-04)
            ("Friday", 7),      # 7 days ahead (2025-09-05)
            ("Saturday", 1),    # 1 day ahead (2025-08-30)
            ("Sunday", 2)       # 2 days ahead (2025-08-31)
        ]
        
        for weekday, expected_offset in weekday_cases:
            result = await time_parser.parse_time_expression(f"How about {weekday}?")
            
            assert result is not None, f"Should parse '{weekday}'"
            assert result["type"] == "weekday"
            assert result["weekday"] == weekday.lower()
            
            expected_date = frozen_datetime.date() + timedelta(days=expected_offset)
            assert result["date"] == expected_date
    
    @pytest.mark.asyncio
    async def test_parse_date_ranges(self, time_parser):
        """Test parsing date ranges like '3rd or 4th'"""
        range_cases = [
            "3rd or 4th",
            "Monday or Tuesday", 
            "tomorrow or day after",
            "this week or next week"
        ]
        
        for case in range_cases:
            result = await time_parser.parse_time_expression(case)
            
            assert result is not None, f"Should parse range '{case}'"
            assert result["type"] == "date_range"
            assert len(result["options"]) >= 2, f"Range should have multiple options: {case}"
    
    @pytest.mark.asyncio
    async def test_parse_time_only(self, time_parser):
        """Test parsing time-only expressions like '5 PM'"""
        time_cases = [
            ("5 PM", 17, 0),
            ("2:30 PM", 14, 30),
            ("10 AM", 10, 0),
            ("9:15 AM", 9, 15),
            ("noon", 12, 0),
            ("midnight", 0, 0)
        ]
        
        for time_expr, expected_hour, expected_minute in time_cases:
            result = await time_parser.parse_time_expression(time_expr)
            
            assert result is not None, f"Should parse '{time_expr}'"
            assert result["type"] == "time_only"
            assert result["hour"] == expected_hour
            assert result["minute"] == expected_minute
    
    @pytest.mark.asyncio
    async def test_parse_anytime(self, time_parser):
        """Test parsing 'anytime' expressions"""
        anytime_cases = [
            "anytime",
            "any time works",
            "I'm flexible",
            "whenever suits you",
            "anytime tomorrow"
        ]
        
        for case in anytime_cases:
            result = await time_parser.parse_time_expression(case)
            
            assert result is not None, f"Should parse '{case}'"
            assert result["type"] == "flexible"
            assert result["flexibility"] == "anytime"


class TestTimezoneHandling:
    """Test timezone conversion and handling"""
    
    @pytest.fixture
    def tz_handler(self):
        """Create timezone handler"""
        return TimezoneHandler(default_timezone="Asia/Kolkata")
    
    def test_utc_persistence(self, tz_handler):
        """Test that times are stored in UTC"""
        # Local time in Asia/Kolkata
        local_tz = pytz.timezone("Asia/Kolkata")
        local_time = local_tz.localize(datetime(2025, 8, 30, 14, 0, 0))  # 2:00 PM IST
        
        # Convert to UTC for storage
        utc_time = tz_handler.to_utc(local_time)
        
        # Should be 8:30 AM UTC (IST is UTC+5:30)
        expected_utc = datetime(2025, 8, 30, 8, 30, 0, tzinfo=pytz.UTC)
        assert utc_time == expected_utc
    
    def test_local_timezone_display(self, tz_handler):
        """Test that confirmations show local timezone"""
        # UTC time
        utc_time = datetime(2025, 8, 30, 8, 30, 0, tzinfo=pytz.UTC)
        
        # Convert to local for display
        local_time = tz_handler.to_local(utc_time, "Asia/Kolkata")
        
        # Should be 2:00 PM IST
        expected_local = pytz.timezone("Asia/Kolkata").localize(
            datetime(2025, 8, 30, 14, 0, 0)
        )
        assert local_time == expected_local
    
    def test_timezone_aware_formatting(self, tz_handler):
        """Test timezone-aware time formatting for confirmations"""
        utc_time = datetime(2025, 8, 30, 8, 30, 0, tzinfo=pytz.UTC)
        
        # Format for confirmation message
        formatted = tz_handler.format_for_confirmation(utc_time, "Asia/Kolkata")
        
        # Should include month name and AM/PM
        assert "August" in formatted
        assert "30" in formatted
        assert "2:00 PM" in formatted or "14:00" in formatted
    
    def test_multiple_timezone_support(self, tz_handler):
        """Test handling of different timezones"""
        utc_time = datetime(2025, 8, 30, 12, 0, 0, tzinfo=pytz.UTC)
        
        timezone_tests = [
            ("Asia/Kolkata", 17, 30),      # UTC+5:30
            ("America/New_York", 8, 0),    # UTC-4 (EDT)
            ("Europe/London", 13, 0),      # UTC+1 (BST)
            ("Australia/Sydney", 22, 0)    # UTC+10 (AEST)
        ]
        
        for tz_name, expected_hour, expected_minute in timezone_tests:
            local_time = tz_handler.to_local(utc_time, tz_name)
            
            assert local_time.hour == expected_hour, f"Wrong hour for {tz_name}"
            assert local_time.minute == expected_minute, f"Wrong minute for {tz_name}"


class TestCalendarCorrectness2025:
    """Test calendar correctness for 2025"""
    
    @freeze_time("2025-08-29T12:00:00+05:30")  # Friday
    def test_weekday_date_alignment_2025(self):
        """Test that weekday names align with correct dates in 2025"""
        base_date = datetime(2025, 8, 29)  # Friday
        
        # Test next week's alignment
        weekday_alignment = [
            (0, "Monday", datetime(2025, 9, 1)),     # Next Monday
            (1, "Tuesday", datetime(2025, 9, 2)),    # Next Tuesday  
            (2, "Wednesday", datetime(2025, 9, 3)),  # Next Wednesday
            (3, "Thursday", datetime(2025, 9, 4)),   # Next Thursday
            (4, "Friday", datetime(2025, 9, 5)),     # Next Friday
            (5, "Saturday", datetime(2025, 8, 30)),  # Tomorrow (Saturday)
            (6, "Sunday", datetime(2025, 8, 31))     # Day after (Sunday)
        ]
        
        for weekday_num, weekday_name, expected_date in weekday_alignment:
            # Calculate next occurrence of this weekday
            days_ahead = (weekday_num - base_date.weekday()) % 7
            if days_ahead == 0:  # If it's the same weekday, get next week's
                days_ahead = 7
            
            calculated_date = base_date + timedelta(days=days_ahead)
            
            assert calculated_date.date() == expected_date.date(), \
                f"{weekday_name} should be {expected_date.date()}, got {calculated_date.date()}"
    
    def test_month_boundaries_2025(self):
        """Test handling of month boundaries in 2025"""
        # Test August to September boundary
        august_31 = datetime(2025, 8, 31)  # Sunday
        september_1 = datetime(2025, 9, 1)  # Monday
        
        assert august_31.weekday() == 6, "August 31, 2025 should be Sunday"
        assert september_1.weekday() == 0, "September 1, 2025 should be Monday"
        
        # Test February (not leap year)
        february_28 = datetime(2025, 2, 28)  # Friday
        march_1 = datetime(2025, 3, 1)      # Saturday
        
        assert february_28.weekday() == 4, "February 28, 2025 should be Friday"
        assert march_1.weekday() == 5, "March 1, 2025 should be Saturday"
        
        # Confirm 2025 is not a leap year
        try:
            datetime(2025, 2, 29)
            assert False, "2025 should not have February 29"
        except ValueError:
            pass  # Expected - 2025 is not a leap year
    
    @freeze_time("2025-08-29T12:00:00+05:30")
    def test_business_days_calculation(self):
        """Test business days calculation for meeting scheduling"""
        from app.meeting_agent.business_days import BusinessDaysCalculator
        
        calc = BusinessDaysCalculator()
        base_date = datetime(2025, 8, 29).date()  # Friday
        
        # Next business day should be Monday (skipping weekend)
        next_business_day = calc.next_business_day(base_date)
        expected = datetime(2025, 9, 1).date()  # Monday
        
        assert next_business_day == expected, \
            f"Next business day from Friday should be Monday, got {next_business_day}"
        
        # Test business days in range
        business_days = calc.business_days_in_range(
            start_date=base_date,
            days_ahead=7
        )
        
        # Should include Mon, Tue, Wed, Thu, Fri of next week
        assert len(business_days) == 5, f"Should find 5 business days, got {len(business_days)}"


class TestMeetingServiceTimezone:
    """Test meeting service timezone integration"""
    
    @pytest.mark.asyncio
    async def test_scheduling_options_timezone_conversion(self):
        """Test that scheduling options are properly timezone converted"""
        options = await coochie_meeting_service.generate_scheduling_options(
            lead_id="test-lead",
            preferred_timezone="Asia/Kolkata"
        )
        
        assert options["timezone"] == "Asia/Kolkata"
        
        if options.get("slots"):
            slot = options["slots"][0]
            
            # Should have both local and UTC times
            assert "datetime_local" in slot
            assert "datetime_utc" in slot
            
            # Local time should be in IST format
            local_time = datetime.fromisoformat(slot["datetime_local"])
            utc_time = datetime.fromisoformat(slot["datetime_utc"].replace('Z', '+00:00'))
            
            # Time difference should be 5.5 hours (IST offset)
            time_diff = local_time.replace(tzinfo=None) - utc_time.replace(tzinfo=None)
            expected_diff = timedelta(hours=5, minutes=30)
            
            assert abs(time_diff - expected_diff) < timedelta(minutes=1), \
                f"Timezone offset should be 5.5 hours, got {time_diff}"
    
    @pytest.mark.asyncio
    async def test_booking_confirmation_timezone_display(self):
        """Test that booking confirmations display correct timezone"""
        mock_option = {
            "option_number": 1,
            "day": "Monday",
            "time": "2:00 PM",
            "datetime_local": "2025-09-01T14:00:00+05:30",
            "datetime_utc": "2025-09-01T08:30:00+00:00",
            "staff_name": "Andy",
            "staff_id": "staff_001",
            "service_id": "service_001",
            "duration_minutes": 30,
            "display_text": "2:00 PM on Monday"
        }
        
        booking_result = await coochie_meeting_service.book_meeting(
            lead_id="test-lead",
            selected_option=mock_option
        )
        
        assert booking_result["success"] is True
        
        # Display time should be in local timezone
        display_time = booking_result.get("display_time", "")
        assert "2:00 PM" in display_time
        assert "Monday" in display_time
        
        # Meeting time should be stored in local format for display
        meeting_time = booking_result.get("meeting_time", "")
        assert "14:00:00+05:30" in meeting_time  # IST format
