"""
Comprehensive tests for objection handling system
Tests objection detection, response generation, and workflow integration.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.objection_handling_service import (
    ObjectionHandlingService, 
    ObjectionType,
    objection_handling_service
)


class TestObjectionHandlingService:
    """Test the objection handling service"""
    
    @pytest.fixture
    def objection_service(self):
        """Create objection handling service"""
        return ObjectionHandlingService()
    
    def test_objection_responses_exist(self, objection_service):
        """Test that all objection responses are defined"""
        expected_types = [
            ObjectionType.NO_VALUE,
            ObjectionType.NOT_HEARD,
            ObjectionType.MARKETING,
            ObjectionType.EXPERIENCE,
            ObjectionType.INCOME,
            ObjectionType.INCOME_GUARANTEE,
            ObjectionType.ROYALTY,
            ObjectionType.GENERAL
        ]
        
        for objection_type in expected_types:
            assert objection_type in objection_service.objection_responses
            response = objection_service.objection_responses[objection_type]
            assert isinstance(response, str)
            assert len(response) > 10  # Reasonable response length
    
    def test_exact_workflow_responses(self, objection_service):
        """Test that responses match exact workflow text"""
        # Test specific workflow responses
        assert "Australia's largest lawn care company" in objection_service.objection_responses[ObjectionType.NO_VALUE]
        assert "30 years" in objection_service.objection_responses[ObjectionType.NOT_HEARD]
        assert "lead guarantee" in objection_service.objection_responses[ObjectionType.MARKETING]
        assert "4 weeks" in objection_service.objection_responses[ObjectionType.EXPERIENCE]
        assert "$200K net" in objection_service.objection_responses[ObjectionType.INCOME]
        assert "$60K net income" in objection_service.objection_responses[ObjectionType.INCOME_GUARANTEE]
        assert "10% royalty and 3% marketing fund" in objection_service.objection_responses[ObjectionType.ROYALTY]
    
    def test_pattern_matching_no_value(self, objection_service):
        """Test pattern matching for no value objections"""
        test_messages = [
            "I don't see the value in this",
            "This seems too expensive",
            "Not worth the cost",
            "Can't afford this price"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] == ObjectionType.NO_VALUE
            assert result["confidence"] > 0
    
    def test_pattern_matching_not_heard(self, objection_service):
        """Test pattern matching for not heard objections"""
        test_messages = [
            "I haven't heard of your company",
            "Don't know you guys",
            "Never seen this brand before",
            "Not familiar with Coochie"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] == ObjectionType.NOT_HEARD
            assert result["confidence"] > 0
    
    def test_pattern_matching_marketing(self, objection_service):
        """Test pattern matching for marketing objections"""
        test_messages = [
            "How do I get customers?",
            "What about marketing support?",
            "Where will the leads come from?",
            "How do I advertise?"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] == ObjectionType.MARKETING
            assert result["confidence"] > 0
    
    def test_pattern_matching_experience(self, objection_service):
        """Test pattern matching for experience objections"""
        test_messages = [
            "I have no experience in this",
            "Don't know how to run a business",
            "Never done anything like this",
            "I'm inexperienced"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] == ObjectionType.EXPERIENCE
            assert result["confidence"] > 0
    
    def test_pattern_matching_income(self, objection_service):
        """Test pattern matching for income objections"""
        test_messages = [
            "How much money can I make?",
            "What's the earning potential?",
            "Will this be profitable?",
            "What about the income?"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] == ObjectionType.INCOME
            assert result["confidence"] > 0
    
    def test_pattern_matching_royalty(self, objection_service):
        """Test pattern matching for royalty objections"""
        test_messages = [
            "What are the ongoing fees?",
            "How much is the royalty?",
            "What do I pay monthly?",
            "Tell me about the franchise fees"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] == ObjectionType.ROYALTY
            assert result["confidence"] > 0
    
    def test_no_objection_detected(self, objection_service):
        """Test when no objection is detected"""
        test_messages = [
            "That sounds good",
            "I'm interested",
            "Tell me more",
            "Okay, let's proceed"
        ]
        
        for message in test_messages:
            result = objection_service._pattern_match_objection(message)
            assert result["type"] is None
            assert result["confidence"] == 0.0
    
    @pytest.mark.asyncio
    async def test_ai_objection_analysis(self, objection_service):
        """Test AI-powered objection analysis"""
        with patch.object(objection_service, '_ai_analyze_objection') as mock_ai:
            mock_ai.return_value = {
                "type": ObjectionType.NO_VALUE,
                "confidence": 0.8,
                "reasoning": "User expressed concern about value"
            }
            
            result = await objection_service.detect_objection("This seems too expensive")
            
            assert result["type"] == ObjectionType.NO_VALUE
            assert result["confidence"] == 0.8
            assert result["detected_by"] == "ai"
            mock_ai.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_handle_objection_detected(self, objection_service):
        """Test complete objection handling when objection is detected"""
        with patch.object(objection_service, 'detect_objection') as mock_detect:
            mock_detect.return_value = {
                "type": ObjectionType.NO_VALUE,
                "confidence": 0.9
            }
            
            with patch('app.services.coochie_conversation_service.coochie_conversation_service') as mock_conv:
                mock_conv.enhance_objection_response.return_value = "Enhanced response"
                
                result = await objection_service.handle_objection("Too expensive")
                
                assert result["objection_detected"] is True
                assert result["objection_type"] == ObjectionType.NO_VALUE
                assert result["confidence"] == 0.9
                assert result["response"] == "Enhanced response"
                assert result["awaiting_response"] is True
    
    @pytest.mark.asyncio
    async def test_handle_objection_not_detected(self, objection_service):
        """Test objection handling when no objection is detected"""
        with patch.object(objection_service, 'detect_objection') as mock_detect:
            mock_detect.return_value = {
                "type": None,
                "confidence": 0.0
            }
            
            with patch.object(objection_service, '_generate_acknowledgment') as mock_ack:
                mock_ack.return_value = "I understand"
                
                result = await objection_service.handle_objection("That sounds good")
                
                assert result["objection_detected"] is False
                assert result["objection_type"] is None
                assert "I understand" in result["response"]
                assert "Does that make sense" in result["response"]
    
    def test_get_objection_response(self, objection_service):
        """Test getting objection responses"""
        # Test valid objection type
        response = objection_service.get_objection_response(ObjectionType.NO_VALUE)
        assert "Australia's largest lawn care company" in response
        
        # Test invalid objection type - should return general response
        response = objection_service.get_objection_response("invalid_type")
        assert "Does that make sense" in response
    
    def test_get_objection_statistics(self, objection_service):
        """Test objection statistics"""
        stats = objection_service.get_objection_statistics()
        
        assert "total_objection_types" in stats
        assert "objection_types" in stats
        assert "pattern_counts" in stats
        assert "total_patterns" in stats
        
        assert stats["total_objection_types"] > 0
        assert len(stats["objection_types"]) > 0
        assert stats["total_patterns"] > 0


class TestObjectionWorkflowIntegration:
    """Test objection handling integration with workflow"""
    
    @pytest.mark.asyncio
    async def test_workflow_agent_objection_handling(self):
        """Test workflow agent uses objection handling service"""
        from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
        from app.models.conversation_session import ConversationSession
        from app.models.lead import Lead
        
        # Mock dependencies
        mock_session = MagicMock(spec=ConversationSession)
        mock_session.workflow_stage = "budget"
        mock_session.id = "test-session"
        
        mock_lead = MagicMock(spec=Lead)
        mock_lead.id = "test-lead"
        
        # Mock objection handling service
        with patch('app.services.objection_handling_service.objection_handling_service') as mock_service:
            mock_service.handle_objection.return_value = {
                "objection_detected": True,
                "objection_type": ObjectionType.NO_VALUE,
                "confidence": 0.8,
                "response": "Test objection response",
                "stage": "budget",
                "next_stage": "budget",
                "awaiting_response": True,
                "detection_details": {"detected_by": "ai"}
            }
            
            agent = CoochieWorkflowAgent()
            result = await agent._handle_objection_or_general(mock_session, mock_lead, "Too expensive")
            
            assert result["objection_detected"] is True
            assert result["objection_handled"] == ObjectionType.NO_VALUE
            assert result["response"] == "Test objection response"
            assert result["objection_confidence"] == 0.8
            assert result["detection_method"] == "ai"
            
            mock_service.handle_objection.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_objection_handling_error_fallback(self):
        """Test error handling in objection processing"""
        from app.agents.coochie_workflow_agent import CoochieWorkflowAgent
        from app.models.conversation_session import ConversationSession
        from app.models.lead import Lead
        
        mock_session = MagicMock(spec=ConversationSession)
        mock_session.workflow_stage = "budget"
        
        mock_lead = MagicMock(spec=Lead)
        mock_lead.id = "test-lead"
        
        # Mock objection service to raise exception
        with patch('app.services.objection_handling_service.objection_handling_service') as mock_service:
            mock_service.handle_objection.side_effect = Exception("Test error")
            
            with patch.object(CoochieWorkflowAgent, '_generate_acknowledgment') as mock_ack:
                mock_ack.return_value = "I understand"
                
                agent = CoochieWorkflowAgent()
                result = await agent._handle_objection_or_general(mock_session, mock_lead, "Test message")
                
                assert "error" in result
                assert result["objection_handled"] is None
                assert "I understand" in result["response"]


class TestObjectionScenarios:
    """Test specific objection scenarios"""
    
    @pytest.mark.asyncio
    async def test_no_value_objection_scenario(self):
        """Test complete no value objection scenario"""
        message = "I don't see the value in this franchise opportunity"
        
        result = await objection_handling_service.handle_objection(message)
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.NO_VALUE
        assert "Australia's largest lawn care company" in result.get("exact_workflow_response", "")
    
    @pytest.mark.asyncio
    async def test_experience_objection_scenario(self):
        """Test complete experience objection scenario"""
        message = "I have no experience running a business like this"
        
        result = await objection_handling_service.handle_objection(message)
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.EXPERIENCE
        assert "4 weeks" in result.get("exact_workflow_response", "")
        assert "training" in result.get("exact_workflow_response", "")
    
    @pytest.mark.asyncio
    async def test_income_objection_scenario(self):
        """Test complete income objection scenario"""
        message = "How much money can I actually make with this?"
        
        result = await objection_handling_service.handle_objection(message)
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.INCOME
        assert "$200K net" in result.get("exact_workflow_response", "")
    
    @pytest.mark.asyncio
    async def test_royalty_objection_scenario(self):
        """Test complete royalty objection scenario"""
        message = "What are the ongoing fees I need to pay?"
        
        result = await objection_handling_service.handle_objection(message)
        
        assert result["objection_detected"] is True
        assert result["objection_type"] == ObjectionType.ROYALTY
        assert "10% royalty and 3% marketing fund" in result.get("exact_workflow_response", "")
    
    @pytest.mark.asyncio
    async def test_multiple_objections_in_message(self):
        """Test handling message with multiple objection types"""
        message = "I don't see the value and I have no experience in this field"
        
        result = await objection_handling_service.handle_objection(message)
        
        # Should detect at least one objection
        assert result["objection_detected"] is True
        assert result["objection_type"] in [ObjectionType.NO_VALUE, ObjectionType.EXPERIENCE]


if __name__ == "__main__":
    pytest.main([__file__])
