"""
Test runner script for GrowthHive test suite
Provides utilities for running tests, managing test data, and generating reports
"""

import os
import sys
import asyncio
import argparse
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Test database configuration
TEST_DATABASE_URL = "postgresql+asyncpg://postgres:root@localhost:5432/growthhive_test"


class TestRunner:
    """Test runner with database management and reporting"""
    
    def __init__(self):
        self.test_db_url = TEST_DATABASE_URL
        self.engine = None
        
    async def setup_test_database(self):
        """Setup test database"""
        print("🔧 Setting up test database...")
        
        # Create engine for database operations
        self.engine = create_async_engine(
            self.test_db_url,
            echo=False,
            pool_pre_ping=True
        )
        
        try:
            # Test connection
            async with self.engine.begin() as conn:
                await conn.execute(sa.text("SELECT 1"))
            print("✅ Test database connection successful")
            
        except Exception as e:
            print(f"❌ Test database connection failed: {e}")
            print("💡 Make sure PostgreSQL is running and test database exists")
            return False
            
        return True
    
    async def flush_test_data(self):
        """Flush all test data from database"""
        print("🧹 Flushing test data...")
        
        if not self.engine:
            await self.setup_test_database()
        
        try:
            async with self.engine.begin() as conn:
                # Delete test data in reverse dependency order
                tables_to_clean = [
                    "conversation_message",
                    "booking", 
                    "lead_status_history",
                    "communication",
                    "lead",
                    "messaging_rule"
                ]
                
                for table in tables_to_clean:
                    try:
                        # Delete recent test data (last 24 hours)
                        await conn.execute(sa.text(
                            f"DELETE FROM {table} WHERE created_at >= NOW() - INTERVAL '24 hours'"
                        ))
                        print(f"  ✅ Cleaned {table}")
                    except Exception as e:
                        print(f"  ⚠️  Could not clean {table}: {e}")
                
                print("✅ Test data flushed successfully")
                
        except Exception as e:
            print(f"❌ Failed to flush test data: {e}")
            return False
            
        return True
    
    async def create_test_database(self):
        """Create test database if it doesn't exist"""
        print("🏗️  Creating test database...")
        
        # Connect to postgres database to create test database
        postgres_url = "postgresql+asyncpg://postgres:root@localhost:5432/postgres"
        
        try:
            engine = create_async_engine(postgres_url, isolation_level="AUTOCOMMIT")
            
            async with engine.begin() as conn:
                # Check if test database exists
                result = await conn.execute(sa.text(
                    "SELECT 1 FROM pg_database WHERE datname = 'growthhive_test'"
                ))
                
                if not result.fetchone():
                    # Create test database
                    await conn.execute(sa.text("CREATE DATABASE growthhive_test"))
                    print("✅ Test database created")
                else:
                    print("✅ Test database already exists")
            
            await engine.dispose()
            return True
            
        except Exception as e:
            print(f"❌ Failed to create test database: {e}")
            return False
    
    def run_tests(self, test_pattern=None, markers=None, verbose=False, coverage=True):
        """Run tests with specified options"""
        
        cmd = ["python", "-m", "pytest"]
        
        # Add test pattern
        if test_pattern:
            cmd.append(test_pattern)
        else:
            cmd.append("tests/")
        
        # Add markers
        if markers:
            cmd.extend(["-m", markers])
        
        # Add verbosity
        if verbose:
            cmd.append("-v")
        else:
            cmd.append("-q")
        
        # Add coverage
        if coverage:
            cmd.extend([
                "--cov=app",
                "--cov-report=term-missing:skip-covered",
                "--cov-report=html:htmlcov"
            ])
        
        # Add other useful options
        cmd.extend([
            "--tb=short",
            "--maxfail=10",
            "--durations=10"
        ])
        
        print(f"🚀 Running tests: {' '.join(cmd)}")
        
        # Run tests
        result = subprocess.run(cmd, cwd=project_root)
        return result.returncode == 0
    
    def generate_test_report(self):
        """Generate test report"""
        print("📊 Generating test report...")
        
        report_dir = project_root / "test_reports"
        report_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"test_report_{timestamp}.html"
        
        # Generate HTML report
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "--html", str(report_file),
            "--self-contained-html",
            "--cov=app",
            "--cov-report=html:htmlcov"
        ]
        
        result = subprocess.run(cmd, cwd=project_root, capture_output=True)
        
        if result.returncode == 0:
            print(f"✅ Test report generated: {report_file}")
            print(f"📊 Coverage report: {project_root}/htmlcov/index.html")
        else:
            print(f"❌ Failed to generate test report")
            print(result.stderr.decode())
        
        return result.returncode == 0
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.engine:
            await self.engine.dispose()


async def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="GrowthHive Test Runner")
    
    parser.add_argument(
        "--setup", 
        action="store_true",
        help="Setup test database"
    )
    
    parser.add_argument(
        "--flush",
        action="store_true", 
        help="Flush test data"
    )
    
    parser.add_argument(
        "--create-db",
        action="store_true",
        help="Create test database"
    )
    
    parser.add_argument(
        "--pattern",
        help="Test pattern to run (e.g., test_sms_*.py)"
    )
    
    parser.add_argument(
        "--markers",
        help="Test markers to run (e.g., 'unit', 'integration', 'sms and followup')"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Disable coverage reporting"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate HTML test report"
    )
    
    parser.add_argument(
        "--all",
        action="store_true",
        help="Run all tests with full setup"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        # Handle specific actions
        if args.create_db:
            success = await runner.create_test_database()
            if not success:
                sys.exit(1)
        
        if args.setup:
            success = await runner.setup_test_database()
            if not success:
                sys.exit(1)
        
        if args.flush:
            success = await runner.flush_test_data()
            if not success:
                sys.exit(1)
        
        if args.all:
            # Full test suite with setup
            print("🎯 Running complete test suite...")
            
            await runner.create_test_database()
            await runner.setup_test_database()
            await runner.flush_test_data()
            
            success = runner.run_tests(
                test_pattern=args.pattern,
                markers=args.markers,
                verbose=args.verbose,
                coverage=not args.no_coverage
            )
            
            if args.report:
                runner.generate_test_report()
            
            if not success:
                print("❌ Some tests failed")
                sys.exit(1)
            else:
                print("✅ All tests passed!")
        
        elif args.pattern or args.markers or not any([args.setup, args.flush, args.create_db]):
            # Run tests
            success = runner.run_tests(
                test_pattern=args.pattern,
                markers=args.markers,
                verbose=args.verbose,
                coverage=not args.no_coverage
            )
            
            if args.report:
                runner.generate_test_report()
            
            if not success:
                sys.exit(1)
        
    finally:
        await runner.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
