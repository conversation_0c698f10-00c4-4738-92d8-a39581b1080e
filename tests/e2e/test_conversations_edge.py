"""
E2E Tests - Edge Cases & Robustness
Tests for ambiguous inputs, provider failures, long messages, and timezone handling
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch
import pytz

from tests.helpers.conversation_harness import ConversationHarness, ConversationTurn
from tests.helpers.factories import LeadFactory
from tests.mocks.kudosity_stub import KudosityStub, create_long_message
from tests.mocks.zoho_bookings_stub import ZohoBookingsStub
from tests.mocks.search_stub import SearchStub


@pytest.mark.e2e
class TestAmbiguousInputHandling:
    """Test handling of ambiguous and unclear user inputs."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61412345678")
    
    @pytest.mark.asyncio
    async def test_ambiguous_evening_clarification(self, conversation_harness, test_lead, frozen_time):
        """
        Test: 'evening sometime' → two clarifications then offer concrete options
        """
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            # Initial ambiguous request
            ConversationTurn(
                user_message="Can we meet sometime in the evening?",
                expected_response_contains="evening",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # Still ambiguous
            ConversationTurn(
                user_message="Yeah, evening works for me",
                expected_response_contains="specific",  # Should ask for more specific time
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # After 2 clarifications, should offer concrete options
            ConversationTurn(
                user_message="Just any evening time",
                expected_response_contains="available",  # Should show specific available times
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # User selects from options
            ConversationTurn(
                user_message="The 6pm slot works",
                expected_response_contains="booked",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 4
        assert all(result.success for result in results)
        
        # Should ask for clarification initially
        assert any(word in results[0].agent_response.lower() 
                  for word in ["evening", "time", "prefer"])
        
        # Should ask for more specific time
        assert any(word in results[1].agent_response.lower() 
                  for word in ["specific", "particular", "exact"])
        
        # Should offer concrete options after max clarifications
        assert any(word in results[2].agent_response.lower() 
                  for word in ["available", "options", "times"])
        
        # Should confirm booking
        assert "booked" in results[3].agent_response.lower()
    
    @pytest.mark.asyncio
    async def test_vague_date_handling(self, conversation_harness, test_lead, frozen_time):
        """Test handling of vague date expressions."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Can we meet soon?",
                expected_response_contains="when",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Maybe next week sometime?",
                expected_response_contains="next week",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Wednesday looks good",
                expected_response_contains="Wednesday",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="2pm",
                expected_response_contains="booked",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should guide user from vague to specific
        assert "when" in results[0].agent_response.lower()
        assert "Wednesday" in results[2].agent_response
    
    @pytest.mark.asyncio
    async def test_conflicting_information_handling(self, conversation_harness, test_lead, frozen_time):
        """Test handling when user provides conflicting information."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I want to meet tomorrow at 2pm",
                expected_response_contains="2:00 PM",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Actually, make that 3pm on Friday instead",
                expected_response_contains="Friday",  # Should handle the change
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Yes, Friday at 3pm is perfect",
                expected_response_contains="booked",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should handle the change gracefully
        assert "Friday" in results[1].agent_response
        assert "3" in results[1].agent_response or "3:00" in results[1].agent_response


@pytest.mark.e2e
class TestProviderFailureRecovery:
    """Test recovery from provider failures and network issues."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61423456789")
    
    @pytest.mark.asyncio
    async def test_zoho_availability_failure_recovery(self, conversation_harness, test_lead, meeting_agent_mocks, frozen_time):
        """
        Test: 2x 500 on availability then success; single final booking only
        """
        # Configure Zoho to fail availability checks twice, then succeed
        zoho_stub = meeting_agent_mocks['zoho']
        
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        # First attempt - availability fails
        zoho_stub.set_failure_mode(availability=True, reason="Server error 500")
        
        turns_attempt1 = [
            ConversationTurn(
                user_message="Can we meet tomorrow at 2pm?",
                expected_response_contains="trouble",  # Should indicate issue
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results_attempt1 = await conversation_harness.run_conversation(turns_attempt1)
        
        # Second attempt - still fails
        turns_attempt2 = [
            ConversationTurn(
                user_message="How about Monday then?",
                expected_response_contains="issue",  # Should still indicate issue
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results_attempt2 = await conversation_harness.run_conversation(turns_attempt2)
        
        # Third attempt - now succeeds
        zoho_stub.set_failure_mode(availability=False)  # Reset failure mode
        
        turns_attempt3 = [
            ConversationTurn(
                user_message="Let me try Wednesday at 1pm",
                expected_response_contains="available",  # Should now work
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Yes, book it",
                expected_response_contains="booked",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results_attempt3 = await conversation_harness.run_conversation(turns_attempt3)
        
        # Should have recovered and made single booking
        assert "trouble" in results_attempt1[0].agent_response.lower() or "issue" in results_attempt1[0].agent_response.lower()
        assert "available" in results_attempt3[0].agent_response.lower()
        assert "booked" in results_attempt3[1].agent_response.lower()
        
        # Verify only one booking was created
        all_bookings = zoho_stub.get_all_bookings()
        assert len(all_bookings) == 1
    
    @pytest.mark.asyncio
    async def test_booking_failure_with_retry(self, conversation_harness, test_lead, meeting_agent_mocks, frozen_time):
        """Test booking failure with automatic retry."""
        zoho_stub = meeting_agent_mocks['zoho']
        
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Book me for tomorrow at 2pm",
                expected_response_contains="2:00 PM",
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        # First booking attempt fails
        zoho_stub.set_failure_mode(booking=True, reason="Slot no longer available")
        
        confirm_turns = [
            ConversationTurn(
                user_message="Yes, confirm it",
                expected_response_contains="trouble",  # Should indicate booking issue
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results1 = await conversation_harness.run_conversation(turns)
        results2 = await conversation_harness.run_conversation(confirm_turns)
        
        # Should handle booking failure gracefully
        assert any(word in results2[0].agent_response.lower() 
                  for word in ["trouble", "issue", "available", "try"])
    
    @pytest.mark.asyncio
    async def test_sms_sending_failure_recovery(self, conversation_harness, test_lead, meeting_agent_mocks, frozen_time):
        """Test recovery when SMS sending fails."""
        kudosity_stub = meeting_agent_mocks['kudosity']
        
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        # Configure SMS to fail
        kudosity_stub.set_failure_mode(True, "Network timeout")
        
        turns = [
            ConversationTurn(
                user_message="I'm interested in your franchise",
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should handle SMS failure gracefully (conversation processing should still work)
        assert len(results) == 1
        
        # Check that failure was recorded
        failed_sends = kudosity_stub.get_failed_sends()
        assert len(failed_sends) >= 1
        assert "Network timeout" in failed_sends[0].error


@pytest.mark.e2e
class TestLongMessageHandling:
    """Test handling of very long messages and responses."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61434567890")
    
    @pytest.mark.asyncio
    async def test_long_user_message_handling(self, conversation_harness, test_lead, frozen_time):
        """Test handling of very long user messages."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        # Create a very long message
        long_message = (
            "Hi there, I'm really interested in your franchise opportunity and I have a lot of questions. "
            "First, I want to know about the initial investment required, including franchise fees, equipment costs, "
            "working capital requirements, and any other upfront expenses. Second, I'm curious about the ongoing "
            "royalty structure and marketing fees. Third, I'd like to understand the territory rights and exclusivity. "
            "Fourth, what kind of training and support do you provide? Fifth, what are the typical profit margins "
            "and return on investment? Sixth, how long does it typically take to break even? Seventh, what are "
            "the requirements for franchisees in terms of experience and qualifications? I'm located in Sydney "
            "and would prefer to meet sometime next week if possible, preferably in the afternoon. "
            "Please let me know what times work best for you."
        )
        
        turns = [
            ConversationTurn(
                user_message=long_message,
                expected_response_contains="questions",  # Should acknowledge multiple questions
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should handle long message appropriately
        response = results[0].agent_response
        assert len(response) > 50  # Should provide substantive response
        
        # Should acknowledge the complexity
        assert any(word in response.lower() 
                  for word in ["questions", "information", "discuss", "meeting"])
    
    @pytest.mark.asyncio
    async def test_long_response_segmentation(self, conversation_harness, test_lead, meeting_agent_mocks, frozen_time):
        """
        Test: Very long answers → splitter triggers; segments validated
        """
        kudosity_stub = meeting_agent_mocks['kudosity']
        
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        # Seed search with very long answer
        search_stub = meeting_agent_mocks['search']
        long_answer = (
            "Our comprehensive franchise program includes multiple components that I'd be happy to explain in detail. "
            "The initial franchise fee is $50,000 plus GST, which covers your territory rights, initial training program, "
            "operations manual, marketing materials, and ongoing support for the first year. In addition to the franchise fee, "
            "you'll need working capital of approximately $30,000 to $50,000 depending on your territory size and local market conditions. "
            "Equipment costs vary by location but typically range from $15,000 to $25,000 for the essential items needed to get started. "
            "Our training program is comprehensive and includes two weeks of intensive training at our head office, followed by "
            "one week of on-site training in your territory. We also provide ongoing support through monthly check-ins, "
            "quarterly business reviews, and access to our online resource portal with updated marketing materials, "
            "operational procedures, and best practices from other successful franchisees in our network."
        )
        
        search_stub.seed_knowledge_item(
            long_answer,
            "comprehensive",
            ["franchise", "program", "fee", "training", "support"]
        )
        
        turns = [
            ConversationTurn(
                user_message="Tell me everything about your franchise program",
                expected_response_contains="franchise",
                expected_db_writes=["conversation_messages_created"],
                expected_sms_segments=3  # Should be split into multiple segments
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Verify message was segmented
        sent_messages = kudosity_stub.get_sent_messages(test_lead.phone)
        assert len(sent_messages) >= 1
        
        last_message = kudosity_stub.get_last_message(test_lead.phone)
        assert len(last_message.segments) >= 2  # Should be split into multiple segments
        
        # Verify each segment is within limits
        for segment in last_message.segments:
            assert len(segment) <= 160  # Standard SMS limit
        
        # Verify reassembly works
        reassembled = "".join(last_message.segments)
        assert "franchise" in reassembled.lower()
        assert "$50,000" in reassembled


@pytest.mark.e2e
class TestTimezoneHandling:
    """Test timezone handling for different user locations."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.mark.asyncio
    async def test_different_user_timezone(self, conversation_harness, db_session, andy_assertions, frozen_time):
        """
        Test: Different user TZ (e.g., Australia/Sydney) → localize correctly; DB stored UTC
        """
        # Create lead in Sydney timezone
        factory = LeadFactory(db_session)
        sydney_lead = await factory.create_qualified_lead(
            first_name="Sydney",
            last_name="User",
            phone="+***********",
            location="Sydney, NSW"
        )
        
        await conversation_harness.setup_lead({
            "first_name": sydney_lead.first_name,
            "phone": sydney_lead.phone,
            "timezone": "Australia/Sydney"
        })
        
        turns = [
            ConversationTurn(
                user_message="Can we meet tomorrow at 2pm?",
                expected_response_contains="2:00 PM",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Yes, book it",
                expected_response_contains="booked",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should display time in user's local timezone
        confirmation_response = results[0].agent_response
        assert "2:00 PM" in confirmation_response
        
        # Should show proper date formatting
        assert any(month in confirmation_response for month in ["August", "September"])
        
        # Verify database storage is in UTC
        from app.models.booking import Booking
        from sqlalchemy import select
        
        booking_result = await db_session.execute(
            select(Booking).where(Booking.lead_id == sydney_lead.id)
        )
        booking = booking_result.scalar_one_or_none()
        
        if booking:
            andy_assertions.assert_utc_storage(booking.start_time)
            andy_assertions.assert_utc_storage(booking.created_at)
    
    @pytest.mark.asyncio
    async def test_timezone_conversion_accuracy(self, conversation_harness, db_session, frozen_time):
        """Test accuracy of timezone conversions."""
        factory = LeadFactory(db_session)
        
        # Test different timezones
        timezone_tests = [
            ("Australia/Sydney", "+***********"),
            ("America/New_York", "+1234567890"),
            ("Europe/London", "+***********"),
            ("Asia/Tokyo", "+***********")
        ]
        
        for timezone_name, phone in timezone_tests:
            lead = await factory.create_qualified_lead(
                first_name=f"User_{timezone_name.split('/')[-1]}",
                phone=phone
            )
            
            await conversation_harness.setup_lead({
                "first_name": lead.first_name,
                "phone": lead.phone,
                "timezone": timezone_name
            })
            
            turns = [
                ConversationTurn(
                    user_message="Book me for tomorrow at 2pm",
                    expected_response_contains="2:00 PM"
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            # Should handle timezone correctly
            assert "2:00 PM" in results[0].agent_response
            
            # Reset for next test
            conversation_harness.conversation_history.clear()


@pytest.mark.e2e
class TestConversationRobustness:
    """Test overall conversation robustness and error recovery."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_qualified_lead(phone="+61456789123")
    
    @pytest.mark.asyncio
    async def test_conversation_timeout_handling(self, conversation_harness, test_lead, frozen_time):
        """Test handling of conversation timeouts."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        # Start conversation
        turns1 = [
            ConversationTurn(
                user_message="I want to schedule a meeting",
                expected_response_contains="meeting"
            )
        ]
        
        results1 = await conversation_harness.run_conversation(turns1)
        
        # Fast forward past conversation timeout (30 minutes)
        frozen_time.tick(delta=timedelta(minutes=35))
        
        # Continue conversation after timeout
        turns2 = [
            ConversationTurn(
                user_message="Tomorrow at 2pm",
                expected_response_contains="2:00 PM"  # Should still work
            )
        ]
        
        results2 = await conversation_harness.run_conversation(turns2)
        
        # Should handle timeout gracefully
        assert "2:00 PM" in results2[0].agent_response
    
    @pytest.mark.asyncio
    async def test_malformed_input_handling(self, conversation_harness, test_lead, frozen_time):
        """Test handling of malformed or nonsensical inputs."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        malformed_inputs = [
            "asdfghjkl qwertyuiop",  # Random characters
            "123 456 789 000",       # Just numbers
            "!@#$%^&*()",           # Just symbols
            "",                      # Empty string
            "   ",                   # Just spaces
        ]
        
        for malformed_input in malformed_inputs:
            turns = [
                ConversationTurn(
                    user_message=malformed_input,
                    expected_db_writes=["conversation_messages_created"]
                )
            ]
            
            results = await conversation_harness.run_conversation(turns)
            
            # Should handle gracefully without crashing
            assert len(results) == 1
            assert results[0].success is True
            assert len(results[0].agent_response) > 0
            
            # Reset for next test
            conversation_harness.conversation_history.clear()
