"""
E2E Tests — Workflow Conformance
Tests complete workflow conformance with exact text matching and no follow-ups
"""

import pytest
from unittest.mock import patch
from tests.helpers.conversation_harness import ConversationHarness


class TestCompleteWorkflowConformance:
    """Test complete workflow from introduction to confirmation"""
    
    @pytest.fixture
    def conversation_harness(self):
        """Create conversation harness for E2E tests"""
        return ConversationHarness()
    
    @pytest.fixture
    def test_lead_data(self):
        """Standard test lead data"""
        return {
            "name": "<PERSON>",
            "phone_number": "+61400000000",
            "email": "<EMAIL>",
            "funds_to_invest": 120000
        }
    
    @pytest.mark.asyncio
    async def test_intro_to_confirmation_corporate_background(
        self, 
        conversation_harness, 
        test_lead_data,
        task_queue_monitor,
        no_followups_assertion,
        emoji_assertion,
        acknowledgment_assertion
    ):
        """Test: Intro -> Q1/Q2/Q3 -> Scheduling -> Confirmation (Corporate Background)"""
        
        conversation_script = [
            {
                "user": "Yes, I have time to chat",
                "expected_contains": ["what you do for work"],
                "expected_stage": "work_background"
            },
            {
                "user": "I'm a marketing manager at a tech company",
                "expected_contains": ["different background", "what made you enquire"],
                "expected_stage": "motivation",
                "assert_acknowledgment": True
            },
            {
                "user": "I'm looking for more flexibility and want to be my own boss",
                "expected_contains": ["budget", "$120", "access to"],
                "expected_stage": "budget",
                "assert_acknowledgment": True
            },
            {
                "user": "Yes, that's correct, I have access to that amount",
                "expected_contains": ["Total Franchise Fee", "$120K", "50%", "finance option"],
                "expected_stage": "scheduling",
                "assert_acknowledgment": True
            },
            {
                "user": "That sounds good",
                "expected_contains": ["phone call with Andy", "available at"],
                "expected_stage": "confirmation"
            },
            {
                "user": "Monday at 2:00 PM works for me",
                "expected_contains": ["Thanks for the confirmation", "Looking forward"],
                "expected_stage": "completed",
                "expected_booking": True
            }
        ]
        
        # Clear task queue before test
        task_queue_monitor.clear()
        
        results = await conversation_harness.run_conversation_script(
            lead_data=test_lead_data,
            script=conversation_script
        )
        
        # Validate each turn
        for i, (result, script_turn) in enumerate(zip(results, conversation_script)):
            assert result.success, f"Turn {i+1} failed: {result.error}"
            
            # Check expected content
            response = result.agent_response.lower()
            for expected_text in script_turn["expected_contains"]:
                assert expected_text.lower() in response, \
                    f"Turn {i+1}: Expected '{expected_text}' in response: {result.agent_response}"
            
            # Check stage progression
            if "expected_stage" in script_turn:
                assert result.conversation_stage == script_turn["expected_stage"], \
                    f"Turn {i+1}: Expected stage {script_turn['expected_stage']}, got {result.conversation_stage}"
            
            # Check acknowledgment-first pattern
            if script_turn.get("assert_acknowledgment") and i > 0:
                user_input = conversation_script[i-1]["user"]
                acknowledgment_assertion(result.agent_response, user_input)
            
            # Check no emojis
            emoji_assertion(result.agent_response)
            
            # Check booking if expected
            if script_turn.get("expected_booking"):
                booking_data = result.processing_metadata.get("booking_result")
                assert booking_data is not None, "Should have booking result"
                assert booking_data.get("success") is True, "Booking should succeed"
        
        # Assert no follow-ups were scheduled throughout the entire conversation
        no_followups_assertion(task_queue_monitor, None)
    
    @pytest.mark.asyncio
    async def test_intro_to_confirmation_trades_background(
        self,
        conversation_harness,
        test_lead_data,
        task_queue_monitor,
        emoji_assertion,
        acknowledgment_assertion
    ):
        """Test: Intro -> Q1/Q2/Q3 -> Scheduling -> Confirmation (Trades Background)"""
        
        conversation_script = [
            {
                "user": "Yes, I have a few minutes",
                "expected_contains": ["what you do for work"],
                "expected_stage": "work_background"
            },
            {
                "user": "I'm a plumber, been doing it for 15 years",
                "expected_contains": ["what made you enquire"],
                "expected_stage": "motivation",
                "assert_acknowledgment": True,
                "should_not_contain": ["different background"]  # Trades shouldn't get this
            },
            {
                "user": "I want to ease into retirement and need something less physical",
                "expected_contains": ["budget", "$120", "access to"],
                "expected_stage": "budget",
                "assert_acknowledgment": True
            },
            {
                "user": "Yes, I have that amount available",
                "expected_contains": ["Total Franchise Fee", "$120K", "50%"],
                "expected_stage": "scheduling",
                "assert_acknowledgment": True
            },
            {
                "user": "Sounds reasonable",
                "expected_contains": ["phone call with Andy", "available at"],
                "expected_stage": "confirmation"
            },
            {
                "user": "Tuesday at 10:00 AM would be perfect",
                "expected_contains": ["Thanks for the confirmation"],
                "expected_stage": "completed",
                "expected_booking": True
            }
        ]
        
        task_queue_monitor.clear()
        
        results = await conversation_harness.run_conversation_script(
            lead_data=test_lead_data,
            script=conversation_script
        )
        
        # Validate trades-specific workflow
        for i, (result, script_turn) in enumerate(zip(results, conversation_script)):
            assert result.success, f"Turn {i+1} failed: {result.error}"
            
            # Check expected content
            response = result.agent_response.lower()
            for expected_text in script_turn["expected_contains"]:
                assert expected_text.lower() in response, \
                    f"Turn {i+1}: Expected '{expected_text}' in response"
            
            # Check content that should NOT be present
            if "should_not_contain" in script_turn:
                for forbidden_text in script_turn["should_not_contain"]:
                    assert forbidden_text.lower() not in response, \
                        f"Turn {i+1}: Should not contain '{forbidden_text}' in response"
            
            # Validate acknowledgment and emoji-free
            if script_turn.get("assert_acknowledgment") and i > 0:
                user_input = conversation_script[i-1]["user"]
                acknowledgment_assertion(result.agent_response, user_input)
            
            emoji_assertion(result.agent_response)
    
    @pytest.mark.asyncio
    async def test_objection_handling_exact_responses(
        self,
        conversation_harness,
        test_lead_data,
        task_queue_monitor,
        emoji_assertion
    ):
        """Test objection handling with exact workflow responses"""
        
        conversation_script = [
            {
                "user": "Yes, I have time",
                "expected_contains": ["what you do for work"]
            },
            {
                "user": "I'm a project manager",
                "expected_contains": ["different background"]
            },
            {
                "user": "I don't see the value in this opportunity",
                "expected_contains": [
                    "I understand however we are yet to walk you through the business potential",
                    "Australia's largest lawn care company",
                    "Let's walk you through all on the call"
                ],
                "objection_type": "no_value"
            },
            {
                "user": "I haven't heard of your company before",
                "expected_contains": [
                    "Fair enough",
                    "Coochie has been operating for over 30 years",
                    "prominent brand in the NSW & QLD"
                ],
                "objection_type": "not_heard"
            },
            {
                "user": "How much money can I actually make?",
                "expected_contains": [
                    "A totally fair question",
                    "Some of our franchise partners",
                    "earn over $200K net",
                    "3-year projections together in our meeting"
                ],
                "objection_type": "income"
            },
            {
                "user": "What's the royalty fee?",
                "expected_contains": [
                    "10% royalty and 3% marketing fund",
                    "total is 13% of your gross sales"
                ],
                "objection_type": "royalty"
            }
        ]
        
        task_queue_monitor.clear()
        
        results = await conversation_harness.run_conversation_script(
            lead_data=test_lead_data,
            script=conversation_script
        )
        
        # Validate exact objection responses
        for i, (result, script_turn) in enumerate(zip(results, conversation_script)):
            assert result.success, f"Turn {i+1} failed: {result.error}"
            
            response = result.agent_response
            
            # Check exact workflow text for objections
            for expected_text in script_turn["expected_contains"]:
                assert expected_text in response, \
                    f"Turn {i+1}: Expected exact text '{expected_text}' in response: {response}"
            
            # Validate objection detection if specified
            if "objection_type" in script_turn:
                objection_data = result.processing_metadata.get("objection_handled")
                assert objection_data is not None, f"Turn {i+1}: Should detect objection"
            
            emoji_assertion(response)
        
        # Ensure no follow-ups scheduled during objection handling
        followup_tasks = [
            task for task in task_queue_monitor 
            if "followup" in task["task_name"].lower()
        ]
        assert len(followup_tasks) == 0, f"Objection handling should not schedule follow-ups: {followup_tasks}"
    
    @pytest.mark.asyncio
    async def test_lead_silence_mid_qualification(
        self,
        conversation_harness,
        test_lead_data,
        task_queue_monitor,
        log_capture
    ):
        """Test: Lead silence mid-qualification -> polite check-in, NO automated follow-ups"""
        
        conversation_script = [
            {
                "user": "Yes, I have time",
                "expected_contains": ["what you do for work"]
            },
            {
                "user": "I'm a sales director",
                "expected_contains": ["different background"]
            },
            # Lead goes silent here - simulate with empty/unclear response
            {
                "user": "",  # Silent/no response
                "expected_contains": [
                    "Just checking if you're still keen to explore this opportunity",
                    "Happy to keep the chat going",
                    "set up a quick call",
                    "How does that sound"
                ],
                "is_silence_response": True
            }
        ]
        
        task_queue_monitor.clear()
        
        results = await conversation_harness.run_conversation_script(
            lead_data=test_lead_data,
            script=conversation_script
        )
        
        # Check silence handling response
        silence_result = results[-1]
        assert silence_result.success, "Silence handling should succeed"
        
        response = silence_result.agent_response
        
        # Should contain exact workflow text for silence
        expected_silence_phrases = [
            "Just checking if you're still keen to explore this opportunity",
            "Happy to keep the chat going and set up a quick call",
            "How does that sound"
        ]
        
        for phrase in expected_silence_phrases:
            assert phrase in response, f"Expected silence phrase '{phrase}' in response: {response}"
        
        # CRITICAL: No automated follow-ups should be scheduled
        followup_tasks = [
            task for task in task_queue_monitor 
            if "followup" in task["task_name"].lower()
        ]
        assert len(followup_tasks) == 0, \
            f"Lead silence should NOT schedule automated follow-ups: {followup_tasks}"
        
        # Check logs for suppression evidence
        log_content = log_capture.getvalue()
        assert "suppressed" in log_content.lower() or "WORKFLOW_ANDY_NO_FOLLOWUPS" in log_content, \
            "Should have evidence of follow-up suppression in logs"
    
    @pytest.mark.asyncio
    async def test_budget_path_variations(
        self,
        conversation_harness,
        test_lead_data,
        emoji_assertion
    ):
        """Test different budget response paths with exact workflow text"""
        
        # Test budget denial -> ask for amount -> provide amount
        conversation_script = [
            {
                "user": "Yes, I have time",
                "expected_contains": ["what you do for work"]
            },
            {
                "user": "I'm an operations manager",
                "expected_contains": ["different background"]
            },
            {
                "user": "Looking to diversify my income",
                "expected_contains": ["budget", "$120", "access to"]
            },
            {
                "user": "No, that's not correct",
                "expected_contains": ["How much funds do you have access to invest"],
                "budget_response": "denied"
            },
            {
                "user": "I have about $150,000 available",
                "expected_contains": [
                    "Total Franchise Fee for Coochie Hydrogreen is $120K(ex. GST)",
                    "50% in advance and 50% after 12 months",
                    "finance option"
                ],
                "budget_response": "amount_provided"
            }
        ]
        
        results = await conversation_harness.run_conversation_script(
            lead_data=test_lead_data,
            script=conversation_script
        )
        
        # Validate budget path responses
        for i, (result, script_turn) in enumerate(zip(results, conversation_script)):
            assert result.success, f"Turn {i+1} failed: {result.error}"
            
            response = result.agent_response
            
            # Check exact workflow text
            for expected_text in script_turn["expected_contains"]:
                assert expected_text in response, \
                    f"Turn {i+1}: Expected exact text '{expected_text}' in response: {response}"
            
            emoji_assertion(response)
    
    @pytest.mark.asyncio
    async def test_conversation_context_retention(
        self,
        conversation_harness,
        test_lead_data
    ):
        """Test that conversation context is retained throughout workflow"""
        
        conversation_script = [
            {"user": "Yes, I have time", "expected_contains": ["what you do for work"]},
            {"user": "I'm a finance director", "expected_contains": ["different background"]},
            {"user": "Want more work-life balance", "expected_contains": ["budget"]},
            {"user": "Yes, I have that amount", "expected_contains": ["$120K"]}
        ]
        
        results = await conversation_harness.run_conversation_script(
            lead_data=test_lead_data,
            script=conversation_script
        )
        
        # Check that context is retained
        for i, result in enumerate(results):
            if i >= 1:  # After work background
                # Should have work background in context
                context = result.processing_metadata.get("conversation_context", {})
                assert "work_background" in str(context).lower() or \
                       "finance" in str(context).lower() or \
                       "corporate" in str(context).lower(), \
                       f"Turn {i+1}: Work background context should be retained"
            
            if i >= 2:  # After motivation
                # Should have motivation in context
                context = result.processing_metadata.get("conversation_context", {})
                assert "motivation" in str(context).lower() or \
                       "balance" in str(context).lower(), \
                       f"Turn {i+1}: Motivation context should be retained"
