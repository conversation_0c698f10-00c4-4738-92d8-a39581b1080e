"""
E2E Tests - Happy Path Conversations
Tests for complete conversation flows from lead intro to meeting booking
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch

from tests.helpers.conversation_harness import ConversationHarness, ConversationTurn, ConversationScripts
from tests.helpers.factories import LeadFactory
from tests.mocks.kudosity_stub import KudosityStub
from tests.mocks.zoho_bookings_stub import ZohoBookingsStub
from tests.mocks.search_stub import SearchStub


@pytest.mark.e2e
class TestHappyPathConversations:
    """Test complete happy path conversation flows."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    def meeting_agent_mocks(self):
        """Set up all mocks for meeting agent."""
        kudosity_stub = KudosityStub()
        zoho_stub = ZohoBookingsStub()
        search_stub = SearchStub()
        
        # Configure availability
        zoho_stub.set_availability({
            "2025-08-30": ["09:30", "12:00", "15:00"],
            "2025-09-01": ["10:00", "13:00", "16:30"],
            "2025-09-03": ["11:00", "13:00", "15:00"]
        })
        
        # Seed knowledge base
        search_stub.seed_knowledge_item(
            "Our franchise fees start at $50,000 plus GST for most territories.",
            "fees",
            ["franchise", "fees", "cost", "price"]
        )
        
        with patch('app.integrations.kudosity.client.KudosityClient', return_value=kudosity_stub), \
             patch('app.services.zoho_bookings_service.ZohoBookingsService', return_value=zoho_stub), \
             patch('app.rag.search.SearchService', return_value=search_stub):
            yield {
                'kudosity': kudosity_stub,
                'zoho': zoho_stub,
                'search': search_stub
            }
    
    @pytest.fixture
    async def new_lead(self, db_session):
        """Create a new lead for testing."""
        factory = LeadFactory(db_session)
        return await factory.create_lead(
            first_name="Sarah",
            last_name="Johnson",
            phone="+61412345678",
            email="<EMAIL>",
            status="New"
        )
    
    @pytest.mark.asyncio
    async def test_new_lead_intro_to_meeting_booking(self, conversation_harness, new_lead, andy_assertions, frozen_time):
        """
        Test complete flow: New lead intro → Q&A → Meeting booked → Follow-up turned off
        """
        await conversation_harness.setup_lead({
            "first_name": new_lead.first_name,
            "last_name": new_lead.last_name,
            "phone": new_lead.phone,
            "email": new_lead.email
        })
        
        turns = [
            # Initial introduction
            ConversationTurn(
                user_message="Hi, I got your details from the website. I'm interested in your franchise opportunity.",
                expected_response_contains="franchise",
                expected_db_writes=["conversation_messages_created"],
                should_cancel_followup=True
            ),
            
            # Q&A about fees
            ConversationTurn(
                user_message="What are your franchise fees?",
                expected_response_contains="$50,000",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # Express interest in meeting
            ConversationTurn(
                user_message="That sounds reasonable. Can we schedule a meeting to discuss this further?",
                expected_response_contains="meeting",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # Provide specific time
            ConversationTurn(
                user_message="How about tomorrow at 2pm?",
                expected_response_contains="2:00 PM",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # Confirm booking
            ConversationTurn(
                user_message="Perfect, let's book it",
                expected_response_contains="booked",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Verify complete flow
        assert len(results) == 5
        assert all(result.success for result in results)
        
        # Verify progression
        assert "franchise" in results[0].agent_response.lower()
        assert "$50,000" in results[1].agent_response
        assert "meeting" in results[2].agent_response.lower()
        assert "2:00 PM" in results[3].agent_response
        assert "booked" in results[4].agent_response.lower()
        
        # Verify no emojis throughout conversation
        for result in results:
            andy_assertions.assert_no_emojis(result.agent_response)
        
        # Verify meeting link provided
        final_response = results[4].agent_response
        assert "zoom.us" in final_response or "meeting" in final_response.lower()
    
    @pytest.mark.asyncio
    async def test_qualified_lead_status_transitions(self, conversation_harness, new_lead, db_session, frozen_time):
        """
        Test status transitions: New → Contacted → Qualified with correct timestamps
        """
        await conversation_harness.setup_lead({
            "first_name": new_lead.first_name,
            "phone": new_lead.phone
        })
        
        turns = [
            # First contact - should move to Contacted
            ConversationTurn(
                user_message="Hello, I'm interested in your franchise",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # Show strong interest - should move to Qualified
            ConversationTurn(
                user_message="I have $100k to invest and I'm ready to proceed",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            # Book meeting - should move to Meeting Scheduled
            ConversationTurn(
                user_message="Let's schedule a meeting for tomorrow at 10am",
                expected_db_writes=["conversation_messages_created"]
            ),
            
            ConversationTurn(
                user_message="Yes, book it",
                expected_db_writes=["conversation_messages_created", "bookings_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Verify lead status progression
        from app.models.lead import Lead
        from sqlalchemy import select
        
        updated_lead_result = await db_session.execute(
            select(Lead).where(Lead.id == new_lead.id)
        )
        updated_lead = updated_lead_result.scalar_one()
        
        # Should have progressed to Meeting Scheduled
        assert updated_lead.status in ["Meeting Scheduled", "Qualified"]
        
        # Timestamps should be in UTC and properly ordered
        assert updated_lead.updated_at > updated_lead.created_at
        assert updated_lead.updated_at.tzinfo is not None
    
    @pytest.mark.asyncio
    async def test_franchise_inquiry_with_multiple_questions(self, conversation_harness, new_lead, andy_assertions, frozen_time):
        """
        Test comprehensive franchise inquiry with multiple Q&A rounds
        """
        await conversation_harness.setup_lead({
            "first_name": new_lead.first_name,
            "phone": new_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I'm looking into franchise opportunities",
                expected_response_contains="franchise"
            ),
            
            ConversationTurn(
                user_message="What are the initial costs?",
                expected_response_contains="$50,000"
            ),
            
            ConversationTurn(
                user_message="Do you provide training?",
                expected_response_contains="training"  # Should get RAG answer or escalate
            ),
            
            ConversationTurn(
                user_message="What territories are available?",
                expected_response_contains="territory"  # Should get RAG answer or escalate
            ),
            
            ConversationTurn(
                user_message="This looks good. Can we set up a call?",
                expected_response_contains="meeting"
            ),
            
            ConversationTurn(
                user_message="Monday at 1pm works",
                expected_response_contains="Monday"
            ),
            
            ConversationTurn(
                user_message="Yes, confirm it",
                expected_response_contains="confirmed"
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        assert len(results) == 7
        assert all(result.success for result in results)
        
        # Should handle multiple questions appropriately
        for result in results:
            andy_assertions.assert_no_emojis(result.agent_response)
        
        # Final booking should be confirmed
        assert "confirmed" in results[6].agent_response.lower() or "booked" in results[6].agent_response.lower()
    
    @pytest.mark.asyncio
    async def test_lead_with_budget_qualification(self, conversation_harness, new_lead, frozen_time):
        """
        Test lead qualification based on budget discussion
        """
        await conversation_harness.setup_lead({
            "first_name": new_lead.first_name,
            "phone": new_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I'm interested in your franchise",
                expected_response_contains="franchise"
            ),
            
            ConversationTurn(
                user_message="What's the investment required?",
                expected_response_contains="$50,000"
            ),
            
            ConversationTurn(
                user_message="That's within my budget. I have about $80k available",
                expected_response_contains="great"  # Should respond positively to qualified budget
            ),
            
            ConversationTurn(
                user_message="When can we discuss this in detail?",
                expected_response_contains="meeting"
            ),
            
            ConversationTurn(
                user_message="Tomorrow afternoon works",
                expected_response_contains="afternoon"
            ),
            
            ConversationTurn(
                user_message="2pm is perfect",
                expected_response_contains="booked"
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should recognize qualified lead and facilitate meeting
        budget_response = results[2].agent_response
        assert any(word in budget_response.lower() 
                  for word in ["great", "excellent", "perfect", "good"])
    
    @pytest.mark.asyncio
    async def test_returning_lead_conversation(self, conversation_harness, db_session, frozen_time):
        """
        Test conversation with returning lead who already has context
        """
        # Create contacted lead (returning)
        factory = LeadFactory(db_session)
        returning_lead = await factory.create_contacted_lead(
            first_name="Mike",
            last_name="Wilson",
            phone="+61423456789",
            status="Contacted"
        )
        
        await conversation_harness.setup_lead({
            "first_name": returning_lead.first_name,
            "phone": returning_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Hi, we spoke yesterday about the franchise opportunity",
                expected_response_contains="Mike"  # Should recognize returning lead
            ),
            
            ConversationTurn(
                user_message="I've thought about it and I'm ready to move forward",
                expected_response_contains="great"
            ),
            
            ConversationTurn(
                user_message="Can we schedule that meeting we discussed?",
                expected_response_contains="meeting"
            ),
            
            ConversationTurn(
                user_message="Friday at 11am",
                expected_response_contains="Friday"
            ),
            
            ConversationTurn(
                user_message="Yes, book it",
                expected_response_contains="booked"
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should recognize returning lead
        welcome_response = results[0].agent_response
        assert "Mike" in welcome_response or "spoke" in welcome_response.lower()
    
    @pytest.mark.asyncio
    async def test_multi_day_conversation_flow(self, conversation_harness, new_lead, frozen_time):
        """
        Test conversation that spans multiple interactions over time
        """
        await conversation_harness.setup_lead({
            "first_name": new_lead.first_name,
            "phone": new_lead.phone
        })
        
        # Day 1: Initial inquiry
        day1_turns = [
            ConversationTurn(
                user_message="I'm interested in your franchise",
                expected_response_contains="franchise"
            ),
            ConversationTurn(
                user_message="Let me think about it",
                expected_response_contains="time"
            )
        ]
        
        day1_results = await conversation_harness.run_conversation(day1_turns)
        
        # Fast forward 2 days
        frozen_time.tick(delta=timedelta(days=2))
        
        # Day 3: Follow up conversation
        day3_turns = [
            ConversationTurn(
                user_message="Hi, I've decided I want to proceed with the franchise",
                expected_response_contains="great"
            ),
            ConversationTurn(
                user_message="Can we schedule a meeting?",
                expected_response_contains="meeting"
            ),
            ConversationTurn(
                user_message="Tomorrow at 3pm",
                expected_response_contains="3:00 PM"
            ),
            ConversationTurn(
                user_message="Perfect",
                expected_response_contains="booked"
            )
        ]
        
        day3_results = await conversation_harness.run_conversation(day3_turns)
        
        # Should handle multi-day conversation flow
        assert len(day1_results) == 2
        assert len(day3_results) == 4
        
        # Should remember context and facilitate booking
        assert "booked" in day3_results[3].agent_response.lower()


@pytest.mark.e2e
class TestConversationQuality:
    """Test conversation quality and tone."""
    
    @pytest.fixture
    def conversation_harness(self, db_session, meeting_agent_mocks):
        return ConversationHarness(db_session, meeting_agent_mocks)
    
    @pytest.fixture
    async def test_lead(self, db_session):
        factory = LeadFactory(db_session)
        return await factory.create_lead(phone="+61456789012")
    
    @pytest.mark.asyncio
    async def test_human_like_conversation_tone(self, conversation_harness, test_lead, andy_assertions):
        """Test that conversations maintain human-like tone throughout."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="Hey there, what's this franchise about?",
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="Sounds interesting, tell me more",
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="I'm definitely interested",
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        for result in results:
            response = result.agent_response
            
            # Should be human-like
            andy_assertions.assert_human_like_tone(response)
            
            # Should not contain emojis
            andy_assertions.assert_no_emojis(response)
            
            # Should not be too robotic
            robotic_phrases = ["Please be advised", "Kindly note", "As per your request"]
            assert not any(phrase in response for phrase in robotic_phrases)
    
    @pytest.mark.asyncio
    async def test_conversation_context_retention(self, conversation_harness, test_lead):
        """Test that conversation context is retained throughout."""
        await conversation_harness.setup_lead({
            "first_name": test_lead.first_name,
            "phone": test_lead.phone
        })
        
        turns = [
            ConversationTurn(
                user_message="I'm looking for a franchise in Sydney",
                expected_response_contains="Sydney"
            ),
            ConversationTurn(
                user_message="What are the requirements?",  # Should understand this is about Sydney franchise
                expected_db_writes=["conversation_messages_created"]
            ),
            ConversationTurn(
                user_message="And the costs?",  # Should understand this is about franchise costs
                expected_db_writes=["conversation_messages_created"]
            )
        ]
        
        results = await conversation_harness.run_conversation(turns)
        
        # Should maintain context throughout
        assert "Sydney" in results[0].agent_response
        
        # Later responses should be contextually appropriate
        for result in results[1:]:
            assert len(result.agent_response) > 10  # Should provide substantive responses
