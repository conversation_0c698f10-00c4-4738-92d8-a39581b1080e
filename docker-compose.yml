# Optimized Docker Compose for GrowthHive
#
# Optimizations included:
# - Multi-stage Dockerfile with production target
# - BuildKit enabled for faster builds
# - Layer caching with cache_from
# - Cached volume mounts for development
# - Optimized network settings
# - Health checks with proper timing
# - Build arguments for pip reliability
#
# Usage:
#   Development: docker-compose up --build
#   Production: Remove volume mounts and use production command
#   Tools: docker-compose --profile tools up (for redis-commander)

services:
  backend:
    container_name: growthhive-backend
    build:
      context: .
      dockerfile: Dockerfile
      target: production  # Use production stage for optimized build
      args:
        - BUILDKIT_INLINE_CACHE=1
        - PIP_DEFAULT_TIMEOUT=100
        - PIP_RETRIES=3
      cache_from:
        - growthhive-backend:latest
    ports:
      - "8000:8000"
    # env_file:
    #   - .env  # Temporarily disabled to prioritize Docker environment variables
    environment:
      - TZ=Asia/Kolkata
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
      - PYTHONPATH=/app
      - DOCKER_BUILDKIT=1  # Enable BuildKit for faster builds
      - DATABASE_URL=postgresql+asyncpg://root:root@postgres:5432/growthhive
      - REDIS_URL=redis://redis:6379/1
      - REDIS_SESSION_DB=2
      - REDIS_CACHE_DB=3
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_BROKER_URL=amqp://guest:guest@rabbitmq:5672//
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_started
      postgres:
        condition: service_healthy
    # For development, mount volumes; for production, remove this
    volumes:
      - .:/app:cached  # Use cached volumes for better performance
      - app_logs:/app/logs  # Persistent logs volume
    networks:
      - growthhive_network
    restart: unless-stopped
    # Development command with reload
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --env-file .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  celery:
    container_name: growthhive-celery
    build:
      context: .
      dockerfile: Dockerfile
      target: production  # Use production stage
      args:
        - BUILDKIT_INLINE_CACHE=1
        - PIP_DEFAULT_TIMEOUT=100
        - PIP_RETRIES=3
      cache_from:
        - growthhive-backend:latest
    # env_file:
    #   - .env  # Temporarily disabled to prioritize Docker environment variables
    depends_on:
      - backend
      - rabbitmq
      - redis
      - postgres
    working_dir: /app
    environment:
      - PYTHONPATH=/app
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=amqp://guest:guest@rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DOCKER_BUILDKIT=1
      - DATABASE_URL=postgresql+asyncpg://root:root@postgres:5432/growthhive
      - REDIS_URL=redis://redis:6379/1
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    command: celery -A app.core.celery_app:celery_app worker -Q celery,document_processing,sms_queue,sms_onboarding_queue -l info
    volumes:
      - .:/app:cached
      - app_logs:/app/logs  # Shared logs volume
    networks:
      - growthhive_network
    restart: always

  rabbitmq:
    container_name: rabbitmq
    image: rabbitmq:3.12-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    networks:
      - growthhive_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    container_name: redis
    image: redis:6.2
    ports:
      - "6379:6379"
    networks:
      - growthhive_network
    restart: always

  redisinsight:
    container_name: redisinsight
    image: redis/redisinsight:latest
    ports:
      - "5540:5540"
    networks:
      - growthhive_network
    restart: unless-stopped
    depends_on:
      - redis

  postgres:
    container_name: growthhive-postgres
    image: pgvector/pgvector:pg15
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=growthhive
      - POSTGRES_USER=root
      - POSTGRES_PASSWORD=root
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - growthhive_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U root -d growthhive"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  rabbitmq_data:
  redis_data:
  postgres_data:
  app_logs:

networks:
  growthhive_network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450  # Optimize for better network performance