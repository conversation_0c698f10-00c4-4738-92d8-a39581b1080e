# Setup.cfg - Configuration for multiple tools (extracted from pyproject.toml)

[metadata]
name = growthhive
version = 1.0.0
description = AI-enabled prospect outreach platform for franchise management
long_description = file: README.md
long_description_content_type = text/markdown
author = GrowthHive Team
author_email = <EMAIL>
license = MIT
license_file = LICENSE
url = https://github.com/growthhive/growthhive
project_urls =
    Homepage = https://github.com/growthhive/growthhive
    Documentation = https://docs.growthhive.com
    Repository = https://github.com/growthhive/growthhive.git
    Issues = https://github.com/growthhive/growthhive/issues
classifiers =
    Development Status :: 4 - Beta
    Intended Audience :: Developers
    License :: OSI Approved :: MIT License
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12

[options]
packages = find:
python_requires = >=3.11
include_package_data = True
zip_safe = False

# Black Configuration
[tool:black]
target-version = py311
line-length = 88
include = \.pyi?$
extend-exclude = \.eggs|\.git|\.hg|\.mypy_cache|\.tox|\.venv|build|dist

# isort Configuration
[tool:isort]
profile = black
multi_line_output = 3
line_length = 88
known_first_party = app,tests
skip = alembic/versions

# Bandit Configuration
[bandit]
exclude_dirs = tests,alembic
skips = B101,B601
