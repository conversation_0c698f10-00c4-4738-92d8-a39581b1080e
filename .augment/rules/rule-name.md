---
type: "agent_requested"
---

GrowthHive Project Analysis - Complete Understanding
Based on my comprehensive analysis of the entire project, here's my understanding of the GrowthHive system's conventions and requirements:

🏗️ Project Architecture
Core System
Framework: FastAPI 0.104+ with async/await patterns
Database: PostgreSQL with SQLAlchemy 2.0+ (async)
Authentication: JWT with refresh tokens + remember-me functionality
AI Integration: OpenAI for embeddings, completions, and document processing
Storage: AWS S3 integration for file management
Background Tasks: Celery with Redis backend
Vector Search: pgvector extension for AI/ML capabilities
Project Structure
app/
├── main.py                 # Application entry point
├── api/                    # API routes and endpoints
│   ├── v1/                # Versioned API routes
│   └── middleware/        # Custom middleware
├── core/                   # Core functionality

🎯 Key Conventions
Database Patterns
Primary Keys: UUID with uuid4() default
Standard Fields: is_active, is_deleted, created_at, updated_at, deleted_at
Naming: snake_case for tables and columns
Relationships: Proper foreign key constraints with cascade options
Migrations: Alembic for all schema changes
API Response Structure
class ApiResponse(BaseModel):
    success: bool
    message: Message  # {title, description}
    data: Union[Dict, List, str, int, float, bool]
    error_code: Optional[int]
Authentication Requirements
All endpoints require authentication except public ones (login, register, webhooks)
JWT access tokens (15 minutes) + refresh tokens (7 days)
Remember-me tokens (30 days) for persistent sessions
Custom exception hierarchy for auth errors
Service Layer Patterns
RORO Pattern: Receive Object, Return Object
Guard Clauses: Early returns instead of nested if-else
Async Operations: All database and external API calls
Error Handling: Custom exceptions with proper HTTP status codes
🔧 Technical Requirements
Code Quality Standards
Type Hints: All functions must have proper type annotations
Async/Await: All database operations must be async
No Unused Imports: Clean, optimized imports
Descriptive Names: is_active, has_permission, etc.
Pydantic v2: For all data validation and serialization
Security Patterns
Input Validation: All user inputs validated with Pydantic
Authentication Dependencies: Depends(get_current_user)
Password Hashing: bcrypt with proper salt rounds
CORS Configuration: Proper origin restrictions
Security Headers: X-Frame-Options, X-Content-Type-Options, etc.
Performance Optimizations
Connection Pooling: Database connection management
Caching: Memory cache with TTL for frequent operations
Pagination: Skip/limit patterns for large datasets
Lazy Loading: Relationships loaded only when needed
📊 DocQA System Specifics
Advanced Features
Multi-format Support: PDF, DOCX, DOC, JPG, PNG with OCR
Vector Search: pgvector with cosine similarity
Priority System: Franchisors results prioritized over documents
Parallel Processing: Multi-threaded file processing
Intelligent Caching: LRU eviction with persistent storage
Chart Analysis: GPT-4 Vision for visual content understanding
Processing Pipeline
File upload → Format detection → Handler selection
Content extraction → Language detection → Translation (if needed)
Text chunking → Embedding generation → Vector storage
Query processing → Priority search → Context preparation → AI response
🚨 Critical Rules
Development Guidelines
Never remove existing functionality without explicit permission
Maintain project structure exactly as defined
Use package managers for dependency management (never edit package files manually)
Follow async patterns for all I/O operations
Implement comprehensive error handling with custom exceptions
Write tests for all new functionality
Use proper authentication on all protected endpoints
Follow naming conventions consistently
User Preferences (from memories)
APIs with authentication required
Standard audit fields (is_active, is_deleted, created_at, deleted_at)
Factory design pattern implementation
Comprehensive testing in master test suite
Webhook event handling patterns for messaging services
QnA/RAG functionality integration into webhook endpoints
🔄 Development Workflow
Before Making Changes
Use codebase-retrieval tool for detailed context
Understand existing patterns and conventions
Follow the established architecture
Implement proper error handling
Add comprehensive tests
Update documentation as needed
Quality Checks
Type checking with MyPy
Linting with Ruff
Test coverage with pytest
Security validation
Performance considerations
📈 Business Context
The system serves as an AI-enabled prospect outreach platform for franchise management, with specific focus on:

Document processing and Q&A capabilities
Lead management and qualification
User authentication and authorization
File storage and processing
Background task management


- It should follow existing applications conventions for request and response Also all the endpoints should be authenticated for accessing.
- Make sure there are no bugs.There is no test DB use existing db.
- First check if there are any errors with pydentic and datatypes and resolve if any.
- It should not alter existing functionality.
- Include is_active and is_deleted fields, created_at and deleted_at fields.
- It should follow factory design pattern.
- Make sure you include all the test in master test suite as well.
- Do not cause naming conflicts between internal variable names and user variable names.
- The `get_current_user` function returns a dictionary.
- In listing APIs always show active/inactive records.
- In listing API always show created_at and updated_at fields.
- Make sure you specify all the routes perfectly.
- Apply these rules wherever applicable.
Comprehensive API documentation
This analysis provides a complete understanding of the project's architecture, conventions, and requirements for any future development work.