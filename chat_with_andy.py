#!/usr/bin/env python3
"""
Interactive Chat with <PERSON> SMS Assistant
Manual testing interface for <PERSON>'s SMS qualification workflow
"""

import asyncio
import sys
import os
from datetime import datetime
import json

# Set environment to reduce logging noise
os.environ['LOG_LEVEL'] = 'WARNING'

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

# Color support for better UX
class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'
    BG_BLUE = '\033[44m'
    BG_GREEN = '\033[42m'


class AndyChatInterface:
    """Interactive chat interface for Andy SMS Assistant"""
    
    def __init__(self, phone_number: str = "+61412345678", name: str = "Test User"):
        self.phone_number = phone_number
        self.name = name
        self.andy = None
        self.conversation_count = 0
        
    async def initialize_andy(self):
        """Initialize Andy SMS Assistant"""
        try:
            from app.agents.sms_assistant import get_andy_sms_assistant
            self.andy = get_andy_sms_assistant()
            print(f"{Colors.GREEN}✅ Andy SMS Assistant initialized successfully{Colors.END}")
            return True
        except Exception as e:
            print(f"{Colors.RED}❌ Failed to initialize Andy: {e}{Colors.END}")
            print(f"{Colors.YELLOW}💡 This might be due to missing dependencies or database connections{Colors.END}")
            return False
    
    def show_welcome(self):
        """Show welcome message and instructions"""
        print(f"\n{Colors.CYAN}{'='*80}{Colors.END}")
        print(f"{Colors.BOLD}🤖 Chat with Andy - Interactive SMS Assistant Test{Colors.END}")
        print(f"{Colors.CYAN}{'='*80}{Colors.END}")
        print(f"📱 Phone: {self.phone_number}")
        print(f"👤 Name: {self.name}")
        print(f"🎯 Franchise: Coochie Hydrogreen")
        print(f"🇦🇺 Specialist: Andy (Lead Qualification)")
        print(f"{Colors.CYAN}{'='*80}{Colors.END}")
        print(f"\n{Colors.YELLOW}💬 Instructions:{Colors.END}")
        print(f"• Type your messages as if you're texting Andy")
        print(f"• Andy will respond using his qualification workflow")
        print(f"• Try asking about the franchise, raising objections, etc.")
        print(f"• Type 'quit', 'exit', or 'bye' to end the chat")
        print(f"• Type 'help' for more commands")
        print(f"• Type 'status' to see your qualification progress")
        print(f"\n{Colors.GREEN}🚀 Andy is ready to chat! Start the conversation...{Colors.END}\n")
    
    def show_help(self):
        """Show help commands"""
        print(f"\n{Colors.CYAN}📋 Available Commands:{Colors.END}")
        print(f"• {Colors.BOLD}help{Colors.END} - Show this help message")
        print(f"• {Colors.BOLD}status{Colors.END} - Show qualification progress")
        print(f"• {Colors.BOLD}context{Colors.END} - Show current lead context")
        print(f"• {Colors.BOLD}reset{Colors.END} - Reset conversation (new lead)")
        print(f"• {Colors.BOLD}quit/exit/bye{Colors.END} - End the chat")
        print(f"\n{Colors.YELLOW}💡 Sample questions to try:{Colors.END}")
        print(f"• 'Hi, I'm interested in the franchise'")
        print(f"• 'What services does Coochie Hydrogreen provide?'")
        print(f"• 'How much does it cost?'")
        print(f"• 'I don't have any experience in lawn care'")
        print(f"• 'This seems too expensive'")
        print(f"• 'I've never heard of Coochie Hydrogreen'")
        print()
    
    async def show_status(self):
        """Show current qualification status"""
        if not self.andy:
            print(f"{Colors.RED}❌ Andy not initialized{Colors.END}")
            return
        
        try:
            # Get current context from Andy's memory
            context = self.andy.memory_manager.get_lead_context(self.phone_number)
            
            print(f"\n{Colors.CYAN}📊 Qualification Status:{Colors.END}")
            print(f"{Colors.CYAN}{'─'*40}{Colors.END}")
            
            if context:
                print(f"👤 Name: {context.get('name', 'Not provided')}")
                print(f"📞 Phone: {context.get('phone', self.phone_number)}")
                print(f"🏢 Work Background: {context.get('work_background', 'Not determined')}")
                print(f"💭 Motivation: {context.get('motivation', 'Not identified')}")
                print(f"💰 Budget: {context.get('budget', 'Not confirmed')}")
                print(f"📈 Qualification Score: {context.get('qualification_score', 0):.1%}")
                print(f"🎯 Current Stage: {context.get('current_stage', 'Unknown')}")
                print(f"💬 Messages Exchanged: {self.conversation_count}")
                
                if context.get('objections_raised'):
                    print(f"🛡️  Objections Raised: {', '.join(context['objections_raised'])}")
                
                if context.get('meeting_interest'):
                    print(f"📅 Meeting Interest: {'Yes' if context['meeting_interest'] else 'No'}")
            else:
                print(f"📝 No context available yet - start chatting with Andy!")
            
            print(f"{Colors.CYAN}{'─'*40}{Colors.END}\n")
            
        except Exception as e:
            print(f"{Colors.RED}❌ Error getting status: {e}{Colors.END}")
    
    async def show_context(self):
        """Show detailed lead context"""
        if not self.andy:
            print(f"{Colors.RED}❌ Andy not initialized{Colors.END}")
            return
        
        try:
            context = self.andy.memory_manager.get_lead_context(self.phone_number)
            
            print(f"\n{Colors.CYAN}🧠 Lead Context (Raw Data):{Colors.END}")
            print(f"{Colors.CYAN}{'─'*50}{Colors.END}")
            
            if context:
                print(json.dumps(context, indent=2, default=str))
            else:
                print("No context data available")
            
            print(f"{Colors.CYAN}{'─'*50}{Colors.END}\n")
            
        except Exception as e:
            print(f"{Colors.RED}❌ Error getting context: {e}{Colors.END}")
    
    async def reset_conversation(self):
        """Reset the conversation"""
        if not self.andy:
            print(f"{Colors.RED}❌ Andy not initialized{Colors.END}")
            return
        
        try:
            # Clear Redis context
            self.andy.memory_manager.redis_client.delete(f"andy_lead_context:{self.phone_number}")
            self.andy.memory_manager.redis_client.delete(f"andy_conversation:{self.phone_number}")
            self.conversation_count = 0
            
            print(f"{Colors.GREEN}✅ Conversation reset - you're now a new lead!{Colors.END}")
            print(f"{Colors.YELLOW}💡 Andy will treat you as a first-time contact{Colors.END}\n")
            
        except Exception as e:
            print(f"{Colors.RED}❌ Error resetting conversation: {e}{Colors.END}")
    
    async def send_message_to_andy(self, message: str):
        """Send message to Andy and get response"""
        if not self.andy:
            print(f"{Colors.RED}❌ Andy not initialized{Colors.END}")
            return
        
        try:
            print(f"{Colors.BLUE}📤 Sending to Andy...{Colors.END}")
            
            # Process message through Andy
            result = await self.andy.process_sms(self.phone_number, message)
            
            self.conversation_count += 1
            
            if result.get('success'):
                response = result.get('response', 'No response')
                
                # Show Andy's response
                print(f"\n{Colors.GREEN}🤖 Andy:{Colors.END}")
                print(f"{Colors.BG_GREEN}{Colors.WHITE} {response} {Colors.END}")
                
                # Show processing info
                processing_time = result.get('processing_time', 0)
                current_stage = result.get('current_stage', 'Unknown')
                qualification_score = result.get('qualification_score', 0)
                
                print(f"\n{Colors.CYAN}📊 Processing Info:{Colors.END}")
                print(f"⏱️  Time: {processing_time:.2f}s | 🎯 Stage: {current_stage} | 📈 Score: {qualification_score:.1%}")
                
                if result.get('error'):
                    print(f"⚠️  Warning: {result['error']}")
                
            else:
                print(f"{Colors.RED}❌ Andy failed to respond: {result.get('error', 'Unknown error')}{Colors.END}")
            
        except Exception as e:
            print(f"{Colors.RED}❌ Error communicating with Andy: {e}{Colors.END}")
            import traceback
            traceback.print_exc()
    
    async def start_chat(self):
        """Start the interactive chat session"""
        self.show_welcome()
        
        # Try to initialize Andy
        andy_initialized = await self.initialize_andy()
        
        if not andy_initialized:
            print(f"{Colors.YELLOW}⚠️  Continuing with limited functionality (Andy not available){Colors.END}")
        
        print(f"{Colors.BOLD}💬 Start chatting with Andy:{Colors.END}")
        
        while True:
            try:
                # Get user input
                user_input = input(f"\n{Colors.BLUE}You: {Colors.END}").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print(f"\n{Colors.YELLOW}👋 Thanks for testing Andy! Goodbye!{Colors.END}\n")
                    break
                
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                
                elif user_input.lower() == 'status':
                    await self.show_status()
                    continue
                
                elif user_input.lower() == 'context':
                    await self.show_context()
                    continue
                
                elif user_input.lower() == 'reset':
                    await self.reset_conversation()
                    continue
                
                # Send message to Andy
                if andy_initialized:
                    await self.send_message_to_andy(user_input)
                else:
                    print(f"{Colors.RED}❌ Andy not available - cannot process message{Colors.END}")
                    print(f"{Colors.YELLOW}💡 Try running: pip install -r requirements.txt{Colors.END}")
            
            except KeyboardInterrupt:
                print(f"\n\n{Colors.YELLOW}👋 Chat interrupted. Goodbye!{Colors.END}\n")
                break
            
            except Exception as e:
                print(f"{Colors.RED}❌ Unexpected error: {e}{Colors.END}")


async def main():
    """Main function"""
    # Get user details
    print(f"{Colors.CYAN}🚀 Setting up chat with Andy...{Colors.END}")
    
    try:
        name = input(f"Enter your name (or press Enter for 'Test User'): ").strip()
        if not name:
            name = "Test User"
        
        phone = input(f"Enter phone number (or press Enter for '+61412345678'): ").strip()
        if not phone:
            phone = "+61412345678"
        
        # Create and start chat interface
        chat = AndyChatInterface(phone_number=phone, name=name)
        await chat.start_chat()
        
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 Setup cancelled. Goodbye!{Colors.END}\n")
    except Exception as e:
        print(f"{Colors.RED}❌ Setup error: {e}{Colors.END}")


if __name__ == "__main__":
    asyncio.run(main())
