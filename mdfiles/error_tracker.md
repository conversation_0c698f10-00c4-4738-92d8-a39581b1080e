# FastAPI Error Tracker

## Critical Runtime Errors (Blocking App Start)
- [x] **CRITICAL**: app/agents/orchestrator.py:72 - AttributeError: 'str' object has no attribute 'value'
- [x] app/agents/document_ingestion.py:199 - F821 Undefined name `uuid`
- [x] app/agents/tools/database_tools.py:42,93,132,181,231 - Missing return statements
- [x] app/agents/tools/database_tools.py:235 - AsyncSession has no attribute 'query'

## High Priority Fixes
- [ ] app/agents/lead_qualification.py:111 - E722 Do not use bare `except`
- [ ] app/agents/meeting_booking.py:146 - E722 Do not use bare `except`
- [ ] app/api/v1/endpoints/agents.py:158 - E722 Do not use bare `except`
- [ ] app/agents/tools/database_tools.py:235 - E712 Use `not Lead.is_deleted` instead of `== False`

## Medium Priority Fixes
- [ ] app/agents/conversation.py:79 - F841 Unused variable `out_of_scope_responses`
- [ ] app/agents/conversation.py:133 - F841 Unused variable `memory_data`
- [ ] app/agents/document_ingestion.py:30 - F841 Unused variable `user_input`
- [ ] app/agents/lead_qualification.py:254 - F841 Unused variable `communication_data`
- [ ] app/agents/meeting_booking.py:196 - F841 Unused variable `meeting_data`
- [ ] app/agents/question_answering.py:74 - F841 Unused variable `enhanced_query`
- [ ] app/agents/question_answering.py:207 - F841 Unused variable `communication_data`
- [ ] app/api/v1/endpoints/agents.py:243 - F841 Unused variable `result`
- [ ] app/api/v1/endpoints/question_bank.py:420 - F841 Unused variable `status`
- [ ] app/cli/services/qa_service.py:222 - F841 Unused variable `response`

## Import and Module Issues
- [ ] app/cli/services/text_extractor.py:27 - F401 Unused import `partition_text`
- [ ] app/cli/services/vision_extractor.py:26 - F401 Unused import `Image`
- [ ] app/main.py:10-35 - E402 Module level imports not at top of file
- [ ] app/cli/main.py:27-28 - E402 Module level imports not at top of file

## Type Annotation Issues (MyPy)
- [ ] app/agents/tools/document_tools.py:47,53 - DocQAIntegrationService has no attribute 'process_document_async'
- [ ] app/agents/tools/document_tools.py:100 - Unexpected keyword arguments for 'ask_question'
- [ ] app/agents/tools/document_tools.py:154 - Cannot find PyPDF2 module
- [ ] app/agents/tools/registry.py:110,114 - List comprehension type issues
- [ ] app/agents/base.py:128 - Unexpected keyword argument 'max_tokens' for ChatOpenAI

## Response Model Issues
- [ ] app/api/v1/endpoints/leads.py:213,437,536 - Missing 'is_active' argument for LeadResponse
- [ ] app/api/v1/endpoints/leads.py:215+ - Column type incompatibility with response models
- [ ] app/api/v1/endpoints/messaging_rules.py:44+ - Incompatible return types
- [ ] app/api/v1/endpoints/holidays.py:154+ - Missing arguments and type issues

## Settings and Configuration Issues
- [ ] app/services/franchisor_service.py:352 - Settings missing S3_BASE_URL, S3_BUCKET_NAME, AWS_REGION
- [ ] app/api/v1/endpoints/background_tasks.py:46+ - ErrorCodes missing INTERNAL_SERVER_ERROR
- [ ] app/api/v1/endpoints/documents.py:714 - ErrorCodes missing PROCESSING_ERROR

## Webhook and Logging Issues
- [ ] app/api/v1/endpoints/webhooks.py:74+ - Multiple logging parameter issues
- [ ] app/api/v1/endpoints/webhooks.py:220 - Incompatible default for answer_result

## Test Issues
- [ ] tests/test_comprehensive_endpoints.py:11 - Cannot find tests.utils module
- [ ] tests/conftest.py:40 - sessionmaker overload issues

## Fixed Issues ✅
- [x] **CRITICAL**: app/agents/orchestrator.py:72 - AttributeError: 'str' object has no attribute 'value'
- [x] app/agents/document_ingestion.py:199 - F821 Undefined name `uuid`
- [x] app/agents/tools/database_tools.py:42,93,132,181,231 - Missing return statements
- [x] app/agents/tools/database_tools.py:235 - AsyncSession has no attribute 'query'

## Status Summary
🎉 **SUCCESS**: FastAPI application is now running successfully!
- App starts without critical errors
- All agents initialize properly
- Tool registry loads 16 tools
- Workflow graph compiles successfully
- Server accessible at http://127.0.0.1:8000

## Remaining Issues (Non-blocking)
The app is functional but has remaining style, type, and test issues that can be addressed incrementally.
