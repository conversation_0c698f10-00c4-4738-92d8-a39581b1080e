# 🔧 Webhook Conversation Message Storage Fix

## 🚨 **Issue Identified**

The webhook conversation message storage is failing because the `conversation_message` table is missing the `updated_at` column. This causes the following error:

```
column conversation_message.updated_at does not exist
HINT: Perhaps you meant to reference the column "conversation_message.created_at".
```

## 📊 **Root Cause Analysis**

### **Primary Issue: Missing Database Column**
- The `conversation_message` table was created without the `updated_at` column
- The SQLAlchemy model expects this column to exist
- The webhook code tries to insert records but fails due to the missing column

### **Secondary Issues: Database Connection**
- Database connection errors: "401: Authentication required"
- Connection refused errors: "could not translate host name"
- These prevent the webhook from even reaching the conversation storage code

## 🛠️ **Solution**

### **1. Database Fix (Primary Solution)**

**File:** `sql/29-07-2025_fix_conversation_message_table.sql`

This SQL file will:
- ✅ Check if the table exists
- ✅ Add the missing `updated_at` column
- ✅ Create necessary indexes
- ✅ Update existing records
- ✅ Verify the fix with a test insert

### **2. How to Apply the Fix**

```bash
# Connect to your PostgreSQL database
psql -h your_host -U your_user -d your_database

# Run the fix SQL file
\i sql/29-07-2025_fix_conversation_message_table.sql
```

### **3. Test the Fix**

```bash
# Run the test script
python test_webhook_conversation_fix.py
```

## 🔍 **What the Webhook Does**

When a Kudosity SMS webhook is received:

1. **Webhook Processing** ✅
   - Receives SMS_INBOUND event
   - Validates payload
   - Processes with AI agent

2. **Lead Lookup** ✅
   - Finds lead by phone number
   - Uses phone number variations for matching

3. **AI Agent Processing** ✅
   - Generates response to lead's message
   - Identifies speakers (lead vs system)
   - Prepares conversation data

4. **Database Storage** ❌ **FIXED**
   - Stores lead message in `conversation_message` table
   - Stores system response in `conversation_message` table
   - Links to lead_id and franchisor_id

## 📋 **Database Schema (After Fix)**

```sql
CREATE TABLE conversation_message (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES leads(id),
  franchisor_id UUID REFERENCES franchisors(id),
  sender TEXT CHECK (sender IN ('lead', 'system')) NOT NULL,
  message TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  is_deleted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),  -- ✅ FIXED
  deleted_at TIMESTAMPTZ
);
```

## 🧪 **Testing the Fix**

### **Test Webhook Payload:**
```json
{
  "event_type": "SMS_INBOUND",
  "timestamp": "2025-07-29T12:00:00Z",
  "mo": {
    "sender": "61426810472",
    "message": "I am interested in your franchise opportunity"
  }
}
```

### **Expected Database Records:**
```sql
-- Lead message
INSERT INTO conversation_message (lead_id, franchisor_id, sender, message)
VALUES ('lead_uuid', 'franchisor_uuid', 'lead', 'I am interested in your franchise opportunity');

-- System response
INSERT INTO conversation_message (lead_id, franchisor_id, sender, message)
VALUES ('lead_uuid', 'franchisor_uuid', 'system', 'Thank you for your interest! I can help you...');
```

## 🔧 **Additional Database Connection Issues**

If you're still having connection issues:

1. **Check DATABASE_URL in .env file**
2. **Ensure PostgreSQL is running**
3. **Verify database credentials**
4. **Check network connectivity**

## 📈 **Expected Results After Fix**

✅ **Webhook Response:** 200 OK
✅ **Database Records:** 2 new conversation messages
✅ **Lead Lookup:** Successful phone number matching
✅ **AI Processing:** Speaker identification working
✅ **No Errors:** Clean logs without database errors

## 🚀 **Next Steps**

1. **Apply the database fix** using the SQL file
2. **Test the webhook** using the test script
3. **Monitor logs** to ensure no more database errors
4. **Verify conversation storage** is working in production

## 📞 **Support**

If you encounter any issues:
1. Check the error logs in `logs/error.log`
2. Run the test script to verify the fix
3. Ensure database connection is working
4. Verify all SQL commands executed successfully

---

**Status:** ✅ **Issue Identified and Solution Provided**
**Priority:** 🔴 **High - Blocking webhook functionality**
**Impact:** 🚫 **No conversation messages being stored** 