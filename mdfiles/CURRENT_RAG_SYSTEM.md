# Current RAG System - Production Ready

## Overview

The current RAG (Retrieval-Augmented Generation) system is a **production-ready, document-agnostic** implementation located in the `docqa/` folder. This is the **latest and best** RAG strategy that handles any type of document dynamically and should be used throughout the application.

## Architecture

### Core Components

1. **Production Integration** (`docqa/production_integration.py`)
   - Main entry point for RAG functionality
   - Provides unified RAG system interface
   - Singleton instance: `production_rag`

2. **Dynamic Document Processing** (`docqa/brochure_rag_system.py`)
   - Handles any document type (PDF, DOCX, TXT, etc.)
   - Advanced text normalization for all content types
   - Dynamic metadata extraction
   - Adaptive section-aware chunking

3. **Universal QA System** (`docqa/brochure_qa_system.py`)
   - Dynamic prompts that adapt to document content
   - Configurable similarity thresholds
   - Context-aware answer generation

4. **Production Vector Store** (`docqa/vector_store/production_vector_store.py`)
   - pgvector-based storage
   - 1536-dimension embeddings
   - Proper similarity calculation
   - Metadata support

5. **Production Embeddings** (`docqa/vector_store/production_embeddings.py`)
   - OpenAI text-embedding-3-small
   - Retry logic and error handling
   - Consistent 1536-dimension vectors

6. **Production Text Processing** (`docqa/text_processing/production_text_processor.py`)
   - Advanced text normalization
   - Recursive character splitting
   - Meaningful text validation

## Key Features

### Dynamic Document Processing
- **Universal Section Detection**: Automatically identifies document structure (headers, content, tables, etc.)
- **Adaptive Metadata Extraction**: Extracts relevant metadata based on document type and content
- **Content-Aware Processing**: Preserves important formatting and context for any document type
- **Configurable Thresholds**: Adjustable similarity thresholds based on document complexity and user needs

### Production Quality
- **Error Handling**: Comprehensive try/catch with proper logging
- **Retry Logic**: Automatic retries for API calls
- **Validation**: Input validation and dimension checking
- **Performance**: Optimized chunking and retrieval

### Integration Points
- **FastAPI Endpoints**: `/api/v1/docqa/ask` for general document questions
- **Webhook Integration**: SMS and messaging webhook support for any document type
- **Agent System**: Compatible with the agents framework for multi-document workflows
- **CLI Support**: Command-line interface for batch processing and testing

## Usage

### Python API
```python
from docqa import production_rag, ask_question, ask_brochure_question

# Direct RAG system usage (works with any document type)
rag_system = production_rag
answer = await rag_system.answer_question(
    question="What is this document about?",
    document_id="uuid-here",
    similarity_threshold=0.5,
    top_k=5
)

# Simplified function usage for any document
answer = await ask_question(
    question="What are the main topics covered?",
    franchisor_id="uuid-here"  # Can be any document ID
)

# Optimized for marketing/business content (but works with any document)
answer = await ask_brochure_question(
    question="What services are offered?",
    franchisor_id="uuid-here"
)
```

### FastAPI Endpoints
- `POST /api/v1/docqa/ask` - General questions about any document type
- `POST /api/v1/docqa/franchisor/{franchisor_id}/ask` - Entity-specific questions

### Webhook Integration
- Automatically integrated with Kudosity SMS webhooks
- Provides AI-powered answers to incoming questions about any document
- Dynamically adapts to different content types and question patterns

## Database Schema

### Franchisors Table
- `embedding` column stores 1536-dimension vectors
- Used for franchisor brochure content
- Supports similarity search and retrieval

### Vector Storage
- pgvector extension for PostgreSQL
- Cosine similarity calculations
- Metadata storage for source tracking

## Configuration

### Environment Variables
- `OPENAI_API_KEY` - OpenAI API access
- `DATABASE_URL` - PostgreSQL connection with pgvector
- `EMBEDDING_MODEL` - text-embedding-3-small (default)

### Parameters
- **Chunk Size**: 350 tokens (configurable based on content type)
- **Chunk Overlap**: 50 tokens
- **Similarity Threshold**: 0.5 (configurable - lower for marketing content, higher for technical docs)
- **Top-K**: 5 chunks per query (adjustable)
- **Temperature**: 0.3 (balanced for general content, 0.2 for marketing content)

## Removed Components

The following outdated RAG implementations have been **removed** to maintain a clean codebase:

- `qna_rag.py` - Older universal QnA system
- `brochure_rag_system.py` (root level) - Duplicate implementation
- `production_rag_system.py` - Older production system
- `integrate_production_rag.py` - Integration scripts
- `integrate_brochure_rag.py` - Integration scripts
- Various demo and test files for old systems

## Best Practices

1. **Use the docqa module**: Always import from `docqa` package
2. **Choose appropriate function**: Use `ask_question` for general content, `ask_brochure_question` for marketing content
3. **Configure parameters**: Adjust similarity thresholds and temperature based on document type
4. **Error handling**: Wrap RAG calls in try/catch blocks
5. **Logging**: Use structured logging for debugging
6. **Testing**: Test with various document types to ensure proper handling

## Maintenance

- **Single Source of Truth**: All RAG functionality is in `docqa/`
- **Production Ready**: No experimental or demo code
- **Well Tested**: Comprehensive test coverage
- **Documented**: Clear API documentation and examples

This is the **only RAG system** that should be used going forward. It dynamically handles any document type without hardcoded assumptions. All other implementations have been removed to prevent confusion and maintain code quality.
