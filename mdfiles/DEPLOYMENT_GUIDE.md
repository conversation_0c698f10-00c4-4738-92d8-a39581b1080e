# GrowthHive Enhanced DocQA Deployment Guide

## 🚀 High-Performance Document Processing System

This guide covers deploying the enhanced DocQA system with RabbitMQ + Redis for high-performance parallel document processing.

## 📋 Prerequisites

### System Requirements
- **CPU**: 4+ cores recommended (8+ for production)
- **Memory**: 8GB+ RAM (16GB+ for production)
- **Storage**: SSD with 50GB+ free space
- **Network**: Stable internet connection for OpenAI API

### Software Requirements
- Docker & Docker Compose
- Python 3.11+
- PostgreSQL 15+ with pgvector extension
- Git

### API Keys & Credentials
- OpenAI API Key (for embeddings and completions)
- AWS S3 credentials (for document storage)
- Database connection details

## 🛠️ Quick Start Deployment

### 1. Clone and Setup
```bash
# Clone repository
git clone <repository-url>
cd growthhive-cursor

# Create environment file
cp .env.example .env

# Configure environment variables
nano .env
```

### 2. Environment Configuration
```bash
# Database Configuration
DATABASE_URL=postgresql+asyncpg://root:root@localhost:5432/growthhive

# RabbitMQ Configuration
CELERY_BROKER_URL=amqp://growthhive:growthhive123@localhost:5672//
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=your_bucket_name

# Processing Configuration
DOCUMENT_PROCESSING_QUEUE=document_processing
DOCUMENT_PROCESSING_MAX_RETRIES=3
DOCUMENT_PROCESSING_RETRY_DELAY=60
```

### 3. Start Background Services
```bash
# Start RabbitMQ, Redis, and Celery workers
./scripts/start_background_services.sh

# Verify services are running
docker-compose -f docker-compose.rabbitmq.yml ps
```

### 4. Start Main Application
```bash
# Install dependencies
pip install -r requirements.txt

# Run database migrations
alembic upgrade head

# Start FastAPI application
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 🔧 Production Deployment

### 1. Infrastructure Setup

#### Database Setup
```sql
-- Create database with pgvector extension
CREATE DATABASE growthhive;
\c growthhive;
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Run setup script
\i docqa/setup_pgvector.sql
```

#### Docker Compose Production
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENV_FILE=.env.production
    depends_on:
      - postgres
      - rabbitmq
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: growthhive
      POSTGRES_USER: growthhive
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      RABBITMQ_DEFAULT_USER: growthhive
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  redis:
    image: redis:7-alpine
    command: redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Multiple worker types for different priorities
  celery-worker-high:
    build: .
    command: celery -A app.core.celery_app worker --queues=high_priority --concurrency=2
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G

  celery-worker-medium:
    build: .
    command: celery -A app.core.celery_app worker --queues=medium_priority --concurrency=4
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1.5G

  celery-worker-batch:
    build: .
    command: celery -A app.core.celery_app worker --queues=batch_processing --concurrency=6
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 3G

volumes:
  postgres_data:
  rabbitmq_data:
  redis_data:
```

### 2. Load Balancer Configuration

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/growthhive
upstream growthhive_app {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # API endpoints
    location /api/ {
        proxy_pass http://growthhive_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Increase timeouts for long-running document processing
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # WebSocket support for real-time updates
    location /ws/ {
        proxy_pass http://growthhive_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    # Static files
    location /static/ {
        alias /var/www/growthhive/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 3. Monitoring Setup

#### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'growthhive-app'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['localhost:15692']

  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']
```

#### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "GrowthHive DocQA Performance",
    "panels": [
      {
        "title": "Document Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(docqa_documents_processed_total[5m])",
            "legendFormat": "Documents/sec"
          }
        ]
      },
      {
        "title": "Cache Hit Rate",
        "type": "singlestat",
        "targets": [
          {
            "expr": "docqa_cache_hit_rate",
            "legendFormat": "Hit Rate %"
          }
        ]
      },
      {
        "title": "Active Jobs",
        "type": "graph",
        "targets": [
          {
            "expr": "docqa_active_jobs",
            "legendFormat": "Active Jobs"
          }
        ]
      }
    ]
  }
}
```

## 🔍 Health Checks & Monitoring

### Application Health Endpoints
```bash
# System health
curl http://localhost:8000/api/docqa/status

# Cache statistics
curl http://localhost:8000/api/docqa/cache/stats

# Performance metrics
curl http://localhost:8000/api/docqa/metrics

# Job status
curl http://localhost:8000/api/docqa/jobs/{job_id}/status
```

### Service Health Checks
```bash
# RabbitMQ
curl http://localhost:15672/api/overview

# Redis
redis-cli ping

# PostgreSQL
pg_isready -h localhost -p 5432

# Celery workers
celery -A app.core.celery_app inspect active
```

## 🚨 Troubleshooting

### Common Issues

#### High Memory Usage
```bash
# Check memory usage
docker stats

# Reduce batch sizes
export BATCH_SIZE=50
export MAX_CONCURRENT_WORKERS=2

# Restart workers
docker-compose -f docker-compose.rabbitmq.yml restart celery-worker-*
```

#### Slow Processing
```bash
# Check OpenAI API limits
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/usage

# Monitor database connections
SELECT count(*) FROM pg_stat_activity;

# Check queue lengths
rabbitmqctl list_queues
```

#### Cache Issues
```bash
# Check Redis memory
redis-cli info memory

# Clear cache if needed
redis-cli flushdb

# Monitor cache hit rates
curl http://localhost:8000/api/docqa/cache/stats
```

### Log Analysis
```bash
# Application logs
docker-compose logs -f app

# Celery worker logs
docker-compose -f docker-compose.rabbitmq.yml logs -f celery-worker-high

# Database logs
docker-compose logs postgres

# Redis logs
docker-compose logs redis
```

## 📊 Performance Tuning

### Database Optimization
```sql
-- Optimize for vector operations
SET maintenance_work_mem = '2GB';
SET max_parallel_workers_per_gather = 4;
SET effective_cache_size = '8GB';

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_documents_embedding_cosine 
ON documents USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Analyze tables
ANALYZE documents;
ANALYZE franchisors;
```

### Redis Optimization
```bash
# Configure Redis for optimal performance
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET maxmemory 4gb
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

### Celery Optimization
```python
# celery_config.py
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True

# Optimize for throughput
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

# Connection pool settings
BROKER_POOL_LIMIT = 10
BROKER_CONNECTION_RETRY_ON_STARTUP = True
```

## 🔐 Security Considerations

### API Security
- Enable HTTPS with valid SSL certificates
- Implement rate limiting for API endpoints
- Use JWT tokens with appropriate expiration
- Validate all input parameters
- Implement proper CORS policies

### Infrastructure Security
- Use strong passwords for all services
- Enable firewall rules to restrict access
- Regular security updates for all components
- Monitor for suspicious activity
- Implement backup and disaster recovery

### Data Protection
- Encrypt sensitive data at rest
- Use secure connections for all services
- Implement proper access controls
- Regular security audits
- Compliance with data protection regulations

## 📈 Scaling Guidelines

### Horizontal Scaling
- Add more Celery workers for increased throughput
- Use multiple Redis instances for cache distribution
- Implement database read replicas
- Use container orchestration (Kubernetes)

### Vertical Scaling
- Increase memory for better caching
- Add more CPU cores for parallel processing
- Use faster storage (NVMe SSDs)
- Optimize network bandwidth

This deployment guide provides a comprehensive foundation for running the enhanced DocQA system in production with high performance, reliability, and scalability.
