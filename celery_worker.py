#!/usr/bin/env python3
"""
Celery Worker Startup Script
Start Celery worker for background task processing
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app

if __name__ == "__main__":
    # Start Celery worker
    celery_app.worker_main([
        "worker",
        "--loglevel=info",
        "--concurrency=4",
        "--queues=document_processing,sms_queue,sms_onboarding_queue,andy_followup_queue",
        "--hostname=worker@%h"
    ])
