# Ruff Configuration (extracted from pyproject.toml)
target-version = "py311"
line-length = 88

select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "S",   # bandit
]

ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert detected
    "S104",  # hardcoded bind all interfaces
]

exclude = [
    ".bzr",
    ".direnv", 
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "alembic/versions",
]

[per-file-ignores]
"tests/*" = ["S101", "ARG001", "ARG002"]
"alembic/*" = ["S101", "ARG001"]
