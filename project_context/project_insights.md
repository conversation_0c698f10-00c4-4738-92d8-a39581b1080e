# Coochie Hydrogreen Franchise Platform: Project Insights & Source of Truth

This file acts as the **source of truth** for the entire Coochie Hydrogreen Franchise Platform. It must be referenced for all future development tasks.

---

## ⚠️ Directive:

- Be **extremely detailed**
- Include **all technical, business, and structural insights**
- Capture **implicit and explicit** requirements
- Record **naming conventions, coding patterns, API rules, and business logic**
- Do **not skip** anything—even assumptions or inferred knowledge
- This file should be **automatically referenced** before generating any new features, database schemas, or endpoints
- Update this file whenever the project evolves or conventions change

---

## 🧠 Project Purpose

Develop a unified digital platform for **Coochie Hydrogreen**, Australia's largest residential lawn care franchise.

The platform must support:

- Franchise application and onboarding
- Prospect outreach and CRM
- Customer and treatment tracking
- Territory and revenue analytics
- Financial management and royalty automation
- Training and certification
- Marketing and campaign effectiveness

---

## 🧩 Core Functional Modules

### 1. Franchisee Management
- Store franchisee profile, contact, status
- Track application-to-approval lifecycle
- Assign territories and manage region exclusivity
- Record start dates, income guarantees, and certifications
- List and search all franchisees (e.g., by name)

### 2. Prospect & Lead CRM
- Store lead source, interest level (Hot, Warm, Cold)
- Track contact status, follow-up dates, decision outcome
- Log call/meeting/email interactions
- Store answers to qualifying questions (budget, background, availability)

### 3. Training Management
- Schedule training sessions
- Record completion status and dates
- Log ChemCert or equivalent certifications

### 4. Customer & Lawn Care Program (LCP)
- Manage bi-monthly treatment schedules per customer
- Track treatment history, service type (full, weed-only, ant)
- Associate customer with franchisee

### 5. Territory Management
- Define and manage region boundaries
- Track maturity stage based on client count
- Enforce exclusivity and link to franchisees

### 6. Financial Module
- Track monthly revenue per franchisee
- Auto-calculate royalty (10% → drops to 5% after $285k) and marketing levy (3% → 1.5%)
- Record expenses (fuel, insurance, stationery, etc.)
- Store forecasted vs actual performance

### 7. Marketing
- Manage national vs local campaigns
- Track effectiveness via leads and conversions
- Store assets (collateral, templates)

---

## 💡 Development Patterns & Debugging

- **Stale Processes**: If routes return unexpected 404s, check for and kill stale `uvicorn` server processes before restarting.
- **Authentication**: All protected endpoints must use the `Depends(get_current_user)` dependency. This dependency handles JWT decoding and user validation.

---

## 🛠️ Coding Conventions

### Backend

- **Language**: Python 3.11+
- **Framework**: FastAPI
- **ORM**: SQLAlchemy (declarative)
- **Data Models**: Pydantic v2
- **Structure**:
    ```bash
    /api
    /services
    /repositories
    /schemas
    /models
    /utils
    ```
- Use **async/await** syntax for all routes
- Follow clear, RESTful API patterns (GET /customers, POST /leads/filter)
- **Security**: Authentication dependencies (`Depends(...)`) **MUST** `raise HTTPException` on failure. Returning a `JSONResponse` will not halt the request and will bypass security.

### Database

- **Engine**: PostgreSQL
- **Naming**: `snake_case` for tables/fields
- **Primary Keys**: UUIDs
- **Timestamps**: `created_at`, `updated_at` everywhere
- Use `Enum` types for statuses and classifications

### API Style

- RESTful, nested resources when needed
- POST `/resource/filter` for complex queries

### Documentation

- Auto-generated Swagger via FastAPI
- Internal docstrings for services and models
- Project logic documentation stored in `docs/`

---

## 📂 File Storage

- **Path**: `project_context/coochie_hydrogreen_insights.md`
- Always load this file into memory/context before any task related to:
    - New modules
    - API endpoints
    - Schema migrations
    - Feature generation
    - Unit/integration testing

---

## 🔁 Update Rule

- **Update immediately** when:
    - New requirements are introduced
    - APIs or data models evolve
    - Coding patterns change
    - Client feedback modifies the business logic
- Use Markdown headers, lists, and code blocks
- Keep the file clean, structured, and exhaustive

---

## 📝 API Response Structure

```python
from enum import Enum
from typing import Any, List, Optional, Dict, Union
from pydantic import BaseModel, Field

# ========================
# ENUMS (UPPERCASE FORMAT)
# ========================
class UserType(str, Enum):
    ADMIN = "ADMIN"
    NORMAL = "NORMAL"
    GUEST = "GUEST"
    ULTIMATE = "ULTIMATE"
    DIVINE = "DIVINE"

# ========================
# MESSAGE STRUCTURE
# ========================
class Message(BaseModel):
    title: Optional[str] = Field(default="", example="Data Retrieved Successfully")
    description: str = Field(..., example="Fetched data successfully.")

# ========================
# PAGINATION DETAILS
# ========================
class Pagination(BaseModel):
    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int

# ========================
# BASE RESPONSE STRUCTURE
# ========================
class ApiResponse(BaseModel):
    success: bool = Field(..., example=True)
    message: Message
    data: Union[Dict[str, Any], List[Any], str, int, float, bool] = Field(default_factory=dict)
    error_code: Optional[int] = Field(default=None, example=1000)

# ========================
# PAGINATED RESPONSE WRAPPER (IF NEEDED)
# ========================
class PaginatedData(BaseModel):
    details: List[Dict[str, Any]]
    pagination: Pagination

# ========================
# FACTORY FUNCTIONS
# ========================
def success_response(
    details: Union[dict, list, str, int, float, bool],
    title: str = "Success",
    description: str = "Request completed successfully"
) -> ApiResponse:
    return ApiResponse(
        success=True,
        message=Message(title=title, description=description),
        data={"details": details}
    )

def paginated_response(
    details: list,
    current_page: int,
    total_pages: int,
    items_per_page: int,
    total_items: int,
    title: str = "Success",
    description: str = "Paginated data fetched"
) -> ApiResponse:
    return ApiResponse(
        success=True,
        message=Message(title=title, description=description),
        data={
            "details": details,
            "pagination": Pagination(
                current_page=current_page,
                total_pages=total_pages,
                items_per_page=items_per_page,
                total_items=total_items
            )
        }
    )

def error_response(
    error_code: int,
    title: str,
    description: str,
    http_success: bool = False
) -> ApiResponse:
    return ApiResponse(
        success=http_success,
        message=Message(title=title, description=description),
        data={},
        error_code=error_code
    )
``` 