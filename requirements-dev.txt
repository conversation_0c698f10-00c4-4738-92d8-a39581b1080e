# GrowthHive Development Dependencies (from pyproject.toml)
# Include production dependencies
-r requirements.txt

# Testing
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<5.0.0
pytest-xdist>=3.3.0,<4.0.0
pytest-randomly>=3.15.0,<4.0.0
time-machine>=2.13.0,<3.0.0
freezegun>=1.2.0,<2.0.0
hypothesis>=6.88.0,<7.0.0
factory-boy>=3.3.0,<4.0.0
faker>=20.0.0,<21.0.0
respx>=0.20.0,<1.0.0

# Ephemeral Infrastructure
testcontainers[postgres]>=3.7.0,<4.0.0
testcontainers[redis]>=3.7.0,<4.0.0

# Static Analysis & Quality
ruff>=0.1.6,<1.0.0
black>=23.11.0,<24.0.0
isort>=5.12.0,<6.0.0
mypy>=1.7.0,<2.0.0
pre-commit>=3.5.0,<4.0.0
bandit>=1.7.5,<2.0.0
pip-audit>=2.6.0,<3.0.0
gitleaks>=8.18.0,<9.0.0

# Type stubs
types-redis>=4.6.0,<5.0.0
types-requests>=2.31.0,<3.0.0
types-python-jose>=3.3.0,<4.0.0
