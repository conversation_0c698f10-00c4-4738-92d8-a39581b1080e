#!/usr/bin/env python3
"""
Enhanced DocQA System Demo

This script demonstrates the enhanced DocQA system with:
- Background processing with immediate response
- Parallel document processing
- Smart chunking and section detection
- Chart analysis and OCR
- Bulk vector operations
- Caching and duplicate detection

Usage:
    python examples/enhanced_docqa_demo.py
"""

import sys
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from docqa.central_processor import CentralDocumentProcessor
from docqa.cache.document_cache import DocumentCache


def demo_background_processing():
    """Demonstrate background processing with immediate response"""
    print("🚀 Enhanced DocQA System Demo - Background Processing")
    print("=" * 60)
    
    # Initialize the central processor
    processor = CentralDocumentProcessor(max_workers=8)
    
    # Example documents to process
    documents = [
        {
            "source": "https://example.com/franchise-brochure.pdf",
            "table": "franchisors",
            "description": "Franchise brochure with charts and financial data"
        },
        {
            "source": "s3://my-bucket/business-plan.pdf", 
            "table": "documents",
            "description": "Business plan document"
        }
    ]
    
    print("📄 Submitting documents for background processing...")
    task_ids = []
    
    for doc in documents:
        print(f"\n📤 Submitting: {doc['description']}")
        print(f"   Source: {doc['source']}")
        print(f"   Table: {doc['table']}")
        
        # Submit for background processing (immediate response)
        task_id = processor.process_document(
            source=doc['source'],
            target_table=doc['table'],
            background=True,  # Background processing
            extract_charts=True,  # Extract and analyze charts
            extract_tables=True,  # Extract tables
            use_ocr=True,  # Use OCR for images
            chunk_size=400,  # 400-token chunks
            chunk_overlap=50  # 50-token overlap
        )
        
        task_ids.append(task_id)
        print(f"   ✅ Task submitted! ID: {task_id[:8]}...")
    
    print(f"\n🎯 All {len(documents)} documents submitted for processing!")
    print("📊 Processing happens in background while you can continue working...")
    
    # Monitor progress
    print("\n📈 Monitoring progress...")
    for i in range(10):  # Monitor for 10 seconds
        print(f"\n⏱️  Status check #{i+1}:")
        
        for j, task_id in enumerate(task_ids):
            status = processor.get_task_status(task_id)
            if status:
                progress = status['progress'] * 100
                print(f"   Task {j+1}: {status['status'].upper()} ({progress:.1f}%)")
                
                if status['status'] == 'completed':
                    result = status.get('result', {})
                    chunks = result.get('chunks_created', 0)
                    proc_time = result.get('processing_time', 0)
                    print(f"      ✅ Completed! {chunks} chunks, {proc_time:.2f}s")
                elif status['status'] == 'failed':
                    error = status.get('error', 'Unknown error')
                    print(f"      ❌ Failed: {error}")
        
        time.sleep(1)
    
    # Get final statistics
    print("\n📊 Final Processing Statistics:")
    stats = processor.get_processing_stats()
    print(f"   Total tasks: {stats['total_tasks']}")
    print(f"   Completed: {stats['completed_tasks']}")
    print(f"   Failed: {stats['failed_tasks']}")
    print(f"   Average processing time: {stats['average_processing_time']:.2f}s")
    print(f"   Total chunks created: {stats['total_chunks_created']}")
    
    # Cleanup
    processor.shutdown()
    print("\n✅ Demo completed!")


def demo_synchronous_processing():
    """Demonstrate synchronous processing with all optimizations"""
    print("\n🔄 Enhanced DocQA System Demo - Synchronous Processing")
    print("=" * 60)
    
    # Initialize the central processor
    processor = CentralDocumentProcessor(max_workers=8)
    
    # Example document
    document_source = "examples/sample_franchise_brochure.pdf"
    
    print(f"📄 Processing document synchronously: {document_source}")
    print("🔧 Features enabled:")
    print("   ✅ Parallel processing with PyMuPDF")
    print("   ✅ Smart chunking with tiktoken (400 tokens, 50 overlap)")
    print("   ✅ Section-aware context tagging")
    print("   ✅ Chart detection with GPT-4 Vision")
    print("   ✅ OCR with pytesseract + OpenCV")
    print("   ✅ Table extraction with camelot + pdfplumber")
    print("   ✅ Bulk vector operations with pgvector")
    print("   ✅ Caching and duplicate detection")
    
    start_time = time.time()
    
    # Process document synchronously
    result = processor.process_document(
        source=document_source,
        target_table="franchisors",
        background=False,  # Synchronous processing
        force_processing=True,  # Force processing for demo
        extract_charts=True,
        extract_tables=True,
        use_ocr=True,
        enhance_ocr=True,  # Enhanced OCR with GPT-4 Vision
        chunk_size=400,
        chunk_overlap=50
    )
    
    processing_time = time.time() - start_time
    
    print("\n📊 Processing Results:")
    if result.success:
        print("   ✅ Success!")
        print(f"   📄 Document ID: {result.document_id}")
        print(f"   🧩 Chunks created: {result.chunks_created}")
        print(f"   ⏱️  Processing time: {result.processing_time:.2f}s")
        print(f"   🎯 Target table: {result.table_name}")
        
        if result.metadata:
            metadata = result.metadata
            print(f"   📑 Sections detected: {metadata.get('sections_detected', 0)}")
            print(f"   📊 Charts detected: {metadata.get('charts_detected', 0)}")
            print(f"   📋 Tables extracted: {metadata.get('tables_extracted', 0)}")
            print(f"   🖼️  Images processed: {metadata.get('images_processed', 0)}")
    else:
        print(f"   ❌ Failed: {result.error_message}")
    
    # Cleanup
    processor.shutdown()
    print("\n✅ Demo completed!")


def demo_caching_system():
    """Demonstrate caching and duplicate detection"""
    print("\n💾 Enhanced DocQA System Demo - Caching & Duplicate Detection")
    print("=" * 60)
    
    # Initialize cache
    cache = DocumentCache()
    
    print("📊 Cache Statistics:")
    stats = cache.get_cache_stats()
    print(f"   📄 Cached documents: {stats['total_entries']}")
    print(f"   ✅ Cache hits: {stats['cache_hits']}")
    print(f"   ❌ Cache misses: {stats['cache_misses']}")
    print(f"   📈 Hit rate: {stats['hit_rate_percent']:.1f}%")
    print(f"   💾 Database size: {stats['database_size_mb']:.2f} MB")
    
    # Show cached documents
    cached_docs = cache.get_cached_documents()
    if cached_docs:
        print("\n📋 Recently Cached Documents:")
        for i, doc in enumerate(cached_docs[:5], 1):  # Show first 5
            print(f"   {i}. {doc['filename']} ({doc['file_type']})")
            print(f"      📄 Document ID: {doc['document_id'][:8]}...")
            print(f"      🧩 Chunks: {doc['chunks_created']}")
            print(f"      ⏱️  Processing time: {doc['processing_time']:.2f}s")
            print(f"      📅 Created: {time.ctime(doc['created_at'])}")
    else:
        print("\n📋 No cached documents found")
    
    print("\n✅ Cache demo completed!")


def demo_question_answering():
    """Demonstrate enhanced question answering with context"""
    print("\n❓ Enhanced DocQA System Demo - Question Answering")
    print("=" * 60)
    
    # Initialize the central processor
    processor = CentralDocumentProcessor()
    
    # Example questions
    questions = [
        "What are the franchise fees and initial investment requirements?",
        "What support is provided to franchisees?",
        "What are the qualification requirements for potential franchisees?",
        "How long is the franchise agreement term?",
        "What is the expected return on investment?"
    ]
    
    print("🤖 Asking questions with enhanced context...")
    
    for i, question in enumerate(questions, 1):
        print(f"\n❓ Question {i}: {question}")
        
        try:
            # Ask question with enhanced context
            response = processor.ask_question(
                question=question,
                top_k=6,
                similarity_threshold=0.7,
                include_context=True
            )
            
            if isinstance(response, dict) and 'answer' in response:
                answer = response['answer']
                sources = response.get('sources', [])
                
                print(f"💬 Answer: {answer[:200]}...")
                print(f"📚 Sources found: {len(sources)}")
                
                # Show context information
                for j, source in enumerate(sources[:2], 1):  # Show first 2 sources
                    if hasattr(source, 'context_info'):
                        context = source.context_info
                        print(f"   📄 Source {j} context:")
                        if context.get('section_types'):
                            print(f"      📑 Sections: {', '.join(context['section_types'])}")
                        if context.get('business_context'):
                            print(f"      💼 Business context: {len(context['business_context'])} categories")
            else:
                print(f"💬 Response: {str(response)[:200]}...")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    # Cleanup
    processor.shutdown()
    print("\n✅ Question answering demo completed!")


def main():
    """Run all demos"""
    print("🎉 Enhanced DocQA System - Complete Demo")
    print("=" * 60)
    print("This demo showcases the enhanced DocQA system with:")
    print("✅ Background processing with immediate response")
    print("✅ Parallel document processing (PyMuPDF, OCR, charts)")
    print("✅ Smart chunking with token awareness (tiktoken)")
    print("✅ Section-aware context tagging")
    print("✅ Chart detection and captioning (GPT-4 Vision)")
    print("✅ Bulk vector operations (pgvector)")
    print("✅ Caching and duplicate detection")
    print("✅ Enhanced question answering with context")
    print()
    
    try:
        # Run demos
        demo_background_processing()
        demo_synchronous_processing()
        demo_caching_system()
        demo_question_answering()
        
        print("\n🎉 All demos completed successfully!")
        print("\n📚 Usage Examples:")
        print("   # Background processing")
        print("   python docqa.py ingest s3://bucket/document.pdf --background")
        print()
        print("   # Synchronous processing")
        print("   python docqa.py ingest document.pdf --sync --charts --tables --ocr")
        print()
        print("   # Ask questions")
        print("   python docqa.py ask 'What are the franchise fees?'")
        print()
        print("   # Check task status")
        print("   python docqa.py status <task_id>")
        print()
        print("   # View statistics")
        print("   python docqa.py stats")
        
    except KeyboardInterrupt:
        print("\n⚠️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
