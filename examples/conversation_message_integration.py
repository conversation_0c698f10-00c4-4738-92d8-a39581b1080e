"""
Conversation Message Integration Example
Demonstrates how to integrate the AI agent conversation processing with Kudosity webhooks
"""

import asyncio
import uuid
from typing import Dict, Any, List

# Example of how to integrate conversation message processing in webhook handlers
async def process_kudosity_webhook_conversation(webhook_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Example function showing how to process Kudosity webhook data
    and store conversation messages using the AI agent
    """
    
    # Example webhook payload structure from Kudosity
    example_webhook_payload = {
        "event_type": "SMS_INBOUND",
        "timestamp": "2024-01-01T12:00:00Z",
        "mo": {
            "sender": "+**********",
            "recipient": "+1987654321",
            "message": "I'm interested in your franchise opportunity."
        }
    }
    
    # Extract conversation data from webhook
    conversation_data = []
    
    # In a real implementation, you would:
    # 1. Extract the conversation history from your system
    # 2. Include the new message from the webhook
    # 3. Process with the AI agent
    
    # Example conversation data structure
    conversation_data = [
        {
            "message": "Hello, I saw your franchise advertisement.",
            "sender_info": "+**********",
            "timestamp": "2024-01-01T11:58:00Z"
        },
        {
            "message": "Thank you for your interest! I'd be happy to help you learn more about our franchise opportunity.",
            "sender_info": "system",
            "timestamp": "2024-01-01T11:59:00Z"
        },
        {
            "message": webhook_payload.get("mo", {}).get("message", ""),
            "sender_info": webhook_payload.get("mo", {}).get("sender", ""),
            "timestamp": webhook_payload.get("timestamp", "")
        }
    ]
    
    # Find or create lead based on phone number
    lead_id = await find_or_create_lead_by_phone(webhook_payload.get("mo", {}).get("sender"))
    
    # Determine franchisor (in this example, using Coochie Hydrogreen)
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"  # Coochie Hydrogreen
    
    # Prepare AI agent request
    ai_agent_request = {
        "conversation_data": conversation_data,
        "lead_id": lead_id,
        "franchisor_id": franchisor_id
    }
    
    # In a real implementation, you would call the conversation message service
    # from app.services.conversation_message_service import ConversationMessageService
    # service = ConversationMessageService(repository)
    # result = await service.parse_and_store_conversation(ai_agent_request)
    
    # Mock response for demonstration
    mock_response = {
        "total_parsed": len(conversation_data),
        "successfully_stored": len(conversation_data),
        "processing_metadata": {
            "processing_time_ms": 150.5,
            "ai_confidence": 0.95,
            "detected_patterns": ["greeting", "interest_expression"],
            "speaker_identification": {
                "lead_messages": 2,
                "system_messages": 1
            }
        }
    }
    
    return mock_response


async def find_or_create_lead_by_phone(phone_number: str) -> str:
    """
    Example function to find or create a lead by phone number
    In a real implementation, this would interact with your lead service
    """
    # Mock implementation - in reality, you would:
    # 1. Query the database for existing lead with this phone number
    # 2. If not found, create a new lead
    # 3. Return the lead ID
    
    return str(uuid.uuid4())


def demonstrate_ai_agent_speaker_identification():
    """
    Demonstrate how the AI agent identifies speakers in conversation messages
    """
    
    print("🤖 AI Agent Speaker Identification Examples")
    print("=" * 50)
    
    # Example messages and how the AI agent would classify them
    test_messages = [
        {
            "message": "I'm interested in your franchise opportunity.",
            "sender_info": "+**********",
            "expected_sender": "lead",
            "reason": "Phone number indicates lead, message shows interest"
        },
        {
            "message": "Thank you for your interest! May I know your full name?",
            "sender_info": "system",
            "expected_sender": "system",
            "reason": "Explicit system sender info, professional response pattern"
        },
        {
            "message": "My name is John Doe and I have $50,000 to invest.",
            "sender_info": "+**********",
            "expected_sender": "lead",
            "reason": "Phone number indicates lead, personal information sharing"
        },
        {
            "message": "Great! Our franchise requires an initial investment of $45,000.",
            "sender_info": "bot",
            "expected_sender": "system",
            "reason": "Bot sender info, providing business information"
        },
        {
            "message": "What kind of training do you provide?",
            "sender_info": "+1987654321",
            "expected_sender": "lead",
            "reason": "Phone number indicates lead, asking questions"
        },
        {
            "message": "We provide comprehensive 2-week training program.",
            "sender_info": "agent",
            "expected_sender": "system",
            "reason": "Agent sender info, providing detailed business information"
        }
    ]
    
    for i, test_case in enumerate(test_messages, 1):
        print(f"\n📝 Example {i}:")
        print(f"   Message: '{test_case['message']}'")
        print(f"   Sender Info: '{test_case['sender_info']}'")
        print(f"   AI Classification: {test_case['expected_sender']}")
        print(f"   Reasoning: {test_case['reason']}")


def demonstrate_conversation_flow():
    """
    Demonstrate a complete conversation flow with AI agent processing
    """
    
    print("\n💬 Complete Conversation Flow Example")
    print("=" * 50)
    
    conversation_flow = [
        {
            "step": 1,
            "webhook_event": "SMS_INBOUND",
            "message": "Hello, I saw your franchise ad on Facebook.",
            "sender": "+**********",
            "ai_classification": "lead",
            "ai_confidence": 0.98,
            "patterns_detected": ["greeting", "lead_source_mention"]
        },
        {
            "step": 2,
            "webhook_event": "SMS_OUTBOUND",
            "message": "Thank you for your interest! I'd love to help you learn more about our franchise opportunity. May I know your full name?",
            "sender": "system",
            "ai_classification": "system",
            "ai_confidence": 0.99,
            "patterns_detected": ["professional_greeting", "information_request"]
        },
        {
            "step": 3,
            "webhook_event": "SMS_INBOUND",
            "message": "My name is Sarah Johnson. I'm looking for a business opportunity in the health and wellness sector.",
            "sender": "+**********",
            "ai_classification": "lead",
            "ai_confidence": 0.97,
            "patterns_detected": ["personal_info_sharing", "sector_preference"]
        },
        {
            "step": 4,
            "webhook_event": "SMS_OUTBOUND",
            "message": "Perfect, Sarah! Our franchise is in the hydroponics and sustainable agriculture space. The initial investment starts from $45,000. Would you like to schedule a call to discuss further?",
            "sender": "system",
            "ai_classification": "system",
            "ai_confidence": 0.99,
            "patterns_detected": ["business_info_sharing", "meeting_request"]
        }
    ]
    
    for step in conversation_flow:
        print(f"\n🔄 Step {step['step']} - {step['webhook_event']}")
        print(f"   📱 Message: '{step['message']}'")
        print(f"   👤 Sender: {step['sender']}")
        print(f"   🤖 AI Classification: {step['ai_classification']} (confidence: {step['ai_confidence']})")
        print(f"   🎯 Patterns Detected: {', '.join(step['patterns_detected'])}")


def demonstrate_api_usage():
    """
    Demonstrate how to use the conversation message API endpoints
    """
    
    print("\n🔌 API Usage Examples")
    print("=" * 50)
    
    api_examples = [
        {
            "endpoint": "POST /api/conversation/conversation-messages",
            "description": "Create a single conversation message",
            "example_payload": {
                "lead_id": "123e4567-e89b-12d3-a456-426614174000",
                "franchisor_id": "456e7890-e89b-12d3-a456-426614174001",
                "sender": "lead",
                "message": "I'm interested in your franchise opportunity."
            }
        },
        {
            "endpoint": "POST /api/conversation/conversation-messages/bulk",
            "description": "Create multiple conversation messages in bulk",
            "example_payload": {
                "messages": [
                    {
                        "lead_id": "123e4567-e89b-12d3-a456-426614174000",
                        "franchisor_id": "456e7890-e89b-12d3-a456-426614174001",
                        "sender": "lead",
                        "message": "I'm interested in your franchise."
                    },
                    {
                        "lead_id": "123e4567-e89b-12d3-a456-426614174000",
                        "franchisor_id": "456e7890-e89b-12d3-a456-426614174001",
                        "sender": "system",
                        "message": "Great! May I know your full name?"
                    }
                ]
            }
        },
        {
            "endpoint": "POST /api/conversation/conversation-messages/parse-and-store",
            "description": "AI agent endpoint to parse and store conversation from webhook",
            "example_payload": {
                "conversation_data": [
                    {
                        "message": "I'm interested in your franchise.",
                        "sender_info": "+**********",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                ],
                "lead_id": "123e4567-e89b-12d3-a456-426614174000",
                "franchisor_id": "456e7890-e89b-12d3-a456-426614174001"
            }
        },
        {
            "endpoint": "GET /api/conversation/conversation-messages/lead/{lead_id}",
            "description": "Get all conversation messages for a specific lead",
            "example_params": "?page=1&per_page=20&order=asc"
        },
        {
            "endpoint": "GET /api/conversation/conversation-messages/stats",
            "description": "Get conversation statistics",
            "example_params": "?lead_id=123e4567-e89b-12d3-a456-426614174000"
        }
    ]
    
    for example in api_examples:
        print(f"\n🌐 {example['endpoint']}")
        print(f"   📝 Description: {example['description']}")
        if 'example_payload' in example:
            print(f"   📦 Example Payload: {example['example_payload']}")
        if 'example_params' in example:
            print(f"   🔗 Example Params: {example['example_params']}")


if __name__ == "__main__":
    print("🚀 Conversation Message Integration Examples")
    print("=" * 60)
    
    # Run demonstrations
    demonstrate_ai_agent_speaker_identification()
    demonstrate_conversation_flow()
    demonstrate_api_usage()
    
    print("\n✅ Integration examples completed!")
    print("\nNext steps:")
    print("1. Integrate the AI agent with your Kudosity webhook handlers")
    print("2. Use the conversation message API endpoints in your application")
    print("3. Monitor conversation statistics and patterns")
    print("4. Implement lead qualification based on conversation analysis")
