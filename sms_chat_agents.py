#!/usr/bin/env python3
"""
SMS Chat Agents - Specialized agents for enhanced SMS conversation management
"""

import json
import time
import random
from datetime import datetime
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod


class BaseAgent(ABC):
    """Base class for all SMS chat agents"""
    
    def __init__(self, name: str):
        self.name = name
        self.created_at = datetime.now()
    
    @abstractmethod
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process the given context and return updated context"""
        pass
    
    def log(self, message: str, level: str = "INFO"):
        """Log agent activity"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {self.name} ({level}): {message}")


class ConversationAnalyzerAgent(BaseAgent):
    """Agent that analyzes conversation patterns and user intent"""
    
    def __init__(self):
        super().__init__("ConversationAnalyzer")
        
        self.intent_patterns = {
            "question": ["what", "how", "when", "where", "why", "can you", "tell me", "?"],
            "interest": ["interested", "want to know", "tell me more", "learn about"],
            "concern": ["worried", "concerned", "but", "however", "what if"],
            "positive": ["great", "excellent", "perfect", "sounds good", "yes"],
            "negative": ["no", "not interested", "don't want", "can't"],
            "closing": ["thanks", "thank you", "goodbye", "bye", "talk later"]
        }
        
        self.engagement_indicators = {
            "high": ["tell me more", "very interested", "when can we", "how do I"],
            "medium": ["interesting", "good to know", "makes sense", "ok"],
            "low": ["ok", "sure", "fine", "whatever"]
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze conversation and update context with insights"""
        user_message = context.get("last_user_message", "").lower()
        
        # Detect intent
        detected_intent = self.detect_intent(user_message)
        
        # Measure engagement level
        engagement_level = self.measure_engagement(user_message)
        
        # Analyze conversation flow
        flow_analysis = self.analyze_conversation_flow(context)
        
        # Update context with analysis
        context.update({
            "detected_intent": detected_intent,
            "engagement_level": engagement_level,
            "flow_analysis": flow_analysis,
            "analysis_timestamp": datetime.now().isoformat()
        })
        
        self.log(f"Intent: {detected_intent}, Engagement: {engagement_level}")
        return context
    
    def detect_intent(self, message: str) -> str:
        """Detect user intent from message"""
        for intent, patterns in self.intent_patterns.items():
            if any(pattern in message for pattern in patterns):
                return intent
        return "general"
    
    def measure_engagement(self, message: str) -> str:
        """Measure user engagement level"""
        for level, indicators in self.engagement_indicators.items():
            if any(indicator in message for indicator in indicators):
                return level
        
        # Default engagement based on message length and complexity
        if len(message) > 50:
            return "high"
        elif len(message) > 20:
            return "medium"
        else:
            return "low"
    
    def analyze_conversation_flow(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze overall conversation flow"""
        message_count = context.get("message_count", 0)
        last_activity = context.get("last_activity")
        
        # Calculate conversation velocity
        velocity = "normal"
        if last_activity:
            try:
                last_time = datetime.fromisoformat(last_activity)
                time_diff = (datetime.now() - last_time).seconds
                if time_diff < 10:
                    velocity = "fast"
                elif time_diff > 60:
                    velocity = "slow"
            except:
                pass
        
        return {
            "message_count": message_count,
            "conversation_velocity": velocity,
            "conversation_stage": self.determine_stage(context)
        }
    
    def determine_stage(self, context: Dict[str, Any]) -> str:
        """Determine current conversation stage"""
        message_count = context.get("message_count", 0)
        qualification_started = context.get("qualification_started", False)
        
        if message_count <= 2:
            return "introduction"
        elif not qualification_started and message_count <= 5:
            return "rapport_building"
        elif qualification_started:
            return "qualification"
        else:
            return "consultation"


class ResponseStrategyAgent(BaseAgent):
    """Agent that determines the best response strategy"""
    
    def __init__(self):
        super().__init__("ResponseStrategy")
        
        self.response_strategies = {
            "answer_question": {
                "priority": 1,
                "description": "User asked a direct question - answer it"
            },
            "ask_qualification": {
                "priority": 2,
                "description": "Natural break to ask qualification question"
            },
            "build_rapport": {
                "priority": 3,
                "description": "Continue building relationship"
            },
            "provide_information": {
                "priority": 4,
                "description": "Share relevant franchise information"
            },
            "schedule_meeting": {
                "priority": 5,
                "description": "Suggest scheduling a consultation"
            }
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Determine the best response strategy"""
        # Analyze current situation
        intent = context.get("detected_intent", "general")
        engagement = context.get("engagement_level", "medium")
        stage = context.get("flow_analysis", {}).get("conversation_stage", "introduction")
        
        # Determine strategy
        strategy = self.select_strategy(intent, engagement, stage, context)
        
        # Update context
        context.update({
            "response_strategy": strategy,
            "strategy_reason": self.get_strategy_reason(strategy, context),
            "strategy_timestamp": datetime.now().isoformat()
        })
        
        self.log(f"Selected strategy: {strategy}")
        return context
    
    def select_strategy(self, intent: str, engagement: str, stage: str, context: Dict[str, Any]) -> str:
        """Select the best response strategy"""
        # If user asked a question, answer it first
        if intent == "question":
            return "answer_question"
        
        # If user shows high engagement, continue the conversation
        if engagement == "high":
            if stage == "introduction":
                return "build_rapport"
            else:
                return "provide_information"
        
        # Check if it's time for qualification
        if self.should_ask_qualification(context):
            return "ask_qualification"
        
        # If user shows interest, provide more information
        if intent == "interest":
            return "provide_information"
        
        # If conversation is advanced, suggest meeting
        if stage == "consultation" and engagement in ["high", "medium"]:
            return "schedule_meeting"
        
        # Default to building rapport
        return "build_rapport"
    
    def should_ask_qualification(self, context: Dict[str, Any]) -> bool:
        """Determine if we should ask a qualification question"""
        # Check if we're in a natural break
        intent = context.get("detected_intent", "general")
        if intent in ["positive", "closing"]:
            return True
        
        # Check message count
        message_count = context.get("message_count", 0)
        if message_count > 0 and message_count % 3 == 0:
            return True
        
        # Check if user seems engaged but we haven't started qualification
        engagement = context.get("engagement_level", "medium")
        qualification_started = context.get("qualification_started", False)
        if engagement == "high" and not qualification_started:
            return True
        
        return False
    
    def get_strategy_reason(self, strategy: str, context: Dict[str, Any]) -> str:
        """Get explanation for why this strategy was selected"""
        reasons = {
            "answer_question": "User asked a direct question",
            "ask_qualification": "Natural break in conversation for qualification",
            "build_rapport": "Building relationship and trust",
            "provide_information": "User shows interest in learning more",
            "schedule_meeting": "Ready for next step consultation"
        }
        return reasons.get(strategy, "Default strategy selection")


class ResponseGeneratorAgent(BaseAgent):
    """Agent that generates appropriate responses based on strategy"""
    
    def __init__(self):
        super().__init__("ResponseGenerator")
        
        self.response_templates = {
            "qualification_transitions": [
                "That's awesome! I'm curious though -",
                "Love it! Quick question for you -",
                "That's exactly what I like to hear! By the way,",
                "Perfect! I'm wondering though,",
                "That makes total sense! Can I ask you something?",
                "I hear you! One thing I'm curious about -"
            ],
            "rapport_builders": [
                "I love your enthusiasm! 😊",
                "That's exactly the kind of thinking that leads to success!",
                "You're asking all the right questions!",
                "I can tell you're really thinking this through carefully.",
                "Your approach to this is really smart!",
                "I'm getting excited just talking with you about this! 🚀"
            ],
            "information_connectors": [
                "Based on what you've shared, here's what I think you'd find interesting...",
                "That's a common concern, and here's what most successful franchisees do...",
                "Great question! Let me break that down for you...",
                "I'm glad you asked! This is actually one of the key benefits...",
                "You know what? That reminds me of something important...",
                "Here's something cool that might interest you..."
            ],
            "natural_responses": [
                "Absolutely!",
                "That makes perfect sense!",
                "I totally get that.",
                "You're spot on with that thinking.",
                "That's a really good point.",
                "I hear you on that one."
            ]
        }
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response based on strategy"""
        strategy = context.get("response_strategy", "build_rapport")
        
        if strategy == "ask_qualification":
            response = self.generate_qualification_response(context)
        elif strategy == "build_rapport":
            response = self.generate_rapport_response(context)
        elif strategy == "provide_information":
            response = self.generate_information_response(context)
        elif strategy == "schedule_meeting":
            response = self.generate_meeting_response(context)
        else:
            response = self.generate_default_response(context)
        
        context.update({
            "generated_response": response,
            "generation_timestamp": datetime.now().isoformat()
        })
        
        self.log(f"Generated {strategy} response")
        return context
    
    def generate_qualification_response(self, context: Dict[str, Any]) -> str:
        """Generate response that includes qualification question"""
        transition = random.choice(self.response_templates["qualification_transitions"])
        
        # This would integrate with the PreQualificationAgent
        # For now, return the transition phrase
        return transition
    
    def generate_rapport_response(self, context: Dict[str, Any]) -> str:
        """Generate rapport-building response"""
        return random.choice(self.response_templates["rapport_builders"])
    
    def generate_information_response(self, context: Dict[str, Any]) -> str:
        """Generate informational response"""
        connector = random.choice(self.response_templates["information_connectors"])
        return connector
    
    def generate_meeting_response(self, context: Dict[str, Any]) -> str:
        """Generate meeting scheduling response"""
        return "I think you'd really benefit from a quick call with one of our franchise specialists. When would be a good time for a 15-minute chat?"
    
    def generate_default_response(self, context: Dict[str, Any]) -> str:
        """Generate default response"""
        return "I'm here to help you explore franchise opportunities. What would you like to know?"


class ConversationOrchestratorAgent(BaseAgent):
    """Master agent that orchestrates all other agents"""
    
    def __init__(self):
        super().__init__("ConversationOrchestrator")
        
        # Initialize specialized agents
        self.analyzer = ConversationAnalyzerAgent()
        self.strategy_agent = ResponseStrategyAgent()
        self.generator = ResponseGeneratorAgent()
        
        self.processing_pipeline = [
            self.analyzer,
            self.strategy_agent,
            self.generator
        ]
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Orchestrate the full conversation processing pipeline"""
        self.log("Starting conversation processing pipeline")
        
        # Run through all agents in sequence
        for agent in self.processing_pipeline:
            try:
                context = await agent.process(context)
                self.log(f"Completed {agent.name} processing")
            except Exception as e:
                self.log(f"Error in {agent.name}: {str(e)}", "ERROR")
                # Continue with next agent even if one fails
        
        # Add orchestration metadata
        context.update({
            "orchestration_complete": True,
            "processing_timestamp": datetime.now().isoformat(),
            "agents_used": [agent.name for agent in self.processing_pipeline]
        })
        
        self.log("Conversation processing pipeline complete")
        return context
    
    def get_processing_summary(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get summary of processing results"""
        return {
            "intent": context.get("detected_intent"),
            "engagement": context.get("engagement_level"),
            "strategy": context.get("response_strategy"),
            "stage": context.get("flow_analysis", {}).get("conversation_stage"),
            "agents_used": context.get("agents_used", []),
            "processing_time": context.get("processing_timestamp")
        }


class SMSChatAgentManager:
    """Manager class for SMS chat agents"""
    
    def __init__(self):
        self.orchestrator = ConversationOrchestratorAgent()
        self.active_sessions = {}
    
    async def process_message(self, session_id: str, user_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a message through the agent pipeline"""
        # Update context with current message
        context.update({
            "session_id": session_id,
            "last_user_message": user_message,
            "processing_started": datetime.now().isoformat()
        })
        
        # Process through orchestrator
        result_context = await self.orchestrator.process(context)
        
        # Store session context
        self.active_sessions[session_id] = result_context
        
        return result_context
    
    def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """Get context for a specific session"""
        return self.active_sessions.get(session_id, {})
    
    def get_processing_summary(self, session_id: str) -> Dict[str, Any]:
        """Get processing summary for a session"""
        context = self.get_session_context(session_id)
        return self.orchestrator.get_processing_summary(context)


# Example usage and testing
async def test_agents():
    """Test the agent system"""
    print("🧪 Testing SMS Chat Agents...")
    
    manager = SMSChatAgentManager()
    
    # Test conversation
    test_messages = [
        "Hi, I'm interested in franchise opportunities",
        "What kind of investment is required?",
        "That sounds reasonable. Tell me more about support.",
        "Great! I'd like to learn more."
    ]
    
    session_id = "test_session_123"
    context = {
        "message_count": 0,
        "qualification_started": False,
        "user_name": "Test User"
    }
    
    for i, message in enumerate(test_messages):
        print(f"\n--- Processing Message {i+1}: '{message}' ---")
        
        context["message_count"] = i + 1
        result = await manager.process_message(session_id, message, context.copy())
        
        # Show results
        summary = manager.get_processing_summary(session_id)
        print(f"Intent: {summary['intent']}")
        print(f"Engagement: {summary['engagement']}")
        print(f"Strategy: {summary['strategy']}")
        print(f"Stage: {summary['stage']}")
        
        context = result  # Update context for next iteration
    
    print("\n✅ Agent testing complete!")


if __name__ == "__main__":
    import asyncio
    asyncio.run(test_agents())
