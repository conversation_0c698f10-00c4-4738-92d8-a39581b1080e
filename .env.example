# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo
OPENAI_VISION_MODEL=gpt-4o
EMBEDDING_MODEL=text-embedding-3-small

# AWS Configuration (optional - can use AWS CLI credentials)
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_DEFAULT_REGION=us-east-1

# DocQA Configuration
FAISS_INDEX_PATH=./data/faiss_index
CHUNK_SIZE=400
CHUNK_OVERLAP=50
TOP_K_RETRIEVAL=6
MAX_TOKENS_RESPONSE=1000

# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/growthhive

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Redis Configuration
REDIS_URL=redis://localhost:6379/1
REDIS_SESSION_DB=2
REDIS_CACHE_DB=3

# Celery Configuration
CELERY_BROKER_URL=amqp://guest:guest@localhost:5672/
CELERY_RESULT_BACKEND=redis://localhost:6379/0
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Kudosity SMS Configuration
KUDOSITY_API_BASE_URL=https://api.transmitsms.com
KUDOSITY_API_KEY=your_kudosity_api_key_here
KUDOSITY_API_SECRET=your_kudosity_api_secret_here
KUDOSITY_FROM_NUMBER=+***********
KUDOSITY_SMS_ENABLED=false

# Zoho CRM Configuration
ZOHO_CLIENT_ID=your_zoho_client_id_here
ZOHO_CLIENT_SECRET=your_zoho_client_secret_here
ZOHO_REFRESH_TOKEN=your_zoho_refresh_token_here
ZOHO_BASE_URL=https://www.zohoapis.com.au/crm/v2
ZOHO_AUTH_URL=https://accounts.zoho.com.au/oauth/v2/token
ZOHO_SYNC_INTERVAL_MINUTES=15

# Zoho Bookings Configuration
ZOHO_BOOKINGS_CLIENT_ID=your_zoho_bookings_client_id_here
ZOHO_BOOKINGS_CLIENT_SECRET=your_zoho_bookings_client_secret_here
ZOHO_BOOKINGS_REFRESH_TOKEN=your_zoho_bookings_refresh_token_here
ZOHO_BOOKINGS_BASE_URL=https://bookings.zoho.com.au/api/v1
ZOHO_BOOKINGS_AUTH_URL=https://accounts.zoho.com.au/oauth/v2/token
ZOHO_BOOKINGS_SCOPE=ZohoBookings.fullaccess.all
ZOHO_BOOKINGS_API_DOMAIN=https://bookings.zoho.com.au
ZOHO_BOOKINGS_TOKEN_TYPE=Bearer
ZOHO_BOOKINGS_EXPIRES_IN=3600

# Meeting Agent Configuration
MEETING_AGENT_ENABLED=false
MEETING_AGENT_PROVIDER=zoho
MEETING_AGENT_LOCALE=en_IN
MEETING_AGENT_DEFAULT_TIMEZONE=Asia/Kolkata
MEETING_AGENT_MAX_CLARIFICATIONS=2
MEETING_AGENT_CONVERSATION_TIMEOUT=1800

# Logging Configuration
LOG_LEVEL=INFO
