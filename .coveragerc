# Coverage Configuration (extracted from pyproject.toml)
[run]
source = app
omit =
    */tests/*
    */alembic/*
    */migrations/*
    */__pycache__/*
    */venv/*
    */.venv/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
show_missing = true
skip_covered = false
precision = 2

[html]
directory = reports/coverage
