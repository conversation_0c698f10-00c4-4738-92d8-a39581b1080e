#!/usr/bin/env python3
"""
GrowthHive Setup Configuration
Replaces pyproject.toml build system and project metadata
"""

from setuptools import setup, find_packages
import os

# Read version from app/__init__.py
def get_version():
    version_file = os.path.join(os.path.dirname(__file__), 'app', '__init__.py')
    if os.path.exists(version_file):
        with open(version_file, 'r') as f:
            for line in f:
                if line.startswith('__version__'):
                    return line.split('=')[1].strip().strip('"').strip("'")
    return "1.0.0"

# Read requirements from requirements.txt
def get_requirements():
    requirements_file = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_file):
        with open(requirements_file, 'r') as f:
            requirements = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-r'):
                    requirements.append(line)
            return requirements
    return []

# Read development requirements
def get_dev_requirements():
    requirements_file = os.path.join(os.path.dirname(__file__), 'requirements-dev.txt')
    if os.path.exists(requirements_file):
        with open(requirements_file, 'r') as f:
            requirements = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('-r'):
                    requirements.append(line)
            return requirements
    return []

# Read README
def get_long_description():
    readme_file = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_file):
        with open(readme_file, 'r', encoding='utf-8') as f:
            return f.read()
    return "AI-enabled prospect outreach platform for franchise management"

setup(
    name="growthhive",
    version=get_version(),
    description="AI-enabled prospect outreach platform for franchise management",
    long_description=get_long_description(),
    long_description_content_type="text/markdown",
    author="GrowthHive Team",
    author_email="<EMAIL>",
    url="https://github.com/growthhive/growthhive",
    project_urls={
        "Homepage": "https://github.com/growthhive/growthhive",
        "Documentation": "https://docs.growthhive.com",
        "Repository": "https://github.com/growthhive/growthhive.git",
        "Issues": "https://github.com/growthhive/growthhive/issues",
    },
    packages=find_packages(),
    include_package_data=True,
    python_requires=">=3.11",
    install_requires=get_requirements(),
    extras_require={
        "dev": get_dev_requirements(),
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    license="MIT",
    zip_safe=False,
)
