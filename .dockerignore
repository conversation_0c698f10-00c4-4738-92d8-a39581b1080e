# Comprehensive .dockerignore to reduce Docker context size from 2.37GB to ~50MB

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
venv311/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation (exclude from Docker build)
*.md
docs/
reports/
README*
CHANGELOG*
LICENSE*
FOLLOWUP_IMPLEMENTATION_COMPLETE.md

# Test files (exclude from Docker build)
tests/
test_*/
*_test.py
pytest.ini
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
run_followup_tests.py

# Jupyter Notebooks
.ipynb_checkpoints/
*.ipynb

# Environment files
!.env.example

# Database files
*.db
*.sqlite
*.sqlite3

# Log files
*.log
logs/
log/

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Large data files
*.csv
*.json
*.xml
*.xlsx
*.xls
data/
datasets/

# Media files
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
*.ico
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mp3
*.wav
*.flac
*.aac

# Archive files
*.zip
*.tar
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Backup files
*.bak
*.backup
*.old
*.orig

# Cache directories
.cache/
.npm/
.yarn/

# Deployment files
deployment/
deploy/
k8s/
kubernetes/

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile

# Monitoring and metrics
prometheus/
grafana/
monitoring/

# Scripts (keep only essential ones)
scripts/
!scripts/start_background_services.sh

# Large dependencies that will be installed in container
site-packages/
Lib/

# Development tools
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# Profiling data
.prof

# MacOS
.AppleDouble
.LSOverride
Icon

# Windows
desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# Exclude large directories that aren't needed in container
alembic/versions/*.py
!alembic/versions/__init__.py

# Keep only essential files for Docker build
# Include: app/, docqa/, requirements.txt, alembic/, .env files
