#!/usr/bin/env python3
"""
Zoho Bookings OAuth Setup Script
Auto-generated with your current credentials
"""

import webbrowser
import urllib.parse
import requests

# Your Zoho API Configuration
CLIENT_ID = "1000.PRYTZ0VSO7PV6KL8ZU0TLLMOF8BU5A"
CLIENT_SECRET = "d2891e7f56126961192d2c98856add5e3f6a7082be"
REDIRECT_URI = "http://localhost:3000/oauth/callback"
SCOPE = "zohobookings.data.CREATE"

def main():
    print("🎯 ZOHO BOOKINGS OAUTH SETUP")
    print("=" * 50)
    
    # Step 1: Generate authorization URL
    auth_params = {
        'scope': SCOPE,
        'client_id': CLIENT_ID,
        'response_type': 'code',
        'redirect_uri': REDIRECT_URI,
        'access_type': 'offline'
    }
    
    auth_url = f"https://accounts.zoho.com/oauth/v2/auth?{urllib.parse.urlencode(auth_params)}"
    
    print(f"📋 Your Configuration:")
    print(f"   Client ID: {CLIENT_ID}")
    print(f"   Redirect URI: {REDIRECT_URI}")
    print(f"   Scope: {SCOPE}")
    print()
    
    print("🔗 Authorization URL:")
    print(auth_url)
    print()
    
    print("📋 INSTRUCTIONS:")
    print("1. Copy the URL above and open it in your browser")
    print("2. Authorize the application")
    print("3. Copy the authorization code from the redirect URL")
    print("4. Use the code with the /exchange-token endpoint")
    print()
    
    try:
        webbrowser.open(auth_url)
        print("✅ Browser opened automatically!")
    except:
        print("❌ Could not open browser automatically")
    
    print("\n🔄 Next step:")
    print("Use the authorization code with:")
    print("POST /zoho-bookings-oauth/exchange-token")
    print('{"authorization_code": "YOUR_CODE_HERE"}')

if __name__ == "__main__":
    main()
