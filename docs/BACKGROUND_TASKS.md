# Background Task Processing with RabbitMQ & Celery

This document explains the implementation of proper thread separation for document upload and processing in the GrowthHive project using RabbitMQ and Celery.

## Architecture Overview

### Thread Separation Design

1. **Upload Thread (FastAPI)**: Handles file uploads immediately and returns success response
2. **Processing Thread (Celery Workers)**: Processes documents in background using separate worker processes
3. **Message Queue (RabbitMQ)**: Decouples upload and processing threads
4. **Result Backend (Redis)**: Stores task results and status

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │───▶│    RabbitMQ     │───▶│ Celery Workers  │
│  (Upload Thread)│    │ (Message Queue) │    │(Processing Thread)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              ┌─────────────────┐             │
         └─────────────▶│     Redis       │◀────────────┘
                        │(Result Backend) │
                        └─────────────────┘
```

## Key Components

### 1. Celery Application (`app/core/celery_app.py`)
- Centralized Celery configuration
- Task routing and queue management
- Retry and error handling settings

### 2. Background Task Manager (`app/services/background_task_manager.py`)
- High-level service for task management
- Queue document processing tasks
- Monitor task status and progress
- Cancel running tasks

### 3. Task Definitions
- **Document Processing** (`app/tasks/document_processing.py`): General document processing
- **DocQA Processing** (`app/tasks/docqa_processing.py`): Franchisor brochure processing

### 4. API Endpoints (`app/api/v1/endpoints/background_tasks.py`)
- Task status monitoring
- Task cancellation
- Queue health checks
- Active task listing

## Benefits of This Implementation

### ✅ Complete Thread Separation
- Upload requests return immediately (< 1 second)
- Processing happens in separate worker processes
- No blocking or timeouts on upload endpoints

### ✅ Scalability
- Multiple worker processes can run in parallel
- Horizontal scaling by adding more workers
- Queue-based load distribution

### ✅ Reliability
- Automatic task retries on failure
- Persistent task queue (survives restarts)
- Dead letter queue for failed tasks

### ✅ Monitoring
- Real-time task status tracking
- Celery Flower web interface
- Queue length and worker health monitoring

## Usage Examples

### Document Upload (Immediate Response)
```python
# POST /api/documents/upload
# Returns immediately with document info
{
    "success": true,
    "data": {
        "id": "doc_123",
        "name": "document.pdf",
        "processing_status": "pending"
    },
    "message": "Document uploaded successfully. Processing started in background."
}
```

### Check Processing Status
```python
# GET /api/background-tasks/status/{task_id}
{
    "success": true,
    "data": {
        "task_id": "abc-123",
        "status": "PROCESSING",
        "ready": false,
        "result": null
    }
}
```

### Monitor Queue Health
```python
# GET /api/background-tasks/health
{
    "success": true,
    "data": {
        "celery_healthy": true,
        "queue_length": 5,
        "active_tasks_count": 2,
        "status": "healthy"
    }
}
```

## Configuration

### Environment Variables
```bash
# RabbitMQ Configuration
RABBITMQ_URL=amqp://guest:guest@localhost:5672//
CELERY_BROKER_URL=amqp://guest:guest@localhost:5672//

# Redis Configuration  
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Task Configuration
DOCUMENT_PROCESSING_QUEUE=document_processing
DOCUMENT_PROCESSING_RETRY_DELAY=60
DOCUMENT_PROCESSING_MAX_RETRIES=3
```

## Running the System

### 1. Start Background Services
```bash
# Start RabbitMQ, Redis, and Celery workers
./scripts/start_background_services.sh
```

### 2. Start FastAPI Application
```bash
# Start the main application
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 3. Monitor with Flower
```bash
# Access Celery monitoring at http://localhost:5555
```

## Service URLs

- **RabbitMQ Management**: http://localhost:15672 (growthhive/growthhive123)
- **Celery Flower**: http://localhost:5555
- **Redis**: localhost:6379

## Task Flow

### Document Upload Flow
1. User uploads document via `/api/documents/upload`
2. FastAPI saves file to S3 and creates database record
3. Task is queued to RabbitMQ with document details
4. FastAPI returns immediate success response
5. Celery worker picks up task from queue
6. Worker processes document with DocQA
7. Worker updates database with processing results
8. User can check status via `/api/background-tasks/status/{task_id}`

### Error Handling
- Failed tasks are automatically retried (max 3 times)
- Permanent failures are logged and marked as failed
- Upload never fails due to processing errors
- Users can monitor task progress and errors

## Monitoring and Debugging

### View Worker Logs
```bash
docker-compose -f docker-compose.rabbitmq.yml logs -f celery-worker
```

### Check Queue Status
```bash
# Via API
curl http://localhost:8000/api/background-tasks/queue/length

# Via RabbitMQ Management UI
# http://localhost:15672
```

### Monitor Active Tasks
```bash
# Via API
curl http://localhost:8000/api/background-tasks/active

# Via Flower
# http://localhost:5555
```

This implementation ensures complete separation between upload and processing threads, providing a responsive user experience while maintaining robust background processing capabilities.
