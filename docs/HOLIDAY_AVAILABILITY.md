# Holiday Availability System

This document explains the holiday availability checking system that automatically handles admin unavailability during holidays in the SMS chatbot.

## Overview

The holiday availability system checks if the admin is available before processing SMS conversations. If the admin is unavailable due to holidays, it automatically sends an "out of reach" message and terminates the conversation.

## How It Works

### 1. Holiday Table Structure

The `holiday` table stores admin availability information:

```sql
CREATE TABLE holiday (
    id UUID PRIMARY KEY,
    holiday_type VARCHAR(20) CHECK (holiday_type IN ('PREDEFINED', 'PERSONAL')),
    date DATE NOT NULL,
    all_day BOOLEAN NOT NULL DEFAULT TRUE,
    start_time TIME,
    end_time TIME,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

### 2. Holiday Types

#### PREDEFINED Holidays
- **Purpose**: Company-wide holidays (Christmas, New Year, etc.)
- **Behavior**: Admin unavailable for the entire day
- **Example**: Christmas Day, Australia Day

#### PERSONAL Holidays
- **Purpose**: Individual admin time off
- **Behavior**: 
  - If `all_day = true`: Admin unavailable for entire day
  - If `all_day = false`: Admin unavailable during `start_time` to `end_time`

### 3. Availability Logic

```python
# Check availability flow:
1. Get current date/time
2. Query active holidays for current date
3. For each holiday:
   - PREDEFINED: Admin unavailable all day
   - PERSONAL + all_day=true: Admin unavailable all day  
   - PERSONAL + all_day=false: Check if current time is within start_time-end_time
4. If unavailable: Send out_of_reach message and terminate conversation
5. If available: Continue normal conversation flow
```

## Implementation

### 1. HolidayAvailabilityService

Located in `app/services/holiday_availability_service.py`:

```python
class HolidayAvailabilityService:
    async def check_admin_availability(self, check_datetime=None) -> Tuple[bool, Optional[str]]:
        """
        Returns:
            (is_available: bool, out_of_reach_message: Optional[str])
        """
```

### 2. Integration with Conversation Agent

The availability check is integrated into the conversation agent's `process_state` method:

```python
# Check admin availability first
availability_service = HolidayAvailabilityService(db)
is_available, out_of_reach_message = await availability_service.check_admin_availability()

if not is_available and out_of_reach_message:
    # Terminate conversation and send out_of_reach message
    session.is_completed = True
    session.completion_reason = "admin_unavailable"
    return out_of_reach_message
```

### 3. Out of Reach Message

The system retrieves the out of reach message from the `general_messages` table:

```sql
SELECT message FROM general_messages WHERE message_type = 'out_of_reach';
```

## Setup Instructions

### 1. Create Holiday Table

Run the holiday table creation script:
```bash
python scripts/simple_holiday_table_check.py
```

### 2. Setup Out of Reach Message

```sql
-- Run this SQL to setup the message:
INSERT INTO general_messages (message, message_type) 
VALUES (
    'Thank you for your interest in our franchise opportunities. Our team is currently unavailable due to holidays. We will get back to you as soon as possible. Have a great day!',
    'out_of_reach'
);
```

### 3. Add Holiday Data

```sql
-- Example: Add Christmas Day as unavailable
INSERT INTO holiday (holiday_type, date, all_day, description) 
VALUES ('PREDEFINED', '2024-12-25', true, 'Christmas Day');

-- Example: Add personal time off (half day)
INSERT INTO holiday (holiday_type, date, all_day, start_time, end_time, description) 
VALUES ('PERSONAL', '2024-07-15', false, '09:00:00', '13:00:00', 'Personal appointment');
```

## Testing

### 1. Test Holiday Availability Service

```bash
python scripts/test_holiday_availability.py
```

### 2. Test SMS Webhook with Holiday Check

```bash
python scripts/test_holiday_webhook.py
```

### 3. Manual Testing

1. Add a holiday for today's date:
```sql
INSERT INTO holiday (holiday_type, date, all_day, description)
VALUES ('PREDEFINED', CURRENT_DATE, true, 'Test Holiday');
```

2. Send an SMS via webhook and verify the out_of_reach message is returned

3. Remove the test holiday:
```sql
DELETE FROM holiday WHERE description = 'Test Holiday';
```

## API Endpoints

The holiday availability system integrates with existing endpoints:

- **SMS Webhook**: `POST /api/webhooks/webhooks/kudosity`
  - Automatically checks availability before processing
- **Holiday Management**: `GET/POST /api/holidays`
  - Manage holiday records
- **General Messages**: `GET/PUT /api/general-messages/out_of_reach`
  - Manage out of reach message

## Configuration Examples

### Australian Public Holidays (2024-2025)

```sql
INSERT INTO holiday (holiday_type, date, all_day, description) VALUES
('PREDEFINED', '2024-01-01', true, 'New Year''s Day'),
('PREDEFINED', '2024-01-26', true, 'Australia Day'),
('PREDEFINED', '2024-03-29', true, 'Good Friday'),
('PREDEFINED', '2024-04-01', true, 'Easter Monday'),
('PREDEFINED', '2024-04-25', true, 'ANZAC Day'),
('PREDEFINED', '2024-06-10', true, 'Queen''s Birthday'),
('PREDEFINED', '2024-12-25', true, 'Christmas Day'),
('PREDEFINED', '2024-12-26', true, 'Boxing Day'),
('PREDEFINED', '2025-01-01', true, 'New Year''s Day'),
('PREDEFINED', '2025-01-27', true, 'Australia Day');
```

### Personal Time Off Examples

```sql
-- Full day personal leave
INSERT INTO holiday (holiday_type, date, all_day, description)
VALUES ('PERSONAL', '2024-08-15', true, 'Personal Leave');

-- Half day appointment
INSERT INTO holiday (holiday_type, date, all_day, start_time, end_time, description)
VALUES ('PERSONAL', '2024-08-20', false, '14:00:00', '17:00:00', 'Medical Appointment');

-- Lunch break (if needed)
INSERT INTO holiday (holiday_type, date, all_day, start_time, end_time, description)
VALUES ('PERSONAL', '2024-08-21', false, '12:00:00', '13:00:00', 'Extended Lunch');
```

## Troubleshooting

### Common Issues

1. **Out of reach message not showing**
   - Check if `general_messages` table has `out_of_reach` record
   - Verify message_type is exactly 'out_of_reach'

2. **Availability check not working**
   - Ensure holiday records have `is_active = true` and `is_deleted = false`
   - Check date format matches current date

3. **Time-based holidays not working**
   - Verify `start_time` and `end_time` are set for `all_day = false` records
   - Check timezone consistency

### Debug Commands

```sql
-- Check current holidays
SELECT * FROM holiday 
WHERE date = CURRENT_DATE 
AND is_active = true 
AND is_deleted = false;

-- Check out_of_reach message
SELECT * FROM general_messages 
WHERE message_type = 'out_of_reach';

-- View conversation sessions terminated due to unavailability
SELECT * FROM conversation_sessions 
WHERE completion_reason = 'admin_unavailable'
ORDER BY completed_at DESC;
```

## Benefits

1. **Automatic Management**: No manual intervention needed during holidays
2. **Professional Response**: Customers receive polite unavailability messages
3. **Conversation Termination**: Prevents incomplete conversations during holidays
4. **Flexible Scheduling**: Supports both full-day and time-specific unavailability
5. **Easy Configuration**: Simple database records control availability
