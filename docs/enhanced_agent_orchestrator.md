# Enhanced AgentOrchestrator - Central Intelligence Layer

## Overview

The Enhanced AgentOrchestrator acts as a central intelligence layer that intercepts, filters, and routes all communication in the multi-agent system. It provides dynamic placeholder resolution, context management, user education, and a unified message pipeline.

## Key Features

### 1. Dynamic Placeholder Resolution

The orchestrator processes sales script entries and dynamically replaces placeholders with actual values from the database.

#### Supported Placeholders

- `{{franchisor_name}}` - Name of the franchisor from database
- `{{user_name}}` - User's name from conversation session/lead data
- `{{greeting}}` - Personalized greeting message
- `{{current_date}}` - Current date in readable format
- `{{current_time}}` - Current time in readable format
- `{{script_*}}` - Sales script content (e.g., `{{script_welcome_message}}`)
- Any context variable passed in the context dictionary

#### Example Usage

```python
# Original message with placeholders
message = "Hello {{user_name}}! Welcome to {{franchisor_name}}. Today is {{current_date}}."

# After processing
# "Hello <PERSON>! Welcome to <PERSON>'s Franchise. Today is December 16, 2024."
```

### 2. Context Management and User Education

The orchestrator validates user inputs based on conversation context and provides educational responses for invalid or out-of-context inputs.

#### Context Validators

- **Prequalification Stage**: Validates name, budget, and experience responses
- **Document Q&A Stage**: Ensures questions are franchise-related
- **Meeting Booking Stage**: Validates date/time format inputs
- **General Stage**: Filters inappropriate or spam content

#### Educational Response Types

- `invalid_prequalification` - Guides users on proper qualification responses
- `invalid_document_qa` - Redirects to franchise-related topics
- `invalid_meeting_format` - Helps with proper meeting request format
- `out_of_scope` - Explains system capabilities and limitations
- `unclear_response` - Requests clarification on ambiguous inputs

### 3. Unified Message Pipeline

Every message (user inputs and agent responses) flows through the orchestrator for processing.

#### Pipeline Stages

1. **Context Validation** (user inputs only) - Validates and educates if needed
2. **Dynamic Placeholder Resolution** - Replaces placeholders with actual values
3. **Content Personalization** - Adds user-specific personalization
4. **Final Sanitization** - Formats and cleans the message

## Implementation Details

### Core Methods

#### `_process_message_through_pipeline(message, context, is_user_input)`
Main pipeline method that processes all messages through the intelligence layer.

#### `_resolve_placeholders(message, context)`
Identifies and replaces placeholders with database values.

#### `_validate_and_educate_context(user_input, context)`
Validates user input and returns educational responses if needed.

#### `_get_placeholder_value(placeholder, context)`
Retrieves specific placeholder values from database or context.

### Database Integration

The orchestrator integrates with multiple database tables:

- `sales_scripts` - For script content and templates
- `franchisors` - For franchisor information
- `conversation_sessions` - For user session data
- `leads` - For user personal information

### Caching System

Placeholder values are cached per session to improve performance:

```python
cache_key = f"{placeholder}_{session_id}"
```

Cache can be cleared per session or globally:

```python
orchestrator.clear_placeholder_cache("session_id")  # Clear specific session
orchestrator.clear_placeholder_cache()              # Clear all cache
```

## Usage Examples

### Basic Message Processing

```python
orchestrator = AgentOrchestrator()

# Process user input
processed_message, should_continue = await orchestrator._process_message_through_pipeline(
    "Hello there!", 
    {"session_id": "user_123", "conversation_stage": "greeting"}, 
    is_user_input=True
)

# Process agent response
processed_response, _ = await orchestrator._process_message_through_pipeline(
    "Welcome {{user_name}} to {{franchisor_name}}!", 
    {"session_id": "user_123", "user_name": "John"}, 
    is_user_input=False
)
```

### Script Processing with Placeholders

```python
resolved_script = await orchestrator.process_script_with_placeholders(
    "Welcome Message",
    {
        "session_id": "user_123",
        "user_name": "John Smith",
        "franchisor_id": "franchise_uuid"
    }
)
```

### Context Validation

```python
is_valid, educational_message = await orchestrator._validate_and_educate_context(
    "123",  # Invalid name input
    {
        "conversation_stage": "prequalification",
        "current_question": "What's your name?"
    }
)

if not is_valid:
    # Return educational message to user
    return educational_message
```

## Integration with Existing System

The enhanced orchestrator seamlessly integrates with the existing conversation agent system:

1. **Router Enhancement**: User inputs are processed through the pipeline before intent classification
2. **Agent Response Processing**: All agent responses are processed for placeholder resolution
3. **Educational Responses**: Invalid inputs receive immediate educational feedback
4. **Unified Experience**: All communications maintain consistent formatting and personalization

## Configuration

### Educational Response Templates

Educational responses can be customized in the `_initialize_educational_responses()` method:

```python
self.educational_responses = {
    "invalid_prequalification": "I understand you'd like to share that information. Right now, I'm asking about {current_question}. Please provide a response that helps me understand your {question_context}.",
    # ... more templates
}
```

### Context Validators

Custom validators can be added for specific conversation stages:

```python
self.context_validators = {
    "custom_stage": self._validate_custom_context,
    # ... other validators
}
```

## Monitoring and Status

Get orchestrator status including central intelligence metrics:

```python
status = orchestrator.get_orchestrator_status()
print(f"Placeholder cache size: {status['central_intelligence']['placeholder_cache_size']}")
print(f"Available validators: {status['central_intelligence']['context_validators']}")
```

## Benefits

1. **Consistent User Experience**: All messages are processed uniformly
2. **Dynamic Personalization**: Real-time placeholder resolution from database
3. **Context-Aware Responses**: Validates inputs based on conversation stage
4. **User Education**: Guides users with helpful feedback for invalid inputs
5. **Centralized Control**: Single point for all communication processing
6. **Performance Optimization**: Caching system reduces database queries
7. **Extensible Design**: Easy to add new placeholders and validators

The Enhanced AgentOrchestrator transforms the multi-agent system into an intelligent, context-aware communication platform that provides personalized, educational, and consistent user interactions.
