# Short Link Reverse Proxy Setup

This document provides instructions for configuring reverse proxy servers to route `ghv.li/r/{slug}` requests to the GrowthHive backend.

## Overview

The short link service requires the domain `ghv.li` to be configured to route requests to the GrowthHive FastAPI backend. The routing pattern is:

- `ghv.li/r/{slug}` → `https://your-backend.com/api/v1/shortlinks/r/{slug}`

## Caddy Configuration

### Basic Caddyfile

```caddyfile
ghv.li {
    # Redirect all /r/* requests to the backend
    handle /r/* {
        reverse_proxy https://your-backend.com {
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # Handle root domain
    handle {
        respond "GrowthHive Short Link Service" 200
    }
    
    # Enable HTTPS
    tls <EMAIL>
}
```

### Advanced Caddyfile with Caching

```caddyfile
ghv.li {
    # Cache short link redirects for 1 hour
    handle /r/* {
        cache {
            ttl 1h
            static
        }
        
        reverse_proxy https://your-backend.com {
            header_up Host {upstream_hostport}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # Handle root domain
    handle {
        respond "GrowthHive Short Link Service" 200
    }
    
    # Enable HTTPS with Let's Encrypt
    tls <EMAIL>
}
```

## Nginx Configuration

### Basic Nginx Config

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name ghv.li;
    
    # SSL configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # Redirect short links to backend
    location /r/ {
        proxy_pass https://your-backend.com/api/v1/shortlinks/r/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle redirects properly
        proxy_intercept_errors on;
        error_page 302 = @handle_redirect;
    }
    
    # Handle redirects
    location @handle_redirect {
        return 302 $upstream_http_location;
    }
    
    # Handle root domain
    location / {
        return 200 "GrowthHive Short Link Service";
        add_header Content-Type text/plain;
    }
}
```

### Nginx with Caching

```nginx
# Cache zone for short links
proxy_cache_path /var/cache/nginx/shortlinks levels=1:2 keys_zone=shortlinks:10m max_size=1g inactive=1h;

server {
    listen 80;
    listen 443 ssl http2;
    server_name ghv.li;
    
    # SSL configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # Redirect short links to backend with caching
    location /r/ {
        proxy_cache shortlinks;
        proxy_cache_valid 200 302 1h;
        proxy_cache_valid 404 1m;
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        
        proxy_pass https://your-backend.com/api/v1/shortlinks/r/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle redirects properly
        proxy_intercept_errors on;
        error_page 302 = @handle_redirect;
    }
    
    # Handle redirects
    location @handle_redirect {
        return 302 $upstream_http_location;
    }
    
    # Handle root domain
    location / {
        return 200 "GrowthHive Short Link Service";
        add_header Content-Type text/plain;
    }
}
```

## Traefik Configuration

### Docker Compose with Traefik

```yaml
version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
    labels:
      - "traefik.enable=true"

  growthhive-backend:
    image: your-growthhive-backend:latest
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.growthhive.rule=Host(`your-backend.com`)"
      - "traefik.http.routers.growthhive.tls.certresolver=letsencrypt"
      - "traefik.http.services.growthhive.loadbalancer.server.port=8000"

  shortlink-proxy:
    image: nginx:alpine
    volumes:
      - ./nginx-shortlink.conf:/etc/nginx/conf.d/default.conf
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.shortlinks.rule=Host(`ghv.li`)"
      - "traefik.http.routers.shortlinks.tls.certresolver=letsencrypt"
      - "traefik.http.services.shortlinks.loadbalancer.server.port=80"
```

### Traefik Labels for Short Link Service

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.shortlinks.rule=Host(`ghv.li`)"
  - "traefik.http.routers.shortlinks.tls.certresolver=letsencrypt"
  - "traefik.http.routers.shortlinks.middlewares=shortlink-redirect"
  - "traefik.http.middlewares.shortlink-redirect.redirectregex.regex=^https://ghv.li/r/(.*)"
  - "traefik.http.middlewares.shortlink-redirect.redirectregex.replacement=https://your-backend.com/api/v1/shortlinks/r/$${1}"
  - "traefik.http.middlewares.shortlink-redirect.redirectregex.permanent=false"
```

## Cloudflare Configuration

### Page Rules

1. Go to Cloudflare Dashboard → Page Rules
2. Create a new page rule for `ghv.li/r/*`
3. Set the following settings:
   - **URL Pattern**: `ghv.li/r/*`
   - **Setting**: Forwarding URL
   - **Status Code**: 302 (Temporary Redirect)
   - **Destination URL**: `https://your-backend.com/api/v1/shortlinks/r/$1`

### Workers (Alternative)

```javascript
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // Handle short link redirects
  if (url.pathname.startsWith('/r/')) {
    const slug = url.pathname.substring(3) // Remove '/r/'
    const backendUrl = `https://your-backend.com/api/v1/shortlinks/r/${slug}`
    
    // Forward to backend
    const response = await fetch(backendUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    })
    
    return response
  }
  
  // Handle root domain
  return new Response('GrowthHive Short Link Service', {
    status: 200,
    headers: { 'Content-Type': 'text/plain' }
  })
}
```

## DNS Configuration

### A Record Setup

```
Type: A
Name: ghv.li
Value: YOUR_SERVER_IP
TTL: 300
```

### CNAME Setup (if using subdomain)

```
Type: CNAME
Name: ghv.li
Value: your-backend.com
TTL: 300
```

## Testing the Setup

### Test Short Link Creation

```bash
curl -X POST "https://your-backend.com/api/v1/shortlinks/create" \
  -H "Content-Type: application/json" \
  -d '{
    "long_url": "https://example.com",
    "context": {"type": "test"}
  }'
```

### Test Short Link Redirect

```bash
curl -I "https://ghv.li/r/your-slug"
```

Expected response:
```
HTTP/2 302
location: https://example.com
```

## Security Considerations

1. **Rate Limiting**: Implement rate limiting on the reverse proxy to prevent abuse
2. **CORS**: Configure CORS headers if needed for API access
3. **SSL/TLS**: Always use HTTPS for the short link domain
4. **Monitoring**: Set up monitoring for redirect performance and errors
5. **Logging**: Enable access logs to track short link usage

## Performance Optimization

1. **Caching**: Cache short link redirects at the reverse proxy level
2. **CDN**: Use a CDN for global distribution
3. **Database Indexing**: Ensure proper database indexes on the `slug` column
4. **Connection Pooling**: Configure database connection pooling for the backend

## Monitoring and Analytics

### Key Metrics to Track

- Short link creation rate
- Redirect success rate
- Average redirect latency
- Error rates (404, 500)
- Geographic distribution of clicks

### Log Analysis

```bash
# Count redirects per hour
grep "302" /var/log/nginx/access.log | awk '{print $4}' | cut -d: -f1-2 | sort | uniq -c

# Top short links by usage
grep "302" /var/log/nginx/access.log | awk '{print $7}' | sort | uniq -c | sort -nr | head -10
```

## Troubleshooting

### Common Issues

1. **404 Errors**: Check if the short link exists in the database
2. **SSL Errors**: Verify SSL certificate configuration
3. **Slow Redirects**: Check database performance and connection pooling
4. **CORS Issues**: Configure CORS headers if accessing from web applications

### Debug Commands

```bash
# Test backend connectivity
curl -I "https://your-backend.com/api/v1/shortlinks/r/test123"

# Check DNS resolution
nslookup ghv.li

# Test SSL certificate
openssl s_client -connect ghv.li:443 -servername ghv.li
```
