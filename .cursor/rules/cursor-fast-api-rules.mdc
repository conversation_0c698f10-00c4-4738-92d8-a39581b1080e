---
description: 
globs: 
alwaysApply: true
---
# ⛔ .cursor-rules – FastAPI Production Standards for Cursor AI

> Follow these strict, production-grade rules when making any modifications to this FastAPI project. Changes must follow project architecture, coding style, and reliability practices.
> 

---

## 👨‍💻 Developer Context

You are an expert in:

- Python 3.10+
- FastAPI
- Modular and scalable API design
- Performance-first architecture

---

## ✅ General Guidelines

- ❗ DO NOT remove or refactor existing functionality unless explicitly instructed.
- 🛠️ Changes should be **modular, isolated**, and **non-breaking**.
- ✅ Project must run without errors:
    
    ```bash
    uvicorn app.main:app --reload
    
    ```
    
- 🚫 No deeply nested logic, unused imports, or duplicate code.
- ✅ All code must have:
    - Type hints
    - Docstrings
    - Descriptive variable names
    - Pydantic-based input/output schemas

---

## 🧠 Key Principles

- ✅ Write concise, technical responses with correct Python examples.
- ✅ Use **functional**, **declarative** programming — avoid classes unless needed.
- ✅ Prefer iteration and modularization over duplication.
- ✅ Use **descriptive names** (e.g., `is_active`, `has_permission`).
- ✅ Use **Receive an Object, Return an Object (RORO)** pattern.
- ✅ Favor **named exports** for routes and utilities.

---

## 📦 Project Structure

Maintain this structure strictly:

```
project/
│
├── app/
│   ├── main.py               # Entry point
│   ├── api/                  # Routes
│   ├── models/               # SQLAlchemy models
│   ├── schemas/              # Pydantic v2 models
│   ├── services/             # Business logic
│   ├── db/                   # DB sessions and base
│   ├── core/                 # Settings, exceptions, lifespans
│   ├── middleware/           # Custom middlewares
│   └── utils/                # Helper utilities
│
├── tests/                    # All unit/integration tests
├── alembic/                  # Migrations
├── .env
├── requirements.txt
└── README.md

```

File/folder naming convention:

- Use lowercase with underscores: `user_service.py`, `auth_router.py`

---

## 🧰 Python/FastAPI Coding Rules

- ✅ Use `def` for pure or sync logic
- ✅ Use `async def` for async operations
- ✅ Use Pydantic v2 for schema validation
- ✅ Type all function signatures
- ✅ Avoid raw dictionaries for inputs — use `BaseModel`
- ✅ One-line syntax for simple conditions:
    
    ```python
    if user.is_admin: return user
    
    ```
    
- ❌ Avoid unnecessary curly braces
- ❌ No `else` after `return` – use guard clauses

---

## 🛡️ Error Handling and Validation

- ✅ Handle edge cases early
- ✅ Use `if-return` pattern
- ✅ Place the "happy path" last in each function
- ✅ Avoid deep nesting and repeated conditionals
- ✅ Use `raise HTTPException(status_code, detail=...)`
- ✅ Prefer custom exceptions or error factories for reusability
- ✅ Implement structured logging for unexpected errors

---

## 🔌 Dependency & Tech Stack

Use only these dependencies unless otherwise specified:

- `FastAPI`
- `Pydantic v2`
- Async DB libraries (`asyncpg`, `aiomysql`)
- `SQLAlchemy 2.0+`
- `alembic` (migrations)
- `httpx` (external APIs)
- `pytest` (testing)

---

## 🔍 FastAPI-Specific Best Practices

- ✅ Prefer functional components for routes
- ✅ Use `Depends()` for shared logic and auth
- ✅ Return typed responses with `response_model=...`
- ✅ Use lifespan context managers instead of:
    
    ```python
    @app.on_event("startup")
    
    ```
    
- ✅ Add global middleware for:
    - Logging
    - Exception handling
    - Performance
- ✅ Use `BaseModel` for all input/output schemas
- ✅ Optimize for performance with:
    - Async I/O
    - Lazy loading
    - Caching

---

## 🚀 Performance Optimization

- ✅ No blocking I/O — all DB/API calls must be async
- ✅ Use Redis/in-memory for caching repeated calls
- ✅ Use lazy loading for large datasets
- ✅ Minimize data serialization cost with `.model_dump()`
- ✅ Profile endpoints for latency, optimize routes

## 🧠 Cursor Must Remember:

- ⚠️ Do NOT change or remove any code without explicit instructions
- ✅ Apply changes in an isolated and minimal way
- ✅ Always follow this document’s rules
- ✅ Avoid editing multiple files unless required
- ✅ Confirm that the project builds and runs successfully at all times
- ✅ Suggest improvements only if the output remains error-free

---

📚 Refer to FastAPI docs for further clarity:
https://fastapi.tiangolo.com/

#!/bin/bash

python -m compileall .
ruff check app/
mypy app/
pyright app/
pytest

Run this commands whenever required.