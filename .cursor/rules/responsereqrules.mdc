---
description: 
globs: 
alwaysApply: true
---
# FastAPI Project Rules for Request/Response Format

## ✅ REQUEST MODEL RULES
- Every API endpoint **must** use a `pydantic.BaseModel` class for the request body.
- Request model class names **must** end with `Request` (e.g., `LoginRequest`, `UserCreateRequest`).
- All fields **must** use type hints and include examples where helpful.
- Use only primitive types: `str`, `int`, `float`, `bool`, `List`, `Dict`.
- **No raw dicts** or untyped parameters for request bodies.
- Add docstrings to all request models describing their purpose.
- Use Pydantic v2 features for validation and serialization.

## ✅ RESPONSE MODEL RULES
- Every endpoint **must** declare a `response_model` using a `pydantic.BaseModel` class.
- Response model class names **must** end with `Response` (e.g., `LoginResponse`, `UserInfoResponse`).
- All responses **must** be structured; never return raw dicts or models directly.
- Response fields **must** use clear types and descriptions.
- Add docstrings to all response models.
- Use `.model_dump()` for serialization when returning models.
- Use project-wide success/error response utilities for consistency.

## ✅ ENUM USAGE
- Use `Enum` (subclassed from `str`) for fields with limited fixed values.
- Enum values **must** be capitalized (e.g., `ADMIN`, `USER`).
- Example:
  ```python
  from enum import Enum
  class UserRole(str, Enum):
      """User roles for access control."""
      ADMIN = "ADMIN"
      USER = "USER"
  ```

## ✅ ASYNC & TYPING
- All endpoint functions **must** be `async def`.
- All functions **must** have complete type hints.
- Use `response_model=...` in all route decorators.

## ✅ DOCSTRINGS & NAMING
- Every model and endpoint **must** have a descriptive docstring.
- Use PascalCase for model and Enum class names.
- Use snake_case for all function and variable names.

## ✅ ERROR HANDLING
- Never raise `HTTPException` for business logic errors; use project error utilities.
- All validation and business errors **must** use structured error responses.
- No sensitive data in error messages or logs.

## ✅ EXAMPLES
- Provide field examples in Pydantic models where helpful for API docs.
- Example request/response models:
  ```python
  from pydantic import BaseModel, Field

  class LoginRequest(BaseModel):
      """Request model for user login."""
      username: str = Field(..., example="<EMAIL>")
      password: str = Field(..., example="strongpassword123")

  class LoginResponse(BaseModel):
      """Response model for successful login."""
      access_token: str = Field(..., description="JWT access token")
      token_type: str = Field(..., example="bearer")
  ```
