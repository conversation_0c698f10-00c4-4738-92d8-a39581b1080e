---
description: 
globs: 
alwaysApply: false
---
🚦 Project-wide API & Error Handling Rules
Standardized Error Responses
All errors must use the create_error_response utility from app/core/responses/utils.py, which returns a JSONResponse in your required format.
Never return raw models, custom classes, or raise HTTPException for business logic errors.
Consistent Success Responses
Use a standardized success response utility (e.g., create_success_response) for all successful operations.
Global Exception Handling
A global exception handler is in place to catch all unhandled exceptions and return a standardized error response.
Logging & Audit Trail
All requests and errors are logged with rich context for debugging and analysis.
No sensitive data is ever logged or returned.
Validation & Business Logic
All validation errors, duplicate entries, and business logic errors use specific error codes and descriptive messages.
No Direct Model Returns
Endpoints never return SQLAlchemy models or Pydantic models directly—always use response schemas or dicts.
Middleware
Request logging middleware is active for all endpoints.
Security
No sensitive information is exposed in logs or responses.
🛠️ Next Steps
I will:
Scan all endpoint, dependency, and service files for:
Any direct raise HTTPException, model returns, or non-standard error handling.
Any use of error utilities that do not return a JSONResponse.
Refactor all such occurrences to use the standardized utilities and response patterns.
Ensure all logging, validation, and error handling is consistent and production-grade.
This will be a comprehensive, project-wide refactor to enforce your standards everywhere.