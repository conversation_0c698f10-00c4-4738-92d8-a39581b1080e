"""create short link table

Revision ID: 20250911_182500
Revises: 20250910_220213
Create Date: 2025-09-11 18:25:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250911_182500'
down_revision = '20250910_220213'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create short_link table
    op.create_table('short_link',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('slug', sa.String(length=16), nullable=False),
        sa.Column('long_url', sa.Text(), nullable=False),
        sa.Column('context_json', postgresql.JSONB(astext_type=sa.Text()), nullable=False, server_default='{}'),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_short_link_id'), 'short_link', ['id'], unique=False)
    op.create_index(op.f('ix_short_link_slug'), 'short_link', ['slug'], unique=True)
    op.create_index(op.f('ix_short_link_expires_at'), 'short_link', ['expires_at'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index(op.f('ix_short_link_expires_at'), table_name='short_link')
    op.drop_index(op.f('ix_short_link_slug'), table_name='short_link')
    op.drop_index(op.f('ix_short_link_id'), table_name='short_link')
    
    # Drop table
    op.drop_table('short_link')
