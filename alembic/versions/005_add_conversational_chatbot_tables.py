"""Add conversational chatbot tables

Revision ID: 005_add_conversational_chatbot_tables
Revises: 004_add_document_processing_status
Create Date: 2025-07-16 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision: str = '005_add_conversational_chatbot_tables'
down_revision: Union[str, None] = '4f6afec85cf6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add conversational chatbot tables"""
    
    # Create sales_script table
    op.create_table(
        'sales_script',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.func.gen_random_uuid()),
        sa.Column('script_title', sa.String(100), nullable=False, unique=True, comment="Title/identifier for the script"),
        sa.Column('script_content', sa.Text(), nullable=False, comment="The actual message content to be sent"),
        sa.Column('script_stage', sa.String(50), nullable=False, comment="Stage in conversation flow"),
        sa.Column('order_sequence', sa.Integer(), nullable=False, default=1, comment="Order sequence for scripts within the same stage"),
        sa.Column('has_variables', sa.Boolean(), default=False, nullable=False, comment="Whether this script contains template variables"),
        sa.Column('variable_schema', sa.Text(), nullable=True, comment="JSON schema describing the variables used"),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False, comment="Whether this script is active"),
        sa.Column('is_deleted', sa.Boolean(), default=False, nullable=False, comment="Soft delete flag"),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, comment="Deletion timestamp"),
    )
    
    # Create indexes for sales_script
    op.create_index('ix_sales_script_script_title', 'sales_script', ['script_title'])
    op.create_index('ix_sales_script_script_stage', 'sales_script', ['script_stage'])
    op.create_index('ix_sales_script_is_active', 'sales_script', ['is_active'])
    op.create_index('ix_sales_script_is_deleted', 'sales_script', ['is_deleted'])
    
    # Create pre_qualification_questions table
    op.create_table(
        'pre_qualification_questions',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.func.gen_random_uuid()),
        sa.Column('question_text', sa.Text(), nullable=False, comment="The question text to be asked to the lead"),
        sa.Column('question_type', sa.String(50), nullable=False, comment="Type of question"),
        sa.Column('order_sequence', sa.Integer(), nullable=False, default=1, comment="Order in which questions should be asked"),
        sa.Column('qualification_weight', sa.Float(), nullable=False, default=1.0, comment="Weight of this question in qualification scoring"),
        sa.Column('passing_criteria', sa.Text(), nullable=True, comment="JSON criteria for what constitutes a qualifying answer"),
        sa.Column('expected_answer_type', sa.String(50), nullable=False, default='text', comment="Expected answer type"),
        sa.Column('validation_rules', sa.Text(), nullable=True, comment="JSON validation rules for the answer"),
        sa.Column('answer_options', sa.Text(), nullable=True, comment="JSON array of answer options for multiple choice"),
        sa.Column('requires_follow_up', sa.Boolean(), default=False, nullable=False, comment="Whether this question requires a follow-up"),
        sa.Column('follow_up_logic', sa.Text(), nullable=True, comment="JSON logic for follow-up questions"),
        sa.Column('is_required', sa.Boolean(), default=True, nullable=False, comment="Whether this question is required"),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False, comment="Whether this question is active"),
        sa.Column('is_deleted', sa.Boolean(), default=False, nullable=False, comment="Soft delete flag"),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, comment="Deletion timestamp"),
    )
    
    # Create indexes for pre_qualification_questions
    op.create_index('ix_pre_qualification_questions_question_type', 'pre_qualification_questions', ['question_type'])
    op.create_index('ix_pre_qualification_questions_is_active', 'pre_qualification_questions', ['is_active'])
    op.create_index('ix_pre_qualification_questions_is_deleted', 'pre_qualification_questions', ['is_deleted'])
    
    # Create conversation_sessions table
    op.create_table(
        'conversation_sessions',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.func.gen_random_uuid()),
        sa.Column('phone_number', sa.String(20), nullable=False, comment="Phone number of the lead/user"),
        sa.Column('session_id', sa.String(100), nullable=False, unique=True, comment="Unique session identifier"),
        sa.Column('lead_id', UUID(as_uuid=True), sa.ForeignKey('leads.id'), nullable=True, comment="Associated lead ID"),
        sa.Column('current_stage', sa.String(50), nullable=False, default='initial_greeting', comment="Current stage in conversation flow"),
        sa.Column('current_question_id', UUID(as_uuid=True), sa.ForeignKey('pre_qualification_questions.id'), nullable=True, comment="Current pre-qualification question"),
        sa.Column('questions_asked', sa.Integer(), nullable=False, default=0, comment="Number of questions asked"),
        sa.Column('questions_answered', sa.Integer(), nullable=False, default=0, comment="Number of questions answered"),
        sa.Column('qualification_score', sa.String(10), nullable=True, comment="Current qualification score as percentage"),
        sa.Column('is_qualified', sa.Boolean(), nullable=True, comment="Whether the lead is qualified"),
        sa.Column('conversation_context', sa.Text(), nullable=True, comment="JSON context data for the conversation"),
        sa.Column('last_message_sent', sa.Text(), nullable=True, comment="Last message sent by the bot"),
        sa.Column('last_message_received', sa.Text(), nullable=True, comment="Last message received from the user"),
        sa.Column('message_count', sa.Integer(), nullable=False, default=0, comment="Total number of messages exchanged"),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False, comment="Whether this session is currently active"),
        sa.Column('is_completed', sa.Boolean(), default=False, nullable=False, comment="Whether this conversation has been completed"),
        sa.Column('completion_reason', sa.String(100), nullable=True, comment="Reason for completion"),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), comment="When the conversation started"),
        sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now(), comment="Last activity timestamp"),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True, comment="When the conversation was completed"),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
    )
    
    # Create indexes for conversation_sessions
    op.create_index('ix_conversation_sessions_phone_number', 'conversation_sessions', ['phone_number'])
    op.create_index('ix_conversation_sessions_session_id', 'conversation_sessions', ['session_id'])
    op.create_index('ix_conversation_sessions_lead_id', 'conversation_sessions', ['lead_id'])
    op.create_index('ix_conversation_sessions_current_stage', 'conversation_sessions', ['current_stage'])
    op.create_index('ix_conversation_sessions_is_active', 'conversation_sessions', ['is_active'])


def downgrade() -> None:
    """Remove conversational chatbot tables"""
    
    # Drop conversation_sessions table and its indexes
    op.drop_index('ix_conversation_sessions_is_active', table_name='conversation_sessions')
    op.drop_index('ix_conversation_sessions_current_stage', table_name='conversation_sessions')
    op.drop_index('ix_conversation_sessions_lead_id', table_name='conversation_sessions')
    op.drop_index('ix_conversation_sessions_session_id', table_name='conversation_sessions')
    op.drop_index('ix_conversation_sessions_phone_number', table_name='conversation_sessions')
    op.drop_table('conversation_sessions')
    
    # Drop pre_qualification_questions table and its indexes
    op.drop_index('ix_pre_qualification_questions_is_deleted', table_name='pre_qualification_questions')
    op.drop_index('ix_pre_qualification_questions_is_active', table_name='pre_qualification_questions')
    op.drop_index('ix_pre_qualification_questions_question_type', table_name='pre_qualification_questions')
    op.drop_table('pre_qualification_questions')
    
    # Drop sales_script table and its indexes
    op.drop_index('ix_sales_script_is_deleted', table_name='sales_script')
    op.drop_index('ix_sales_script_is_active', table_name='sales_script')
    op.drop_index('ix_sales_script_script_stage', table_name='sales_script')
    op.drop_index('ix_sales_script_script_title', table_name='sales_script')
    op.drop_table('sales_script')
