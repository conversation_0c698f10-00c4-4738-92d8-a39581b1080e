"""Add escalation questions table for Andy AI

Revision ID: 006_escalation_questions
Revises: 005_add_conversational_chatbot_tables
Create Date: 2025-08-29 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006_escalation_questions'
down_revision = '005_add_conversational_chatbot_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add escalation questions table"""
    
    # Create escalation_questions table
    op.create_table(
        'escalation_questions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('question', sa.Text(), nullable=False),
        sa.Column('answer', sa.Text(), nullable=True),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('confidence_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('similarity_threshold', sa.Float(), nullable=False, default=0.8),
        sa.Column('status', sa.Enum('pending', 'in_review', 'answered', 'resolved', 'cancelled', name='escalationstatus'), nullable=False, default='pending'),
        sa.Column('clarification_count', sa.Integer(), nullable=False, default=0),
        sa.Column('max_clarifications', sa.Integer(), nullable=False, default=2),
        sa.Column('reviewed_by', sa.String(255), nullable=True),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('embedding', sa.Text(), nullable=True),
        sa.Column('embedding_model', sa.String(100), nullable=True, default='text-embedding-ada-002'),
        sa.Column('source_context', sa.Text(), nullable=True),
        sa.Column('conversation_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('priority', sa.String(20), nullable=False, default='medium'),
        sa.Column('category', sa.String(100), nullable=True),
        sa.Column('tags', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    )
    
    # Add foreign key constraint to leads table
    op.create_foreign_key(
        'fk_escalation_questions_lead_id',
        'escalation_questions',
        'leads',
        ['lead_id'],
        ['id'],
        ondelete='SET NULL'
    )
    
    # Add indexes for performance
    op.create_index('ix_escalation_questions_lead_id', 'escalation_questions', ['lead_id'])
    op.create_index('ix_escalation_questions_status', 'escalation_questions', ['status'])
    op.create_index('ix_escalation_questions_conversation_id', 'escalation_questions', ['conversation_id'])
    op.create_index('ix_escalation_questions_priority', 'escalation_questions', ['priority'])
    op.create_index('ix_escalation_questions_created_at', 'escalation_questions', ['created_at'])
    op.create_index('ix_escalation_questions_confidence_score', 'escalation_questions', ['confidence_score'])


def downgrade() -> None:
    """Remove escalation questions table"""
    
    # Drop indexes
    op.drop_index('ix_escalation_questions_confidence_score', 'escalation_questions')
    op.drop_index('ix_escalation_questions_created_at', 'escalation_questions')
    op.drop_index('ix_escalation_questions_priority', 'escalation_questions')
    op.drop_index('ix_escalation_questions_conversation_id', 'escalation_questions')
    op.drop_index('ix_escalation_questions_status', 'escalation_questions')
    op.drop_index('ix_escalation_questions_lead_id', 'escalation_questions')
    
    # Drop foreign key constraint
    op.drop_constraint('fk_escalation_questions_lead_id', 'escalation_questions', type_='foreignkey')
    
    # Drop table
    op.drop_table('escalation_questions')
    
    # Drop enum type
    op.execute('DROP TYPE IF EXISTS escalationstatus')
