"""Add follow-up tracking tables

Revision ID: followup_tracking_001
Revises: 
Create Date: 2025-08-25 19:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'followup_tracking_001'
down_revision = None  # Update this to the latest revision
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create follow-up tracking tables"""
    
    # Create scheduled_tasks table
    op.create_table(
        'scheduled_tasks',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('task_type', sa.String(length=50), nullable=False),
        sa.Column('celery_task_id', sa.String(length=255), nullable=True),
        sa.Column('idempotency_key', sa.String(length=64), nullable=False),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('scheduled_for', sa.DateTime(timezone=True), nullable=False),
        sa.Column('task_parameters', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False),
        sa.Column('max_retries', sa.Integer(), nullable=False),
        sa.Column('correlation_id', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('retry_count >= 0', name='ck_scheduled_tasks_retry_count_non_negative'),
        sa.CheckConstraint('max_retries >= 0', name='ck_scheduled_tasks_max_retries_non_negative'),
        sa.CheckConstraint('retry_count <= max_retries', name='ck_scheduled_tasks_retry_count_within_max'),
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('idempotency_key'),
        comment='Scheduled task tracking for follow-up messages'
    )
    
    # Create indexes for scheduled_tasks
    op.create_index('ix_scheduled_tasks_task_type', 'scheduled_tasks', ['task_type'])
    op.create_index('ix_scheduled_tasks_celery_task_id', 'scheduled_tasks', ['celery_task_id'])
    op.create_index('ix_scheduled_tasks_idempotency_key', 'scheduled_tasks', ['idempotency_key'])
    op.create_index('ix_scheduled_tasks_lead_id', 'scheduled_tasks', ['lead_id'])
    op.create_index('ix_scheduled_tasks_scheduled_for', 'scheduled_tasks', ['scheduled_for'])
    op.create_index('ix_scheduled_tasks_status', 'scheduled_tasks', ['status'])
    op.create_index('ix_scheduled_tasks_correlation_id', 'scheduled_tasks', ['correlation_id'])
    op.create_index('ix_scheduled_tasks_lead_status', 'scheduled_tasks', ['lead_id', 'status'])
    op.create_index('ix_scheduled_tasks_scheduled_status', 'scheduled_tasks', ['scheduled_for', 'status'])
    
    # Create followup_log table
    op.create_table(
        'followup_log',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('attempt_number', sa.Integer(), nullable=False),
        sa.Column('template_id', sa.String(length=100), nullable=False),
        sa.Column('message_text', sa.Text(), nullable=False),
        sa.Column('phone_number', sa.String(length=20), nullable=False),
        sa.Column('sms_provider_message_id', sa.String(length=255), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('scheduled_for', sa.DateTime(timezone=True), nullable=False),
        sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('lead_responded', sa.Boolean(), nullable=False),
        sa.Column('response_time_minutes', sa.Integer(), nullable=True),
        sa.Column('processing_time_ms', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('scheduled_task_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('attempt_number > 0', name='ck_followup_log_attempt_number_positive'),
        sa.CheckConstraint('response_time_minutes >= 0', name='ck_followup_log_response_time_non_negative'),
        sa.CheckConstraint('processing_time_ms >= 0', name='ck_followup_log_processing_time_non_negative'),
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
        sa.ForeignKeyConstraint(['scheduled_task_id'], ['scheduled_tasks.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('lead_id', 'attempt_number', 'scheduled_for', name='uq_followup_log_lead_attempt_scheduled'),
        comment='Follow-up message logging and history'
    )
    
    # Create indexes for followup_log
    op.create_index('ix_followup_log_lead_id', 'followup_log', ['lead_id'])
    op.create_index('ix_followup_log_phone_number', 'followup_log', ['phone_number'])
    op.create_index('ix_followup_log_status', 'followup_log', ['status'])
    op.create_index('ix_followup_log_sent_at', 'followup_log', ['sent_at'])
    op.create_index('ix_followup_log_lead_attempt', 'followup_log', ['lead_id', 'attempt_number'])
    op.create_index('ix_followup_log_phone_status', 'followup_log', ['phone_number', 'status'])
    
    # Create followup_metrics table
    op.create_table(
        'followup_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('total_followups_scheduled', sa.Integer(), nullable=False),
        sa.Column('total_followups_sent', sa.Integer(), nullable=False),
        sa.Column('total_followups_failed', sa.Integer(), nullable=False),
        sa.Column('total_responses', sa.Integer(), nullable=False),
        sa.Column('response_rate', sa.Float(), nullable=False),
        sa.Column('avg_processing_time_ms', sa.Float(), nullable=True),
        sa.Column('avg_response_time_minutes', sa.Float(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.CheckConstraint('total_followups_scheduled >= 0', name='ck_followup_metrics_scheduled_non_negative'),
        sa.CheckConstraint('total_followups_sent >= 0', name='ck_followup_metrics_sent_non_negative'),
        sa.CheckConstraint('total_followups_failed >= 0', name='ck_followup_metrics_failed_non_negative'),
        sa.CheckConstraint('total_responses >= 0', name='ck_followup_metrics_responses_non_negative'),
        sa.CheckConstraint('response_rate >= 0.0 AND response_rate <= 1.0', name='ck_followup_metrics_response_rate_valid'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('date', name='uq_followup_metrics_date'),
        comment='Follow-up performance metrics and analytics'
    )
    
    # Create indexes for followup_metrics
    op.create_index('ix_followup_metrics_date', 'followup_metrics', ['date'])
    
    # Set default values for new columns
    op.execute("UPDATE scheduled_tasks SET status = 'pending' WHERE status IS NULL")
    op.execute("UPDATE scheduled_tasks SET retry_count = 0 WHERE retry_count IS NULL")
    op.execute("UPDATE scheduled_tasks SET max_retries = 3 WHERE max_retries IS NULL")
    op.execute("UPDATE scheduled_tasks SET is_active = true WHERE is_active IS NULL")
    op.execute("UPDATE scheduled_tasks SET is_deleted = false WHERE is_deleted IS NULL")
    
    op.execute("UPDATE followup_log SET status = 'scheduled' WHERE status IS NULL")
    op.execute("UPDATE followup_log SET lead_responded = false WHERE lead_responded IS NULL")
    op.execute("UPDATE followup_log SET is_active = true WHERE is_active IS NULL")
    op.execute("UPDATE followup_log SET is_deleted = false WHERE is_deleted IS NULL")
    
    op.execute("UPDATE followup_metrics SET total_followups_scheduled = 0 WHERE total_followups_scheduled IS NULL")
    op.execute("UPDATE followup_metrics SET total_followups_sent = 0 WHERE total_followups_sent IS NULL")
    op.execute("UPDATE followup_metrics SET total_followups_failed = 0 WHERE total_followups_failed IS NULL")
    op.execute("UPDATE followup_metrics SET total_responses = 0 WHERE total_responses IS NULL")
    op.execute("UPDATE followup_metrics SET response_rate = 0.0 WHERE response_rate IS NULL")
    op.execute("UPDATE followup_metrics SET is_active = true WHERE is_active IS NULL")


def downgrade() -> None:
    """Drop follow-up tracking tables"""
    
    # Drop indexes first
    op.drop_index('ix_followup_metrics_date', table_name='followup_metrics')
    op.drop_index('ix_followup_log_phone_status', table_name='followup_log')
    op.drop_index('ix_followup_log_lead_attempt', table_name='followup_log')
    op.drop_index('ix_followup_log_sent_at', table_name='followup_log')
    op.drop_index('ix_followup_log_status', table_name='followup_log')
    op.drop_index('ix_followup_log_phone_number', table_name='followup_log')
    op.drop_index('ix_followup_log_lead_id', table_name='followup_log')
    op.drop_index('ix_scheduled_tasks_scheduled_status', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_lead_status', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_correlation_id', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_status', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_scheduled_for', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_lead_id', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_idempotency_key', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_celery_task_id', table_name='scheduled_tasks')
    op.drop_index('ix_scheduled_tasks_task_type', table_name='scheduled_tasks')
    
    # Drop tables
    op.drop_table('followup_metrics')
    op.drop_table('followup_log')
    op.drop_table('scheduled_tasks')
