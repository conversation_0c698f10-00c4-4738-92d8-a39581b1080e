"""Add embedding columns to franchisors table

Revision ID: add_franchisor_embedding
Revises: 
Create Date: 2025-09-10 20:40:00.000000

"""
from alembic import op
import sqlalchemy as sa
from pgvector.sqlalchemy import Vector

# revision identifiers, used by Alembic.
revision = 'add_franchisor_embedding'
down_revision = None  # Will be updated by Alembic
branch_labels = None
depends_on = None


def upgrade():
    """Add embeddings columns to franchisors table"""
    # Add embeddings column for vector storage
    op.add_column('franchisors', sa.Column('embeddings', Vector(1536), nullable=True, comment="OpenAI embedding vector for brochure content"))
    
    # Add embedding metadata columns
    op.add_column('franchisors', sa.Column('embedding_model', sa.String(100), nullable=True, default="text-embedding-3-small", comment="Model used to generate embedding"))
    op.add_column('franchisors', sa.Column('brochure_processed_at', sa.DateTime(timezone=True), nullable=True, comment="When brochure was last processed for embeddings"))


def downgrade():
    """Remove embeddings columns from franchisors table"""
    op.drop_column('franchisors', 'brochure_processed_at')
    op.drop_column('franchisors', 'embedding_model')
    op.drop_column('franchisors', 'embeddings')
