"""Add is_deleted column to users table

Revision ID: 46c979418266
Revises: abf1fbbddd24
Create Date: 2025-06-25 10:14:32.990054

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '46c979418266'
down_revision: Union[str, None] = 'abf1fbbddd24'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add is_deleted column to users table
    op.add_column('users', sa.Column('is_deleted', sa.<PERSON>an(), nullable=False, server_default=sa.text('false')))


def downgrade() -> None:
    # Remove is_deleted column from users table
    op.drop_column('users', 'is_deleted')
