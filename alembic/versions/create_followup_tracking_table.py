"""Create followup_tracking table

This migration creates the followup_tracking table for comprehensive
follow-up attempt tracking and audit trail.

Revision ID: create_followup_tracking
Revises: add_conversation_metadata
Create Date: 2024-01-01 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'create_followup_tracking'
down_revision = 'add_conversation_metadata'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create followup_tracking table"""
    
    # Create followup_tracking table
    op.create_table(
        'followup_tracking',
        sa.Column('id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('attempt_number', sa.Integer(), nullable=False),
        sa.Column('max_attempts', sa.Integer(), nullable=False),
        sa.Column('message_text', sa.Text(), nullable=False),
        sa.Column('phone_number', sa.String(length=20), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('scheduled_for', sa.DateTime(timezone=True), nullable=False),
        sa.Column('sent_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('lead_responded', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('response_time_minutes', sa.Integer(), nullable=True),
        sa.Column('sms_provider_message_id', sa.String(length=255), nullable=True),
        sa.Column('processing_time_ms', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('additional_metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('celery_task_id', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('true')),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('attempt_number > 0', name='ck_followup_tracking_attempt_number_positive'),
        sa.CheckConstraint('max_attempts >= 0', name='ck_followup_tracking_max_attempts_non_negative'),
        sa.CheckConstraint('response_time_minutes >= 0', name='ck_followup_tracking_response_time_non_negative'),
        sa.CheckConstraint('processing_time_ms >= 0', name='ck_followup_tracking_processing_time_non_negative'),
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('lead_id', 'attempt_number', name='uq_followup_tracking_lead_attempt'),
        comment='Follow-up tracking table for comprehensive follow-up management'
    )
    
    # Create indexes for efficient querying
    op.create_index('ix_followup_tracking_lead_id', 'followup_tracking', ['lead_id'])
    op.create_index('ix_followup_tracking_phone_number', 'followup_tracking', ['phone_number'])
    op.create_index('ix_followup_tracking_status', 'followup_tracking', ['status'])
    op.create_index('ix_followup_tracking_scheduled_for', 'followup_tracking', ['scheduled_for'])
    op.create_index('ix_followup_tracking_sent_at', 'followup_tracking', ['sent_at'])
    op.create_index('ix_followup_tracking_celery_task_id', 'followup_tracking', ['celery_task_id'])
    
    # Create composite indexes for common queries
    op.create_index('ix_followup_tracking_lead_status', 'followup_tracking', ['lead_id', 'status'])
    op.create_index('ix_followup_tracking_phone_status', 'followup_tracking', ['phone_number', 'status'])
    op.create_index('ix_followup_tracking_scheduled_status', 'followup_tracking', ['scheduled_for', 'status'])


def downgrade() -> None:
    """Drop followup_tracking table"""
    
    # Drop indexes first
    op.drop_index('ix_followup_tracking_scheduled_status', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_phone_status', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_lead_status', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_celery_task_id', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_sent_at', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_scheduled_for', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_status', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_phone_number', table_name='followup_tracking')
    op.drop_index('ix_followup_tracking_lead_id', table_name='followup_tracking')
    
    # Drop table
    op.drop_table('followup_tracking')
