"""Add Holiday and MessagingRule tables for General Settings

Revision ID: 002_add_holiday_and_messaging_rule_tables
Revises: abf1fbbddd24
Create Date: 2024-06-26 13:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = '002_add_holiday_and_messaging_rule_tables'
down_revision = 'abf1fbbddd24'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add Holiday and MessagingRule tables"""
    
    # Create holiday table
    op.create_table(
        'holiday',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('holiday_type', sa.String(20), nullable=False, comment='Type of holiday: PREDEFINED or PERSONAL'),
        sa.Column('date', sa.Date(), nullable=False, comment='Holiday date'),
        sa.Column('all_day', sa.<PERSON>(), nullable=False, default=True, comment='Whether holiday is all day'),
        sa.Column('start_time', sa.Time(), nullable=True, comment='Start time if not all day'),
        sa.Column('end_time', sa.Time(), nullable=True, comment='End time if not all day'),
        sa.Column('description', sa.Text(), nullable=True, comment='Holiday description'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, comment='Whether holiday is active'),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False, comment='Soft delete flag'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), comment='Creation timestamp'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, comment='Deletion timestamp'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now(), comment='Last update timestamp'),
        
        # Add constraints
        sa.CheckConstraint("holiday_type IN ('PREDEFINED', 'PERSONAL')", name='ck_holiday_type'),
        sa.CheckConstraint("all_day = true OR (start_time IS NOT NULL AND end_time IS NOT NULL)", name='ck_holiday_time_constraint'),
    )
    
    # Create messaging_rule table
    op.create_table(
        'messaging_rule',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('lead_init_delay_h', sa.Integer(), nullable=False, comment='Initial delay in hours before first message to lead'),
        sa.Column('no_response_delay_h', sa.Integer(), nullable=False, comment='Delay in hours between follow-up messages when no response'),
        sa.Column('max_followups', sa.Integer(), nullable=False, comment='Maximum number of follow-up messages allowed'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, comment='Whether this messaging rule is active'),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False, comment='Soft delete flag'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), comment='Creation timestamp'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True, comment='Deletion timestamp'),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now(), comment='Last update timestamp'),
        
        # Add constraints
        sa.CheckConstraint('lead_init_delay_h >= 0', name='ck_lead_init_delay_non_negative'),
        sa.CheckConstraint('no_response_delay_h > 0', name='ck_no_response_delay_positive'),
        sa.CheckConstraint('max_followups >= 0', name='ck_max_followups_non_negative'),
    )
    
    # Create indexes for better performance
    op.create_index('ix_holiday_date', 'holiday', ['date'])
    op.create_index('ix_holiday_type', 'holiday', ['holiday_type'])
    op.create_index('ix_holiday_is_active', 'holiday', ['is_active'])
    op.create_index('ix_holiday_is_deleted', 'holiday', ['is_deleted'])
    
    op.create_index('ix_messaging_rule_is_active', 'messaging_rule', ['is_active'])
    op.create_index('ix_messaging_rule_is_deleted', 'messaging_rule', ['is_deleted'])


def downgrade() -> None:
    """Drop Holiday and MessagingRule tables"""
    
    # Drop indexes
    op.drop_index('ix_messaging_rule_is_deleted', 'messaging_rule')
    op.drop_index('ix_messaging_rule_is_active', 'messaging_rule')
    op.drop_index('ix_holiday_is_deleted', 'holiday')
    op.drop_index('ix_holiday_is_active', 'holiday')
    op.drop_index('ix_holiday_type', 'holiday')
    op.drop_index('ix_holiday_date', 'holiday')
    
    # Drop tables
    op.drop_table('messaging_rule')
    op.drop_table('holiday')
