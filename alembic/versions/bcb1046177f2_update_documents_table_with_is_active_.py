"""Update documents table with is_active, is_deleted, deleted_at fields

Revision ID: bcb1046177f2
Revises: 4f6afec85cf6
Create Date: 2025-06-27 15:58:54.727602

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bcb1046177f2'
down_revision: Union[str, None] = '4f6afec85cf6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_messaging_rule_is_active', table_name='messaging_rule')
    op.drop_index('ix_messaging_rule_is_deleted', table_name='messaging_rule')
    op.drop_table('messaging_rule')
    op.drop_index('ix_holiday_date', table_name='holiday')
    op.drop_index('ix_holiday_is_active', table_name='holiday')
    op.drop_index('ix_holiday_is_deleted', table_name='holiday')
    op.drop_index('ix_holiday_type', table_name='holiday')
    op.drop_table('holiday')
    op.add_column('documents', sa.Column('is_active', sa.Boolean(), nullable=False))
    op.add_column('documents', sa.Column('is_deleted', sa.Boolean(), nullable=False))
    op.add_column('documents', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f('ix_documents_franchisor_id'), 'documents', ['franchisor_id'], unique=False)
    op.create_index(op.f('ix_documents_id'), 'documents', ['id'], unique=False)
    op.create_index(op.f('ix_documents_is_active'), 'documents', ['is_active'], unique=False)
    op.create_index(op.f('ix_documents_is_deleted'), 'documents', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_documents_name'), 'documents', ['name'], unique=False)
    op.create_index(op.f('ix_documents_user_id'), 'documents', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_documents_user_id'), table_name='documents')
    op.drop_index(op.f('ix_documents_name'), table_name='documents')
    op.drop_index(op.f('ix_documents_is_deleted'), table_name='documents')
    op.drop_index(op.f('ix_documents_is_active'), table_name='documents')
    op.drop_index(op.f('ix_documents_id'), table_name='documents')
    op.drop_index(op.f('ix_documents_franchisor_id'), table_name='documents')
    op.drop_column('documents', 'deleted_at')
    op.drop_column('documents', 'is_deleted')
    op.drop_column('documents', 'is_active')
    op.create_table('holiday',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False),
    sa.Column('holiday_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='Type of holiday: PREDEFINED or PERSONAL'),
    sa.Column('date', sa.DATE(), autoincrement=False, nullable=False, comment='Holiday date'),
    sa.Column('all_day', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='Whether holiday is all day'),
    sa.Column('start_time', postgresql.TIME(), autoincrement=False, nullable=True, comment='Start time if not all day'),
    sa.Column('end_time', postgresql.TIME(), autoincrement=False, nullable=True, comment='End time if not all day'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='Holiday description'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='Whether holiday is active'),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='Soft delete flag'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False, comment='Creation timestamp'),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='Deletion timestamp'),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False, comment='Last update timestamp'),
    sa.CheckConstraint("holiday_type::text = ANY (ARRAY['PREDEFINED'::character varying, 'PERSONAL'::character varying]::text[])", name='ck_holiday_type'),
    sa.CheckConstraint('all_day = true OR start_time IS NOT NULL AND end_time IS NOT NULL', name='ck_holiday_time_constraint'),
    sa.PrimaryKeyConstraint('id', name='holiday_pkey')
    )
    op.create_index('ix_holiday_type', 'holiday', ['holiday_type'], unique=False)
    op.create_index('ix_holiday_is_deleted', 'holiday', ['is_deleted'], unique=False)
    op.create_index('ix_holiday_is_active', 'holiday', ['is_active'], unique=False)
    op.create_index('ix_holiday_date', 'holiday', ['date'], unique=False)
    op.create_table('messaging_rule',
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), autoincrement=False, nullable=False),
    sa.Column('lead_init_delay_h', sa.INTEGER(), autoincrement=False, nullable=False, comment='Initial delay in hours before first message to lead'),
    sa.Column('no_response_delay_h', sa.INTEGER(), autoincrement=False, nullable=False, comment='Delay in hours between follow-up messages when no response'),
    sa.Column('max_followups', sa.INTEGER(), autoincrement=False, nullable=False, comment='Maximum number of follow-up messages allowed'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='Whether this messaging rule is active'),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='Soft delete flag'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False, comment='Creation timestamp'),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True, comment='Deletion timestamp'),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False, comment='Last update timestamp'),
    sa.CheckConstraint('lead_init_delay_h >= 0', name='ck_lead_init_delay_non_negative'),
    sa.CheckConstraint('max_followups >= 0', name='ck_max_followups_non_negative'),
    sa.CheckConstraint('no_response_delay_h > 0', name='ck_no_response_delay_positive'),
    sa.PrimaryKeyConstraint('id', name='messaging_rule_pkey')
    )
    op.create_index('ix_messaging_rule_is_deleted', 'messaging_rule', ['is_deleted'], unique=False)
    op.create_index('ix_messaging_rule_is_active', 'messaging_rule', ['is_active'], unique=False)
    # ### end Alembic commands ###
