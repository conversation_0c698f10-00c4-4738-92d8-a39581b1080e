"""Remove legacy category and sub_category fields from franchisors

Revision ID: abf1fbbddd24
Revises: e66ca63f92dd
Create Date: 2025-06-24 18:50:23.022174

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'abf1fbbddd24'
down_revision: Union[str, None] = 'e66ca63f92dd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_franchisors_category'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_sub_category'), table_name='franchisors')
    op.drop_column('franchisors', 'category')
    op.drop_column('franchisors', 'sub_category')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('franchisors', sa.Column('sub_category', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('franchisors', sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.create_index(op.f('ix_franchisors_sub_category'), 'franchisors', ['sub_category'], unique=False)
    op.create_index(op.f('ix_franchisors_category'), 'franchisors', ['category'], unique=False)
    # ### end Alembic commands ###
