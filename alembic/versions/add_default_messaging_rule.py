"""Add default messaging rule for follow-up system

This migration creates a default messaging rule to enable the follow-up system.
The rule configures 3 follow-up attempts with 1 hour initial delay and 24 hour intervals.

Revision ID: add_default_messaging_rule
Revises: create_followup_tracking
Create Date: 2024-01-01 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_default_messaging_rule'
down_revision = 'create_followup_tracking'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add default messaging rule"""
    
    # Insert default messaging rule
    op.execute("""
        INSERT INTO messaging_rule (
            id,
            is_active,
            is_deleted,
            max_followups,
            lead_init_delay_m,
            no_response_delay_m,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            true,
            false,
            3,
            60,
            1440,
            NOW(),
            NOW()
        )
        ON CONFLICT DO NOTHING;
    """)


def downgrade() -> None:
    """Remove default messaging rule"""
    
    # Remove the default messaging rule
    op.execute("""
        DELETE FROM messaging_rule 
        WHERE max_followups = 3 
          AND lead_init_delay_m = 60 
          AND no_response_delay_m = 1440
          AND is_active = true;
    """)
