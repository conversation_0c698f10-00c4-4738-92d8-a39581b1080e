"""Create all tables with UUID primary keys

Revision ID: 001_create_all_tables_with_uuid
Revises:
Create Date: 2024-06-24 17:35:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = '001_create_all_tables_with_uuid'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create all tables with UUID primary keys"""

    # Enable UUID extension
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')

    # Create users table
    op.create_table(
        'users',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('email', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('mobile', sa.String(20), nullable=True, unique=True, index=True),
        sa.Column('password_hash', sa.String(255), nullable=False),
        sa.Column('password_reset_token', sa.String(255), nullable=True),
        sa.Column('password_reset_expires', sa.DateTime(timezone=True), nullable=True),
        sa.Column('first_name', sa.String(100), nullable=True),
        sa.Column('last_name', sa.String(100), nullable=True),
        sa.Column('role', sa.String(50), nullable=False, default='USER'),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('is_email_verified', sa.Boolean(), default=False, nullable=False),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('remember_me_token', sa.String(255), nullable=True),
        sa.Column('remember_me_token_expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )

    # Create user_roles table
    op.create_table(
        'user_roles',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('user_id', UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True),
        sa.Column('role', sa.String(20), nullable=False, default='user'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )

    # Create category table
    op.create_table(
        'category',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False, index=True),
        sa.Column('name', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('description', sa.String(255), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('is_deleted', sa.Boolean(), default=False, nullable=False),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )

    # Create subcategory table
    op.create_table(
        'subcategory',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False, index=True),
        sa.Column('name', sa.String(100), nullable=False, index=True),
        sa.Column('description', sa.String(255), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('is_deleted', sa.Boolean(), default=False, nullable=False),
        sa.Column('category_id', UUID(as_uuid=True), sa.ForeignKey('category.id', ondelete='CASCADE'), nullable=False, index=True),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )

    # Create franchisors table
    op.create_table(
        'franchisors',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False, index=True),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('category_id', UUID(as_uuid=True), sa.ForeignKey('category.id', ondelete='SET NULL'), nullable=True, index=True),
        sa.Column('subcategory_id', UUID(as_uuid=True), sa.ForeignKey('subcategory.id', ondelete='SET NULL'), nullable=True, index=True),
        sa.Column('category', sa.String(100), nullable=True, index=True),  # Legacy field
        sa.Column('sub_category', sa.String(100), nullable=True, index=True),  # Legacy field
        sa.Column('region', sa.String(100), nullable=True, index=True),
        sa.Column('budget', sa.Float(), nullable=True),
        sa.Column('brochure_url', sa.String(500), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False, index=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )

    # Create leads table
    op.create_table(
        'leads',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False, index=True),
        sa.Column('name', sa.String(100), nullable=False, index=True),
        sa.Column('email', sa.String(100), nullable=False, index=True),
        sa.Column('phone', sa.String(20), nullable=True, index=True),
        sa.Column('location', sa.String(100), nullable=True, index=True),
        sa.Column('budget', sa.Float(), nullable=True),
        sa.Column('category', sa.String(100), nullable=True, index=True),
        sa.Column('sub_category', sa.String(100), nullable=True, index=True),
        sa.Column('status', sa.String(20), nullable=False, default='new', index=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )


def downgrade() -> None:
    """Drop all tables"""

    # Drop tables in reverse order to handle foreign key constraints
    op.drop_table('leads')
    op.drop_table('franchisors')
    op.drop_table('subcategory')
    op.drop_table('category')
    op.drop_table('user_roles')
    op.drop_table('users')

    # Drop UUID extension
    op.execute('DROP EXTENSION IF EXISTS "uuid-ossp"')
