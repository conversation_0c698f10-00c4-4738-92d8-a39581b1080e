"""Add document processing status fields

Revision ID: 004_add_document_processing_status
Revises: 003_add_pgvector_support_for_docqa
Create Date: 2025-07-09 11:20:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '004_add_document_processing_status'
down_revision: Union[str, None] = 'bcb1046177f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add processing status fields to documents table"""
    
    # Add processing status fields
    op.add_column('documents', sa.Column('processing_status', sa.String(20), nullable=False, server_default='pending'))
    op.add_column('documents', sa.Column('processing_progress', sa.Integer(), nullable=True))
    op.add_column('documents', sa.Column('processing_message', sa.Text(), nullable=True))
    op.add_column('documents', sa.Column('processing_error', sa.Text(), nullable=True))
    op.add_column('documents', sa.Column('processing_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('documents', sa.Column('processing_completed_at', sa.DateTime(timezone=True), nullable=True))
    
    # Create index on processing_status for efficient queries
    op.create_index('ix_documents_processing_status', 'documents', ['processing_status'])


def downgrade() -> None:
    """Remove processing status fields from documents table"""
    
    # Drop index
    op.drop_index('ix_documents_processing_status', table_name='documents')
    
    # Drop processing status fields
    op.drop_column('documents', 'processing_completed_at')
    op.drop_column('documents', 'processing_started_at')
    op.drop_column('documents', 'processing_error')
    op.drop_column('documents', 'processing_message')
    op.drop_column('documents', 'processing_progress')
    op.drop_column('documents', 'processing_status')
