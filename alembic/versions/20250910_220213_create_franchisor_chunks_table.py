"""Create franchisor_chunks table for franchisor-scoped RAG

Revision ID: 20250910_220213
Revises: add_franchisor_embedding_columns
Create Date: 2025-09-10 22:02:13.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250910_220213'
down_revision = 'add_franchisor_embedding_columns'
branch_labels = None
depends_on = None


def upgrade():
    """Create franchisor_chunks table for franchisor-scoped RAG system"""
    
    # Ensure pgvector extension is enabled
    op.execute('CREATE EXTENSION IF NOT EXISTS vector;')
    
    # Create franchisor_chunks table
    op.create_table(
        'franchisor_chunks',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('franchisor_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('source_url', sa.Text(), nullable=True),
        sa.Column('source_title', sa.Text(), nullable=True),
        sa.Column('page_no', sa.Integer(), nullable=True),
        sa.Column('section', sa.Text(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), nullable=False, server_default=sa.text("'{}'::jsonb")),
        sa.Column('embedding', sa.Text().with_variant(postgresql.base.ischema_names['vector'](1536), 'postgresql'), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.ForeignKeyConstraint(['franchisor_id'], ['franchisors.id'], ondelete='CASCADE'),
    )
    
    # Create indexes for performance
    # Exact search helper
    op.create_index(
        'ix_franchisor_chunks_franchisor',
        'franchisor_chunks',
        ['franchisor_id']
    )
    
    # ANN index using ivfflat for vector similarity search
    op.execute("""
        CREATE INDEX IF NOT EXISTS ivf_franchisor_chunks_embedding
        ON franchisor_chunks
        USING ivfflat (embedding vector_cosine_ops)
        WITH (lists = 100);
    """)
    
    # Additional performance indexes
    op.create_index(
        'ix_franchisor_chunks_created_at',
        'franchisor_chunks',
        ['created_at']
    )
    
    op.create_index(
        'ix_franchisor_chunks_source_url',
        'franchisor_chunks',
        ['source_url']
    )
    
    # Create trigger for updating updated_at timestamp
    op.execute("""
        CREATE OR REPLACE FUNCTION update_franchisor_chunks_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    op.execute("""
        CREATE TRIGGER update_franchisor_chunks_updated_at_trigger
        BEFORE UPDATE ON franchisor_chunks
        FOR EACH ROW
        EXECUTE FUNCTION update_franchisor_chunks_updated_at();
    """)
    
    # Analyze table for optimal query planning
    op.execute('ANALYZE franchisor_chunks;')


def downgrade():
    """Drop franchisor_chunks table and related objects"""
    
    # Drop trigger and function
    op.execute('DROP TRIGGER IF EXISTS update_franchisor_chunks_updated_at_trigger ON franchisor_chunks;')
    op.execute('DROP FUNCTION IF EXISTS update_franchisor_chunks_updated_at();')
    
    # Drop indexes (some will be dropped automatically with table)
    op.execute('DROP INDEX IF EXISTS ivf_franchisor_chunks_embedding;')
    op.drop_index('ix_franchisor_chunks_source_url', table_name='franchisor_chunks')
    op.drop_index('ix_franchisor_chunks_created_at', table_name='franchisor_chunks')
    op.drop_index('ix_franchisor_chunks_franchisor', table_name='franchisor_chunks')
    
    # Drop table
    op.drop_table('franchisor_chunks')
