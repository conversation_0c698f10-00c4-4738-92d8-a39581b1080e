"""add_lead_status_and_history_tables

Revision ID: 005_add_lead_status_and_history_tables
Revises: 004_add_document_processing_status
Create Date: 2025-08-22 13:55:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision: str = '006_add_lead_status_and_history_tables'
down_revision: Union[str, None] = 'f752351b0351'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add lead_statuses and lead_status_history tables"""
    
    # Create lead_statuses table
    op.create_table(
        'lead_statuses',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('name', sa.String(100), nullable=False, unique=True, index=True),
        sa.Column('colour', sa.String(7), nullable=False, comment='Hex color code'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, index=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False, index=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
    )
    
    # Create indexes for lead_statuses
    op.create_index('ix_lead_statuses_id', 'lead_statuses', ['id'])
    op.create_index('ix_lead_statuses_name', 'lead_statuses', ['name'])
    op.create_index('ix_lead_statuses_is_active', 'lead_statuses', ['is_active'])
    op.create_index('ix_lead_statuses_is_deleted', 'lead_statuses', ['is_deleted'])
    
    # Create lead_status_history table
    op.create_table(
        'lead_status_history',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('lead_id', UUID(as_uuid=True), nullable=False, index=True),
        sa.Column('from_status_id', UUID(as_uuid=True), nullable=True, index=True, comment='Nullable for initial status'),
        sa.Column('to_status_id', UUID(as_uuid=True), nullable=False, index=True),
        sa.Column('reason', sa.String(255), nullable=True, comment='Reason for status change'),
        sa.Column('confidence', sa.Float(), nullable=True, comment='AI confidence score (0.0 to 1.0)'),
        sa.Column('rationale', sa.Text(), nullable=True, comment='AI reasoning for the change'),
        sa.Column('message_excerpt', sa.Text(), nullable=True, comment='Excerpt from the message that triggered the change'),
        sa.Column('changed_by', sa.String(100), nullable=False, default='system', comment='Who/what made the change'),
        sa.Column('source', sa.String(50), nullable=False, default='andy', comment='Source of the change (andy, manual, etc.)'),
        sa.Column('review_needed', sa.Boolean(), nullable=False, default=False, index=True, comment='Flag for low confidence changes'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True, index=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False, index=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        
        # Foreign key constraints
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], name='fk_lead_status_history_lead_id'),
        sa.ForeignKeyConstraint(['from_status_id'], ['lead_statuses.id'], name='fk_lead_status_history_from_status_id'),
        sa.ForeignKeyConstraint(['to_status_id'], ['lead_statuses.id'], name='fk_lead_status_history_to_status_id'),
    )
    
    # Create indexes for lead_status_history
    op.create_index('ix_lead_status_history_id', 'lead_status_history', ['id'])
    op.create_index('ix_lead_status_history_lead_id', 'lead_status_history', ['lead_id'])
    op.create_index('ix_lead_status_history_from_status_id', 'lead_status_history', ['from_status_id'])
    op.create_index('ix_lead_status_history_to_status_id', 'lead_status_history', ['to_status_id'])
    op.create_index('ix_lead_status_history_review_needed', 'lead_status_history', ['review_needed'])
    op.create_index('ix_lead_status_history_is_active', 'lead_status_history', ['is_active'])
    op.create_index('ix_lead_status_history_is_deleted', 'lead_status_history', ['is_deleted'])
    
    # Add lead_status_id column to leads table if it doesn't exist
    # First check if the column exists by trying to add it
    try:
        op.add_column('leads', sa.Column('lead_status_id', UUID(as_uuid=True), nullable=True, index=True))
        op.create_foreign_key('fk_leads_lead_status_id', 'leads', 'lead_statuses', ['lead_status_id'], ['id'])
        op.create_index('ix_leads_lead_status_id', 'leads', ['lead_status_id'])
    except Exception:
        # Column might already exist, ignore the error
        pass


def downgrade() -> None:
    """Remove lead_statuses and lead_status_history tables"""
    
    # Drop foreign key and column from leads table
    try:
        op.drop_constraint('fk_leads_lead_status_id', 'leads', type_='foreignkey')
        op.drop_index('ix_leads_lead_status_id', table_name='leads')
        op.drop_column('leads', 'lead_status_id')
    except Exception:
        # Column might not exist, ignore the error
        pass
    
    # Drop lead_status_history table and its indexes
    op.drop_index('ix_lead_status_history_is_deleted', table_name='lead_status_history')
    op.drop_index('ix_lead_status_history_is_active', table_name='lead_status_history')
    op.drop_index('ix_lead_status_history_review_needed', table_name='lead_status_history')
    op.drop_index('ix_lead_status_history_to_status_id', table_name='lead_status_history')
    op.drop_index('ix_lead_status_history_from_status_id', table_name='lead_status_history')
    op.drop_index('ix_lead_status_history_lead_id', table_name='lead_status_history')
    op.drop_index('ix_lead_status_history_id', table_name='lead_status_history')
    op.drop_table('lead_status_history')
    
    # Drop lead_statuses table and its indexes
    op.drop_index('ix_lead_statuses_is_deleted', table_name='lead_statuses')
    op.drop_index('ix_lead_statuses_is_active', table_name='lead_statuses')
    op.drop_index('ix_lead_statuses_name', table_name='lead_statuses')
    op.drop_index('ix_lead_statuses_id', table_name='lead_statuses')
    op.drop_table('lead_statuses')
