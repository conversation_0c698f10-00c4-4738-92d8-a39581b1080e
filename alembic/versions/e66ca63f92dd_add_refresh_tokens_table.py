"""Add refresh_tokens table

Revision ID: e66ca63f92dd
Revises: 001_create_all_tables_with_uuid
Create Date: 2025-06-24 18:28:13.711230

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e66ca63f92dd'
down_revision: Union[str, None] = '001_create_all_tables_with_uuid'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('questions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('question_text', sa.Text(), nullable=False),
    sa.Column('question_type', sa.String(length=50), nullable=True),
    sa.Column('is_required', sa.<PERSON>(), nullable=True),
    sa.Column('order_sequence', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('system_settings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('setting_key', sa.String(length=100), nullable=False),
    sa.Column('setting_value', sa.Text(), nullable=True),
    sa.Column('setting_type', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('setting_key')
    )
    op.create_table('lead_responses',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('lead_id', sa.UUID(), nullable=True),
    sa.Column('question_id', sa.UUID(), nullable=True),
    sa.Column('response_text', sa.Text(), nullable=True),
    sa.Column('answered_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('refresh_tokens',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('token', sa.String(length=1000), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token')
    )
    op.create_table('sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('user_agent', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_activity', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sessions_id'), 'sessions', ['id'], unique=False)
    op.create_table('documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_type', sa.String(length=50), nullable=True),
    sa.Column('file_size', sa.String(length=20), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('franchisor_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['franchisor_id'], ['franchisors.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_index(op.f('ix_user_roles_user_id'), table_name='user_roles')
    op.drop_table('user_roles')
    op.add_column('leads', sa.Column('zoho_lead_id', sa.String(length=100), nullable=True))
    op.add_column('leads', sa.Column('full_name', sa.String(length=255), nullable=False))
    op.add_column('leads', sa.Column('contact_number', sa.String(length=20), nullable=False))
    op.add_column('leads', sa.Column('lead_source', sa.String(length=100), nullable=True))
    op.add_column('leads', sa.Column('franchise_preference', sa.String(length=255), nullable=True))
    op.add_column('leads', sa.Column('budget_preference', sa.Numeric(precision=12, scale=2), nullable=True))
    op.add_column('leads', sa.Column('qualification_status', sa.String(length=50), nullable=True))
    op.alter_column('leads', 'email',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=255),
               nullable=True)
    op.alter_column('leads', 'location',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=255),
               existing_nullable=True)
    op.drop_index(op.f('ix_leads_category'), table_name='leads')
    op.drop_index(op.f('ix_leads_email'), table_name='leads')
    op.drop_index(op.f('ix_leads_id'), table_name='leads')
    op.drop_index(op.f('ix_leads_location'), table_name='leads')
    op.drop_index(op.f('ix_leads_name'), table_name='leads')
    op.drop_index(op.f('ix_leads_phone'), table_name='leads')
    op.drop_index(op.f('ix_leads_status'), table_name='leads')
    op.drop_index(op.f('ix_leads_sub_category'), table_name='leads')
    op.create_index(op.f('ix_leads_contact_number'), 'leads', ['contact_number'], unique=False)
    op.create_unique_constraint(None, 'leads', ['zoho_lead_id'])
    op.drop_column('leads', 'name')
    op.drop_column('leads', 'phone')
    op.drop_column('leads', 'status')
    op.drop_column('leads', 'notes')
    op.drop_column('leads', 'budget')
    op.drop_column('leads', 'sub_category')
    op.drop_column('leads', 'category')
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=50),
               nullable=False,
               existing_server_default=sa.text("'USER'::character varying"))
    op.alter_column('users', 'is_email_verified',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_unique_constraint(None, 'users', ['email'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='unique')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.alter_column('users', 'is_email_verified',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=50),
               nullable=True,
               existing_server_default=sa.text("'USER'::character varying"))
    op.alter_column('users', 'email',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=100),
               existing_nullable=False)
    op.add_column('leads', sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('leads', sa.Column('sub_category', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('leads', sa.Column('budget', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('leads', sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('leads', sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False))
    op.add_column('leads', sa.Column('phone', sa.VARCHAR(length=20), autoincrement=False, nullable=True))
    op.add_column('leads', sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'leads', type_='unique')
    op.drop_index(op.f('ix_leads_contact_number'), table_name='leads')
    op.create_index(op.f('ix_leads_sub_category'), 'leads', ['sub_category'], unique=False)
    op.create_index(op.f('ix_leads_status'), 'leads', ['status'], unique=False)
    op.create_index(op.f('ix_leads_phone'), 'leads', ['phone'], unique=False)
    op.create_index(op.f('ix_leads_name'), 'leads', ['name'], unique=False)
    op.create_index(op.f('ix_leads_location'), 'leads', ['location'], unique=False)
    op.create_index(op.f('ix_leads_id'), 'leads', ['id'], unique=False)
    op.create_index(op.f('ix_leads_email'), 'leads', ['email'], unique=False)
    op.create_index(op.f('ix_leads_category'), 'leads', ['category'], unique=False)
    op.alter_column('leads', 'location',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('leads', 'email',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=100),
               nullable=False)
    op.drop_column('leads', 'qualification_status')
    op.drop_column('leads', 'budget_preference')
    op.drop_column('leads', 'franchise_preference')
    op.drop_column('leads', 'lead_source')
    op.drop_column('leads', 'contact_number')
    op.drop_column('leads', 'full_name')
    op.drop_column('leads', 'zoho_lead_id')
    op.create_table('user_roles',
    sa.Column('id', sa.UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('role', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('user_roles_user_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('user_roles_pkey'))
    )
    op.create_index(op.f('ix_user_roles_user_id'), 'user_roles', ['user_id'], unique=False)
    op.drop_table('documents')
    op.drop_index(op.f('ix_sessions_id'), table_name='sessions')
    op.drop_table('sessions')
    op.drop_table('refresh_tokens')
    op.drop_table('lead_responses')
    op.drop_table('system_settings')
    op.drop_table('questions')
    # ### end Alembic commands ###
