"""Add pgvector support for docqa system

Revision ID: 003_add_pgvector_support_for_docqa
Revises: bcb1046177f2
Create Date: 2025-01-08 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from pgvector.sqlalchemy import Vector


# revision identifiers, used by Alembic.
revision = '003pgvector'
down_revision = 'bcb1046177f2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add pgvector extension and embedding columns"""
    
    # Enable pgvector extension
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')
    
    # Add embedding column to documents table
    op.add_column('documents', sa.Column('embedding', Vector(1536), nullable=True))
    
    # Add embedding column to franchisors table
    op.add_column('franchisors', sa.Column('embedding', Vector(1536), nullable=True))
    
    # Create ivfflat indexes for cosine similarity search
    op.execute('CREATE INDEX ON documents USING ivfflat (embedding vector_cosine_ops)')
    op.execute('CREATE INDEX ON franchisors USING ivfflat (embedding vector_cosine_ops)')


def downgrade() -> None:
    """Remove pgvector support"""
    
    # Drop indexes
    op.execute('DROP INDEX IF EXISTS documents_embedding_idx')
    op.execute('DROP INDEX IF EXISTS franchisors_embedding_idx')
    
    # Drop embedding columns
    op.drop_column('franchisors', 'embedding')
    op.drop_column('documents', 'embedding')
    
    # Drop pgvector extension (optional, might be used by other parts)
    # op.execute('DROP EXTENSION IF EXISTS vector')
