# Query: SECRET_KEY
# ContextLines: 1

64 results - 18 files

.env:
  3  # JWT Configuration
  4: SECRET_KEY=your-super-secret-jwt-key-change-in-production
  5  JWT_ALGORITHM=HS256

.env.development:
  3  # JWT Configuration
  4: SECRET_KEY=your-super-secret-jwt-key-change-in-production
  5  JWT_ALGORITHM=HS256

test_auth.py:
  15      print(f"Settings type: {type(settings)}")
  16:     print(f"SECRET_KEY exists: {hasattr(settings, 'SECRET_KEY')}")
  17:     print(f"SECRET_KEY exists: {hasattr(settings, 'SECRET_KEY')}")
  18:     print(f"SECRET_KEY value: {settings.SECRET_KEY}")
  19:     print(f"JWT_SECRET_KEY value: {settings.JWT_SECRET_KEY}")
  20      

  27              test_token,
  28:             settings.JWT_SECRET_KEY,
  29              algorithms=[settings.ALGORITHM]

app/api/v1/endpoints/auth.py:
   57      to_encode.update({"exp": expire})
   58:     encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
   59      return encoded_jwt, expire

  462          # Calculate new token expiration
  463:         new_payload = jwt.decode(new_access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
  464          expires_in = int((new_payload["exp"] - datetime.now(timezone.utc).timestamp()))

  607          
  608:         payload = jwt.decode(access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
  609          expires_in = int((payload["exp"] - datetime.now(timezone.utc).timestamp()))

app/core/config/security.py:
  12      # JWT settings
  13:     secret_key: str = Field(..., description="JWT secret key")
  14      jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")

  51  security_settings = SecurityConfig(
  52:     secret_key=settings.JWT_SECRET_KEY,
  53      jwt_algorithm=settings.ALGORITHM,

app/core/config/settings.py:
  33      # JWT Configuration
  34:     SECRET_KEY: str = Field(
  35          default="your-secret-key-here",
  36          description="Secret key for JWT tokens",
  37:         alias="SECRET_KEY"
  38      )

app/core/operations/auth/auth_operations.py:
  265              import jwt
  266:             payload = jwt.decode(access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
  267              expires_in = int((payload["exp"] - datetime.now(timezone.utc).timestamp()))

app/core/security/auth_config_validator.py:
  51          # JWT Secret Key
  52:         if not hasattr(self.settings, 'JWT_SECRET_KEY') or not self.settings.JWT_SECRET_KEY:
  53:             raise ValueError("JWT_SECRET_KEY is required")
  54:         elif len(self.settings.JWT_SECRET_KEY) < 32:
  55:             raise ValueError("JWT_SECRET_KEY must be at least 32 characters long")
  56:         elif self.settings.JWT_SECRET_KEY == "your-super-secret-jwt-key-change-in-production-32-chars":
  57:             self.validation_warnings.append("JWT_SECRET_KEY is using default value - change in production")
  58          

app/core/security/auth.py:
  42              token,
  43:             settings.JWT_SECRET_KEY,
  44              algorithms=[settings.ALGORITHM]

app/core/security/email_verification.py:
  26              }
  27:             token = jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.ALGORITHM)
  28              return token

  43          try:
  44:             payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.ALGORITHM])
  45              if payload.get("type") != "email_verification":

app/core/security/jwt_manager.py:
   22          self,
   23:         secret_key: str,
   24          algorithm: str = "HS256",

   31          Args:
   32:             secret_key: The secret key for signing tokens
   33              algorithm: The algorithm to use for signing

   36          """
   37:         self.secret_key = secret_key
   38          self.algorithm = algorithm

   70  
   71:         return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
   72  

  101  
  102:         return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
  103  

  117          try:
  118:             payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
  119              return TokenData(**payload)

  144          try:
  145:             return jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
  146          except jwt.JWTError:

  162          try:
  163:             payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
  164              exp = datetime.fromtimestamp(payload["exp"], timezone.utc)

  170  jwt_manager = JWTManager(
  171:     secret_key=settings.JWT_SECRET_KEY,
  172      algorithm=settings.ALGORITHM,

app/core/security/refresh_token.py:
  48                  to_encode,
  49:                 settings.JWT_SECRET_KEY,
  50                  algorithm=settings.ALGORITHM

  86                  token,
  87:                 settings.JWT_SECRET_KEY,
  88                  algorithms=[settings.ALGORITHM]

app/core/security/token.py:
  20      to_encode.update({"exp": expire})
  21:     encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.ALGORITHM)
  22      return encoded_jwt

  28      to_encode.update({"exp": expire})
  29:     encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.ALGORITHM)
  30      return encoded_jwt

  34      try:
  35:         payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.ALGORITHM])
  36          return payload

  44              token,
  45:             settings.JWT_SECRET_KEY,
  46              algorithms=[settings.ALGORITHM]

app/core/security/utils.py:
   47          to_encode,
   48:         settings.JWT_SECRET_KEY,
   49          algorithm=settings.ALGORITHM

   68              token,
   69:             settings.JWT_SECRET_KEY,
   70              algorithms=[settings.ALGORITHM]

   93              token,
   94:             settings.JWT_SECRET_KEY,
   95              algorithms=[settings.ALGORITHM],

  114              token,
  115:             settings.JWT_SECRET_KEY,
  116              algorithms=[settings.ALGORITHM]

  138              token,
  139:             settings.JWT_SECRET_KEY,
  140              algorithms=[settings.ALGORITHM],

  162              token,
  163:             settings.JWT_SECRET_KEY,
  164              algorithms=[settings.ALGORITHM],

  186              token,
  187:             settings.JWT_SECRET_KEY,
  188              algorithms=[settings.ALGORITHM],

app/core/security/authentication_manager/jwt_handler.py:
   16          """Initialize JWT handler with settings"""
   17:         self.secret_key = settings.JWT_SECRET_KEY
   18          self.algorithm = settings.JWT_ALGORITHM

   95                  to_encode,
   96:                 self.secret_key,
   97                  algorithm=self.algorithm

  235                  token,
  236:                 self.secret_key,
  237                  algorithms=[self.algorithm],

app/schemas/security.py:
  81      # JWT settings
  82:     secret_key: str = Field(..., description="JWT secret key")
  83      jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")

app/services/s3_service.py:
  50          aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
  51:         aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
  52          
  53:         if not aws_access_key or not aws_secret_key:
  54              logger.error("AWS credentials not found in environment variables")

  57          # Check if credentials are not placeholder values
  58:         if aws_access_key in ['your-aws-access-key-id', ''] or aws_secret_key in ['your-aws-secret-access-key', '']:
  59              logger.error("AWS credentials are placeholder values")

rules/PROJECT_DEVELOPMENT_GUIDELINES.md:
  281  AWS_ACCESS_KEY_ID=your_access_key
  282: AWS_SECRET_ACCESS_KEY=your_secret_key
  283  AWS_DEFAULT_REGION=ap-south-1

  508      # JWT
  509:     SECRET_KEY: str
  510      JWT_ALGORITHM: str = "HS256"
